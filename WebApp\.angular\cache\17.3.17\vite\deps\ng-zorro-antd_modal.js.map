{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-pipes.mjs", "../../../../../node_modules/@angular/cdk/fesm2022/drag-drop.mjs", "../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-modal.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Pipe, NgModule } from '@angular/core';\nimport { sum, isNumberFinite, toDecimal, isNil } from 'ng-zorro-antd/core/util';\nimport * as i1 from '@angular/platform-browser';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzAggregatePipe {\n  transform(value, method) {\n    if (!Array.isArray(value)) {\n      return value;\n    }\n    if (value.length === 0) {\n      return undefined;\n    }\n    switch (method) {\n      case 'sum':\n        return sum(value);\n      case 'avg':\n        return sum(value) / value.length;\n      case 'max':\n        return Math.max(...value);\n      case 'min':\n        return Math.min(...value);\n      default:\n        throw Error(`Invalid Pipe Arguments: Aggregate pipe doesn't support this type`);\n    }\n  }\n  static {\n    this.ɵfac = function NzAggregatePipe_Factory(t) {\n      return new (t || NzAggregatePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzAggregate\",\n      type: NzAggregatePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzAggregatePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzAggregate',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzBytesPipe {\n  static {\n    this.formats = {\n      B: {\n        max: 1024\n      },\n      kB: {\n        max: Math.pow(1024, 2),\n        prev: 'B'\n      },\n      KB: {\n        max: Math.pow(1024, 2),\n        prev: 'B'\n      },\n      MB: {\n        max: Math.pow(1024, 3),\n        prev: 'kB'\n      },\n      GB: {\n        max: Math.pow(1024, 4),\n        prev: 'MB'\n      },\n      TB: {\n        max: Number.MAX_SAFE_INTEGER,\n        prev: 'GB'\n      }\n    };\n  }\n  transform(input, decimal = 0, from = 'B', to) {\n    if (!(isNumberFinite(input) && isNumberFinite(decimal) && decimal % 1 === 0 && decimal >= 0)) {\n      return input;\n    }\n    let bytes = input;\n    let unit = from;\n    while (unit !== 'B') {\n      bytes *= 1024;\n      unit = NzBytesPipe.formats[unit].prev;\n    }\n    if (to) {\n      const format = NzBytesPipe.formats[to];\n      const result = toDecimal(NzBytesPipe.calculateResult(format, bytes), decimal);\n      return NzBytesPipe.formatResult(result, to);\n    }\n    for (const key in NzBytesPipe.formats) {\n      if (NzBytesPipe.formats.hasOwnProperty(key)) {\n        const format = NzBytesPipe.formats[key];\n        if (bytes < format.max) {\n          const result = toDecimal(NzBytesPipe.calculateResult(format, bytes), decimal);\n          return NzBytesPipe.formatResult(result, key);\n        }\n      }\n    }\n  }\n  static formatResult(result, unit) {\n    return `${result} ${unit}`;\n  }\n  static calculateResult(format, bytes) {\n    const prev = format.prev ? NzBytesPipe.formats[format.prev] : undefined;\n    return prev ? bytes / prev.max : bytes;\n  }\n  static {\n    this.ɵfac = function NzBytesPipe_Factory(t) {\n      return new (t || NzBytesPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzBytes\",\n      type: NzBytesPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBytesPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzBytes',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzToCssUnitPipe {\n  transform(value, defaultUnit = 'px') {\n    return typeof value === 'number' ? `${value}${defaultUnit}` : value;\n  }\n  static {\n    this.ɵfac = function NzToCssUnitPipe_Factory(t) {\n      return new (t || NzToCssUnitPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzToCssUnit\",\n      type: NzToCssUnitPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzToCssUnitPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzToCssUnit',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzEllipsisPipe {\n  transform(value, length, suffix = '') {\n    if (typeof value !== 'string') {\n      return value;\n    }\n    const len = typeof length === 'undefined' ? value.length : length;\n    if (value.length <= len) {\n      return value;\n    }\n    return value.substring(0, len) + suffix;\n  }\n  static {\n    this.ɵfac = function NzEllipsisPipe_Factory(t) {\n      return new (t || NzEllipsisPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzEllipsis\",\n      type: NzEllipsisPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzEllipsisPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzEllipsis',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * @deprecated v17.0.0 - Use Nullish coalescing operator (??) instead of `NzSafeNullPipe`.\n */\nclass NzSafeNullPipe {\n  transform(value, replace = '') {\n    if (isNil(value)) {\n      return replace;\n    }\n    return value;\n  }\n  static {\n    this.ɵfac = function NzSafeNullPipe_Factory(t) {\n      return new (t || NzSafeNullPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzSafeNull\",\n      type: NzSafeNullPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSafeNullPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzSafeNull',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSanitizerPipe {\n  constructor(sanitizer) {\n    this.sanitizer = sanitizer;\n  }\n  transform(value, type = 'html') {\n    switch (type) {\n      case 'html':\n        return this.sanitizer.bypassSecurityTrustHtml(value);\n      case 'style':\n        return this.sanitizer.bypassSecurityTrustStyle(value);\n      case 'url':\n        return this.sanitizer.bypassSecurityTrustUrl(value);\n      case 'resourceUrl':\n        return this.sanitizer.bypassSecurityTrustResourceUrl(value);\n      default:\n        throw new Error(`Invalid safe type specified`);\n    }\n  }\n  static {\n    this.ɵfac = function NzSanitizerPipe_Factory(t) {\n      return new (t || NzSanitizerPipe)(i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzSanitizer\",\n      type: NzSanitizerPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSanitizerPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzSanitizer',\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.DomSanitizer\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTrimPipe {\n  // TODO(chensimeng) trimEnd, trimStart\n  transform(text) {\n    return text.trim();\n  }\n  static {\n    this.ɵfac = function NzTrimPipe_Factory(t) {\n      return new (t || NzTrimPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzTrim\",\n      type: NzTrimPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTrimPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzTrim',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst pipes = [NzToCssUnitPipe, NzSafeNullPipe, NzSanitizerPipe, NzTrimPipe, NzBytesPipe, NzAggregatePipe, NzEllipsisPipe];\nclass NzPipesModule {\n  static {\n    this.ɵfac = function NzPipesModule_Factory(t) {\n      return new (t || NzPipesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzPipesModule,\n      imports: [NzToCssUnitPipe, NzSafeNullPipe, NzSanitizerPipe, NzTrimPipe, NzBytesPipe, NzAggregatePipe, NzEllipsisPipe],\n      exports: [NzToCssUnitPipe, NzSafeNullPipe, NzSanitizerPipe, NzTrimPipe, NzBytesPipe, NzAggregatePipe, NzEllipsisPipe]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPipesModule, [{\n    type: NgModule,\n    args: [{\n      imports: [pipes],\n      exports: [pipes]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzAggregatePipe, NzBytesPipe, NzEllipsisPipe, NzPipesModule, NzSafeNullPipe, NzSanitizerPipe, NzToCssUnitPipe, NzTrimPipe };\n", "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, ApplicationRef, EnvironmentInjector, createComponent, Injectable, Inject, InjectionToken, booleanAttribute, Directive, Optional, SkipSelf, Input, EventEmitter, Self, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { _getEventTarget, normalizePassiveListenerOptions, _getShadowRoot } from '@angular/cdk/platform';\nimport { coerceElement, coerceNumberProperty, coerceArray } from '@angular/cdk/coercion';\nimport { isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { Subject, Subscription, interval, animationFrameScheduler, Observable, merge, BehaviorSubject } from 'rxjs';\nimport { takeUntil, map, take, tap, switchMap, startWith } from 'rxjs/operators';\nimport * as i1$1 from '@angular/cdk/bidi';\n\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\nfunction extendStyles(dest, source, importantProperties) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      const value = source[key];\n      if (value) {\n        dest.setProperty(key, value, importantProperties?.has(key) ? 'important' : '');\n      } else {\n        dest.removeProperty(key);\n      }\n    }\n  }\n  return dest;\n}\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\nfunction toggleNativeDragInteractions(element, enable) {\n  const userSelect = enable ? '' : 'none';\n  extendStyles(element.style, {\n    'touch-action': enable ? '' : 'none',\n    '-webkit-user-drag': enable ? '' : 'none',\n    '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n    'user-select': userSelect,\n    '-ms-user-select': userSelect,\n    '-webkit-user-select': userSelect,\n    '-moz-user-select': userSelect\n  });\n}\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\nfunction toggleVisibility(element, enable, importantProperties) {\n  extendStyles(element.style, {\n    position: enable ? '' : 'fixed',\n    top: enable ? '' : '0',\n    opacity: enable ? '' : '0',\n    left: enable ? '' : '-999em'\n  }, importantProperties);\n}\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\nfunction combineTransforms(transform, initialTransform) {\n  return initialTransform && initialTransform != 'none' ? transform + ' ' + initialTransform : transform;\n}\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\nfunction matchElementSize(target, sourceRect) {\n  target.style.width = `${sourceRect.width}px`;\n  target.style.height = `${sourceRect.height}px`;\n  target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\nfunction getTransform(x, y) {\n  // Round the transforms since some browsers will\n  // blur the elements for sub-pixel transforms.\n  return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n\n/** Gets a mutable version of an element's bounding `DOMRect`. */\nfunction getMutableClientRect(element) {\n  const rect = element.getBoundingClientRect();\n  // We need to clone the `clientRect` here, because all the values on it are readonly\n  // and we need to be able to update them. Also we can't use a spread here, because\n  // the values on a `DOMRect` aren't own properties. See:\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n  return {\n    top: rect.top,\n    right: rect.right,\n    bottom: rect.bottom,\n    left: rect.left,\n    width: rect.width,\n    height: rect.height,\n    x: rect.x,\n    y: rect.y\n  };\n}\n/**\n * Checks whether some coordinates are within a `DOMRect`.\n * @param clientRect DOMRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\nfunction isInsideClientRect(clientRect, x, y) {\n  const {\n    top,\n    bottom,\n    left,\n    right\n  } = clientRect;\n  return y >= top && y <= bottom && x >= left && x <= right;\n}\n/**\n * Updates the top/left positions of a `DOMRect`, as well as their bottom/right counterparts.\n * @param domRect `DOMRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\nfunction adjustDomRect(domRect, top, left) {\n  domRect.top += top;\n  domRect.bottom = domRect.top + domRect.height;\n  domRect.left += left;\n  domRect.right = domRect.left + domRect.width;\n}\n/**\n * Checks whether the pointer coordinates are close to a DOMRect.\n * @param rect DOMRect to check against.\n * @param threshold Threshold around the DOMRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\nfunction isPointerNearDomRect(rect, threshold, pointerX, pointerY) {\n  const {\n    top,\n    right,\n    bottom,\n    left,\n    width,\n    height\n  } = rect;\n  const xThreshold = width * threshold;\n  const yThreshold = height * threshold;\n  return pointerY > top - yThreshold && pointerY < bottom + yThreshold && pointerX > left - xThreshold && pointerX < right + xThreshold;\n}\n\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\nclass ParentPositionTracker {\n  constructor(_document) {\n    this._document = _document;\n    /** Cached positions of the scrollable parent elements. */\n    this.positions = new Map();\n  }\n  /** Clears the cached positions. */\n  clear() {\n    this.positions.clear();\n  }\n  /** Caches the positions. Should be called at the beginning of a drag sequence. */\n  cache(elements) {\n    this.clear();\n    this.positions.set(this._document, {\n      scrollPosition: this.getViewportScrollPosition()\n    });\n    elements.forEach(element => {\n      this.positions.set(element, {\n        scrollPosition: {\n          top: element.scrollTop,\n          left: element.scrollLeft\n        },\n        clientRect: getMutableClientRect(element)\n      });\n    });\n  }\n  /** Handles scrolling while a drag is taking place. */\n  handleScroll(event) {\n    const target = _getEventTarget(event);\n    const cachedPosition = this.positions.get(target);\n    if (!cachedPosition) {\n      return null;\n    }\n    const scrollPosition = cachedPosition.scrollPosition;\n    let newTop;\n    let newLeft;\n    if (target === this._document) {\n      const viewportScrollPosition = this.getViewportScrollPosition();\n      newTop = viewportScrollPosition.top;\n      newLeft = viewportScrollPosition.left;\n    } else {\n      newTop = target.scrollTop;\n      newLeft = target.scrollLeft;\n    }\n    const topDifference = scrollPosition.top - newTop;\n    const leftDifference = scrollPosition.left - newLeft;\n    // Go through and update the cached positions of the scroll\n    // parents that are inside the element that was scrolled.\n    this.positions.forEach((position, node) => {\n      if (position.clientRect && target !== node && target.contains(node)) {\n        adjustDomRect(position.clientRect, topDifference, leftDifference);\n      }\n    });\n    scrollPosition.top = newTop;\n    scrollPosition.left = newLeft;\n    return {\n      top: topDifference,\n      left: leftDifference\n    };\n  }\n  /**\n   * Gets the scroll position of the viewport. Note that we use the scrollX and scrollY directly,\n   * instead of going through the `ViewportRuler`, because the first value the ruler looks at is\n   * the top/left offset of the `document.documentElement` which works for most cases, but breaks\n   * if the element is offset by something like the `BlockScrollStrategy`.\n   */\n  getViewportScrollPosition() {\n    return {\n      top: window.scrollY,\n      left: window.scrollX\n    };\n  }\n}\n\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n  const clone = node.cloneNode(true);\n  const descendantsWithId = clone.querySelectorAll('[id]');\n  const nodeName = node.nodeName.toLowerCase();\n  // Remove the `id` to avoid having multiple elements with the same id on the page.\n  clone.removeAttribute('id');\n  for (let i = 0; i < descendantsWithId.length; i++) {\n    descendantsWithId[i].removeAttribute('id');\n  }\n  if (nodeName === 'canvas') {\n    transferCanvasData(node, clone);\n  } else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n    transferInputData(node, clone);\n  }\n  transferData('canvas', node, clone, transferCanvasData);\n  transferData('input, textarea, select', node, clone, transferInputData);\n  return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n  const descendantElements = node.querySelectorAll(selector);\n  if (descendantElements.length) {\n    const cloneElements = clone.querySelectorAll(selector);\n    for (let i = 0; i < descendantElements.length; i++) {\n      callback(descendantElements[i], cloneElements[i]);\n    }\n  }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n  // Browsers throw an error when assigning the value of a file input programmatically.\n  if (clone.type !== 'file') {\n    clone.value = source.value;\n  }\n  // Radio button `name` attributes must be unique for radio button groups\n  // otherwise original radio buttons can lose their checked state\n  // once the clone is inserted in the DOM.\n  if (clone.type === 'radio' && clone.name) {\n    clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n  }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n  const context = clone.getContext('2d');\n  if (context) {\n    // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n    // We can't do much about it so just ignore the error.\n    try {\n      context.drawImage(source, 0, 0);\n    } catch {}\n  }\n}\n\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\nfunction getRootNode(viewRef, _document) {\n  const rootNodes = viewRef.rootNodes;\n  if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n    return rootNodes[0];\n  }\n  const wrapper = _document.createElement('div');\n  rootNodes.forEach(node => wrapper.appendChild(node));\n  return wrapper;\n}\n\n/** Parses a CSS time value to milliseconds. */\nfunction parseCssTimeUnitsToMs(value) {\n  // Some browsers will return it in seconds, whereas others will return milliseconds.\n  const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n  return parseFloat(value) * multiplier;\n}\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\nfunction getTransformTransitionDurationInMs(element) {\n  const computedStyle = getComputedStyle(element);\n  const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n  const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all');\n  // If there's no transition for `all` or `transform`, we shouldn't do anything.\n  if (!property) {\n    return 0;\n  }\n  // Get the index of the property that we're interested in and match\n  // it up to the same index in `transition-delay` and `transition-duration`.\n  const propertyIndex = transitionedProperties.indexOf(property);\n  const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n  const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n  return parseCssTimeUnitsToMs(rawDurations[propertyIndex]) + parseCssTimeUnitsToMs(rawDelays[propertyIndex]);\n}\n/** Parses out multiple values from a computed style into an array. */\nfunction parseCssPropertyValue(computedStyle, name) {\n  const value = computedStyle.getPropertyValue(name);\n  return value.split(',').map(part => part.trim());\n}\n\n/** Inline styles to be set as `!important` while dragging. */\nconst importantProperties = new Set([\n// Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n'position']);\nclass PreviewRef {\n  constructor(_document, _rootElement, _direction, _initialDomRect, _previewTemplate, _previewClass, _pickupPositionOnPage, _initialTransform, _zIndex) {\n    this._document = _document;\n    this._rootElement = _rootElement;\n    this._direction = _direction;\n    this._initialDomRect = _initialDomRect;\n    this._previewTemplate = _previewTemplate;\n    this._previewClass = _previewClass;\n    this._pickupPositionOnPage = _pickupPositionOnPage;\n    this._initialTransform = _initialTransform;\n    this._zIndex = _zIndex;\n  }\n  attach(parent) {\n    this._preview = this._createPreview();\n    parent.appendChild(this._preview);\n    // The null check is necessary for browsers that don't support the popover API.\n    // Note that we use a string access for compatibility with Closure.\n    if ('showPopover' in this._preview) {\n      this._preview['showPopover']();\n    }\n  }\n  destroy() {\n    this._preview.remove();\n    this._previewEmbeddedView?.destroy();\n    this._preview = this._previewEmbeddedView = null;\n  }\n  setTransform(value) {\n    this._preview.style.transform = value;\n  }\n  getBoundingClientRect() {\n    return this._preview.getBoundingClientRect();\n  }\n  addClass(className) {\n    this._preview.classList.add(className);\n  }\n  getTransitionDuration() {\n    return getTransformTransitionDurationInMs(this._preview);\n  }\n  addEventListener(name, handler) {\n    this._preview.addEventListener(name, handler);\n  }\n  removeEventListener(name, handler) {\n    this._preview.removeEventListener(name, handler);\n  }\n  _createPreview() {\n    const previewConfig = this._previewTemplate;\n    const previewClass = this._previewClass;\n    const previewTemplate = previewConfig ? previewConfig.template : null;\n    let preview;\n    if (previewTemplate && previewConfig) {\n      // Measure the element before we've inserted the preview\n      // since the insertion could throw off the measurement.\n      const rootRect = previewConfig.matchSize ? this._initialDomRect : null;\n      const viewRef = previewConfig.viewContainer.createEmbeddedView(previewTemplate, previewConfig.context);\n      viewRef.detectChanges();\n      preview = getRootNode(viewRef, this._document);\n      this._previewEmbeddedView = viewRef;\n      if (previewConfig.matchSize) {\n        matchElementSize(preview, rootRect);\n      } else {\n        preview.style.transform = getTransform(this._pickupPositionOnPage.x, this._pickupPositionOnPage.y);\n      }\n    } else {\n      preview = deepCloneNode(this._rootElement);\n      matchElementSize(preview, this._initialDomRect);\n      if (this._initialTransform) {\n        preview.style.transform = this._initialTransform;\n      }\n    }\n    extendStyles(preview.style, {\n      // It's important that we disable the pointer events on the preview, because\n      // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n      'pointer-events': 'none',\n      // We have to reset the margin, because it can throw off positioning relative to the viewport.\n      'margin': '0',\n      'position': 'fixed',\n      'top': '0',\n      'left': '0',\n      'z-index': this._zIndex + ''\n    }, importantProperties);\n    toggleNativeDragInteractions(preview, false);\n    preview.classList.add('cdk-drag-preview');\n    preview.setAttribute('popover', 'manual');\n    preview.setAttribute('dir', this._direction);\n    if (previewClass) {\n      if (Array.isArray(previewClass)) {\n        previewClass.forEach(className => preview.classList.add(className));\n      } else {\n        preview.classList.add(previewClass);\n      }\n    }\n    return preview;\n  }\n}\n\n/** Options that can be used to bind a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/** Options that can be used to bind an active event listener. */\nconst activeEventListenerOptions = normalizePassiveListenerOptions({\n  passive: false\n});\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions$1 = normalizePassiveListenerOptions({\n  passive: false,\n  capture: true\n});\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n/** Inline styles to be set as `!important` while dragging. */\nconst dragImportantProperties = new Set([\n// Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n'position']);\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\nclass DragRef {\n  /** Whether starting to drag this element is disabled. */\n  get disabled() {\n    return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n  }\n  set disabled(value) {\n    if (value !== this._disabled) {\n      this._disabled = value;\n      this._toggleNativeDragInteractions();\n      this._handles.forEach(handle => toggleNativeDragInteractions(handle, value));\n    }\n  }\n  constructor(element, _config, _document, _ngZone, _viewportRuler, _dragDropRegistry) {\n    this._config = _config;\n    this._document = _document;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._dragDropRegistry = _dragDropRegistry;\n    /**\n     * CSS `transform` applied to the element when it isn't being dragged. We need a\n     * passive transform in order for the dragged element to retain its new position\n     * after the user has stopped dragging and because we need to know the relative\n     * position in case they start dragging again. This corresponds to `element.style.transform`.\n     */\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n    /** CSS `transform` that is applied to the element while it's being dragged. */\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    /**\n     * Whether the dragging sequence has been started. Doesn't\n     * necessarily mean that the element has been moved.\n     */\n    this._hasStartedDragging = false;\n    /** Emits when the item is being moved. */\n    this._moveEvents = new Subject();\n    /** Subscription to pointer movement events. */\n    this._pointerMoveSubscription = Subscription.EMPTY;\n    /** Subscription to the event that is dispatched when the user lifts their pointer. */\n    this._pointerUpSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being scrolled. */\n    this._scrollSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being resized. */\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Cached reference to the boundary element. */\n    this._boundaryElement = null;\n    /** Whether the native dragging interactions have been enabled on the root element. */\n    this._nativeInteractionsEnabled = true;\n    /** Elements that can be used to drag the draggable item. */\n    this._handles = [];\n    /** Registered handles that are currently disabled. */\n    this._disabledHandles = new Set();\n    /** Layout direction of the item. */\n    this._direction = 'ltr';\n    /**\n     * Amount of milliseconds to wait after the user has put their\n     * pointer down before starting to drag the element.\n     */\n    this.dragStartDelay = 0;\n    this._disabled = false;\n    /** Emits as the drag sequence is being prepared. */\n    this.beforeStarted = new Subject();\n    /** Emits when the user starts dragging the item. */\n    this.started = new Subject();\n    /** Emits when the user has released a drag item, before any animations have started. */\n    this.released = new Subject();\n    /** Emits when the user stops dragging an item in the container. */\n    this.ended = new Subject();\n    /** Emits when the user has moved the item into a new container. */\n    this.entered = new Subject();\n    /** Emits when the user removes the item its container by dragging it into another container. */\n    this.exited = new Subject();\n    /** Emits when the user drops the item inside a container. */\n    this.dropped = new Subject();\n    /**\n     * Emits as the user is dragging the item. Use with caution,\n     * because this event will fire for every pixel that the user has dragged.\n     */\n    this.moved = this._moveEvents;\n    /** Handler for the `mousedown`/`touchstart` events. */\n    this._pointerDown = event => {\n      this.beforeStarted.next();\n      // Delegate the event based on whether it started from a handle or the element itself.\n      if (this._handles.length) {\n        const targetHandle = this._getTargetHandle(event);\n        if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n          this._initializeDragSequence(targetHandle, event);\n        }\n      } else if (!this.disabled) {\n        this._initializeDragSequence(this._rootElement, event);\n      }\n    };\n    /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n    this._pointerMove = event => {\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      if (!this._hasStartedDragging) {\n        const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n        const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n        const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold;\n        // Only start dragging after the user has moved more than the minimum distance in either\n        // direction. Note that this is preferable over doing something like `skip(minimumDistance)`\n        // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n        // per pixel of movement (e.g. if the user moves their pointer quickly).\n        if (isOverThreshold) {\n          const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n          const container = this._dropContainer;\n          if (!isDelayElapsed) {\n            this._endDragSequence(event);\n            return;\n          }\n          // Prevent other drag sequences from starting while something in the container is still\n          // being dragged. This can happen while we're waiting for the drop animation to finish\n          // and can cause errors, because some elements might still be moving around.\n          if (!container || !container.isDragging() && !container.isReceiving()) {\n            // Prevent the default action as soon as the dragging sequence is considered as\n            // \"started\" since waiting for the next event can allow the device to begin scrolling.\n            if (event.cancelable) {\n              event.preventDefault();\n            }\n            this._hasStartedDragging = true;\n            this._ngZone.run(() => this._startDragSequence(event));\n          }\n        }\n        return;\n      }\n      // We prevent the default action down here so that we know that dragging has started. This is\n      // important for touch devices where doing this too early can unnecessarily block scrolling,\n      // if there's a dragging delay.\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n      this._hasMoved = true;\n      this._lastKnownPointerPosition = pointerPosition;\n      this._updatePointerDirectionDelta(constrainedPointerPosition);\n      if (this._dropContainer) {\n        this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n      } else {\n        // If there's a position constraint function, we want the element's top/left to be at the\n        // specific position on the page. Use the initial position as a reference if that's the case.\n        const offset = this.constrainPosition ? this._initialDomRect : this._pickupPositionOnPage;\n        const activeTransform = this._activeTransform;\n        activeTransform.x = constrainedPointerPosition.x - offset.x + this._passiveTransform.x;\n        activeTransform.y = constrainedPointerPosition.y - offset.y + this._passiveTransform.y;\n        this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n      }\n      // Since this event gets fired for every pixel while dragging, we only\n      // want to fire it if the consumer opted into it. Also we have to\n      // re-enter the zone because we run all of the events on the outside.\n      if (this._moveEvents.observers.length) {\n        this._ngZone.run(() => {\n          this._moveEvents.next({\n            source: this,\n            pointerPosition: constrainedPointerPosition,\n            event,\n            distance: this._getDragDistance(constrainedPointerPosition),\n            delta: this._pointerDirectionDelta\n          });\n        });\n      }\n    };\n    /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n    this._pointerUp = event => {\n      this._endDragSequence(event);\n    };\n    /** Handles a native `dragstart` event. */\n    this._nativeDragStart = event => {\n      if (this._handles.length) {\n        const targetHandle = this._getTargetHandle(event);\n        if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n          event.preventDefault();\n        }\n      } else if (!this.disabled) {\n        // Usually this isn't necessary since the we prevent the default action in `pointerDown`,\n        // but some cases like dragging of links can slip through (see #24403).\n        event.preventDefault();\n      }\n    };\n    this.withRootElement(element).withParent(_config.parentDragRef || null);\n    this._parentPositions = new ParentPositionTracker(_document);\n    _dragDropRegistry.registerDragItem(this);\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement() {\n    return this._placeholder;\n  }\n  /** Returns the root draggable element. */\n  getRootElement() {\n    return this._rootElement;\n  }\n  /**\n   * Gets the currently-visible element that represents the drag item.\n   * While dragging this is the placeholder, otherwise it's the root element.\n   */\n  getVisibleElement() {\n    return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n  }\n  /** Registers the handles that can be used to drag the element. */\n  withHandles(handles) {\n    this._handles = handles.map(handle => coerceElement(handle));\n    this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n    this._toggleNativeDragInteractions();\n    // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n    // the set, rather than iterate over it and filter out the destroyed handles, because while\n    // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n    // use an array internally which may throw an error.\n    const disabledHandles = new Set();\n    this._disabledHandles.forEach(handle => {\n      if (this._handles.indexOf(handle) > -1) {\n        disabledHandles.add(handle);\n      }\n    });\n    this._disabledHandles = disabledHandles;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag preview.\n   * @param template Template that from which to stamp out the preview.\n   */\n  withPreviewTemplate(template) {\n    this._previewTemplate = template;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag placeholder.\n   * @param template Template that from which to stamp out the placeholder.\n   */\n  withPlaceholderTemplate(template) {\n    this._placeholderTemplate = template;\n    return this;\n  }\n  /**\n   * Sets an alternate drag root element. The root element is the element that will be moved as\n   * the user is dragging. Passing an alternate root element is useful when trying to enable\n   * dragging on an element that you might not have access to.\n   */\n  withRootElement(rootElement) {\n    const element = coerceElement(rootElement);\n    if (element !== this._rootElement) {\n      if (this._rootElement) {\n        this._removeRootElementListeners(this._rootElement);\n      }\n      this._ngZone.runOutsideAngular(() => {\n        element.addEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n        element.addEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n        element.addEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n      });\n      this._initialTransform = undefined;\n      this._rootElement = element;\n    }\n    if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n      this._ownerSVGElement = this._rootElement.ownerSVGElement;\n    }\n    return this;\n  }\n  /**\n   * Element to which the draggable's position will be constrained.\n   */\n  withBoundaryElement(boundaryElement) {\n    this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n    this._resizeSubscription.unsubscribe();\n    if (boundaryElement) {\n      this._resizeSubscription = this._viewportRuler.change(10).subscribe(() => this._containInsideBoundaryOnResize());\n    }\n    return this;\n  }\n  /** Sets the parent ref that the ref is nested in.  */\n  withParent(parent) {\n    this._parentDragRef = parent;\n    return this;\n  }\n  /** Removes the dragging functionality from the DOM element. */\n  dispose() {\n    this._removeRootElementListeners(this._rootElement);\n    // Do this check before removing from the registry since it'll\n    // stop being considered as dragged once it is removed.\n    if (this.isDragging()) {\n      // Since we move out the element to the end of the body while it's being\n      // dragged, we have to make sure that it's removed if it gets destroyed.\n      this._rootElement?.remove();\n    }\n    this._anchor?.remove();\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._dragDropRegistry.removeDragItem(this);\n    this._removeListeners();\n    this.beforeStarted.complete();\n    this.started.complete();\n    this.released.complete();\n    this.ended.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this._moveEvents.complete();\n    this._handles = [];\n    this._disabledHandles.clear();\n    this._dropContainer = undefined;\n    this._resizeSubscription.unsubscribe();\n    this._parentPositions.clear();\n    this._boundaryElement = this._rootElement = this._ownerSVGElement = this._placeholderTemplate = this._previewTemplate = this._anchor = this._parentDragRef = null;\n  }\n  /** Checks whether the element is currently being dragged. */\n  isDragging() {\n    return this._hasStartedDragging && this._dragDropRegistry.isDragging(this);\n  }\n  /** Resets a standalone drag item to its initial position. */\n  reset() {\n    this._rootElement.style.transform = this._initialTransform || '';\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n  }\n  /**\n   * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n   * @param handle Handle element that should be disabled.\n   */\n  disableHandle(handle) {\n    if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n      this._disabledHandles.add(handle);\n      toggleNativeDragInteractions(handle, true);\n    }\n  }\n  /**\n   * Enables a handle, if it has been disabled.\n   * @param handle Handle element to be enabled.\n   */\n  enableHandle(handle) {\n    if (this._disabledHandles.has(handle)) {\n      this._disabledHandles.delete(handle);\n      toggleNativeDragInteractions(handle, this.disabled);\n    }\n  }\n  /** Sets the layout direction of the draggable item. */\n  withDirection(direction) {\n    this._direction = direction;\n    return this;\n  }\n  /** Sets the container that the item is part of. */\n  _withDropContainer(container) {\n    this._dropContainer = container;\n  }\n  /**\n   * Gets the current position in pixels the draggable outside of a drop container.\n   */\n  getFreeDragPosition() {\n    const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n    return {\n      x: position.x,\n      y: position.y\n    };\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value) {\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform.x = value.x;\n    this._passiveTransform.y = value.y;\n    if (!this._dropContainer) {\n      this._applyRootElementTransform(value.x, value.y);\n    }\n    return this;\n  }\n  /**\n   * Sets the container into which to insert the preview element.\n   * @param value Container into which to insert the preview.\n   */\n  withPreviewContainer(value) {\n    this._previewContainer = value;\n    return this;\n  }\n  /** Updates the item's sort order based on the last-known pointer position. */\n  _sortFromLastPointerPosition() {\n    const position = this._lastKnownPointerPosition;\n    if (position && this._dropContainer) {\n      this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n    }\n  }\n  /** Unsubscribes from the global subscriptions. */\n  _removeListeners() {\n    this._pointerMoveSubscription.unsubscribe();\n    this._pointerUpSubscription.unsubscribe();\n    this._scrollSubscription.unsubscribe();\n    this._getShadowRoot()?.removeEventListener('selectstart', shadowDomSelectStart, activeCapturingEventOptions$1);\n  }\n  /** Destroys the preview element and its ViewRef. */\n  _destroyPreview() {\n    this._preview?.destroy();\n    this._preview = null;\n  }\n  /** Destroys the placeholder element and its ViewRef. */\n  _destroyPlaceholder() {\n    this._placeholder?.remove();\n    this._placeholderRef?.destroy();\n    this._placeholder = this._placeholderRef = null;\n  }\n  /**\n   * Clears subscriptions and stops the dragging sequence.\n   * @param event Browser event object that ended the sequence.\n   */\n  _endDragSequence(event) {\n    // Note that here we use `isDragging` from the service, rather than from `this`.\n    // The difference is that the one from the service reflects whether a dragging sequence\n    // has been initiated, whereas the one on `this` includes whether the user has passed\n    // the minimum dragging threshold.\n    if (!this._dragDropRegistry.isDragging(this)) {\n      return;\n    }\n    this._removeListeners();\n    this._dragDropRegistry.stopDragging(this);\n    this._toggleNativeDragInteractions();\n    if (this._handles) {\n      this._rootElement.style.webkitTapHighlightColor = this._rootElementTapHighlight;\n    }\n    if (!this._hasStartedDragging) {\n      return;\n    }\n    this.released.next({\n      source: this,\n      event\n    });\n    if (this._dropContainer) {\n      // Stop scrolling immediately, instead of waiting for the animation to finish.\n      this._dropContainer._stopScrolling();\n      this._animatePreviewToPlaceholder().then(() => {\n        this._cleanupDragArtifacts(event);\n        this._cleanupCachedDimensions();\n        this._dragDropRegistry.stopDragging(this);\n      });\n    } else {\n      // Convert the active transform into a passive one. This means that next time\n      // the user starts dragging the item, its position will be calculated relatively\n      // to the new passive transform.\n      this._passiveTransform.x = this._activeTransform.x;\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      this._passiveTransform.y = this._activeTransform.y;\n      this._ngZone.run(() => {\n        this.ended.next({\n          source: this,\n          distance: this._getDragDistance(pointerPosition),\n          dropPoint: pointerPosition,\n          event\n        });\n      });\n      this._cleanupCachedDimensions();\n      this._dragDropRegistry.stopDragging(this);\n    }\n  }\n  /** Starts the dragging sequence. */\n  _startDragSequence(event) {\n    if (isTouchEvent(event)) {\n      this._lastTouchEventTime = Date.now();\n    }\n    this._toggleNativeDragInteractions();\n    // Needs to happen before the root element is moved.\n    const shadowRoot = this._getShadowRoot();\n    const dropContainer = this._dropContainer;\n    if (shadowRoot) {\n      // In some browsers the global `selectstart` that we maintain in the `DragDropRegistry`\n      // doesn't cross the shadow boundary so we have to prevent it at the shadow root (see #28792).\n      this._ngZone.runOutsideAngular(() => {\n        shadowRoot.addEventListener('selectstart', shadowDomSelectStart, activeCapturingEventOptions$1);\n      });\n    }\n    if (dropContainer) {\n      const element = this._rootElement;\n      const parent = element.parentNode;\n      const placeholder = this._placeholder = this._createPlaceholderElement();\n      const anchor = this._anchor = this._anchor || this._document.createComment('');\n      // Insert an anchor node so that we can restore the element's position in the DOM.\n      parent.insertBefore(anchor, element);\n      // There's no risk of transforms stacking when inside a drop container so\n      // we can keep the initial transform up to date any time dragging starts.\n      this._initialTransform = element.style.transform || '';\n      // Create the preview after the initial transform has\n      // been cached, because it can be affected by the transform.\n      this._preview = new PreviewRef(this._document, this._rootElement, this._direction, this._initialDomRect, this._previewTemplate || null, this.previewClass || null, this._pickupPositionOnPage, this._initialTransform, this._config.zIndex || 1000);\n      this._preview.attach(this._getPreviewInsertionPoint(parent, shadowRoot));\n      // We move the element out at the end of the body and we make it hidden, because keeping it in\n      // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n      // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n      toggleVisibility(element, false, dragImportantProperties);\n      this._document.body.appendChild(parent.replaceChild(placeholder, element));\n      this.started.next({\n        source: this,\n        event\n      }); // Emit before notifying the container.\n      dropContainer.start();\n      this._initialContainer = dropContainer;\n      this._initialIndex = dropContainer.getItemIndex(this);\n    } else {\n      this.started.next({\n        source: this,\n        event\n      });\n      this._initialContainer = this._initialIndex = undefined;\n    }\n    // Important to run after we've called `start` on the parent container\n    // so that it has had time to resolve its scrollable parents.\n    this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n  }\n  /**\n   * Sets up the different variables and subscriptions\n   * that will be necessary for the dragging sequence.\n   * @param referenceElement Element that started the drag sequence.\n   * @param event Browser event object that started the sequence.\n   */\n  _initializeDragSequence(referenceElement, event) {\n    // Stop propagation if the item is inside another\n    // draggable so we don't start multiple drag sequences.\n    if (this._parentDragRef) {\n      event.stopPropagation();\n    }\n    const isDragging = this.isDragging();\n    const isTouchSequence = isTouchEvent(event);\n    const isAuxiliaryMouseButton = !isTouchSequence && event.button !== 0;\n    const rootElement = this._rootElement;\n    const target = _getEventTarget(event);\n    const isSyntheticEvent = !isTouchSequence && this._lastTouchEventTime && this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n    const isFakeEvent = isTouchSequence ? isFakeTouchstartFromScreenReader(event) : isFakeMousedownFromScreenReader(event);\n    // If the event started from an element with the native HTML drag&drop, it'll interfere\n    // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n    // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n    // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n    // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n    // events from firing on touch devices.\n    if (target && target.draggable && event.type === 'mousedown') {\n      event.preventDefault();\n    }\n    // Abort if the user is already dragging or is using a mouse button other than the primary one.\n    if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n      return;\n    }\n    // If we've got handles, we need to disable the tap highlight on the entire root element,\n    // otherwise iOS will still add it, even though all the drag interactions on the handle\n    // are disabled.\n    if (this._handles.length) {\n      const rootStyles = rootElement.style;\n      this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n      rootStyles.webkitTapHighlightColor = 'transparent';\n    }\n    this._hasStartedDragging = this._hasMoved = false;\n    // Avoid multiple subscriptions and memory leaks when multi touch\n    // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n    this._removeListeners();\n    this._initialDomRect = this._rootElement.getBoundingClientRect();\n    this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n    this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n    this._scrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n    if (this._boundaryElement) {\n      this._boundaryRect = getMutableClientRect(this._boundaryElement);\n    }\n    // If we have a custom preview we can't know ahead of time how large it'll be so we position\n    // it next to the cursor. The exception is when the consumer has opted into making the preview\n    // the same size as the root element, in which case we do know the size.\n    const previewTemplate = this._previewTemplate;\n    this._pickupPositionInElement = previewTemplate && previewTemplate.template && !previewTemplate.matchSize ? {\n      x: 0,\n      y: 0\n    } : this._getPointerPositionInElement(this._initialDomRect, referenceElement, event);\n    const pointerPosition = this._pickupPositionOnPage = this._lastKnownPointerPosition = this._getPointerPositionOnPage(event);\n    this._pointerDirectionDelta = {\n      x: 0,\n      y: 0\n    };\n    this._pointerPositionAtLastDirectionChange = {\n      x: pointerPosition.x,\n      y: pointerPosition.y\n    };\n    this._dragStartTime = Date.now();\n    this._dragDropRegistry.startDragging(this, event);\n  }\n  /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n  _cleanupDragArtifacts(event) {\n    // Restore the element's visibility and insert it at its old position in the DOM.\n    // It's important that we maintain the position, because moving the element around in the DOM\n    // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n    // while moving the existing elements in all other cases.\n    toggleVisibility(this._rootElement, true, dragImportantProperties);\n    this._anchor.parentNode.replaceChild(this._rootElement, this._anchor);\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._initialDomRect = this._boundaryRect = this._previewRect = this._initialTransform = undefined;\n    // Re-enter the NgZone since we bound `document` events on the outside.\n    this._ngZone.run(() => {\n      const container = this._dropContainer;\n      const currentIndex = container.getItemIndex(this);\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      const distance = this._getDragDistance(pointerPosition);\n      const isPointerOverContainer = container._isOverContainer(pointerPosition.x, pointerPosition.y);\n      this.ended.next({\n        source: this,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      this.dropped.next({\n        item: this,\n        currentIndex,\n        previousIndex: this._initialIndex,\n        container: container,\n        previousContainer: this._initialContainer,\n        isPointerOverContainer,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      container.drop(this, currentIndex, this._initialIndex, this._initialContainer, isPointerOverContainer, distance, pointerPosition, event);\n      this._dropContainer = this._initialContainer;\n    });\n  }\n  /**\n   * Updates the item's position in its drop container, or moves it\n   * into a new one, depending on its current drag position.\n   */\n  _updateActiveDropContainer({\n    x,\n    y\n  }, {\n    x: rawX,\n    y: rawY\n  }) {\n    // Drop container that draggable has been moved into.\n    let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y);\n    // If we couldn't find a new container to move the item into, and the item has left its\n    // initial container, check whether the it's over the initial container. This handles the\n    // case where two containers are connected one way and the user tries to undo dragging an\n    // item into a new container.\n    if (!newContainer && this._dropContainer !== this._initialContainer && this._initialContainer._isOverContainer(x, y)) {\n      newContainer = this._initialContainer;\n    }\n    if (newContainer && newContainer !== this._dropContainer) {\n      this._ngZone.run(() => {\n        // Notify the old container that the item has left.\n        this.exited.next({\n          item: this,\n          container: this._dropContainer\n        });\n        this._dropContainer.exit(this);\n        // Notify the new container that the item has entered.\n        this._dropContainer = newContainer;\n        this._dropContainer.enter(this, x, y, newContainer === this._initialContainer &&\n        // If we're re-entering the initial container and sorting is disabled,\n        // put item the into its starting index to begin with.\n        newContainer.sortingDisabled ? this._initialIndex : undefined);\n        this.entered.next({\n          item: this,\n          container: newContainer,\n          currentIndex: newContainer.getItemIndex(this)\n        });\n      });\n    }\n    // Dragging may have been interrupted as a result of the events above.\n    if (this.isDragging()) {\n      this._dropContainer._startScrollingIfNecessary(rawX, rawY);\n      this._dropContainer._sortItem(this, x, y, this._pointerDirectionDelta);\n      if (this.constrainPosition) {\n        this._applyPreviewTransform(x, y);\n      } else {\n        this._applyPreviewTransform(x - this._pickupPositionInElement.x, y - this._pickupPositionInElement.y);\n      }\n    }\n  }\n  /**\n   * Animates the preview element from its current position to the location of the drop placeholder.\n   * @returns Promise that resolves when the animation completes.\n   */\n  _animatePreviewToPlaceholder() {\n    // If the user hasn't moved yet, the transitionend event won't fire.\n    if (!this._hasMoved) {\n      return Promise.resolve();\n    }\n    const placeholderRect = this._placeholder.getBoundingClientRect();\n    // Apply the class that adds a transition to the preview.\n    this._preview.addClass('cdk-drag-animating');\n    // Move the preview to the placeholder position.\n    this._applyPreviewTransform(placeholderRect.left, placeholderRect.top);\n    // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n    // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n    // apply its style, we take advantage of the available info to figure out whether we need to\n    // bind the event in the first place.\n    const duration = this._preview.getTransitionDuration();\n    if (duration === 0) {\n      return Promise.resolve();\n    }\n    return this._ngZone.runOutsideAngular(() => {\n      return new Promise(resolve => {\n        const handler = event => {\n          if (!event || _getEventTarget(event) === this._preview && event.propertyName === 'transform') {\n            this._preview?.removeEventListener('transitionend', handler);\n            resolve();\n            clearTimeout(timeout);\n          }\n        };\n        // If a transition is short enough, the browser might not fire the `transitionend` event.\n        // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n        // fire if the transition hasn't completed when it was supposed to.\n        const timeout = setTimeout(handler, duration * 1.5);\n        this._preview.addEventListener('transitionend', handler);\n      });\n    });\n  }\n  /** Creates an element that will be shown instead of the current element while dragging. */\n  _createPlaceholderElement() {\n    const placeholderConfig = this._placeholderTemplate;\n    const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n    let placeholder;\n    if (placeholderTemplate) {\n      this._placeholderRef = placeholderConfig.viewContainer.createEmbeddedView(placeholderTemplate, placeholderConfig.context);\n      this._placeholderRef.detectChanges();\n      placeholder = getRootNode(this._placeholderRef, this._document);\n    } else {\n      placeholder = deepCloneNode(this._rootElement);\n    }\n    // Stop pointer events on the preview so the user can't\n    // interact with it while the preview is animating.\n    placeholder.style.pointerEvents = 'none';\n    placeholder.classList.add('cdk-drag-placeholder');\n    return placeholder;\n  }\n  /**\n   * Figures out the coordinates at which an element was picked up.\n   * @param referenceElement Element that initiated the dragging.\n   * @param event Event that initiated the dragging.\n   */\n  _getPointerPositionInElement(elementRect, referenceElement, event) {\n    const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n    const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n    const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n    const scrollPosition = this._getViewportScrollPosition();\n    const x = point.pageX - referenceRect.left - scrollPosition.left;\n    const y = point.pageY - referenceRect.top - scrollPosition.top;\n    return {\n      x: referenceRect.left - elementRect.left + x,\n      y: referenceRect.top - elementRect.top + y\n    };\n  }\n  /** Determines the point of the page that was touched by the user. */\n  _getPointerPositionOnPage(event) {\n    const scrollPosition = this._getViewportScrollPosition();\n    const point = isTouchEvent(event) ?\n    // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n    // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n    // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n    // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n    // throwing an error. The value returned here will be incorrect, but since this only\n    // breaks inside a developer tool and the value is only used for secondary information,\n    // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n    event.touches[0] || event.changedTouches[0] || {\n      pageX: 0,\n      pageY: 0\n    } : event;\n    const x = point.pageX - scrollPosition.left;\n    const y = point.pageY - scrollPosition.top;\n    // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n    // coordinate system\n    if (this._ownerSVGElement) {\n      const svgMatrix = this._ownerSVGElement.getScreenCTM();\n      if (svgMatrix) {\n        const svgPoint = this._ownerSVGElement.createSVGPoint();\n        svgPoint.x = x;\n        svgPoint.y = y;\n        return svgPoint.matrixTransform(svgMatrix.inverse());\n      }\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Gets the pointer position on the page, accounting for any position constraints. */\n  _getConstrainedPointerPosition(point) {\n    const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n    let {\n      x,\n      y\n    } = this.constrainPosition ? this.constrainPosition(point, this, this._initialDomRect, this._pickupPositionInElement) : point;\n    if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n      y = this._pickupPositionOnPage.y - (this.constrainPosition ? this._pickupPositionInElement.y : 0);\n    } else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n      x = this._pickupPositionOnPage.x - (this.constrainPosition ? this._pickupPositionInElement.x : 0);\n    }\n    if (this._boundaryRect) {\n      // If not using a custom constrain we need to account for the pickup position in the element\n      // otherwise we do not need to do this, as it has already been accounted for\n      const {\n        x: pickupX,\n        y: pickupY\n      } = !this.constrainPosition ? this._pickupPositionInElement : {\n        x: 0,\n        y: 0\n      };\n      const boundaryRect = this._boundaryRect;\n      const {\n        width: previewWidth,\n        height: previewHeight\n      } = this._getPreviewRect();\n      const minY = boundaryRect.top + pickupY;\n      const maxY = boundaryRect.bottom - (previewHeight - pickupY);\n      const minX = boundaryRect.left + pickupX;\n      const maxX = boundaryRect.right - (previewWidth - pickupX);\n      x = clamp$1(x, minX, maxX);\n      y = clamp$1(y, minY, maxY);\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the current drag delta, based on the user's current pointer position on the page. */\n  _updatePointerDirectionDelta(pointerPositionOnPage) {\n    const {\n      x,\n      y\n    } = pointerPositionOnPage;\n    const delta = this._pointerDirectionDelta;\n    const positionSinceLastChange = this._pointerPositionAtLastDirectionChange;\n    // Amount of pixels the user has dragged since the last time the direction changed.\n    const changeX = Math.abs(x - positionSinceLastChange.x);\n    const changeY = Math.abs(y - positionSinceLastChange.y);\n    // Because we handle pointer events on a per-pixel basis, we don't want the delta\n    // to change for every pixel, otherwise anything that depends on it can look erratic.\n    // To make the delta more consistent, we track how much the user has moved since the last\n    // delta change and we only update it after it has reached a certain threshold.\n    if (changeX > this._config.pointerDirectionChangeThreshold) {\n      delta.x = x > positionSinceLastChange.x ? 1 : -1;\n      positionSinceLastChange.x = x;\n    }\n    if (changeY > this._config.pointerDirectionChangeThreshold) {\n      delta.y = y > positionSinceLastChange.y ? 1 : -1;\n      positionSinceLastChange.y = y;\n    }\n    return delta;\n  }\n  /** Toggles the native drag interactions, based on how many handles are registered. */\n  _toggleNativeDragInteractions() {\n    if (!this._rootElement || !this._handles) {\n      return;\n    }\n    const shouldEnable = this._handles.length > 0 || !this.isDragging();\n    if (shouldEnable !== this._nativeInteractionsEnabled) {\n      this._nativeInteractionsEnabled = shouldEnable;\n      toggleNativeDragInteractions(this._rootElement, shouldEnable);\n    }\n  }\n  /** Removes the manually-added event listeners from the root element. */\n  _removeRootElementListeners(element) {\n    element.removeEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n    element.removeEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n    element.removeEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n  }\n  /**\n   * Applies a `transform` to the root element, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  _applyRootElementTransform(x, y) {\n    const transform = getTransform(x, y);\n    const styles = this._rootElement.style;\n    // Cache the previous transform amount only after the first drag sequence, because\n    // we don't want our own transforms to stack on top of each other.\n    // Should be excluded none because none + translate3d(x, y, x) is invalid css\n    if (this._initialTransform == null) {\n      this._initialTransform = styles.transform && styles.transform != 'none' ? styles.transform : '';\n    }\n    // Preserve the previous `transform` value, if there was one. Note that we apply our own\n    // transform before the user's, because things like rotation can affect which direction\n    // the element will be translated towards.\n    styles.transform = combineTransforms(transform, this._initialTransform);\n  }\n  /**\n   * Applies a `transform` to the preview, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  _applyPreviewTransform(x, y) {\n    // Only apply the initial transform if the preview is a clone of the original element, otherwise\n    // it could be completely different and the transform might not make sense anymore.\n    const initialTransform = this._previewTemplate?.template ? undefined : this._initialTransform;\n    const transform = getTransform(x, y);\n    this._preview.setTransform(combineTransforms(transform, initialTransform));\n  }\n  /**\n   * Gets the distance that the user has dragged during the current drag sequence.\n   * @param currentPosition Current position of the user's pointer.\n   */\n  _getDragDistance(currentPosition) {\n    const pickupPosition = this._pickupPositionOnPage;\n    if (pickupPosition) {\n      return {\n        x: currentPosition.x - pickupPosition.x,\n        y: currentPosition.y - pickupPosition.y\n      };\n    }\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n  _cleanupCachedDimensions() {\n    this._boundaryRect = this._previewRect = undefined;\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the element is still inside its boundary after the viewport has been resized.\n   * If not, the position is adjusted so that the element fits again.\n   */\n  _containInsideBoundaryOnResize() {\n    let {\n      x,\n      y\n    } = this._passiveTransform;\n    if (x === 0 && y === 0 || this.isDragging() || !this._boundaryElement) {\n      return;\n    }\n    // Note: don't use `_clientRectAtStart` here, because we want the latest position.\n    const elementRect = this._rootElement.getBoundingClientRect();\n    const boundaryRect = this._boundaryElement.getBoundingClientRect();\n    // It's possible that the element got hidden away after dragging (e.g. by switching to a\n    // different tab). Don't do anything in this case so we don't clear the user's position.\n    if (boundaryRect.width === 0 && boundaryRect.height === 0 || elementRect.width === 0 && elementRect.height === 0) {\n      return;\n    }\n    const leftOverflow = boundaryRect.left - elementRect.left;\n    const rightOverflow = elementRect.right - boundaryRect.right;\n    const topOverflow = boundaryRect.top - elementRect.top;\n    const bottomOverflow = elementRect.bottom - boundaryRect.bottom;\n    // If the element has become wider than the boundary, we can't\n    // do much to make it fit so we just anchor it to the left.\n    if (boundaryRect.width > elementRect.width) {\n      if (leftOverflow > 0) {\n        x += leftOverflow;\n      }\n      if (rightOverflow > 0) {\n        x -= rightOverflow;\n      }\n    } else {\n      x = 0;\n    }\n    // If the element has become taller than the boundary, we can't\n    // do much to make it fit so we just anchor it to the top.\n    if (boundaryRect.height > elementRect.height) {\n      if (topOverflow > 0) {\n        y += topOverflow;\n      }\n      if (bottomOverflow > 0) {\n        y -= bottomOverflow;\n      }\n    } else {\n      y = 0;\n    }\n    if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n      this.setFreeDragPosition({\n        y,\n        x\n      });\n    }\n  }\n  /** Gets the drag start delay, based on the event type. */\n  _getDragStartDelay(event) {\n    const value = this.dragStartDelay;\n    if (typeof value === 'number') {\n      return value;\n    } else if (isTouchEvent(event)) {\n      return value.touch;\n    }\n    return value ? value.mouse : 0;\n  }\n  /** Updates the internal state of the draggable element when scrolling has occurred. */\n  _updateOnScroll(event) {\n    const scrollDifference = this._parentPositions.handleScroll(event);\n    if (scrollDifference) {\n      const target = _getEventTarget(event);\n      // DOMRect dimensions are based on the scroll position of the page and its parent\n      // node so we have to update the cached boundary DOMRect if the user has scrolled.\n      if (this._boundaryRect && target !== this._boundaryElement && target.contains(this._boundaryElement)) {\n        adjustDomRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n      }\n      this._pickupPositionOnPage.x += scrollDifference.left;\n      this._pickupPositionOnPage.y += scrollDifference.top;\n      // If we're in free drag mode, we have to update the active transform, because\n      // it isn't relative to the viewport like the preview inside a drop list.\n      if (!this._dropContainer) {\n        this._activeTransform.x -= scrollDifference.left;\n        this._activeTransform.y -= scrollDifference.top;\n        this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n      }\n    }\n  }\n  /** Gets the scroll position of the viewport. */\n  _getViewportScrollPosition() {\n    return this._parentPositions.positions.get(this._document)?.scrollPosition || this._parentPositions.getViewportScrollPosition();\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  _getShadowRoot() {\n    if (this._cachedShadowRoot === undefined) {\n      this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n    }\n    return this._cachedShadowRoot;\n  }\n  /** Gets the element into which the drag preview should be inserted. */\n  _getPreviewInsertionPoint(initialParent, shadowRoot) {\n    const previewContainer = this._previewContainer || 'global';\n    if (previewContainer === 'parent') {\n      return initialParent;\n    }\n    if (previewContainer === 'global') {\n      const documentRef = this._document;\n      // We can't use the body if the user is in fullscreen mode,\n      // because the preview will render under the fullscreen element.\n      // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n      return shadowRoot || documentRef.fullscreenElement || documentRef.webkitFullscreenElement || documentRef.mozFullScreenElement || documentRef.msFullscreenElement || documentRef.body;\n    }\n    return coerceElement(previewContainer);\n  }\n  /** Lazily resolves and returns the dimensions of the preview. */\n  _getPreviewRect() {\n    // Cache the preview element rect if we haven't cached it already or if\n    // we cached it too early before the element dimensions were computed.\n    if (!this._previewRect || !this._previewRect.width && !this._previewRect.height) {\n      this._previewRect = this._preview ? this._preview.getBoundingClientRect() : this._initialDomRect;\n    }\n    return this._previewRect;\n  }\n  /** Gets a handle that is the target of an event. */\n  _getTargetHandle(event) {\n    return this._handles.find(handle => {\n      return event.target && (event.target === handle || handle.contains(event.target));\n    });\n  }\n}\n/** Clamps a value between a minimum and a maximum. */\nfunction clamp$1(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/** Determines whether an event is a touch event. */\nfunction isTouchEvent(event) {\n  // This function is called for every pixel that the user has dragged so we need it to be\n  // as fast as possible. Since we only bind mouse events and touch events, we can assume\n  // that if the event's name starts with `t`, it's a touch event.\n  return event.type[0] === 't';\n}\n/** Callback invoked for `selectstart` events inside the shadow DOM. */\nfunction shadowDomSelectStart(event) {\n  event.preventDefault();\n}\n\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\nfunction moveItemInArray(array, fromIndex, toIndex) {\n  const from = clamp(fromIndex, array.length - 1);\n  const to = clamp(toIndex, array.length - 1);\n  if (from === to) {\n    return;\n  }\n  const target = array[from];\n  const delta = to < from ? -1 : 1;\n  for (let i = from; i !== to; i += delta) {\n    array[i] = array[i + delta];\n  }\n  array[to] = target;\n}\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\nfunction transferArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const from = clamp(currentIndex, currentArray.length - 1);\n  const to = clamp(targetIndex, targetArray.length);\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n  }\n}\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\nfunction copyArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const to = clamp(targetIndex, targetArray.length);\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray[currentIndex]);\n  }\n}\n/** Clamps a number between zero and a maximum. */\nfunction clamp(value, max) {\n  return Math.max(0, Math.min(max, value));\n}\n\n/**\n * Strategy that only supports sorting along a single axis.\n * Items are reordered using CSS transforms which allows for sorting to be animated.\n * @docs-private\n */\nclass SingleAxisSortStrategy {\n  constructor(_element, _dragDropRegistry) {\n    this._element = _element;\n    this._dragDropRegistry = _dragDropRegistry;\n    /** Cache of the dimensions of all the items inside the container. */\n    this._itemPositions = [];\n    /** Direction in which the list is oriented. */\n    this.orientation = 'vertical';\n    /**\n     * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n     * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n     * overlap with the swapped item after the swapping occurred.\n     */\n    this._previousSwap = {\n      drag: null,\n      delta: 0,\n      overlaps: false\n    };\n  }\n  /**\n   * To be called when the drag sequence starts.\n   * @param items Items that are currently in the list.\n   */\n  start(items) {\n    this.withItems(items);\n  }\n  /**\n   * To be called when an item is being sorted.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  sort(item, pointerX, pointerY, pointerDelta) {\n    const siblings = this._itemPositions;\n    const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n    if (newIndex === -1 && siblings.length > 0) {\n      return null;\n    }\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n    const siblingAtNewPosition = siblings[newIndex];\n    const currentPosition = siblings[currentIndex].clientRect;\n    const newPosition = siblingAtNewPosition.clientRect;\n    const delta = currentIndex > newIndex ? 1 : -1;\n    // How many pixels the item's placeholder should be offset.\n    const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta);\n    // How many pixels all the other items should be offset.\n    const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta);\n    // Save the previous order of the items before moving the item to its new index.\n    // We use this to check whether an item has been moved as a result of the sorting.\n    const oldOrder = siblings.slice();\n    // Shuffle the array in place.\n    moveItemInArray(siblings, currentIndex, newIndex);\n    siblings.forEach((sibling, index) => {\n      // Don't do anything if the position hasn't changed.\n      if (oldOrder[index] === sibling) {\n        return;\n      }\n      const isDraggedItem = sibling.drag === item;\n      const offset = isDraggedItem ? itemOffset : siblingOffset;\n      const elementToOffset = isDraggedItem ? item.getPlaceholderElement() : sibling.drag.getRootElement();\n      // Update the offset to reflect the new position.\n      sibling.offset += offset;\n      // Since we're moving the items with a `transform`, we need to adjust their cached\n      // client rects to reflect their new position, as well as swap their positions in the cache.\n      // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n      // elements may be mid-animation which will give us a wrong result.\n      if (isHorizontal) {\n        // Round the transforms since some browsers will\n        // blur the elements, for sub-pixel transforms.\n        elementToOffset.style.transform = combineTransforms(`translate3d(${Math.round(sibling.offset)}px, 0, 0)`, sibling.initialTransform);\n        adjustDomRect(sibling.clientRect, 0, offset);\n      } else {\n        elementToOffset.style.transform = combineTransforms(`translate3d(0, ${Math.round(sibling.offset)}px, 0)`, sibling.initialTransform);\n        adjustDomRect(sibling.clientRect, offset, 0);\n      }\n    });\n    // Note that it's important that we do this after the client rects have been adjusted.\n    this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n    this._previousSwap.drag = siblingAtNewPosition.drag;\n    this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n    return {\n      previousIndex: currentIndex,\n      currentIndex: newIndex\n    };\n  }\n  /**\n   * Called when an item is being moved into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    const newIndex = index == null || index < 0 ?\n    // We use the coordinates of where the item entered the drop\n    // zone to figure out at which index it should be inserted.\n    this._getItemIndexFromPointerPosition(item, pointerX, pointerY) : index;\n    const activeDraggables = this._activeDraggables;\n    const currentIndex = activeDraggables.indexOf(item);\n    const placeholder = item.getPlaceholderElement();\n    let newPositionReference = activeDraggables[newIndex];\n    // If the item at the new position is the same as the item that is being dragged,\n    // it means that we're trying to restore the item to its initial position. In this\n    // case we should use the next item from the list as the reference.\n    if (newPositionReference === item) {\n      newPositionReference = activeDraggables[newIndex + 1];\n    }\n    // If we didn't find a new position reference, it means that either the item didn't start off\n    // in this container, or that the item requested to be inserted at the end of the list.\n    if (!newPositionReference && (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) && this._shouldEnterAsFirstChild(pointerX, pointerY)) {\n      newPositionReference = activeDraggables[0];\n    }\n    // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n    // into another container and back again), we have to ensure that it isn't duplicated.\n    if (currentIndex > -1) {\n      activeDraggables.splice(currentIndex, 1);\n    }\n    // Don't use items that are being dragged as a reference, because\n    // their element has been moved down to the bottom of the body.\n    if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n      const element = newPositionReference.getRootElement();\n      element.parentElement.insertBefore(placeholder, element);\n      activeDraggables.splice(newIndex, 0, item);\n    } else {\n      coerceElement(this._element).appendChild(placeholder);\n      activeDraggables.push(item);\n    }\n    // The transform needs to be cleared so it doesn't throw off the measurements.\n    placeholder.style.transform = '';\n    // Note that usually `start` is called together with `enter` when an item goes into a new\n    // container. This will cache item positions, but we need to refresh them since the amount\n    // of items has changed.\n    this._cacheItemPositions();\n  }\n  /** Sets the items that are currently part of the list. */\n  withItems(items) {\n    this._activeDraggables = items.slice();\n    this._cacheItemPositions();\n  }\n  /** Assigns a sort predicate to the strategy. */\n  withSortPredicate(predicate) {\n    this._sortPredicate = predicate;\n  }\n  /** Resets the strategy to its initial state before dragging was started. */\n  reset() {\n    // TODO(crisbeto): may have to wait for the animations to finish.\n    this._activeDraggables.forEach(item => {\n      const rootElement = item.getRootElement();\n      if (rootElement) {\n        const initialTransform = this._itemPositions.find(p => p.drag === item)?.initialTransform;\n        rootElement.style.transform = initialTransform || '';\n      }\n    });\n    this._itemPositions = [];\n    this._activeDraggables = [];\n    this._previousSwap.drag = null;\n    this._previousSwap.delta = 0;\n    this._previousSwap.overlaps = false;\n  }\n  /**\n   * Gets a snapshot of items currently in the list.\n   * Can include items that we dragged in from another list.\n   */\n  getActiveItemsSnapshot() {\n    return this._activeDraggables;\n  }\n  /** Gets the index of a specific item. */\n  getItemIndex(item) {\n    // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n    // The rest of the logic still stands no matter what orientation we're in, however\n    // we need to invert the array when determining the index.\n    const items = this.orientation === 'horizontal' && this.direction === 'rtl' ? this._itemPositions.slice().reverse() : this._itemPositions;\n    return items.findIndex(currentItem => currentItem.drag === item);\n  }\n  /** Used to notify the strategy that the scroll position has changed. */\n  updateOnScroll(topDifference, leftDifference) {\n    // Since we know the amount that the user has scrolled we can shift all of the\n    // client rectangles ourselves. This is cheaper than re-measuring everything and\n    // we can avoid inconsistent behavior where we might be measuring the element before\n    // its position has changed.\n    this._itemPositions.forEach(({\n      clientRect\n    }) => {\n      adjustDomRect(clientRect, topDifference, leftDifference);\n    });\n    // We need two loops for this, because we want all of the cached\n    // positions to be up-to-date before we re-sort the item.\n    this._itemPositions.forEach(({\n      drag\n    }) => {\n      if (this._dragDropRegistry.isDragging(drag)) {\n        // We need to re-sort the item manually, because the pointer move\n        // events won't be dispatched while the user is scrolling.\n        drag._sortFromLastPointerPosition();\n      }\n    });\n  }\n  /** Refreshes the position cache of the items and sibling containers. */\n  _cacheItemPositions() {\n    const isHorizontal = this.orientation === 'horizontal';\n    this._itemPositions = this._activeDraggables.map(drag => {\n      const elementToMeasure = drag.getVisibleElement();\n      return {\n        drag,\n        offset: 0,\n        initialTransform: elementToMeasure.style.transform || '',\n        clientRect: getMutableClientRect(elementToMeasure)\n      };\n    }).sort((a, b) => {\n      return isHorizontal ? a.clientRect.left - b.clientRect.left : a.clientRect.top - b.clientRect.top;\n    });\n  }\n  /**\n   * Gets the offset in pixels by which the item that is being dragged should be moved.\n   * @param currentPosition Current position of the item.\n   * @param newPosition Position of the item where the current item should be moved.\n   * @param delta Direction in which the user is moving.\n   */\n  _getItemOffsetPx(currentPosition, newPosition, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    let itemOffset = isHorizontal ? newPosition.left - currentPosition.left : newPosition.top - currentPosition.top;\n    // Account for differences in the item width/height.\n    if (delta === -1) {\n      itemOffset += isHorizontal ? newPosition.width - currentPosition.width : newPosition.height - currentPosition.height;\n    }\n    return itemOffset;\n  }\n  /**\n   * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n   * @param currentIndex Index of the item currently being dragged.\n   * @param siblings All of the items in the list.\n   * @param delta Direction in which the user is moving.\n   */\n  _getSiblingOffsetPx(currentIndex, siblings, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentPosition = siblings[currentIndex].clientRect;\n    const immediateSibling = siblings[currentIndex + delta * -1];\n    let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n    if (immediateSibling) {\n      const start = isHorizontal ? 'left' : 'top';\n      const end = isHorizontal ? 'right' : 'bottom';\n      // Get the spacing between the start of the current item and the end of the one immediately\n      // after it in the direction in which the user is dragging, or vice versa. We add it to the\n      // offset in order to push the element to where it will be when it's inline and is influenced\n      // by the `margin` of its siblings.\n      if (delta === -1) {\n        siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n      } else {\n        siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n      }\n    }\n    return siblingOffset;\n  }\n  /**\n   * Checks if pointer is entering in the first position\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   */\n  _shouldEnterAsFirstChild(pointerX, pointerY) {\n    if (!this._activeDraggables.length) {\n      return false;\n    }\n    const itemPositions = this._itemPositions;\n    const isHorizontal = this.orientation === 'horizontal';\n    // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n    // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n    const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n    if (reversed) {\n      const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n      return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n    } else {\n      const firstItemRect = itemPositions[0].clientRect;\n      return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n    }\n  }\n  /**\n   * Gets the index of an item in the drop container, based on the position of the user's pointer.\n   * @param item Item that is being sorted.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   * @param delta Direction in which the user is moving their pointer.\n   */\n  _getItemIndexFromPointerPosition(item, pointerX, pointerY, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const index = this._itemPositions.findIndex(({\n      drag,\n      clientRect\n    }) => {\n      // Skip the item itself.\n      if (drag === item) {\n        return false;\n      }\n      if (delta) {\n        const direction = isHorizontal ? delta.x : delta.y;\n        // If the user is still hovering over the same item as last time, their cursor hasn't left\n        // the item after we made the swap, and they didn't change the direction in which they're\n        // dragging, we don't consider it a direction swap.\n        if (drag === this._previousSwap.drag && this._previousSwap.overlaps && direction === this._previousSwap.delta) {\n          return false;\n        }\n      }\n      return isHorizontal ?\n      // Round these down since most browsers report client rects with\n      // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n      pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right) : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n    });\n    return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n  }\n}\n\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n/** Vertical direction in which we can auto-scroll. */\nvar AutoScrollVerticalDirection;\n(function (AutoScrollVerticalDirection) {\n  AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"NONE\"] = 0] = \"NONE\";\n  AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"UP\"] = 1] = \"UP\";\n  AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"DOWN\"] = 2] = \"DOWN\";\n})(AutoScrollVerticalDirection || (AutoScrollVerticalDirection = {}));\n/** Horizontal direction in which we can auto-scroll. */\nvar AutoScrollHorizontalDirection;\n(function (AutoScrollHorizontalDirection) {\n  AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"NONE\"] = 0] = \"NONE\";\n  AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"LEFT\"] = 1] = \"LEFT\";\n  AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"RIGHT\"] = 2] = \"RIGHT\";\n})(AutoScrollHorizontalDirection || (AutoScrollHorizontalDirection = {}));\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\nclass DropListRef {\n  constructor(element, _dragDropRegistry, _document, _ngZone, _viewportRuler) {\n    this._dragDropRegistry = _dragDropRegistry;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    /** Whether starting a dragging sequence from this container is disabled. */\n    this.disabled = false;\n    /** Whether sorting items within the list is disabled. */\n    this.sortingDisabled = false;\n    /**\n     * Whether auto-scrolling the view when the user\n     * moves their pointer close to the edges is disabled.\n     */\n    this.autoScrollDisabled = false;\n    /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n    this.autoScrollStep = 2;\n    /**\n     * Function that is used to determine whether an item\n     * is allowed to be moved into a drop container.\n     */\n    this.enterPredicate = () => true;\n    /** Function that is used to determine whether an item can be sorted into a particular index. */\n    this.sortPredicate = () => true;\n    /** Emits right before dragging has started. */\n    this.beforeStarted = new Subject();\n    /**\n     * Emits when the user has moved a new drag item into this container.\n     */\n    this.entered = new Subject();\n    /**\n     * Emits when the user removes an item from the container\n     * by dragging it into another container.\n     */\n    this.exited = new Subject();\n    /** Emits when the user drops an item inside the container. */\n    this.dropped = new Subject();\n    /** Emits as the user is swapping items while actively dragging. */\n    this.sorted = new Subject();\n    /** Emits when a dragging sequence is started in a list connected to the current one. */\n    this.receivingStarted = new Subject();\n    /** Emits when a dragging sequence is stopped from a list connected to the current one. */\n    this.receivingStopped = new Subject();\n    /** Whether an item in the list is being dragged. */\n    this._isDragging = false;\n    /** Draggable items in the container. */\n    this._draggables = [];\n    /** Drop lists that are connected to the current one. */\n    this._siblings = [];\n    /** Connected siblings that currently have a dragged item. */\n    this._activeSiblings = new Set();\n    /** Subscription to the window being scrolled. */\n    this._viewportScrollSubscription = Subscription.EMPTY;\n    /** Vertical direction in which the list is currently scrolling. */\n    this._verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n    /** Horizontal direction in which the list is currently scrolling. */\n    this._horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n    /** Used to signal to the current auto-scroll sequence when to stop. */\n    this._stopScrollTimers = new Subject();\n    /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n    this._cachedShadowRoot = null;\n    /** Starts the interval that'll auto-scroll the element. */\n    this._startScrollInterval = () => {\n      this._stopScrolling();\n      interval(0, animationFrameScheduler).pipe(takeUntil(this._stopScrollTimers)).subscribe(() => {\n        const node = this._scrollNode;\n        const scrollStep = this.autoScrollStep;\n        if (this._verticalScrollDirection === AutoScrollVerticalDirection.UP) {\n          node.scrollBy(0, -scrollStep);\n        } else if (this._verticalScrollDirection === AutoScrollVerticalDirection.DOWN) {\n          node.scrollBy(0, scrollStep);\n        }\n        if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.LEFT) {\n          node.scrollBy(-scrollStep, 0);\n        } else if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.RIGHT) {\n          node.scrollBy(scrollStep, 0);\n        }\n      });\n    };\n    this.element = coerceElement(element);\n    this._document = _document;\n    this.withScrollableParents([this.element]);\n    _dragDropRegistry.registerDropContainer(this);\n    this._parentPositions = new ParentPositionTracker(_document);\n    this._sortStrategy = new SingleAxisSortStrategy(this.element, _dragDropRegistry);\n    this._sortStrategy.withSortPredicate((index, item) => this.sortPredicate(index, item, this));\n  }\n  /** Removes the drop list functionality from the DOM element. */\n  dispose() {\n    this._stopScrolling();\n    this._stopScrollTimers.complete();\n    this._viewportScrollSubscription.unsubscribe();\n    this.beforeStarted.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this.sorted.complete();\n    this.receivingStarted.complete();\n    this.receivingStopped.complete();\n    this._activeSiblings.clear();\n    this._scrollNode = null;\n    this._parentPositions.clear();\n    this._dragDropRegistry.removeDropContainer(this);\n  }\n  /** Whether an item from this list is currently being dragged. */\n  isDragging() {\n    return this._isDragging;\n  }\n  /** Starts dragging an item. */\n  start() {\n    this._draggingStarted();\n    this._notifyReceivingSiblings();\n  }\n  /**\n   * Attempts to move an item into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    this._draggingStarted();\n    // If sorting is disabled, we want the item to return to its starting\n    // position if the user is returning it to its initial container.\n    if (index == null && this.sortingDisabled) {\n      index = this._draggables.indexOf(item);\n    }\n    this._sortStrategy.enter(item, pointerX, pointerY, index);\n    // Note that this usually happens inside `_draggingStarted` as well, but the dimensions\n    // can change when the sort strategy moves the item around inside `enter`.\n    this._cacheParentPositions();\n    // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n    this._notifyReceivingSiblings();\n    this.entered.next({\n      item,\n      container: this,\n      currentIndex: this.getItemIndex(item)\n    });\n  }\n  /**\n   * Removes an item from the container after it was dragged into another container by the user.\n   * @param item Item that was dragged out.\n   */\n  exit(item) {\n    this._reset();\n    this.exited.next({\n      item,\n      container: this\n    });\n  }\n  /**\n   * Drops an item into this container.\n   * @param item Item being dropped into the container.\n   * @param currentIndex Index at which the item should be inserted.\n   * @param previousIndex Index of the item when dragging started.\n   * @param previousContainer Container from which the item got dragged in.\n   * @param isPointerOverContainer Whether the user's pointer was over the\n   *    container when the item was dropped.\n   * @param distance Distance the user has dragged since the start of the dragging sequence.\n   * @param event Event that triggered the dropping sequence.\n   *\n   * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n   */\n  drop(item, currentIndex, previousIndex, previousContainer, isPointerOverContainer, distance, dropPoint, event = {}) {\n    this._reset();\n    this.dropped.next({\n      item,\n      currentIndex,\n      previousIndex,\n      container: this,\n      previousContainer,\n      isPointerOverContainer,\n      distance,\n      dropPoint,\n      event\n    });\n  }\n  /**\n   * Sets the draggable items that are a part of this list.\n   * @param items Items that are a part of this list.\n   */\n  withItems(items) {\n    const previousItems = this._draggables;\n    this._draggables = items;\n    items.forEach(item => item._withDropContainer(this));\n    if (this.isDragging()) {\n      const draggedItems = previousItems.filter(item => item.isDragging());\n      // If all of the items being dragged were removed\n      // from the list, abort the current drag sequence.\n      if (draggedItems.every(item => items.indexOf(item) === -1)) {\n        this._reset();\n      } else {\n        this._sortStrategy.withItems(this._draggables);\n      }\n    }\n    return this;\n  }\n  /** Sets the layout direction of the drop list. */\n  withDirection(direction) {\n    this._sortStrategy.direction = direction;\n    return this;\n  }\n  /**\n   * Sets the containers that are connected to this one. When two or more containers are\n   * connected, the user will be allowed to transfer items between them.\n   * @param connectedTo Other containers that the current containers should be connected to.\n   */\n  connectedTo(connectedTo) {\n    this._siblings = connectedTo.slice();\n    return this;\n  }\n  /**\n   * Sets the orientation of the container.\n   * @param orientation New orientation for the container.\n   */\n  withOrientation(orientation) {\n    // TODO(crisbeto): eventually we should be constructing the new sort strategy here based on\n    // the new orientation. For now we can assume that it'll always be `SingleAxisSortStrategy`.\n    this._sortStrategy.orientation = orientation;\n    return this;\n  }\n  /**\n   * Sets which parent elements are can be scrolled while the user is dragging.\n   * @param elements Elements that can be scrolled.\n   */\n  withScrollableParents(elements) {\n    const element = coerceElement(this.element);\n    // We always allow the current element to be scrollable\n    // so we need to ensure that it's in the array.\n    this._scrollableElements = elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n    return this;\n  }\n  /** Gets the scrollable parents that are registered with this drop container. */\n  getScrollableParents() {\n    return this._scrollableElements;\n  }\n  /**\n   * Figures out the index of an item in the container.\n   * @param item Item whose index should be determined.\n   */\n  getItemIndex(item) {\n    return this._isDragging ? this._sortStrategy.getItemIndex(item) : this._draggables.indexOf(item);\n  }\n  /**\n   * Whether the list is able to receive the item that\n   * is currently being dragged inside a connected drop list.\n   */\n  isReceiving() {\n    return this._activeSiblings.size > 0;\n  }\n  /**\n   * Sorts an item inside the container based on its position.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  _sortItem(item, pointerX, pointerY, pointerDelta) {\n    // Don't sort the item if sorting is disabled or it's out of range.\n    if (this.sortingDisabled || !this._domRect || !isPointerNearDomRect(this._domRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n      return;\n    }\n    const result = this._sortStrategy.sort(item, pointerX, pointerY, pointerDelta);\n    if (result) {\n      this.sorted.next({\n        previousIndex: result.previousIndex,\n        currentIndex: result.currentIndex,\n        container: this,\n        item\n      });\n    }\n  }\n  /**\n   * Checks whether the user's pointer is close to the edges of either the\n   * viewport or the drop list and starts the auto-scroll sequence.\n   * @param pointerX User's pointer position along the x axis.\n   * @param pointerY User's pointer position along the y axis.\n   */\n  _startScrollingIfNecessary(pointerX, pointerY) {\n    if (this.autoScrollDisabled) {\n      return;\n    }\n    let scrollNode;\n    let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n    let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n    // Check whether we should start scrolling any of the parent containers.\n    this._parentPositions.positions.forEach((position, element) => {\n      // We have special handling for the `document` below. Also this would be\n      // nicer with a  for...of loop, but it requires changing a compiler flag.\n      if (element === this._document || !position.clientRect || scrollNode) {\n        return;\n      }\n      if (isPointerNearDomRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n        [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(element, position.clientRect, this._sortStrategy.direction, pointerX, pointerY);\n        if (verticalScrollDirection || horizontalScrollDirection) {\n          scrollNode = element;\n        }\n      }\n    });\n    // Otherwise check if we can start scrolling the viewport.\n    if (!verticalScrollDirection && !horizontalScrollDirection) {\n      const {\n        width,\n        height\n      } = this._viewportRuler.getViewportSize();\n      const domRect = {\n        width,\n        height,\n        top: 0,\n        right: width,\n        bottom: height,\n        left: 0\n      };\n      verticalScrollDirection = getVerticalScrollDirection(domRect, pointerY);\n      horizontalScrollDirection = getHorizontalScrollDirection(domRect, pointerX);\n      scrollNode = window;\n    }\n    if (scrollNode && (verticalScrollDirection !== this._verticalScrollDirection || horizontalScrollDirection !== this._horizontalScrollDirection || scrollNode !== this._scrollNode)) {\n      this._verticalScrollDirection = verticalScrollDirection;\n      this._horizontalScrollDirection = horizontalScrollDirection;\n      this._scrollNode = scrollNode;\n      if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n        this._ngZone.runOutsideAngular(this._startScrollInterval);\n      } else {\n        this._stopScrolling();\n      }\n    }\n  }\n  /** Stops any currently-running auto-scroll sequences. */\n  _stopScrolling() {\n    this._stopScrollTimers.next();\n  }\n  /** Starts the dragging sequence within the list. */\n  _draggingStarted() {\n    const styles = coerceElement(this.element).style;\n    this.beforeStarted.next();\n    this._isDragging = true;\n    // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n    // scrolling. The browser seems to round the value based on the snapping points which means\n    // that we can't increment/decrement the scroll position.\n    this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n    styles.scrollSnapType = styles.msScrollSnapType = 'none';\n    this._sortStrategy.start(this._draggables);\n    this._cacheParentPositions();\n    this._viewportScrollSubscription.unsubscribe();\n    this._listenToScrollEvents();\n  }\n  /** Caches the positions of the configured scrollable parents. */\n  _cacheParentPositions() {\n    const element = coerceElement(this.element);\n    this._parentPositions.cache(this._scrollableElements);\n    // The list element is always in the `scrollableElements`\n    // so we can take advantage of the cached `DOMRect`.\n    this._domRect = this._parentPositions.positions.get(element).clientRect;\n  }\n  /** Resets the container to its initial state. */\n  _reset() {\n    this._isDragging = false;\n    const styles = coerceElement(this.element).style;\n    styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap;\n    this._siblings.forEach(sibling => sibling._stopReceiving(this));\n    this._sortStrategy.reset();\n    this._stopScrolling();\n    this._viewportScrollSubscription.unsubscribe();\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the user's pointer is positioned over the container.\n   * @param x Pointer position along the X axis.\n   * @param y Pointer position along the Y axis.\n   */\n  _isOverContainer(x, y) {\n    return this._domRect != null && isInsideClientRect(this._domRect, x, y);\n  }\n  /**\n   * Figures out whether an item should be moved into a sibling\n   * drop container, based on its current position.\n   * @param item Drag item that is being moved.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _getSiblingContainerFromPosition(item, x, y) {\n    return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n  }\n  /**\n   * Checks whether the drop list can receive the passed-in item.\n   * @param item Item that is being dragged into the list.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _canReceive(item, x, y) {\n    if (!this._domRect || !isInsideClientRect(this._domRect, x, y) || !this.enterPredicate(item, this)) {\n      return false;\n    }\n    const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y);\n    // If there's no element at the pointer position, then\n    // the client rect is probably scrolled out of the view.\n    if (!elementFromPoint) {\n      return false;\n    }\n    const nativeElement = coerceElement(this.element);\n    // The `DOMRect`, that we're using to find the container over which the user is\n    // hovering, doesn't give us any information on whether the element has been scrolled\n    // out of the view or whether it's overlapping with other containers. This means that\n    // we could end up transferring the item into a container that's invisible or is positioned\n    // below another one. We use the result from `elementFromPoint` to get the top-most element\n    // at the pointer position and to find whether it's one of the intersecting drop containers.\n    return elementFromPoint === nativeElement || nativeElement.contains(elementFromPoint);\n  }\n  /**\n   * Called by one of the connected drop lists when a dragging sequence has started.\n   * @param sibling Sibling in which dragging has started.\n   */\n  _startReceiving(sibling, items) {\n    const activeSiblings = this._activeSiblings;\n    if (!activeSiblings.has(sibling) && items.every(item => {\n      // Note that we have to add an exception to the `enterPredicate` for items that started off\n      // in this drop list. The drag ref has logic that allows an item to return to its initial\n      // container, if it has left the initial container and none of the connected containers\n      // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n      return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n    })) {\n      activeSiblings.add(sibling);\n      this._cacheParentPositions();\n      this._listenToScrollEvents();\n      this.receivingStarted.next({\n        initiator: sibling,\n        receiver: this,\n        items\n      });\n    }\n  }\n  /**\n   * Called by a connected drop list when dragging has stopped.\n   * @param sibling Sibling whose dragging has stopped.\n   */\n  _stopReceiving(sibling) {\n    this._activeSiblings.delete(sibling);\n    this._viewportScrollSubscription.unsubscribe();\n    this.receivingStopped.next({\n      initiator: sibling,\n      receiver: this\n    });\n  }\n  /**\n   * Starts listening to scroll events on the viewport.\n   * Used for updating the internal state of the list.\n   */\n  _listenToScrollEvents() {\n    this._viewportScrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(event => {\n      if (this.isDragging()) {\n        const scrollDifference = this._parentPositions.handleScroll(event);\n        if (scrollDifference) {\n          this._sortStrategy.updateOnScroll(scrollDifference.top, scrollDifference.left);\n        }\n      } else if (this.isReceiving()) {\n        this._cacheParentPositions();\n      }\n    });\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  _getShadowRoot() {\n    if (!this._cachedShadowRoot) {\n      const shadowRoot = _getShadowRoot(coerceElement(this.element));\n      this._cachedShadowRoot = shadowRoot || this._document;\n    }\n    return this._cachedShadowRoot;\n  }\n  /** Notifies any siblings that may potentially receive the item. */\n  _notifyReceivingSiblings() {\n    const draggedItems = this._sortStrategy.getActiveItemsSnapshot().filter(item => item.isDragging());\n    this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n  }\n}\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getVerticalScrollDirection(clientRect, pointerY) {\n  const {\n    top,\n    bottom,\n    height\n  } = clientRect;\n  const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n  if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n    return AutoScrollVerticalDirection.UP;\n  } else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n    return AutoScrollVerticalDirection.DOWN;\n  }\n  return AutoScrollVerticalDirection.NONE;\n}\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\nfunction getHorizontalScrollDirection(clientRect, pointerX) {\n  const {\n    left,\n    right,\n    width\n  } = clientRect;\n  const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n  if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n    return AutoScrollHorizontalDirection.LEFT;\n  } else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n    return AutoScrollHorizontalDirection.RIGHT;\n  }\n  return AutoScrollHorizontalDirection.NONE;\n}\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param direction Layout direction of the drop list.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getElementScrollDirections(element, clientRect, direction, pointerX, pointerY) {\n  const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n  const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n  let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n  let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n  // Note that we here we do some extra checks for whether the element is actually scrollable in\n  // a certain direction and we only assign the scroll direction if it is. We do this so that we\n  // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n  // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n  if (computedVertical) {\n    const scrollTop = element.scrollTop;\n    if (computedVertical === AutoScrollVerticalDirection.UP) {\n      if (scrollTop > 0) {\n        verticalScrollDirection = AutoScrollVerticalDirection.UP;\n      }\n    } else if (element.scrollHeight - scrollTop > element.clientHeight) {\n      verticalScrollDirection = AutoScrollVerticalDirection.DOWN;\n    }\n  }\n  if (computedHorizontal) {\n    const scrollLeft = element.scrollLeft;\n    if (direction === 'rtl') {\n      if (computedHorizontal === AutoScrollHorizontalDirection.RIGHT) {\n        // In RTL `scrollLeft` will be negative when scrolled.\n        if (scrollLeft < 0) {\n          horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n        }\n      } else if (element.scrollWidth + scrollLeft > element.clientWidth) {\n        horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n      }\n    } else {\n      if (computedHorizontal === AutoScrollHorizontalDirection.LEFT) {\n        if (scrollLeft > 0) {\n          horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n        }\n      } else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n        horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n      }\n    }\n  }\n  return [verticalScrollDirection, horizontalScrollDirection];\n}\n\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: false,\n  capture: true\n});\n/** Keeps track of the apps currently containing drag items. */\nconst activeApps = new Set();\n/**\n * Component used to load the drag&drop reset styles.\n * @docs-private\n */\nclass _ResetsLoader {\n  static {\n    this.ɵfac = function _ResetsLoader_Factory(t) {\n      return new (t || _ResetsLoader)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _ResetsLoader,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [\"cdk-drag-resets-container\", \"\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function _ResetsLoader_Template(rf, ctx) {},\n      styles: [\"@layer cdk-resets{.cdk-drag-preview{background:none;border:none;padding:0;color:inherit}}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_ResetsLoader, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'cdk-drag-resets-container': ''\n      },\n      styles: [\"@layer cdk-resets{.cdk-drag-preview{background:none;border:none;padding:0;color:inherit}}\"]\n    }]\n  }], null, null);\n})();\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\n// Note: this class is generic, rather than referencing CdkDrag and CdkDropList directly, in order\n// to avoid circular imports. If we were to reference them here, importing the registry into the\n// classes that are registering themselves will introduce a circular import.\nclass DragDropRegistry {\n  constructor(_ngZone, _document) {\n    this._ngZone = _ngZone;\n    this._appRef = inject(ApplicationRef);\n    this._environmentInjector = inject(EnvironmentInjector);\n    /** Registered drop container instances. */\n    this._dropInstances = new Set();\n    /** Registered drag item instances. */\n    this._dragInstances = new Set();\n    /** Drag item instances that are currently being dragged. */\n    this._activeDragInstances = [];\n    /** Keeps track of the event listeners that we've bound to the `document`. */\n    this._globalListeners = new Map();\n    /**\n     * Predicate function to check if an item is being dragged.  Moved out into a property,\n     * because it'll be called a lot and we don't want to create a new function every time.\n     */\n    this._draggingPredicate = item => item.isDragging();\n    /**\n     * Emits the `touchmove` or `mousemove` events that are dispatched\n     * while the user is dragging a drag item instance.\n     */\n    this.pointerMove = new Subject();\n    /**\n     * Emits the `touchend` or `mouseup` events that are dispatched\n     * while the user is dragging a drag item instance.\n     */\n    this.pointerUp = new Subject();\n    /**\n     * Emits when the viewport has been scrolled while the user is dragging an item.\n     * @deprecated To be turned into a private member. Use the `scrolled` method instead.\n     * @breaking-change 13.0.0\n     */\n    this.scroll = new Subject();\n    /**\n     * Event listener that will prevent the default browser action while the user is dragging.\n     * @param event Event whose default action should be prevented.\n     */\n    this._preventDefaultWhileDragging = event => {\n      if (this._activeDragInstances.length > 0) {\n        event.preventDefault();\n      }\n    };\n    /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n    this._persistentTouchmoveListener = event => {\n      if (this._activeDragInstances.length > 0) {\n        // Note that we only want to prevent the default action after dragging has actually started.\n        // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n        // but it could be pushed back if the user has set up a drag delay or threshold.\n        if (this._activeDragInstances.some(this._draggingPredicate)) {\n          event.preventDefault();\n        }\n        this.pointerMove.next(event);\n      }\n    };\n    this._document = _document;\n  }\n  /** Adds a drop container to the registry. */\n  registerDropContainer(drop) {\n    if (!this._dropInstances.has(drop)) {\n      this._dropInstances.add(drop);\n    }\n  }\n  /** Adds a drag item instance to the registry. */\n  registerDragItem(drag) {\n    this._dragInstances.add(drag);\n    // The `touchmove` event gets bound once, ahead of time, because WebKit\n    // won't preventDefault on a dynamically-added `touchmove` listener.\n    // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n    if (this._dragInstances.size === 1) {\n      this._ngZone.runOutsideAngular(() => {\n        // The event handler has to be explicitly active,\n        // because newer browsers make it passive by default.\n        this._document.addEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n      });\n    }\n  }\n  /** Removes a drop container from the registry. */\n  removeDropContainer(drop) {\n    this._dropInstances.delete(drop);\n  }\n  /** Removes a drag item instance from the registry. */\n  removeDragItem(drag) {\n    this._dragInstances.delete(drag);\n    this.stopDragging(drag);\n    if (this._dragInstances.size === 0) {\n      this._document.removeEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n    }\n  }\n  /**\n   * Starts the dragging sequence for a drag instance.\n   * @param drag Drag instance which is being dragged.\n   * @param event Event that initiated the dragging.\n   */\n  startDragging(drag, event) {\n    // Do not process the same drag twice to avoid memory leaks and redundant listeners\n    if (this._activeDragInstances.indexOf(drag) > -1) {\n      return;\n    }\n    this._loadResets();\n    this._activeDragInstances.push(drag);\n    if (this._activeDragInstances.length === 1) {\n      const isTouchEvent = event.type.startsWith('touch');\n      // We explicitly bind __active__ listeners here, because newer browsers will default to\n      // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n      // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n      this._globalListeners.set(isTouchEvent ? 'touchend' : 'mouseup', {\n        handler: e => this.pointerUp.next(e),\n        options: true\n      }).set('scroll', {\n        handler: e => this.scroll.next(e),\n        // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n        // the document. See https://github.com/angular/components/issues/17144.\n        options: true\n      })\n      // Preventing the default action on `mousemove` isn't enough to disable text selection\n      // on Safari so we need to prevent the selection event as well. Alternatively this can\n      // be done by setting `user-select: none` on the `body`, however it has causes a style\n      // recalculation which can be expensive on pages with a lot of elements.\n      .set('selectstart', {\n        handler: this._preventDefaultWhileDragging,\n        options: activeCapturingEventOptions\n      });\n      // We don't have to bind a move event for touch drag sequences, because\n      // we already have a persistent global one bound from `registerDragItem`.\n      if (!isTouchEvent) {\n        this._globalListeners.set('mousemove', {\n          handler: e => this.pointerMove.next(e),\n          options: activeCapturingEventOptions\n        });\n      }\n      this._ngZone.runOutsideAngular(() => {\n        this._globalListeners.forEach((config, name) => {\n          this._document.addEventListener(name, config.handler, config.options);\n        });\n      });\n    }\n  }\n  /** Stops dragging a drag item instance. */\n  stopDragging(drag) {\n    const index = this._activeDragInstances.indexOf(drag);\n    if (index > -1) {\n      this._activeDragInstances.splice(index, 1);\n      if (this._activeDragInstances.length === 0) {\n        this._clearGlobalListeners();\n      }\n    }\n  }\n  /** Gets whether a drag item instance is currently being dragged. */\n  isDragging(drag) {\n    return this._activeDragInstances.indexOf(drag) > -1;\n  }\n  /**\n   * Gets a stream that will emit when any element on the page is scrolled while an item is being\n   * dragged.\n   * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n   *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n   *   be used to include an additional top-level listener at the shadow root level.\n   */\n  scrolled(shadowRoot) {\n    const streams = [this.scroll];\n    if (shadowRoot && shadowRoot !== this._document) {\n      // Note that this is basically the same as `fromEvent` from rxjs, but we do it ourselves,\n      // because we want to guarantee that the event is bound outside of the `NgZone`. With\n      // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n      streams.push(new Observable(observer => {\n        return this._ngZone.runOutsideAngular(() => {\n          const eventOptions = true;\n          const callback = event => {\n            if (this._activeDragInstances.length) {\n              observer.next(event);\n            }\n          };\n          shadowRoot.addEventListener('scroll', callback, eventOptions);\n          return () => {\n            shadowRoot.removeEventListener('scroll', callback, eventOptions);\n          };\n        });\n      }));\n    }\n    return merge(...streams);\n  }\n  ngOnDestroy() {\n    this._dragInstances.forEach(instance => this.removeDragItem(instance));\n    this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n    this._clearGlobalListeners();\n    this.pointerMove.complete();\n    this.pointerUp.complete();\n  }\n  /** Clears out the global event listeners from the `document`. */\n  _clearGlobalListeners() {\n    this._globalListeners.forEach((config, name) => {\n      this._document.removeEventListener(name, config.handler, config.options);\n    });\n    this._globalListeners.clear();\n  }\n  // TODO(crisbeto): abstract this away into something reusable.\n  /** Loads the CSS resets needed for the module to work correctly. */\n  _loadResets() {\n    if (!activeApps.has(this._appRef)) {\n      activeApps.add(this._appRef);\n      const componentRef = createComponent(_ResetsLoader, {\n        environmentInjector: this._environmentInjector\n      });\n      this._appRef.onDestroy(() => {\n        activeApps.delete(this._appRef);\n        if (activeApps.size === 0) {\n          componentRef.destroy();\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function DragDropRegistry_Factory(t) {\n      return new (t || DragDropRegistry)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DragDropRegistry,\n      factory: DragDropRegistry.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDropRegistry, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/** Default configuration to be used when creating a `DragRef`. */\nconst DEFAULT_CONFIG = {\n  dragStartThreshold: 5,\n  pointerDirectionChangeThreshold: 5\n};\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\nclass DragDrop {\n  constructor(_document, _ngZone, _viewportRuler, _dragDropRegistry) {\n    this._document = _document;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._dragDropRegistry = _dragDropRegistry;\n  }\n  /**\n   * Turns an element into a draggable item.\n   * @param element Element to which to attach the dragging functionality.\n   * @param config Object used to configure the dragging behavior.\n   */\n  createDrag(element, config = DEFAULT_CONFIG) {\n    return new DragRef(element, config, this._document, this._ngZone, this._viewportRuler, this._dragDropRegistry);\n  }\n  /**\n   * Turns an element into a drop list.\n   * @param element Element to which to attach the drop list functionality.\n   */\n  createDropList(element) {\n    return new DropListRef(element, this._dragDropRegistry, this._document, this._ngZone, this._viewportRuler);\n  }\n  static {\n    this.ɵfac = function DragDrop_Factory(t) {\n      return new (t || DragDrop)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(DragDropRegistry));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DragDrop,\n      factory: DragDrop.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDrop, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1.ViewportRuler\n  }, {\n    type: DragDropRegistry\n  }], null);\n})();\n\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\nconst CDK_DRAG_PARENT = new InjectionToken('CDK_DRAG_PARENT');\n\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\nfunction assertElementNode(node, name) {\n  if (node.nodeType !== 1) {\n    throw Error(`${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`);\n  }\n}\n\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_HANDLE = new InjectionToken('CdkDragHandle');\n/** Handle that can be used to drag a CdkDrag instance. */\nclass CdkDragHandle {\n  /** Whether starting to drag through this handle is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._stateChanges.next(this);\n  }\n  constructor(element, _parentDrag) {\n    this.element = element;\n    this._parentDrag = _parentDrag;\n    /** Emits when the state of the handle has changed. */\n    this._stateChanges = new Subject();\n    this._disabled = false;\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(element.nativeElement, 'cdkDragHandle');\n    }\n    _parentDrag?._addHandle(this);\n  }\n  ngOnDestroy() {\n    this._parentDrag?._removeHandle(this);\n    this._stateChanges.complete();\n  }\n  static {\n    this.ɵfac = function CdkDragHandle_Factory(t) {\n      return new (t || CdkDragHandle)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDragHandle,\n      selectors: [[\"\", \"cdkDragHandle\", \"\"]],\n      hostAttrs: [1, \"cdk-drag-handle\"],\n      inputs: {\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkDragHandleDisabled\", \"disabled\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DRAG_HANDLE,\n        useExisting: CdkDragHandle\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragHandle, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDragHandle]',\n      standalone: true,\n      host: {\n        'class': 'cdk-drag-handle'\n      },\n      providers: [{\n        provide: CDK_DRAG_HANDLE,\n        useExisting: CdkDragHandle\n      }]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_DRAG_PARENT]\n    }, {\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }], {\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDragHandleDisabled',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\nconst CDK_DRAG_CONFIG = new InjectionToken('CDK_DRAG_CONFIG');\nconst DRAG_HOST_CLASS = 'cdk-drag';\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST = new InjectionToken('CdkDropList');\n/** Element that can be moved inside a CdkDropList container. */\nclass CdkDrag {\n  static {\n    this._dragInstances = [];\n  }\n  /** Whether starting to drag this element is disabled. */\n  get disabled() {\n    return this._disabled || this.dropContainer && this.dropContainer.disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._dragRef.disabled = this._disabled;\n  }\n  constructor(/** Element that the draggable is attached to. */\n  element, /** Droppable container that the draggable is a part of. */\n  dropContainer,\n  /**\n   * @deprecated `_document` parameter no longer being used and will be removed.\n   * @breaking-change 12.0.0\n   */\n  _document, _ngZone, _viewContainerRef, config, _dir, dragDrop, _changeDetectorRef, _selfHandle, _parentDrag) {\n    this.element = element;\n    this.dropContainer = dropContainer;\n    this._ngZone = _ngZone;\n    this._viewContainerRef = _viewContainerRef;\n    this._dir = _dir;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._selfHandle = _selfHandle;\n    this._parentDrag = _parentDrag;\n    this._destroyed = new Subject();\n    this._handles = new BehaviorSubject([]);\n    /** Emits when the user starts dragging the item. */\n    this.started = new EventEmitter();\n    /** Emits when the user has released a drag item, before any animations have started. */\n    this.released = new EventEmitter();\n    /** Emits when the user stops dragging an item in the container. */\n    this.ended = new EventEmitter();\n    /** Emits when the user has moved the item into a new container. */\n    this.entered = new EventEmitter();\n    /** Emits when the user removes the item its container by dragging it into another container. */\n    this.exited = new EventEmitter();\n    /** Emits when the user drops the item inside a container. */\n    this.dropped = new EventEmitter();\n    /**\n     * Emits as the user is dragging the item. Use with caution,\n     * because this event will fire for every pixel that the user has dragged.\n     */\n    this.moved = new Observable(observer => {\n      const subscription = this._dragRef.moved.pipe(map(movedEvent => ({\n        source: this,\n        pointerPosition: movedEvent.pointerPosition,\n        event: movedEvent.event,\n        delta: movedEvent.delta,\n        distance: movedEvent.distance\n      }))).subscribe(observer);\n      return () => {\n        subscription.unsubscribe();\n      };\n    });\n    this._dragRef = dragDrop.createDrag(element, {\n      dragStartThreshold: config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n      pointerDirectionChangeThreshold: config && config.pointerDirectionChangeThreshold != null ? config.pointerDirectionChangeThreshold : 5,\n      zIndex: config?.zIndex\n    });\n    this._dragRef.data = this;\n    // We have to keep track of the drag instances in order to be able to match an element to\n    // a drag instance. We can't go through the global registry of `DragRef`, because the root\n    // element could be different.\n    CdkDrag._dragInstances.push(this);\n    if (config) {\n      this._assignDefaults(config);\n    }\n    // Note that usually the container is assigned when the drop list is picks up the item, but in\n    // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n    // where there are no items on the first change detection pass, but the items get picked up as\n    // soon as the user triggers another pass by dragging. This is a problem, because the item would\n    // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n    // is too late since the two modes save different kinds of information. We work around it by\n    // assigning the drop container both from here and the list.\n    if (dropContainer) {\n      this._dragRef._withDropContainer(dropContainer._dropListRef);\n      dropContainer.addItem(this);\n    }\n    this._syncInputs(this._dragRef);\n    this._handleEvents(this._dragRef);\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement() {\n    return this._dragRef.getPlaceholderElement();\n  }\n  /** Returns the root draggable element. */\n  getRootElement() {\n    return this._dragRef.getRootElement();\n  }\n  /** Resets a standalone drag item to its initial position. */\n  reset() {\n    this._dragRef.reset();\n  }\n  /**\n   * Gets the pixel coordinates of the draggable outside of a drop container.\n   */\n  getFreeDragPosition() {\n    return this._dragRef.getFreeDragPosition();\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value) {\n    this._dragRef.setFreeDragPosition(value);\n  }\n  ngAfterViewInit() {\n    // Normally this isn't in the zone, but it can cause major performance regressions for apps\n    // using `zone-patch-rxjs` because it'll trigger a change detection when it unsubscribes.\n    this._ngZone.runOutsideAngular(() => {\n      // We need to wait for the zone to stabilize, in order for the reference\n      // element to be in the proper place in the DOM. This is mostly relevant\n      // for draggable elements inside portals since they get stamped out in\n      // their original DOM position and then they get transferred to the portal.\n      this._ngZone.onStable.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n        this._updateRootElement();\n        this._setupHandlesListener();\n        if (this.freeDragPosition) {\n          this._dragRef.setFreeDragPosition(this.freeDragPosition);\n        }\n      });\n    });\n  }\n  ngOnChanges(changes) {\n    const rootSelectorChange = changes['rootElementSelector'];\n    const positionChange = changes['freeDragPosition'];\n    // We don't have to react to the first change since it's being\n    // handled in `ngAfterViewInit` where it needs to be deferred.\n    if (rootSelectorChange && !rootSelectorChange.firstChange) {\n      this._updateRootElement();\n    }\n    // Skip the first change since it's being handled in `ngAfterViewInit`.\n    if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n      this._dragRef.setFreeDragPosition(this.freeDragPosition);\n    }\n  }\n  ngOnDestroy() {\n    if (this.dropContainer) {\n      this.dropContainer.removeItem(this);\n    }\n    const index = CdkDrag._dragInstances.indexOf(this);\n    if (index > -1) {\n      CdkDrag._dragInstances.splice(index, 1);\n    }\n    // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n    this._ngZone.runOutsideAngular(() => {\n      this._handles.complete();\n      this._destroyed.next();\n      this._destroyed.complete();\n      this._dragRef.dispose();\n    });\n  }\n  _addHandle(handle) {\n    const handles = this._handles.getValue();\n    handles.push(handle);\n    this._handles.next(handles);\n  }\n  _removeHandle(handle) {\n    const handles = this._handles.getValue();\n    const index = handles.indexOf(handle);\n    if (index > -1) {\n      handles.splice(index, 1);\n      this._handles.next(handles);\n    }\n  }\n  _setPreviewTemplate(preview) {\n    this._previewTemplate = preview;\n  }\n  _resetPreviewTemplate(preview) {\n    if (preview === this._previewTemplate) {\n      this._previewTemplate = null;\n    }\n  }\n  _setPlaceholderTemplate(placeholder) {\n    this._placeholderTemplate = placeholder;\n  }\n  _resetPlaceholderTemplate(placeholder) {\n    if (placeholder === this._placeholderTemplate) {\n      this._placeholderTemplate = null;\n    }\n  }\n  /** Syncs the root element with the `DragRef`. */\n  _updateRootElement() {\n    const element = this.element.nativeElement;\n    let rootElement = element;\n    if (this.rootElementSelector) {\n      rootElement = element.closest !== undefined ? element.closest(this.rootElementSelector) :\n      // Comment tag doesn't have closest method, so use parent's one.\n      element.parentElement?.closest(this.rootElementSelector);\n    }\n    if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      assertElementNode(rootElement, 'cdkDrag');\n    }\n    this._dragRef.withRootElement(rootElement || element);\n  }\n  /** Gets the boundary element, based on the `boundaryElement` value. */\n  _getBoundaryElement() {\n    const boundary = this.boundaryElement;\n    if (!boundary) {\n      return null;\n    }\n    if (typeof boundary === 'string') {\n      return this.element.nativeElement.closest(boundary);\n    }\n    return coerceElement(boundary);\n  }\n  /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n  _syncInputs(ref) {\n    ref.beforeStarted.subscribe(() => {\n      if (!ref.isDragging()) {\n        const dir = this._dir;\n        const dragStartDelay = this.dragStartDelay;\n        const placeholder = this._placeholderTemplate ? {\n          template: this._placeholderTemplate.templateRef,\n          context: this._placeholderTemplate.data,\n          viewContainer: this._viewContainerRef\n        } : null;\n        const preview = this._previewTemplate ? {\n          template: this._previewTemplate.templateRef,\n          context: this._previewTemplate.data,\n          matchSize: this._previewTemplate.matchSize,\n          viewContainer: this._viewContainerRef\n        } : null;\n        ref.disabled = this.disabled;\n        ref.lockAxis = this.lockAxis;\n        ref.dragStartDelay = typeof dragStartDelay === 'object' && dragStartDelay ? dragStartDelay : coerceNumberProperty(dragStartDelay);\n        ref.constrainPosition = this.constrainPosition;\n        ref.previewClass = this.previewClass;\n        ref.withBoundaryElement(this._getBoundaryElement()).withPlaceholderTemplate(placeholder).withPreviewTemplate(preview).withPreviewContainer(this.previewContainer || 'global');\n        if (dir) {\n          ref.withDirection(dir.value);\n        }\n      }\n    });\n    // This only needs to be resolved once.\n    ref.beforeStarted.pipe(take(1)).subscribe(() => {\n      // If we managed to resolve a parent through DI, use it.\n      if (this._parentDrag) {\n        ref.withParent(this._parentDrag._dragRef);\n        return;\n      }\n      // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n      // the item was projected into another item by something like `ngTemplateOutlet`.\n      let parent = this.element.nativeElement.parentElement;\n      while (parent) {\n        if (parent.classList.contains(DRAG_HOST_CLASS)) {\n          ref.withParent(CdkDrag._dragInstances.find(drag => {\n            return drag.element.nativeElement === parent;\n          })?._dragRef || null);\n          break;\n        }\n        parent = parent.parentElement;\n      }\n    });\n  }\n  /** Handles the events from the underlying `DragRef`. */\n  _handleEvents(ref) {\n    ref.started.subscribe(startEvent => {\n      this.started.emit({\n        source: this,\n        event: startEvent.event\n      });\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.released.subscribe(releaseEvent => {\n      this.released.emit({\n        source: this,\n        event: releaseEvent.event\n      });\n    });\n    ref.ended.subscribe(endEvent => {\n      this.ended.emit({\n        source: this,\n        distance: endEvent.distance,\n        dropPoint: endEvent.dropPoint,\n        event: endEvent.event\n      });\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.entered.subscribe(enterEvent => {\n      this.entered.emit({\n        container: enterEvent.container.data,\n        item: this,\n        currentIndex: enterEvent.currentIndex\n      });\n    });\n    ref.exited.subscribe(exitEvent => {\n      this.exited.emit({\n        container: exitEvent.container.data,\n        item: this\n      });\n    });\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        item: this,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event\n      });\n    });\n  }\n  /** Assigns the default input values based on a provided config object. */\n  _assignDefaults(config) {\n    const {\n      lockAxis,\n      dragStartDelay,\n      constrainPosition,\n      previewClass,\n      boundaryElement,\n      draggingDisabled,\n      rootElementSelector,\n      previewContainer\n    } = config;\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.dragStartDelay = dragStartDelay || 0;\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n    if (constrainPosition) {\n      this.constrainPosition = constrainPosition;\n    }\n    if (previewClass) {\n      this.previewClass = previewClass;\n    }\n    if (boundaryElement) {\n      this.boundaryElement = boundaryElement;\n    }\n    if (rootElementSelector) {\n      this.rootElementSelector = rootElementSelector;\n    }\n    if (previewContainer) {\n      this.previewContainer = previewContainer;\n    }\n  }\n  /** Sets up the listener that syncs the handles with the drag ref. */\n  _setupHandlesListener() {\n    // Listen for any newly-added handles.\n    this._handles.pipe(\n    // Sync the new handles with the DragRef.\n    tap(handles => {\n      const handleElements = handles.map(handle => handle.element);\n      // Usually handles are only allowed to be a descendant of the drag element, but if\n      // the consumer defined a different drag root, we should allow the drag element\n      // itself to be a handle too.\n      if (this._selfHandle && this.rootElementSelector) {\n        handleElements.push(this.element);\n      }\n      this._dragRef.withHandles(handleElements);\n    }),\n    // Listen if the state of any of the handles changes.\n    switchMap(handles => {\n      return merge(...handles.map(item => item._stateChanges.pipe(startWith(item))));\n    }), takeUntil(this._destroyed)).subscribe(handleInstance => {\n      // Enabled/disable the handle that changed in the DragRef.\n      const dragRef = this._dragRef;\n      const handle = handleInstance.element.nativeElement;\n      handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n    });\n  }\n  static {\n    this.ɵfac = function CdkDrag_Factory(t) {\n      return new (t || CdkDrag)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DROP_LIST, 12), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8), i0.ɵɵdirectiveInject(i1$1.Directionality, 8), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(CDK_DRAG_HANDLE, 10), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDrag,\n      selectors: [[\"\", \"cdkDrag\", \"\"]],\n      hostAttrs: [1, \"cdk-drag\"],\n      hostVars: 4,\n      hostBindings: function CdkDrag_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"cdk-drag-disabled\", ctx.disabled)(\"cdk-drag-dragging\", ctx._dragRef.isDragging());\n        }\n      },\n      inputs: {\n        data: [i0.ɵɵInputFlags.None, \"cdkDragData\", \"data\"],\n        lockAxis: [i0.ɵɵInputFlags.None, \"cdkDragLockAxis\", \"lockAxis\"],\n        rootElementSelector: [i0.ɵɵInputFlags.None, \"cdkDragRootElement\", \"rootElementSelector\"],\n        boundaryElement: [i0.ɵɵInputFlags.None, \"cdkDragBoundary\", \"boundaryElement\"],\n        dragStartDelay: [i0.ɵɵInputFlags.None, \"cdkDragStartDelay\", \"dragStartDelay\"],\n        freeDragPosition: [i0.ɵɵInputFlags.None, \"cdkDragFreeDragPosition\", \"freeDragPosition\"],\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkDragDisabled\", \"disabled\", booleanAttribute],\n        constrainPosition: [i0.ɵɵInputFlags.None, \"cdkDragConstrainPosition\", \"constrainPosition\"],\n        previewClass: [i0.ɵɵInputFlags.None, \"cdkDragPreviewClass\", \"previewClass\"],\n        previewContainer: [i0.ɵɵInputFlags.None, \"cdkDragPreviewContainer\", \"previewContainer\"]\n      },\n      outputs: {\n        started: \"cdkDragStarted\",\n        released: \"cdkDragReleased\",\n        ended: \"cdkDragEnded\",\n        entered: \"cdkDragEntered\",\n        exited: \"cdkDragExited\",\n        dropped: \"cdkDragDropped\",\n        moved: \"cdkDragMoved\"\n      },\n      exportAs: [\"cdkDrag\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DRAG_PARENT,\n        useExisting: CdkDrag\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDrag, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDrag]',\n      exportAs: 'cdkDrag',\n      standalone: true,\n      host: {\n        'class': DRAG_HOST_CLASS,\n        '[class.cdk-drag-disabled]': 'disabled',\n        '[class.cdk-drag-dragging]': '_dragRef.isDragging()'\n      },\n      providers: [{\n        provide: CDK_DRAG_PARENT,\n        useExisting: CdkDrag\n      }]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_DROP_LIST]\n    }, {\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CDK_DRAG_CONFIG]\n    }]\n  }, {\n    type: i1$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: DragDrop\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: CdkDragHandle,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [CDK_DRAG_HANDLE]\n    }]\n  }, {\n    type: CdkDrag,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }, {\n      type: Inject,\n      args: [CDK_DRAG_PARENT]\n    }]\n  }], {\n    data: [{\n      type: Input,\n      args: ['cdkDragData']\n    }],\n    lockAxis: [{\n      type: Input,\n      args: ['cdkDragLockAxis']\n    }],\n    rootElementSelector: [{\n      type: Input,\n      args: ['cdkDragRootElement']\n    }],\n    boundaryElement: [{\n      type: Input,\n      args: ['cdkDragBoundary']\n    }],\n    dragStartDelay: [{\n      type: Input,\n      args: ['cdkDragStartDelay']\n    }],\n    freeDragPosition: [{\n      type: Input,\n      args: ['cdkDragFreeDragPosition']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDragDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    constrainPosition: [{\n      type: Input,\n      args: ['cdkDragConstrainPosition']\n    }],\n    previewClass: [{\n      type: Input,\n      args: ['cdkDragPreviewClass']\n    }],\n    previewContainer: [{\n      type: Input,\n      args: ['cdkDragPreviewContainer']\n    }],\n    started: [{\n      type: Output,\n      args: ['cdkDragStarted']\n    }],\n    released: [{\n      type: Output,\n      args: ['cdkDragReleased']\n    }],\n    ended: [{\n      type: Output,\n      args: ['cdkDragEnded']\n    }],\n    entered: [{\n      type: Output,\n      args: ['cdkDragEntered']\n    }],\n    exited: [{\n      type: Output,\n      args: ['cdkDragExited']\n    }],\n    dropped: [{\n      type: Output,\n      args: ['cdkDragDropped']\n    }],\n    moved: [{\n      type: Output,\n      args: ['cdkDragMoved']\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST_GROUP = new InjectionToken('CdkDropListGroup');\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\nclass CdkDropListGroup {\n  constructor() {\n    /** Drop lists registered inside the group. */\n    this._items = new Set();\n    /** Whether starting a dragging sequence from inside this group is disabled. */\n    this.disabled = false;\n  }\n  ngOnDestroy() {\n    this._items.clear();\n  }\n  static {\n    this.ɵfac = function CdkDropListGroup_Factory(t) {\n      return new (t || CdkDropListGroup)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDropListGroup,\n      selectors: [[\"\", \"cdkDropListGroup\", \"\"]],\n      inputs: {\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkDropListGroupDisabled\", \"disabled\", booleanAttribute]\n      },\n      exportAs: [\"cdkDropListGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DROP_LIST_GROUP,\n        useExisting: CdkDropListGroup\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDropListGroup, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDropListGroup]',\n      exportAs: 'cdkDropListGroup',\n      standalone: true,\n      providers: [{\n        provide: CDK_DROP_LIST_GROUP,\n        useExisting: CdkDropListGroup\n      }]\n    }]\n  }], null, {\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListGroupDisabled',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Counter used to generate unique ids for drop zones. */\nlet _uniqueIdCounter = 0;\n/** Container that wraps a set of draggable items. */\nclass CdkDropList {\n  /** Keeps track of the drop lists that are currently on the page. */\n  static {\n    this._dropLists = [];\n  }\n  /** Whether starting a dragging sequence from this container is disabled. */\n  get disabled() {\n    return this._disabled || !!this._group && this._group.disabled;\n  }\n  set disabled(value) {\n    // Usually we sync the directive and ref state right before dragging starts, in order to have\n    // a single point of failure and to avoid having to use setters for everything. `disabled` is\n    // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n    // the user in a disabled state, so we also need to sync it as it's being set.\n    this._dropListRef.disabled = this._disabled = value;\n  }\n  constructor(/** Element that the drop list is attached to. */\n  element, dragDrop, _changeDetectorRef, _scrollDispatcher, _dir, _group, config) {\n    this.element = element;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._scrollDispatcher = _scrollDispatcher;\n    this._dir = _dir;\n    this._group = _group;\n    /** Emits when the list has been destroyed. */\n    this._destroyed = new Subject();\n    /**\n     * Other draggable containers that this container is connected to and into which the\n     * container's items can be transferred. Can either be references to other drop containers,\n     * or their unique IDs.\n     */\n    this.connectedTo = [];\n    /**\n     * Unique ID for the drop zone. Can be used as a reference\n     * in the `connectedTo` of another `CdkDropList`.\n     */\n    this.id = `cdk-drop-list-${_uniqueIdCounter++}`;\n    /**\n     * Function that is used to determine whether an item\n     * is allowed to be moved into a drop container.\n     */\n    this.enterPredicate = () => true;\n    /** Functions that is used to determine whether an item can be sorted into a particular index. */\n    this.sortPredicate = () => true;\n    /** Emits when the user drops an item inside the container. */\n    this.dropped = new EventEmitter();\n    /**\n     * Emits when the user has moved a new drag item into this container.\n     */\n    this.entered = new EventEmitter();\n    /**\n     * Emits when the user removes an item from the container\n     * by dragging it into another container.\n     */\n    this.exited = new EventEmitter();\n    /** Emits as the user is swapping items while actively dragging. */\n    this.sorted = new EventEmitter();\n    /**\n     * Keeps track of the items that are registered with this container. Historically we used to\n     * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n     * well which means that we can't handle cases like dragging the headers of a `mat-table`\n     * correctly. What we do instead is to have the items register themselves with the container\n     * and then we sort them based on their position in the DOM.\n     */\n    this._unsortedItems = new Set();\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(element.nativeElement, 'cdkDropList');\n    }\n    this._dropListRef = dragDrop.createDropList(element);\n    this._dropListRef.data = this;\n    if (config) {\n      this._assignDefaults(config);\n    }\n    this._dropListRef.enterPredicate = (drag, drop) => {\n      return this.enterPredicate(drag.data, drop.data);\n    };\n    this._dropListRef.sortPredicate = (index, drag, drop) => {\n      return this.sortPredicate(index, drag.data, drop.data);\n    };\n    this._setupInputSyncSubscription(this._dropListRef);\n    this._handleEvents(this._dropListRef);\n    CdkDropList._dropLists.push(this);\n    if (_group) {\n      _group._items.add(this);\n    }\n  }\n  /** Registers an items with the drop list. */\n  addItem(item) {\n    this._unsortedItems.add(item);\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef();\n    }\n  }\n  /** Removes an item from the drop list. */\n  removeItem(item) {\n    this._unsortedItems.delete(item);\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef();\n    }\n  }\n  /** Gets the registered items in the list, sorted by their position in the DOM. */\n  getSortedItems() {\n    return Array.from(this._unsortedItems).sort((a, b) => {\n      const documentPosition = a._dragRef.getVisibleElement().compareDocumentPosition(b._dragRef.getVisibleElement());\n      // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n      // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n      // tslint:disable-next-line:no-bitwise\n      return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n    });\n  }\n  ngOnDestroy() {\n    const index = CdkDropList._dropLists.indexOf(this);\n    if (index > -1) {\n      CdkDropList._dropLists.splice(index, 1);\n    }\n    if (this._group) {\n      this._group._items.delete(this);\n    }\n    this._unsortedItems.clear();\n    this._dropListRef.dispose();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n  _setupInputSyncSubscription(ref) {\n    if (this._dir) {\n      this._dir.change.pipe(startWith(this._dir.value), takeUntil(this._destroyed)).subscribe(value => ref.withDirection(value));\n    }\n    ref.beforeStarted.subscribe(() => {\n      const siblings = coerceArray(this.connectedTo).map(drop => {\n        if (typeof drop === 'string') {\n          const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n          if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n          }\n          return correspondingDropList;\n        }\n        return drop;\n      });\n      if (this._group) {\n        this._group._items.forEach(drop => {\n          if (siblings.indexOf(drop) === -1) {\n            siblings.push(drop);\n          }\n        });\n      }\n      // Note that we resolve the scrollable parents here so that we delay the resolution\n      // as long as possible, ensuring that the element is in its final place in the DOM.\n      if (!this._scrollableParentsResolved) {\n        const scrollableParents = this._scrollDispatcher.getAncestorScrollContainers(this.element).map(scrollable => scrollable.getElementRef().nativeElement);\n        this._dropListRef.withScrollableParents(scrollableParents);\n        // Only do this once since it involves traversing the DOM and the parents\n        // shouldn't be able to change without the drop list being destroyed.\n        this._scrollableParentsResolved = true;\n      }\n      ref.disabled = this.disabled;\n      ref.lockAxis = this.lockAxis;\n      ref.sortingDisabled = this.sortingDisabled;\n      ref.autoScrollDisabled = this.autoScrollDisabled;\n      ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n      ref.connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef)).withOrientation(this.orientation);\n    });\n  }\n  /** Handles events from the underlying DropListRef. */\n  _handleEvents(ref) {\n    ref.beforeStarted.subscribe(() => {\n      this._syncItemsWithRef();\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.entered.subscribe(event => {\n      this.entered.emit({\n        container: this,\n        item: event.item.data,\n        currentIndex: event.currentIndex\n      });\n    });\n    ref.exited.subscribe(event => {\n      this.exited.emit({\n        container: this,\n        item: event.item.data\n      });\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.sorted.subscribe(event => {\n      this.sorted.emit({\n        previousIndex: event.previousIndex,\n        currentIndex: event.currentIndex,\n        container: this,\n        item: event.item.data\n      });\n    });\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        item: dropEvent.item.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event\n      });\n      // Mark for check since all of these events run outside of change\n      // detection and we're not guaranteed for something else to have triggered it.\n      this._changeDetectorRef.markForCheck();\n    });\n    merge(ref.receivingStarted, ref.receivingStopped).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  /** Assigns the default input values based on a provided config object. */\n  _assignDefaults(config) {\n    const {\n      lockAxis,\n      draggingDisabled,\n      sortingDisabled,\n      listAutoScrollDisabled,\n      listOrientation\n    } = config;\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n    this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n    this.orientation = listOrientation || 'vertical';\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n  }\n  /** Syncs up the registered drag items with underlying drop list ref. */\n  _syncItemsWithRef() {\n    this._dropListRef.withItems(this.getSortedItems().map(item => item._dragRef));\n  }\n  static {\n    this.ɵfac = function CdkDropList_Factory(t) {\n      return new (t || CdkDropList)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i1$1.Directionality, 8), i0.ɵɵdirectiveInject(CDK_DROP_LIST_GROUP, 12), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDropList,\n      selectors: [[\"\", \"cdkDropList\", \"\"], [\"cdk-drop-list\"]],\n      hostAttrs: [1, \"cdk-drop-list\"],\n      hostVars: 7,\n      hostBindings: function CdkDropList_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id);\n          i0.ɵɵclassProp(\"cdk-drop-list-disabled\", ctx.disabled)(\"cdk-drop-list-dragging\", ctx._dropListRef.isDragging())(\"cdk-drop-list-receiving\", ctx._dropListRef.isReceiving());\n        }\n      },\n      inputs: {\n        connectedTo: [i0.ɵɵInputFlags.None, \"cdkDropListConnectedTo\", \"connectedTo\"],\n        data: [i0.ɵɵInputFlags.None, \"cdkDropListData\", \"data\"],\n        orientation: [i0.ɵɵInputFlags.None, \"cdkDropListOrientation\", \"orientation\"],\n        id: \"id\",\n        lockAxis: [i0.ɵɵInputFlags.None, \"cdkDropListLockAxis\", \"lockAxis\"],\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkDropListDisabled\", \"disabled\", booleanAttribute],\n        sortingDisabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkDropListSortingDisabled\", \"sortingDisabled\", booleanAttribute],\n        enterPredicate: [i0.ɵɵInputFlags.None, \"cdkDropListEnterPredicate\", \"enterPredicate\"],\n        sortPredicate: [i0.ɵɵInputFlags.None, \"cdkDropListSortPredicate\", \"sortPredicate\"],\n        autoScrollDisabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkDropListAutoScrollDisabled\", \"autoScrollDisabled\", booleanAttribute],\n        autoScrollStep: [i0.ɵɵInputFlags.None, \"cdkDropListAutoScrollStep\", \"autoScrollStep\"]\n      },\n      outputs: {\n        dropped: \"cdkDropListDropped\",\n        entered: \"cdkDropListEntered\",\n        exited: \"cdkDropListExited\",\n        sorted: \"cdkDropListSorted\"\n      },\n      exportAs: [\"cdkDropList\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([\n      // Prevent child drop lists from picking up the same group as their parent.\n      {\n        provide: CDK_DROP_LIST_GROUP,\n        useValue: undefined\n      }, {\n        provide: CDK_DROP_LIST,\n        useExisting: CdkDropList\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDropList, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDropList], cdk-drop-list',\n      exportAs: 'cdkDropList',\n      standalone: true,\n      providers: [\n      // Prevent child drop lists from picking up the same group as their parent.\n      {\n        provide: CDK_DROP_LIST_GROUP,\n        useValue: undefined\n      }, {\n        provide: CDK_DROP_LIST,\n        useExisting: CdkDropList\n      }],\n      host: {\n        'class': 'cdk-drop-list',\n        '[attr.id]': 'id',\n        '[class.cdk-drop-list-disabled]': 'disabled',\n        '[class.cdk-drop-list-dragging]': '_dropListRef.isDragging()',\n        '[class.cdk-drop-list-receiving]': '_dropListRef.isReceiving()'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: DragDrop\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.ScrollDispatcher\n  }, {\n    type: i1$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: CdkDropListGroup,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CDK_DROP_LIST_GROUP]\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CDK_DRAG_CONFIG]\n    }]\n  }], {\n    connectedTo: [{\n      type: Input,\n      args: ['cdkDropListConnectedTo']\n    }],\n    data: [{\n      type: Input,\n      args: ['cdkDropListData']\n    }],\n    orientation: [{\n      type: Input,\n      args: ['cdkDropListOrientation']\n    }],\n    id: [{\n      type: Input\n    }],\n    lockAxis: [{\n      type: Input,\n      args: ['cdkDropListLockAxis']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    sortingDisabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListSortingDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    enterPredicate: [{\n      type: Input,\n      args: ['cdkDropListEnterPredicate']\n    }],\n    sortPredicate: [{\n      type: Input,\n      args: ['cdkDropListSortPredicate']\n    }],\n    autoScrollDisabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListAutoScrollDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    autoScrollStep: [{\n      type: Input,\n      args: ['cdkDropListAutoScrollStep']\n    }],\n    dropped: [{\n      type: Output,\n      args: ['cdkDropListDropped']\n    }],\n    entered: [{\n      type: Output,\n      args: ['cdkDropListEntered']\n    }],\n    exited: [{\n      type: Output,\n      args: ['cdkDropListExited']\n    }],\n    sorted: [{\n      type: Output,\n      args: ['cdkDropListSorted']\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PREVIEW = new InjectionToken('CdkDragPreview');\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\nclass CdkDragPreview {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n    this._drag = inject(CDK_DRAG_PARENT, {\n      optional: true\n    });\n    /** Whether the preview should preserve the same size as the item that is being dragged. */\n    this.matchSize = false;\n    this._drag?._setPreviewTemplate(this);\n  }\n  ngOnDestroy() {\n    this._drag?._resetPreviewTemplate(this);\n  }\n  static {\n    this.ɵfac = function CdkDragPreview_Factory(t) {\n      return new (t || CdkDragPreview)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDragPreview,\n      selectors: [[\"ng-template\", \"cdkDragPreview\", \"\"]],\n      inputs: {\n        data: \"data\",\n        matchSize: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matchSize\", \"matchSize\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DRAG_PREVIEW,\n        useExisting: CdkDragPreview\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragPreview, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkDragPreview]',\n      standalone: true,\n      providers: [{\n        provide: CDK_DRAG_PREVIEW,\n        useExisting: CdkDragPreview\n      }]\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], {\n    data: [{\n      type: Input\n    }],\n    matchSize: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PLACEHOLDER = new InjectionToken('CdkDragPlaceholder');\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\nclass CdkDragPlaceholder {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n    this._drag = inject(CDK_DRAG_PARENT, {\n      optional: true\n    });\n    this._drag?._setPlaceholderTemplate(this);\n  }\n  ngOnDestroy() {\n    this._drag?._resetPlaceholderTemplate(this);\n  }\n  static {\n    this.ɵfac = function CdkDragPlaceholder_Factory(t) {\n      return new (t || CdkDragPlaceholder)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDragPlaceholder,\n      selectors: [[\"ng-template\", \"cdkDragPlaceholder\", \"\"]],\n      inputs: {\n        data: \"data\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DRAG_PLACEHOLDER,\n        useExisting: CdkDragPlaceholder\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragPlaceholder, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkDragPlaceholder]',\n      standalone: true,\n      providers: [{\n        provide: CDK_DRAG_PLACEHOLDER,\n        useExisting: CdkDragPlaceholder\n      }]\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], {\n    data: [{\n      type: Input\n    }]\n  });\n})();\nconst DRAG_DROP_DIRECTIVES = [CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder];\nclass DragDropModule {\n  static {\n    this.ɵfac = function DragDropModule_Factory(t) {\n      return new (t || DragDropModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DragDropModule,\n      imports: [CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder],\n      exports: [CdkScrollableModule, CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [DragDrop],\n      imports: [CdkScrollableModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDropModule, [{\n    type: NgModule,\n    args: [{\n      imports: DRAG_DROP_DIRECTIVES,\n      exports: [CdkScrollableModule, ...DRAG_DROP_DIRECTIVES],\n      providers: [DragDrop]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_DRAG_CONFIG, CDK_DRAG_HANDLE, CDK_DRAG_PARENT, CDK_DRAG_PLACEHOLDER, CDK_DRAG_PREVIEW, CDK_DROP_LIST, CDK_DROP_LIST_GROUP, CdkDrag, CdkDragHandle, CdkDragPlaceholder, CdkDragPreview, CdkDropList, CdkDropListGroup, DragDrop, DragDropModule, DragDropRegistry, DragRef, DropListRef, copyArrayItem, moveItemInArray, transferArrayItem };\n", "import * as i2$1 from '@angular/cdk/overlay';\nimport { OverlayConfig, OverlayRef } from '@angular/cdk/overlay';\nimport * as i9 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, PortalModule, ComponentPortal, TemplatePortal } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Component, ChangeDetectionStrategy, EventEmitter, Directive, Optional, Inject, ViewChild, Output, Input, Injector, TemplateRef, Injectable, SkipSelf, ContentChild, NgModule } from '@angular/core';\nimport { Subject, fromEvent, defer } from 'rxjs';\nimport { takeUntil, filter, take, startWith } from 'rxjs/operators';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { overlayZIndexSetter } from 'ng-zorro-antd/core/overlay';\nimport { getElementOffset, isNotNil, isPromise, InputBoolean } from 'ng-zorro-antd/core/util';\nimport { DOCUMENT, NgClass, NgStyle } from '@angular/common';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport * as i10 from 'ng-zorro-antd/button';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport * as i3 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i2 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i6 from 'ng-zorro-antd/pipes';\nimport { NzPipesModule } from 'ng-zorro-antd/pipes';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { reqAnimFrame } from 'ng-zorro-antd/core/polyfill';\nimport * as i1 from '@angular/cdk/a11y';\nimport * as i3$1 from 'ng-zorro-antd/core/config';\nimport * as i1$1 from 'ng-zorro-antd/i18n';\nimport * as i11 from 'ng-zorro-antd/core/transition-patch';\nimport * as i12 from 'ng-zorro-antd/core/wave';\nimport { CdkDrag, CdkDragHandle } from '@angular/cdk/drag-drop';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i3$2 from '@angular/cdk/bidi';\nimport { __decorate } from 'tslib';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"nz-modal-close\", \"\"];\nfunction NzModalCloseComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const closeIcon_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzType\", closeIcon_r1);\n  }\n}\nconst _c1 = [\"modalElement\"];\nfunction NzModalConfirmContainerComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function NzModalConfirmContainerComponent_Conditional_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCloseClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NzModalConfirmContainerComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.config.nzTitle, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NzModalConfirmContainerComponent_ng_template_12_Template(rf, ctx) {}\nfunction NzModalConfirmContainerComponent_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.config.nzContent, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NzModalConfirmContainerComponent_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function NzModalConfirmContainerComponent_Conditional_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCancel());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzLoading\", ctx_r1.config.nzCancelLoading)(\"disabled\", ctx_r1.config.nzCancelDisabled);\n    i0.ɵɵattribute(\"cdkFocusInitial\", ctx_r1.config.nzAutofocus === \"cancel\" || null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.config.nzCancelText || ctx_r1.locale.cancelText, \" \");\n  }\n}\nfunction NzModalConfirmContainerComponent_Conditional_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function NzModalConfirmContainerComponent_Conditional_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOk());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzType\", ctx_r1.config.nzOkType)(\"nzLoading\", ctx_r1.config.nzOkLoading)(\"disabled\", ctx_r1.config.nzOkDisabled)(\"nzDanger\", ctx_r1.config.nzOkDanger);\n    i0.ɵɵattribute(\"cdkFocusInitial\", ctx_r1.config.nzAutofocus === \"ok\" || null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.config.nzOkText || ctx_r1.locale.okText, \" \");\n  }\n}\nconst _c2 = [\"nz-modal-footer\", \"\"];\nconst _c3 = (a0, a1) => ({\n  $implicit: a0,\n  modalRef: a1\n});\nfunction NzModalFooterComponent_Conditional_0_ng_container_0_Conditional_1_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function NzModalFooterComponent_Conditional_0_ng_container_0_Conditional_1_For_1_Template_button_click_0_listener() {\n      const button_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onButtonClick(button_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const button_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"hidden\", !ctx_r2.getButtonCallableProp(button_r2, \"show\"))(\"nzLoading\", ctx_r2.getButtonCallableProp(button_r2, \"loading\"))(\"disabled\", ctx_r2.getButtonCallableProp(button_r2, \"disabled\"))(\"nzType\", button_r2.type)(\"nzDanger\", button_r2.danger)(\"nzShape\", button_r2.shape)(\"nzSize\", button_r2.size)(\"nzGhost\", button_r2.ghost);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", button_r2.label, \" \");\n  }\n}\nfunction NzModalFooterComponent_Conditional_0_ng_container_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, NzModalFooterComponent_Conditional_0_ng_container_0_Conditional_1_For_1_Template, 2, 9, \"button\", 1, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵrepeater(ctx_r2.buttons);\n  }\n}\nfunction NzModalFooterComponent_Conditional_0_ng_container_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.config.nzFooter, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NzModalFooterComponent_Conditional_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzModalFooterComponent_Conditional_0_ng_container_0_Conditional_1_Template, 2, 0)(2, NzModalFooterComponent_Conditional_0_ng_container_0_Conditional_2_Template, 1, 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r2.buttonsFooter ? 1 : 2);\n  }\n}\nfunction NzModalFooterComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzModalFooterComponent_Conditional_0_ng_container_0_Template, 3, 1, \"ng-container\", 0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r2.config.nzFooter)(\"nzStringTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c3, ctx_r2.config.nzData, ctx_r2.modalRef));\n  }\n}\nfunction NzModalFooterComponent_Conditional_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function NzModalFooterComponent_Conditional_1_Conditional_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onCancel());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"nzLoading\", ctx_r2.config.nzCancelLoading)(\"disabled\", ctx_r2.config.nzCancelDisabled);\n    i0.ɵɵattribute(\"cdkFocusInitial\", ctx_r2.config.nzAutofocus === \"cancel\" || null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.nzCancelText || ctx_r2.locale.cancelText, \" \");\n  }\n}\nfunction NzModalFooterComponent_Conditional_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function NzModalFooterComponent_Conditional_1_Conditional_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onOk());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"nzType\", ctx_r2.config.nzOkType)(\"nzDanger\", ctx_r2.config.nzOkDanger)(\"nzLoading\", ctx_r2.config.nzOkLoading)(\"disabled\", ctx_r2.config.nzOkDisabled);\n    i0.ɵɵattribute(\"cdkFocusInitial\", ctx_r2.config.nzAutofocus === \"ok\" || null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.nzOkText || ctx_r2.locale.okText, \" \");\n  }\n}\nfunction NzModalFooterComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzModalFooterComponent_Conditional_1_Conditional_0_Template, 2, 4, \"button\", 4)(1, NzModalFooterComponent_Conditional_1_Conditional_1_Template, 2, 6, \"button\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, ctx_r2.config.nzCancelText !== null ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r2.config.nzOkText !== null ? 1 : -1);\n  }\n}\nconst _c4 = [\"nz-modal-title\", \"\"];\nfunction NzModalTitleComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r0.config.nzTitle, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NzModalContainerComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function NzModalContainerComponent_Conditional_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCloseClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NzModalContainerComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"cursor\", ctx_r1.config.nzDraggable ? \"move\" : \"auto\");\n  }\n}\nfunction NzModalContainerComponent_ng_template_7_Template(rf, ctx) {}\nfunction NzModalContainerComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.config.nzContent, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NzModalContainerComponent_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵlistener(\"cancelTriggered\", function NzModalContainerComponent_Conditional_9_Template_div_cancelTriggered_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCloseClick());\n    })(\"okTriggered\", function NzModalContainerComponent_Conditional_9_Template_div_okTriggered_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOkClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"modalRef\", ctx_r1.modalRef);\n  }\n}\nconst noopFun = () => void 0;\nclass ModalOptions {\n  constructor() {\n    this.nzCentered = false;\n    this.nzClosable = true;\n    this.nzOkLoading = false;\n    this.nzOkDisabled = false;\n    this.nzCancelDisabled = false;\n    this.nzCancelLoading = false;\n    this.nzDraggable = false;\n    this.nzNoAnimation = false;\n    this.nzAutofocus = 'auto';\n    this.nzKeyboard = true;\n    this.nzZIndex = 1000;\n    this.nzWidth = 520;\n    this.nzCloseIcon = 'close';\n    this.nzOkType = 'primary';\n    this.nzOkDanger = false;\n    this.nzModalType = 'default';\n    this.nzOnCancel = noopFun;\n    this.nzOnOk = noopFun;\n    // Confirm\n    this.nzIconType = 'question-circle';\n  }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst ZOOM_CLASS_NAME_MAP = {\n  enter: 'ant-zoom-enter',\n  enterActive: 'ant-zoom-enter-active',\n  leave: 'ant-zoom-leave',\n  leaveActive: 'ant-zoom-leave-active'\n};\nconst FADE_CLASS_NAME_MAP = {\n  enter: 'ant-fade-enter',\n  enterActive: 'ant-fade-enter-active',\n  leave: 'ant-fade-leave',\n  leaveActive: 'ant-fade-leave-active'\n};\nconst MODAL_MASK_CLASS_NAME = 'ant-modal-mask';\nconst NZ_CONFIG_MODULE_NAME = 'modal';\nconst NZ_MODAL_DATA = new InjectionToken('NZ_MODAL_DATA');\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst nzModalAnimations = {\n  modalContainer: trigger('modalContainer', [state('void, exit', style({})), state('enter', style({})), transition('* => enter', animate('.24s', style({}))), transition('* => void, * => exit', animate('.2s', style({})))])\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzModalCloseComponent {\n  constructor(config) {\n    this.config = config;\n  }\n  static {\n    this.ɵfac = function NzModalCloseComponent_Factory(t) {\n      return new (t || NzModalCloseComponent)(i0.ɵɵdirectiveInject(ModalOptions));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzModalCloseComponent,\n      selectors: [[\"button\", \"nz-modal-close\", \"\"]],\n      hostAttrs: [\"aria-label\", \"Close\", 1, \"ant-modal-close\"],\n      exportAs: [\"NzModalCloseBuiltin\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"ant-modal-close-x\"], [4, \"nzStringTemplateOutlet\"], [\"nz-icon\", \"\", 1, \"ant-modal-close-icon\", 3, \"nzType\"]],\n      template: function NzModalCloseComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵtemplate(1, NzModalCloseComponent_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.config.nzCloseIcon);\n        }\n      },\n      dependencies: [NzIconModule, i2.NzIconDirective, NzOutletModule, i3.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzModalCloseComponent, [{\n    type: Component,\n    args: [{\n      selector: 'button[nz-modal-close]',\n      exportAs: 'NzModalCloseBuiltin',\n      template: `\n    <span class=\"ant-modal-close-x\">\n      <ng-container *nzStringTemplateOutlet=\"config.nzCloseIcon; let closeIcon\">\n        <span nz-icon [nzType]=\"closeIcon\" class=\"ant-modal-close-icon\"></span>\n      </ng-container>\n    </span>\n  `,\n      host: {\n        class: 'ant-modal-close',\n        'aria-label': 'Close'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NzIconModule, NzOutletModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: ModalOptions\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction applyConfigDefaults(config, defaultOptions) {\n  return {\n    ...defaultOptions,\n    ...config\n  };\n}\nfunction getValueWithConfig(userValue, configValue, defaultValue) {\n  return typeof userValue === 'undefined' ? typeof configValue === 'undefined' ? defaultValue : configValue : userValue;\n}\nfunction getConfigFromComponent(component) {\n  const {\n    nzCentered,\n    nzMask,\n    nzMaskClosable,\n    nzClosable,\n    nzOkLoading,\n    nzOkDisabled,\n    nzCancelDisabled,\n    nzCancelLoading,\n    nzKeyboard,\n    nzNoAnimation,\n    nzDraggable,\n    nzContent,\n    nzFooter,\n    nzZIndex,\n    nzWidth,\n    nzWrapClassName,\n    nzClassName,\n    nzStyle,\n    nzTitle,\n    nzCloseIcon,\n    nzMaskStyle,\n    nzBodyStyle,\n    nzOkText,\n    nzCancelText,\n    nzOkType,\n    nzOkDanger,\n    nzIconType,\n    nzModalType,\n    nzOnOk,\n    nzOnCancel,\n    nzAfterOpen,\n    nzAfterClose,\n    nzCloseOnNavigation,\n    nzAutofocus\n  } = component;\n  return {\n    nzCentered,\n    nzMask,\n    nzMaskClosable,\n    nzDraggable,\n    nzClosable,\n    nzOkLoading,\n    nzOkDisabled,\n    nzCancelDisabled,\n    nzCancelLoading,\n    nzKeyboard,\n    nzNoAnimation,\n    nzContent,\n    nzFooter,\n    nzZIndex,\n    nzWidth,\n    nzWrapClassName,\n    nzClassName,\n    nzStyle,\n    nzTitle,\n    nzCloseIcon,\n    nzMaskStyle,\n    nzBodyStyle,\n    nzOkText,\n    nzCancelText,\n    nzOkType,\n    nzOkDanger,\n    nzIconType,\n    nzModalType,\n    nzOnOk,\n    nzOnCancel,\n    nzAfterOpen,\n    nzAfterClose,\n    nzCloseOnNavigation,\n    nzAutofocus\n  };\n}\nfunction throwNzModalContentAlreadyAttachedError() {\n  throw Error('Attempting to attach modal content after content is already attached');\n}\nclass BaseModalContainerComponent extends BasePortalOutlet {\n  get showMask() {\n    const defaultConfig = this.nzConfigService.getConfigForComponent(NZ_CONFIG_MODULE_NAME) || {};\n    return !!getValueWithConfig(this.config.nzMask, defaultConfig.nzMask, true);\n  }\n  get maskClosable() {\n    const defaultConfig = this.nzConfigService.getConfigForComponent(NZ_CONFIG_MODULE_NAME) || {};\n    return !!getValueWithConfig(this.config.nzMaskClosable, defaultConfig.nzMaskClosable, true);\n  }\n  constructor(ngZone, host, focusTrapFactory, cdr, render, overlayRef, nzConfigService, config, document, animationType) {\n    super();\n    this.ngZone = ngZone;\n    this.host = host;\n    this.focusTrapFactory = focusTrapFactory;\n    this.cdr = cdr;\n    this.render = render;\n    this.overlayRef = overlayRef;\n    this.nzConfigService = nzConfigService;\n    this.config = config;\n    this.animationType = animationType;\n    this.animationStateChanged = new EventEmitter();\n    this.containerClick = new EventEmitter();\n    this.cancelTriggered = new EventEmitter();\n    this.okTriggered = new EventEmitter();\n    this.state = 'enter';\n    this.isStringContent = false;\n    this.dir = 'ltr';\n    this.elementFocusedBeforeModalWasOpened = null;\n    this.mouseDown = false;\n    this.oldMaskStyle = null;\n    this.destroy$ = new Subject();\n    this.document = document;\n    this.dir = overlayRef.getDirection();\n    this.isStringContent = typeof config.nzContent === 'string';\n    this.nzConfigService.getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME).pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateMaskClassname();\n    });\n  }\n  onContainerClick(e) {\n    if (e.target === e.currentTarget && !this.mouseDown && this.showMask && this.maskClosable) {\n      this.containerClick.emit();\n    }\n  }\n  onCloseClick() {\n    this.cancelTriggered.emit();\n  }\n  onOkClick() {\n    this.okTriggered.emit();\n  }\n  attachComponentPortal(portal) {\n    if (this.portalOutlet.hasAttached()) {\n      throwNzModalContentAlreadyAttachedError();\n    }\n    this.savePreviouslyFocusedElement();\n    this.setZIndexForBackdrop();\n    return this.portalOutlet.attachComponentPortal(portal);\n  }\n  attachTemplatePortal(portal) {\n    if (this.portalOutlet.hasAttached()) {\n      throwNzModalContentAlreadyAttachedError();\n    }\n    this.savePreviouslyFocusedElement();\n    this.setZIndexForBackdrop();\n    return this.portalOutlet.attachTemplatePortal(portal);\n  }\n  attachStringContent() {\n    this.savePreviouslyFocusedElement();\n    this.setZIndexForBackdrop();\n  }\n  getNativeElement() {\n    return this.host.nativeElement;\n  }\n  animationDisabled() {\n    return this.config.nzNoAnimation || this.animationType === 'NoopAnimations';\n  }\n  setModalTransformOrigin() {\n    const modalElement = this.modalElementRef.nativeElement;\n    if (this.elementFocusedBeforeModalWasOpened) {\n      const previouslyDOMRect = this.elementFocusedBeforeModalWasOpened.getBoundingClientRect();\n      const lastPosition = getElementOffset(this.elementFocusedBeforeModalWasOpened);\n      const x = lastPosition.left + previouslyDOMRect.width / 2;\n      const y = lastPosition.top + previouslyDOMRect.height / 2;\n      const transformOrigin = `${x - modalElement.offsetLeft}px ${y - modalElement.offsetTop}px 0px`;\n      this.render.setStyle(modalElement, 'transform-origin', transformOrigin);\n    }\n  }\n  savePreviouslyFocusedElement() {\n    if (!this.focusTrap) {\n      this.focusTrap = this.focusTrapFactory.create(this.host.nativeElement);\n    }\n    if (this.document) {\n      this.elementFocusedBeforeModalWasOpened = this.document.activeElement;\n      if (this.host.nativeElement.focus) {\n        this.ngZone.runOutsideAngular(() => reqAnimFrame(() => this.host.nativeElement.focus()));\n      }\n    }\n  }\n  trapFocus() {\n    const element = this.host.nativeElement;\n    if (this.config.nzAutofocus) {\n      this.focusTrap.focusInitialElementWhenReady();\n    } else {\n      const activeElement = this.document.activeElement;\n      if (activeElement !== element && !element.contains(activeElement)) {\n        element.focus();\n      }\n    }\n  }\n  restoreFocus() {\n    const toFocus = this.elementFocusedBeforeModalWasOpened;\n    // We need the extra check, because IE can set the `activeElement` to null in some cases.\n    if (toFocus && typeof toFocus.focus === 'function') {\n      const activeElement = this.document.activeElement;\n      const element = this.host.nativeElement;\n      if (!activeElement || activeElement === this.document.body || activeElement === element || element.contains(activeElement)) {\n        toFocus.focus();\n      }\n    }\n    if (this.focusTrap) {\n      this.focusTrap.destroy();\n    }\n  }\n  setEnterAnimationClass() {\n    if (this.animationDisabled()) {\n      return;\n    }\n    // Make sure to set the `TransformOrigin` style before set the modelElement's class names\n    this.setModalTransformOrigin();\n    const modalElement = this.modalElementRef.nativeElement;\n    const backdropElement = this.overlayRef.backdropElement;\n    modalElement.classList.add(ZOOM_CLASS_NAME_MAP.enter);\n    modalElement.classList.add(ZOOM_CLASS_NAME_MAP.enterActive);\n    if (backdropElement) {\n      backdropElement.classList.add(FADE_CLASS_NAME_MAP.enter);\n      backdropElement.classList.add(FADE_CLASS_NAME_MAP.enterActive);\n    }\n  }\n  setExitAnimationClass() {\n    const modalElement = this.modalElementRef.nativeElement;\n    modalElement.classList.add(ZOOM_CLASS_NAME_MAP.leave);\n    modalElement.classList.add(ZOOM_CLASS_NAME_MAP.leaveActive);\n    this.setMaskExitAnimationClass();\n  }\n  setMaskExitAnimationClass(force = false) {\n    const backdropElement = this.overlayRef.backdropElement;\n    if (backdropElement) {\n      if (this.animationDisabled() || force) {\n        // https://github.com/angular/components/issues/18645\n        backdropElement.classList.remove(MODAL_MASK_CLASS_NAME);\n        return;\n      }\n      backdropElement.classList.add(FADE_CLASS_NAME_MAP.leave);\n      backdropElement.classList.add(FADE_CLASS_NAME_MAP.leaveActive);\n    }\n  }\n  cleanAnimationClass() {\n    if (this.animationDisabled()) {\n      return;\n    }\n    const backdropElement = this.overlayRef.backdropElement;\n    const modalElement = this.modalElementRef.nativeElement;\n    if (backdropElement) {\n      backdropElement.classList.remove(FADE_CLASS_NAME_MAP.enter);\n      backdropElement.classList.remove(FADE_CLASS_NAME_MAP.enterActive);\n    }\n    modalElement.classList.remove(ZOOM_CLASS_NAME_MAP.enter);\n    modalElement.classList.remove(ZOOM_CLASS_NAME_MAP.enterActive);\n    modalElement.classList.remove(ZOOM_CLASS_NAME_MAP.leave);\n    modalElement.classList.remove(ZOOM_CLASS_NAME_MAP.leaveActive);\n  }\n  setZIndexForBackdrop() {\n    const backdropElement = this.overlayRef.backdropElement;\n    if (backdropElement) {\n      if (isNotNil(this.config.nzZIndex)) {\n        this.render.setStyle(backdropElement, 'z-index', this.config.nzZIndex);\n      }\n    }\n  }\n  bindBackdropStyle() {\n    const backdropElement = this.overlayRef.backdropElement;\n    if (backdropElement) {\n      if (this.oldMaskStyle) {\n        const styles = this.oldMaskStyle;\n        Object.keys(styles).forEach(key => {\n          this.render.removeStyle(backdropElement, key);\n        });\n        this.oldMaskStyle = null;\n      }\n      this.setZIndexForBackdrop();\n      if (typeof this.config.nzMaskStyle === 'object' && Object.keys(this.config.nzMaskStyle).length) {\n        const styles = {\n          ...this.config.nzMaskStyle\n        };\n        Object.keys(styles).forEach(key => {\n          this.render.setStyle(backdropElement, key, styles[key]);\n        });\n        this.oldMaskStyle = styles;\n      }\n    }\n  }\n  updateMaskClassname() {\n    const backdropElement = this.overlayRef.backdropElement;\n    if (backdropElement) {\n      if (this.showMask) {\n        backdropElement.classList.add(MODAL_MASK_CLASS_NAME);\n      } else {\n        backdropElement.classList.remove(MODAL_MASK_CLASS_NAME);\n      }\n    }\n  }\n  onAnimationDone(event) {\n    if (event.toState === 'enter') {\n      this.trapFocus();\n    } else if (event.toState === 'exit') {\n      this.restoreFocus();\n    }\n    this.cleanAnimationClass();\n    this.animationStateChanged.emit(event);\n  }\n  onAnimationStart(event) {\n    if (event.toState === 'enter') {\n      this.setEnterAnimationClass();\n      this.bindBackdropStyle();\n    } else if (event.toState === 'exit') {\n      this.setExitAnimationClass();\n    }\n    this.animationStateChanged.emit(event);\n  }\n  startExitAnimation() {\n    this.state = 'exit';\n    this.cdr.markForCheck();\n  }\n  ngOnDestroy() {\n    this.setMaskExitAnimationClass(true);\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  setupMouseListeners(modalContainer) {\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.host.nativeElement, 'mouseup').pipe(takeUntil(this.destroy$)).subscribe(() => {\n        if (this.mouseDown) {\n          setTimeout(() => {\n            this.mouseDown = false;\n          });\n        }\n      });\n      fromEvent(modalContainer.nativeElement, 'mousedown').pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.mouseDown = true;\n      });\n    });\n  }\n  static {\n    this.ɵfac = function BaseModalContainerComponent_Factory(t) {\n      i0.ɵɵinvalidFactory();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: BaseModalContainerComponent,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseModalContainerComponent, [{\n    type: Directive\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1.FocusTrapFactory\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i2$1.OverlayRef\n  }, {\n    type: i3$1.NzConfigService\n  }, {\n    type: ModalOptions\n  }, {\n    type: undefined\n  }, {\n    type: undefined\n  }], null);\n})();\nclass NzModalConfirmContainerComponent extends BaseModalContainerComponent {\n  constructor(ngZone, i18n, host, focusTrapFactory, cdr, render, overlayRef, nzConfigService, config, document, animationType) {\n    super(ngZone, host, focusTrapFactory, cdr, render, overlayRef, nzConfigService, config, document, animationType);\n    this.i18n = i18n;\n    this.config = config;\n    this.cancelTriggered = new EventEmitter();\n    this.okTriggered = new EventEmitter();\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Modal');\n    });\n  }\n  ngOnInit() {\n    this.setupMouseListeners(this.modalElementRef);\n  }\n  onCancel() {\n    this.cancelTriggered.emit();\n  }\n  onOk() {\n    this.okTriggered.emit();\n  }\n  static {\n    this.ɵfac = function NzModalConfirmContainerComponent_Factory(t) {\n      return new (t || NzModalConfirmContainerComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.NzI18nService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusTrapFactory), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2$1.OverlayRef), i0.ɵɵdirectiveInject(i3$1.NzConfigService), i0.ɵɵdirectiveInject(ModalOptions), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzModalConfirmContainerComponent,\n      selectors: [[\"nz-modal-confirm-container\"]],\n      viewQuery: function NzModalConfirmContainerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n          i0.ɵɵviewQuery(_c1, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.portalOutlet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalElementRef = _t.first);\n        }\n      },\n      hostAttrs: [\"tabindex\", \"-1\", \"role\", \"dialog\"],\n      hostVars: 10,\n      hostBindings: function NzModalConfirmContainerComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵsyntheticHostListener(\"@modalContainer.start\", function NzModalConfirmContainerComponent_animation_modalContainer_start_HostBindingHandler($event) {\n            return ctx.onAnimationStart($event);\n          })(\"@modalContainer.done\", function NzModalConfirmContainerComponent_animation_modalContainer_done_HostBindingHandler($event) {\n            return ctx.onAnimationDone($event);\n          });\n          i0.ɵɵlistener(\"click\", function NzModalConfirmContainerComponent_click_HostBindingHandler($event) {\n            return ctx.onContainerClick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@.disabled\", ctx.config.nzNoAnimation)(\"@modalContainer\", ctx.state);\n          i0.ɵɵclassMap(ctx.config.nzWrapClassName ? \"ant-modal-wrap \" + ctx.config.nzWrapClassName : \"ant-modal-wrap\");\n          i0.ɵɵstyleProp(\"z-index\", ctx.config.nzZIndex);\n          i0.ɵɵclassProp(\"ant-modal-wrap-rtl\", ctx.dir === \"rtl\")(\"ant-modal-centered\", ctx.config.nzCentered);\n        }\n      },\n      outputs: {\n        cancelTriggered: \"cancelTriggered\",\n        okTriggered: \"okTriggered\"\n      },\n      exportAs: [\"nzModalConfirmContainer\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 17,\n      vars: 13,\n      consts: [[\"modalElement\", \"\"], [\"role\", \"document\", 1, \"ant-modal\", 3, \"ngClass\", \"ngStyle\"], [1, \"ant-modal-content\"], [\"nz-modal-close\", \"\"], [1, \"ant-modal-body\", 3, \"ngStyle\"], [1, \"ant-modal-confirm-body-wrapper\"], [1, \"ant-modal-confirm-body\"], [\"nz-icon\", \"\", 3, \"nzType\"], [1, \"ant-modal-confirm-title\"], [4, \"nzStringTemplateOutlet\"], [1, \"ant-modal-confirm-content\"], [\"cdkPortalOutlet\", \"\"], [3, \"innerHTML\"], [1, \"ant-modal-confirm-btns\"], [\"nz-button\", \"\", 3, \"nzLoading\", \"disabled\"], [\"nz-button\", \"\", 3, \"nzType\", \"nzLoading\", \"disabled\", \"nzDanger\"], [\"nz-modal-close\", \"\", 3, \"click\"], [\"nz-button\", \"\", 3, \"click\", \"nzLoading\", \"disabled\"], [\"nz-button\", \"\", 3, \"click\", \"nzType\", \"nzLoading\", \"disabled\", \"nzDanger\"]],\n      template: function NzModalConfirmContainerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1, 0);\n          i0.ɵɵpipe(2, \"nzToCssUnit\");\n          i0.ɵɵelementStart(3, \"div\", 2);\n          i0.ɵɵtemplate(4, NzModalConfirmContainerComponent_Conditional_4_Template, 1, 0, \"button\", 3);\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6);\n          i0.ɵɵelement(8, \"span\", 7);\n          i0.ɵɵelementStart(9, \"span\", 8);\n          i0.ɵɵtemplate(10, NzModalConfirmContainerComponent_ng_container_10_Template, 2, 1, \"ng-container\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10);\n          i0.ɵɵtemplate(12, NzModalConfirmContainerComponent_ng_template_12_Template, 0, 0, \"ng-template\", 11)(13, NzModalConfirmContainerComponent_Conditional_13_Template, 1, 1, \"div\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 13);\n          i0.ɵɵtemplate(15, NzModalConfirmContainerComponent_Conditional_15_Template, 2, 4, \"button\", 14)(16, NzModalConfirmContainerComponent_Conditional_16_Template, 2, 6, \"button\", 15);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"width\", i0.ɵɵpipeBind1(2, 11, ctx.config == null ? null : ctx.config.nzWidth));\n          i0.ɵɵproperty(\"ngClass\", ctx.config.nzClassName)(\"ngStyle\", ctx.config.nzStyle);\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(4, ctx.config.nzClosable ? 4 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", ctx.config.nzBodyStyle);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"nzType\", ctx.config.nzIconType);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.config.nzTitle);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(13, ctx.isStringContent ? 13 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(15, ctx.config.nzCancelText !== null ? 15 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(16, ctx.config.nzOkText !== null ? 16 : -1);\n        }\n      },\n      dependencies: [NgClass, NgStyle, NzPipesModule, i6.NzToCssUnitPipe, NzIconModule, i2.NzIconDirective, NzModalCloseComponent, NzOutletModule, i3.NzStringTemplateOutletDirective, PortalModule, i9.CdkPortalOutlet, NzButtonModule, i10.NzButtonComponent, i11.ɵNzTransitionPatchDirective, i12.NzWaveDirective],\n      encapsulation: 2,\n      data: {\n        animation: [nzModalAnimations.modalContainer]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzModalConfirmContainerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-modal-confirm-container',\n      exportAs: 'nzModalConfirmContainer',\n      template: `\n    <div\n      #modalElement\n      role=\"document\"\n      class=\"ant-modal\"\n      [ngClass]=\"config.nzClassName!\"\n      [ngStyle]=\"config.nzStyle!\"\n      [style.width]=\"config?.nzWidth! | nzToCssUnit\"\n    >\n      <div class=\"ant-modal-content\">\n        @if (config.nzClosable) {\n          <button nz-modal-close (click)=\"onCloseClick()\"></button>\n        }\n\n        <div class=\"ant-modal-body\" [ngStyle]=\"config.nzBodyStyle!\">\n          <div class=\"ant-modal-confirm-body-wrapper\">\n            <div class=\"ant-modal-confirm-body\">\n              <span nz-icon [nzType]=\"config.nzIconType!\"></span>\n              <span class=\"ant-modal-confirm-title\">\n                <ng-container *nzStringTemplateOutlet=\"config.nzTitle\">\n                  <span [innerHTML]=\"config.nzTitle\"></span>\n                </ng-container>\n              </span>\n              <div class=\"ant-modal-confirm-content\">\n                <ng-template cdkPortalOutlet></ng-template>\n                @if (isStringContent) {\n                  <div [innerHTML]=\"config.nzContent\"></div>\n                }\n              </div>\n            </div>\n            <div class=\"ant-modal-confirm-btns\">\n              @if (config.nzCancelText !== null) {\n                <button\n                  [attr.cdkFocusInitial]=\"config.nzAutofocus === 'cancel' || null\"\n                  nz-button\n                  (click)=\"onCancel()\"\n                  [nzLoading]=\"config.nzCancelLoading\"\n                  [disabled]=\"config.nzCancelDisabled\"\n                >\n                  {{ config.nzCancelText || locale.cancelText }}\n                </button>\n              }\n              @if (config.nzOkText !== null) {\n                <button\n                  [attr.cdkFocusInitial]=\"config.nzAutofocus === 'ok' || null\"\n                  nz-button\n                  [nzType]=\"config.nzOkType!\"\n                  (click)=\"onOk()\"\n                  [nzLoading]=\"config.nzOkLoading\"\n                  [disabled]=\"config.nzOkDisabled\"\n                  [nzDanger]=\"config.nzOkDanger\"\n                >\n                  {{ config.nzOkText || locale.okText }}\n                </button>\n              }\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n      animations: [nzModalAnimations.modalContainer],\n      // Using OnPush for modal caused footer can not to detect changes. we can fix it when 8.x.\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        tabindex: '-1',\n        role: 'dialog',\n        '[class]': 'config.nzWrapClassName ? \"ant-modal-wrap \" + config.nzWrapClassName : \"ant-modal-wrap\"',\n        '[class.ant-modal-wrap-rtl]': `dir === 'rtl'`,\n        '[class.ant-modal-centered]': 'config.nzCentered',\n        '[style.zIndex]': 'config.nzZIndex',\n        '[@.disabled]': 'config.nzNoAnimation',\n        '[@modalContainer]': 'state',\n        '(@modalContainer.start)': 'onAnimationStart($event)',\n        '(@modalContainer.done)': 'onAnimationDone($event)',\n        '(click)': 'onContainerClick($event)'\n      },\n      imports: [NgClass, NgStyle, NzPipesModule, NzIconModule, NzModalCloseComponent, NzOutletModule, PortalModule, NzButtonModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i1$1.NzI18nService\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1.FocusTrapFactory\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i2$1.OverlayRef\n  }, {\n    type: i3$1.NzConfigService\n  }, {\n    type: ModalOptions\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }],\n    modalElementRef: [{\n      type: ViewChild,\n      args: ['modalElement', {\n        static: true\n      }]\n    }],\n    cancelTriggered: [{\n      type: Output\n    }],\n    okTriggered: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzModalFooterComponent {\n  constructor(i18n, config) {\n    this.i18n = i18n;\n    this.config = config;\n    this.buttonsFooter = false;\n    this.buttons = [];\n    this.cancelTriggered = new EventEmitter();\n    this.okTriggered = new EventEmitter();\n    this.destroy$ = new Subject();\n    if (Array.isArray(config.nzFooter)) {\n      this.buttonsFooter = true;\n      this.buttons = config.nzFooter.map(mergeDefaultOption);\n    }\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Modal');\n    });\n  }\n  onCancel() {\n    this.cancelTriggered.emit();\n  }\n  onOk() {\n    this.okTriggered.emit();\n  }\n  /**\n   * Returns the value of the specified key.\n   * If it is a function, run and return the return value of the function.\n   */\n  getButtonCallableProp(options, prop) {\n    const value = options[prop];\n    const componentInstance = this.modalRef.getContentComponent();\n    return typeof value === 'function' ? value.apply(options, componentInstance && [componentInstance]) : value;\n  }\n  /**\n   * Run function based on the type and set its `loading` prop if needed.\n   */\n  onButtonClick(options) {\n    const loading = this.getButtonCallableProp(options, 'loading');\n    if (!loading) {\n      const result = this.getButtonCallableProp(options, 'onClick');\n      if (options.autoLoading && isPromise(result)) {\n        options.loading = true;\n        result.then(() => options.loading = false).catch(e => {\n          options.loading = false;\n          throw e;\n        });\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzModalFooterComponent_Factory(t) {\n      return new (t || NzModalFooterComponent)(i0.ɵɵdirectiveInject(i1$1.NzI18nService), i0.ɵɵdirectiveInject(ModalOptions));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzModalFooterComponent,\n      selectors: [[\"div\", \"nz-modal-footer\", \"\"]],\n      hostAttrs: [1, \"ant-modal-footer\"],\n      inputs: {\n        modalRef: \"modalRef\"\n      },\n      outputs: {\n        cancelTriggered: \"cancelTriggered\",\n        okTriggered: \"okTriggered\"\n      },\n      exportAs: [\"NzModalFooterBuiltin\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      decls: 2,\n      vars: 1,\n      consts: [[4, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"], [\"nz-button\", \"\", 3, \"hidden\", \"nzLoading\", \"disabled\", \"nzType\", \"nzDanger\", \"nzShape\", \"nzSize\", \"nzGhost\"], [\"nz-button\", \"\", 3, \"click\", \"hidden\", \"nzLoading\", \"disabled\", \"nzType\", \"nzDanger\", \"nzShape\", \"nzSize\", \"nzGhost\"], [3, \"innerHTML\"], [\"nz-button\", \"\", 3, \"nzLoading\", \"disabled\"], [\"nz-button\", \"\", 3, \"nzType\", \"nzDanger\", \"nzLoading\", \"disabled\"], [\"nz-button\", \"\", 3, \"click\", \"nzLoading\", \"disabled\"], [\"nz-button\", \"\", 3, \"click\", \"nzType\", \"nzDanger\", \"nzLoading\", \"disabled\"]],\n      template: function NzModalFooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzModalFooterComponent_Conditional_0_Template, 1, 5, \"ng-container\")(1, NzModalFooterComponent_Conditional_1_Template, 2, 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.config.nzFooter ? 0 : 1);\n        }\n      },\n      dependencies: [NzOutletModule, i3.NzStringTemplateOutletDirective, NzButtonModule, i10.NzButtonComponent, i11.ɵNzTransitionPatchDirective, i12.NzWaveDirective],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzModalFooterComponent, [{\n    type: Component,\n    args: [{\n      selector: 'div[nz-modal-footer]',\n      exportAs: 'NzModalFooterBuiltin',\n      template: `\n    @if (config.nzFooter) {\n      <ng-container\n        *nzStringTemplateOutlet=\"config.nzFooter; context: { $implicit: config.nzData, modalRef: modalRef }\"\n      >\n        @if (buttonsFooter) {\n          @for (button of buttons; track button) {\n            <button\n              nz-button\n              (click)=\"onButtonClick(button)\"\n              [hidden]=\"!getButtonCallableProp(button, 'show')\"\n              [nzLoading]=\"getButtonCallableProp(button, 'loading')\"\n              [disabled]=\"getButtonCallableProp(button, 'disabled')\"\n              [nzType]=\"button.type!\"\n              [nzDanger]=\"button.danger\"\n              [nzShape]=\"button.shape!\"\n              [nzSize]=\"button.size!\"\n              [nzGhost]=\"button.ghost!\"\n            >\n              {{ button.label }}\n            </button>\n          }\n        } @else {\n          <div [innerHTML]=\"config.nzFooter\"></div>\n        }\n      </ng-container>\n    } @else {\n      @if (config.nzCancelText !== null) {\n        <button\n          [attr.cdkFocusInitial]=\"config.nzAutofocus === 'cancel' || null\"\n          nz-button\n          (click)=\"onCancel()\"\n          [nzLoading]=\"config.nzCancelLoading\"\n          [disabled]=\"config.nzCancelDisabled\"\n        >\n          {{ config.nzCancelText || locale.cancelText }}\n        </button>\n      }\n      @if (config.nzOkText !== null) {\n        <button\n          [attr.cdkFocusInitial]=\"config.nzAutofocus === 'ok' || null\"\n          nz-button\n          [nzType]=\"config.nzOkType!\"\n          [nzDanger]=\"config.nzOkDanger\"\n          (click)=\"onOk()\"\n          [nzLoading]=\"config.nzOkLoading\"\n          [disabled]=\"config.nzOkDisabled\"\n        >\n          {{ config.nzOkText || locale.okText }}\n        </button>\n      }\n    }\n  `,\n      host: {\n        class: 'ant-modal-footer'\n      },\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [NzOutletModule, NzButtonModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$1.NzI18nService\n  }, {\n    type: ModalOptions\n  }], {\n    cancelTriggered: [{\n      type: Output\n    }],\n    okTriggered: [{\n      type: Output\n    }],\n    modalRef: [{\n      type: Input\n    }]\n  });\n})();\nfunction mergeDefaultOption(options) {\n  return {\n    type: null,\n    size: 'default',\n    autoLoading: true,\n    show: true,\n    loading: false,\n    disabled: false,\n    ...options\n  };\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzModalTitleComponent {\n  constructor(config) {\n    this.config = config;\n  }\n  static {\n    this.ɵfac = function NzModalTitleComponent_Factory(t) {\n      return new (t || NzModalTitleComponent)(i0.ɵɵdirectiveInject(ModalOptions));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzModalTitleComponent,\n      selectors: [[\"div\", \"nz-modal-title\", \"\"]],\n      hostAttrs: [1, \"ant-modal-header\"],\n      exportAs: [\"NzModalTitleBuiltin\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c4,\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"ant-modal-title\"], [4, \"nzStringTemplateOutlet\"], [3, \"innerHTML\"]],\n      template: function NzModalTitleComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, NzModalTitleComponent_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.config.nzTitle);\n        }\n      },\n      dependencies: [NzOutletModule, i3.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzModalTitleComponent, [{\n    type: Component,\n    args: [{\n      selector: 'div[nz-modal-title]',\n      exportAs: 'NzModalTitleBuiltin',\n      template: `\n    <div class=\"ant-modal-title\">\n      <ng-container *nzStringTemplateOutlet=\"config.nzTitle\">\n        <div [innerHTML]=\"config.nzTitle\"></div>\n      </ng-container>\n    </div>\n  `,\n      host: {\n        class: 'ant-modal-header'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NzOutletModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: ModalOptions\n  }], null);\n})();\nclass NzModalContainerComponent extends BaseModalContainerComponent {\n  constructor(ngZone, host, focusTrapFactory, cdr, render, overlayRef, nzConfigService, config, document, animationType) {\n    super(ngZone, host, focusTrapFactory, cdr, render, overlayRef, nzConfigService, config, document, animationType);\n    this.config = config;\n  }\n  ngOnInit() {\n    this.setupMouseListeners(this.modalElementRef);\n  }\n  static {\n    this.ɵfac = function NzModalContainerComponent_Factory(t) {\n      return new (t || NzModalContainerComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusTrapFactory), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2$1.OverlayRef), i0.ɵɵdirectiveInject(i3$1.NzConfigService), i0.ɵɵdirectiveInject(ModalOptions), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzModalContainerComponent,\n      selectors: [[\"nz-modal-container\"]],\n      viewQuery: function NzModalContainerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n          i0.ɵɵviewQuery(_c1, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.portalOutlet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalElementRef = _t.first);\n        }\n      },\n      hostAttrs: [\"tabindex\", \"-1\", \"role\", \"dialog\"],\n      hostVars: 10,\n      hostBindings: function NzModalContainerComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵsyntheticHostListener(\"@modalContainer.start\", function NzModalContainerComponent_animation_modalContainer_start_HostBindingHandler($event) {\n            return ctx.onAnimationStart($event);\n          })(\"@modalContainer.done\", function NzModalContainerComponent_animation_modalContainer_done_HostBindingHandler($event) {\n            return ctx.onAnimationDone($event);\n          });\n          i0.ɵɵlistener(\"click\", function NzModalContainerComponent_click_HostBindingHandler($event) {\n            return ctx.onContainerClick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@.disabled\", ctx.config.nzNoAnimation)(\"@modalContainer\", ctx.state);\n          i0.ɵɵclassMap(ctx.config.nzWrapClassName ? \"ant-modal-wrap \" + ctx.config.nzWrapClassName : \"ant-modal-wrap\");\n          i0.ɵɵstyleProp(\"z-index\", ctx.config.nzZIndex);\n          i0.ɵɵclassProp(\"ant-modal-wrap-rtl\", ctx.dir === \"rtl\")(\"ant-modal-centered\", ctx.config.nzCentered);\n        }\n      },\n      exportAs: [\"nzModalContainer\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 10,\n      vars: 12,\n      consts: [[\"modalElement\", \"\"], [\"cdkDrag\", \"\", \"cdkDragBoundary\", \".cdk-overlay-container\", \"role\", \"document\", 1, \"ant-modal\", 3, \"cdkDragDisabled\", \"ngClass\", \"ngStyle\"], [1, \"ant-modal-content\"], [\"nz-modal-close\", \"\"], [\"nz-modal-title\", \"\", \"cdkDragHandle\", \"\", 3, \"cursor\"], [1, \"ant-modal-body\", 3, \"ngStyle\"], [\"cdkPortalOutlet\", \"\"], [3, \"innerHTML\"], [\"nz-modal-footer\", \"\", 3, \"modalRef\"], [\"nz-modal-close\", \"\", 3, \"click\"], [\"nz-modal-title\", \"\", \"cdkDragHandle\", \"\"], [\"nz-modal-footer\", \"\", 3, \"cancelTriggered\", \"okTriggered\", \"modalRef\"]],\n      template: function NzModalContainerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1, 0);\n          i0.ɵɵpipe(2, \"nzToCssUnit\");\n          i0.ɵɵelementStart(3, \"div\", 2);\n          i0.ɵɵtemplate(4, NzModalContainerComponent_Conditional_4_Template, 1, 0, \"button\", 3)(5, NzModalContainerComponent_Conditional_5_Template, 1, 2, \"div\", 4);\n          i0.ɵɵelementStart(6, \"div\", 5);\n          i0.ɵɵtemplate(7, NzModalContainerComponent_ng_template_7_Template, 0, 0, \"ng-template\", 6)(8, NzModalContainerComponent_Conditional_8_Template, 1, 1, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, NzModalContainerComponent_Conditional_9_Template, 1, 1, \"div\", 8);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"width\", i0.ɵɵpipeBind1(2, 10, ctx.config == null ? null : ctx.config.nzWidth));\n          i0.ɵɵproperty(\"cdkDragDisabled\", !ctx.config.nzDraggable)(\"ngClass\", ctx.config.nzClassName)(\"ngStyle\", ctx.config.nzStyle);\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(4, ctx.config.nzClosable ? 4 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(5, ctx.config.nzTitle ? 5 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", ctx.config.nzBodyStyle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(8, ctx.isStringContent ? 8 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(9, ctx.config.nzFooter !== null ? 9 : -1);\n        }\n      },\n      dependencies: [NgClass, NgStyle, NzModalCloseComponent, NzModalTitleComponent, PortalModule, i9.CdkPortalOutlet, NzModalFooterComponent, NzPipesModule, i6.NzToCssUnitPipe, CdkDrag, CdkDragHandle],\n      encapsulation: 2,\n      data: {\n        animation: [nzModalAnimations.modalContainer]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzModalContainerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-modal-container',\n      exportAs: 'nzModalContainer',\n      template: `\n    <div\n      #modalElement\n      cdkDrag\n      cdkDragBoundary=\".cdk-overlay-container\"\n      [cdkDragDisabled]=\"!config.nzDraggable\"\n      role=\"document\"\n      class=\"ant-modal\"\n      [ngClass]=\"config.nzClassName!\"\n      [ngStyle]=\"config.nzStyle!\"\n      [style.width]=\"config?.nzWidth! | nzToCssUnit\"\n    >\n      <div class=\"ant-modal-content\">\n        @if (config.nzClosable) {\n          <button nz-modal-close (click)=\"onCloseClick()\"></button>\n        }\n        @if (config.nzTitle) {\n          <div nz-modal-title cdkDragHandle [style.cursor]=\"config.nzDraggable ? 'move' : 'auto'\"></div>\n        }\n\n        <div class=\"ant-modal-body\" [ngStyle]=\"config.nzBodyStyle!\">\n          <ng-template cdkPortalOutlet />\n          @if (isStringContent) {\n            <div [innerHTML]=\"config.nzContent\"></div>\n          }\n        </div>\n        @if (config.nzFooter !== null) {\n          <div\n            nz-modal-footer\n            [modalRef]=\"modalRef\"\n            (cancelTriggered)=\"onCloseClick()\"\n            (okTriggered)=\"onOkClick()\"\n          ></div>\n        }\n      </div>\n    </div>\n  `,\n      animations: [nzModalAnimations.modalContainer],\n      // Using OnPush for modal caused footer can not to detect changes. we can fix it when 8.x.\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        tabindex: '-1',\n        role: 'dialog',\n        '[class]': 'config.nzWrapClassName ? \"ant-modal-wrap \" + config.nzWrapClassName : \"ant-modal-wrap\"',\n        '[class.ant-modal-wrap-rtl]': `dir === 'rtl'`,\n        '[class.ant-modal-centered]': 'config.nzCentered',\n        '[style.zIndex]': 'config.nzZIndex',\n        '[@.disabled]': 'config.nzNoAnimation',\n        '[@modalContainer]': 'state',\n        '(@modalContainer.start)': 'onAnimationStart($event)',\n        '(@modalContainer.done)': 'onAnimationDone($event)',\n        '(click)': 'onContainerClick($event)'\n      },\n      imports: [NgClass, NgStyle, NzModalCloseComponent, NzModalTitleComponent, PortalModule, NzModalFooterComponent, NzPipesModule, CdkDrag, CdkDragHandle],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1.FocusTrapFactory\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i2$1.OverlayRef\n  }, {\n    type: i3$1.NzConfigService\n  }, {\n    type: ModalOptions\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }],\n    modalElementRef: [{\n      type: ViewChild,\n      args: ['modalElement', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzModalRef {\n  constructor(overlayRef, config, containerInstance) {\n    this.overlayRef = overlayRef;\n    this.config = config;\n    this.containerInstance = containerInstance;\n    this.componentInstance = null;\n    this.componentRef = null;\n    this.state = 0 /* NzModalState.OPEN */;\n    this.afterClose = new Subject();\n    this.afterOpen = new Subject();\n    this.destroy$ = new Subject();\n    containerInstance.animationStateChanged.pipe(filter(event => event.phaseName === 'done' && event.toState === 'enter'), take(1)).subscribe(() => {\n      this.afterOpen.next();\n      this.afterOpen.complete();\n      if (config.nzAfterOpen instanceof EventEmitter) {\n        config.nzAfterOpen.emit();\n      }\n    });\n    containerInstance.animationStateChanged.pipe(filter(event => event.phaseName === 'done' && event.toState === 'exit'), take(1)).subscribe(() => {\n      clearTimeout(this.closeTimeout);\n      this._finishDialogClose();\n    });\n    containerInstance.containerClick.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      const cancelable = !this.config.nzCancelLoading && !this.config.nzOkLoading;\n      if (cancelable) {\n        this.trigger(\"cancel\" /* NzTriggerAction.CANCEL */);\n      }\n    });\n    overlayRef.keydownEvents().pipe(filter(event => this.config.nzKeyboard && !this.config.nzCancelLoading && !this.config.nzOkLoading && event.keyCode === ESCAPE && !hasModifierKey(event))).subscribe(event => {\n      event.preventDefault();\n      this.trigger(\"cancel\" /* NzTriggerAction.CANCEL */);\n    });\n    containerInstance.cancelTriggered.pipe(takeUntil(this.destroy$)).subscribe(() => this.trigger(\"cancel\" /* NzTriggerAction.CANCEL */));\n    containerInstance.okTriggered.pipe(takeUntil(this.destroy$)).subscribe(() => this.trigger(\"ok\" /* NzTriggerAction.OK */));\n    overlayRef.detachments().subscribe(() => {\n      this.afterClose.next(this.result);\n      this.afterClose.complete();\n      if (config.nzAfterClose instanceof EventEmitter) {\n        config.nzAfterClose.emit(this.result);\n      }\n      this.componentInstance = null;\n      this.componentRef = null;\n      this.overlayRef.dispose();\n    });\n  }\n  getContentComponent() {\n    return this.componentInstance;\n  }\n  getContentComponentRef() {\n    return this.componentRef;\n  }\n  getElement() {\n    return this.containerInstance.getNativeElement();\n  }\n  destroy(result) {\n    this.close(result);\n  }\n  triggerOk() {\n    return this.trigger(\"ok\" /* NzTriggerAction.OK */);\n  }\n  triggerCancel() {\n    return this.trigger(\"cancel\" /* NzTriggerAction.CANCEL */);\n  }\n  close(result) {\n    if (this.state !== 0 /* NzModalState.OPEN */) {\n      return;\n    }\n    this.result = result;\n    this.containerInstance.animationStateChanged.pipe(filter(event => event.phaseName === 'start'), take(1)).subscribe(event => {\n      this.overlayRef.detachBackdrop();\n      this.closeTimeout = setTimeout(() => {\n        this._finishDialogClose();\n      }, event.totalTime + 100);\n    });\n    this.containerInstance.startExitAnimation();\n    this.state = 1 /* NzModalState.CLOSING */;\n  }\n  updateConfig(config) {\n    Object.assign(this.config, config);\n    this.containerInstance.bindBackdropStyle();\n    this.containerInstance.cdr.markForCheck();\n  }\n  getState() {\n    return this.state;\n  }\n  getConfig() {\n    return this.config;\n  }\n  getBackdropElement() {\n    return this.overlayRef.backdropElement;\n  }\n  async trigger(action) {\n    if (this.state === 1 /* NzModalState.CLOSING */) {\n      return;\n    }\n    const trigger = {\n      ok: this.config.nzOnOk,\n      cancel: this.config.nzOnCancel\n    }[action];\n    const loadingKey = {\n      ok: 'nzOkLoading',\n      cancel: 'nzCancelLoading'\n    }[action];\n    const loading = this.config[loadingKey];\n    if (loading) {\n      return;\n    }\n    if (trigger instanceof EventEmitter) {\n      trigger.emit(this.getContentComponent());\n    } else if (typeof trigger === 'function') {\n      const result = trigger(this.getContentComponent());\n      if (isPromise(result)) {\n        this.config[loadingKey] = true;\n        let doClose = false;\n        try {\n          doClose = await result;\n        } finally {\n          this.config[loadingKey] = false;\n          this.closeWhitResult(doClose);\n        }\n      } else {\n        this.closeWhitResult(result);\n      }\n    }\n  }\n  closeWhitResult(result) {\n    if (result !== false) {\n      this.close(result);\n    }\n  }\n  _finishDialogClose() {\n    this.state = 2 /* NzModalState.CLOSED */;\n    this.overlayRef.dispose();\n    this.destroy$.next();\n  }\n}\nclass NzModalService {\n  get openModals() {\n    return this.parentModal ? this.parentModal.openModals : this.openModalsAtThisLevel;\n  }\n  get _afterAllClosed() {\n    const parent = this.parentModal;\n    return parent ? parent._afterAllClosed : this.afterAllClosedAtThisLevel;\n  }\n  constructor(overlay, injector, nzConfigService, parentModal, directionality) {\n    this.overlay = overlay;\n    this.injector = injector;\n    this.nzConfigService = nzConfigService;\n    this.parentModal = parentModal;\n    this.directionality = directionality;\n    this.openModalsAtThisLevel = [];\n    this.afterAllClosedAtThisLevel = new Subject();\n    this.afterAllClose = defer(() => this.openModals.length ? this._afterAllClosed : this._afterAllClosed.pipe(startWith(undefined)));\n  }\n  create(config) {\n    return this.open(config.nzContent, config);\n  }\n  closeAll() {\n    this.closeModals(this.openModals);\n  }\n  confirm(options = {}, confirmType = 'confirm') {\n    if ('nzFooter' in options) {\n      warn(`The Confirm-Modal doesn't support \"nzFooter\", this property will be ignored.`);\n    }\n    if (!('nzWidth' in options)) {\n      options.nzWidth = 416;\n    }\n    if (!('nzMaskClosable' in options)) {\n      options.nzMaskClosable = false;\n    }\n    options.nzModalType = 'confirm';\n    options.nzClassName = `ant-modal-confirm ant-modal-confirm-${confirmType} ${options.nzClassName || ''}`;\n    return this.create(options);\n  }\n  info(options = {}) {\n    return this.confirmFactory(options, 'info');\n  }\n  success(options = {}) {\n    return this.confirmFactory(options, 'success');\n  }\n  error(options = {}) {\n    return this.confirmFactory(options, 'error');\n  }\n  warning(options = {}) {\n    return this.confirmFactory(options, 'warning');\n  }\n  open(componentOrTemplateRef, config) {\n    const configMerged = applyConfigDefaults(config || {}, new ModalOptions());\n    const overlayRef = this.createOverlay(configMerged);\n    const modalContainer = this.attachModalContainer(overlayRef, configMerged);\n    const modalRef = this.attachModalContent(componentOrTemplateRef, modalContainer, overlayRef, configMerged);\n    modalContainer.modalRef = modalRef;\n    overlayZIndexSetter(overlayRef, config?.nzZIndex);\n    this.openModals.push(modalRef);\n    modalRef.afterClose.subscribe(() => this.removeOpenModal(modalRef));\n    return modalRef;\n  }\n  removeOpenModal(modalRef) {\n    const index = this.openModals.indexOf(modalRef);\n    if (index > -1) {\n      this.openModals.splice(index, 1);\n      if (!this.openModals.length) {\n        this._afterAllClosed.next();\n      }\n    }\n  }\n  closeModals(dialogs) {\n    let i = dialogs.length;\n    while (i--) {\n      dialogs[i].close();\n      if (!this.openModals.length) {\n        this._afterAllClosed.next();\n      }\n    }\n  }\n  createOverlay(config) {\n    const globalConfig = this.nzConfigService.getConfigForComponent(NZ_CONFIG_MODULE_NAME) || {};\n    const overlayConfig = new OverlayConfig({\n      hasBackdrop: true,\n      scrollStrategy: this.overlay.scrollStrategies.block(),\n      positionStrategy: this.overlay.position().global(),\n      disposeOnNavigation: getValueWithConfig(config.nzCloseOnNavigation, globalConfig.nzCloseOnNavigation, true),\n      direction: getValueWithConfig(config.nzDirection, globalConfig.nzDirection, this.directionality.value)\n    });\n    if (getValueWithConfig(config.nzMask, globalConfig.nzMask, true)) {\n      overlayConfig.backdropClass = MODAL_MASK_CLASS_NAME;\n    }\n    return this.overlay.create(overlayConfig);\n  }\n  attachModalContainer(overlayRef, config) {\n    const userInjector = config && config.nzViewContainerRef && config.nzViewContainerRef.injector;\n    const injector = Injector.create({\n      parent: userInjector || this.injector,\n      providers: [{\n        provide: OverlayRef,\n        useValue: overlayRef\n      }, {\n        provide: ModalOptions,\n        useValue: config\n      }]\n    });\n    const ContainerComponent = config.nzModalType === 'confirm' ?\n    // If the mode is `confirm`, use `NzModalConfirmContainerComponent`\n    NzModalConfirmContainerComponent :\n    // If the mode is not `confirm`, use `NzModalContainerComponent`\n    NzModalContainerComponent;\n    const containerPortal = new ComponentPortal(ContainerComponent, config.nzViewContainerRef, injector);\n    const containerRef = overlayRef.attach(containerPortal);\n    return containerRef.instance;\n  }\n  attachModalContent(componentOrTemplateRef, modalContainer, overlayRef, config) {\n    const modalRef = new NzModalRef(overlayRef, config, modalContainer);\n    if (componentOrTemplateRef instanceof TemplateRef) {\n      modalContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, {\n        $implicit: config.nzData,\n        modalRef\n      }));\n    } else if (isNotNil(componentOrTemplateRef) && typeof componentOrTemplateRef !== 'string') {\n      const injector = this.createInjector(modalRef, config);\n      const contentRef = modalContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.nzViewContainerRef, injector));\n      modalRef.componentRef = contentRef;\n      modalRef.componentInstance = contentRef.instance;\n    } else {\n      modalContainer.attachStringContent();\n    }\n    return modalRef;\n  }\n  createInjector(modalRef, config) {\n    const userInjector = config && config.nzViewContainerRef && config.nzViewContainerRef.injector;\n    return Injector.create({\n      parent: userInjector || this.injector,\n      providers: [{\n        provide: NzModalRef,\n        useValue: modalRef\n      }, {\n        provide: NZ_MODAL_DATA,\n        useValue: config.nzData\n      }]\n    });\n  }\n  confirmFactory(options = {}, confirmType) {\n    const iconMap = {\n      info: 'info-circle',\n      success: 'check-circle',\n      error: 'close-circle',\n      warning: 'exclamation-circle'\n    };\n    if (!('nzIconType' in options)) {\n      options.nzIconType = iconMap[confirmType];\n    }\n    if (!('nzCancelText' in options)) {\n      // Remove the Cancel button if the user not specify a Cancel button\n      options.nzCancelText = null;\n    }\n    return this.confirm(options, confirmType);\n  }\n  ngOnDestroy() {\n    this.closeModals(this.openModalsAtThisLevel);\n    this.afterAllClosedAtThisLevel.complete();\n  }\n  static {\n    this.ɵfac = function NzModalService_Factory(t) {\n      return new (t || NzModalService)(i0.ɵɵinject(i2$1.Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i3$1.NzConfigService), i0.ɵɵinject(NzModalService, 12), i0.ɵɵinject(i3$2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzModalService,\n      factory: NzModalService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzModalService, [{\n    type: Injectable\n  }], () => [{\n    type: i2$1.Overlay\n  }, {\n    type: i0.Injector\n  }, {\n    type: i3$1.NzConfigService\n  }, {\n    type: NzModalService,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: i3$2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzModalContentDirective {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n  }\n  static {\n    this.ɵfac = function NzModalContentDirective_Factory(t) {\n      return new (t || NzModalContentDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzModalContentDirective,\n      selectors: [[\"\", \"nzModalContent\", \"\"]],\n      exportAs: [\"nzModalContent\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzModalContentDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzModalContent]',\n      exportAs: 'nzModalContent',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzModalFooterDirective {\n  constructor(nzModalRef, templateRef) {\n    this.nzModalRef = nzModalRef;\n    this.templateRef = templateRef;\n    if (this.nzModalRef) {\n      this.nzModalRef.updateConfig({\n        nzFooter: this.templateRef\n      });\n    }\n  }\n  static {\n    this.ɵfac = function NzModalFooterDirective_Factory(t) {\n      return new (t || NzModalFooterDirective)(i0.ɵɵdirectiveInject(NzModalRef, 8), i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzModalFooterDirective,\n      selectors: [[\"\", \"nzModalFooter\", \"\"]],\n      exportAs: [\"nzModalFooter\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzModalFooterDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzModalFooter]',\n      exportAs: 'nzModalFooter',\n      standalone: true\n    }]\n  }], () => [{\n    type: NzModalRef,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.TemplateRef\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzModalTitleDirective {\n  constructor(nzModalRef, templateRef) {\n    this.nzModalRef = nzModalRef;\n    this.templateRef = templateRef;\n    if (this.nzModalRef) {\n      this.nzModalRef.updateConfig({\n        nzTitle: this.templateRef\n      });\n    }\n  }\n  static {\n    this.ɵfac = function NzModalTitleDirective_Factory(t) {\n      return new (t || NzModalTitleDirective)(i0.ɵɵdirectiveInject(NzModalRef, 8), i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzModalTitleDirective,\n      selectors: [[\"\", \"nzModalTitle\", \"\"]],\n      exportAs: [\"nzModalTitle\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzModalTitleDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzModalTitle]',\n      exportAs: 'nzModalTitle',\n      standalone: true\n    }]\n  }], () => [{\n    type: NzModalRef,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NzModalComponent {\n  set modalTitle(value) {\n    if (value) {\n      this.setTitleWithTemplate(value);\n    }\n  }\n  set modalFooter(value) {\n    if (value) {\n      this.setFooterWithTemplate(value);\n    }\n  }\n  get afterOpen() {\n    // Observable alias for nzAfterOpen\n    return this.nzAfterOpen.asObservable();\n  }\n  get afterClose() {\n    // Observable alias for nzAfterClose\n    return this.nzAfterClose.asObservable();\n  }\n  constructor(cdr, modal, viewContainerRef) {\n    this.cdr = cdr;\n    this.modal = modal;\n    this.viewContainerRef = viewContainerRef;\n    this.nzVisible = false;\n    this.nzClosable = true;\n    this.nzOkLoading = false;\n    this.nzOkDisabled = false;\n    this.nzCancelDisabled = false;\n    this.nzCancelLoading = false;\n    this.nzKeyboard = true;\n    this.nzNoAnimation = false;\n    this.nzCentered = false;\n    this.nzDraggable = false;\n    this.nzZIndex = 1000;\n    this.nzWidth = 520;\n    this.nzCloseIcon = 'close';\n    this.nzOkType = 'primary';\n    this.nzOkDanger = false;\n    this.nzIconType = 'question-circle'; // Confirm Modal ONLY\n    this.nzModalType = 'default';\n    this.nzAutofocus = 'auto';\n    // TODO(@hsuanxyz) Input will not be supported\n    this.nzOnOk = new EventEmitter();\n    // TODO(@hsuanxyz) Input will not be supported\n    this.nzOnCancel = new EventEmitter();\n    this.nzAfterOpen = new EventEmitter();\n    this.nzAfterClose = new EventEmitter();\n    this.nzVisibleChange = new EventEmitter();\n    this.modalRef = null;\n    this.destroy$ = new Subject();\n  }\n  open() {\n    if (!this.nzVisible) {\n      this.nzVisible = true;\n      this.nzVisibleChange.emit(true);\n    }\n    if (!this.modalRef) {\n      const config = this.getConfig();\n      this.modalRef = this.modal.create(config);\n      // When the modal is implicitly closed (e.g. closeAll) the nzVisible needs to be set to the correct value and emit.\n      this.modalRef.afterClose.asObservable().pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.close();\n      });\n    }\n  }\n  close(result) {\n    if (this.nzVisible) {\n      this.nzVisible = false;\n      this.nzVisibleChange.emit(false);\n    }\n    if (this.modalRef) {\n      this.modalRef.close(result);\n      this.modalRef = null;\n    }\n  }\n  destroy(result) {\n    this.close(result);\n  }\n  triggerOk() {\n    this.modalRef?.triggerOk();\n  }\n  triggerCancel() {\n    this.modalRef?.triggerCancel();\n  }\n  getContentComponent() {\n    return this.modalRef?.getContentComponent();\n  }\n  getElement() {\n    return this.modalRef?.getElement();\n  }\n  getModalRef() {\n    return this.modalRef;\n  }\n  setTitleWithTemplate(templateRef) {\n    this.nzTitle = templateRef;\n    if (this.modalRef) {\n      // If modalRef already created, set the title in next tick\n      Promise.resolve().then(() => {\n        this.modalRef.updateConfig({\n          nzTitle: this.nzTitle\n        });\n      });\n    }\n  }\n  setFooterWithTemplate(templateRef) {\n    this.nzFooter = templateRef;\n    if (this.modalRef) {\n      // If modalRef already created, set the footer in next tick\n      Promise.resolve().then(() => {\n        this.modalRef.updateConfig({\n          nzFooter: this.nzFooter\n        });\n      });\n    }\n    this.cdr.markForCheck();\n  }\n  getConfig() {\n    const componentConfig = getConfigFromComponent(this);\n    componentConfig.nzViewContainerRef = this.viewContainerRef;\n    componentConfig.nzContent = this.nzContent || this.contentFromContentChild;\n    return componentConfig;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzVisible,\n      ...otherChanges\n    } = changes;\n    if (Object.keys(otherChanges).length && this.modalRef) {\n      this.modalRef.updateConfig(getConfigFromComponent(this));\n    }\n    if (nzVisible) {\n      if (this.nzVisible) {\n        this.open();\n      } else {\n        this.close();\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.modalRef?._finishDialogClose();\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzModalComponent_Factory(t) {\n      return new (t || NzModalComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(NzModalService), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzModalComponent,\n      selectors: [[\"nz-modal\"]],\n      contentQueries: function NzModalComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzModalTitleDirective, 7, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NzModalContentDirective, 7, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NzModalFooterDirective, 7, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalTitle = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentFromContentChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalFooter = _t.first);\n        }\n      },\n      inputs: {\n        nzMask: \"nzMask\",\n        nzMaskClosable: \"nzMaskClosable\",\n        nzCloseOnNavigation: \"nzCloseOnNavigation\",\n        nzVisible: \"nzVisible\",\n        nzClosable: \"nzClosable\",\n        nzOkLoading: \"nzOkLoading\",\n        nzOkDisabled: \"nzOkDisabled\",\n        nzCancelDisabled: \"nzCancelDisabled\",\n        nzCancelLoading: \"nzCancelLoading\",\n        nzKeyboard: \"nzKeyboard\",\n        nzNoAnimation: \"nzNoAnimation\",\n        nzCentered: \"nzCentered\",\n        nzDraggable: \"nzDraggable\",\n        nzContent: \"nzContent\",\n        nzFooter: \"nzFooter\",\n        nzZIndex: \"nzZIndex\",\n        nzWidth: \"nzWidth\",\n        nzWrapClassName: \"nzWrapClassName\",\n        nzClassName: \"nzClassName\",\n        nzStyle: \"nzStyle\",\n        nzTitle: \"nzTitle\",\n        nzCloseIcon: \"nzCloseIcon\",\n        nzMaskStyle: \"nzMaskStyle\",\n        nzBodyStyle: \"nzBodyStyle\",\n        nzOkText: \"nzOkText\",\n        nzCancelText: \"nzCancelText\",\n        nzOkType: \"nzOkType\",\n        nzOkDanger: \"nzOkDanger\",\n        nzIconType: \"nzIconType\",\n        nzModalType: \"nzModalType\",\n        nzAutofocus: \"nzAutofocus\",\n        nzOnOk: \"nzOnOk\",\n        nzOnCancel: \"nzOnCancel\"\n      },\n      outputs: {\n        nzOnOk: \"nzOnOk\",\n        nzOnCancel: \"nzOnCancel\",\n        nzAfterOpen: \"nzAfterOpen\",\n        nzAfterClose: \"nzAfterClose\",\n        nzVisibleChange: \"nzVisibleChange\"\n      },\n      exportAs: [\"nzModal\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function NzModalComponent_Template(rf, ctx) {},\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzMask\", void 0);\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzMaskClosable\", void 0);\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzCloseOnNavigation\", void 0);\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzVisible\", void 0);\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzClosable\", void 0);\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzOkLoading\", void 0);\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzOkDisabled\", void 0);\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzCancelDisabled\", void 0);\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzCancelLoading\", void 0);\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzKeyboard\", void 0);\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzNoAnimation\", void 0);\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzCentered\", void 0);\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzDraggable\", void 0);\n__decorate([InputBoolean()], NzModalComponent.prototype, \"nzOkDanger\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzModalComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-modal',\n      exportAs: 'nzModal',\n      template: ``,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: NzModalService\n  }, {\n    type: i0.ViewContainerRef\n  }], {\n    nzMask: [{\n      type: Input\n    }],\n    nzMaskClosable: [{\n      type: Input\n    }],\n    nzCloseOnNavigation: [{\n      type: Input\n    }],\n    nzVisible: [{\n      type: Input\n    }],\n    nzClosable: [{\n      type: Input\n    }],\n    nzOkLoading: [{\n      type: Input\n    }],\n    nzOkDisabled: [{\n      type: Input\n    }],\n    nzCancelDisabled: [{\n      type: Input\n    }],\n    nzCancelLoading: [{\n      type: Input\n    }],\n    nzKeyboard: [{\n      type: Input\n    }],\n    nzNoAnimation: [{\n      type: Input\n    }],\n    nzCentered: [{\n      type: Input\n    }],\n    nzDraggable: [{\n      type: Input\n    }],\n    nzContent: [{\n      type: Input\n    }],\n    nzFooter: [{\n      type: Input\n    }],\n    nzZIndex: [{\n      type: Input\n    }],\n    nzWidth: [{\n      type: Input\n    }],\n    nzWrapClassName: [{\n      type: Input\n    }],\n    nzClassName: [{\n      type: Input\n    }],\n    nzStyle: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzCloseIcon: [{\n      type: Input\n    }],\n    nzMaskStyle: [{\n      type: Input\n    }],\n    nzBodyStyle: [{\n      type: Input\n    }],\n    nzOkText: [{\n      type: Input\n    }],\n    nzCancelText: [{\n      type: Input\n    }],\n    nzOkType: [{\n      type: Input\n    }],\n    nzOkDanger: [{\n      type: Input\n    }],\n    nzIconType: [{\n      type: Input\n    }],\n    nzModalType: [{\n      type: Input\n    }],\n    nzAutofocus: [{\n      type: Input\n    }],\n    nzOnOk: [{\n      type: Input\n    }, {\n      type: Output\n    }],\n    nzOnCancel: [{\n      type: Input\n    }, {\n      type: Output\n    }],\n    nzAfterOpen: [{\n      type: Output\n    }],\n    nzAfterClose: [{\n      type: Output\n    }],\n    nzVisibleChange: [{\n      type: Output\n    }],\n    modalTitle: [{\n      type: ContentChild,\n      args: [NzModalTitleDirective, {\n        static: true,\n        read: TemplateRef\n      }]\n    }],\n    contentFromContentChild: [{\n      type: ContentChild,\n      args: [NzModalContentDirective, {\n        static: true,\n        read: TemplateRef\n      }]\n    }],\n    modalFooter: [{\n      type: ContentChild,\n      args: [NzModalFooterDirective, {\n        static: true,\n        read: TemplateRef\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzModalModule {\n  static {\n    this.ɵfac = function NzModalModule_Factory(t) {\n      return new (t || NzModalModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzModalModule,\n      imports: [NzModalComponent, NzModalFooterDirective, NzModalContentDirective, NzModalCloseComponent, NzModalFooterComponent, NzModalTitleComponent, NzModalTitleDirective, NzModalContainerComponent, NzModalConfirmContainerComponent],\n      exports: [NzModalComponent, NzModalFooterDirective, NzModalContentDirective, NzModalTitleDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [NzModalService],\n      imports: [NzModalCloseComponent, NzModalFooterComponent, NzModalTitleComponent, NzModalContainerComponent, NzModalConfirmContainerComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzModalModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzModalComponent, NzModalFooterDirective, NzModalContentDirective, NzModalCloseComponent, NzModalFooterComponent, NzModalTitleComponent, NzModalTitleDirective, NzModalContainerComponent, NzModalConfirmContainerComponent],\n      exports: [NzModalComponent, NzModalFooterDirective, NzModalContentDirective, NzModalTitleDirective],\n      providers: [NzModalService]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzModalLegacyAPI {}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseModalContainerComponent, FADE_CLASS_NAME_MAP, MODAL_MASK_CLASS_NAME, ModalOptions, NZ_CONFIG_MODULE_NAME, NZ_MODAL_DATA, NzModalCloseComponent, NzModalComponent, NzModalConfirmContainerComponent, NzModalContainerComponent, NzModalContentDirective, NzModalFooterComponent, NzModalFooterDirective, NzModalLegacyAPI, NzModalModule, NzModalRef, NzModalService, NzModalTitleComponent, NzModalTitleDirective, ZOOM_CLASS_NAME_MAP, applyConfigDefaults, getConfigFromComponent, getValueWithConfig, nzModalAnimations, throwNzModalContentAlreadyAttachedError };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,UAAU,OAAO,QAAQ;AACvB,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,WAAW,GAAG;AACtB,aAAO;AAAA,IACT;AACA,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH,eAAO,IAAI,KAAK;AAAA,MAClB,KAAK;AACH,eAAO,IAAI,KAAK,IAAI,MAAM;AAAA,MAC5B,KAAK;AACH,eAAO,KAAK,IAAI,GAAG,KAAK;AAAA,MAC1B,KAAK;AACH,eAAO,KAAK,IAAI,GAAG,KAAK;AAAA,MAC1B;AACE,cAAM,MAAM,kEAAkE;AAAA,IAClF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,OAAO;AACL,SAAK,UAAU;AAAA,MACb,GAAG;AAAA,QACD,KAAK;AAAA,MACP;AAAA,MACA,IAAI;AAAA,QACF,KAAK,KAAK,IAAI,MAAM,CAAC;AAAA,QACrB,MAAM;AAAA,MACR;AAAA,MACA,IAAI;AAAA,QACF,KAAK,KAAK,IAAI,MAAM,CAAC;AAAA,QACrB,MAAM;AAAA,MACR;AAAA,MACA,IAAI;AAAA,QACF,KAAK,KAAK,IAAI,MAAM,CAAC;AAAA,QACrB,MAAM;AAAA,MACR;AAAA,MACA,IAAI;AAAA,QACF,KAAK,KAAK,IAAI,MAAM,CAAC;AAAA,QACrB,MAAM;AAAA,MACR;AAAA,MACA,IAAI;AAAA,QACF,KAAK,OAAO;AAAA,QACZ,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,OAAO,UAAU,GAAG,OAAO,KAAK,IAAI;AAC5C,QAAI,EAAE,eAAe,KAAK,KAAK,eAAe,OAAO,KAAK,UAAU,MAAM,KAAK,WAAW,IAAI;AAC5F,aAAO;AAAA,IACT;AACA,QAAI,QAAQ;AACZ,QAAI,OAAO;AACX,WAAO,SAAS,KAAK;AACnB,eAAS;AACT,aAAO,aAAY,QAAQ,IAAI,EAAE;AAAA,IACnC;AACA,QAAI,IAAI;AACN,YAAM,SAAS,aAAY,QAAQ,EAAE;AACrC,YAAM,SAAS,UAAU,aAAY,gBAAgB,QAAQ,KAAK,GAAG,OAAO;AAC5E,aAAO,aAAY,aAAa,QAAQ,EAAE;AAAA,IAC5C;AACA,eAAW,OAAO,aAAY,SAAS;AACrC,UAAI,aAAY,QAAQ,eAAe,GAAG,GAAG;AAC3C,cAAM,SAAS,aAAY,QAAQ,GAAG;AACtC,YAAI,QAAQ,OAAO,KAAK;AACtB,gBAAM,SAAS,UAAU,aAAY,gBAAgB,QAAQ,KAAK,GAAG,OAAO;AAC5E,iBAAO,aAAY,aAAa,QAAQ,GAAG;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,aAAa,QAAQ,MAAM;AAChC,WAAO,GAAG,MAAM,IAAI,IAAI;AAAA,EAC1B;AAAA,EACA,OAAO,gBAAgB,QAAQ,OAAO;AACpC,UAAM,OAAO,OAAO,OAAO,aAAY,QAAQ,OAAO,IAAI,IAAI;AAC9D,WAAO,OAAO,QAAQ,KAAK,MAAM;AAAA,EACnC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,GAAG;AAC1C,aAAO,KAAK,KAAK,cAAa;AAAA,IAChC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,UAAU,OAAO,cAAc,MAAM;AACnC,WAAO,OAAO,UAAU,WAAW,GAAG,KAAK,GAAG,WAAW,KAAK;AAAA,EAChE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,UAAU,OAAO,QAAQ,SAAS,IAAI;AACpC,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO;AAAA,IACT;AACA,UAAM,MAAM,OAAO,WAAW,cAAc,MAAM,SAAS;AAC3D,QAAI,MAAM,UAAU,KAAK;AACvB,aAAO;AAAA,IACT;AACA,WAAO,MAAM,UAAU,GAAG,GAAG,IAAI;AAAA,EACnC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AASH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,UAAU,OAAO,UAAU,IAAI;AAC7B,QAAI,MAAM,KAAK,GAAG;AAChB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,WAAW;AACrB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,UAAU,OAAO,OAAO,QAAQ;AAC9B,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,eAAO,KAAK,UAAU,wBAAwB,KAAK;AAAA,MACrD,KAAK;AACH,eAAO,KAAK,UAAU,yBAAyB,KAAK;AAAA,MACtD,KAAK;AACH,eAAO,KAAK,UAAU,uBAAuB,KAAK;AAAA,MACpD,KAAK;AACH,eAAO,KAAK,UAAU,+BAA+B,KAAK;AAAA,MAC5D;AACE,cAAM,IAAI,MAAM,6BAA6B;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAoB,kBAAqB,cAAc,EAAE,CAAC;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA,EAEf,UAAU,MAAM;AACd,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,GAAG;AACzC,aAAO,KAAK,KAAK,aAAY;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,QAAQ,CAAC,iBAAiB,gBAAgB,iBAAiB,YAAY,aAAa,iBAAiB,cAAc;AACzH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,gBAAgB,iBAAiB,YAAY,aAAa,iBAAiB,cAAc;AAAA,MACpH,SAAS,CAAC,iBAAiB,gBAAgB,iBAAiB,YAAY,aAAa,iBAAiB,cAAc;AAAA,IACtH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,KAAK;AAAA,MACf,SAAS,CAAC,KAAK;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACzVH,SAAS,aAAa,MAAM,QAAQA,sBAAqB;AACvD,WAAS,OAAO,QAAQ;AACtB,QAAI,OAAO,eAAe,GAAG,GAAG;AAC9B,YAAM,QAAQ,OAAO,GAAG;AACxB,UAAI,OAAO;AACT,aAAK,YAAY,KAAK,OAAOA,sBAAqB,IAAI,GAAG,IAAI,cAAc,EAAE;AAAA,MAC/E,OAAO;AACL,aAAK,eAAe,GAAG;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,6BAA6B,SAAS,QAAQ;AACrD,QAAM,aAAa,SAAS,KAAK;AACjC,eAAa,QAAQ,OAAO;AAAA,IAC1B,gBAAgB,SAAS,KAAK;AAAA,IAC9B,qBAAqB,SAAS,KAAK;AAAA,IACnC,+BAA+B,SAAS,KAAK;AAAA,IAC7C,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,oBAAoB;AAAA,EACtB,CAAC;AACH;AAQA,SAAS,iBAAiB,SAAS,QAAQA,sBAAqB;AAC9D,eAAa,QAAQ,OAAO;AAAA,IAC1B,UAAU,SAAS,KAAK;AAAA,IACxB,KAAK,SAAS,KAAK;AAAA,IACnB,SAAS,SAAS,KAAK;AAAA,IACvB,MAAM,SAAS,KAAK;AAAA,EACtB,GAAGA,oBAAmB;AACxB;AAKA,SAAS,kBAAkB,WAAW,kBAAkB;AACtD,SAAO,oBAAoB,oBAAoB,SAAS,YAAY,MAAM,mBAAmB;AAC/F;AAMA,SAAS,iBAAiB,QAAQ,YAAY;AAC5C,SAAO,MAAM,QAAQ,GAAG,WAAW,KAAK;AACxC,SAAO,MAAM,SAAS,GAAG,WAAW,MAAM;AAC1C,SAAO,MAAM,YAAY,aAAa,WAAW,MAAM,WAAW,GAAG;AACvE;AAMA,SAAS,aAAa,GAAG,GAAG;AAG1B,SAAO,eAAe,KAAK,MAAM,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC;AACzD;AAGA,SAAS,qBAAqB,SAAS;AACrC,QAAM,OAAO,QAAQ,sBAAsB;AAK3C,SAAO;AAAA,IACL,KAAK,KAAK;AAAA,IACV,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,IACb,MAAM,KAAK;AAAA,IACX,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,IACb,GAAG,KAAK;AAAA,IACR,GAAG,KAAK;AAAA,EACV;AACF;AAOA,SAAS,mBAAmB,YAAY,GAAG,GAAG;AAC5C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO,KAAK,OAAO,KAAK,UAAU,KAAK,QAAQ,KAAK;AACtD;AAOA,SAAS,cAAc,SAAS,KAAK,MAAM;AACzC,UAAQ,OAAO;AACf,UAAQ,SAAS,QAAQ,MAAM,QAAQ;AACvC,UAAQ,QAAQ;AAChB,UAAQ,QAAQ,QAAQ,OAAO,QAAQ;AACzC;AAQA,SAAS,qBAAqB,MAAM,WAAW,UAAU,UAAU;AACjE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,QAAQ;AAC3B,QAAM,aAAa,SAAS;AAC5B,SAAO,WAAW,MAAM,cAAc,WAAW,SAAS,cAAc,WAAW,OAAO,cAAc,WAAW,QAAQ;AAC7H;AAGA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,WAAW;AACrB,SAAK,YAAY;AAEjB,SAAK,YAAY,oBAAI,IAAI;AAAA,EAC3B;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA,EAEA,MAAM,UAAU;AACd,SAAK,MAAM;AACX,SAAK,UAAU,IAAI,KAAK,WAAW;AAAA,MACjC,gBAAgB,KAAK,0BAA0B;AAAA,IACjD,CAAC;AACD,aAAS,QAAQ,aAAW;AAC1B,WAAK,UAAU,IAAI,SAAS;AAAA,QAC1B,gBAAgB;AAAA,UACd,KAAK,QAAQ;AAAA,UACb,MAAM,QAAQ;AAAA,QAChB;AAAA,QACA,YAAY,qBAAqB,OAAO;AAAA,MAC1C,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,UAAM,SAAS,gBAAgB,KAAK;AACpC,UAAM,iBAAiB,KAAK,UAAU,IAAI,MAAM;AAChD,QAAI,CAAC,gBAAgB;AACnB,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,eAAe;AACtC,QAAI;AACJ,QAAI;AACJ,QAAI,WAAW,KAAK,WAAW;AAC7B,YAAM,yBAAyB,KAAK,0BAA0B;AAC9D,eAAS,uBAAuB;AAChC,gBAAU,uBAAuB;AAAA,IACnC,OAAO;AACL,eAAS,OAAO;AAChB,gBAAU,OAAO;AAAA,IACnB;AACA,UAAM,gBAAgB,eAAe,MAAM;AAC3C,UAAM,iBAAiB,eAAe,OAAO;AAG7C,SAAK,UAAU,QAAQ,CAAC,UAAU,SAAS;AACzC,UAAI,SAAS,cAAc,WAAW,QAAQ,OAAO,SAAS,IAAI,GAAG;AACnE,sBAAc,SAAS,YAAY,eAAe,cAAc;AAAA,MAClE;AAAA,IACF,CAAC;AACD,mBAAe,MAAM;AACrB,mBAAe,OAAO;AACtB,WAAO;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,4BAA4B;AAC1B,WAAO;AAAA,MACL,KAAK,OAAO;AAAA,MACZ,MAAM,OAAO;AAAA,IACf;AAAA,EACF;AACF;AAGA,SAAS,cAAc,MAAM;AAC3B,QAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,QAAM,oBAAoB,MAAM,iBAAiB,MAAM;AACvD,QAAM,WAAW,KAAK,SAAS,YAAY;AAE3C,QAAM,gBAAgB,IAAI;AAC1B,WAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,sBAAkB,CAAC,EAAE,gBAAgB,IAAI;AAAA,EAC3C;AACA,MAAI,aAAa,UAAU;AACzB,uBAAmB,MAAM,KAAK;AAAA,EAChC,WAAW,aAAa,WAAW,aAAa,YAAY,aAAa,YAAY;AACnF,sBAAkB,MAAM,KAAK;AAAA,EAC/B;AACA,eAAa,UAAU,MAAM,OAAO,kBAAkB;AACtD,eAAa,2BAA2B,MAAM,OAAO,iBAAiB;AACtE,SAAO;AACT;AAEA,SAAS,aAAa,UAAU,MAAM,OAAO,UAAU;AACrD,QAAM,qBAAqB,KAAK,iBAAiB,QAAQ;AACzD,MAAI,mBAAmB,QAAQ;AAC7B,UAAM,gBAAgB,MAAM,iBAAiB,QAAQ;AACrD,aAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAClD,eAAS,mBAAmB,CAAC,GAAG,cAAc,CAAC,CAAC;AAAA,IAClD;AAAA,EACF;AACF;AAEA,IAAI,gBAAgB;AAEpB,SAAS,kBAAkB,QAAQ,OAAO;AAExC,MAAI,MAAM,SAAS,QAAQ;AACzB,UAAM,QAAQ,OAAO;AAAA,EACvB;AAIA,MAAI,MAAM,SAAS,WAAW,MAAM,MAAM;AACxC,UAAM,OAAO,aAAa,MAAM,IAAI,IAAI,eAAe;AAAA,EACzD;AACF;AAEA,SAAS,mBAAmB,QAAQ,OAAO;AACzC,QAAM,UAAU,MAAM,WAAW,IAAI;AACrC,MAAI,SAAS;AAGX,QAAI;AACF,cAAQ,UAAU,QAAQ,GAAG,CAAC;AAAA,IAChC,QAAQ;AAAA,IAAC;AAAA,EACX;AACF;AAMA,SAAS,YAAY,SAAS,WAAW;AACvC,QAAM,YAAY,QAAQ;AAC1B,MAAI,UAAU,WAAW,KAAK,UAAU,CAAC,EAAE,aAAa,UAAU,cAAc;AAC9E,WAAO,UAAU,CAAC;AAAA,EACpB;AACA,QAAM,UAAU,UAAU,cAAc,KAAK;AAC7C,YAAU,QAAQ,UAAQ,QAAQ,YAAY,IAAI,CAAC;AACnD,SAAO;AACT;AAGA,SAAS,sBAAsB,OAAO;AAEpC,QAAM,aAAa,MAAM,YAAY,EAAE,QAAQ,IAAI,IAAI,KAAK,IAAI;AAChE,SAAO,WAAW,KAAK,IAAI;AAC7B;AAEA,SAAS,mCAAmC,SAAS;AACnD,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,yBAAyB,sBAAsB,eAAe,qBAAqB;AACzF,QAAM,WAAW,uBAAuB,KAAK,UAAQ,SAAS,eAAe,SAAS,KAAK;AAE3F,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AAGA,QAAM,gBAAgB,uBAAuB,QAAQ,QAAQ;AAC7D,QAAM,eAAe,sBAAsB,eAAe,qBAAqB;AAC/E,QAAM,YAAY,sBAAsB,eAAe,kBAAkB;AACzE,SAAO,sBAAsB,aAAa,aAAa,CAAC,IAAI,sBAAsB,UAAU,aAAa,CAAC;AAC5G;AAEA,SAAS,sBAAsB,eAAe,MAAM;AAClD,QAAM,QAAQ,cAAc,iBAAiB,IAAI;AACjD,SAAO,MAAM,MAAM,GAAG,EAAE,IAAI,UAAQ,KAAK,KAAK,CAAC;AACjD;AAGA,IAAM,sBAAsB,oBAAI,IAAI;AAAA;AAAA,EAEpC;AAAU,CAAC;AACX,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,WAAW,cAAc,YAAY,iBAAiB,kBAAkB,eAAe,uBAAuB,mBAAmB,SAAS;AACpJ,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,oBAAoB;AACzB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO,QAAQ;AACb,SAAK,WAAW,KAAK,eAAe;AACpC,WAAO,YAAY,KAAK,QAAQ;AAGhC,QAAI,iBAAiB,KAAK,UAAU;AAClC,WAAK,SAAS,aAAa,EAAE;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,SAAS,OAAO;AACrB,SAAK,sBAAsB,QAAQ;AACnC,SAAK,WAAW,KAAK,uBAAuB;AAAA,EAC9C;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,SAAS,MAAM,YAAY;AAAA,EAClC;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,SAAS,sBAAsB;AAAA,EAC7C;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,SAAS,UAAU,IAAI,SAAS;AAAA,EACvC;AAAA,EACA,wBAAwB;AACtB,WAAO,mCAAmC,KAAK,QAAQ;AAAA,EACzD;AAAA,EACA,iBAAiB,MAAM,SAAS;AAC9B,SAAK,SAAS,iBAAiB,MAAM,OAAO;AAAA,EAC9C;AAAA,EACA,oBAAoB,MAAM,SAAS;AACjC,SAAK,SAAS,oBAAoB,MAAM,OAAO;AAAA,EACjD;AAAA,EACA,iBAAiB;AACf,UAAM,gBAAgB,KAAK;AAC3B,UAAM,eAAe,KAAK;AAC1B,UAAM,kBAAkB,gBAAgB,cAAc,WAAW;AACjE,QAAI;AACJ,QAAI,mBAAmB,eAAe;AAGpC,YAAM,WAAW,cAAc,YAAY,KAAK,kBAAkB;AAClE,YAAM,UAAU,cAAc,cAAc,mBAAmB,iBAAiB,cAAc,OAAO;AACrG,cAAQ,cAAc;AACtB,gBAAU,YAAY,SAAS,KAAK,SAAS;AAC7C,WAAK,uBAAuB;AAC5B,UAAI,cAAc,WAAW;AAC3B,yBAAiB,SAAS,QAAQ;AAAA,MACpC,OAAO;AACL,gBAAQ,MAAM,YAAY,aAAa,KAAK,sBAAsB,GAAG,KAAK,sBAAsB,CAAC;AAAA,MACnG;AAAA,IACF,OAAO;AACL,gBAAU,cAAc,KAAK,YAAY;AACzC,uBAAiB,SAAS,KAAK,eAAe;AAC9C,UAAI,KAAK,mBAAmB;AAC1B,gBAAQ,MAAM,YAAY,KAAK;AAAA,MACjC;AAAA,IACF;AACA,iBAAa,QAAQ,OAAO;AAAA;AAAA;AAAA,MAG1B,kBAAkB;AAAA;AAAA,MAElB,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,WAAW,KAAK,UAAU;AAAA,IAC5B,GAAG,mBAAmB;AACtB,iCAA6B,SAAS,KAAK;AAC3C,YAAQ,UAAU,IAAI,kBAAkB;AACxC,YAAQ,aAAa,WAAW,QAAQ;AACxC,YAAQ,aAAa,OAAO,KAAK,UAAU;AAC3C,QAAI,cAAc;AAChB,UAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,qBAAa,QAAQ,eAAa,QAAQ,UAAU,IAAI,SAAS,CAAC;AAAA,MACpE,OAAO;AACL,gBAAQ,UAAU,IAAI,YAAY;AAAA,MACpC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAGA,IAAM,8BAA8B,gCAAgC;AAAA,EAClE,SAAS;AACX,CAAC;AAED,IAAM,6BAA6B,gCAAgC;AAAA,EACjE,SAAS;AACX,CAAC;AAED,IAAM,gCAAgC,gCAAgC;AAAA,EACpE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAOD,IAAM,0BAA0B;AAEhC,IAAM,0BAA0B,oBAAI,IAAI;AAAA;AAAA,EAExC;AAAU,CAAC;AAIX,IAAM,UAAN,MAAc;AAAA;AAAA,EAEZ,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,CAAC,EAAE,KAAK,kBAAkB,KAAK,eAAe;AAAA,EACzE;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,UAAU,KAAK,WAAW;AAC5B,WAAK,YAAY;AACjB,WAAK,8BAA8B;AACnC,WAAK,SAAS,QAAQ,YAAU,6BAA6B,QAAQ,KAAK,CAAC;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,YAAY,SAAS,SAAS,WAAW,SAAS,gBAAgB,mBAAmB;AACnF,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AAOzB,SAAK,oBAAoB;AAAA,MACvB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAEA,SAAK,mBAAmB;AAAA,MACtB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAKA,SAAK,sBAAsB;AAE3B,SAAK,cAAc,IAAI,QAAQ;AAE/B,SAAK,2BAA2B,aAAa;AAE7C,SAAK,yBAAyB,aAAa;AAE3C,SAAK,sBAAsB,aAAa;AAExC,SAAK,sBAAsB,aAAa;AAExC,SAAK,mBAAmB;AAExB,SAAK,6BAA6B;AAElC,SAAK,WAAW,CAAC;AAEjB,SAAK,mBAAmB,oBAAI,IAAI;AAEhC,SAAK,aAAa;AAKlB,SAAK,iBAAiB;AACtB,SAAK,YAAY;AAEjB,SAAK,gBAAgB,IAAI,QAAQ;AAEjC,SAAK,UAAU,IAAI,QAAQ;AAE3B,SAAK,WAAW,IAAI,QAAQ;AAE5B,SAAK,QAAQ,IAAI,QAAQ;AAEzB,SAAK,UAAU,IAAI,QAAQ;AAE3B,SAAK,SAAS,IAAI,QAAQ;AAE1B,SAAK,UAAU,IAAI,QAAQ;AAK3B,SAAK,QAAQ,KAAK;AAElB,SAAK,eAAe,WAAS;AAC3B,WAAK,cAAc,KAAK;AAExB,UAAI,KAAK,SAAS,QAAQ;AACxB,cAAM,eAAe,KAAK,iBAAiB,KAAK;AAChD,YAAI,gBAAgB,CAAC,KAAK,iBAAiB,IAAI,YAAY,KAAK,CAAC,KAAK,UAAU;AAC9E,eAAK,wBAAwB,cAAc,KAAK;AAAA,QAClD;AAAA,MACF,WAAW,CAAC,KAAK,UAAU;AACzB,aAAK,wBAAwB,KAAK,cAAc,KAAK;AAAA,MACvD;AAAA,IACF;AAEA,SAAK,eAAe,WAAS;AAC3B,YAAM,kBAAkB,KAAK,0BAA0B,KAAK;AAC5D,UAAI,CAAC,KAAK,qBAAqB;AAC7B,cAAM,YAAY,KAAK,IAAI,gBAAgB,IAAI,KAAK,sBAAsB,CAAC;AAC3E,cAAM,YAAY,KAAK,IAAI,gBAAgB,IAAI,KAAK,sBAAsB,CAAC;AAC3E,cAAM,kBAAkB,YAAY,aAAa,KAAK,QAAQ;AAK9D,YAAI,iBAAiB;AACnB,gBAAM,iBAAiB,KAAK,IAAI,KAAK,KAAK,iBAAiB,KAAK,mBAAmB,KAAK;AACxF,gBAAM,YAAY,KAAK;AACvB,cAAI,CAAC,gBAAgB;AACnB,iBAAK,iBAAiB,KAAK;AAC3B;AAAA,UACF;AAIA,cAAI,CAAC,aAAa,CAAC,UAAU,WAAW,KAAK,CAAC,UAAU,YAAY,GAAG;AAGrE,gBAAI,MAAM,YAAY;AACpB,oBAAM,eAAe;AAAA,YACvB;AACA,iBAAK,sBAAsB;AAC3B,iBAAK,QAAQ,IAAI,MAAM,KAAK,mBAAmB,KAAK,CAAC;AAAA,UACvD;AAAA,QACF;AACA;AAAA,MACF;AAIA,UAAI,MAAM,YAAY;AACpB,cAAM,eAAe;AAAA,MACvB;AACA,YAAM,6BAA6B,KAAK,+BAA+B,eAAe;AACtF,WAAK,YAAY;AACjB,WAAK,4BAA4B;AACjC,WAAK,6BAA6B,0BAA0B;AAC5D,UAAI,KAAK,gBAAgB;AACvB,aAAK,2BAA2B,4BAA4B,eAAe;AAAA,MAC7E,OAAO;AAGL,cAAM,SAAS,KAAK,oBAAoB,KAAK,kBAAkB,KAAK;AACpE,cAAM,kBAAkB,KAAK;AAC7B,wBAAgB,IAAI,2BAA2B,IAAI,OAAO,IAAI,KAAK,kBAAkB;AACrF,wBAAgB,IAAI,2BAA2B,IAAI,OAAO,IAAI,KAAK,kBAAkB;AACrF,aAAK,2BAA2B,gBAAgB,GAAG,gBAAgB,CAAC;AAAA,MACtE;AAIA,UAAI,KAAK,YAAY,UAAU,QAAQ;AACrC,aAAK,QAAQ,IAAI,MAAM;AACrB,eAAK,YAAY,KAAK;AAAA,YACpB,QAAQ;AAAA,YACR,iBAAiB;AAAA,YACjB;AAAA,YACA,UAAU,KAAK,iBAAiB,0BAA0B;AAAA,YAC1D,OAAO,KAAK;AAAA,UACd,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAEA,SAAK,aAAa,WAAS;AACzB,WAAK,iBAAiB,KAAK;AAAA,IAC7B;AAEA,SAAK,mBAAmB,WAAS;AAC/B,UAAI,KAAK,SAAS,QAAQ;AACxB,cAAM,eAAe,KAAK,iBAAiB,KAAK;AAChD,YAAI,gBAAgB,CAAC,KAAK,iBAAiB,IAAI,YAAY,KAAK,CAAC,KAAK,UAAU;AAC9E,gBAAM,eAAe;AAAA,QACvB;AAAA,MACF,WAAW,CAAC,KAAK,UAAU;AAGzB,cAAM,eAAe;AAAA,MACvB;AAAA,IACF;AACA,SAAK,gBAAgB,OAAO,EAAE,WAAW,QAAQ,iBAAiB,IAAI;AACtE,SAAK,mBAAmB,IAAI,sBAAsB,SAAS;AAC3D,sBAAkB,iBAAiB,IAAI;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,WAAO,KAAK,WAAW,IAAI,KAAK,sBAAsB,IAAI,KAAK,eAAe;AAAA,EAChF;AAAA;AAAA,EAEA,YAAY,SAAS;AACnB,SAAK,WAAW,QAAQ,IAAI,YAAU,cAAc,MAAM,CAAC;AAC3D,SAAK,SAAS,QAAQ,YAAU,6BAA6B,QAAQ,KAAK,QAAQ,CAAC;AACnF,SAAK,8BAA8B;AAKnC,UAAM,kBAAkB,oBAAI,IAAI;AAChC,SAAK,iBAAiB,QAAQ,YAAU;AACtC,UAAI,KAAK,SAAS,QAAQ,MAAM,IAAI,IAAI;AACtC,wBAAgB,IAAI,MAAM;AAAA,MAC5B;AAAA,IACF,CAAC;AACD,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,UAAU;AAC5B,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,UAAU;AAChC,SAAK,uBAAuB;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,aAAa;AAC3B,UAAM,UAAU,cAAc,WAAW;AACzC,QAAI,YAAY,KAAK,cAAc;AACjC,UAAI,KAAK,cAAc;AACrB,aAAK,4BAA4B,KAAK,YAAY;AAAA,MACpD;AACA,WAAK,QAAQ,kBAAkB,MAAM;AACnC,gBAAQ,iBAAiB,aAAa,KAAK,cAAc,0BAA0B;AACnF,gBAAQ,iBAAiB,cAAc,KAAK,cAAc,2BAA2B;AACrF,gBAAQ,iBAAiB,aAAa,KAAK,kBAAkB,0BAA0B;AAAA,MACzF,CAAC;AACD,WAAK,oBAAoB;AACzB,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,OAAO,eAAe,eAAe,KAAK,wBAAwB,YAAY;AAChF,WAAK,mBAAmB,KAAK,aAAa;AAAA,IAC5C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB,iBAAiB;AACnC,SAAK,mBAAmB,kBAAkB,cAAc,eAAe,IAAI;AAC3E,SAAK,oBAAoB,YAAY;AACrC,QAAI,iBAAiB;AACnB,WAAK,sBAAsB,KAAK,eAAe,OAAO,EAAE,EAAE,UAAU,MAAM,KAAK,+BAA+B,CAAC;AAAA,IACjH;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,WAAW,QAAQ;AACjB,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,4BAA4B,KAAK,YAAY;AAGlD,QAAI,KAAK,WAAW,GAAG;AAGrB,WAAK,cAAc,OAAO;AAAA,IAC5B;AACA,SAAK,SAAS,OAAO;AACrB,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB,eAAe,IAAI;AAC1C,SAAK,iBAAiB;AACtB,SAAK,cAAc,SAAS;AAC5B,SAAK,QAAQ,SAAS;AACtB,SAAK,SAAS,SAAS;AACvB,SAAK,MAAM,SAAS;AACpB,SAAK,QAAQ,SAAS;AACtB,SAAK,OAAO,SAAS;AACrB,SAAK,QAAQ,SAAS;AACtB,SAAK,YAAY,SAAS;AAC1B,SAAK,WAAW,CAAC;AACjB,SAAK,iBAAiB,MAAM;AAC5B,SAAK,iBAAiB;AACtB,SAAK,oBAAoB,YAAY;AACrC,SAAK,iBAAiB,MAAM;AAC5B,SAAK,mBAAmB,KAAK,eAAe,KAAK,mBAAmB,KAAK,uBAAuB,KAAK,mBAAmB,KAAK,UAAU,KAAK,iBAAiB;AAAA,EAC/J;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK,uBAAuB,KAAK,kBAAkB,WAAW,IAAI;AAAA,EAC3E;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,aAAa,MAAM,YAAY,KAAK,qBAAqB;AAC9D,SAAK,mBAAmB;AAAA,MACtB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,SAAK,oBAAoB;AAAA,MACvB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,QAAQ;AACpB,QAAI,CAAC,KAAK,iBAAiB,IAAI,MAAM,KAAK,KAAK,SAAS,QAAQ,MAAM,IAAI,IAAI;AAC5E,WAAK,iBAAiB,IAAI,MAAM;AAChC,mCAA6B,QAAQ,IAAI;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,QAAQ;AACnB,QAAI,KAAK,iBAAiB,IAAI,MAAM,GAAG;AACrC,WAAK,iBAAiB,OAAO,MAAM;AACnC,mCAA6B,QAAQ,KAAK,QAAQ;AAAA,IACpD;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,WAAW;AACvB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,mBAAmB,WAAW;AAC5B,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AACpB,UAAM,WAAW,KAAK,WAAW,IAAI,KAAK,mBAAmB,KAAK;AAClE,WAAO;AAAA,MACL,GAAG,SAAS;AAAA,MACZ,GAAG,SAAS;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,OAAO;AACzB,SAAK,mBAAmB;AAAA,MACtB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,SAAK,kBAAkB,IAAI,MAAM;AACjC,SAAK,kBAAkB,IAAI,MAAM;AACjC,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,2BAA2B,MAAM,GAAG,MAAM,CAAC;AAAA,IAClD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,OAAO;AAC1B,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,+BAA+B;AAC7B,UAAM,WAAW,KAAK;AACtB,QAAI,YAAY,KAAK,gBAAgB;AACnC,WAAK,2BAA2B,KAAK,+BAA+B,QAAQ,GAAG,QAAQ;AAAA,IACzF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,yBAAyB,YAAY;AAC1C,SAAK,uBAAuB,YAAY;AACxC,SAAK,oBAAoB,YAAY;AACrC,SAAK,eAAe,GAAG,oBAAoB,eAAe,sBAAsB,6BAA6B;AAAA,EAC/G;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,UAAU,QAAQ;AACvB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,sBAAsB;AACpB,SAAK,cAAc,OAAO;AAC1B,SAAK,iBAAiB,QAAQ;AAC9B,SAAK,eAAe,KAAK,kBAAkB;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,OAAO;AAKtB,QAAI,CAAC,KAAK,kBAAkB,WAAW,IAAI,GAAG;AAC5C;AAAA,IACF;AACA,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,aAAa,IAAI;AACxC,SAAK,8BAA8B;AACnC,QAAI,KAAK,UAAU;AACjB,WAAK,aAAa,MAAM,0BAA0B,KAAK;AAAA,IACzD;AACA,QAAI,CAAC,KAAK,qBAAqB;AAC7B;AAAA,IACF;AACA,SAAK,SAAS,KAAK;AAAA,MACjB,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AACD,QAAI,KAAK,gBAAgB;AAEvB,WAAK,eAAe,eAAe;AACnC,WAAK,6BAA6B,EAAE,KAAK,MAAM;AAC7C,aAAK,sBAAsB,KAAK;AAChC,aAAK,yBAAyB;AAC9B,aAAK,kBAAkB,aAAa,IAAI;AAAA,MAC1C,CAAC;AAAA,IACH,OAAO;AAIL,WAAK,kBAAkB,IAAI,KAAK,iBAAiB;AACjD,YAAM,kBAAkB,KAAK,0BAA0B,KAAK;AAC5D,WAAK,kBAAkB,IAAI,KAAK,iBAAiB;AACjD,WAAK,QAAQ,IAAI,MAAM;AACrB,aAAK,MAAM,KAAK;AAAA,UACd,QAAQ;AAAA,UACR,UAAU,KAAK,iBAAiB,eAAe;AAAA,UAC/C,WAAW;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,WAAK,yBAAyB;AAC9B,WAAK,kBAAkB,aAAa,IAAI;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,OAAO;AACxB,QAAI,aAAa,KAAK,GAAG;AACvB,WAAK,sBAAsB,KAAK,IAAI;AAAA,IACtC;AACA,SAAK,8BAA8B;AAEnC,UAAM,aAAa,KAAK,eAAe;AACvC,UAAM,gBAAgB,KAAK;AAC3B,QAAI,YAAY;AAGd,WAAK,QAAQ,kBAAkB,MAAM;AACnC,mBAAW,iBAAiB,eAAe,sBAAsB,6BAA6B;AAAA,MAChG,CAAC;AAAA,IACH;AACA,QAAI,eAAe;AACjB,YAAM,UAAU,KAAK;AACrB,YAAM,SAAS,QAAQ;AACvB,YAAM,cAAc,KAAK,eAAe,KAAK,0BAA0B;AACvE,YAAM,SAAS,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,cAAc,EAAE;AAE7E,aAAO,aAAa,QAAQ,OAAO;AAGnC,WAAK,oBAAoB,QAAQ,MAAM,aAAa;AAGpD,WAAK,WAAW,IAAI,WAAW,KAAK,WAAW,KAAK,cAAc,KAAK,YAAY,KAAK,iBAAiB,KAAK,oBAAoB,MAAM,KAAK,gBAAgB,MAAM,KAAK,uBAAuB,KAAK,mBAAmB,KAAK,QAAQ,UAAU,GAAI;AAClP,WAAK,SAAS,OAAO,KAAK,0BAA0B,QAAQ,UAAU,CAAC;AAIvE,uBAAiB,SAAS,OAAO,uBAAuB;AACxD,WAAK,UAAU,KAAK,YAAY,OAAO,aAAa,aAAa,OAAO,CAAC;AACzE,WAAK,QAAQ,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AACD,oBAAc,MAAM;AACpB,WAAK,oBAAoB;AACzB,WAAK,gBAAgB,cAAc,aAAa,IAAI;AAAA,IACtD,OAAO;AACL,WAAK,QAAQ,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AACD,WAAK,oBAAoB,KAAK,gBAAgB;AAAA,IAChD;AAGA,SAAK,iBAAiB,MAAM,gBAAgB,cAAc,qBAAqB,IAAI,CAAC,CAAC;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB,kBAAkB,OAAO;AAG/C,QAAI,KAAK,gBAAgB;AACvB,YAAM,gBAAgB;AAAA,IACxB;AACA,UAAM,aAAa,KAAK,WAAW;AACnC,UAAM,kBAAkB,aAAa,KAAK;AAC1C,UAAM,yBAAyB,CAAC,mBAAmB,MAAM,WAAW;AACpE,UAAM,cAAc,KAAK;AACzB,UAAM,SAAS,gBAAgB,KAAK;AACpC,UAAM,mBAAmB,CAAC,mBAAmB,KAAK,uBAAuB,KAAK,sBAAsB,0BAA0B,KAAK,IAAI;AACvI,UAAM,cAAc,kBAAkB,iCAAiC,KAAK,IAAI,gCAAgC,KAAK;AAOrH,QAAI,UAAU,OAAO,aAAa,MAAM,SAAS,aAAa;AAC5D,YAAM,eAAe;AAAA,IACvB;AAEA,QAAI,cAAc,0BAA0B,oBAAoB,aAAa;AAC3E;AAAA,IACF;AAIA,QAAI,KAAK,SAAS,QAAQ;AACxB,YAAM,aAAa,YAAY;AAC/B,WAAK,2BAA2B,WAAW,2BAA2B;AACtE,iBAAW,0BAA0B;AAAA,IACvC;AACA,SAAK,sBAAsB,KAAK,YAAY;AAG5C,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,KAAK,aAAa,sBAAsB;AAC/D,SAAK,2BAA2B,KAAK,kBAAkB,YAAY,UAAU,KAAK,YAAY;AAC9F,SAAK,yBAAyB,KAAK,kBAAkB,UAAU,UAAU,KAAK,UAAU;AACxF,SAAK,sBAAsB,KAAK,kBAAkB,SAAS,KAAK,eAAe,CAAC,EAAE,UAAU,iBAAe,KAAK,gBAAgB,WAAW,CAAC;AAC5I,QAAI,KAAK,kBAAkB;AACzB,WAAK,gBAAgB,qBAAqB,KAAK,gBAAgB;AAAA,IACjE;AAIA,UAAM,kBAAkB,KAAK;AAC7B,SAAK,2BAA2B,mBAAmB,gBAAgB,YAAY,CAAC,gBAAgB,YAAY;AAAA,MAC1G,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,KAAK,6BAA6B,KAAK,iBAAiB,kBAAkB,KAAK;AACnF,UAAM,kBAAkB,KAAK,wBAAwB,KAAK,4BAA4B,KAAK,0BAA0B,KAAK;AAC1H,SAAK,yBAAyB;AAAA,MAC5B,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,SAAK,wCAAwC;AAAA,MAC3C,GAAG,gBAAgB;AAAA,MACnB,GAAG,gBAAgB;AAAA,IACrB;AACA,SAAK,iBAAiB,KAAK,IAAI;AAC/B,SAAK,kBAAkB,cAAc,MAAM,KAAK;AAAA,EAClD;AAAA;AAAA,EAEA,sBAAsB,OAAO;AAK3B,qBAAiB,KAAK,cAAc,MAAM,uBAAuB;AACjE,SAAK,QAAQ,WAAW,aAAa,KAAK,cAAc,KAAK,OAAO;AACpE,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,eAAe,KAAK,oBAAoB;AAEzF,SAAK,QAAQ,IAAI,MAAM;AACrB,YAAM,YAAY,KAAK;AACvB,YAAM,eAAe,UAAU,aAAa,IAAI;AAChD,YAAM,kBAAkB,KAAK,0BAA0B,KAAK;AAC5D,YAAM,WAAW,KAAK,iBAAiB,eAAe;AACtD,YAAM,yBAAyB,UAAU,iBAAiB,gBAAgB,GAAG,gBAAgB,CAAC;AAC9F,WAAK,MAAM,KAAK;AAAA,QACd,QAAQ;AAAA,QACR;AAAA,QACA,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AACD,WAAK,QAAQ,KAAK;AAAA,QAChB,MAAM;AAAA,QACN;AAAA,QACA,eAAe,KAAK;AAAA,QACpB;AAAA,QACA,mBAAmB,KAAK;AAAA,QACxB;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AACD,gBAAU,KAAK,MAAM,cAAc,KAAK,eAAe,KAAK,mBAAmB,wBAAwB,UAAU,iBAAiB,KAAK;AACvI,WAAK,iBAAiB,KAAK;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,2BAA2B;AAAA,IACzB;AAAA,IACA;AAAA,EACF,GAAG;AAAA,IACD,GAAG;AAAA,IACH,GAAG;AAAA,EACL,GAAG;AAED,QAAI,eAAe,KAAK,kBAAkB,iCAAiC,MAAM,GAAG,CAAC;AAKrF,QAAI,CAAC,gBAAgB,KAAK,mBAAmB,KAAK,qBAAqB,KAAK,kBAAkB,iBAAiB,GAAG,CAAC,GAAG;AACpH,qBAAe,KAAK;AAAA,IACtB;AACA,QAAI,gBAAgB,iBAAiB,KAAK,gBAAgB;AACxD,WAAK,QAAQ,IAAI,MAAM;AAErB,aAAK,OAAO,KAAK;AAAA,UACf,MAAM;AAAA,UACN,WAAW,KAAK;AAAA,QAClB,CAAC;AACD,aAAK,eAAe,KAAK,IAAI;AAE7B,aAAK,iBAAiB;AACtB,aAAK,eAAe,MAAM,MAAM,GAAG,GAAG,iBAAiB,KAAK;AAAA;AAAA,QAG5D,aAAa,kBAAkB,KAAK,gBAAgB,MAAS;AAC7D,aAAK,QAAQ,KAAK;AAAA,UAChB,MAAM;AAAA,UACN,WAAW;AAAA,UACX,cAAc,aAAa,aAAa,IAAI;AAAA,QAC9C,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,eAAe,2BAA2B,MAAM,IAAI;AACzD,WAAK,eAAe,UAAU,MAAM,GAAG,GAAG,KAAK,sBAAsB;AACrE,UAAI,KAAK,mBAAmB;AAC1B,aAAK,uBAAuB,GAAG,CAAC;AAAA,MAClC,OAAO;AACL,aAAK,uBAAuB,IAAI,KAAK,yBAAyB,GAAG,IAAI,KAAK,yBAAyB,CAAC;AAAA,MACtG;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,+BAA+B;AAE7B,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,kBAAkB,KAAK,aAAa,sBAAsB;AAEhE,SAAK,SAAS,SAAS,oBAAoB;AAE3C,SAAK,uBAAuB,gBAAgB,MAAM,gBAAgB,GAAG;AAKrE,UAAM,WAAW,KAAK,SAAS,sBAAsB;AACrD,QAAI,aAAa,GAAG;AAClB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,WAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,aAAO,IAAI,QAAQ,aAAW;AAC5B,cAAM,UAAU,WAAS;AACvB,cAAI,CAAC,SAAS,gBAAgB,KAAK,MAAM,KAAK,YAAY,MAAM,iBAAiB,aAAa;AAC5F,iBAAK,UAAU,oBAAoB,iBAAiB,OAAO;AAC3D,oBAAQ;AACR,yBAAa,OAAO;AAAA,UACtB;AAAA,QACF;AAIA,cAAM,UAAU,WAAW,SAAS,WAAW,GAAG;AAClD,aAAK,SAAS,iBAAiB,iBAAiB,OAAO;AAAA,MACzD,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,4BAA4B;AAC1B,UAAM,oBAAoB,KAAK;AAC/B,UAAM,sBAAsB,oBAAoB,kBAAkB,WAAW;AAC7E,QAAI;AACJ,QAAI,qBAAqB;AACvB,WAAK,kBAAkB,kBAAkB,cAAc,mBAAmB,qBAAqB,kBAAkB,OAAO;AACxH,WAAK,gBAAgB,cAAc;AACnC,oBAAc,YAAY,KAAK,iBAAiB,KAAK,SAAS;AAAA,IAChE,OAAO;AACL,oBAAc,cAAc,KAAK,YAAY;AAAA,IAC/C;AAGA,gBAAY,MAAM,gBAAgB;AAClC,gBAAY,UAAU,IAAI,sBAAsB;AAChD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B,aAAa,kBAAkB,OAAO;AACjE,UAAM,gBAAgB,qBAAqB,KAAK,eAAe,OAAO;AACtE,UAAM,gBAAgB,gBAAgB,cAAc,sBAAsB,IAAI;AAC9E,UAAM,QAAQ,aAAa,KAAK,IAAI,MAAM,cAAc,CAAC,IAAI;AAC7D,UAAM,iBAAiB,KAAK,2BAA2B;AACvD,UAAM,IAAI,MAAM,QAAQ,cAAc,OAAO,eAAe;AAC5D,UAAM,IAAI,MAAM,QAAQ,cAAc,MAAM,eAAe;AAC3D,WAAO;AAAA,MACL,GAAG,cAAc,OAAO,YAAY,OAAO;AAAA,MAC3C,GAAG,cAAc,MAAM,YAAY,MAAM;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA,EAEA,0BAA0B,OAAO;AAC/B,UAAM,iBAAiB,KAAK,2BAA2B;AACvD,UAAM,QAAQ,aAAa,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQhC,MAAM,QAAQ,CAAC,KAAK,MAAM,eAAe,CAAC,KAAK;AAAA,QAC7C,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,QAAI;AACJ,UAAM,IAAI,MAAM,QAAQ,eAAe;AACvC,UAAM,IAAI,MAAM,QAAQ,eAAe;AAGvC,QAAI,KAAK,kBAAkB;AACzB,YAAM,YAAY,KAAK,iBAAiB,aAAa;AACrD,UAAI,WAAW;AACb,cAAM,WAAW,KAAK,iBAAiB,eAAe;AACtD,iBAAS,IAAI;AACb,iBAAS,IAAI;AACb,eAAO,SAAS,gBAAgB,UAAU,QAAQ,CAAC;AAAA,MACrD;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,+BAA+B,OAAO;AACpC,UAAM,oBAAoB,KAAK,iBAAiB,KAAK,eAAe,WAAW;AAC/E,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,KAAK,oBAAoB,KAAK,kBAAkB,OAAO,MAAM,KAAK,iBAAiB,KAAK,wBAAwB,IAAI;AACxH,QAAI,KAAK,aAAa,OAAO,sBAAsB,KAAK;AACtD,UAAI,KAAK,sBAAsB,KAAK,KAAK,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,IACjG,WAAW,KAAK,aAAa,OAAO,sBAAsB,KAAK;AAC7D,UAAI,KAAK,sBAAsB,KAAK,KAAK,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,IACjG;AACA,QAAI,KAAK,eAAe;AAGtB,YAAM;AAAA,QACJ,GAAG;AAAA,QACH,GAAG;AAAA,MACL,IAAI,CAAC,KAAK,oBAAoB,KAAK,2BAA2B;AAAA,QAC5D,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,YAAM,eAAe,KAAK;AAC1B,YAAM;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,IAAI,KAAK,gBAAgB;AACzB,YAAM,OAAO,aAAa,MAAM;AAChC,YAAM,OAAO,aAAa,UAAU,gBAAgB;AACpD,YAAM,OAAO,aAAa,OAAO;AACjC,YAAM,OAAO,aAAa,SAAS,eAAe;AAClD,UAAI,QAAQ,GAAG,MAAM,IAAI;AACzB,UAAI,QAAQ,GAAG,MAAM,IAAI;AAAA,IAC3B;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,6BAA6B,uBAAuB;AAClD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,QAAQ,KAAK;AACnB,UAAM,0BAA0B,KAAK;AAErC,UAAM,UAAU,KAAK,IAAI,IAAI,wBAAwB,CAAC;AACtD,UAAM,UAAU,KAAK,IAAI,IAAI,wBAAwB,CAAC;AAKtD,QAAI,UAAU,KAAK,QAAQ,iCAAiC;AAC1D,YAAM,IAAI,IAAI,wBAAwB,IAAI,IAAI;AAC9C,8BAAwB,IAAI;AAAA,IAC9B;AACA,QAAI,UAAU,KAAK,QAAQ,iCAAiC;AAC1D,YAAM,IAAI,IAAI,wBAAwB,IAAI,IAAI;AAC9C,8BAAwB,IAAI;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,gCAAgC;AAC9B,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,UAAU;AACxC;AAAA,IACF;AACA,UAAM,eAAe,KAAK,SAAS,SAAS,KAAK,CAAC,KAAK,WAAW;AAClE,QAAI,iBAAiB,KAAK,4BAA4B;AACpD,WAAK,6BAA6B;AAClC,mCAA6B,KAAK,cAAc,YAAY;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA,EAEA,4BAA4B,SAAS;AACnC,YAAQ,oBAAoB,aAAa,KAAK,cAAc,0BAA0B;AACtF,YAAQ,oBAAoB,cAAc,KAAK,cAAc,2BAA2B;AACxF,YAAQ,oBAAoB,aAAa,KAAK,kBAAkB,0BAA0B;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,2BAA2B,GAAG,GAAG;AAC/B,UAAM,YAAY,aAAa,GAAG,CAAC;AACnC,UAAM,SAAS,KAAK,aAAa;AAIjC,QAAI,KAAK,qBAAqB,MAAM;AAClC,WAAK,oBAAoB,OAAO,aAAa,OAAO,aAAa,SAAS,OAAO,YAAY;AAAA,IAC/F;AAIA,WAAO,YAAY,kBAAkB,WAAW,KAAK,iBAAiB;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,GAAG,GAAG;AAG3B,UAAM,mBAAmB,KAAK,kBAAkB,WAAW,SAAY,KAAK;AAC5E,UAAM,YAAY,aAAa,GAAG,CAAC;AACnC,SAAK,SAAS,aAAa,kBAAkB,WAAW,gBAAgB,CAAC;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,iBAAiB;AAChC,UAAM,iBAAiB,KAAK;AAC5B,QAAI,gBAAgB;AAClB,aAAO;AAAA,QACL,GAAG,gBAAgB,IAAI,eAAe;AAAA,QACtC,GAAG,gBAAgB,IAAI,eAAe;AAAA,MACxC;AAAA,IACF;AACA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,2BAA2B;AACzB,SAAK,gBAAgB,KAAK,eAAe;AACzC,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iCAAiC;AAC/B,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,QAAI,MAAM,KAAK,MAAM,KAAK,KAAK,WAAW,KAAK,CAAC,KAAK,kBAAkB;AACrE;AAAA,IACF;AAEA,UAAM,cAAc,KAAK,aAAa,sBAAsB;AAC5D,UAAM,eAAe,KAAK,iBAAiB,sBAAsB;AAGjE,QAAI,aAAa,UAAU,KAAK,aAAa,WAAW,KAAK,YAAY,UAAU,KAAK,YAAY,WAAW,GAAG;AAChH;AAAA,IACF;AACA,UAAM,eAAe,aAAa,OAAO,YAAY;AACrD,UAAM,gBAAgB,YAAY,QAAQ,aAAa;AACvD,UAAM,cAAc,aAAa,MAAM,YAAY;AACnD,UAAM,iBAAiB,YAAY,SAAS,aAAa;AAGzD,QAAI,aAAa,QAAQ,YAAY,OAAO;AAC1C,UAAI,eAAe,GAAG;AACpB,aAAK;AAAA,MACP;AACA,UAAI,gBAAgB,GAAG;AACrB,aAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,UAAI;AAAA,IACN;AAGA,QAAI,aAAa,SAAS,YAAY,QAAQ;AAC5C,UAAI,cAAc,GAAG;AACnB,aAAK;AAAA,MACP;AACA,UAAI,iBAAiB,GAAG;AACtB,aAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,UAAI;AAAA,IACN;AACA,QAAI,MAAM,KAAK,kBAAkB,KAAK,MAAM,KAAK,kBAAkB,GAAG;AACpE,WAAK,oBAAoB;AAAA,QACvB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,OAAO;AACxB,UAAM,QAAQ,KAAK;AACnB,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO;AAAA,IACT,WAAW,aAAa,KAAK,GAAG;AAC9B,aAAO,MAAM;AAAA,IACf;AACA,WAAO,QAAQ,MAAM,QAAQ;AAAA,EAC/B;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,UAAM,mBAAmB,KAAK,iBAAiB,aAAa,KAAK;AACjE,QAAI,kBAAkB;AACpB,YAAM,SAAS,gBAAgB,KAAK;AAGpC,UAAI,KAAK,iBAAiB,WAAW,KAAK,oBAAoB,OAAO,SAAS,KAAK,gBAAgB,GAAG;AACpG,sBAAc,KAAK,eAAe,iBAAiB,KAAK,iBAAiB,IAAI;AAAA,MAC/E;AACA,WAAK,sBAAsB,KAAK,iBAAiB;AACjD,WAAK,sBAAsB,KAAK,iBAAiB;AAGjD,UAAI,CAAC,KAAK,gBAAgB;AACxB,aAAK,iBAAiB,KAAK,iBAAiB;AAC5C,aAAK,iBAAiB,KAAK,iBAAiB;AAC5C,aAAK,2BAA2B,KAAK,iBAAiB,GAAG,KAAK,iBAAiB,CAAC;AAAA,MAClF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,6BAA6B;AAC3B,WAAO,KAAK,iBAAiB,UAAU,IAAI,KAAK,SAAS,GAAG,kBAAkB,KAAK,iBAAiB,0BAA0B;AAAA,EAChI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,QAAI,KAAK,sBAAsB,QAAW;AACxC,WAAK,oBAAoB,eAAe,KAAK,YAAY;AAAA,IAC3D;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,0BAA0B,eAAe,YAAY;AACnD,UAAM,mBAAmB,KAAK,qBAAqB;AACnD,QAAI,qBAAqB,UAAU;AACjC,aAAO;AAAA,IACT;AACA,QAAI,qBAAqB,UAAU;AACjC,YAAM,cAAc,KAAK;AAIzB,aAAO,cAAc,YAAY,qBAAqB,YAAY,2BAA2B,YAAY,wBAAwB,YAAY,uBAAuB,YAAY;AAAA,IAClL;AACA,WAAO,cAAc,gBAAgB;AAAA,EACvC;AAAA;AAAA,EAEA,kBAAkB;AAGhB,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,aAAa,SAAS,CAAC,KAAK,aAAa,QAAQ;AAC/E,WAAK,eAAe,KAAK,WAAW,KAAK,SAAS,sBAAsB,IAAI,KAAK;AAAA,IACnF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB,OAAO;AACtB,WAAO,KAAK,SAAS,KAAK,YAAU;AAClC,aAAO,MAAM,WAAW,MAAM,WAAW,UAAU,OAAO,SAAS,MAAM,MAAM;AAAA,IACjF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,QAAQ,OAAO,KAAK,KAAK;AAChC,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC;AAC3C;AAEA,SAAS,aAAa,OAAO;AAI3B,SAAO,MAAM,KAAK,CAAC,MAAM;AAC3B;AAEA,SAAS,qBAAqB,OAAO;AACnC,QAAM,eAAe;AACvB;AAQA,SAAS,gBAAgB,OAAO,WAAW,SAAS;AAClD,QAAM,OAAO,MAAM,WAAW,MAAM,SAAS,CAAC;AAC9C,QAAM,KAAK,MAAM,SAAS,MAAM,SAAS,CAAC;AAC1C,MAAI,SAAS,IAAI;AACf;AAAA,EACF;AACA,QAAM,SAAS,MAAM,IAAI;AACzB,QAAM,QAAQ,KAAK,OAAO,KAAK;AAC/B,WAAS,IAAI,MAAM,MAAM,IAAI,KAAK,OAAO;AACvC,UAAM,CAAC,IAAI,MAAM,IAAI,KAAK;AAAA,EAC5B;AACA,QAAM,EAAE,IAAI;AACd;AA+BA,SAAS,MAAM,OAAO,KAAK;AACzB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AACzC;AAOA,IAAM,yBAAN,MAA6B;AAAA,EAC3B,YAAY,UAAU,mBAAmB;AACvC,SAAK,WAAW;AAChB,SAAK,oBAAoB;AAEzB,SAAK,iBAAiB,CAAC;AAEvB,SAAK,cAAc;AAMnB,SAAK,gBAAgB;AAAA,MACnB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,SAAK,UAAU,KAAK;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,MAAM,UAAU,UAAU,cAAc;AAC3C,UAAM,WAAW,KAAK;AACtB,UAAM,WAAW,KAAK,iCAAiC,MAAM,UAAU,UAAU,YAAY;AAC7F,QAAI,aAAa,MAAM,SAAS,SAAS,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,eAAe,SAAS,UAAU,iBAAe,YAAY,SAAS,IAAI;AAChF,UAAM,uBAAuB,SAAS,QAAQ;AAC9C,UAAM,kBAAkB,SAAS,YAAY,EAAE;AAC/C,UAAM,cAAc,qBAAqB;AACzC,UAAM,QAAQ,eAAe,WAAW,IAAI;AAE5C,UAAM,aAAa,KAAK,iBAAiB,iBAAiB,aAAa,KAAK;AAE5E,UAAM,gBAAgB,KAAK,oBAAoB,cAAc,UAAU,KAAK;AAG5E,UAAM,WAAW,SAAS,MAAM;AAEhC,oBAAgB,UAAU,cAAc,QAAQ;AAChD,aAAS,QAAQ,CAAC,SAAS,UAAU;AAEnC,UAAI,SAAS,KAAK,MAAM,SAAS;AAC/B;AAAA,MACF;AACA,YAAM,gBAAgB,QAAQ,SAAS;AACvC,YAAM,SAAS,gBAAgB,aAAa;AAC5C,YAAM,kBAAkB,gBAAgB,KAAK,sBAAsB,IAAI,QAAQ,KAAK,eAAe;AAEnG,cAAQ,UAAU;AAKlB,UAAI,cAAc;AAGhB,wBAAgB,MAAM,YAAY,kBAAkB,eAAe,KAAK,MAAM,QAAQ,MAAM,CAAC,aAAa,QAAQ,gBAAgB;AAClI,sBAAc,QAAQ,YAAY,GAAG,MAAM;AAAA,MAC7C,OAAO;AACL,wBAAgB,MAAM,YAAY,kBAAkB,kBAAkB,KAAK,MAAM,QAAQ,MAAM,CAAC,UAAU,QAAQ,gBAAgB;AAClI,sBAAc,QAAQ,YAAY,QAAQ,CAAC;AAAA,MAC7C;AAAA,IACF,CAAC;AAED,SAAK,cAAc,WAAW,mBAAmB,aAAa,UAAU,QAAQ;AAChF,SAAK,cAAc,OAAO,qBAAqB;AAC/C,SAAK,cAAc,QAAQ,eAAe,aAAa,IAAI,aAAa;AACxE,WAAO;AAAA,MACL,eAAe;AAAA,MACf,cAAc;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,MAAM,UAAU,UAAU,OAAO;AACrC,UAAM,WAAW,SAAS,QAAQ,QAAQ;AAAA;AAAA;AAAA,MAG1C,KAAK,iCAAiC,MAAM,UAAU,QAAQ;AAAA,QAAI;AAClE,UAAM,mBAAmB,KAAK;AAC9B,UAAM,eAAe,iBAAiB,QAAQ,IAAI;AAClD,UAAM,cAAc,KAAK,sBAAsB;AAC/C,QAAI,uBAAuB,iBAAiB,QAAQ;AAIpD,QAAI,yBAAyB,MAAM;AACjC,6BAAuB,iBAAiB,WAAW,CAAC;AAAA,IACtD;AAGA,QAAI,CAAC,yBAAyB,YAAY,QAAQ,aAAa,MAAM,WAAW,iBAAiB,SAAS,MAAM,KAAK,yBAAyB,UAAU,QAAQ,GAAG;AACjK,6BAAuB,iBAAiB,CAAC;AAAA,IAC3C;AAGA,QAAI,eAAe,IAAI;AACrB,uBAAiB,OAAO,cAAc,CAAC;AAAA,IACzC;AAGA,QAAI,wBAAwB,CAAC,KAAK,kBAAkB,WAAW,oBAAoB,GAAG;AACpF,YAAM,UAAU,qBAAqB,eAAe;AACpD,cAAQ,cAAc,aAAa,aAAa,OAAO;AACvD,uBAAiB,OAAO,UAAU,GAAG,IAAI;AAAA,IAC3C,OAAO;AACL,oBAAc,KAAK,QAAQ,EAAE,YAAY,WAAW;AACpD,uBAAiB,KAAK,IAAI;AAAA,IAC5B;AAEA,gBAAY,MAAM,YAAY;AAI9B,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,UAAU,OAAO;AACf,SAAK,oBAAoB,MAAM,MAAM;AACrC,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,kBAAkB,WAAW;AAC3B,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAEA,QAAQ;AAEN,SAAK,kBAAkB,QAAQ,UAAQ;AACrC,YAAM,cAAc,KAAK,eAAe;AACxC,UAAI,aAAa;AACf,cAAM,mBAAmB,KAAK,eAAe,KAAK,OAAK,EAAE,SAAS,IAAI,GAAG;AACzE,oBAAY,MAAM,YAAY,oBAAoB;AAAA,MACpD;AAAA,IACF,CAAC;AACD,SAAK,iBAAiB,CAAC;AACvB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,cAAc,OAAO;AAC1B,SAAK,cAAc,QAAQ;AAC3B,SAAK,cAAc,WAAW;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,aAAa,MAAM;AAIjB,UAAM,QAAQ,KAAK,gBAAgB,gBAAgB,KAAK,cAAc,QAAQ,KAAK,eAAe,MAAM,EAAE,QAAQ,IAAI,KAAK;AAC3H,WAAO,MAAM,UAAU,iBAAe,YAAY,SAAS,IAAI;AAAA,EACjE;AAAA;AAAA,EAEA,eAAe,eAAe,gBAAgB;AAK5C,SAAK,eAAe,QAAQ,CAAC;AAAA,MAC3B;AAAA,IACF,MAAM;AACJ,oBAAc,YAAY,eAAe,cAAc;AAAA,IACzD,CAAC;AAGD,SAAK,eAAe,QAAQ,CAAC;AAAA,MAC3B;AAAA,IACF,MAAM;AACJ,UAAI,KAAK,kBAAkB,WAAW,IAAI,GAAG;AAG3C,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB;AACpB,UAAM,eAAe,KAAK,gBAAgB;AAC1C,SAAK,iBAAiB,KAAK,kBAAkB,IAAI,UAAQ;AACvD,YAAM,mBAAmB,KAAK,kBAAkB;AAChD,aAAO;AAAA,QACL;AAAA,QACA,QAAQ;AAAA,QACR,kBAAkB,iBAAiB,MAAM,aAAa;AAAA,QACtD,YAAY,qBAAqB,gBAAgB;AAAA,MACnD;AAAA,IACF,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM;AAChB,aAAO,eAAe,EAAE,WAAW,OAAO,EAAE,WAAW,OAAO,EAAE,WAAW,MAAM,EAAE,WAAW;AAAA,IAChG,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,iBAAiB,aAAa,OAAO;AACpD,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,aAAa,eAAe,YAAY,OAAO,gBAAgB,OAAO,YAAY,MAAM,gBAAgB;AAE5G,QAAI,UAAU,IAAI;AAChB,oBAAc,eAAe,YAAY,QAAQ,gBAAgB,QAAQ,YAAY,SAAS,gBAAgB;AAAA,IAChH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,cAAc,UAAU,OAAO;AACjD,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,kBAAkB,SAAS,YAAY,EAAE;AAC/C,UAAM,mBAAmB,SAAS,eAAe,QAAQ,EAAE;AAC3D,QAAI,gBAAgB,gBAAgB,eAAe,UAAU,QAAQ,IAAI;AACzE,QAAI,kBAAkB;AACpB,YAAM,QAAQ,eAAe,SAAS;AACtC,YAAM,MAAM,eAAe,UAAU;AAKrC,UAAI,UAAU,IAAI;AAChB,yBAAiB,iBAAiB,WAAW,KAAK,IAAI,gBAAgB,GAAG;AAAA,MAC3E,OAAO;AACL,yBAAiB,gBAAgB,KAAK,IAAI,iBAAiB,WAAW,GAAG;AAAA,MAC3E;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,UAAU,UAAU;AAC3C,QAAI,CAAC,KAAK,kBAAkB,QAAQ;AAClC,aAAO;AAAA,IACT;AACA,UAAM,gBAAgB,KAAK;AAC3B,UAAM,eAAe,KAAK,gBAAgB;AAG1C,UAAM,WAAW,cAAc,CAAC,EAAE,SAAS,KAAK,kBAAkB,CAAC;AACnE,QAAI,UAAU;AACZ,YAAM,eAAe,cAAc,cAAc,SAAS,CAAC,EAAE;AAC7D,aAAO,eAAe,YAAY,aAAa,QAAQ,YAAY,aAAa;AAAA,IAClF,OAAO;AACL,YAAM,gBAAgB,cAAc,CAAC,EAAE;AACvC,aAAO,eAAe,YAAY,cAAc,OAAO,YAAY,cAAc;AAAA,IACnF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iCAAiC,MAAM,UAAU,UAAU,OAAO;AAChE,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,QAAQ,KAAK,eAAe,UAAU,CAAC;AAAA,MAC3C;AAAA,MACA;AAAA,IACF,MAAM;AAEJ,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AACA,UAAI,OAAO;AACT,cAAM,YAAY,eAAe,MAAM,IAAI,MAAM;AAIjD,YAAI,SAAS,KAAK,cAAc,QAAQ,KAAK,cAAc,YAAY,cAAc,KAAK,cAAc,OAAO;AAC7G,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA;AAAA;AAAA,QAGP,YAAY,KAAK,MAAM,WAAW,IAAI,KAAK,WAAW,KAAK,MAAM,WAAW,KAAK;AAAA,UAAI,YAAY,KAAK,MAAM,WAAW,GAAG,KAAK,WAAW,KAAK,MAAM,WAAW,MAAM;AAAA,IACxK,CAAC;AACD,WAAO,UAAU,MAAM,CAAC,KAAK,eAAe,OAAO,IAAI,IAAI,KAAK;AAAA,EAClE;AACF;AAMA,IAAM,2BAA2B;AAKjC,IAAM,6BAA6B;AAEnC,IAAI;AAAA,CACH,SAAUC,8BAA6B;AACtC,EAAAA,6BAA4BA,6BAA4B,MAAM,IAAI,CAAC,IAAI;AACvE,EAAAA,6BAA4BA,6BAA4B,IAAI,IAAI,CAAC,IAAI;AACrE,EAAAA,6BAA4BA,6BAA4B,MAAM,IAAI,CAAC,IAAI;AACzE,GAAG,gCAAgC,8BAA8B,CAAC,EAAE;AAEpE,IAAI;AAAA,CACH,SAAUC,gCAA+B;AACxC,EAAAA,+BAA8BA,+BAA8B,MAAM,IAAI,CAAC,IAAI;AAC3E,EAAAA,+BAA8BA,+BAA8B,MAAM,IAAI,CAAC,IAAI;AAC3E,EAAAA,+BAA8BA,+BAA8B,OAAO,IAAI,CAAC,IAAI;AAC9E,GAAG,kCAAkC,gCAAgC,CAAC,EAAE;AAIxE,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,SAAS,mBAAmB,WAAW,SAAS,gBAAgB;AAC1E,SAAK,oBAAoB;AACzB,SAAK,UAAU;AACf,SAAK,iBAAiB;AAEtB,SAAK,WAAW;AAEhB,SAAK,kBAAkB;AAKvB,SAAK,qBAAqB;AAE1B,SAAK,iBAAiB;AAKtB,SAAK,iBAAiB,MAAM;AAE5B,SAAK,gBAAgB,MAAM;AAE3B,SAAK,gBAAgB,IAAI,QAAQ;AAIjC,SAAK,UAAU,IAAI,QAAQ;AAK3B,SAAK,SAAS,IAAI,QAAQ;AAE1B,SAAK,UAAU,IAAI,QAAQ;AAE3B,SAAK,SAAS,IAAI,QAAQ;AAE1B,SAAK,mBAAmB,IAAI,QAAQ;AAEpC,SAAK,mBAAmB,IAAI,QAAQ;AAEpC,SAAK,cAAc;AAEnB,SAAK,cAAc,CAAC;AAEpB,SAAK,YAAY,CAAC;AAElB,SAAK,kBAAkB,oBAAI,IAAI;AAE/B,SAAK,8BAA8B,aAAa;AAEhD,SAAK,2BAA2B,4BAA4B;AAE5D,SAAK,6BAA6B,8BAA8B;AAEhE,SAAK,oBAAoB,IAAI,QAAQ;AAErC,SAAK,oBAAoB;AAEzB,SAAK,uBAAuB,MAAM;AAChC,WAAK,eAAe;AACpB,eAAS,GAAG,uBAAuB,EAAE,KAAK,UAAU,KAAK,iBAAiB,CAAC,EAAE,UAAU,MAAM;AAC3F,cAAM,OAAO,KAAK;AAClB,cAAM,aAAa,KAAK;AACxB,YAAI,KAAK,6BAA6B,4BAA4B,IAAI;AACpE,eAAK,SAAS,GAAG,CAAC,UAAU;AAAA,QAC9B,WAAW,KAAK,6BAA6B,4BAA4B,MAAM;AAC7E,eAAK,SAAS,GAAG,UAAU;AAAA,QAC7B;AACA,YAAI,KAAK,+BAA+B,8BAA8B,MAAM;AAC1E,eAAK,SAAS,CAAC,YAAY,CAAC;AAAA,QAC9B,WAAW,KAAK,+BAA+B,8BAA8B,OAAO;AAClF,eAAK,SAAS,YAAY,CAAC;AAAA,QAC7B;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,UAAU,cAAc,OAAO;AACpC,SAAK,YAAY;AACjB,SAAK,sBAAsB,CAAC,KAAK,OAAO,CAAC;AACzC,sBAAkB,sBAAsB,IAAI;AAC5C,SAAK,mBAAmB,IAAI,sBAAsB,SAAS;AAC3D,SAAK,gBAAgB,IAAI,uBAAuB,KAAK,SAAS,iBAAiB;AAC/E,SAAK,cAAc,kBAAkB,CAAC,OAAO,SAAS,KAAK,cAAc,OAAO,MAAM,IAAI,CAAC;AAAA,EAC7F;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,eAAe;AACpB,SAAK,kBAAkB,SAAS;AAChC,SAAK,4BAA4B,YAAY;AAC7C,SAAK,cAAc,SAAS;AAC5B,SAAK,QAAQ,SAAS;AACtB,SAAK,OAAO,SAAS;AACrB,SAAK,QAAQ,SAAS;AACtB,SAAK,OAAO,SAAS;AACrB,SAAK,iBAAiB,SAAS;AAC/B,SAAK,iBAAiB,SAAS;AAC/B,SAAK,gBAAgB,MAAM;AAC3B,SAAK,cAAc;AACnB,SAAK,iBAAiB,MAAM;AAC5B,SAAK,kBAAkB,oBAAoB,IAAI;AAAA,EACjD;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,iBAAiB;AACtB,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,MAAM,UAAU,UAAU,OAAO;AACrC,SAAK,iBAAiB;AAGtB,QAAI,SAAS,QAAQ,KAAK,iBAAiB;AACzC,cAAQ,KAAK,YAAY,QAAQ,IAAI;AAAA,IACvC;AACA,SAAK,cAAc,MAAM,MAAM,UAAU,UAAU,KAAK;AAGxD,SAAK,sBAAsB;AAE3B,SAAK,yBAAyB;AAC9B,SAAK,QAAQ,KAAK;AAAA,MAChB;AAAA,MACA,WAAW;AAAA,MACX,cAAc,KAAK,aAAa,IAAI;AAAA,IACtC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,MAAM;AACT,SAAK,OAAO;AACZ,SAAK,OAAO,KAAK;AAAA,MACf;AAAA,MACA,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,KAAK,MAAM,cAAc,eAAe,mBAAmB,wBAAwB,UAAU,WAAW,QAAQ,CAAC,GAAG;AAClH,SAAK,OAAO;AACZ,SAAK,QAAQ,KAAK;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACf,UAAM,gBAAgB,KAAK;AAC3B,SAAK,cAAc;AACnB,UAAM,QAAQ,UAAQ,KAAK,mBAAmB,IAAI,CAAC;AACnD,QAAI,KAAK,WAAW,GAAG;AACrB,YAAM,eAAe,cAAc,OAAO,UAAQ,KAAK,WAAW,CAAC;AAGnE,UAAI,aAAa,MAAM,UAAQ,MAAM,QAAQ,IAAI,MAAM,EAAE,GAAG;AAC1D,aAAK,OAAO;AAAA,MACd,OAAO;AACL,aAAK,cAAc,UAAU,KAAK,WAAW;AAAA,MAC/C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc,WAAW;AACvB,SAAK,cAAc,YAAY;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,aAAa;AACvB,SAAK,YAAY,YAAY,MAAM;AACnC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,aAAa;AAG3B,SAAK,cAAc,cAAc;AACjC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,UAAU;AAC9B,UAAM,UAAU,cAAc,KAAK,OAAO;AAG1C,SAAK,sBAAsB,SAAS,QAAQ,OAAO,MAAM,KAAK,CAAC,SAAS,GAAG,QAAQ,IAAI,SAAS,MAAM;AACtG,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,MAAM;AACjB,WAAO,KAAK,cAAc,KAAK,cAAc,aAAa,IAAI,IAAI,KAAK,YAAY,QAAQ,IAAI;AAAA,EACjG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO,KAAK,gBAAgB,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,MAAM,UAAU,UAAU,cAAc;AAEhD,QAAI,KAAK,mBAAmB,CAAC,KAAK,YAAY,CAAC,qBAAqB,KAAK,UAAU,0BAA0B,UAAU,QAAQ,GAAG;AAChI;AAAA,IACF;AACA,UAAM,SAAS,KAAK,cAAc,KAAK,MAAM,UAAU,UAAU,YAAY;AAC7E,QAAI,QAAQ;AACV,WAAK,OAAO,KAAK;AAAA,QACf,eAAe,OAAO;AAAA,QACtB,cAAc,OAAO;AAAA,QACrB,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,2BAA2B,UAAU,UAAU;AAC7C,QAAI,KAAK,oBAAoB;AAC3B;AAAA,IACF;AACA,QAAI;AACJ,QAAI,0BAA0B,4BAA4B;AAC1D,QAAI,4BAA4B,8BAA8B;AAE9D,SAAK,iBAAiB,UAAU,QAAQ,CAAC,UAAU,YAAY;AAG7D,UAAI,YAAY,KAAK,aAAa,CAAC,SAAS,cAAc,YAAY;AACpE;AAAA,MACF;AACA,UAAI,qBAAqB,SAAS,YAAY,0BAA0B,UAAU,QAAQ,GAAG;AAC3F,SAAC,yBAAyB,yBAAyB,IAAI,2BAA2B,SAAS,SAAS,YAAY,KAAK,cAAc,WAAW,UAAU,QAAQ;AAChK,YAAI,2BAA2B,2BAA2B;AACxD,uBAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF,CAAC;AAED,QAAI,CAAC,2BAA2B,CAAC,2BAA2B;AAC1D,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,eAAe,gBAAgB;AACxC,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AACA,gCAA0B,2BAA2B,SAAS,QAAQ;AACtE,kCAA4B,6BAA6B,SAAS,QAAQ;AAC1E,mBAAa;AAAA,IACf;AACA,QAAI,eAAe,4BAA4B,KAAK,4BAA4B,8BAA8B,KAAK,8BAA8B,eAAe,KAAK,cAAc;AACjL,WAAK,2BAA2B;AAChC,WAAK,6BAA6B;AAClC,WAAK,cAAc;AACnB,WAAK,2BAA2B,8BAA8B,YAAY;AACxE,aAAK,QAAQ,kBAAkB,KAAK,oBAAoB;AAAA,MAC1D,OAAO;AACL,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,kBAAkB,KAAK;AAAA,EAC9B;AAAA;AAAA,EAEA,mBAAmB;AACjB,UAAM,SAAS,cAAc,KAAK,OAAO,EAAE;AAC3C,SAAK,cAAc,KAAK;AACxB,SAAK,cAAc;AAInB,SAAK,qBAAqB,OAAO,oBAAoB,OAAO,kBAAkB;AAC9E,WAAO,iBAAiB,OAAO,mBAAmB;AAClD,SAAK,cAAc,MAAM,KAAK,WAAW;AACzC,SAAK,sBAAsB;AAC3B,SAAK,4BAA4B,YAAY;AAC7C,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA,EAEA,wBAAwB;AACtB,UAAM,UAAU,cAAc,KAAK,OAAO;AAC1C,SAAK,iBAAiB,MAAM,KAAK,mBAAmB;AAGpD,SAAK,WAAW,KAAK,iBAAiB,UAAU,IAAI,OAAO,EAAE;AAAA,EAC/D;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,cAAc;AACnB,UAAM,SAAS,cAAc,KAAK,OAAO,EAAE;AAC3C,WAAO,iBAAiB,OAAO,mBAAmB,KAAK;AACvD,SAAK,UAAU,QAAQ,aAAW,QAAQ,eAAe,IAAI,CAAC;AAC9D,SAAK,cAAc,MAAM;AACzB,SAAK,eAAe;AACpB,SAAK,4BAA4B,YAAY;AAC7C,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,GAAG,GAAG;AACrB,WAAO,KAAK,YAAY,QAAQ,mBAAmB,KAAK,UAAU,GAAG,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iCAAiC,MAAM,GAAG,GAAG;AAC3C,WAAO,KAAK,UAAU,KAAK,aAAW,QAAQ,YAAY,MAAM,GAAG,CAAC,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM,GAAG,GAAG;AACtB,QAAI,CAAC,KAAK,YAAY,CAAC,mBAAmB,KAAK,UAAU,GAAG,CAAC,KAAK,CAAC,KAAK,eAAe,MAAM,IAAI,GAAG;AAClG,aAAO;AAAA,IACT;AACA,UAAM,mBAAmB,KAAK,eAAe,EAAE,iBAAiB,GAAG,CAAC;AAGpE,QAAI,CAAC,kBAAkB;AACrB,aAAO;AAAA,IACT;AACA,UAAM,gBAAgB,cAAc,KAAK,OAAO;AAOhD,WAAO,qBAAqB,iBAAiB,cAAc,SAAS,gBAAgB;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,SAAS,OAAO;AAC9B,UAAM,iBAAiB,KAAK;AAC5B,QAAI,CAAC,eAAe,IAAI,OAAO,KAAK,MAAM,MAAM,UAAQ;AAKtD,aAAO,KAAK,eAAe,MAAM,IAAI,KAAK,KAAK,YAAY,QAAQ,IAAI,IAAI;AAAA,IAC7E,CAAC,GAAG;AACF,qBAAe,IAAI,OAAO;AAC1B,WAAK,sBAAsB;AAC3B,WAAK,sBAAsB;AAC3B,WAAK,iBAAiB,KAAK;AAAA,QACzB,WAAW;AAAA,QACX,UAAU;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,SAAS;AACtB,SAAK,gBAAgB,OAAO,OAAO;AACnC,SAAK,4BAA4B,YAAY;AAC7C,SAAK,iBAAiB,KAAK;AAAA,MACzB,WAAW;AAAA,MACX,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,SAAK,8BAA8B,KAAK,kBAAkB,SAAS,KAAK,eAAe,CAAC,EAAE,UAAU,WAAS;AAC3G,UAAI,KAAK,WAAW,GAAG;AACrB,cAAM,mBAAmB,KAAK,iBAAiB,aAAa,KAAK;AACjE,YAAI,kBAAkB;AACpB,eAAK,cAAc,eAAe,iBAAiB,KAAK,iBAAiB,IAAI;AAAA,QAC/E;AAAA,MACF,WAAW,KAAK,YAAY,GAAG;AAC7B,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,QAAI,CAAC,KAAK,mBAAmB;AAC3B,YAAM,aAAa,eAAe,cAAc,KAAK,OAAO,CAAC;AAC7D,WAAK,oBAAoB,cAAc,KAAK;AAAA,IAC9C;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,2BAA2B;AACzB,UAAM,eAAe,KAAK,cAAc,uBAAuB,EAAE,OAAO,UAAQ,KAAK,WAAW,CAAC;AACjG,SAAK,UAAU,QAAQ,aAAW,QAAQ,gBAAgB,MAAM,YAAY,CAAC;AAAA,EAC/E;AACF;AAMA,SAAS,2BAA2B,YAAY,UAAU;AACxD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,SAAS;AAC5B,MAAI,YAAY,MAAM,cAAc,YAAY,MAAM,YAAY;AAChE,WAAO,4BAA4B;AAAA,EACrC,WAAW,YAAY,SAAS,cAAc,YAAY,SAAS,YAAY;AAC7E,WAAO,4BAA4B;AAAA,EACrC;AACA,SAAO,4BAA4B;AACrC;AAMA,SAAS,6BAA6B,YAAY,UAAU;AAC1D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,QAAQ;AAC3B,MAAI,YAAY,OAAO,cAAc,YAAY,OAAO,YAAY;AAClE,WAAO,8BAA8B;AAAA,EACvC,WAAW,YAAY,QAAQ,cAAc,YAAY,QAAQ,YAAY;AAC3E,WAAO,8BAA8B;AAAA,EACvC;AACA,SAAO,8BAA8B;AACvC;AAUA,SAAS,2BAA2B,SAAS,YAAY,WAAW,UAAU,UAAU;AACtF,QAAM,mBAAmB,2BAA2B,YAAY,QAAQ;AACxE,QAAM,qBAAqB,6BAA6B,YAAY,QAAQ;AAC5E,MAAI,0BAA0B,4BAA4B;AAC1D,MAAI,4BAA4B,8BAA8B;AAK9D,MAAI,kBAAkB;AACpB,UAAM,YAAY,QAAQ;AAC1B,QAAI,qBAAqB,4BAA4B,IAAI;AACvD,UAAI,YAAY,GAAG;AACjB,kCAA0B,4BAA4B;AAAA,MACxD;AAAA,IACF,WAAW,QAAQ,eAAe,YAAY,QAAQ,cAAc;AAClE,gCAA0B,4BAA4B;AAAA,IACxD;AAAA,EACF;AACA,MAAI,oBAAoB;AACtB,UAAM,aAAa,QAAQ;AAC3B,QAAI,cAAc,OAAO;AACvB,UAAI,uBAAuB,8BAA8B,OAAO;AAE9D,YAAI,aAAa,GAAG;AAClB,sCAA4B,8BAA8B;AAAA,QAC5D;AAAA,MACF,WAAW,QAAQ,cAAc,aAAa,QAAQ,aAAa;AACjE,oCAA4B,8BAA8B;AAAA,MAC5D;AAAA,IACF,OAAO;AACL,UAAI,uBAAuB,8BAA8B,MAAM;AAC7D,YAAI,aAAa,GAAG;AAClB,sCAA4B,8BAA8B;AAAA,QAC5D;AAAA,MACF,WAAW,QAAQ,cAAc,aAAa,QAAQ,aAAa;AACjE,oCAA4B,8BAA8B;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC,yBAAyB,yBAAyB;AAC5D;AAGA,IAAM,8BAA8B,gCAAgC;AAAA,EAClE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,aAAa,oBAAI,IAAI;AAK3B,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,MAC5B,WAAW,CAAC,6BAA6B,EAAE;AAAA,MAC3C,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,uBAAuB,IAAI,KAAK;AAAA,MAAC;AAAA,MACpD,QAAQ,CAAC,2FAA2F;AAAA,MACpG,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,6BAA6B;AAAA,MAC/B;AAAA,MACA,QAAQ,CAAC,2FAA2F;AAAA,IACtG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AASH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,SAAS,WAAW;AAC9B,SAAK,UAAU;AACf,SAAK,UAAU,OAAO,cAAc;AACpC,SAAK,uBAAuB,OAAO,mBAAmB;AAEtD,SAAK,iBAAiB,oBAAI,IAAI;AAE9B,SAAK,iBAAiB,oBAAI,IAAI;AAE9B,SAAK,uBAAuB,CAAC;AAE7B,SAAK,mBAAmB,oBAAI,IAAI;AAKhC,SAAK,qBAAqB,UAAQ,KAAK,WAAW;AAKlD,SAAK,cAAc,IAAI,QAAQ;AAK/B,SAAK,YAAY,IAAI,QAAQ;AAM7B,SAAK,SAAS,IAAI,QAAQ;AAK1B,SAAK,+BAA+B,WAAS;AAC3C,UAAI,KAAK,qBAAqB,SAAS,GAAG;AACxC,cAAM,eAAe;AAAA,MACvB;AAAA,IACF;AAEA,SAAK,+BAA+B,WAAS;AAC3C,UAAI,KAAK,qBAAqB,SAAS,GAAG;AAIxC,YAAI,KAAK,qBAAqB,KAAK,KAAK,kBAAkB,GAAG;AAC3D,gBAAM,eAAe;AAAA,QACvB;AACA,aAAK,YAAY,KAAK,KAAK;AAAA,MAC7B;AAAA,IACF;AACA,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,sBAAsB,MAAM;AAC1B,QAAI,CAAC,KAAK,eAAe,IAAI,IAAI,GAAG;AAClC,WAAK,eAAe,IAAI,IAAI;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,MAAM;AACrB,SAAK,eAAe,IAAI,IAAI;AAI5B,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,WAAK,QAAQ,kBAAkB,MAAM;AAGnC,aAAK,UAAU,iBAAiB,aAAa,KAAK,8BAA8B,2BAA2B;AAAA,MAC7G,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB,MAAM;AACxB,SAAK,eAAe,OAAO,IAAI;AAAA,EACjC;AAAA;AAAA,EAEA,eAAe,MAAM;AACnB,SAAK,eAAe,OAAO,IAAI;AAC/B,SAAK,aAAa,IAAI;AACtB,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,WAAK,UAAU,oBAAoB,aAAa,KAAK,8BAA8B,2BAA2B;AAAA,IAChH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,MAAM,OAAO;AAEzB,QAAI,KAAK,qBAAqB,QAAQ,IAAI,IAAI,IAAI;AAChD;AAAA,IACF;AACA,SAAK,YAAY;AACjB,SAAK,qBAAqB,KAAK,IAAI;AACnC,QAAI,KAAK,qBAAqB,WAAW,GAAG;AAC1C,YAAMC,gBAAe,MAAM,KAAK,WAAW,OAAO;AAIlD,WAAK,iBAAiB,IAAIA,gBAAe,aAAa,WAAW;AAAA,QAC/D,SAAS,OAAK,KAAK,UAAU,KAAK,CAAC;AAAA,QACnC,SAAS;AAAA,MACX,CAAC,EAAE,IAAI,UAAU;AAAA,QACf,SAAS,OAAK,KAAK,OAAO,KAAK,CAAC;AAAA;AAAA;AAAA,QAGhC,SAAS;AAAA,MACX,CAAC,EAKA,IAAI,eAAe;AAAA,QAClB,SAAS,KAAK;AAAA,QACd,SAAS;AAAA,MACX,CAAC;AAGD,UAAI,CAACA,eAAc;AACjB,aAAK,iBAAiB,IAAI,aAAa;AAAA,UACrC,SAAS,OAAK,KAAK,YAAY,KAAK,CAAC;AAAA,UACrC,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,iBAAiB,QAAQ,CAAC,QAAQ,SAAS;AAC9C,eAAK,UAAU,iBAAiB,MAAM,OAAO,SAAS,OAAO,OAAO;AAAA,QACtE,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,MAAM;AACjB,UAAM,QAAQ,KAAK,qBAAqB,QAAQ,IAAI;AACpD,QAAI,QAAQ,IAAI;AACd,WAAK,qBAAqB,OAAO,OAAO,CAAC;AACzC,UAAI,KAAK,qBAAqB,WAAW,GAAG;AAC1C,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,WAAW,MAAM;AACf,WAAO,KAAK,qBAAqB,QAAQ,IAAI,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,YAAY;AACnB,UAAM,UAAU,CAAC,KAAK,MAAM;AAC5B,QAAI,cAAc,eAAe,KAAK,WAAW;AAI/C,cAAQ,KAAK,IAAI,WAAW,cAAY;AACtC,eAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,gBAAM,eAAe;AACrB,gBAAM,WAAW,WAAS;AACxB,gBAAI,KAAK,qBAAqB,QAAQ;AACpC,uBAAS,KAAK,KAAK;AAAA,YACrB;AAAA,UACF;AACA,qBAAW,iBAAiB,UAAU,UAAU,YAAY;AAC5D,iBAAO,MAAM;AACX,uBAAW,oBAAoB,UAAU,UAAU,YAAY;AAAA,UACjE;AAAA,QACF,CAAC;AAAA,MACH,CAAC,CAAC;AAAA,IACJ;AACA,WAAO,MAAM,GAAG,OAAO;AAAA,EACzB;AAAA,EACA,cAAc;AACZ,SAAK,eAAe,QAAQ,cAAY,KAAK,eAAe,QAAQ,CAAC;AACrE,SAAK,eAAe,QAAQ,cAAY,KAAK,oBAAoB,QAAQ,CAAC;AAC1E,SAAK,sBAAsB;AAC3B,SAAK,YAAY,SAAS;AAC1B,SAAK,UAAU,SAAS;AAAA,EAC1B;AAAA;AAAA,EAEA,wBAAwB;AACtB,SAAK,iBAAiB,QAAQ,CAAC,QAAQ,SAAS;AAC9C,WAAK,UAAU,oBAAoB,MAAM,OAAO,SAAS,OAAO,OAAO;AAAA,IACzE,CAAC;AACD,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA,EAGA,cAAc;AACZ,QAAI,CAAC,WAAW,IAAI,KAAK,OAAO,GAAG;AACjC,iBAAW,IAAI,KAAK,OAAO;AAC3B,YAAM,eAAe,gBAAgB,eAAe;AAAA,QAClD,qBAAqB,KAAK;AAAA,MAC5B,CAAC;AACD,WAAK,QAAQ,UAAU,MAAM;AAC3B,mBAAW,OAAO,KAAK,OAAO;AAC9B,YAAI,WAAW,SAAS,GAAG;AACzB,uBAAa,QAAQ;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAqB,SAAY,MAAM,GAAM,SAAS,QAAQ,CAAC;AAAA,IAClF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAGH,IAAM,iBAAiB;AAAA,EACrB,oBAAoB;AAAA,EACpB,iCAAiC;AACnC;AAIA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,YAAY,WAAW,SAAS,gBAAgB,mBAAmB;AACjE,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,SAAS,SAAS,gBAAgB;AAC3C,WAAO,IAAI,QAAQ,SAAS,QAAQ,KAAK,WAAW,KAAK,SAAS,KAAK,gBAAgB,KAAK,iBAAiB;AAAA,EAC/G;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,SAAS;AACtB,WAAO,IAAI,YAAY,SAAS,KAAK,mBAAmB,KAAK,WAAW,KAAK,SAAS,KAAK,cAAc;AAAA,EAC3G;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iBAAiB,GAAG;AACvC,aAAO,KAAK,KAAK,WAAa,SAAS,QAAQ,GAAM,SAAY,MAAM,GAAM,SAAY,aAAa,GAAM,SAAS,gBAAgB,CAAC;AAAA,IACxI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,UAAS;AAAA,MAClB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAQH,IAAM,kBAAkB,IAAI,eAAe,iBAAiB;AAO5D,SAAS,kBAAkB,MAAM,MAAM;AACrC,MAAI,KAAK,aAAa,GAAG;AACvB,UAAM,MAAM,GAAG,IAAI,gEAAqE,KAAK,QAAQ,IAAI;AAAA,EAC3G;AACF;AAOA,IAAM,kBAAkB,IAAI,eAAe,eAAe;AAE1D,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA,EAElB,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,cAAc,KAAK,IAAI;AAAA,EAC9B;AAAA,EACA,YAAY,SAAS,aAAa;AAChC,SAAK,UAAU;AACf,SAAK,cAAc;AAEnB,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,YAAY;AACjB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,wBAAkB,QAAQ,eAAe,eAAe;AAAA,IAC1D;AACA,iBAAa,WAAW,IAAI;AAAA,EAC9B;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,cAAc,IAAI;AACpC,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAkB,kBAAqB,UAAU,GAAM,kBAAkB,iBAAiB,EAAE,CAAC;AAAA,IAChH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACrC,WAAW,CAAC,GAAG,iBAAiB;AAAA,MAChC,QAAQ;AAAA,QACN,UAAU,CAAI,WAAa,4BAA4B,yBAAyB,YAAY,gBAAgB;AAAA,MAC9G;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,wBAAwB;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,kBAAkB,IAAI,eAAe,iBAAiB;AAC5D,IAAM,kBAAkB;AAMxB,IAAM,gBAAgB,IAAI,eAAe,aAAa;AAEtD,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,OAAO;AACL,SAAK,iBAAiB,CAAC;AAAA,EACzB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,iBAAiB,KAAK,cAAc;AAAA,EACpE;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,SAAS,WAAW,KAAK;AAAA,EAChC;AAAA,EACA,YACA,SACA,eAKA,WAAW,SAAS,mBAAmB,QAAQ,MAAM,UAAU,oBAAoB,aAAa,aAAa;AAC3G,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,oBAAoB;AACzB,SAAK,OAAO;AACZ,SAAK,qBAAqB;AAC1B,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,WAAW,IAAI,gBAAgB,CAAC,CAAC;AAEtC,SAAK,UAAU,IAAI,aAAa;AAEhC,SAAK,WAAW,IAAI,aAAa;AAEjC,SAAK,QAAQ,IAAI,aAAa;AAE9B,SAAK,UAAU,IAAI,aAAa;AAEhC,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,UAAU,IAAI,aAAa;AAKhC,SAAK,QAAQ,IAAI,WAAW,cAAY;AACtC,YAAM,eAAe,KAAK,SAAS,MAAM,KAAK,IAAI,iBAAe;AAAA,QAC/D,QAAQ;AAAA,QACR,iBAAiB,WAAW;AAAA,QAC5B,OAAO,WAAW;AAAA,QAClB,OAAO,WAAW;AAAA,QAClB,UAAU,WAAW;AAAA,MACvB,EAAE,CAAC,EAAE,UAAU,QAAQ;AACvB,aAAO,MAAM;AACX,qBAAa,YAAY;AAAA,MAC3B;AAAA,IACF,CAAC;AACD,SAAK,WAAW,SAAS,WAAW,SAAS;AAAA,MAC3C,oBAAoB,UAAU,OAAO,sBAAsB,OAAO,OAAO,qBAAqB;AAAA,MAC9F,iCAAiC,UAAU,OAAO,mCAAmC,OAAO,OAAO,kCAAkC;AAAA,MACrI,QAAQ,QAAQ;AAAA,IAClB,CAAC;AACD,SAAK,SAAS,OAAO;AAIrB,aAAQ,eAAe,KAAK,IAAI;AAChC,QAAI,QAAQ;AACV,WAAK,gBAAgB,MAAM;AAAA,IAC7B;AAQA,QAAI,eAAe;AACjB,WAAK,SAAS,mBAAmB,cAAc,YAAY;AAC3D,oBAAc,QAAQ,IAAI;AAAA,IAC5B;AACA,SAAK,YAAY,KAAK,QAAQ;AAC9B,SAAK,cAAc,KAAK,QAAQ;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,WAAO,KAAK,SAAS,sBAAsB;AAAA,EAC7C;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK,SAAS,eAAe;AAAA,EACtC;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,SAAS,MAAM;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AACpB,WAAO,KAAK,SAAS,oBAAoB;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,OAAO;AACzB,SAAK,SAAS,oBAAoB,KAAK;AAAA,EACzC;AAAA,EACA,kBAAkB;AAGhB,SAAK,QAAQ,kBAAkB,MAAM;AAKnC,WAAK,QAAQ,SAAS,KAAK,KAAK,CAAC,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC9E,aAAK,mBAAmB;AACxB,aAAK,sBAAsB;AAC3B,YAAI,KAAK,kBAAkB;AACzB,eAAK,SAAS,oBAAoB,KAAK,gBAAgB;AAAA,QACzD;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,qBAAqB,QAAQ,qBAAqB;AACxD,UAAM,iBAAiB,QAAQ,kBAAkB;AAGjD,QAAI,sBAAsB,CAAC,mBAAmB,aAAa;AACzD,WAAK,mBAAmB;AAAA,IAC1B;AAEA,QAAI,kBAAkB,CAAC,eAAe,eAAe,KAAK,kBAAkB;AAC1E,WAAK,SAAS,oBAAoB,KAAK,gBAAgB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,WAAW,IAAI;AAAA,IACpC;AACA,UAAM,QAAQ,SAAQ,eAAe,QAAQ,IAAI;AACjD,QAAI,QAAQ,IAAI;AACd,eAAQ,eAAe,OAAO,OAAO,CAAC;AAAA,IACxC;AAEA,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,SAAS,SAAS;AACvB,WAAK,WAAW,KAAK;AACrB,WAAK,WAAW,SAAS;AACzB,WAAK,SAAS,QAAQ;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,WAAW,QAAQ;AACjB,UAAM,UAAU,KAAK,SAAS,SAAS;AACvC,YAAQ,KAAK,MAAM;AACnB,SAAK,SAAS,KAAK,OAAO;AAAA,EAC5B;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,UAAU,KAAK,SAAS,SAAS;AACvC,UAAM,QAAQ,QAAQ,QAAQ,MAAM;AACpC,QAAI,QAAQ,IAAI;AACd,cAAQ,OAAO,OAAO,CAAC;AACvB,WAAK,SAAS,KAAK,OAAO;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,oBAAoB,SAAS;AAC3B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,sBAAsB,SAAS;AAC7B,QAAI,YAAY,KAAK,kBAAkB;AACrC,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,wBAAwB,aAAa;AACnC,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,0BAA0B,aAAa;AACrC,QAAI,gBAAgB,KAAK,sBAAsB;AAC7C,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB;AACnB,UAAM,UAAU,KAAK,QAAQ;AAC7B,QAAI,cAAc;AAClB,QAAI,KAAK,qBAAqB;AAC5B,oBAAc,QAAQ,YAAY,SAAY,QAAQ,QAAQ,KAAK,mBAAmB;AAAA;AAAA,QAEtF,QAAQ,eAAe,QAAQ,KAAK,mBAAmB;AAAA;AAAA,IACzD;AACA,QAAI,gBAAgB,OAAO,cAAc,eAAe,YAAY;AAClE,wBAAkB,aAAa,SAAS;AAAA,IAC1C;AACA,SAAK,SAAS,gBAAgB,eAAe,OAAO;AAAA,EACtD;AAAA;AAAA,EAEA,sBAAsB;AACpB,UAAM,WAAW,KAAK;AACtB,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,aAAO,KAAK,QAAQ,cAAc,QAAQ,QAAQ;AAAA,IACpD;AACA,WAAO,cAAc,QAAQ;AAAA,EAC/B;AAAA;AAAA,EAEA,YAAY,KAAK;AACf,QAAI,cAAc,UAAU,MAAM;AAChC,UAAI,CAAC,IAAI,WAAW,GAAG;AACrB,cAAM,MAAM,KAAK;AACjB,cAAM,iBAAiB,KAAK;AAC5B,cAAM,cAAc,KAAK,uBAAuB;AAAA,UAC9C,UAAU,KAAK,qBAAqB;AAAA,UACpC,SAAS,KAAK,qBAAqB;AAAA,UACnC,eAAe,KAAK;AAAA,QACtB,IAAI;AACJ,cAAM,UAAU,KAAK,mBAAmB;AAAA,UACtC,UAAU,KAAK,iBAAiB;AAAA,UAChC,SAAS,KAAK,iBAAiB;AAAA,UAC/B,WAAW,KAAK,iBAAiB;AAAA,UACjC,eAAe,KAAK;AAAA,QACtB,IAAI;AACJ,YAAI,WAAW,KAAK;AACpB,YAAI,WAAW,KAAK;AACpB,YAAI,iBAAiB,OAAO,mBAAmB,YAAY,iBAAiB,iBAAiB,qBAAqB,cAAc;AAChI,YAAI,oBAAoB,KAAK;AAC7B,YAAI,eAAe,KAAK;AACxB,YAAI,oBAAoB,KAAK,oBAAoB,CAAC,EAAE,wBAAwB,WAAW,EAAE,oBAAoB,OAAO,EAAE,qBAAqB,KAAK,oBAAoB,QAAQ;AAC5K,YAAI,KAAK;AACP,cAAI,cAAc,IAAI,KAAK;AAAA,QAC7B;AAAA,MACF;AAAA,IACF,CAAC;AAED,QAAI,cAAc,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AAE9C,UAAI,KAAK,aAAa;AACpB,YAAI,WAAW,KAAK,YAAY,QAAQ;AACxC;AAAA,MACF;AAGA,UAAI,SAAS,KAAK,QAAQ,cAAc;AACxC,aAAO,QAAQ;AACb,YAAI,OAAO,UAAU,SAAS,eAAe,GAAG;AAC9C,cAAI,WAAW,SAAQ,eAAe,KAAK,UAAQ;AACjD,mBAAO,KAAK,QAAQ,kBAAkB;AAAA,UACxC,CAAC,GAAG,YAAY,IAAI;AACpB;AAAA,QACF;AACA,iBAAS,OAAO;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,cAAc,KAAK;AACjB,QAAI,QAAQ,UAAU,gBAAc;AAClC,WAAK,QAAQ,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR,OAAO,WAAW;AAAA,MACpB,CAAC;AAGD,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,QAAI,SAAS,UAAU,kBAAgB;AACrC,WAAK,SAAS,KAAK;AAAA,QACjB,QAAQ;AAAA,QACR,OAAO,aAAa;AAAA,MACtB,CAAC;AAAA,IACH,CAAC;AACD,QAAI,MAAM,UAAU,cAAY;AAC9B,WAAK,MAAM,KAAK;AAAA,QACd,QAAQ;AAAA,QACR,UAAU,SAAS;AAAA,QACnB,WAAW,SAAS;AAAA,QACpB,OAAO,SAAS;AAAA,MAClB,CAAC;AAGD,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,QAAI,QAAQ,UAAU,gBAAc;AAClC,WAAK,QAAQ,KAAK;AAAA,QAChB,WAAW,WAAW,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,cAAc,WAAW;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC;AACD,QAAI,OAAO,UAAU,eAAa;AAChC,WAAK,OAAO,KAAK;AAAA,QACf,WAAW,UAAU,UAAU;AAAA,QAC/B,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AACD,QAAI,QAAQ,UAAU,eAAa;AACjC,WAAK,QAAQ,KAAK;AAAA,QAChB,eAAe,UAAU;AAAA,QACzB,cAAc,UAAU;AAAA,QACxB,mBAAmB,UAAU,kBAAkB;AAAA,QAC/C,WAAW,UAAU,UAAU;AAAA,QAC/B,wBAAwB,UAAU;AAAA,QAClC,MAAM;AAAA,QACN,UAAU,UAAU;AAAA,QACpB,WAAW,UAAU;AAAA,QACrB,OAAO,UAAU;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,gBAAgB,QAAQ;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,WAAW,oBAAoB,OAAO,QAAQ;AACnD,SAAK,iBAAiB,kBAAkB;AACxC,QAAI,UAAU;AACZ,WAAK,WAAW;AAAA,IAClB;AACA,QAAI,mBAAmB;AACrB,WAAK,oBAAoB;AAAA,IAC3B;AACA,QAAI,cAAc;AAChB,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,iBAAiB;AACnB,WAAK,kBAAkB;AAAA,IACzB;AACA,QAAI,qBAAqB;AACvB,WAAK,sBAAsB;AAAA,IAC7B;AACA,QAAI,kBAAkB;AACpB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AAEtB,SAAK,SAAS;AAAA;AAAA,MAEd,IAAI,aAAW;AACb,cAAM,iBAAiB,QAAQ,IAAI,YAAU,OAAO,OAAO;AAI3D,YAAI,KAAK,eAAe,KAAK,qBAAqB;AAChD,yBAAe,KAAK,KAAK,OAAO;AAAA,QAClC;AACA,aAAK,SAAS,YAAY,cAAc;AAAA,MAC1C,CAAC;AAAA;AAAA,MAED,UAAU,aAAW;AACnB,eAAO,MAAM,GAAG,QAAQ,IAAI,UAAQ,KAAK,cAAc,KAAK,UAAU,IAAI,CAAC,CAAC,CAAC;AAAA,MAC/E,CAAC;AAAA,MAAG,UAAU,KAAK,UAAU;AAAA,IAAC,EAAE,UAAU,oBAAkB;AAE1D,YAAM,UAAU,KAAK;AACrB,YAAM,SAAS,eAAe,QAAQ;AACtC,qBAAe,WAAW,QAAQ,cAAc,MAAM,IAAI,QAAQ,aAAa,MAAM;AAAA,IACvF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gBAAgB,GAAG;AACtC,aAAO,KAAK,KAAK,UAAY,kBAAqB,UAAU,GAAM,kBAAkB,eAAe,EAAE,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,iBAAiB,CAAC,GAAM,kBAAuB,gBAAgB,CAAC,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,iBAAiB,EAAE,GAAM,kBAAkB,iBAAiB,EAAE,CAAC;AAAA,IAC9c;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/B,WAAW,CAAC,GAAG,UAAU;AAAA,MACzB,UAAU;AAAA,MACV,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,qBAAqB,IAAI,QAAQ,EAAE,qBAAqB,IAAI,SAAS,WAAW,CAAC;AAAA,QAClG;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,MAAM,CAAI,WAAa,MAAM,eAAe,MAAM;AAAA,QAClD,UAAU,CAAI,WAAa,MAAM,mBAAmB,UAAU;AAAA,QAC9D,qBAAqB,CAAI,WAAa,MAAM,sBAAsB,qBAAqB;AAAA,QACvF,iBAAiB,CAAI,WAAa,MAAM,mBAAmB,iBAAiB;AAAA,QAC5E,gBAAgB,CAAI,WAAa,MAAM,qBAAqB,gBAAgB;AAAA,QAC5E,kBAAkB,CAAI,WAAa,MAAM,2BAA2B,kBAAkB;AAAA,QACtF,UAAU,CAAI,WAAa,4BAA4B,mBAAmB,YAAY,gBAAgB;AAAA,QACtG,mBAAmB,CAAI,WAAa,MAAM,4BAA4B,mBAAmB;AAAA,QACzF,cAAc,CAAI,WAAa,MAAM,uBAAuB,cAAc;AAAA,QAC1E,kBAAkB,CAAI,WAAa,MAAM,2BAA2B,kBAAkB;AAAA,MACxF;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,0BAA6B,oBAAoB;AAAA,IAC3D,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,MAC/B;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,sBAAsB,IAAI,eAAe,kBAAkB;AAOjE,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AAEZ,SAAK,SAAS,oBAAI,IAAI;AAEtB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,MAAM;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,MACxC,QAAQ;AAAA,QACN,UAAU,CAAI,WAAa,4BAA4B,4BAA4B,YAAY,gBAAgB;AAAA,MACjH;AAAA,MACA,UAAU,CAAC,kBAAkB;AAAA,MAC7B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,wBAAwB;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAI,mBAAmB;AAEvB,IAAM,cAAN,MAAM,aAAY;AAAA,EAEhB,OAAO;AACL,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,CAAC,CAAC,KAAK,UAAU,KAAK,OAAO;AAAA,EACxD;AAAA,EACA,IAAI,SAAS,OAAO;AAKlB,SAAK,aAAa,WAAW,KAAK,YAAY;AAAA,EAChD;AAAA,EACA,YACA,SAAS,UAAU,oBAAoB,mBAAmB,MAAM,QAAQ,QAAQ;AAC9E,SAAK,UAAU;AACf,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB;AACzB,SAAK,OAAO;AACZ,SAAK,SAAS;AAEd,SAAK,aAAa,IAAI,QAAQ;AAM9B,SAAK,cAAc,CAAC;AAKpB,SAAK,KAAK,iBAAiB,kBAAkB;AAK7C,SAAK,iBAAiB,MAAM;AAE5B,SAAK,gBAAgB,MAAM;AAE3B,SAAK,UAAU,IAAI,aAAa;AAIhC,SAAK,UAAU,IAAI,aAAa;AAKhC,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,SAAS,IAAI,aAAa;AAQ/B,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,wBAAkB,QAAQ,eAAe,aAAa;AAAA,IACxD;AACA,SAAK,eAAe,SAAS,eAAe,OAAO;AACnD,SAAK,aAAa,OAAO;AACzB,QAAI,QAAQ;AACV,WAAK,gBAAgB,MAAM;AAAA,IAC7B;AACA,SAAK,aAAa,iBAAiB,CAAC,MAAM,SAAS;AACjD,aAAO,KAAK,eAAe,KAAK,MAAM,KAAK,IAAI;AAAA,IACjD;AACA,SAAK,aAAa,gBAAgB,CAAC,OAAO,MAAM,SAAS;AACvD,aAAO,KAAK,cAAc,OAAO,KAAK,MAAM,KAAK,IAAI;AAAA,IACvD;AACA,SAAK,4BAA4B,KAAK,YAAY;AAClD,SAAK,cAAc,KAAK,YAAY;AACpC,iBAAY,WAAW,KAAK,IAAI;AAChC,QAAI,QAAQ;AACV,aAAO,OAAO,IAAI,IAAI;AAAA,IACxB;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ,MAAM;AACZ,SAAK,eAAe,IAAI,IAAI;AAC5B,QAAI,KAAK,aAAa,WAAW,GAAG;AAClC,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,WAAW,MAAM;AACf,SAAK,eAAe,OAAO,IAAI;AAC/B,QAAI,KAAK,aAAa,WAAW,GAAG;AAClC,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,MAAM,KAAK,KAAK,cAAc,EAAE,KAAK,CAAC,GAAG,MAAM;AACpD,YAAM,mBAAmB,EAAE,SAAS,kBAAkB,EAAE,wBAAwB,EAAE,SAAS,kBAAkB,CAAC;AAI9G,aAAO,mBAAmB,KAAK,8BAA8B,KAAK;AAAA,IACpE,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ,aAAY,WAAW,QAAQ,IAAI;AACjD,QAAI,QAAQ,IAAI;AACd,mBAAY,WAAW,OAAO,OAAO,CAAC;AAAA,IACxC;AACA,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,OAAO,OAAO,IAAI;AAAA,IAChC;AACA,SAAK,eAAe,MAAM;AAC1B,SAAK,aAAa,QAAQ;AAC1B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA,EAEA,4BAA4B,KAAK;AAC/B,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,OAAO,KAAK,UAAU,KAAK,KAAK,KAAK,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,WAAS,IAAI,cAAc,KAAK,CAAC;AAAA,IAC3H;AACA,QAAI,cAAc,UAAU,MAAM;AAChC,YAAM,WAAW,YAAY,KAAK,WAAW,EAAE,IAAI,UAAQ;AACzD,YAAI,OAAO,SAAS,UAAU;AAC5B,gBAAM,wBAAwB,aAAY,WAAW,KAAK,UAAQ,KAAK,OAAO,IAAI;AAClF,cAAI,CAAC,0BAA0B,OAAO,cAAc,eAAe,YAAY;AAC7E,oBAAQ,KAAK,2DAA2D,IAAI,GAAG;AAAA,UACjF;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC;AACD,UAAI,KAAK,QAAQ;AACf,aAAK,OAAO,OAAO,QAAQ,UAAQ;AACjC,cAAI,SAAS,QAAQ,IAAI,MAAM,IAAI;AACjC,qBAAS,KAAK,IAAI;AAAA,UACpB;AAAA,QACF,CAAC;AAAA,MACH;AAGA,UAAI,CAAC,KAAK,4BAA4B;AACpC,cAAM,oBAAoB,KAAK,kBAAkB,4BAA4B,KAAK,OAAO,EAAE,IAAI,gBAAc,WAAW,cAAc,EAAE,aAAa;AACrJ,aAAK,aAAa,sBAAsB,iBAAiB;AAGzD,aAAK,6BAA6B;AAAA,MACpC;AACA,UAAI,WAAW,KAAK;AACpB,UAAI,WAAW,KAAK;AACpB,UAAI,kBAAkB,KAAK;AAC3B,UAAI,qBAAqB,KAAK;AAC9B,UAAI,iBAAiB,qBAAqB,KAAK,gBAAgB,CAAC;AAChE,UAAI,YAAY,SAAS,OAAO,UAAQ,QAAQ,SAAS,IAAI,EAAE,IAAI,UAAQ,KAAK,YAAY,CAAC,EAAE,gBAAgB,KAAK,WAAW;AAAA,IACjI,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,cAAc,KAAK;AACjB,QAAI,cAAc,UAAU,MAAM;AAChC,WAAK,kBAAkB;AACvB,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,QAAI,QAAQ,UAAU,WAAS;AAC7B,WAAK,QAAQ,KAAK;AAAA,QAChB,WAAW;AAAA,QACX,MAAM,MAAM,KAAK;AAAA,QACjB,cAAc,MAAM;AAAA,MACtB,CAAC;AAAA,IACH,CAAC;AACD,QAAI,OAAO,UAAU,WAAS;AAC5B,WAAK,OAAO,KAAK;AAAA,QACf,WAAW;AAAA,QACX,MAAM,MAAM,KAAK;AAAA,MACnB,CAAC;AACD,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,QAAI,OAAO,UAAU,WAAS;AAC5B,WAAK,OAAO,KAAK;AAAA,QACf,eAAe,MAAM;AAAA,QACrB,cAAc,MAAM;AAAA,QACpB,WAAW;AAAA,QACX,MAAM,MAAM,KAAK;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AACD,QAAI,QAAQ,UAAU,eAAa;AACjC,WAAK,QAAQ,KAAK;AAAA,QAChB,eAAe,UAAU;AAAA,QACzB,cAAc,UAAU;AAAA,QACxB,mBAAmB,UAAU,kBAAkB;AAAA,QAC/C,WAAW,UAAU,UAAU;AAAA,QAC/B,MAAM,UAAU,KAAK;AAAA,QACrB,wBAAwB,UAAU;AAAA,QAClC,UAAU,UAAU;AAAA,QACpB,WAAW,UAAU;AAAA,QACrB,OAAO,UAAU;AAAA,MACnB,CAAC;AAGD,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,UAAM,IAAI,kBAAkB,IAAI,gBAAgB,EAAE,UAAU,MAAM,KAAK,mBAAmB,aAAa,CAAC;AAAA,EAC1G;AAAA;AAAA,EAEA,gBAAgB,QAAQ;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,WAAW,oBAAoB,OAAO,QAAQ;AACnD,SAAK,kBAAkB,mBAAmB,OAAO,QAAQ;AACzD,SAAK,qBAAqB,0BAA0B,OAAO,QAAQ;AACnE,SAAK,cAAc,mBAAmB;AACtC,QAAI,UAAU;AACZ,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB;AAClB,SAAK,aAAa,UAAU,KAAK,eAAe,EAAE,IAAI,UAAQ,KAAK,QAAQ,CAAC;AAAA,EAC9E;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,GAAG;AAC1C,aAAO,KAAK,KAAK,cAAgB,kBAAqB,UAAU,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,GAAM,kBAAuB,gBAAgB,CAAC,GAAM,kBAAkB,qBAAqB,EAAE,GAAM,kBAAkB,iBAAiB,CAAC,CAAC;AAAA,IACjU;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,GAAG,CAAC,eAAe,CAAC;AAAA,MACtD,WAAW,CAAC,GAAG,eAAe;AAAA,MAC9B,UAAU;AAAA,MACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,MAAM,IAAI,EAAE;AAC3B,UAAG,YAAY,0BAA0B,IAAI,QAAQ,EAAE,0BAA0B,IAAI,aAAa,WAAW,CAAC,EAAE,2BAA2B,IAAI,aAAa,YAAY,CAAC;AAAA,QAC3K;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa,CAAI,WAAa,MAAM,0BAA0B,aAAa;AAAA,QAC3E,MAAM,CAAI,WAAa,MAAM,mBAAmB,MAAM;AAAA,QACtD,aAAa,CAAI,WAAa,MAAM,0BAA0B,aAAa;AAAA,QAC3E,IAAI;AAAA,QACJ,UAAU,CAAI,WAAa,MAAM,uBAAuB,UAAU;AAAA,QAClE,UAAU,CAAI,WAAa,4BAA4B,uBAAuB,YAAY,gBAAgB;AAAA,QAC1G,iBAAiB,CAAI,WAAa,4BAA4B,8BAA8B,mBAAmB,gBAAgB;AAAA,QAC/H,gBAAgB,CAAI,WAAa,MAAM,6BAA6B,gBAAgB;AAAA,QACpF,eAAe,CAAI,WAAa,MAAM,4BAA4B,eAAe;AAAA,QACjF,oBAAoB,CAAI,WAAa,4BAA4B,iCAAiC,sBAAsB,gBAAgB;AAAA,QACxI,gBAAgB,CAAI,WAAa,MAAM,6BAA6B,gBAAgB;AAAA,MACtF;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA;AAAA,QAEjC;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,QAAG;AAAA,UACD,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,MAAC,CAAC,GAAM,wBAAwB;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW;AAAA;AAAA,QAEX;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,QAAG;AAAA,UACD,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,MAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,aAAa;AAAA,QACb,kCAAkC;AAAA,QAClC,kCAAkC;AAAA,QAClC,mCAAmC;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,mBAAmB,IAAI,eAAe,gBAAgB;AAK5D,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,QAAQ,OAAO,iBAAiB;AAAA,MACnC,UAAU;AAAA,IACZ,CAAC;AAED,SAAK,YAAY;AACjB,SAAK,OAAO,oBAAoB,IAAI;AAAA,EACtC;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,sBAAsB,IAAI;AAAA,EACxC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAmB,kBAAqB,WAAW,CAAC;AAAA,IACvE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,kBAAkB,EAAE,CAAC;AAAA,MACjD,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,WAAW,CAAI,WAAa,4BAA4B,aAAa,aAAa,gBAAgB;AAAA,MACpG;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,wBAAwB;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,uBAAuB,IAAI,eAAe,oBAAoB;AAKpE,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,QAAQ,OAAO,iBAAiB;AAAA,MACnC,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,OAAO,wBAAwB,IAAI;AAAA,EAC1C;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,0BAA0B,IAAI;AAAA,EAC5C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAuB,kBAAqB,WAAW,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,sBAAsB,EAAE,CAAC;AAAA,MACrD,QAAQ;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,CAAC;AAAA,IACL,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,uBAAuB,CAAC,aAAa,kBAAkB,SAAS,eAAe,gBAAgB,kBAAkB;AACvH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,aAAa,kBAAkB,SAAS,eAAe,gBAAgB,kBAAkB;AAAA,MACnG,SAAS,CAAC,qBAAqB,aAAa,kBAAkB,SAAS,eAAe,gBAAgB,kBAAkB;AAAA,IAC1H,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,QAAQ;AAAA,MACpB,SAAS,CAAC,mBAAmB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS;AAAA,MACT,SAAS,CAAC,qBAAqB,GAAG,oBAAoB;AAAA,MACtD,WAAW,CAAC,QAAQ;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACjjIH,IAAM,MAAM,CAAC,kBAAkB,EAAE;AACjC,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,YAAY;AAAA,EACtC;AACF;AACA,IAAM,MAAM,CAAC,cAAc;AAC3B,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,kFAAkF;AAChH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,CAAC;AAAA,IAC7C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,OAAO,OAAO,SAAY,cAAc;AAAA,EACrE;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AAAC;AAC5E,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,OAAO,WAAc,cAAc;AAAA,EACvE;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,mFAAmF;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,CAAC;AAAA,IACzC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,OAAO,eAAe,EAAE,YAAY,OAAO,OAAO,gBAAgB;AACpG,IAAG,YAAY,mBAAmB,OAAO,OAAO,gBAAgB,YAAY,IAAI;AAChF,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,gBAAgB,OAAO,OAAO,YAAY,GAAG;AAAA,EACxF;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,mFAAmF;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,KAAK,CAAC;AAAA,IACrC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,OAAO,OAAO,QAAQ,EAAE,aAAa,OAAO,OAAO,WAAW,EAAE,YAAY,OAAO,OAAO,YAAY,EAAE,YAAY,OAAO,OAAO,UAAU;AACpK,IAAG,YAAY,mBAAmB,OAAO,OAAO,gBAAgB,QAAQ,IAAI;AAC5E,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,YAAY,OAAO,OAAO,QAAQ,GAAG;AAAA,EAChF;AACF;AACA,IAAM,MAAM,CAAC,mBAAmB,EAAE;AAClC,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,WAAW;AAAA,EACX,UAAU;AACZ;AACA,SAAS,iFAAiF,IAAI,KAAK;AACjG,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,2GAA2G;AACzI,YAAM,YAAe,cAAc,GAAG,EAAE;AACxC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,cAAc,SAAS,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,CAAC,OAAO,sBAAsB,WAAW,MAAM,CAAC,EAAE,aAAa,OAAO,sBAAsB,WAAW,SAAS,CAAC,EAAE,YAAY,OAAO,sBAAsB,WAAW,UAAU,CAAC,EAAE,UAAU,UAAU,IAAI,EAAE,YAAY,UAAU,MAAM,EAAE,WAAW,UAAU,KAAK,EAAE,UAAU,UAAU,IAAI,EAAE,WAAW,UAAU,KAAK;AACpV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,UAAU,OAAO,GAAG;AAAA,EACjD;AACF;AACA,SAAS,2EAA2E,IAAI,KAAK;AAC3F,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,kFAAkF,GAAG,GAAG,UAAU,GAAM,yBAAyB;AAAA,EAC1J;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,OAAO;AAAA,EAC9B;AACF;AACA,SAAS,2EAA2E,IAAI,KAAK;AAC3F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,OAAO,UAAa,cAAc;AAAA,EACtE;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,4EAA4E,GAAG,CAAC,EAAE,GAAG,4EAA4E,GAAG,CAAC;AACtL,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,gBAAgB,IAAI,CAAC;AAAA,EAClD;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,gBAAgB,CAAC;AAAA,EACxG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,0BAA0B,OAAO,OAAO,QAAQ,EAAE,iCAAoC,gBAAgB,GAAG,KAAK,OAAO,OAAO,QAAQ,OAAO,QAAQ,CAAC;AAAA,EACpK;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,sFAAsF;AACpH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,SAAS,CAAC;AAAA,IACzC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,OAAO,eAAe,EAAE,YAAY,OAAO,OAAO,gBAAgB;AACpG,IAAG,YAAY,mBAAmB,OAAO,OAAO,gBAAgB,YAAY,IAAI;AAChF,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,gBAAgB,OAAO,OAAO,YAAY,GAAG;AAAA,EACxF;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,sFAAsF;AACpH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,KAAK,CAAC;AAAA,IACrC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,OAAO,QAAQ,EAAE,YAAY,OAAO,OAAO,UAAU,EAAE,aAAa,OAAO,OAAO,WAAW,EAAE,YAAY,OAAO,OAAO,YAAY;AACpK,IAAG,YAAY,mBAAmB,OAAO,OAAO,gBAAgB,QAAQ,IAAI;AAC5E,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,YAAY,OAAO,OAAO,QAAQ,GAAG;AAAA,EAChF;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,UAAU,CAAC,EAAE,GAAG,6DAA6D,GAAG,GAAG,UAAU,CAAC;AAAA,EACpL;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,GAAG,OAAO,OAAO,iBAAiB,OAAO,IAAI,EAAE;AAChE,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,OAAO,aAAa,OAAO,IAAI,EAAE;AAAA,EAC9D;AACF;AACA,IAAM,MAAM,CAAC,kBAAkB,EAAE;AACjC,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,OAAO,CAAC;AACxB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,OAAO,OAAO,SAAY,cAAc;AAAA,EACrE;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,2EAA2E;AACzG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,CAAC;AAAA,IAC7C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,UAAU,OAAO,OAAO,cAAc,SAAS,MAAM;AAAA,EACtE;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AAAC;AACpE,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,OAAO,WAAc,cAAc;AAAA,EACvE;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,mBAAmB,SAAS,kFAAkF;AAC1H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,CAAC;AAAA,IAC7C,CAAC,EAAE,eAAe,SAAS,8EAA8E;AACvG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,CAAC;AAAA,IAC1C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,QAAQ;AAAA,EAC3C;AACF;AACA,IAAM,UAAU,MAAM;AACtB,IAAM,eAAN,MAAmB;AAAA,EACjB,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,SAAS;AAEd,SAAK,aAAa;AAAA,EACpB;AACF;AAMA,IAAM,sBAAsB;AAAA,EAC1B,OAAO;AAAA,EACP,aAAa;AAAA,EACb,OAAO;AAAA,EACP,aAAa;AACf;AACA,IAAM,sBAAsB;AAAA,EAC1B,OAAO;AAAA,EACP,aAAa;AAAA,EACb,OAAO;AAAA,EACP,aAAa;AACf;AACA,IAAM,wBAAwB;AAC9B,IAAM,wBAAwB;AAC9B,IAAM,gBAAgB,IAAI,eAAe,eAAe;AAMxD,IAAM,oBAAoB;AAAA,EACxB,gBAAgB,QAAQ,kBAAkB,CAAC,MAAM,cAAc,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,SAAS,MAAM,CAAC,CAAC,CAAC,GAAG,WAAW,cAAc,QAAQ,QAAQ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,wBAAwB,QAAQ,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5N;AAMA,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,QAAQ;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAA0B,kBAAkB,YAAY,CAAC;AAAA,IAC5E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,kBAAkB,EAAE,CAAC;AAAA,MAC5C,WAAW,CAAC,cAAc,SAAS,GAAG,iBAAiB;AAAA,MACvD,UAAU,CAAC,qBAAqB;AAAA,MAChC,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,WAAW,IAAI,GAAG,wBAAwB,GAAG,QAAQ,CAAC;AAAA,MACzH,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,UAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,gBAAgB,CAAC;AACvF,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,0BAA0B,IAAI,OAAO,WAAW;AAAA,QAChE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAiB,iBAAiB,gBAAmB,+BAA+B;AAAA,MACnG,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,cAAc;AAAA,MAChB;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,cAAc,cAAc;AAAA,MACtC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,SAAS,oBAAoB,QAAQ,gBAAgB;AACnD,SAAO,kCACF,iBACA;AAEP;AACA,SAAS,mBAAmB,WAAW,aAAa,cAAc;AAChE,SAAO,OAAO,cAAc,cAAc,OAAO,gBAAgB,cAAc,eAAe,cAAc;AAC9G;AACA,SAAS,uBAAuB,WAAW;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,0CAA0C;AACjD,QAAM,MAAM,sEAAsE;AACpF;AACA,IAAM,8BAAN,MAAM,qCAAoC,iBAAiB;AAAA,EACzD,IAAI,WAAW;AACb,UAAM,gBAAgB,KAAK,gBAAgB,sBAAsB,qBAAqB,KAAK,CAAC;AAC5F,WAAO,CAAC,CAAC,mBAAmB,KAAK,OAAO,QAAQ,cAAc,QAAQ,IAAI;AAAA,EAC5E;AAAA,EACA,IAAI,eAAe;AACjB,UAAM,gBAAgB,KAAK,gBAAgB,sBAAsB,qBAAqB,KAAK,CAAC;AAC5F,WAAO,CAAC,CAAC,mBAAmB,KAAK,OAAO,gBAAgB,cAAc,gBAAgB,IAAI;AAAA,EAC5F;AAAA,EACA,YAAY,QAAQ,MAAM,kBAAkB,KAAK,QAAQ,YAAY,iBAAiB,QAAQ,UAAU,eAAe;AACrH,UAAM;AACN,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,mBAAmB;AACxB,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,SAAS;AACd,SAAK,gBAAgB;AACrB,SAAK,wBAAwB,IAAI,aAAa;AAC9C,SAAK,iBAAiB,IAAI,aAAa;AACvC,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,MAAM;AACX,SAAK,qCAAqC;AAC1C,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,WAAW;AAChB,SAAK,MAAM,WAAW,aAAa;AACnC,SAAK,kBAAkB,OAAO,OAAO,cAAc;AACnD,SAAK,gBAAgB,iCAAiC,qBAAqB,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC1H,WAAK,oBAAoB;AAAA,IAC3B,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,GAAG;AAClB,QAAI,EAAE,WAAW,EAAE,iBAAiB,CAAC,KAAK,aAAa,KAAK,YAAY,KAAK,cAAc;AACzF,WAAK,eAAe,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,eAAe;AACb,SAAK,gBAAgB,KAAK;AAAA,EAC5B;AAAA,EACA,YAAY;AACV,SAAK,YAAY,KAAK;AAAA,EACxB;AAAA,EACA,sBAAsB,QAAQ;AAC5B,QAAI,KAAK,aAAa,YAAY,GAAG;AACnC,8CAAwC;AAAA,IAC1C;AACA,SAAK,6BAA6B;AAClC,SAAK,qBAAqB;AAC1B,WAAO,KAAK,aAAa,sBAAsB,MAAM;AAAA,EACvD;AAAA,EACA,qBAAqB,QAAQ;AAC3B,QAAI,KAAK,aAAa,YAAY,GAAG;AACnC,8CAAwC;AAAA,IAC1C;AACA,SAAK,6BAA6B;AAClC,SAAK,qBAAqB;AAC1B,WAAO,KAAK,aAAa,qBAAqB,MAAM;AAAA,EACtD;AAAA,EACA,sBAAsB;AACpB,SAAK,6BAA6B;AAClC,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,OAAO,iBAAiB,KAAK,kBAAkB;AAAA,EAC7D;AAAA,EACA,0BAA0B;AACxB,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,KAAK,oCAAoC;AAC3C,YAAM,oBAAoB,KAAK,mCAAmC,sBAAsB;AACxF,YAAM,eAAe,iBAAiB,KAAK,kCAAkC;AAC7E,YAAM,IAAI,aAAa,OAAO,kBAAkB,QAAQ;AACxD,YAAM,IAAI,aAAa,MAAM,kBAAkB,SAAS;AACxD,YAAM,kBAAkB,GAAG,IAAI,aAAa,UAAU,MAAM,IAAI,aAAa,SAAS;AACtF,WAAK,OAAO,SAAS,cAAc,oBAAoB,eAAe;AAAA,IACxE;AAAA,EACF;AAAA,EACA,+BAA+B;AAC7B,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY,KAAK,iBAAiB,OAAO,KAAK,KAAK,aAAa;AAAA,IACvE;AACA,QAAI,KAAK,UAAU;AACjB,WAAK,qCAAqC,KAAK,SAAS;AACxD,UAAI,KAAK,KAAK,cAAc,OAAO;AACjC,aAAK,OAAO,kBAAkB,MAAM,aAAa,MAAM,KAAK,KAAK,cAAc,MAAM,CAAC,CAAC;AAAA,MACzF;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AACV,UAAM,UAAU,KAAK,KAAK;AAC1B,QAAI,KAAK,OAAO,aAAa;AAC3B,WAAK,UAAU,6BAA6B;AAAA,IAC9C,OAAO;AACL,YAAM,gBAAgB,KAAK,SAAS;AACpC,UAAI,kBAAkB,WAAW,CAAC,QAAQ,SAAS,aAAa,GAAG;AACjE,gBAAQ,MAAM;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe;AACb,UAAM,UAAU,KAAK;AAErB,QAAI,WAAW,OAAO,QAAQ,UAAU,YAAY;AAClD,YAAM,gBAAgB,KAAK,SAAS;AACpC,YAAM,UAAU,KAAK,KAAK;AAC1B,UAAI,CAAC,iBAAiB,kBAAkB,KAAK,SAAS,QAAQ,kBAAkB,WAAW,QAAQ,SAAS,aAAa,GAAG;AAC1H,gBAAQ,MAAM;AAAA,MAChB;AAAA,IACF;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,QAAQ;AAAA,IACzB;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,QAAI,KAAK,kBAAkB,GAAG;AAC5B;AAAA,IACF;AAEA,SAAK,wBAAwB;AAC7B,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,kBAAkB,KAAK,WAAW;AACxC,iBAAa,UAAU,IAAI,oBAAoB,KAAK;AACpD,iBAAa,UAAU,IAAI,oBAAoB,WAAW;AAC1D,QAAI,iBAAiB;AACnB,sBAAgB,UAAU,IAAI,oBAAoB,KAAK;AACvD,sBAAgB,UAAU,IAAI,oBAAoB,WAAW;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,UAAM,eAAe,KAAK,gBAAgB;AAC1C,iBAAa,UAAU,IAAI,oBAAoB,KAAK;AACpD,iBAAa,UAAU,IAAI,oBAAoB,WAAW;AAC1D,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,0BAA0B,QAAQ,OAAO;AACvC,UAAM,kBAAkB,KAAK,WAAW;AACxC,QAAI,iBAAiB;AACnB,UAAI,KAAK,kBAAkB,KAAK,OAAO;AAErC,wBAAgB,UAAU,OAAO,qBAAqB;AACtD;AAAA,MACF;AACA,sBAAgB,UAAU,IAAI,oBAAoB,KAAK;AACvD,sBAAgB,UAAU,IAAI,oBAAoB,WAAW;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,kBAAkB,GAAG;AAC5B;AAAA,IACF;AACA,UAAM,kBAAkB,KAAK,WAAW;AACxC,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,iBAAiB;AACnB,sBAAgB,UAAU,OAAO,oBAAoB,KAAK;AAC1D,sBAAgB,UAAU,OAAO,oBAAoB,WAAW;AAAA,IAClE;AACA,iBAAa,UAAU,OAAO,oBAAoB,KAAK;AACvD,iBAAa,UAAU,OAAO,oBAAoB,WAAW;AAC7D,iBAAa,UAAU,OAAO,oBAAoB,KAAK;AACvD,iBAAa,UAAU,OAAO,oBAAoB,WAAW;AAAA,EAC/D;AAAA,EACA,uBAAuB;AACrB,UAAM,kBAAkB,KAAK,WAAW;AACxC,QAAI,iBAAiB;AACnB,UAAI,SAAS,KAAK,OAAO,QAAQ,GAAG;AAClC,aAAK,OAAO,SAAS,iBAAiB,WAAW,KAAK,OAAO,QAAQ;AAAA,MACvE;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,UAAM,kBAAkB,KAAK,WAAW;AACxC,QAAI,iBAAiB;AACnB,UAAI,KAAK,cAAc;AACrB,cAAM,SAAS,KAAK;AACpB,eAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AACjC,eAAK,OAAO,YAAY,iBAAiB,GAAG;AAAA,QAC9C,CAAC;AACD,aAAK,eAAe;AAAA,MACtB;AACA,WAAK,qBAAqB;AAC1B,UAAI,OAAO,KAAK,OAAO,gBAAgB,YAAY,OAAO,KAAK,KAAK,OAAO,WAAW,EAAE,QAAQ;AAC9F,cAAM,SAAS,mBACV,KAAK,OAAO;AAEjB,eAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AACjC,eAAK,OAAO,SAAS,iBAAiB,KAAK,OAAO,GAAG,CAAC;AAAA,QACxD,CAAC;AACD,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,UAAM,kBAAkB,KAAK,WAAW;AACxC,QAAI,iBAAiB;AACnB,UAAI,KAAK,UAAU;AACjB,wBAAgB,UAAU,IAAI,qBAAqB;AAAA,MACrD,OAAO;AACL,wBAAgB,UAAU,OAAO,qBAAqB;AAAA,MACxD;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB,OAAO;AACrB,QAAI,MAAM,YAAY,SAAS;AAC7B,WAAK,UAAU;AAAA,IACjB,WAAW,MAAM,YAAY,QAAQ;AACnC,WAAK,aAAa;AAAA,IACpB;AACA,SAAK,oBAAoB;AACzB,SAAK,sBAAsB,KAAK,KAAK;AAAA,EACvC;AAAA,EACA,iBAAiB,OAAO;AACtB,QAAI,MAAM,YAAY,SAAS;AAC7B,WAAK,uBAAuB;AAC5B,WAAK,kBAAkB;AAAA,IACzB,WAAW,MAAM,YAAY,QAAQ;AACnC,WAAK,sBAAsB;AAAA,IAC7B;AACA,SAAK,sBAAsB,KAAK,KAAK;AAAA,EACvC;AAAA,EACA,qBAAqB;AACnB,SAAK,QAAQ;AACb,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,cAAc;AACZ,SAAK,0BAA0B,IAAI;AACnC,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,oBAAoB,gBAAgB;AAClC,SAAK,OAAO,kBAAkB,MAAM;AAClC,gBAAU,KAAK,KAAK,eAAe,SAAS,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC3F,YAAI,KAAK,WAAW;AAClB,qBAAW,MAAM;AACf,iBAAK,YAAY;AAAA,UACnB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,gBAAU,eAAe,eAAe,WAAW,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAClG,aAAK,YAAY;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,GAAG;AAC1D,MAAG,iBAAiB;AAAA,IACtB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,UAAU,CAAI,0BAA0B;AAAA,IAC1C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,mCAAN,MAAM,0CAAyC,4BAA4B;AAAA,EACzE,YAAY,QAAQ,MAAM,MAAM,kBAAkB,KAAK,QAAQ,YAAY,iBAAiB,QAAQ,UAAU,eAAe;AAC3H,UAAM,QAAQ,MAAM,kBAAkB,KAAK,QAAQ,YAAY,iBAAiB,QAAQ,UAAU,aAAa;AAC/G,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,KAAK,aAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACpE,WAAK,SAAS,KAAK,KAAK,cAAc,OAAO;AAAA,IAC/C,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,SAAK,oBAAoB,KAAK,eAAe;AAAA,EAC/C;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB,KAAK;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,YAAY,KAAK;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yCAAyC,GAAG;AAC/D,aAAO,KAAK,KAAK,mCAAqC,kBAAqB,MAAM,GAAM,kBAAuB,aAAa,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,SAAS,GAAM,kBAAuB,UAAU,GAAM,kBAAuB,eAAe,GAAM,kBAAkB,YAAY,GAAM,kBAAkB,UAAU,CAAC,GAAM,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,IACxe;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,4BAA4B,CAAC;AAAA,MAC1C,WAAW,SAAS,uCAAuC,IAAI,KAAK;AAClE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,iBAAiB,CAAC;AACjC,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AAAA,QACxE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,YAAY,MAAM,QAAQ,QAAQ;AAAA,MAC9C,UAAU;AAAA,MACV,cAAc,SAAS,8CAA8C,IAAI,KAAK;AAC5E,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,yBAAyB,SAAS,mFAAmF,QAAQ;AACtJ,mBAAO,IAAI,iBAAiB,MAAM;AAAA,UACpC,CAAC,EAAE,wBAAwB,SAAS,kFAAkF,QAAQ;AAC5H,mBAAO,IAAI,gBAAgB,MAAM;AAAA,UACnC,CAAC;AACD,UAAG,WAAW,SAAS,SAAS,0DAA0D,QAAQ;AAChG,mBAAO,IAAI,iBAAiB,MAAM;AAAA,UACpC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,cAAc,IAAI,OAAO,aAAa,EAAE,mBAAmB,IAAI,KAAK;AAC/F,UAAG,WAAW,IAAI,OAAO,kBAAkB,oBAAoB,IAAI,OAAO,kBAAkB,gBAAgB;AAC5G,UAAG,YAAY,WAAW,IAAI,OAAO,QAAQ;AAC7C,UAAG,YAAY,sBAAsB,IAAI,QAAQ,KAAK,EAAE,sBAAsB,IAAI,OAAO,UAAU;AAAA,QACrG;AAAA,MACF;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,QACjB,aAAa;AAAA,MACf;AAAA,MACA,UAAU,CAAC,yBAAyB;AAAA,MACpC,YAAY;AAAA,MACZ,UAAU,CAAI,4BAA+B,mBAAmB;AAAA,MAChE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,QAAQ,YAAY,GAAG,aAAa,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC,GAAG,kBAAkB,GAAG,SAAS,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,WAAW,IAAI,GAAG,QAAQ,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,aAAa,IAAI,GAAG,aAAa,UAAU,GAAG,CAAC,aAAa,IAAI,GAAG,UAAU,aAAa,YAAY,UAAU,GAAG,CAAC,kBAAkB,IAAI,GAAG,OAAO,GAAG,CAAC,aAAa,IAAI,GAAG,SAAS,aAAa,UAAU,GAAG,CAAC,aAAa,IAAI,GAAG,SAAS,UAAU,aAAa,YAAY,UAAU,CAAC;AAAA,MAChuB,UAAU,SAAS,0CAA0C,IAAI,KAAK;AACpE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,UAAG,OAAO,GAAG,aAAa;AAC1B,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,UAAU,CAAC;AAC3F,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACvD,UAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,UAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,UAAG,WAAW,IAAI,2DAA2D,GAAG,GAAG,gBAAgB,CAAC;AACpG,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,OAAO,EAAE;AAC/B,UAAG,WAAW,IAAI,0DAA0D,GAAG,GAAG,eAAe,EAAE,EAAE,IAAI,0DAA0D,GAAG,GAAG,OAAO,EAAE;AAClL,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,IAAI,OAAO,EAAE;AAC/B,UAAG,WAAW,IAAI,0DAA0D,GAAG,GAAG,UAAU,EAAE,EAAE,IAAI,0DAA0D,GAAG,GAAG,UAAU,EAAE;AAChL,UAAG,aAAa,EAAE,EAAE,EAAE,EAAE;AAAA,QAC1B;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,SAAY,YAAY,GAAG,IAAI,IAAI,UAAU,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC;AAC7F,UAAG,WAAW,WAAW,IAAI,OAAO,WAAW,EAAE,WAAW,IAAI,OAAO,OAAO;AAC9E,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,GAAG,IAAI,OAAO,aAAa,IAAI,EAAE;AAClD,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,OAAO,WAAW;AAC/C,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,UAAU,IAAI,OAAO,UAAU;AAC7C,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,0BAA0B,IAAI,OAAO,OAAO;AAC1D,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,IAAI,kBAAkB,KAAK,EAAE;AAClD,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,IAAI,OAAO,iBAAiB,OAAO,KAAK,EAAE;AAC/D,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,IAAI,OAAO,aAAa,OAAO,KAAK,EAAE;AAAA,QAC7D;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,SAAS,eAAkB,iBAAiB,cAAiB,iBAAiB,uBAAuB,gBAAmB,iCAAiC,cAAiB,iBAAiB,gBAAoB,mBAAuB,4BAAiC,eAAe;AAAA,MAC9S,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,kBAAkB,cAAc;AAAA,MAC9C;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA6DV,YAAY,CAAC,kBAAkB,cAAc;AAAA;AAAA,MAE7C,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,UAAU;AAAA,QACV,MAAM;AAAA,QACN,WAAW;AAAA,QACX,8BAA8B;AAAA,QAC9B,8BAA8B;AAAA,QAC9B,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,2BAA2B;AAAA,QAC3B,0BAA0B;AAAA,QAC1B,WAAW;AAAA,MACb;AAAA,MACA,SAAS,CAAC,SAAS,SAAS,eAAe,cAAc,uBAAuB,gBAAgB,cAAc,cAAc;AAAA,MAC5H,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,YAAY,MAAM,QAAQ;AACxB,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,gBAAgB;AACrB,SAAK,UAAU,CAAC;AAChB,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,WAAW,IAAI,QAAQ;AAC5B,QAAI,MAAM,QAAQ,OAAO,QAAQ,GAAG;AAClC,WAAK,gBAAgB;AACrB,WAAK,UAAU,OAAO,SAAS,IAAI,kBAAkB;AAAA,IACvD;AACA,SAAK,KAAK,aAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACpE,WAAK,SAAS,KAAK,KAAK,cAAc,OAAO;AAAA,IAC/C,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB,KAAK;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,YAAY,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,SAAS,MAAM;AACnC,UAAM,QAAQ,QAAQ,IAAI;AAC1B,UAAM,oBAAoB,KAAK,SAAS,oBAAoB;AAC5D,WAAO,OAAO,UAAU,aAAa,MAAM,MAAM,SAAS,qBAAqB,CAAC,iBAAiB,CAAC,IAAI;AAAA,EACxG;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,SAAS;AACrB,UAAM,UAAU,KAAK,sBAAsB,SAAS,SAAS;AAC7D,QAAI,CAAC,SAAS;AACZ,YAAM,SAAS,KAAK,sBAAsB,SAAS,SAAS;AAC5D,UAAI,QAAQ,eAAe,UAAU,MAAM,GAAG;AAC5C,gBAAQ,UAAU;AAClB,eAAO,KAAK,MAAM,QAAQ,UAAU,KAAK,EAAE,MAAM,OAAK;AACpD,kBAAQ,UAAU;AAClB,gBAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAA2B,kBAAuB,aAAa,GAAM,kBAAkB,YAAY,CAAC;AAAA,IACvH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,OAAO,mBAAmB,EAAE,CAAC;AAAA,MAC1C,WAAW,CAAC,GAAG,kBAAkB;AAAA,MACjC,QAAQ;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,QACjB,aAAa;AAAA,MACf;AAAA,MACA,UAAU,CAAC,sBAAsB;AAAA,MACjC,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,0BAA0B,+BAA+B,GAAG,CAAC,aAAa,IAAI,GAAG,UAAU,aAAa,YAAY,UAAU,YAAY,WAAW,UAAU,SAAS,GAAG,CAAC,aAAa,IAAI,GAAG,SAAS,UAAU,aAAa,YAAY,UAAU,YAAY,WAAW,UAAU,SAAS,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,aAAa,IAAI,GAAG,aAAa,UAAU,GAAG,CAAC,aAAa,IAAI,GAAG,UAAU,YAAY,aAAa,UAAU,GAAG,CAAC,aAAa,IAAI,GAAG,SAAS,aAAa,UAAU,GAAG,CAAC,aAAa,IAAI,GAAG,SAAS,UAAU,YAAY,aAAa,UAAU,CAAC;AAAA,MAC1jB,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,cAAc,EAAE,GAAG,+CAA+C,GAAG,CAAC;AAAA,QAC9I;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,GAAG,IAAI,OAAO,WAAW,IAAI,CAAC;AAAA,QACjD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,iCAAiC,gBAAoB,mBAAuB,4BAAiC,eAAe;AAAA,MAC9J,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqDV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,gBAAgB,cAAc;AAAA,MACxC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,mBAAmB,SAAS;AACnC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,KACP;AAEP;AAMA,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,QAAQ;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAA0B,kBAAkB,YAAY,CAAC;AAAA,IAC5E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,OAAO,kBAAkB,EAAE,CAAC;AAAA,MACzC,WAAW,CAAC,GAAG,kBAAkB;AAAA,MACjC,UAAU,CAAC,qBAAqB;AAAA,MAChC,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,WAAW,CAAC;AAAA,MAChF,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,gBAAgB,CAAC;AACvF,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,0BAA0B,IAAI,OAAO,OAAO;AAAA,QAC5D;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,+BAA+B;AAAA,MACjE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,cAAc;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,4BAAN,MAAM,mCAAkC,4BAA4B;AAAA,EAClE,YAAY,QAAQ,MAAM,kBAAkB,KAAK,QAAQ,YAAY,iBAAiB,QAAQ,UAAU,eAAe;AACrH,UAAM,QAAQ,MAAM,kBAAkB,KAAK,QAAQ,YAAY,iBAAiB,QAAQ,UAAU,aAAa;AAC/G,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,WAAW;AACT,SAAK,oBAAoB,KAAK,eAAe;AAAA,EAC/C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,GAAG;AACxD,aAAO,KAAK,KAAK,4BAA8B,kBAAqB,MAAM,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,SAAS,GAAM,kBAAuB,UAAU,GAAM,kBAAuB,eAAe,GAAM,kBAAkB,YAAY,GAAM,kBAAkB,UAAU,CAAC,GAAM,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,IACvb;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,MAClC,WAAW,SAAS,gCAAgC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,iBAAiB,CAAC;AACjC,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AAAA,QACxE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,YAAY,MAAM,QAAQ,QAAQ;AAAA,MAC9C,UAAU;AAAA,MACV,cAAc,SAAS,uCAAuC,IAAI,KAAK;AACrE,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,yBAAyB,SAAS,4EAA4E,QAAQ;AAC/I,mBAAO,IAAI,iBAAiB,MAAM;AAAA,UACpC,CAAC,EAAE,wBAAwB,SAAS,2EAA2E,QAAQ;AACrH,mBAAO,IAAI,gBAAgB,MAAM;AAAA,UACnC,CAAC;AACD,UAAG,WAAW,SAAS,SAAS,mDAAmD,QAAQ;AACzF,mBAAO,IAAI,iBAAiB,MAAM;AAAA,UACpC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,cAAc,IAAI,OAAO,aAAa,EAAE,mBAAmB,IAAI,KAAK;AAC/F,UAAG,WAAW,IAAI,OAAO,kBAAkB,oBAAoB,IAAI,OAAO,kBAAkB,gBAAgB;AAC5G,UAAG,YAAY,WAAW,IAAI,OAAO,QAAQ;AAC7C,UAAG,YAAY,sBAAsB,IAAI,QAAQ,KAAK,EAAE,sBAAsB,IAAI,OAAO,UAAU;AAAA,QACrG;AAAA,MACF;AAAA,MACA,UAAU,CAAC,kBAAkB;AAAA,MAC7B,YAAY;AAAA,MACZ,UAAU,CAAI,4BAA+B,mBAAmB;AAAA,MAChE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,WAAW,IAAI,mBAAmB,0BAA0B,QAAQ,YAAY,GAAG,aAAa,GAAG,mBAAmB,WAAW,SAAS,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC,kBAAkB,IAAI,iBAAiB,IAAI,GAAG,QAAQ,GAAG,CAAC,GAAG,kBAAkB,GAAG,SAAS,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,mBAAmB,IAAI,GAAG,UAAU,GAAG,CAAC,kBAAkB,IAAI,GAAG,OAAO,GAAG,CAAC,kBAAkB,IAAI,iBAAiB,EAAE,GAAG,CAAC,mBAAmB,IAAI,GAAG,mBAAmB,eAAe,UAAU,CAAC;AAAA,MAC1iB,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,UAAG,OAAO,GAAG,aAAa;AAC1B,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,UAAU,CAAC,EAAE,GAAG,kDAAkD,GAAG,GAAG,OAAO,CAAC;AACzJ,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,kDAAkD,GAAG,GAAG,OAAO,CAAC;AAC9J,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,OAAO,CAAC;AACjF,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,SAAY,YAAY,GAAG,IAAI,IAAI,UAAU,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC;AAC7F,UAAG,WAAW,mBAAmB,CAAC,IAAI,OAAO,WAAW,EAAE,WAAW,IAAI,OAAO,WAAW,EAAE,WAAW,IAAI,OAAO,OAAO;AAC1H,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,GAAG,IAAI,OAAO,aAAa,IAAI,EAAE;AAClD,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,OAAO,UAAU,IAAI,EAAE;AAC/C,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,OAAO,WAAW;AAC/C,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,GAAG,IAAI,kBAAkB,IAAI,EAAE;AAChD,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,OAAO,aAAa,OAAO,IAAI,EAAE;AAAA,QAC3D;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,SAAS,uBAAuB,uBAAuB,cAAiB,iBAAiB,wBAAwB,eAAkB,iBAAiB,SAAS,aAAa;AAAA,MAClM,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,kBAAkB,cAAc;AAAA,MAC9C;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqCV,YAAY,CAAC,kBAAkB,cAAc;AAAA;AAAA,MAE7C,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,UAAU;AAAA,QACV,MAAM;AAAA,QACN,WAAW;AAAA,QACX,8BAA8B;AAAA,QAC9B,8BAA8B;AAAA,QAC9B,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,2BAA2B;AAAA,QAC3B,0BAA0B;AAAA,QAC1B,WAAW;AAAA,MACb;AAAA,MACA,SAAS,CAAC,SAAS,SAAS,uBAAuB,uBAAuB,cAAc,wBAAwB,eAAe,SAAS,aAAa;AAAA,MACrJ,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,YAAY,QAAQ,mBAAmB;AACjD,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AACzB,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,WAAW,IAAI,QAAQ;AAC5B,sBAAkB,sBAAsB,KAAK,OAAO,WAAS,MAAM,cAAc,UAAU,MAAM,YAAY,OAAO,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AAC9I,WAAK,UAAU,KAAK;AACpB,WAAK,UAAU,SAAS;AACxB,UAAI,OAAO,uBAAuB,cAAc;AAC9C,eAAO,YAAY,KAAK;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,sBAAkB,sBAAsB,KAAK,OAAO,WAAS,MAAM,cAAc,UAAU,MAAM,YAAY,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AAC7I,mBAAa,KAAK,YAAY;AAC9B,WAAK,mBAAmB;AAAA,IAC1B,CAAC;AACD,sBAAkB,eAAe,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC9E,YAAM,aAAa,CAAC,KAAK,OAAO,mBAAmB,CAAC,KAAK,OAAO;AAChE,UAAI,YAAY;AACd,aAAK;AAAA,UAAQ;AAAA;AAAA,QAAqC;AAAA,MACpD;AAAA,IACF,CAAC;AACD,eAAW,cAAc,EAAE,KAAK,OAAO,WAAS,KAAK,OAAO,cAAc,CAAC,KAAK,OAAO,mBAAmB,CAAC,KAAK,OAAO,eAAe,MAAM,YAAY,UAAU,CAAC,eAAe,KAAK,CAAC,CAAC,EAAE,UAAU,WAAS;AAC5M,YAAM,eAAe;AACrB,WAAK;AAAA,QAAQ;AAAA;AAAA,MAAqC;AAAA,IACpD,CAAC;AACD,sBAAkB,gBAAgB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK;AAAA,MAAQ;AAAA;AAAA,IAAqC,CAAC;AACpI,sBAAkB,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK;AAAA,MAAQ;AAAA;AAAA,IAA6B,CAAC;AACxH,eAAW,YAAY,EAAE,UAAU,MAAM;AACvC,WAAK,WAAW,KAAK,KAAK,MAAM;AAChC,WAAK,WAAW,SAAS;AACzB,UAAI,OAAO,wBAAwB,cAAc;AAC/C,eAAO,aAAa,KAAK,KAAK,MAAM;AAAA,MACtC;AACA,WAAK,oBAAoB;AACzB,WAAK,eAAe;AACpB,WAAK,WAAW,QAAQ;AAAA,IAC1B,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,yBAAyB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa;AACX,WAAO,KAAK,kBAAkB,iBAAiB;AAAA,EACjD;AAAA,EACA,QAAQ,QAAQ;AACd,SAAK,MAAM,MAAM;AAAA,EACnB;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,MAAQ;AAAA;AAAA,IAA6B;AAAA,EACnD;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK;AAAA,MAAQ;AAAA;AAAA,IAAqC;AAAA,EAC3D;AAAA,EACA,MAAM,QAAQ;AACZ,QAAI,KAAK,UAAU,GAA2B;AAC5C;AAAA,IACF;AACA,SAAK,SAAS;AACd,SAAK,kBAAkB,sBAAsB,KAAK,OAAO,WAAS,MAAM,cAAc,OAAO,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,WAAS;AAC1H,WAAK,WAAW,eAAe;AAC/B,WAAK,eAAe,WAAW,MAAM;AACnC,aAAK,mBAAmB;AAAA,MAC1B,GAAG,MAAM,YAAY,GAAG;AAAA,IAC1B,CAAC;AACD,SAAK,kBAAkB,mBAAmB;AAC1C,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,aAAa,QAAQ;AACnB,WAAO,OAAO,KAAK,QAAQ,MAAM;AACjC,SAAK,kBAAkB,kBAAkB;AACzC,SAAK,kBAAkB,IAAI,aAAa;AAAA,EAC1C;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACM,QAAQ,QAAQ;AAAA;AACpB,UAAI,KAAK,UAAU,GAA8B;AAC/C;AAAA,MACF;AACA,YAAMC,WAAU;AAAA,QACd,IAAI,KAAK,OAAO;AAAA,QAChB,QAAQ,KAAK,OAAO;AAAA,MACtB,EAAE,MAAM;AACR,YAAM,aAAa;AAAA,QACjB,IAAI;AAAA,QACJ,QAAQ;AAAA,MACV,EAAE,MAAM;AACR,YAAM,UAAU,KAAK,OAAO,UAAU;AACtC,UAAI,SAAS;AACX;AAAA,MACF;AACA,UAAIA,oBAAmB,cAAc;AACnC,QAAAA,SAAQ,KAAK,KAAK,oBAAoB,CAAC;AAAA,MACzC,WAAW,OAAOA,aAAY,YAAY;AACxC,cAAM,SAASA,SAAQ,KAAK,oBAAoB,CAAC;AACjD,YAAI,UAAU,MAAM,GAAG;AACrB,eAAK,OAAO,UAAU,IAAI;AAC1B,cAAI,UAAU;AACd,cAAI;AACF,sBAAU,MAAM;AAAA,UAClB,UAAE;AACA,iBAAK,OAAO,UAAU,IAAI;AAC1B,iBAAK,gBAAgB,OAAO;AAAA,UAC9B;AAAA,QACF,OAAO;AACL,eAAK,gBAAgB,MAAM;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA;AAAA,EACA,gBAAgB,QAAQ;AACtB,QAAI,WAAW,OAAO;AACpB,WAAK,MAAM,MAAM;AAAA,IACnB;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,QAAQ;AACb,SAAK,WAAW,QAAQ;AACxB,SAAK,SAAS,KAAK;AAAA,EACrB;AACF;AACA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,IAAI,aAAa;AACf,WAAO,KAAK,cAAc,KAAK,YAAY,aAAa,KAAK;AAAA,EAC/D;AAAA,EACA,IAAI,kBAAkB;AACpB,UAAM,SAAS,KAAK;AACpB,WAAO,SAAS,OAAO,kBAAkB,KAAK;AAAA,EAChD;AAAA,EACA,YAAY,SAAS,UAAU,iBAAiB,aAAa,gBAAgB;AAC3E,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,iBAAiB;AACtB,SAAK,wBAAwB,CAAC;AAC9B,SAAK,4BAA4B,IAAI,QAAQ;AAC7C,SAAK,gBAAgB,MAAM,MAAM,KAAK,WAAW,SAAS,KAAK,kBAAkB,KAAK,gBAAgB,KAAK,UAAU,MAAS,CAAC,CAAC;AAAA,EAClI;AAAA,EACA,OAAO,QAAQ;AACb,WAAO,KAAK,KAAK,OAAO,WAAW,MAAM;AAAA,EAC3C;AAAA,EACA,WAAW;AACT,SAAK,YAAY,KAAK,UAAU;AAAA,EAClC;AAAA,EACA,QAAQ,UAAU,CAAC,GAAG,cAAc,WAAW;AAC7C,QAAI,cAAc,SAAS;AACzB,WAAK,8EAA8E;AAAA,IACrF;AACA,QAAI,EAAE,aAAa,UAAU;AAC3B,cAAQ,UAAU;AAAA,IACpB;AACA,QAAI,EAAE,oBAAoB,UAAU;AAClC,cAAQ,iBAAiB;AAAA,IAC3B;AACA,YAAQ,cAAc;AACtB,YAAQ,cAAc,uCAAuC,WAAW,IAAI,QAAQ,eAAe,EAAE;AACrG,WAAO,KAAK,OAAO,OAAO;AAAA,EAC5B;AAAA,EACA,KAAK,UAAU,CAAC,GAAG;AACjB,WAAO,KAAK,eAAe,SAAS,MAAM;AAAA,EAC5C;AAAA,EACA,QAAQ,UAAU,CAAC,GAAG;AACpB,WAAO,KAAK,eAAe,SAAS,SAAS;AAAA,EAC/C;AAAA,EACA,MAAM,UAAU,CAAC,GAAG;AAClB,WAAO,KAAK,eAAe,SAAS,OAAO;AAAA,EAC7C;AAAA,EACA,QAAQ,UAAU,CAAC,GAAG;AACpB,WAAO,KAAK,eAAe,SAAS,SAAS;AAAA,EAC/C;AAAA,EACA,KAAK,wBAAwB,QAAQ;AACnC,UAAM,eAAe,oBAAoB,UAAU,CAAC,GAAG,IAAI,aAAa,CAAC;AACzE,UAAM,aAAa,KAAK,cAAc,YAAY;AAClD,UAAM,iBAAiB,KAAK,qBAAqB,YAAY,YAAY;AACzE,UAAM,WAAW,KAAK,mBAAmB,wBAAwB,gBAAgB,YAAY,YAAY;AACzG,mBAAe,WAAW;AAC1B,wBAAoB,YAAY,QAAQ,QAAQ;AAChD,SAAK,WAAW,KAAK,QAAQ;AAC7B,aAAS,WAAW,UAAU,MAAM,KAAK,gBAAgB,QAAQ,CAAC;AAClE,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,UAAU;AACxB,UAAM,QAAQ,KAAK,WAAW,QAAQ,QAAQ;AAC9C,QAAI,QAAQ,IAAI;AACd,WAAK,WAAW,OAAO,OAAO,CAAC;AAC/B,UAAI,CAAC,KAAK,WAAW,QAAQ;AAC3B,aAAK,gBAAgB,KAAK;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,IAAI,QAAQ;AAChB,WAAO,KAAK;AACV,cAAQ,CAAC,EAAE,MAAM;AACjB,UAAI,CAAC,KAAK,WAAW,QAAQ;AAC3B,aAAK,gBAAgB,KAAK;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,eAAe,KAAK,gBAAgB,sBAAsB,qBAAqB,KAAK,CAAC;AAC3F,UAAM,gBAAgB,IAAI,cAAc;AAAA,MACtC,aAAa;AAAA,MACb,gBAAgB,KAAK,QAAQ,iBAAiB,MAAM;AAAA,MACpD,kBAAkB,KAAK,QAAQ,SAAS,EAAE,OAAO;AAAA,MACjD,qBAAqB,mBAAmB,OAAO,qBAAqB,aAAa,qBAAqB,IAAI;AAAA,MAC1G,WAAW,mBAAmB,OAAO,aAAa,aAAa,aAAa,KAAK,eAAe,KAAK;AAAA,IACvG,CAAC;AACD,QAAI,mBAAmB,OAAO,QAAQ,aAAa,QAAQ,IAAI,GAAG;AAChE,oBAAc,gBAAgB;AAAA,IAChC;AACA,WAAO,KAAK,QAAQ,OAAO,aAAa;AAAA,EAC1C;AAAA,EACA,qBAAqB,YAAY,QAAQ;AACvC,UAAM,eAAe,UAAU,OAAO,sBAAsB,OAAO,mBAAmB;AACtF,UAAM,WAAW,SAAS,OAAO;AAAA,MAC/B,QAAQ,gBAAgB,KAAK;AAAA,MAC7B,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AACD,UAAM,qBAAqB,OAAO,gBAAgB;AAAA;AAAA,MAElD;AAAA;AAAA;AAAA,MAEA;AAAA;AACA,UAAM,kBAAkB,IAAI,gBAAgB,oBAAoB,OAAO,oBAAoB,QAAQ;AACnG,UAAM,eAAe,WAAW,OAAO,eAAe;AACtD,WAAO,aAAa;AAAA,EACtB;AAAA,EACA,mBAAmB,wBAAwB,gBAAgB,YAAY,QAAQ;AAC7E,UAAM,WAAW,IAAI,WAAW,YAAY,QAAQ,cAAc;AAClE,QAAI,kCAAkC,aAAa;AACjD,qBAAe,qBAAqB,IAAI,eAAe,wBAAwB,MAAM;AAAA,QACnF,WAAW,OAAO;AAAA,QAClB;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,WAAW,SAAS,sBAAsB,KAAK,OAAO,2BAA2B,UAAU;AACzF,YAAM,WAAW,KAAK,eAAe,UAAU,MAAM;AACrD,YAAM,aAAa,eAAe,sBAAsB,IAAI,gBAAgB,wBAAwB,OAAO,oBAAoB,QAAQ,CAAC;AACxI,eAAS,eAAe;AACxB,eAAS,oBAAoB,WAAW;AAAA,IAC1C,OAAO;AACL,qBAAe,oBAAoB;AAAA,IACrC;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe,UAAU,QAAQ;AAC/B,UAAM,eAAe,UAAU,OAAO,sBAAsB,OAAO,mBAAmB;AACtF,WAAO,SAAS,OAAO;AAAA,MACrB,QAAQ,gBAAgB,KAAK;AAAA,MAC7B,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU,OAAO;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,eAAe,UAAU,CAAC,GAAG,aAAa;AACxC,UAAM,UAAU;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AACA,QAAI,EAAE,gBAAgB,UAAU;AAC9B,cAAQ,aAAa,QAAQ,WAAW;AAAA,IAC1C;AACA,QAAI,EAAE,kBAAkB,UAAU;AAEhC,cAAQ,eAAe;AAAA,IACzB;AACA,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA,EAC1C;AAAA,EACA,cAAc;AACZ,SAAK,YAAY,KAAK,qBAAqB;AAC3C,SAAK,0BAA0B,SAAS;AAAA,EAC1C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAmB,SAAc,OAAO,GAAM,SAAY,QAAQ,GAAM,SAAc,eAAe,GAAM,SAAS,iBAAgB,EAAE,GAAM,SAAc,gBAAgB,CAAC,CAAC;AAAA,IAC/L;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAA4B,kBAAqB,WAAW,CAAC;AAAA,IAChF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,MACtC,UAAU,CAAC,gBAAgB;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,YAAY,YAAY,aAAa;AACnC,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,aAAa;AAAA,QAC3B,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAA2B,kBAAkB,YAAY,CAAC,GAAM,kBAAqB,WAAW,CAAC;AAAA,IACpH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACrC,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,YAAY,aAAa;AACnC,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,aAAa;AAAA,QAC3B,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAA0B,kBAAkB,YAAY,CAAC,GAAM,kBAAqB,WAAW,CAAC;AAAA,IACnH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,MACpC,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,IAAI,WAAW,OAAO;AACpB,QAAI,OAAO;AACT,WAAK,qBAAqB,KAAK;AAAA,IACjC;AAAA,EACF;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,QAAI,OAAO;AACT,WAAK,sBAAsB,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EACA,IAAI,YAAY;AAEd,WAAO,KAAK,YAAY,aAAa;AAAA,EACvC;AAAA,EACA,IAAI,aAAa;AAEf,WAAO,KAAK,aAAa,aAAa;AAAA,EACxC;AAAA,EACA,YAAY,KAAK,OAAO,kBAAkB;AACxC,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,mBAAmB;AACxB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,cAAc;AAEnB,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,WAAW;AAChB,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,OAAO;AACL,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY;AACjB,WAAK,gBAAgB,KAAK,IAAI;AAAA,IAChC;AACA,QAAI,CAAC,KAAK,UAAU;AAClB,YAAM,SAAS,KAAK,UAAU;AAC9B,WAAK,WAAW,KAAK,MAAM,OAAO,MAAM;AAExC,WAAK,SAAS,WAAW,aAAa,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACrF,aAAK,MAAM;AAAA,MACb,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,MAAM,QAAQ;AACZ,QAAI,KAAK,WAAW;AAClB,WAAK,YAAY;AACjB,WAAK,gBAAgB,KAAK,KAAK;AAAA,IACjC;AACA,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,MAAM,MAAM;AAC1B,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,QAAQ,QAAQ;AACd,SAAK,MAAM,MAAM;AAAA,EACnB;AAAA,EACA,YAAY;AACV,SAAK,UAAU,UAAU;AAAA,EAC3B;AAAA,EACA,gBAAgB;AACd,SAAK,UAAU,cAAc;AAAA,EAC/B;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,UAAU,oBAAoB;AAAA,EAC5C;AAAA,EACA,aAAa;AACX,WAAO,KAAK,UAAU,WAAW;AAAA,EACnC;AAAA,EACA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,qBAAqB,aAAa;AAChC,SAAK,UAAU;AACf,QAAI,KAAK,UAAU;AAEjB,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,aAAK,SAAS,aAAa;AAAA,UACzB,SAAS,KAAK;AAAA,QAChB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,sBAAsB,aAAa;AACjC,SAAK,WAAW;AAChB,QAAI,KAAK,UAAU;AAEjB,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,aAAK,SAAS,aAAa;AAAA,UACzB,UAAU,KAAK;AAAA,QACjB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,YAAY;AACV,UAAM,kBAAkB,uBAAuB,IAAI;AACnD,oBAAgB,qBAAqB,KAAK;AAC1C,oBAAgB,YAAY,KAAK,aAAa,KAAK;AACnD,WAAO;AAAA,EACT;AAAA,EACA,YAAY,SAAS;AACnB,UAGI,cAFF;AAAA;AAAA,IAvjEN,IAyjEQ,IADC,yBACD,IADC;AAAA,MADH;AAAA;AAGF,QAAI,OAAO,KAAK,YAAY,EAAE,UAAU,KAAK,UAAU;AACrD,WAAK,SAAS,aAAa,uBAAuB,IAAI,CAAC;AAAA,IACzD;AACA,QAAI,WAAW;AACb,UAAI,KAAK,WAAW;AAClB,aAAK,KAAK;AAAA,MACZ,OAAO;AACL,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,mBAAmB;AAClC,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAqB,kBAAqB,iBAAiB,GAAM,kBAAkB,cAAc,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,IAChK;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,MACxB,gBAAgB,SAAS,gCAAgC,IAAI,KAAK,UAAU;AAC1E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,uBAAuB,GAAG,WAAW;AACjE,UAAG,eAAe,UAAU,yBAAyB,GAAG,WAAW;AACnE,UAAG,eAAe,UAAU,wBAAwB,GAAG,WAAW;AAAA,QACpE;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,0BAA0B,GAAG;AAC9E,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,QACpE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA,QACd,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,QACjB,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,QACV,UAAU;AAAA,QACV,SAAS;AAAA,QACT,iBAAiB;AAAA,QACjB,aAAa;AAAA,QACb,SAAS;AAAA,QACT,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,QACb,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,YAAY;AAAA,MACd;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA,QACd,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,0BAA0B,IAAI,KAAK;AAAA,MAAC;AAAA,MACvD,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,UAAU,MAAM;AACzE,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,kBAAkB,MAAM;AACjF,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,uBAAuB,MAAM;AACtF,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,aAAa,MAAM;AAC5E,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,cAAc,MAAM;AAC7E,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,eAAe,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,gBAAgB,MAAM;AAC/E,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,oBAAoB,MAAM;AACnF,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,mBAAmB,MAAM;AAClF,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,cAAc,MAAM;AAC7E,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,iBAAiB,MAAM;AAChF,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,cAAc,MAAM;AAC7E,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,eAAe,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,cAAc,MAAM;AAAA,CAC5E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,QAC5B,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,QAC9B,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,QAC7B,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,kBAAkB,wBAAwB,yBAAyB,uBAAuB,wBAAwB,uBAAuB,uBAAuB,2BAA2B,gCAAgC;AAAA,MACrO,SAAS,CAAC,kBAAkB,wBAAwB,yBAAyB,qBAAqB;AAAA,IACpG,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,cAAc;AAAA,MAC1B,SAAS,CAAC,uBAAuB,wBAAwB,uBAAuB,2BAA2B,gCAAgC;AAAA,IAC7I,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB,wBAAwB,yBAAyB,uBAAuB,wBAAwB,uBAAuB,uBAAuB,2BAA2B,gCAAgC;AAAA,MACrO,SAAS,CAAC,kBAAkB,wBAAwB,yBAAyB,qBAAqB;AAAA,MAClG,WAAW,CAAC,cAAc;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,mBAAN,MAAuB;AAAC;", "names": ["importantProperties", "AutoScrollVerticalDirection", "AutoScrollHorizontalDirection", "isTouchEvent", "trigger"]}