<div class="min-h-screen p-6 transition-[var(--transition-default)]"
     [ngClass]="{
       'bg-[var(--background-light-gray)] text-[var(--text-dark)]': !themeService.isDarkMode(),
       'bg-[var(--background-white)] text-[var(--text-dark)]': themeService.isDarkMode()
     }">

  <!-- Header -->
  <div class="flex items-center gap-3 mb-8">
    <div class="w-1 h-8 bg-gradient-to-b from-[var(--primary-purple)] to-[var(--secondary-purple)] rounded-full"></div>
    <div>
      <h1 class="text-2xl font-semibold text-[var(--text-dark)]">Project Summary</h1>
      <p class="text-sm text-[var(--text-medium-gray)] mt-1">Overview of all project details and progress</p>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="flex flex-col justify-center items-center py-16">
    <div class="relative">
      <div class="w-12 h-12 border-4 border-[var(--hover-blue-gray)] border-t-[var(--primary-purple)] rounded-full animate-spin"></div>
    </div>
    <p class="mt-4 text-sm text-[var(--text-medium-gray)] font-medium">Loading projects...</p>
  </div>

  <!-- Project Cards -->
  <div *ngIf="!isLoading" class="space-y-6">
    <div *ngFor="let project of projects; trackBy: trackByProject"
      class="group bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] shadow-[var(--box-shadow)] hover:shadow-[0_8px_25px_rgba(0,0,0,0.15)] transition-[var(--transition-default)] overflow-hidden">

      <!-- Project Header -->
      <div class="relative p-6 bg-[var(--background-white)] border-b border-[var(--hover-blue-gray)]">
        <!-- Status Indicator -->
        <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[var(--primary-purple)] to-[var(--secondary-purple)]"></div>

        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <!-- Project Title -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-3 mb-2">
              <div class="w-10 h-10 bg-[var(--secondary-purple)] bg-opacity-20 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="ri-folder-line text-[var(--primary-purple)] text-lg"></i>
              </div>
              <h3 class="text-xl font-semibold text-[var(--text-dark)] group-hover:text-[var(--primary-purple)] transition-colors duration-200 line-clamp-2">
                {{ project.subject || 'Untitled Project' }}
              </h3>
            </div>
          </div>

          <!-- Project Meta Information -->
          <div class="flex flex-col sm:flex-row gap-4 text-sm">
            <!-- Assigned User -->
            <div class="flex items-center gap-2 px-3 py-2 bg-[var(--background-light-gray)] rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)]">
              <i class="ri-user-line text-[var(--primary-purple)]"></i>
              <span class="text-[var(--text-medium-gray)]">Assigned:</span>
              <span class="font-medium text-[var(--text-dark)]">{{ project.userEmail || 'Unassigned' }}</span>
            </div>

            <!-- Due Date -->
            <div class="flex items-center gap-2 px-3 py-2 bg-[var(--background-light-gray)] rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)]">
              <i class="ri-calendar-line text-[var(--primary-purple)]"></i>
              <span class="text-[var(--text-medium-gray)]">Due:</span>
              <span class="font-medium"
                    [ngClass]="{
                      'text-red-600': project.completionDate && isOverdue(project.completionDate),
                      'text-green-600': project.completionDate && !isOverdue(project.completionDate),
                      'text-[var(--text-medium-gray)]': !project.completionDate
                    }">
                {{ project.completionDate ? (project.completionDate.toJSDate() | date:'MMM dd, yyyy') : 'No due date' }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Project Summary Content -->
      <div class="p-6 bg-[var(--background-light-gray)]">
        <div class="space-y-4">
          <!-- Summary Header -->
          <div class="flex items-center gap-2 mb-4">
            <i class="ri-file-text-line text-[var(--primary-purple)]"></i>
            <h4 class="text-lg font-medium text-[var(--text-dark)]">Project Summary</h4>
          </div>

          <!-- Markdown Content -->
          <div class="prose prose-sm max-w-none"
               [ngClass]="{
                 'prose-theme-light': !themeService.isDarkMode(),
                 'prose-theme-dark': themeService.isDarkMode()
               }">
            <div markdown [data]="project.summary || 'No summary available for this project.'"
                 class="text-[var(--text-dark)] leading-relaxed"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="projects.length === 0"
      class="flex flex-col items-center justify-center py-16 bg-[var(--background-white)] border-2 border-dashed border-[var(--hover-blue-gray)] rounded-[var(--border-radius-large)]">
      <div class="w-16 h-16 bg-[var(--secondary-purple)] bg-opacity-20 rounded-full flex items-center justify-center mb-4">
        <i class="ri-folder-open-line text-3xl text-[var(--primary-purple)]"></i>
      </div>
      <h3 class="text-lg font-semibold text-[var(--text-dark)] mb-2">No Projects Found</h3>
      <p class="text-sm text-[var(--text-medium-gray)] text-center max-w-md">
        There are no projects available in this workspace. Create your first project to get started.
      </p>
    </div>
  </div>
</div>
