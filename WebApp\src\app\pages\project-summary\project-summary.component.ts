import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { MarkdownModule, MarkdownService } from 'ngx-markdown';
import { provideMarkdown } from 'ngx-markdown';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { ProjectsServiceProxy, ProjectViewDto } from '../../../shared/service-proxies/service-proxies';
import { ThemeService } from '../../../shared/services/theam.service';


@Component({
  selector: 'app-project-summary',
  standalone: true,
  imports: [
    CommonModule,
    MarkdownModule,
    ServiceProxyModule,
  ],
  templateUrl: './project-summary.component.html',
  styleUrl: './project-summary.component.css',
  providers: [
    MarkdownService,
    provideMarkdown(),
    ProjectsServiceProxy
  ]
})
export class ProjectSummaryComponent implements OnInit {
  projects: ProjectViewDto[] = [];
  workspaceId: number = 0;
  isLoading: boolean = false;

  // Inject services
  themeService = inject(ThemeService);

  constructor(
    private _projectsService: ProjectsServiceProxy,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(() => {
      const urlSegments = this.router.url.split('/');
      this.workspaceId = +urlSegments[urlSegments.indexOf('workspace') + 1]; // Get the workspace ID from the URL
      if (this.workspaceId) {
        this.loadProjects();
      }
    });
        this.loadProjects();

  }
  loadProjects() {
    this.isLoading = true; // Set loading state to true
    this._projectsService
      .getAll()
      .subscribe({
        next: (result: ProjectViewDto[]) => {
          if (result) {
            console.log(result);
            this.projects = result;
          }
          this.isLoading = false; // Set loading state to false
        },
        error: () => {
          this.isLoading = false; // Set loading state to false on error
        }
      });
  }

  /**
   * TrackBy function for project items to improve performance
   */
  trackByProject(_index: number, project: ProjectViewDto): number {
    return project.id;
  }

  /**
   * Check if a project is overdue
   */
  isOverdue(completionDate: any): boolean {
    if (!completionDate) return false;
    const today = new Date();
    const dueDate = completionDate.toJSDate ? completionDate.toJSDate() : new Date(completionDate);
    return dueDate < today;
  }
}
