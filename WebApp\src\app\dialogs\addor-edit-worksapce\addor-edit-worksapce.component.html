<!-- HTML template -->
<div class="bg-background-white px-4 py-2 rounded-lg shadow-default w-full">
  <h3 class="text-primary-purple text-header font-bold mb-4">{{data.title}}</h3>
  <form #workspaceForm="ngForm" (ngSubmit)="saveWorkspace()">
    <div class="mb-4">
      <label for="workspaceName" class="block text-text-medium-gray text-body mb-2">Title</label>
      <input
        type="text"
        id="workspaceName"
        name="workspaceName"
        class="w-full p-2 border dark:bg-gray-800 dark:border-gray-700 dark:text-white border-gray-300 rounded-lg text-black outline-none"
        required
        [(ngModel)]="workspace.title">
    </div>

    <div class="mb-4">
      <label for="workspaceDescription" class="block text-text-medium-gray text-body mb-2">Description</label>
      <textarea
        id="workspaceDescription"
        name="description"
        class="w-full p-2 max-h-[20rem] text-black border dark:bg-gray-800 dark:border-gray-700 dark:text-white border-gray-300 rounded-lg outline-none"
        rows="2"
        required
        [(ngModel)]="workspace.description"></textarea>
    </div>

    <div class="mb-4">
      <label for="systemInformation" class="block text-text-medium-gray text-body mb-2">System Information</label>
      <textarea
        id="systemInformation"
        name="systemInformation"
        class="w-full p-2 max-h-[20rem] text-black border dark:bg-gray-800 dark:border-gray-700 dark:text-white border-gray-300 rounded-lg outline-none"
        rows="2"
        required
        [(ngModel)]="workspace.systemInformation"></textarea>
    </div>

    <div class="mb-4 flex justify-between items-center gap-2">
      <div class="flex items-center justify-center gap-4 *:m-0">
        <label for="isDefault" class="block text-text-medium-gray text-body mb-2">Is Default</label>
        <nz-switch
          id="isDefault"
          name="isDefault"
          class="custom-switch"
          [(ngModel)]="workspace.isDefault"></nz-switch>
      </div>
      <div class="flex items-center justify-center gap-4 *:m-0">
        <label for="isProjectManagement" class="block text-text-medium-gray text-body mb-2">Is Project Management</label>
        <nz-switch
          id="isProjectManagement"
          name="isProjectManagement"
          [(ngModel)]="workspace.isProjectManagement"></nz-switch>
      </div>
    </div>

    <div class="mb-4">
      <label for="model" class="block font-[var(--font-weight-regular)] text-[var(--text-medium-gray)] mb-1">Model</label>
      <nz-input-group nzSearch nzSize="large" [nzAddOnAfter]="suffixIconButton">
        <input
          type="text"
          nz-input
          name="model"
          id="model"
          autocomplete="off"
          [nzAutocomplete]="auto"
          [(ngModel)]="modelSearchQuery"
          (input)="onChange($event)"
          placeholder="Search model name..."
          class="w-full p-2 !bg-[var(--background-light-gray)] border !text-[var(--text-dark)] !border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)]" />
      </nz-input-group>
      <ng-template #suffixIconButton>
        <button nz-button nzType="primary" nzSize="large" nzSearch
          class="p-2 bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)]">
        </button>
      </ng-template>
      <nz-autocomplete #auto class="!bg-[var(--background-white)] w-full">
        @for (option of filteredModels; track option.modelName) {
          <nz-auto-option
            class="search-item !bg-[var(--background-white)] !border-[var(--hover-blue-gray)] hover:!bg-[var(--secondary-purple)]"
            [nzValue]="option.modelName">
            <div class="search-item-desc !text-[var(--text-dark)] p-2" (click)="updateModel(option.modelName)">
              {{ option.modelName }}
            </div>
          </nz-auto-option>
        }
      </nz-autocomplete>
    </div>

    <div class="flex justify-end *:font-bold mt-2 *:cursor-pointer *:!text-[var(--text-dark)]">
      <button
        type="button"
        class="bg-gray-300 dark:bg-gray-500 cursor-pointer  outline-none border-none px-4 py-2 !text-black hover:text-black rounded-lg mr-2"
        (click)="cancel()">
        <span class="text-black dark:text-white">Cancel</span>
      </button>
      <button
        type="submit"
        class="bg-secondary-purple px-4 py-2 rounded-lg outline-none border-none"
        [disabled]="!workspaceForm.valid"
        *ngIf="!data.isUpdating">
        <span class="text-black">Add</span>
      </button>
      <button
        type="submit"
        class="bg-secondary-purple px-4 py-2 rounded-lg outline-none border-none"
        [disabled]="!workspaceForm.valid"
        *ngIf="data.isUpdating">
        <span class="text-black dark:text-white">Update</span>
      </button>
    </div>
  </form>
</div>
