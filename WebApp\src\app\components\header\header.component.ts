import { CommonModule } from '@angular/common';
import {
  Component,
  inject,
  OnInit,
  OnChanges,
  OnDestroy,
  ViewChild,
  ElementRef,
  HostListener
} from '@angular/core';
import { TogglingService } from '../../toggling.service';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterLink,
} from '@angular/router';
import { AuthService } from '../../../shared/services/auth.service';
import {
  AgentDefinitionServiceProxy,
  ModelDetailsServiceProxy,
  AiServiceProxy,
  NavigationServiceProxy,
  NavigationSearchResult
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { FormsModule } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { ThemeService } from '../../../shared/services/theam.service';
import { ThemeToggleComponent } from '../../components/theme-toogle.component';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    ServiceProxyModule,
    FormsModule,
    NzBreadCrumbModule,
    RouterLink,
    ThemeToggleComponent,
  ],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css',
})
export class HeaderComponent implements OnDestroy {
  @ViewChild('userMenu') userMenu!: ElementRef;
  @ViewChild('profileButton') profileButton!: ElementRef;
  @ViewChild('profileContainer') profileContainer!: ElementRef;

  currentUrl: string = '';
  tab: any = '';
  router = inject(Router);

  togglingservice = inject(TogglingService);
  themeService = inject(ThemeService);
  models: any = [];
  filteredModels: any = [];
  searchModelQuery = '';
  selectedModel = '';
  breadCrumsList: any = [];
  isAtHomePage: boolean = false;
  workspaceName: string = '';
  displayHeaderTitle: string = '';

  // Profile-related properties
  isProfileMenuOpen = false;
  user: any;

  // Global Search Properties
  searchQuery: string = '';
  showSearchResults: boolean = false;
  searchResults: any[] = [];
  filteredResults: any[] = [];
  searchTimeout: any = null;

  // Agent Search Properties
  agents: any[] = [];
  filteredAgents: any[] = [];
  isLoadingAgents: boolean = false;

  // Navigation Search Properties
  navigationResults: NavigationSearchResult[] = [];
  showNavigationResults: boolean = false;
  isSearchingNavigation: boolean = false;
  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();
  isNavigationInitialized: boolean = false;
  constructor(
    public auth: AuthService,
    private route: ActivatedRoute,
    private modelDetailsService: ModelDetailsServiceProxy,
    public authService: AuthService,
    private message: NzMessageService,
    private _agentDefinition: AgentDefinitionServiceProxy,
    private _aiService: AiServiceProxy,
    private _navigationService: NavigationServiceProxy
  ) {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        if (this.router.url.includes('workspaces')) {
          this.workspaceName = this.router.url.split('/')[2] || '';
          console.log(this.workspaceName);
          this.workspaceName = decodeURIComponent(this.workspaceName);
        } else {
          this.workspaceName = "";
        }

        // Check if URL is empty or just the root URL
        if (this.router.url === '' || this.router.url === '/') {
          this.isAtHomePage = true;
        } else {
          this.isAtHomePage = false;
        }

        // Update display header title based on URL
        this.updateDisplayHeaderTitle();
      }
    });

    this.getBreadCrumsList(); // Initialize the breadcrumb list on component creation

    // Setup navigation search debouncing
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(query => {
      this.performNavigationSearch(query);
    });
  }

  updateDisplayHeaderTitle() {
    // Check if URL is empty or just the root URL
    if (this.currentUrl === '' || this.currentUrl === '/' || this.isAtHomePage) {
      this.displayHeaderTitle = 'AI Hub';
      return;
    }

    if (this.currentUrl.includes('/settings/agents/new')) {
      this.displayHeaderTitle = 'Agent new';
    } else if (this.currentUrl.includes('/workspaces/') && this.currentUrl.endsWith('/chat')) {
      this.displayHeaderTitle = `${this.workspaceName.toLowerCase()} agent new`;
    } else if (this.currentUrl.includes('/workspaces/') && this.currentUrl.endsWith('/agents/new')) {
      this.displayHeaderTitle = `${this.workspaceName.toLowerCase()} agent new`;
    } else if (this.breadCrumsList.length > 0) {
      this.displayHeaderTitle = this.breadCrumsList[this.breadCrumsList.length - 1].title;
    } else {
      this.displayHeaderTitle = 'AI Hub';
    }
  }

  getBreadCrumsList() {
    let previousUrl = '';

    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.currentUrl = this.router.url;

        // Only reset and rebuild breadcrumbs if the URL has actually changed
        // This prevents breadcrumbs from disappearing when clicking the same route
        if (this.currentUrl !== previousUrl) {
          previousUrl = this.currentUrl;
          this.breadCrumsList = []; // Reset the breadcrumb list only when URL changes

          // Create breadcrumb objects with title & link
          const segments = this.currentUrl
            .split('/')
            .filter((segment) => segment.length > 0);
          let path = '';

          // Set the home breadcrumb
          this.breadCrumsList = [{ title: 'AI Hub', link: '/' }];

          // If we're at the root URL, don't add any more breadcrumbs
          if (this.currentUrl === '/' || this.currentUrl === '') {
            return;
          }

          segments.forEach((segment) => {
            path += `/${decodeURIComponent(segment)}`;
            this.breadCrumsList.push({ title: decodeURIComponent(segment), link: path });
          });
        }

        // Handle basic breadcrumb transformations
        if (this.breadCrumsList[this.breadCrumsList.length - 2]?.title === 'home') {
          this.breadCrumsList[this.breadCrumsList.length - 1].title = 'Chat History';
          this.breadCrumsList.splice(this.breadCrumsList.length - 2, 1);
        }

        if (this.breadCrumsList[this.breadCrumsList.length - 2]?.title === 'chat') {
          this.breadCrumsList[this.breadCrumsList.length - 1].title = 'Chat History';
          this.breadCrumsList.splice(this.breadCrumsList.length - 2, 1);
        }

        if (this.breadCrumsList[this.breadCrumsList.length - 1]?.title === '0') {
          this.breadCrumsList[this.breadCrumsList.length - 1].title = 'Chat History';
          this.breadCrumsList.splice(this.breadCrumsList.length - 2, 1);
        }

        // Special cases for agent breadcrumbs
        if (this.currentUrl.includes('/settings/agents/new')) {
          // For /settings/agents/new URL pattern
          this.breadCrumsList[this.breadCrumsList.length - 1].title = 'Agent new';
        } else if (this.workspaceName &&
          (this.currentUrl.endsWith('/agents/new') || this.currentUrl.endsWith('/chat'))) {
          // For both /workspaces/NAME/agents/new and /workspaces/NAME/chat URL patterns
          this.breadCrumsList[this.breadCrumsList.length - 1].title = `${this.workspaceName.toLowerCase()} agent new`;
        }

        console.log('Breadcrumbs Updated:', this.breadCrumsList);
      }
    });
  }

  async ngOnInit() {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //Add 'implements OnInit' to the class.
    this.tab = this.route.snapshot.paramMap.get('adnminTab');
    this.getBreadCrums();

    // Load user info and models
    this.user = await this.auth.getUser();
    this.loadActiveModels();
    // this.loadAllAgents();
    this.initializeNavigationData();
  }

  // loadAllAgents() {
  //   this.isLoadingAgents = true;
  //   this._agentDefinition.getAll().subscribe({
  //     next: (result: any) => {
  //       this.agents = result;
  //       this.filteredAgents = [...this.agents];
  //       this.isLoadingAgents = false;
  //     },
  //     error: (error: any) => {
  //       console.error('Error loading agents:', error);
  //       this.isLoadingAgents = false;
  //       // Fallback to empty array
  //       this.agents = [];
  //       this.filteredAgents = [];
  //     }
  //   });
  // }

  @HostListener('document:click', ['$event'])
  handleDocumentClick(event: MouseEvent) {
    // Check if userMenu and profileButton are defined
    if (!this.userMenu || !this.profileButton || !this.profileContainer) return;

    // Get the DOM elements
    const userMenuElement = this.userMenu.nativeElement;
    const profileButtonElement = this.profileButton.nativeElement;
    const profileContainerElement = this.profileContainer.nativeElement;

    // Check if the click was outside both the menu and the button
    if (this.isProfileMenuOpen &&
      !userMenuElement.contains(event.target) &&
      !profileContainerElement.contains(event.target)) {
      // Close the menu
      this.closeProfileMenu();
    }
  }

  // Method to toggle profile menu
  toggleProfileMenu(event: Event) {
    event.stopPropagation();
    this.isProfileMenuOpen = !this.isProfileMenuOpen;

    // Toggle classes on the menu element
    if (this.userMenu) {
      this.userMenu.nativeElement.classList.toggle('opacity-0');
      this.userMenu.nativeElement.classList.toggle('hidden');
    }
  }

  // Method to close profile menu
  closeProfileMenu() {
    this.isProfileMenuOpen = false;

    // Add classes to hide the menu
    if (this.userMenu) {
      this.userMenu.nativeElement.classList.add('opacity-0');
      this.userMenu.nativeElement.classList.add('hidden');
    }
  }

  getBreadCrums() {
    // console.log(this.breadCrumsList);
  }

  ngOnChanges(): void {
    //Called before any other lifecycle hook. Use it to inject dependencies, but avoid any serious work here.
    //Add '${implements OnChanges}' to the class.

    // Update URL segments for debugging
    const url = this.router.url;
    console.log('URL segments:', url);
  }

  loadActiveModels() {
    this.modelDetailsService.getAllActiveModel().subscribe((result: any) => {
      this.models = result;
      // console.log(this.models);
      this.filteredModels = result;
    });
  }

  searchModels() {
    this.filteredModels = this.models.filter((model: any) =>
      model.modelName
        .toLowerCase()
        .includes(this.searchModelQuery.toLowerCase())
    );
  }

  navigateToSettingsPanel() {
    this.router.navigate(['settings', 'users']);
  }

  logout() {
    this.closeProfileMenu();
    this.auth.logout();
  }

  getModels() {
    this.selectedModel = this.auth.getModel();
    this.message.success('Model Changed Successfully!');
  }

  getUserInitial(): string {
    // Get user information from auth service
    if (this.user?.name) {
      return this.user.name.charAt(0).toUpperCase();
    } else if (this.auth.getUserName()) {
      return this.auth.getUserName().charAt(0).toUpperCase();
    }
    // Default fallback
    return 'U';
  }

  // Global Search Methods
  onSearchInputChange(): void {
    // Clear any existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Debounce the search to avoid excessive API calls
    this.searchTimeout = setTimeout(() => {
      if (this.searchQuery.trim()) {
        this.performSearch();
        this.onNavigationSearchInputChange(); // Also trigger navigation search
      } else {
        this.filteredResults = [];
        this.navigationResults = [];
        this.showNavigationResults = false;
      }
    }, 300);
  }

  performSearch(): void {
    const query = this.searchQuery.toLowerCase();

    // Only search agents by agentName
    const agentResults = this.agents
      .filter(agent =>
        agent.agentName?.toLowerCase().includes(query)
      )
      .map(agent => ({
        id: agent.guid,
        type: 'agent',
        title: agent.agentName,
        snippet: '', // Remove description/instructions
        date: new Date(),
        agentData: agent
      }));

    // Only show agent results
    this.filteredResults = agentResults;
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.filteredResults = [];
    this.showSearchResults = false;
    this.navigationResults = [];
    this.showNavigationResults = false;
  }

  onSearchBlur(): void {
    // Delay hiding results to allow clicks on results
    setTimeout(() => {
      this.showSearchResults = false;
      this.showNavigationResults = false;
    }, 200);
  }

  navigateToResult(result: any): void {
    // Check if this is a NavigationResult or legacy result
    if (result.route) {
      // This is a NavigationResult from the smart search
      this.navigateToNavigationResult(result);
      return;
    }

    // Legacy navigation logic for agent search results
    switch (result.type) {
      case 'chat':
        if (result.workspaceId) {
          this.router.navigate(['/workspaces', result.workspaceId, 'chat', result.id]);
        } else {
          this.router.navigate(['/chat', result.id]);
        }
        break;
      case 'document':
        this.router.navigate(['/notes', result.id]);
        break;
      case 'workspace':
        this.router.navigate(['/workspaces', result.id]);
        break;
      case 'agent':
        // Navigate to agent chat if it's a real agent, otherwise to settings
        if (result.agentData) {
          this.router.navigate(['/chat/agent', result.agentData.agentName]);
          // http://localhost:4200/agent-chat/SqlQueryAgent
        } else {
          this.router.navigate(['/settings', 'agents', result.id]);
        }
        break;
    }

    this.clearSearch();
  }



  /**
   * Search agents specifically by agent name
   */
  searchAgentsByName(query: string): any[] {
    if (!query || query.trim().length === 0) {
      return [];
    }

    const searchTerm = query.toLowerCase().trim();
    return this.agents.filter(agent =>
      agent.agentName?.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Get agent search results for display
   */
  getAgentSearchResults(): any[] {
    return this.filteredResults.filter(result => result.type === 'agent');
  }

  /**
   * Navigate to agent chat page
   * @param agent The agent to view chat for
   * @param event The click event
   */
  viewAgentChat(agent: any, event: Event): void {
    // Prevent the card click event from firing
    event.stopPropagation();

    if (!agent || !agent.agentName) return;

    // Navigate to agent chat page
    this.router.navigate(['/chat', 'agent', agent.agentName]);


    // Clear search
    this.clearSearch();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Navigation Search Methods
  initializeNavigationData(): void {
    // No need to initialize - navigation data is added manually via Swagger API
    // or automatically when agents/workspaces are created
    this.isNavigationInitialized = true;
    console.log('Navigation search ready');
  }

  onNavigationSearchInputChange(): void {
    if (this.searchQuery.trim().length > 0) {
      this.showNavigationResults = true;
      this.searchSubject.next(this.searchQuery);
    } else {
      this.showNavigationResults = false;
      this.navigationResults = [];
    }
  }
  performNavigationSearch(query: string): void {
    if (!query.trim()) {
      this.navigationResults = [];
      this.showNavigationResults = false;
      this.isSearchingNavigation = false;
      return;
    }

    this.isSearchingNavigation = true;

    this._navigationService.searchNavigation(query).subscribe({
      next: (results: NavigationSearchResult[]) => {
        this.navigationResults = results || [];
        this.showNavigationResults = this.navigationResults.length > 0;
        this.isSearchingNavigation = false;
        console.log('Navigation search results:', this.navigationResults);
      },
      error: (error: any) => {
        console.error('Navigation search error:', error);
        this.navigationResults = [];
        this.showNavigationResults = false;
        this.isSearchingNavigation = false;
      }
    });
  }

  navigateToNavigationResult(result: NavigationSearchResult): void {
    if (result.route) {
      this.router.navigate([result.route]);
      this.clearNavigationSearch();
    }
  }

  clearNavigationSearch(): void {
    this.searchQuery = '';
    this.navigationResults = [];
    this.showNavigationResults = false;
  }

  onNavigationSearchFocus(): void {
    if (this.searchQuery.trim().length > 0) {
      this.showNavigationResults = true;
    }
  }

  onNavigationSearchBlur(): void {
    // Delay hiding results to allow for clicks
    setTimeout(() => {
      this.showNavigationResults = false;
    }, 200);
  }

  getNavigationResultIcon(result: NavigationSearchResult): string {
    return result.icon || this.getIconForNavigationType(result.navigationType || '');
  }

  getIconForNavigationType(type: string): string {
    switch (type.toLowerCase()) {
      case 'agent':
        return 'ri-robot-line';
      case 'workspace':
        return 'ri-folder-line';
      case 'settings':
        return 'ri-settings-line';
      case 'plugin':
        return 'ri-plug-line';
      case 'notes':
        return 'ri-file-text-line';
      case 'user':
        return 'ri-user-line';
      case 'admin':
        return 'ri-admin-line';
      case 'chat':
        return 'ri-chat-line';
      default:
        return 'ri-navigation-line';
    }
  }

  getNavigationTypeColor(type: string): string {
    switch (type.toLowerCase()) {
      case 'agent':
        return 'text-blue-500';
      case 'workspace':
        return 'text-green-500';
      case 'settings':
        return 'text-gray-500';
      case 'plugin':
        return 'text-purple-500';
      case 'notes':
        return 'text-yellow-500';
      case 'user':
      case 'admin':
        return 'text-red-500';
      case 'chat':
        return 'text-indigo-500';
      default:
        return 'text-gray-400';
    }
  }

  trackByNavigationResultId(index: number, result: NavigationSearchResult): string {
    return result.id || index.toString();
  }


}
