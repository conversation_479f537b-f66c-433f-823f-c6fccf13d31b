import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import {
  PromptLibraryServiceProxy,
  PromptLibraryResponseDto,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AddOrEditPromptLibraryComponent } from '../../components/models/add-or-edit-prompt-library/add-or-edit-prompt-library.component';
import { AuthService } from '../../../shared/services/auth.service';
import { Router } from '@angular/router';
import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';
import { MatDialog } from '@angular/material/dialog';
import { DeleteDialogComponent } from '../../components/models/delete-dialog/delete-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
@Component({
  selector: 'app-prompts-library',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ServiceProxyModule,
    NzModalModule,
    SpinnerComponent,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
  ],
  templateUrl: './prompts-library.component.html',
  styles: [
    `
      /* Animations */
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(-10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes pulse {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
        100% {
          transform: scale(1);
        }
      }

      @keyframes slideInRight {
        from {
          opacity: 0;
          transform: translateX(20px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      .animate-fadeIn {
        animation: fadeIn 0.5s ease-in-out;
      }

      .animate-pulse {
        animation: pulse 2s ease-in-out infinite;
      }

      /* Header styles */
      .sticky-header {
        position: sticky;
        top: 0;
        z-index: 10;
        backdrop-filter: blur(5px);
      }

      /* Table styles */
      .prompt-table {
        border-collapse: separate;
        border-spacing: 0;
      }

      .prompt-table th {
        position: sticky;
        top: 0;
        z-index: 10;
        font-weight: var(--font-weight-semibold);
        color: var(--text-dark);
        background-color: var(--hover-blue-gray);
        transition: background-color 0.3s ease;
      }

      .prompt-table tr {
        transition: all 0.2s ease;
      }

      .prompt-truncate {
        max-width: 300px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        transition: all 0.3s ease;
      }

      .prompt-truncate:hover {
        white-space: normal;
        overflow: visible;
        max-width: none;
        background-color: var(--background-light-gray);
        position: relative;
        z-index: 20;
        border-radius: var(--border-radius-small);
        box-shadow: var(--box-shadow);
        padding: 0.5rem;
      }

      /* Button styles */
      .action-button {
        opacity: 0.9;
        transition: all 0.2s ease;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      }

      .action-button:hover {
        opacity: 1;
        transform: translateY(-2px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      tr:hover .action-button {
        opacity: 1;
      }

      /* Dark mode button styles */
      :host-context(.dark) .action-button {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
      }

      :host-context(.dark) .action-button:first-child {
        background-color: #2d3748;
      }

      :host-context(.dark) .action-button:first-child:hover {
        background-color: #4a5568;
      }

      :host-context(.dark) .action-button:last-child {
        background-color: #742a2a;
      }

      :host-context(.dark) .action-button:last-child:hover {
        background-color: #9b2c2c;
      }

      button {
        position: relative;
        overflow: hidden;
      }

      button::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(255, 255, 255, 0.5);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%);
        transform-origin: 50% 50%;
      }

      button:focus:not(:active)::after {
        animation: ripple 1s ease-out;
      }

      @keyframes ripple {
        0% {
          transform: scale(0, 0);
          opacity: 0.5;
        }
        20% {
          transform: scale(25, 25);
          opacity: 0.3;
        }
        100% {
          opacity: 0;
          transform: scale(40, 40);
        }
      }

      /* Dark mode specific styles */
      :host-context(.dark) input,
      :host-context(.dark) select,
      :host-context(.dark) button {
        color: var(--text-dark);
        border-color: rgba(255, 255, 255, 0.1);
      }

      :host-context(.dark) .prompt-table th {
        background-color: var(--hover-blue-gray);
        color: var(--text-dark);
      }

      :host-context(.dark) .prompt-truncate:hover {
        background-color: var(--hover-blue-gray);
        color: var(--text-dark);
      }

      /* Dark mode pagination styles */
      :host-context(.dark) .pagination-container {
        background-color: var(--hover-blue-gray);
        border-color: rgba(255, 255, 255, 0.1);
      }

      :host-context(.dark) .pagination-container span,
      :host-context(.dark) .pagination-container button {
        color: var(--text-dark);
      }

      :host-context(.dark)
        .pagination-container
        button:hover:not(:disabled):not(.bg-\[var\(--primary-purple\)\]) {
        background-color: #4a5568;
      }

      :host-context(.dark) .pagination-container button:disabled {
        color: rgba(255, 255, 255, 0.3);
      }

      /* Responsive styles */
      @media (max-width: 768px) {
        .action-button {
          opacity: 1;
        }

        .prompt-truncate {
          max-width: 150px;
        }
      }
    `,
  ],
})
export class PromptsLibraryComponent implements OnInit {
  prompts: PromptLibraryResponseDto[] = [];
  filteredPrompts: PromptLibraryResponseDto[] = [];
  paginatedPrompts: PromptLibraryResponseDto[] = [];
  isUpdating = false;
  isLoading = false; // Loading state for spinner
  searchTerm = '';
  selectedFilter = 'All Prompts';
  prompt: any = {
    id: 0,
    prompt: '',
    isGlobal: false,
    workspaceName: '',
    userEmail: '',
  };
  workspaceName: string | undefined = '';

  // Pagination properties
  currentPage: number = 1;
  pageSize: number = 10;
  totalPages: number = 1;
  previousPageSize: number = 10;
  jumpToPage: number = 1; // Property for jump-to-page input

  // Make Math available to the template
  Math = Math;

  constructor(
    private promptsService: PromptLibraryServiceProxy,
    public modal: NzModalService,
    private message: NzMessageService,
    public authService: AuthService,
    private router: Router,
    private cdr: ChangeDetectorRef,
    private dailog: MatDialog
  ) {}

  ngOnInit(): void {
    const routerSegments = this.router.url.split('/');

    // Only allow access from settings page
    if (
      !routerSegments.includes('settings') ||
      !routerSegments.includes('prompt-library')
    ) {
      // Redirect to settings if not accessed from settings page
      this.router.navigate(['/settings', 'prompt-library']);
      return;
    }

    // Ensure pageSize is a number and set previousPageSize
    this.pageSize = Number(this.pageSize);
    this.previousPageSize = this.pageSize;

    // Initialize jumpToPage to current page
    this.jumpToPage = this.currentPage;

    this.getPrompts();
  }

  get isAdmin() {
    return this.authService.isAdmin();
  }
  getPrompts() {
    this.isLoading = true; // Show spinner
    var userEmail = this.authService.getUser().email;
    var isAdmin = this.authService.isAdmin();
    var email = isAdmin ? undefined : userEmail;
    // Get all prompts without workspace filtering since we're in settings
    this.promptsService.getAll(undefined, undefined, email).subscribe({
      next: (res: any) => {
        if (res) {
          this.prompts = res;
          console.log(this.prompts);
          this.filterPrompts();
        }
        this.isLoading = false; // Hide spinner
      },
      error: (error) => {
        console.error('Error fetching prompts:', error);
        this.message.error('Failed to load prompts');
        this.isLoading = false; // Hide spinner even on error
      },
    });
  }

  filterPrompts() {
    let filtered = [...this.prompts];

    // Apply search filter if there's a search term
    if (this.searchTerm?.trim()) {
      const searchLower = this.searchTerm.trim().toLowerCase();
      filtered = filtered.filter(
        (prompt) =>
          (prompt.prompt?.toLowerCase() || '').includes(searchLower) ||
          (prompt.shortMessage?.toLowerCase() || '').includes(searchLower) ||
          (prompt.workspaceName?.toLowerCase() || '').includes(searchLower) ||
          (prompt.agentName?.toLowerCase() || '').includes(searchLower)
      );
    }

    // Apply type filter
    switch (this.selectedFilter) {
      case 'workspace':
        filtered = filtered.filter(
          (prompt) => prompt.workspaceName && !prompt.agentName
        );
        break;
      case 'agent':
        filtered = filtered.filter(
          (prompt) => prompt.agentName && !prompt.workspaceName
        );
        break;
      // 'all' case - no filtering needed
    }

    this.filteredPrompts = filtered;

    // Reset to first page when filters change
    this.currentPage = 1;
    this.jumpToPage = 1;

    this.updatePagination();
  }

  /**
   * Update pagination based on current page and page size
   */
  updatePagination() {
    // Ensure pageSize is a number
    this.pageSize = Number(this.pageSize);

    // Check if page size has changed
    const pageSizeChanged = this.previousPageSize !== this.pageSize;

    // Calculate total pages (minimum 1 page)
    this.totalPages = Math.max(
      1,
      Math.ceil(this.filteredPrompts.length / this.pageSize)
    );

    // Reset to page 1 when page size changes
    if (pageSizeChanged) {
      this.currentPage = 1;
      console.log(
        'Page size changed from',
        this.previousPageSize,
        'to',
        this.pageSize,
        '- resetting to page 1'
      );
    }

    // Ensure current page is within bounds
    if (this.currentPage < 1) this.currentPage = 1;
    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages;

    // Sync jumpToPage with currentPage
    this.jumpToPage = this.currentPage;

    // Get current page of prompts
    if (this.filteredPrompts.length === 0) {
      this.paginatedPrompts = [];
    } else {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = Math.min(
        startIndex + this.pageSize,
        this.filteredPrompts.length
      );
      this.paginatedPrompts = this.filteredPrompts.slice(startIndex, endIndex);
    }

    // Store current page size for next comparison
    this.previousPageSize = this.pageSize;

    // Log pagination state for debugging
    console.log('Pagination updated:', {
      totalItems: this.filteredPrompts.length,
      pageSize: this.pageSize,
      totalPages: this.totalPages,
      currentPage: this.currentPage,
      itemsOnCurrentPage: this.paginatedPrompts.length,
    });
  }

  /**
   * Go to a specific page
   */
  goToPage(page: number) {
    // Ensure page is a number
    page = Number(page);

    // Validate the page number is within bounds
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePagination();

      // Reset the jumpToPage input to match current page
      this.jumpToPage = this.currentPage;
    } else if (page < 1) {
      // If less than 1, go to first page
      this.currentPage = 1;
      this.updatePagination();
      this.jumpToPage = 1;
    } else if (page > this.totalPages) {
      // If greater than total, go to last page
      this.currentPage = this.totalPages;
      this.updatePagination();
      this.jumpToPage = this.totalPages;
    }
  }

  /**
   * Go to the previous page
   */
  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePagination();
    }
  }

  /**
   * Go to the next page
   */
  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePagination();
    }
  }

  // Update search term and trigger filtering
  onSearchChange(value: string) {
    this.searchTerm = value;
    this.filterPrompts();
  }

  // Clear search
  clearSearch() {
    this.searchTerm = '';
    this.filterPrompts();
  }

  resetForm() {
    this.prompt = {
      id: 0,
      prompt: '',
      shortMessage: '',
      workspaceName: '',
      agentName: '',
      userEmail: '',
    };
  }

  openAddEditModal(prompt?: any): void {
    this.isUpdating = !!prompt;
    if (prompt) {
      this.prompt = { ...prompt };
    } else {
      this.prompt = {
        shortMessage: '',
        prompt: '',
        workspaceName: '',
        agentName: ''
      };
    }

    const dialogRef = this.dailog.open(AddOrEditPromptLibraryComponent, {
      width: '40rem',
      panelClass: 'prompt-modal',
      data: { prompt: this.prompt }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        if (this.isUpdating) {
          this.updatePrompt(result);
        } else {
          this.createPrompt(result);
        }
      }
    });
  }

  createPrompt(prompt: any): void {
    this.isLoading = true;
    prompt.userEmail = this.authService.getUser().email;

    console.log('Creating prompt with data:', prompt);

    this.promptsService.createOrUpdate(prompt).subscribe({
      next: () => {
        this.message.success('Prompt created successfully');
        this.getPrompts();
      },
      error: (error: any) => {
        if (error.isError && error.message) {
          this.message.error(error.message);
        } else {
          this.message.error('Failed to create prompt');
          console.error('Failed to create prompt:', error);
        }
        this.isLoading = false;
      }
    });
  }

  updatePrompt(prompt: any): void {
    this.isLoading = true;

    console.log('Updating prompt with data:', prompt);

    this.promptsService.createOrUpdate(prompt).subscribe({
      next: () => {
        this.message.success('Prompt updated successfully');
        this.getPrompts();
      },
      error: (error: any) => {
        if (error.isError && error.message) {
          this.message.error(error.message);
        } else {
          this.message.error('Failed to update prompt');
          console.error('Failed to update prompt:', error);
        }
        this.isLoading = false;
      }
    });
  }


  deletePrompt(prompt: any) {
    const dialogRef = this.dailog.open(DeleteDialogComponent, {
      data: {
        title: 'Delete Prompt',
        message: 'This action cannot be undone.',
        prompt: prompt,
      },
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.isLoading = true;
        const isAdmin = this.authService.isAdmin();
        const email = isAdmin ? undefined : this.authService.getUser().email;
        this.promptsService.delete(prompt.id, email).subscribe({
          next: () => {
            this.message.success('Prompt deleted successfully');
            this.getPrompts();
          },
          error: (error: any) => {
            if (error.isError && error.message) {
              this.message.error(error.message);
            } else {
              this.message.error('Failed to delete prompt');
              console.error('Failed to delete prompt:', error);
            }
            this.isLoading = false;
          }
        });
      }
    });
  }

  isOpen: boolean = false;

  toggleDropdown() {
    this.isOpen = !this.isOpen;
  }

  selectOption(value: string) {
    this.selectedFilter = value;
    this.isOpen = false;
    this.filterPrompts();
  }
  isPageSizeDropdownOpen: boolean = false;
  isDropdownAbove: boolean = false;
  isPageLenthOpen: boolean = false;

  togglePageSizeDropdown() {
    this.isPageSizeDropdownOpen = !this.isPageSizeDropdownOpen;
    if (this.isPageSizeDropdownOpen) {
      setTimeout(() => {
        const btn = document.getElementById('pageSizeDropdownBtn');
        const dropdown = document.getElementById('pageSizeDropdownMenu');
        if (btn && dropdown) {
          const rect = btn.getBoundingClientRect();
          const dropdownHeight = dropdown.offsetHeight || 160;
          const spaceBelow = window.innerHeight - rect.bottom;
          this.isDropdownAbove = spaceBelow < dropdownHeight + 16;
        }
      }, 0);
    }
  }

  selectPageSize(size: number) {
    this.pageSize = size;
    this.isPageSizeDropdownOpen = false;
    this.currentPage = 1;
    this.updatePagination();
  }
  paginatiionDropdown() {
    this.isPageLenthOpen = !this.isPageLenthOpen;
    this.isDropdownAbove = !this.isDropdownAbove;
    if (this.isPageLenthOpen) {
      // Use setTimeout to ensure DOM is updated before checking position
      setTimeout(() => {
        this.checkDropdownPosition();
        this.cdr.detectChanges(); // Trigger change detection
      }, 0);
    }
  }

  @ViewChild('dropdownButton') dropdownButton!: ElementRef;
  @ViewChild('dropdownMenu') dropdownMenu!: ElementRef;

  checkDropdownPosition() {
    if (this.dropdownButton && this.dropdownMenu) {
      const buttonRect =
        this.dropdownButton.nativeElement.getBoundingClientRect();
      const menuRect = this.dropdownMenu.nativeElement.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Check if dropdown bottom would exceed window height
      const dropdownBottom = buttonRect.bottom + menuRect.height;
      this.isDropdownAbove = dropdownBottom > windowHeight;
    }
  }

  ngAfterViewInit() {
    // Ensure initial position check if dropdown is open
    if (this.isPageLenthOpen) {
      this.checkDropdownPosition();
      this.cdr.detectChanges();
    }
  }
}
