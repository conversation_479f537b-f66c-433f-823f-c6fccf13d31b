{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-upload.mjs"], "sourcesContent": ["import { ENTER } from '@angular/cdk/keycodes';\nimport * as i1 from '@angular/common/http';\nimport { HttpRequest, HttpHeaders, HttpEventType, HttpResponse } from '@angular/common/http';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Optional, ViewChild, Input, ChangeDetectionStrategy, Inject, EventEmitter, Output, NgModule } from '@angular/core';\nimport { of, Observable, Subscription, Subject, fromEvent } from 'rxjs';\nimport { switchMap, map, tap, takeUntil, filter } from 'rxjs/operators';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { DOCUMENT, NgForOf, NgSwitch, NgTemplateOutlet, NgIf, NgSwitchDefault, NgSwitchCase, NgStyle, NgClass } from '@angular/common';\nimport * as i4 from 'ng-zorro-antd/button';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i6 from 'ng-zorro-antd/progress';\nimport { NzProgressModule } from 'ng-zorro-antd/progress';\nimport * as i2 from 'ng-zorro-antd/tooltip';\nimport { NzToolTipModule } from 'ng-zorro-antd/tooltip';\nimport * as i1$1 from '@angular/cdk/platform';\nimport * as i5 from 'ng-zorro-antd/core/transition-patch';\nimport { __decorate } from 'tslib';\nimport { toBoolean, InputNumber, InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i1$2 from 'ng-zorro-antd/i18n';\nimport * as i2$1 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"file\"];\nconst _c1 = [\"nz-upload-btn\", \"\"];\nconst _c2 = [\"*\"];\nconst _c3 = a0 => ({\n  $implicit: a0\n});\nconst _c4 = () => ({\n  opacity: 0.5,\n  \"pointer-events\": \"none\"\n});\nfunction NzUploadListComponent_div_0_ng_template_2_div_1_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_2_div_1_ng_template_1_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    const iconNode_r2 = i0.ɵɵreference(5);\n    i0.ɵɵclassProp(\"ant-upload-list-item-file\", !file_r1.isUploading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", iconNode_r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c3, file_r1));\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_2_a_2_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 24);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"src\", file_r1.thumbUrl || file_r1.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"alt\", file_r1.name);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_2_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 22);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_div_0_ng_template_2_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handlePreview(file_r1, $event));\n    });\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_2_a_2_img_1_Template, 1, 2, \"img\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const noImageThumbTpl_r5 = i0.ɵɵreference(5);\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"ant-upload-list-item-file\", !file_r1.isImageUrl);\n    i0.ɵɵproperty(\"href\", file_r1.url || file_r1.thumbUrl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r1.isImageUrl)(\"ngIfElse\", noImageThumbTpl_r5);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_2_div_3_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_2_div_3_ng_template_1_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    const iconNode_r2 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", iconNode_r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, file_r1));\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_2_ng_template_4_ng_template_0_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_div_0_ng_template_2_ng_template_4_ng_template_0_Template, 0, 0, \"ng-template\", 21);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    const iconNode_r2 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", iconNode_r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, file_r1));\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 16);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_2_div_1_Template, 2, 6, \"div\", 17)(2, NzUploadListComponent_div_0_ng_template_2_a_2_Template, 2, 5, \"a\", 18)(3, NzUploadListComponent_div_0_ng_template_2_div_3_Template, 2, 4, \"div\", 19);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(4, NzUploadListComponent_div_0_ng_template_2_ng_template_4_Template, 1, 4, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngSwitch\", file_r1.iconType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"uploading\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"thumbnail\");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 29);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_2_ng_container_1_Template, 2, 0, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const file_r6 = i0.ɵɵnextContext(2).$implicit;\n    const iconNodeFileIcon_r7 = i0.ɵɵreference(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r6.isUploading)(\"ngIfElse\", iconNodeFileIcon_r7);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.locale.uploading, \" \");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_3_ng_container_1_Template, 2, 1, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const file_r6 = i0.ɵɵnextContext(2).$implicit;\n    const iconNodeFileIcon_r7 = i0.ɵɵreference(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r6.isUploading)(\"ngIfElse\", iconNodeFileIcon_r7);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_container_0_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n  if (rf & 2) {\n    const file_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"nzType\", file_r6.isUploading ? \"loading\" : \"paper-clip\");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0)(1, 16);\n    i0.ɵɵtemplate(2, NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_2_Template, 2, 2, \"ng-container\", 27)(3, NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_3_Template, 2, 2, \"ng-container\", 27)(4, NzUploadListComponent_div_0_ng_template_4_ng_container_0_span_4_Template, 1, 1, \"span\", 28);\n    i0.ɵɵelementContainerEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", ctx_r3.listType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"picture\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"picture-card\");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 31);\n  }\n  if (rf & 2) {\n    const file_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"nzType\", file_r6.isImageUrl ? \"picture\" : \"file\");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_div_0_ng_template_4_ng_container_0_Template, 5, 3, \"ng-container\", 26)(1, NzUploadListComponent_div_0_ng_template_4_ng_template_1_Template, 0, 0, \"ng-template\", 21, 7, i0.ɵɵtemplateRefExtractor)(3, NzUploadListComponent_div_0_ng_template_4_ng_template_3_Template, 1, 1, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const file_r6 = ctx.$implicit;\n    const customIconRender_r8 = i0.ɵɵreference(2);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.iconRender)(\"ngIfElse\", customIconRender_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.iconRender)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c3, file_r6));\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_6_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_div_0_ng_template_6_button_0_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleRemove(file_r1, $event));\n    });\n    i0.ɵɵelement(1, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"title\", ctx_r3.locale.removeFile);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_div_0_ng_template_6_button_0_Template, 2, 1, \"button\", 32);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.icons.showRemoveIcon);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_8_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_div_0_ng_template_8_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleDownload(file_r1));\n    });\n    i0.ɵɵelement(1, \"span\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"title\", ctx_r3.locale.downloadFile);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_div_0_ng_template_8_button_0_Template, 2, 1, \"button\", 32);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngIf\", file_r1.showDownload);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_10_span_0_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_10_span_0_ng_template_2_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_10_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_10_span_0_ng_template_1_Template, 0, 0, \"ng-template\", 13)(2, NzUploadListComponent_div_0_ng_template_10_span_0_ng_template_2_Template, 0, 0, \"ng-template\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const removeIcon_r11 = i0.ɵɵreference(7);\n    const downloadIcon_r12 = i0.ɵɵreference(9);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"ant-upload-list-item-card-actions \", ctx_r3.listType === \"picture\" ? \"picture\" : \"\", \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", downloadIcon_r12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", removeIcon_r11);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_div_0_ng_template_10_span_0_Template, 3, 5, \"span\", 36);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.listType !== \"picture-card\");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_12_a_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 39);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_div_0_ng_template_12_a_0_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handlePreview(file_r1, $event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"href\", file_r1.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"title\", file_r1.name)(\"download\", file_r1.linkProps && file_r1.linkProps.download);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", file_r1.name, \" \");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_div_0_ng_template_12_span_1_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handlePreview(file_r1, $event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵattribute(\"title\", file_r1.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", file_r1.name, \" \");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_12_ng_template_2_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_div_0_ng_template_12_a_0_Template, 2, 4, \"a\", 37)(1, NzUploadListComponent_div_0_ng_template_12_span_1_Template, 2, 2, \"span\", 38)(2, NzUploadListComponent_div_0_ng_template_12_ng_template_2_Template, 0, 0, \"ng-template\", 13);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    const downloadOrDelete_r15 = i0.ɵɵreference(11);\n    i0.ɵɵproperty(\"ngIf\", file_r1.url);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !file_r1.url);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", downloadOrDelete_r15);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_16_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_17_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_span_18_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 44);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_div_0_span_18_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handlePreview(file_r1, $event));\n    });\n    i0.ɵɵelement(1, \"span\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"href\", file_r1.url || file_r1.thumbUrl, i0.ɵɵsanitizeUrl)(\"ngStyle\", !(file_r1.url || file_r1.thumbUrl) ? i0.ɵɵpureFunction0(3, _c4) : null);\n    i0.ɵɵattribute(\"title\", ctx_r3.locale.previewFile);\n  }\n}\nfunction NzUploadListComponent_div_0_span_18_ng_container_2_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_span_18_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_span_18_ng_container_2_ng_template_1_Template, 0, 0, \"ng-template\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const downloadIcon_r12 = i0.ɵɵreference(9);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", downloadIcon_r12);\n  }\n}\nfunction NzUploadListComponent_div_0_span_18_ng_template_3_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 41);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_span_18_a_1_Template, 2, 4, \"a\", 42)(2, NzUploadListComponent_div_0_span_18_ng_container_2_Template, 2, 1, \"ng-container\", 43)(3, NzUploadListComponent_div_0_span_18_ng_template_3_Template, 0, 0, \"ng-template\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    const removeIcon_r11 = i0.ɵɵreference(7);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.icons.showPreviewIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r1.status === \"done\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", removeIcon_r11);\n  }\n}\nfunction NzUploadListComponent_div_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"nz-progress\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzPercent\", file_r1.percent)(\"nzShowInfo\", false)(\"nzStrokeWidth\", 2);\n  }\n}\nfunction NzUploadListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 10);\n    i0.ɵɵtemplate(2, NzUploadListComponent_div_0_ng_template_2_Template, 6, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, NzUploadListComponent_div_0_ng_template_4_Template, 5, 6, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(6, NzUploadListComponent_div_0_ng_template_6_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(8, NzUploadListComponent_div_0_ng_template_8_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(10, NzUploadListComponent_div_0_ng_template_10_Template, 1, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor)(12, NzUploadListComponent_div_0_ng_template_12_Template, 3, 3, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(14, \"div\", 11)(15, \"span\", 12);\n    i0.ɵɵtemplate(16, NzUploadListComponent_div_0_ng_template_16_Template, 0, 0, \"ng-template\", 13)(17, NzUploadListComponent_div_0_ng_template_17_Template, 0, 0, \"ng-template\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, NzUploadListComponent_div_0_span_18_Template, 4, 3, \"span\", 14)(19, NzUploadListComponent_div_0_div_19_Template, 2, 3, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r1 = ctx.$implicit;\n    const icon_r17 = i0.ɵɵreference(3);\n    const preview_r18 = i0.ɵɵreference(13);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"ant-upload-list-\", ctx_r3.listType, \"-container\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMapInterpolate2(\"ant-upload-list-item ant-upload-list-item-\", file_r1.status, \" ant-upload-list-item-list-type-\", ctx_r3.listType, \"\");\n    i0.ɵɵproperty(\"@itemState\", undefined)(\"nzTooltipTitle\", file_r1.status === \"error\" ? file_r1.message : null);\n    i0.ɵɵattribute(\"data-key\", file_r1.key);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", icon_r17);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", preview_r18);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.listType === \"picture-card\" && !file_r1.isUploading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r1.isUploading);\n  }\n}\nconst _c5 = [\"uploadComp\"];\nconst _c6 = [\"listComp\"];\nconst _c7 = () => [];\nfunction NzUploadComponent_ng_template_0_nz_upload_list_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-upload-list\", 10, 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"display\", ctx_r0.nzShowUploadList ? \"\" : \"none\");\n    i0.ɵɵproperty(\"locale\", ctx_r0.locale)(\"listType\", ctx_r0.nzListType)(\"items\", ctx_r0.nzFileList || i0.ɵɵpureFunction0(13, _c7))(\"icons\", ctx_r0.nzShowUploadList)(\"iconRender\", ctx_r0.nzIconRender)(\"previewFile\", ctx_r0.nzPreviewFile)(\"previewIsImage\", ctx_r0.nzPreviewIsImage)(\"onPreview\", ctx_r0.nzPreview)(\"onRemove\", ctx_r0.onRemove)(\"onDownload\", ctx_r0.nzDownload)(\"dir\", ctx_r0.dir);\n  }\n}\nfunction NzUploadComponent_ng_template_0_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NzUploadComponent_ng_template_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzUploadComponent_ng_template_0_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.nzFileListRender)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, ctx_r0.nzFileList));\n  }\n}\nfunction NzUploadComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadComponent_ng_template_0_nz_upload_list_0_Template, 2, 14, \"nz-upload-list\", 8)(1, NzUploadComponent_ng_template_0_ng_container_1_Template, 2, 4, \"ng-container\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.locale && !ctx_r0.nzFileListRender);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.nzFileListRender);\n  }\n}\nfunction NzUploadComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzUploadComponent_ng_template_4_ng_template_3_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13, 6);\n    i0.ɵɵtemplate(3, NzUploadComponent_ng_template_4_ng_template_3_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const con_r2 = i0.ɵɵreference(3);\n    i0.ɵɵstyleProp(\"display\", ctx_r0.nzShowButton ? \"\" : \"none\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.classList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0._btnOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", con_r2);\n  }\n}\nfunction NzUploadComponent_ng_container_6_ng_template_5_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_container_6_ng_template_6_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵlistener(\"drop\", function NzUploadComponent_ng_container_6_Template_div_drop_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.fileDrop($event));\n    })(\"dragover\", function NzUploadComponent_ng_container_6_Template_div_dragover_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.fileDrop($event));\n    })(\"dragleave\", function NzUploadComponent_ng_container_6_Template_div_dragleave_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.fileDrop($event));\n    });\n    i0.ɵɵelementStart(2, \"div\", 16, 6)(4, \"div\", 17);\n    i0.ɵɵtemplate(5, NzUploadComponent_ng_container_6_ng_template_5_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(6, NzUploadComponent_ng_container_6_ng_template_6_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const list_r4 = i0.ɵɵreference(1);\n    const con_r2 = i0.ɵɵreference(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.classList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0._btnOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", con_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", list_r4);\n  }\n}\nfunction NzUploadComponent_ng_template_7_ng_container_0_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_template_7_ng_container_0_ng_template_2_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_template_7_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzUploadComponent_ng_template_7_ng_container_0_ng_template_1_Template, 0, 0, \"ng-template\", 14)(2, NzUploadComponent_ng_template_7_ng_container_0_ng_template_2_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const list_r4 = i0.ɵɵreference(1);\n    const btn_r5 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", list_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", btn_r5);\n  }\n}\nfunction NzUploadComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadComponent_ng_template_7_ng_container_0_Template, 3, 2, \"ng-container\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const pic_r6 = i0.ɵɵreference(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.nzListType === \"picture-card\")(\"ngIfElse\", pic_r6);\n  }\n}\nfunction NzUploadComponent_ng_template_9_ng_template_0_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_template_9_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadComponent_ng_template_9_ng_template_0_Template, 0, 0, \"ng-template\", 14)(1, NzUploadComponent_ng_template_9_ng_template_1_Template, 0, 0, \"ng-template\", 14);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const list_r4 = i0.ɵɵreference(1);\n    const btn_r5 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", btn_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", list_r4);\n  }\n}\nclass NzUploadBtnComponent {\n  onClick() {\n    if (this.options.disabled || !this.options.openFileDialogOnClick) {\n      return;\n    }\n    this.file.nativeElement.click();\n  }\n  // skip safari bug\n  onFileDrop(e) {\n    if (this.options.disabled || e.type === 'dragover') {\n      e.preventDefault();\n      return;\n    }\n    if (this.options.directory) {\n      this.traverseFileTree(e.dataTransfer.items);\n    } else {\n      const files = Array.prototype.slice.call(e.dataTransfer.files).filter(file => this.attrAccept(file, this.options.accept));\n      if (files.length) {\n        this.uploadFiles(files);\n      }\n    }\n    e.preventDefault();\n  }\n  onChange(e) {\n    if (this.options.disabled) {\n      return;\n    }\n    const hie = e.target;\n    this.uploadFiles(hie.files);\n    hie.value = '';\n  }\n  traverseFileTree(files) {\n    const _traverseFileTree = (item, path) => {\n      if (item.isFile) {\n        item.file(file => {\n          if (this.attrAccept(file, this.options.accept)) {\n            this.uploadFiles([file]);\n          }\n        });\n      } else if (item.isDirectory) {\n        const dirReader = item.createReader();\n        dirReader.readEntries(entries => {\n          for (const entrieItem of entries) {\n            _traverseFileTree(entrieItem, `${path}${item.name}/`);\n          }\n        });\n      }\n    };\n    for (const file of files) {\n      _traverseFileTree(file.webkitGetAsEntry(), '');\n    }\n  }\n  attrAccept(file, acceptedFiles) {\n    if (file && acceptedFiles) {\n      const acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n      const fileName = `${file.name}`;\n      const mimeType = `${file.type}`;\n      const baseMimeType = mimeType.replace(/\\/.*$/, '');\n      return acceptedFilesArray.some(type => {\n        const validType = type.trim();\n        if (validType.charAt(0) === '.') {\n          return fileName.toLowerCase().indexOf(validType.toLowerCase(), fileName.toLowerCase().length - validType.toLowerCase().length) !== -1;\n        } else if (/\\/\\*$/.test(validType)) {\n          // This is something like a image/* mime type\n          return baseMimeType === validType.replace(/\\/.*$/, '');\n        }\n        return mimeType === validType;\n      });\n    }\n    return true;\n  }\n  attachUid(file) {\n    if (!file.uid) {\n      file.uid = Math.random().toString(36).substring(2);\n    }\n    return file;\n  }\n  uploadFiles(fileList) {\n    let filters$ = of(Array.prototype.slice.call(fileList));\n    if (this.options.filters) {\n      this.options.filters.forEach(f => {\n        filters$ = filters$.pipe(switchMap(list => {\n          const fnRes = f.fn(list);\n          return fnRes instanceof Observable ? fnRes : of(fnRes);\n        }));\n      });\n    }\n    filters$.subscribe(list => {\n      list.forEach(file => {\n        this.attachUid(file);\n        this.upload(file, list);\n      });\n    }, e => {\n      warn(`Unhandled upload filter error`, e);\n    });\n  }\n  upload(file, fileList) {\n    if (!this.options.beforeUpload) {\n      return this.post(file);\n    }\n    const before = this.options.beforeUpload(file, fileList);\n    if (before instanceof Observable) {\n      before.subscribe(processedFile => {\n        const processedFileType = Object.prototype.toString.call(processedFile);\n        if (processedFileType === '[object File]' || processedFileType === '[object Blob]') {\n          this.attachUid(processedFile);\n          this.post(processedFile);\n        } else if (typeof processedFile === 'boolean' && processedFile !== false) {\n          this.post(file);\n        }\n      }, e => {\n        warn(`Unhandled upload beforeUpload error`, e);\n      });\n    } else if (before !== false) {\n      return this.post(file);\n    }\n  }\n  post(file) {\n    if (this.destroy) {\n      return;\n    }\n    let process$ = of(file);\n    let transformedFile;\n    const opt = this.options;\n    const {\n      uid\n    } = file;\n    const {\n      action,\n      data,\n      headers,\n      transformFile\n    } = opt;\n    const args = {\n      action: typeof action === 'string' ? action : '',\n      name: opt.name,\n      headers,\n      file,\n      postFile: file,\n      data,\n      withCredentials: opt.withCredentials,\n      onProgress: opt.onProgress ? e => {\n        opt.onProgress(e, file);\n      } : undefined,\n      onSuccess: (ret, xhr) => {\n        this.clean(uid);\n        opt.onSuccess(ret, file, xhr);\n      },\n      onError: xhr => {\n        this.clean(uid);\n        opt.onError(xhr, file);\n      }\n    };\n    if (typeof action === 'function') {\n      const actionResult = action(file);\n      if (actionResult instanceof Observable) {\n        process$ = process$.pipe(switchMap(() => actionResult), map(res => {\n          args.action = res;\n          return file;\n        }));\n      } else {\n        args.action = actionResult;\n      }\n    }\n    if (typeof transformFile === 'function') {\n      const transformResult = transformFile(file);\n      process$ = process$.pipe(switchMap(() => transformResult instanceof Observable ? transformResult : of(transformResult)), tap(newFile => transformedFile = newFile));\n    }\n    if (typeof data === 'function') {\n      const dataResult = data(file);\n      if (dataResult instanceof Observable) {\n        process$ = process$.pipe(switchMap(() => dataResult), map(res => {\n          args.data = res;\n          return transformedFile ?? file;\n        }));\n      } else {\n        args.data = dataResult;\n      }\n    }\n    if (typeof headers === 'function') {\n      const headersResult = headers(file);\n      if (headersResult instanceof Observable) {\n        process$ = process$.pipe(switchMap(() => headersResult), map(res => {\n          args.headers = res;\n          return transformedFile ?? file;\n        }));\n      } else {\n        args.headers = headersResult;\n      }\n    }\n    process$.subscribe(newFile => {\n      args.postFile = newFile;\n      const req$ = (opt.customRequest || this.xhr).call(this, args);\n      if (!(req$ instanceof Subscription)) {\n        warn(`Must return Subscription type in '[nzCustomRequest]' property`);\n      }\n      this.reqs[uid] = req$;\n      opt.onStart(file);\n    });\n  }\n  xhr(args) {\n    const formData = new FormData();\n    if (args.data) {\n      Object.keys(args.data).map(key => {\n        formData.append(key, args.data[key]);\n      });\n    }\n    formData.append(args.name, args.postFile);\n    if (!args.headers) {\n      args.headers = {};\n    }\n    if (args.headers['X-Requested-With'] !== null) {\n      args.headers['X-Requested-With'] = `XMLHttpRequest`;\n    } else {\n      delete args.headers['X-Requested-With'];\n    }\n    const req = new HttpRequest('POST', args.action, formData, {\n      reportProgress: true,\n      withCredentials: args.withCredentials,\n      headers: new HttpHeaders(args.headers)\n    });\n    return this.http.request(req).subscribe(event => {\n      if (event.type === HttpEventType.UploadProgress) {\n        if (event.total > 0) {\n          event.percent = event.loaded / event.total * 100;\n        }\n        args.onProgress(event, args.file);\n      } else if (event instanceof HttpResponse) {\n        args.onSuccess(event.body, args.file, event);\n      }\n    }, err => {\n      this.abort(args.file);\n      args.onError(err, args.file);\n    });\n  }\n  clean(uid) {\n    const req$ = this.reqs[uid];\n    if (req$ instanceof Subscription) {\n      req$.unsubscribe();\n    }\n    delete this.reqs[uid];\n  }\n  abort(file) {\n    if (file) {\n      this.clean(file && file.uid);\n    } else {\n      Object.keys(this.reqs).forEach(uid => this.clean(uid));\n    }\n  }\n  constructor(ngZone, http, elementRef) {\n    this.ngZone = ngZone;\n    this.http = http;\n    this.elementRef = elementRef;\n    this.reqs = {};\n    this.destroy = false;\n    this.destroy$ = new Subject();\n    if (!http) {\n      throw new Error(`Not found 'HttpClient', You can import 'HttpClientModule' in your root module.`);\n    }\n  }\n  ngOnInit() {\n    // Caretaker note: `input[type=file].click()` will open a native OS file picker,\n    // it doesn't require Angular to run `ApplicationRef.tick()`.\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.elementRef.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(() => this.onClick());\n      fromEvent(this.elementRef.nativeElement, 'keydown').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        if (this.options.disabled) {\n          return;\n        }\n        if (event.key === 'Enter' || event.keyCode === ENTER) {\n          this.onClick();\n        }\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.destroy = true;\n    this.destroy$.next();\n    this.abort();\n  }\n  static {\n    this.ɵfac = function NzUploadBtnComponent_Factory(t) {\n      return new (t || NzUploadBtnComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.HttpClient, 8), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzUploadBtnComponent,\n      selectors: [[\"\", \"nz-upload-btn\", \"\"]],\n      viewQuery: function NzUploadBtnComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.file = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-upload\"],\n      hostVars: 4,\n      hostBindings: function NzUploadBtnComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"drop\", function NzUploadBtnComponent_drop_HostBindingHandler($event) {\n            return ctx.onFileDrop($event);\n          })(\"dragover\", function NzUploadBtnComponent_dragover_HostBindingHandler($event) {\n            return ctx.onFileDrop($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"tabindex\", \"0\")(\"role\", \"button\");\n          i0.ɵɵclassProp(\"ant-upload-disabled\", ctx.options.disabled);\n        }\n      },\n      inputs: {\n        options: \"options\"\n      },\n      exportAs: [\"nzUploadBtn\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c1,\n      ngContentSelectors: _c2,\n      decls: 3,\n      vars: 6,\n      consts: [[\"file\", \"\"], [\"type\", \"file\", 3, \"change\", \"multiple\"]],\n      template: function NzUploadBtnComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"input\", 1, 0);\n          i0.ɵɵlistener(\"change\", function NzUploadBtnComponent_Template_input_change_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(2);\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"display\", \"none\");\n          i0.ɵɵproperty(\"multiple\", ctx.options.multiple);\n          i0.ɵɵattribute(\"accept\", ctx.options.accept)(\"directory\", ctx.options.directory ? \"directory\" : null)(\"webkitdirectory\", ctx.options.directory ? \"webkitdirectory\" : null);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzUploadBtnComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-upload-btn]',\n      exportAs: 'nzUploadBtn',\n      host: {\n        class: 'ant-upload',\n        '[attr.tabindex]': '\"0\"',\n        '[attr.role]': '\"button\"',\n        '[class.ant-upload-disabled]': 'options.disabled',\n        '(drop)': 'onFileDrop($event)',\n        '(dragover)': 'onFileDrop($event)'\n      },\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      template: \"<!--\\n  We explicitly bind `style.display` to avoid using an inline style\\n  attribute property (which is not allowed when CSP `unsafe-inline`\\n  is not specified).\\n-->\\n<input\\n  type=\\\"file\\\"\\n  #file\\n  (change)=\\\"onChange($event)\\\"\\n  [attr.accept]=\\\"options.accept\\\"\\n  [attr.directory]=\\\"options.directory ? 'directory' : null\\\"\\n  [attr.webkitdirectory]=\\\"options.directory ? 'webkitdirectory' : null\\\"\\n  [multiple]=\\\"options.multiple\\\"\\n  [style.display]=\\\"'none'\\\"\\n/>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i1.HttpClient,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.ElementRef\n  }], {\n    file: [{\n      type: ViewChild,\n      args: ['file', {\n        static: true\n      }]\n    }],\n    options: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst isImageFileType = type => !!type && type.indexOf('image/') === 0;\nconst MEASURE_SIZE = 200;\nclass NzUploadListComponent {\n  get showPic() {\n    return this.listType === 'picture' || this.listType === 'picture-card';\n  }\n  set items(list) {\n    this.list = list;\n  }\n  genErr(file) {\n    if (file.response && typeof file.response === 'string') {\n      return file.response;\n    }\n    return file.error && file.error.statusText || this.locale.uploadError;\n  }\n  extname(url) {\n    const temp = url.split('/');\n    const filename = temp[temp.length - 1];\n    const filenameWithoutSuffix = filename.split(/#|\\?/)[0];\n    return (/\\.[^./\\\\]*$/.exec(filenameWithoutSuffix) || [''])[0];\n  }\n  isImageUrl(file) {\n    if (isImageFileType(file.type)) {\n      return true;\n    }\n    const url = file.thumbUrl || file.url || '';\n    if (!url) {\n      return false;\n    }\n    const extension = this.extname(url);\n    if (/^data:image\\//.test(url) || /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg)$/i.test(extension)) {\n      return true;\n    } else if (/^data:/.test(url)) {\n      // other file types of base64\n      return false;\n    } else if (extension) {\n      // other file types which have extension\n      return false;\n    }\n    return true;\n  }\n  getIconType(file) {\n    if (!this.showPic) {\n      return '';\n    }\n    if (file.isUploading || !file.thumbUrl && !file.url) {\n      return 'uploading';\n    } else {\n      return 'thumbnail';\n    }\n  }\n  previewImage(file) {\n    if (!isImageFileType(file.type) || !this.platform.isBrowser) {\n      return of('');\n    }\n    const canvas = this.doc.createElement('canvas');\n    canvas.width = MEASURE_SIZE;\n    canvas.height = MEASURE_SIZE;\n    canvas.style.cssText = `position: fixed; left: 0; top: 0; width: ${MEASURE_SIZE}px; height: ${MEASURE_SIZE}px; z-index: 9999; display: none;`;\n    this.doc.body.appendChild(canvas);\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n    const objectUrl = URL.createObjectURL(file);\n    img.src = objectUrl;\n    return fromEvent(img, 'load').pipe(map(() => {\n      const {\n        width,\n        height\n      } = img;\n      let drawWidth = MEASURE_SIZE;\n      let drawHeight = MEASURE_SIZE;\n      let offsetX = 0;\n      let offsetY = 0;\n      if (width < height) {\n        drawHeight = height * (MEASURE_SIZE / width);\n        offsetY = -(drawHeight - drawWidth) / 2;\n      } else {\n        drawWidth = width * (MEASURE_SIZE / height);\n        offsetX = -(drawWidth - drawHeight) / 2;\n      }\n      try {\n        ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);\n      } catch {}\n      const dataURL = canvas.toDataURL();\n      this.doc.body.removeChild(canvas);\n      URL.revokeObjectURL(objectUrl);\n      return dataURL;\n    }));\n  }\n  genThumb() {\n    if (!this.platform.isBrowser) {\n      return;\n    }\n    const win = window;\n    if (!this.showPic || typeof document === 'undefined' || typeof win === 'undefined' || !win.FileReader || !win.File) {\n      return;\n    }\n    this.list.filter(file => file.originFileObj instanceof File && file.thumbUrl === undefined).forEach(file => {\n      file.thumbUrl = '';\n      // Caretaker note: we shouldn't use promises here since they're not cancellable.\n      // A promise microtask can be resolved after the view is destroyed. Thus running `detectChanges()`\n      // will cause a runtime exception (`detectChanges()` cannot be run on destroyed views).\n      const dataUrl$ = (this.previewFile ? this.previewFile(file) : this.previewImage(file.originFileObj)).pipe(takeUntil(this.destroy$));\n      this.ngZone.runOutsideAngular(() => {\n        dataUrl$.subscribe(dataUrl => {\n          this.ngZone.run(() => {\n            file.thumbUrl = dataUrl;\n            this.detectChanges();\n          });\n        });\n      });\n    });\n  }\n  showDownload(file) {\n    return !!(this.icons.showDownloadIcon && file.status === 'done');\n  }\n  fixData() {\n    this.list.forEach(file => {\n      file.isUploading = file.status === 'uploading';\n      file.message = this.genErr(file);\n      file.linkProps = typeof file.linkProps === 'string' ? JSON.parse(file.linkProps) : file.linkProps;\n      file.isImageUrl = this.previewIsImage ? this.previewIsImage(file) : this.isImageUrl(file);\n      file.iconType = this.getIconType(file);\n      file.showDownload = this.showDownload(file);\n    });\n  }\n  handlePreview(file, e) {\n    if (!this.onPreview) {\n      return;\n    }\n    e.preventDefault();\n    return this.onPreview(file);\n  }\n  handleRemove(file, e) {\n    e.preventDefault();\n    if (this.onRemove) {\n      this.onRemove(file);\n    }\n    return;\n  }\n  handleDownload(file) {\n    if (typeof this.onDownload === 'function') {\n      this.onDownload(file);\n    } else if (file.url) {\n      window.open(file.url);\n    }\n  }\n  // #endregion\n  constructor(cdr, doc, ngZone, platform) {\n    this.cdr = cdr;\n    this.doc = doc;\n    this.ngZone = ngZone;\n    this.platform = platform;\n    this.list = [];\n    this.locale = {};\n    this.iconRender = null;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  detectChanges() {\n    this.fixData();\n    this.cdr.detectChanges();\n  }\n  ngOnChanges() {\n    this.fixData();\n    this.genThumb();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n  }\n  static {\n    this.ɵfac = function NzUploadListComponent_Factory(t) {\n      return new (t || NzUploadListComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.Platform));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzUploadListComponent,\n      selectors: [[\"nz-upload-list\"]],\n      hostAttrs: [1, \"ant-upload-list\"],\n      hostVars: 8,\n      hostBindings: function NzUploadListComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-upload-list-rtl\", ctx.dir === \"rtl\")(\"ant-upload-list-text\", ctx.listType === \"text\")(\"ant-upload-list-picture\", ctx.listType === \"picture\")(\"ant-upload-list-picture-card\", ctx.listType === \"picture-card\");\n        }\n      },\n      inputs: {\n        locale: \"locale\",\n        listType: \"listType\",\n        items: \"items\",\n        icons: \"icons\",\n        onPreview: \"onPreview\",\n        onRemove: \"onRemove\",\n        onDownload: \"onDownload\",\n        previewFile: \"previewFile\",\n        previewIsImage: \"previewIsImage\",\n        iconRender: \"iconRender\",\n        dir: \"dir\"\n      },\n      exportAs: [\"nzUploadList\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[\"icon\", \"\"], [\"iconNode\", \"\"], [\"removeIcon\", \"\"], [\"downloadIcon\", \"\"], [\"downloadOrDelete\", \"\"], [\"preview\", \"\"], [\"noImageThumbTpl\", \"\"], [\"customIconRender\", \"\"], [\"iconNodeFileIcon\", \"\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [\"nz-tooltip\", \"\", 3, \"nzTooltipTitle\"], [1, \"ant-upload-list-item-info\"], [1, \"ant-upload-span\"], [3, \"ngTemplateOutlet\"], [\"class\", \"ant-upload-list-item-actions\", 4, \"ngIf\"], [\"class\", \"ant-upload-list-item-progress\", 4, \"ngIf\"], [3, \"ngSwitch\"], [\"class\", \"ant-upload-list-item-thumbnail\", 3, \"ant-upload-list-item-file\", 4, \"ngSwitchCase\"], [\"class\", \"ant-upload-list-item-thumbnail\", \"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"ant-upload-list-item-file\", \"href\", \"click\", 4, \"ngSwitchCase\"], [\"class\", \"ant-upload-text-icon\", 4, \"ngSwitchDefault\"], [1, \"ant-upload-list-item-thumbnail\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"ant-upload-list-item-thumbnail\", 3, \"click\", \"href\"], [\"class\", \"ant-upload-list-item-image\", 3, \"src\", 4, \"ngIf\", \"ngIfElse\"], [1, \"ant-upload-list-item-image\", 3, \"src\"], [1, \"ant-upload-text-icon\"], [4, \"ngIf\", \"ngIfElse\"], [4, \"ngSwitchCase\"], [\"nz-icon\", \"\", 3, \"nzType\", 4, \"ngSwitchDefault\"], [\"nz-icon\", \"\", \"nzType\", \"loading\"], [\"nz-icon\", \"\", 3, \"nzType\"], [\"nz-icon\", \"\", \"nzTheme\", \"twotone\", 3, \"nzType\"], [\"type\", \"button\", \"nz-button\", \"\", \"nzType\", \"text\", \"nzSize\", \"small\", \"class\", \"ant-upload-list-item-card-actions-btn\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"nz-button\", \"\", \"nzType\", \"text\", \"nzSize\", \"small\", 1, \"ant-upload-list-item-card-actions-btn\", 3, \"click\"], [\"nz-icon\", \"\", \"nzType\", \"delete\"], [\"nz-icon\", \"\", \"nzType\", \"download\"], [3, \"class\", 4, \"ngIf\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", \"class\", \"ant-upload-list-item-name\", 3, \"href\", \"click\", 4, \"ngIf\"], [\"class\", \"ant-upload-list-item-name\", 3, \"click\", 4, \"ngIf\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"ant-upload-list-item-name\", 3, \"click\", \"href\"], [1, \"ant-upload-list-item-name\", 3, \"click\"], [1, \"ant-upload-list-item-actions\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"href\", \"ngStyle\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"click\", \"href\", \"ngStyle\"], [\"nz-icon\", \"\", \"nzType\", \"eye\"], [1, \"ant-upload-list-item-progress\"], [\"nzType\", \"line\", 3, \"nzPercent\", \"nzShowInfo\", \"nzStrokeWidth\"]],\n      template: function NzUploadListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzUploadListComponent_div_0_Template, 20, 14, \"div\", 9);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngForOf\", ctx.list);\n        }\n      },\n      dependencies: [NgForOf, NzToolTipModule, i2.NzTooltipDirective, NgSwitch, NgTemplateOutlet, NgIf, NgSwitchDefault, NgSwitchCase, NzIconModule, i3.NzIconDirective, NzButtonModule, i4.NzButtonComponent, i5.ɵNzTransitionPatchDirective, NgStyle, NzProgressModule, i6.NzProgressComponent],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('itemState', [transition(':enter', [style({\n          height: '0',\n          width: '0',\n          opacity: 0\n        }), animate(150, style({\n          height: '*',\n          width: '*',\n          opacity: 1\n        }))]), transition(':leave', [animate(150, style({\n          height: '0',\n          width: '0',\n          opacity: 0\n        }))])])]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzUploadListComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-upload-list',\n      exportAs: 'nzUploadList',\n      animations: [trigger('itemState', [transition(':enter', [style({\n        height: '0',\n        width: '0',\n        opacity: 0\n      }), animate(150, style({\n        height: '*',\n        width: '*',\n        opacity: 1\n      }))]), transition(':leave', [animate(150, style({\n        height: '0',\n        width: '0',\n        opacity: 0\n      }))])])],\n      host: {\n        class: 'ant-upload-list',\n        '[class.ant-upload-list-rtl]': `dir === 'rtl'`,\n        '[class.ant-upload-list-text]': `listType === 'text'`,\n        '[class.ant-upload-list-picture]': `listType === 'picture'`,\n        '[class.ant-upload-list-picture-card]': `listType === 'picture-card'`\n      },\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NgForOf, NzToolTipModule, NgSwitch, NgTemplateOutlet, NgIf, NgSwitchDefault, NgSwitchCase, NzIconModule, NzButtonModule, NgStyle, NzProgressModule],\n      standalone: true,\n      template: \"<div *ngFor=\\\"let file of list\\\" class=\\\"ant-upload-list-{{ listType }}-container\\\">\\n  <div\\n    class=\\\"ant-upload-list-item ant-upload-list-item-{{ file.status }} ant-upload-list-item-list-type-{{ listType }}\\\"\\n    [attr.data-key]=\\\"file.key\\\"\\n    @itemState\\n    nz-tooltip\\n    [nzTooltipTitle]=\\\"file.status === 'error' ? file.message : null\\\"\\n  >\\n    <ng-template #icon>\\n      <ng-container [ngSwitch]=\\\"file.iconType\\\">\\n        <div\\n          *ngSwitchCase=\\\"'uploading'\\\"\\n          class=\\\"ant-upload-list-item-thumbnail\\\"\\n          [class.ant-upload-list-item-file]=\\\"!file.isUploading\\\"\\n        >\\n          <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n        </div>\\n        <a\\n          *ngSwitchCase=\\\"'thumbnail'\\\"\\n          class=\\\"ant-upload-list-item-thumbnail\\\"\\n          [class.ant-upload-list-item-file]=\\\"!file.isImageUrl\\\"\\n          target=\\\"_blank\\\"\\n          rel=\\\"noopener noreferrer\\\"\\n          [href]=\\\"file.url || file.thumbUrl\\\"\\n          (click)=\\\"handlePreview(file, $event)\\\"\\n        >\\n          <img\\n            *ngIf=\\\"file.isImageUrl; else noImageThumbTpl\\\"\\n            class=\\\"ant-upload-list-item-image\\\"\\n            [src]=\\\"file.thumbUrl || file.url\\\"\\n            [attr.alt]=\\\"file.name\\\"\\n          />\\n        </a>\\n        <div *ngSwitchDefault class=\\\"ant-upload-text-icon\\\">\\n          <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n        </div>\\n      </ng-container>\\n      <ng-template #noImageThumbTpl>\\n        <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n      </ng-template>\\n    </ng-template>\\n    <ng-template #iconNode let-file>\\n      <ng-container *ngIf=\\\"!iconRender; else customIconRender\\\">\\n        <ng-container [ngSwitch]=\\\"listType\\\">\\n          <ng-container *ngSwitchCase=\\\"'picture'\\\">\\n            <ng-container *ngIf=\\\"file.isUploading; else iconNodeFileIcon\\\">\\n              <span nz-icon nzType=\\\"loading\\\"></span>\\n            </ng-container>\\n          </ng-container>\\n          <ng-container *ngSwitchCase=\\\"'picture-card'\\\">\\n            <ng-container *ngIf=\\\"file.isUploading; else iconNodeFileIcon\\\">\\n              {{ locale.uploading }}\\n            </ng-container>\\n          </ng-container>\\n          <span *ngSwitchDefault nz-icon [nzType]=\\\"file.isUploading ? 'loading' : 'paper-clip'\\\"></span>\\n        </ng-container>\\n      </ng-container>\\n      <ng-template\\n        #customIconRender\\n        [ngTemplateOutlet]=\\\"iconRender\\\"\\n        [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"\\n      ></ng-template>\\n      <ng-template #iconNodeFileIcon>\\n        <span nz-icon [nzType]=\\\"file.isImageUrl ? 'picture' : 'file'\\\" nzTheme=\\\"twotone\\\"></span>\\n      </ng-template>\\n    </ng-template>\\n    <ng-template #removeIcon>\\n      <button\\n        *ngIf=\\\"icons.showRemoveIcon\\\"\\n        type=\\\"button\\\"\\n        nz-button\\n        nzType=\\\"text\\\"\\n        nzSize=\\\"small\\\"\\n        (click)=\\\"handleRemove(file, $event)\\\"\\n        [attr.title]=\\\"locale.removeFile\\\"\\n        class=\\\"ant-upload-list-item-card-actions-btn\\\"\\n      >\\n        <span nz-icon nzType=\\\"delete\\\"></span>\\n      </button>\\n    </ng-template>\\n    <ng-template #downloadIcon>\\n      <button\\n        *ngIf=\\\"file.showDownload\\\"\\n        type=\\\"button\\\"\\n        nz-button\\n        nzType=\\\"text\\\"\\n        nzSize=\\\"small\\\"\\n        (click)=\\\"handleDownload(file)\\\"\\n        [attr.title]=\\\"locale.downloadFile\\\"\\n        class=\\\"ant-upload-list-item-card-actions-btn\\\"\\n      >\\n        <span nz-icon nzType=\\\"download\\\"></span>\\n      </button>\\n    </ng-template>\\n    <ng-template #downloadOrDelete>\\n      <span\\n        *ngIf=\\\"listType !== 'picture-card'\\\"\\n        class=\\\"ant-upload-list-item-card-actions {{ listType === 'picture' ? 'picture' : '' }}\\\"\\n      >\\n        <ng-template [ngTemplateOutlet]=\\\"downloadIcon\\\"></ng-template>\\n        <ng-template [ngTemplateOutlet]=\\\"removeIcon\\\"></ng-template>\\n      </span>\\n    </ng-template>\\n    <ng-template #preview>\\n      <a\\n        *ngIf=\\\"file.url\\\"\\n        target=\\\"_blank\\\"\\n        rel=\\\"noopener noreferrer\\\"\\n        class=\\\"ant-upload-list-item-name\\\"\\n        [attr.title]=\\\"file.name\\\"\\n        [href]=\\\"file.url\\\"\\n        [attr.download]=\\\"file.linkProps && file.linkProps.download\\\"\\n        (click)=\\\"handlePreview(file, $event)\\\"\\n      >\\n        {{ file.name }}\\n      </a>\\n      <span\\n        *ngIf=\\\"!file.url\\\"\\n        class=\\\"ant-upload-list-item-name\\\"\\n        [attr.title]=\\\"file.name\\\"\\n        (click)=\\\"handlePreview(file, $event)\\\"\\n      >\\n        {{ file.name }}\\n      </span>\\n      <ng-template [ngTemplateOutlet]=\\\"downloadOrDelete\\\"></ng-template>\\n    </ng-template>\\n    <div class=\\\"ant-upload-list-item-info\\\">\\n      <span class=\\\"ant-upload-span\\\">\\n        <ng-template [ngTemplateOutlet]=\\\"icon\\\"></ng-template>\\n        <ng-template [ngTemplateOutlet]=\\\"preview\\\"></ng-template>\\n      </span>\\n    </div>\\n    <span *ngIf=\\\"listType === 'picture-card' && !file.isUploading\\\" class=\\\"ant-upload-list-item-actions\\\">\\n      <a\\n        *ngIf=\\\"icons.showPreviewIcon\\\"\\n        [href]=\\\"file.url || file.thumbUrl\\\"\\n        target=\\\"_blank\\\"\\n        rel=\\\"noopener noreferrer\\\"\\n        [attr.title]=\\\"locale.previewFile\\\"\\n        [ngStyle]=\\\"!(file.url || file.thumbUrl) ? { opacity: 0.5, 'pointer-events': 'none' } : null\\\"\\n        (click)=\\\"handlePreview(file, $event)\\\"\\n      >\\n        <span nz-icon nzType=\\\"eye\\\"></span>\\n      </a>\\n      <ng-container *ngIf=\\\"file.status === 'done'\\\">\\n        <ng-template [ngTemplateOutlet]=\\\"downloadIcon\\\"></ng-template>\\n      </ng-container>\\n      <ng-template [ngTemplateOutlet]=\\\"removeIcon\\\"></ng-template>\\n    </span>\\n    <div *ngIf=\\\"file.isUploading\\\" class=\\\"ant-upload-list-item-progress\\\">\\n      <nz-progress [nzPercent]=\\\"file.percent!\\\" nzType=\\\"line\\\" [nzShowInfo]=\\\"false\\\" [nzStrokeWidth]=\\\"2\\\"></nz-progress>\\n    </div>\\n  </div>\\n</div>\\n\"\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$1.Platform\n  }], {\n    locale: [{\n      type: Input\n    }],\n    listType: [{\n      type: Input\n    }],\n    items: [{\n      type: Input\n    }],\n    icons: [{\n      type: Input\n    }],\n    onPreview: [{\n      type: Input\n    }],\n    onRemove: [{\n      type: Input\n    }],\n    onDownload: [{\n      type: Input\n    }],\n    previewFile: [{\n      type: Input\n    }],\n    previewIsImage: [{\n      type: Input\n    }],\n    iconRender: [{\n      type: Input\n    }],\n    dir: [{\n      type: Input\n    }]\n  });\n})();\nclass NzUploadComponent {\n  set nzShowUploadList(value) {\n    this._showUploadList = typeof value === 'boolean' ? toBoolean(value) : value;\n  }\n  get nzShowUploadList() {\n    return this._showUploadList;\n  }\n  zipOptions() {\n    if (typeof this.nzShowUploadList === 'boolean' && this.nzShowUploadList) {\n      this.nzShowUploadList = {\n        showPreviewIcon: true,\n        showRemoveIcon: true,\n        showDownloadIcon: true\n      };\n    }\n    // filters\n    const filters = this.nzFilter.slice();\n    if (this.nzMultiple && this.nzLimit > 0 && filters.findIndex(w => w.name === 'limit') === -1) {\n      filters.push({\n        name: 'limit',\n        fn: fileList => fileList.slice(-this.nzLimit)\n      });\n    }\n    if (this.nzSize > 0 && filters.findIndex(w => w.name === 'size') === -1) {\n      filters.push({\n        name: 'size',\n        fn: fileList => fileList.filter(w => w.size / 1024 <= this.nzSize)\n      });\n    }\n    if (this.nzFileType && this.nzFileType.length > 0 && filters.findIndex(w => w.name === 'type') === -1) {\n      const types = this.nzFileType.split(',');\n      filters.push({\n        name: 'type',\n        fn: fileList => fileList.filter(w => ~types.indexOf(w.type))\n      });\n    }\n    this._btnOptions = {\n      disabled: this.nzDisabled,\n      accept: this.nzAccept,\n      action: this.nzAction,\n      directory: this.nzDirectory,\n      openFileDialogOnClick: this.nzOpenFileDialogOnClick,\n      beforeUpload: this.nzBeforeUpload,\n      customRequest: this.nzCustomRequest,\n      data: this.nzData,\n      headers: this.nzHeaders,\n      name: this.nzName,\n      multiple: this.nzMultiple,\n      withCredentials: this.nzWithCredentials,\n      filters,\n      transformFile: this.nzTransformFile,\n      onStart: this.onStart,\n      onProgress: this.onProgress,\n      onSuccess: this.onSuccess,\n      onError: this.onError\n    };\n    return this;\n  }\n  // #endregion\n  constructor(ngZone, document, cdr, i18n, directionality) {\n    this.ngZone = ngZone;\n    this.document = document;\n    this.cdr = cdr;\n    this.i18n = i18n;\n    this.directionality = directionality;\n    this.destroy$ = new Subject();\n    this.dir = 'ltr';\n    // #region fields\n    this.nzType = 'select';\n    this.nzLimit = 0;\n    this.nzSize = 0;\n    this.nzDirectory = false;\n    this.nzOpenFileDialogOnClick = true;\n    this.nzFilter = [];\n    this.nzFileList = [];\n    this.nzDisabled = false;\n    this.nzListType = 'text';\n    this.nzMultiple = false;\n    this.nzName = 'file';\n    this._showUploadList = true;\n    this.nzShowButton = true;\n    this.nzWithCredentials = false;\n    this.nzIconRender = null;\n    this.nzFileListRender = null;\n    this.nzChange = new EventEmitter();\n    this.nzFileListChange = new EventEmitter();\n    this.onStart = file => {\n      if (!this.nzFileList) {\n        this.nzFileList = [];\n      }\n      const targetItem = this.fileToObject(file);\n      targetItem.status = 'uploading';\n      this.nzFileList = this.nzFileList.concat(targetItem);\n      this.nzFileListChange.emit(this.nzFileList);\n      this.nzChange.emit({\n        file: targetItem,\n        fileList: this.nzFileList,\n        type: 'start'\n      });\n      this.detectChangesList();\n    };\n    this.onProgress = (e, file) => {\n      const fileList = this.nzFileList;\n      const targetItem = this.getFileItem(file, fileList);\n      targetItem.percent = e.percent;\n      this.nzChange.emit({\n        event: e,\n        file: {\n          ...targetItem\n        },\n        fileList: this.nzFileList,\n        type: 'progress'\n      });\n      this.detectChangesList();\n    };\n    this.onSuccess = (res, file) => {\n      const fileList = this.nzFileList;\n      const targetItem = this.getFileItem(file, fileList);\n      targetItem.status = 'done';\n      targetItem.response = res;\n      this.nzChange.emit({\n        file: {\n          ...targetItem\n        },\n        fileList,\n        type: 'success'\n      });\n      this.detectChangesList();\n    };\n    this.onError = (err, file) => {\n      const fileList = this.nzFileList;\n      const targetItem = this.getFileItem(file, fileList);\n      targetItem.error = err;\n      targetItem.status = 'error';\n      this.nzChange.emit({\n        file: {\n          ...targetItem\n        },\n        fileList,\n        type: 'error'\n      });\n      this.detectChangesList();\n    };\n    this.onRemove = file => {\n      this.uploadComp.abort(file);\n      file.status = 'removed';\n      const fnRes = typeof this.nzRemove === 'function' ? this.nzRemove(file) : this.nzRemove == null ? true : this.nzRemove;\n      (fnRes instanceof Observable ? fnRes : of(fnRes)).pipe(filter(res => res)).subscribe(() => {\n        this.nzFileList = this.removeFileItem(file, this.nzFileList);\n        this.nzChange.emit({\n          file,\n          fileList: this.nzFileList,\n          type: 'removed'\n        });\n        this.nzFileListChange.emit(this.nzFileList);\n        this.cdr.detectChanges();\n      });\n    };\n    // #endregion\n    // #region styles\n    this.prefixCls = 'ant-upload';\n    this.classList = [];\n  }\n  // #region upload\n  fileToObject(file) {\n    return {\n      lastModified: file.lastModified,\n      lastModifiedDate: file.lastModifiedDate,\n      name: file.filename || file.name,\n      size: file.size,\n      type: file.type,\n      uid: file.uid,\n      response: file.response,\n      error: file.error,\n      percent: 0,\n      originFileObj: file\n    };\n  }\n  getFileItem(file, fileList) {\n    return fileList.filter(item => item.uid === file.uid)[0];\n  }\n  removeFileItem(file, fileList) {\n    return fileList.filter(item => item.uid !== file.uid);\n  }\n  // skip safari bug\n  fileDrop(e) {\n    if (e.type === this.dragState) {\n      return;\n    }\n    this.dragState = e.type;\n    this.setClassMap();\n  }\n  // #endregion\n  // #region list\n  detectChangesList() {\n    this.cdr.detectChanges();\n    this.listComp?.detectChanges();\n  }\n  setClassMap() {\n    let subCls = [];\n    if (this.nzType === 'drag') {\n      if (this.nzFileList.some(file => file.status === 'uploading')) {\n        subCls.push(`${this.prefixCls}-drag-uploading`);\n      }\n      if (this.dragState === 'dragover') {\n        subCls.push(`${this.prefixCls}-drag-hover`);\n      }\n    } else {\n      subCls = [`${this.prefixCls}-select-${this.nzListType}`];\n    }\n    this.classList = [this.prefixCls, `${this.prefixCls}-${this.nzType}`, ...subCls, this.nzDisabled && `${this.prefixCls}-disabled` || '', this.dir === 'rtl' && `${this.prefixCls}-rtl` || ''].filter(item => !!item);\n    this.cdr.detectChanges();\n  }\n  // #endregion\n  ngOnInit() {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.setClassMap();\n      this.cdr.detectChanges();\n    });\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Upload');\n      this.detectChangesList();\n    });\n  }\n  ngAfterViewInit() {\n    // fix firefox drop open new tab\n    this.ngZone.runOutsideAngular(() => fromEvent(this.document.body, 'drop').pipe(takeUntil(this.destroy$)).subscribe(event => {\n      event.preventDefault();\n      event.stopPropagation();\n    }));\n  }\n  ngOnChanges() {\n    this.zipOptions().setClassMap();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzUploadComponent_Factory(t) {\n      return new (t || NzUploadComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$2.NzI18nService), i0.ɵɵdirectiveInject(i2$1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzUploadComponent,\n      selectors: [[\"nz-upload\"]],\n      viewQuery: function NzUploadComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c5, 5);\n          i0.ɵɵviewQuery(_c6, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.uploadComp = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listComp = _t.first);\n        }\n      },\n      hostVars: 2,\n      hostBindings: function NzUploadComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-upload-picture-card-wrapper\", ctx.nzListType === \"picture-card\");\n        }\n      },\n      inputs: {\n        nzType: \"nzType\",\n        nzLimit: \"nzLimit\",\n        nzSize: \"nzSize\",\n        nzFileType: \"nzFileType\",\n        nzAccept: \"nzAccept\",\n        nzAction: \"nzAction\",\n        nzDirectory: \"nzDirectory\",\n        nzOpenFileDialogOnClick: \"nzOpenFileDialogOnClick\",\n        nzBeforeUpload: \"nzBeforeUpload\",\n        nzCustomRequest: \"nzCustomRequest\",\n        nzData: \"nzData\",\n        nzFilter: \"nzFilter\",\n        nzFileList: \"nzFileList\",\n        nzDisabled: \"nzDisabled\",\n        nzHeaders: \"nzHeaders\",\n        nzListType: \"nzListType\",\n        nzMultiple: \"nzMultiple\",\n        nzName: \"nzName\",\n        nzShowUploadList: \"nzShowUploadList\",\n        nzShowButton: \"nzShowButton\",\n        nzWithCredentials: \"nzWithCredentials\",\n        nzRemove: \"nzRemove\",\n        nzPreview: \"nzPreview\",\n        nzPreviewFile: \"nzPreviewFile\",\n        nzPreviewIsImage: \"nzPreviewIsImage\",\n        nzTransformFile: \"nzTransformFile\",\n        nzDownload: \"nzDownload\",\n        nzIconRender: \"nzIconRender\",\n        nzFileListRender: \"nzFileListRender\"\n      },\n      outputs: {\n        nzChange: \"nzChange\",\n        nzFileListChange: \"nzFileListChange\"\n      },\n      exportAs: [\"nzUpload\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 11,\n      vars: 2,\n      consts: [[\"list\", \"\"], [\"con\", \"\"], [\"btn\", \"\"], [\"select\", \"\"], [\"pic\", \"\"], [\"listComp\", \"\"], [\"uploadComp\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"display\", \"locale\", \"listType\", \"items\", \"icons\", \"iconRender\", \"previewFile\", \"previewIsImage\", \"onPreview\", \"onRemove\", \"onDownload\", \"dir\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"locale\", \"listType\", \"items\", \"icons\", \"iconRender\", \"previewFile\", \"previewIsImage\", \"onPreview\", \"onRemove\", \"onDownload\", \"dir\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"ngClass\"], [\"nz-upload-btn\", \"\", 3, \"options\"], [3, \"ngTemplateOutlet\"], [3, \"drop\", \"dragover\", \"dragleave\", \"ngClass\"], [\"nz-upload-btn\", \"\", 1, \"ant-upload-btn\", 3, \"options\"], [1, \"ant-upload-drag-container\"]],\n      template: function NzUploadComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzUploadComponent_ng_template_0_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, NzUploadComponent_ng_template_2_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(4, NzUploadComponent_ng_template_4_Template, 4, 5, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(6, NzUploadComponent_ng_container_6_Template, 7, 4, \"ng-container\", 7)(7, NzUploadComponent_ng_template_7_Template, 1, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(9, NzUploadComponent_ng_template_9_Template, 2, 2, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const select_r7 = i0.ɵɵreference(8);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.nzType === \"drag\")(\"ngIfElse\", select_r7);\n        }\n      },\n      dependencies: [NzUploadListComponent, NgIf, NgTemplateOutlet, NgClass, NzUploadBtnComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputNumber()], NzUploadComponent.prototype, \"nzLimit\", void 0);\n__decorate([InputNumber()], NzUploadComponent.prototype, \"nzSize\", void 0);\n__decorate([InputBoolean()], NzUploadComponent.prototype, \"nzDirectory\", void 0);\n__decorate([InputBoolean()], NzUploadComponent.prototype, \"nzOpenFileDialogOnClick\", void 0);\n__decorate([InputBoolean()], NzUploadComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzUploadComponent.prototype, \"nzMultiple\", void 0);\n__decorate([InputBoolean()], NzUploadComponent.prototype, \"nzShowButton\", void 0);\n__decorate([InputBoolean()], NzUploadComponent.prototype, \"nzWithCredentials\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzUploadComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-upload',\n      exportAs: 'nzUpload',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class.ant-upload-picture-card-wrapper]': 'nzListType === \"picture-card\"'\n      },\n      imports: [NzUploadListComponent, NgIf, NgTemplateOutlet, NgClass, NzUploadBtnComponent],\n      standalone: true,\n      template: \"<ng-template #list>\\n  <nz-upload-list\\n    *ngIf=\\\"locale && !nzFileListRender\\\"\\n    #listComp\\n    [style.display]=\\\"nzShowUploadList ? '' : 'none'\\\"\\n    [locale]=\\\"locale\\\"\\n    [listType]=\\\"nzListType\\\"\\n    [items]=\\\"nzFileList || []\\\"\\n    [icons]=\\\"$any(nzShowUploadList)\\\"\\n    [iconRender]=\\\"nzIconRender\\\"\\n    [previewFile]=\\\"nzPreviewFile\\\"\\n    [previewIsImage]=\\\"nzPreviewIsImage\\\"\\n    [onPreview]=\\\"nzPreview\\\"\\n    [onRemove]=\\\"onRemove\\\"\\n    [onDownload]=\\\"nzDownload\\\"\\n    [dir]=\\\"dir\\\"\\n  ></nz-upload-list>\\n  <ng-container *ngIf=\\\"nzFileListRender\\\">\\n    <ng-container *ngTemplateOutlet=\\\"nzFileListRender; context: { $implicit: nzFileList }\\\"></ng-container>\\n  </ng-container>\\n</ng-template>\\n<ng-template #con><ng-content></ng-content></ng-template>\\n<ng-template #btn>\\n  <div [ngClass]=\\\"classList\\\" [style.display]=\\\"nzShowButton ? '' : 'none'\\\">\\n    <div nz-upload-btn #uploadComp [options]=\\\"_btnOptions!\\\">\\n      <ng-template [ngTemplateOutlet]=\\\"con\\\"></ng-template>\\n    </div>\\n  </div>\\n</ng-template>\\n<ng-container *ngIf=\\\"nzType === 'drag'; else select\\\">\\n  <div [ngClass]=\\\"classList\\\" (drop)=\\\"fileDrop($event)\\\" (dragover)=\\\"fileDrop($event)\\\" (dragleave)=\\\"fileDrop($event)\\\">\\n    <div nz-upload-btn #uploadComp [options]=\\\"_btnOptions!\\\" class=\\\"ant-upload-btn\\\">\\n      <div class=\\\"ant-upload-drag-container\\\">\\n        <ng-template [ngTemplateOutlet]=\\\"con\\\"></ng-template>\\n      </div>\\n    </div>\\n  </div>\\n  <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n</ng-container>\\n<ng-template #select>\\n  <ng-container *ngIf=\\\"nzListType === 'picture-card'; else pic\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n    <ng-template [ngTemplateOutlet]=\\\"btn\\\"></ng-template>\\n  </ng-container>\\n</ng-template>\\n<ng-template #pic>\\n  <ng-template [ngTemplateOutlet]=\\\"btn\\\"></ng-template>\\n  <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n</ng-template>\\n\"\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1$2.NzI18nService\n  }, {\n    type: i2$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    uploadComp: [{\n      type: ViewChild,\n      args: ['uploadComp', {\n        static: false\n      }]\n    }],\n    listComp: [{\n      type: ViewChild,\n      args: ['listComp', {\n        static: false\n      }]\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzLimit: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzFileType: [{\n      type: Input\n    }],\n    nzAccept: [{\n      type: Input\n    }],\n    nzAction: [{\n      type: Input\n    }],\n    nzDirectory: [{\n      type: Input\n    }],\n    nzOpenFileDialogOnClick: [{\n      type: Input\n    }],\n    nzBeforeUpload: [{\n      type: Input\n    }],\n    nzCustomRequest: [{\n      type: Input\n    }],\n    nzData: [{\n      type: Input\n    }],\n    nzFilter: [{\n      type: Input\n    }],\n    nzFileList: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzHeaders: [{\n      type: Input\n    }],\n    nzListType: [{\n      type: Input\n    }],\n    nzMultiple: [{\n      type: Input\n    }],\n    nzName: [{\n      type: Input\n    }],\n    nzShowUploadList: [{\n      type: Input\n    }],\n    nzShowButton: [{\n      type: Input\n    }],\n    nzWithCredentials: [{\n      type: Input\n    }],\n    nzRemove: [{\n      type: Input\n    }],\n    nzPreview: [{\n      type: Input\n    }],\n    nzPreviewFile: [{\n      type: Input\n    }],\n    nzPreviewIsImage: [{\n      type: Input\n    }],\n    nzTransformFile: [{\n      type: Input\n    }],\n    nzDownload: [{\n      type: Input\n    }],\n    nzIconRender: [{\n      type: Input\n    }],\n    nzFileListRender: [{\n      type: Input\n    }],\n    nzChange: [{\n      type: Output\n    }],\n    nzFileListChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzUploadModule {\n  static {\n    this.ɵfac = function NzUploadModule_Factory(t) {\n      return new (t || NzUploadModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzUploadModule,\n      imports: [NzUploadComponent, NzUploadBtnComponent, NzUploadListComponent],\n      exports: [NzUploadComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzUploadComponent, NzUploadListComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzUploadModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzUploadComponent, NzUploadBtnComponent, NzUploadListComponent],\n      exports: [NzUploadComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzUploadBtnComponent, NzUploadComponent, NzUploadListComponent, NzUploadModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,iBAAiB,EAAE;AAChC,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,SAAS;AAAA,EACT,kBAAkB;AACpB;AACA,SAAS,uEAAuE,IAAI,KAAK;AAAC;AAC1F,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,eAAe,EAAE;AAChH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,cAAiB,YAAY,CAAC;AACpC,IAAG,YAAY,6BAA6B,CAAC,QAAQ,WAAW;AAChE,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,WAAW,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,CAAC;AAAA,EAC/G;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,WAAW,OAAO,QAAQ,YAAY,QAAQ,KAAQ,aAAa;AACtE,IAAG,YAAY,OAAO,QAAQ,IAAI;AAAA,EACpC;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,0EAA0E,QAAQ;AAChH,MAAG,cAAc,GAAG;AACpB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,SAAS,MAAM,CAAC;AAAA,IAC7D,CAAC;AACD,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,OAAO,EAAE;AAC9F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,qBAAwB,YAAY,CAAC;AAC3C,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,YAAY,6BAA6B,CAAC,QAAQ,UAAU;AAC/D,IAAG,WAAW,QAAQ,QAAQ,OAAO,QAAQ,UAAa,aAAa;AACvE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,QAAQ,UAAU,EAAE,YAAY,kBAAkB;AAAA,EAC1E;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AAAC;AAC1F,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,eAAe,EAAE;AAChH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,cAAiB,YAAY,CAAC;AACpC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,WAAW,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,CAAC;AAAA,EAC/G;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAAC;AAClG,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gFAAgF,GAAG,GAAG,eAAe,EAAE;AAAA,EAC1H;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,cAAiB,YAAY,CAAC;AACpC,IAAG,WAAW,oBAAoB,WAAW,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,CAAC;AAAA,EAC/G;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,GAAG,EAAE;AAChC,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,OAAO,EAAE,EAAE,GAAG,wDAAwD,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,0DAA0D,GAAG,GAAG,OAAO,EAAE;AAClP,IAAG,sBAAsB;AACzB,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,EAC5I;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,WAAW,YAAY,QAAQ,QAAQ;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,WAAW;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,WAAW;AAAA,EAC3C;AACF;AACA,SAAS,gGAAgG,IAAI,KAAK;AAChH,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,sBAAsB;AAAA,EAC3B;AACF;AACA,SAAS,iFAAiF,IAAI,KAAK;AACjG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,iGAAiG,GAAG,GAAG,gBAAgB,EAAE;AAC1I,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,sBAAyB,YAAY,CAAC;AAC5C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,QAAQ,WAAW,EAAE,YAAY,mBAAmB;AAAA,EAC5E;AACF;AACA,SAAS,gGAAgG,IAAI,KAAK;AAChH,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,WAAW,GAAG;AAAA,EACzD;AACF;AACA,SAAS,iFAAiF,IAAI,KAAK;AACjG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,iGAAiG,GAAG,GAAG,gBAAgB,EAAE;AAC1I,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,sBAAyB,YAAY,CAAC;AAC5C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,QAAQ,WAAW,EAAE,YAAY,mBAAmB;AAAA,EAC5E;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AACzF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,WAAW,UAAU,QAAQ,cAAc,YAAY,YAAY;AAAA,EACxE;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC,EAAE,GAAG,EAAE;AACnC,IAAG,WAAW,GAAG,kFAAkF,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,kFAAkF,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,0EAA0E,GAAG,GAAG,QAAQ,EAAE;AACzU,IAAG,sBAAsB,EAAE;AAAA,EAC7B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,OAAO,QAAQ;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,SAAS;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,cAAc;AAAA,EAC9C;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AAAC;AACpF,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,WAAW,UAAU,QAAQ,aAAa,YAAY,MAAM;AAAA,EACjE;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,kEAAkE,GAAG,GAAG,eAAe,IAAI,GAAM,sBAAsB,EAAE,GAAG,kEAAkE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,EACxW;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,sBAAyB,YAAY,CAAC;AAC5C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,CAAC,OAAO,UAAU,EAAE,YAAY,mBAAmB;AACzE,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,UAAU,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,CAAC;AAAA,EACrH;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,oFAAoF,QAAQ;AAC1H,MAAG,cAAc,GAAG;AACpB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,SAAS,MAAM,CAAC;AAAA,IAC5D,CAAC;AACD,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,SAAS,OAAO,OAAO,UAAU;AAAA,EAClD;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,UAAU,EAAE;AAAA,EAClG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,MAAM,cAAc;AAAA,EACnD;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,sFAAsF;AACpH,MAAG,cAAc,IAAI;AACrB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,OAAO,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,SAAS,OAAO,OAAO,YAAY;AAAA,EACpD;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,UAAU,EAAE;AAAA,EAClG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,WAAW,QAAQ,QAAQ,YAAY;AAAA,EAC5C;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AAAC;AAC5F,SAAS,yEAAyE,IAAI,KAAK;AAAC;AAC5F,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,eAAe,EAAE,EAAE,GAAG,0EAA0E,GAAG,GAAG,eAAe,EAAE;AACxN,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,iBAAoB,YAAY,CAAC;AACvC,UAAM,mBAAsB,YAAY,CAAC;AACzC,UAAM,SAAY,cAAc;AAChC,IAAG,uBAAuB,sCAAsC,OAAO,aAAa,YAAY,YAAY,IAAI,EAAE;AAClH,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,gBAAgB;AAClD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,cAAc;AAAA,EAClD;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,QAAQ,EAAE;AAAA,EAC/F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,aAAa,cAAc;AAAA,EAC1D;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,2EAA2E,QAAQ;AACjH,MAAG,cAAc,IAAI;AACrB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,SAAS,MAAM,CAAC;AAAA,IAC7D,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,WAAW,QAAQ,QAAQ,KAAQ,aAAa;AACnD,IAAG,YAAY,SAAS,QAAQ,IAAI,EAAE,YAAY,QAAQ,aAAa,QAAQ,UAAU,QAAQ;AACjG,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,QAAQ,MAAM,GAAG;AAAA,EAC9C;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,SAAS,SAAS,iFAAiF,QAAQ;AACvH,MAAG,cAAc,IAAI;AACrB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,SAAS,MAAM,CAAC;AAAA,IAC7D,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,YAAY,SAAS,QAAQ,IAAI;AACpC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,QAAQ,MAAM,GAAG;AAAA,EAC9C;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAAC;AACrF,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,4DAA4D,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,mEAAmE,GAAG,GAAG,eAAe,EAAE;AAAA,EACzQ;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,UAAM,uBAA0B,YAAY,EAAE;AAC9C,IAAG,WAAW,QAAQ,QAAQ,GAAG;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,QAAQ,GAAG;AAClC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,oBAAoB;AAAA,EACxD;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AAAC;AACvE,SAAS,oDAAoD,IAAI,KAAK;AAAC;AACvE,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,oEAAoE,QAAQ;AAC1G,MAAG,cAAc,IAAI;AACrB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,SAAS,MAAM,CAAC;AAAA,IAC7D,CAAC;AACD,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,QAAQ,OAAO,QAAQ,UAAa,aAAa,EAAE,WAAW,EAAE,QAAQ,OAAO,QAAQ,YAAe,gBAAgB,GAAG,GAAG,IAAI,IAAI;AAC1J,IAAG,YAAY,SAAS,OAAO,OAAO,WAAW;AAAA,EACnD;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAAC;AAC7F,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,2EAA2E,GAAG,GAAG,eAAe,EAAE;AACnH,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,mBAAsB,YAAY,CAAC;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,gBAAgB;AAAA,EACpD;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAAC;AAC9E,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,6DAA6D,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,4DAA4D,GAAG,GAAG,eAAe,EAAE;AAClQ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,UAAM,iBAAoB,YAAY,CAAC;AACvC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,MAAM,eAAe;AAClD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,QAAQ,WAAW,MAAM;AAC/C,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,cAAc;AAAA,EAClD;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,eAAe,EAAE;AACjC,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,QAAQ,OAAO,EAAE,cAAc,KAAK,EAAE,iBAAiB,CAAC;AAAA,EACrF;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,EAAE;AACxC,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,oDAAoD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,oDAAoD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,oDAAoD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,IAAI,qDAAqD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,IAAI,qDAAqD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAChrB,IAAG,eAAe,IAAI,OAAO,EAAE,EAAE,IAAI,QAAQ,EAAE;AAC/C,IAAG,WAAW,IAAI,qDAAqD,GAAG,GAAG,eAAe,EAAE,EAAE,IAAI,qDAAqD,GAAG,GAAG,eAAe,EAAE;AAChL,IAAG,aAAa,EAAE;AAClB,IAAG,WAAW,IAAI,8CAA8C,GAAG,GAAG,QAAQ,EAAE,EAAE,IAAI,6CAA6C,GAAG,GAAG,OAAO,EAAE;AAClJ,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,WAAc,YAAY,CAAC;AACjC,UAAM,cAAiB,YAAY,EAAE;AACrC,UAAM,SAAY,cAAc;AAChC,IAAG,uBAAuB,oBAAoB,OAAO,UAAU,YAAY;AAC3E,IAAG,UAAU;AACb,IAAG,uBAAuB,8CAA8C,QAAQ,QAAQ,oCAAoC,OAAO,UAAU,EAAE;AAC/I,IAAG,WAAW,cAAc,MAAS,EAAE,kBAAkB,QAAQ,WAAW,UAAU,QAAQ,UAAU,IAAI;AAC5G,IAAG,YAAY,YAAY,QAAQ,GAAG;AACtC,IAAG,UAAU,EAAE;AACf,IAAG,WAAW,oBAAoB,QAAQ;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,WAAW;AAC7C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,aAAa,kBAAkB,CAAC,QAAQ,WAAW;AAChF,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,QAAQ,WAAW;AAAA,EAC3C;AACF;AACA,IAAM,MAAM,CAAC,YAAY;AACzB,IAAM,MAAM,CAAC,UAAU;AACvB,IAAM,MAAM,MAAM,CAAC;AACnB,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB,IAAI,CAAC;AAAA,EACzC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,WAAW,OAAO,mBAAmB,KAAK,MAAM;AAC/D,IAAG,WAAW,UAAU,OAAO,MAAM,EAAE,YAAY,OAAO,UAAU,EAAE,SAAS,OAAO,cAAiB,gBAAgB,IAAI,GAAG,CAAC,EAAE,SAAS,OAAO,gBAAgB,EAAE,cAAc,OAAO,YAAY,EAAE,eAAe,OAAO,aAAa,EAAE,kBAAkB,OAAO,gBAAgB,EAAE,aAAa,OAAO,SAAS,EAAE,YAAY,OAAO,QAAQ,EAAE,cAAc,OAAO,UAAU,EAAE,OAAO,OAAO,GAAG;AAAA,EACtY;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,gBAAgB,EAAE;AACjH,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,gBAAgB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,UAAU,CAAC;AAAA,EACrI;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2DAA2D,GAAG,IAAI,kBAAkB,CAAC,EAAE,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,CAAC;AAAA,EAC7L;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,OAAO,UAAU,CAAC,OAAO,gBAAgB;AAC/D,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,gBAAgB;AAAA,EAC/C;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,IAAI,CAAC;AAC/C,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,EAAE;AAChG,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,SAAY,YAAY,CAAC;AAC/B,IAAG,YAAY,WAAW,OAAO,eAAe,KAAK,MAAM;AAC3D,IAAG,WAAW,WAAW,OAAO,SAAS;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,WAAW;AAC3C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,MAAM;AAAA,EAC1C;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AAAC;AAC3E,SAAS,wDAAwD,IAAI,KAAK;AAAC;AAC3E,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,QAAQ,SAAS,8DAA8D,QAAQ;AACnG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,MAAM,CAAC;AAAA,IAC/C,CAAC,EAAE,YAAY,SAAS,kEAAkE,QAAQ;AAChG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,MAAM,CAAC;AAAA,IAC/C,CAAC,EAAE,aAAa,SAAS,mEAAmE,QAAQ;AAClG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,MAAM,CAAC;AAAA,IAC/C,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC,EAAE,GAAG,OAAO,EAAE;AAC/C,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,eAAe,EAAE;AACjG,IAAG,aAAa,EAAE,EAAE;AACpB,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,eAAe,EAAE;AACjG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,UAAa,YAAY,CAAC;AAChC,UAAM,SAAY,YAAY,CAAC;AAC/B,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,SAAS;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,WAAW;AAC3C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,MAAM;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO;AAAA,EAC3C;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AAAC;AACzF,SAAS,sEAAsE,IAAI,KAAK;AAAC;AACzF,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,eAAe,EAAE,EAAE,GAAG,uEAAuE,GAAG,GAAG,eAAe,EAAE;AAClN,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,UAAa,YAAY,CAAC;AAChC,UAAM,SAAY,YAAY,CAAC;AAC/B,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,MAAM;AAAA,EAC1C;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,CAAC;AAAA,EACnG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,SAAY,YAAY,EAAE;AAChC,IAAG,WAAW,QAAQ,OAAO,eAAe,cAAc,EAAE,YAAY,MAAM;AAAA,EAChF;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,EAAE,EAAE,GAAG,wDAAwD,GAAG,GAAG,eAAe,EAAE;AAAA,EACtL;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,UAAa,YAAY,CAAC;AAChC,UAAM,SAAY,YAAY,CAAC;AAC/B,IAAG,WAAW,oBAAoB,MAAM;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO;AAAA,EAC3C;AACF;AACA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,UAAU;AACR,QAAI,KAAK,QAAQ,YAAY,CAAC,KAAK,QAAQ,uBAAuB;AAChE;AAAA,IACF;AACA,SAAK,KAAK,cAAc,MAAM;AAAA,EAChC;AAAA;AAAA,EAEA,WAAW,GAAG;AACZ,QAAI,KAAK,QAAQ,YAAY,EAAE,SAAS,YAAY;AAClD,QAAE,eAAe;AACjB;AAAA,IACF;AACA,QAAI,KAAK,QAAQ,WAAW;AAC1B,WAAK,iBAAiB,EAAE,aAAa,KAAK;AAAA,IAC5C,OAAO;AACL,YAAM,QAAQ,MAAM,UAAU,MAAM,KAAK,EAAE,aAAa,KAAK,EAAE,OAAO,UAAQ,KAAK,WAAW,MAAM,KAAK,QAAQ,MAAM,CAAC;AACxH,UAAI,MAAM,QAAQ;AAChB,aAAK,YAAY,KAAK;AAAA,MACxB;AAAA,IACF;AACA,MAAE,eAAe;AAAA,EACnB;AAAA,EACA,SAAS,GAAG;AACV,QAAI,KAAK,QAAQ,UAAU;AACzB;AAAA,IACF;AACA,UAAM,MAAM,EAAE;AACd,SAAK,YAAY,IAAI,KAAK;AAC1B,QAAI,QAAQ;AAAA,EACd;AAAA,EACA,iBAAiB,OAAO;AACtB,UAAM,oBAAoB,CAAC,MAAM,SAAS;AACxC,UAAI,KAAK,QAAQ;AACf,aAAK,KAAK,UAAQ;AAChB,cAAI,KAAK,WAAW,MAAM,KAAK,QAAQ,MAAM,GAAG;AAC9C,iBAAK,YAAY,CAAC,IAAI,CAAC;AAAA,UACzB;AAAA,QACF,CAAC;AAAA,MACH,WAAW,KAAK,aAAa;AAC3B,cAAM,YAAY,KAAK,aAAa;AACpC,kBAAU,YAAY,aAAW;AAC/B,qBAAW,cAAc,SAAS;AAChC,8BAAkB,YAAY,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG;AAAA,UACtD;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,eAAW,QAAQ,OAAO;AACxB,wBAAkB,KAAK,iBAAiB,GAAG,EAAE;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,WAAW,MAAM,eAAe;AAC9B,QAAI,QAAQ,eAAe;AACzB,YAAM,qBAAqB,MAAM,QAAQ,aAAa,IAAI,gBAAgB,cAAc,MAAM,GAAG;AACjG,YAAM,WAAW,GAAG,KAAK,IAAI;AAC7B,YAAM,WAAW,GAAG,KAAK,IAAI;AAC7B,YAAM,eAAe,SAAS,QAAQ,SAAS,EAAE;AACjD,aAAO,mBAAmB,KAAK,UAAQ;AACrC,cAAM,YAAY,KAAK,KAAK;AAC5B,YAAI,UAAU,OAAO,CAAC,MAAM,KAAK;AAC/B,iBAAO,SAAS,YAAY,EAAE,QAAQ,UAAU,YAAY,GAAG,SAAS,YAAY,EAAE,SAAS,UAAU,YAAY,EAAE,MAAM,MAAM;AAAA,QACrI,WAAW,QAAQ,KAAK,SAAS,GAAG;AAElC,iBAAO,iBAAiB,UAAU,QAAQ,SAAS,EAAE;AAAA,QACvD;AACA,eAAO,aAAa;AAAA,MACtB,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,MAAM;AACd,QAAI,CAAC,KAAK,KAAK;AACb,WAAK,MAAM,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,CAAC;AAAA,IACnD;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY,UAAU;AACpB,QAAI,WAAW,GAAG,MAAM,UAAU,MAAM,KAAK,QAAQ,CAAC;AACtD,QAAI,KAAK,QAAQ,SAAS;AACxB,WAAK,QAAQ,QAAQ,QAAQ,OAAK;AAChC,mBAAW,SAAS,KAAK,UAAU,UAAQ;AACzC,gBAAM,QAAQ,EAAE,GAAG,IAAI;AACvB,iBAAO,iBAAiB,aAAa,QAAQ,GAAG,KAAK;AAAA,QACvD,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AACA,aAAS,UAAU,UAAQ;AACzB,WAAK,QAAQ,UAAQ;AACnB,aAAK,UAAU,IAAI;AACnB,aAAK,OAAO,MAAM,IAAI;AAAA,MACxB,CAAC;AAAA,IACH,GAAG,OAAK;AACN,WAAK,iCAAiC,CAAC;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EACA,OAAO,MAAM,UAAU;AACrB,QAAI,CAAC,KAAK,QAAQ,cAAc;AAC9B,aAAO,KAAK,KAAK,IAAI;AAAA,IACvB;AACA,UAAM,SAAS,KAAK,QAAQ,aAAa,MAAM,QAAQ;AACvD,QAAI,kBAAkB,YAAY;AAChC,aAAO,UAAU,mBAAiB;AAChC,cAAM,oBAAoB,OAAO,UAAU,SAAS,KAAK,aAAa;AACtE,YAAI,sBAAsB,mBAAmB,sBAAsB,iBAAiB;AAClF,eAAK,UAAU,aAAa;AAC5B,eAAK,KAAK,aAAa;AAAA,QACzB,WAAW,OAAO,kBAAkB,aAAa,kBAAkB,OAAO;AACxE,eAAK,KAAK,IAAI;AAAA,QAChB;AAAA,MACF,GAAG,OAAK;AACN,aAAK,uCAAuC,CAAC;AAAA,MAC/C,CAAC;AAAA,IACH,WAAW,WAAW,OAAO;AAC3B,aAAO,KAAK,KAAK,IAAI;AAAA,IACvB;AAAA,EACF;AAAA,EACA,KAAK,MAAM;AACT,QAAI,KAAK,SAAS;AAChB;AAAA,IACF;AACA,QAAI,WAAW,GAAG,IAAI;AACtB,QAAI;AACJ,UAAM,MAAM,KAAK;AACjB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO;AAAA,MACX,QAAQ,OAAO,WAAW,WAAW,SAAS;AAAA,MAC9C,MAAM,IAAI;AAAA,MACV;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA,iBAAiB,IAAI;AAAA,MACrB,YAAY,IAAI,aAAa,OAAK;AAChC,YAAI,WAAW,GAAG,IAAI;AAAA,MACxB,IAAI;AAAA,MACJ,WAAW,CAAC,KAAK,QAAQ;AACvB,aAAK,MAAM,GAAG;AACd,YAAI,UAAU,KAAK,MAAM,GAAG;AAAA,MAC9B;AAAA,MACA,SAAS,SAAO;AACd,aAAK,MAAM,GAAG;AACd,YAAI,QAAQ,KAAK,IAAI;AAAA,MACvB;AAAA,IACF;AACA,QAAI,OAAO,WAAW,YAAY;AAChC,YAAM,eAAe,OAAO,IAAI;AAChC,UAAI,wBAAwB,YAAY;AACtC,mBAAW,SAAS,KAAK,UAAU,MAAM,YAAY,GAAG,IAAI,SAAO;AACjE,eAAK,SAAS;AACd,iBAAO;AAAA,QACT,CAAC,CAAC;AAAA,MACJ,OAAO;AACL,aAAK,SAAS;AAAA,MAChB;AAAA,IACF;AACA,QAAI,OAAO,kBAAkB,YAAY;AACvC,YAAM,kBAAkB,cAAc,IAAI;AAC1C,iBAAW,SAAS,KAAK,UAAU,MAAM,2BAA2B,aAAa,kBAAkB,GAAG,eAAe,CAAC,GAAG,IAAI,aAAW,kBAAkB,OAAO,CAAC;AAAA,IACpK;AACA,QAAI,OAAO,SAAS,YAAY;AAC9B,YAAM,aAAa,KAAK,IAAI;AAC5B,UAAI,sBAAsB,YAAY;AACpC,mBAAW,SAAS,KAAK,UAAU,MAAM,UAAU,GAAG,IAAI,SAAO;AAC/D,eAAK,OAAO;AACZ,iBAAO,mBAAmB;AAAA,QAC5B,CAAC,CAAC;AAAA,MACJ,OAAO;AACL,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AACA,QAAI,OAAO,YAAY,YAAY;AACjC,YAAM,gBAAgB,QAAQ,IAAI;AAClC,UAAI,yBAAyB,YAAY;AACvC,mBAAW,SAAS,KAAK,UAAU,MAAM,aAAa,GAAG,IAAI,SAAO;AAClE,eAAK,UAAU;AACf,iBAAO,mBAAmB;AAAA,QAC5B,CAAC,CAAC;AAAA,MACJ,OAAO;AACL,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AACA,aAAS,UAAU,aAAW;AAC5B,WAAK,WAAW;AAChB,YAAM,QAAQ,IAAI,iBAAiB,KAAK,KAAK,KAAK,MAAM,IAAI;AAC5D,UAAI,EAAE,gBAAgB,eAAe;AACnC,aAAK,+DAA+D;AAAA,MACtE;AACA,WAAK,KAAK,GAAG,IAAI;AACjB,UAAI,QAAQ,IAAI;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EACA,IAAI,MAAM;AACR,UAAM,WAAW,IAAI,SAAS;AAC9B,QAAI,KAAK,MAAM;AACb,aAAO,KAAK,KAAK,IAAI,EAAE,IAAI,SAAO;AAChC,iBAAS,OAAO,KAAK,KAAK,KAAK,GAAG,CAAC;AAAA,MACrC,CAAC;AAAA,IACH;AACA,aAAS,OAAO,KAAK,MAAM,KAAK,QAAQ;AACxC,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU,CAAC;AAAA,IAClB;AACA,QAAI,KAAK,QAAQ,kBAAkB,MAAM,MAAM;AAC7C,WAAK,QAAQ,kBAAkB,IAAI;AAAA,IACrC,OAAO;AACL,aAAO,KAAK,QAAQ,kBAAkB;AAAA,IACxC;AACA,UAAM,MAAM,IAAI,YAAY,QAAQ,KAAK,QAAQ,UAAU;AAAA,MACzD,gBAAgB;AAAA,MAChB,iBAAiB,KAAK;AAAA,MACtB,SAAS,IAAI,YAAY,KAAK,OAAO;AAAA,IACvC,CAAC;AACD,WAAO,KAAK,KAAK,QAAQ,GAAG,EAAE,UAAU,WAAS;AAC/C,UAAI,MAAM,SAAS,cAAc,gBAAgB;AAC/C,YAAI,MAAM,QAAQ,GAAG;AACnB,gBAAM,UAAU,MAAM,SAAS,MAAM,QAAQ;AAAA,QAC/C;AACA,aAAK,WAAW,OAAO,KAAK,IAAI;AAAA,MAClC,WAAW,iBAAiB,cAAc;AACxC,aAAK,UAAU,MAAM,MAAM,KAAK,MAAM,KAAK;AAAA,MAC7C;AAAA,IACF,GAAG,SAAO;AACR,WAAK,MAAM,KAAK,IAAI;AACpB,WAAK,QAAQ,KAAK,KAAK,IAAI;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACA,MAAM,KAAK;AACT,UAAM,OAAO,KAAK,KAAK,GAAG;AAC1B,QAAI,gBAAgB,cAAc;AAChC,WAAK,YAAY;AAAA,IACnB;AACA,WAAO,KAAK,KAAK,GAAG;AAAA,EACtB;AAAA,EACA,MAAM,MAAM;AACV,QAAI,MAAM;AACR,WAAK,MAAM,QAAQ,KAAK,GAAG;AAAA,IAC7B,OAAO;AACL,aAAO,KAAK,KAAK,IAAI,EAAE,QAAQ,SAAO,KAAK,MAAM,GAAG,CAAC;AAAA,IACvD;AAAA,EACF;AAAA,EACA,YAAY,QAAQ,MAAM,YAAY;AACpC,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,aAAa;AAClB,SAAK,OAAO,CAAC;AACb,SAAK,UAAU;AACf,SAAK,WAAW,IAAI,QAAQ;AAC5B,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,gFAAgF;AAAA,IAClG;AAAA,EACF;AAAA,EACA,WAAW;AAGT,SAAK,OAAO,kBAAkB,MAAM;AAClC,gBAAU,KAAK,WAAW,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,QAAQ,CAAC;AAC/G,gBAAU,KAAK,WAAW,eAAe,SAAS,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AACpG,YAAI,KAAK,QAAQ,UAAU;AACzB;AAAA,QACF;AACA,YAAI,MAAM,QAAQ,WAAW,MAAM,YAAY,OAAO;AACpD,eAAK,QAAQ;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,UAAU;AACf,SAAK,SAAS,KAAK;AACnB,SAAK,MAAM;AAAA,EACb;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,kBAAqB,MAAM,GAAM,kBAAqB,YAAY,CAAC,GAAM,kBAAqB,UAAU,CAAC;AAAA,IACrJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACrC,WAAW,SAAS,2BAA2B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,OAAO,GAAG;AAAA,QAC7D;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,YAAY;AAAA,MAC3B,UAAU;AAAA,MACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAQ,SAAS,6CAA6C,QAAQ;AAClF,mBAAO,IAAI,WAAW,MAAM;AAAA,UAC9B,CAAC,EAAE,YAAY,SAAS,iDAAiD,QAAQ;AAC/E,mBAAO,IAAI,WAAW,MAAM;AAAA,UAC9B,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,YAAY,GAAG,EAAE,QAAQ,QAAQ;AAChD,UAAG,YAAY,uBAAuB,IAAI,QAAQ,QAAQ;AAAA,QAC5D;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,QAAQ,GAAG,UAAU,UAAU,CAAC;AAAA,MAChE,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,UAAG,WAAW,UAAU,SAAS,sDAAsD,QAAQ;AAC7F,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,SAAS,MAAM,CAAC;AAAA,UAC5C,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,aAAa,CAAC;AAAA,QACnB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,WAAW,MAAM;AAChC,UAAG,WAAW,YAAY,IAAI,QAAQ,QAAQ;AAC9C,UAAG,YAAY,UAAU,IAAI,QAAQ,MAAM,EAAE,aAAa,IAAI,QAAQ,YAAY,cAAc,IAAI,EAAE,mBAAmB,IAAI,QAAQ,YAAY,oBAAoB,IAAI;AAAA,QAC3K;AAAA,MACF;AAAA,MACA,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,+BAA+B;AAAA,QAC/B,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,MACA,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,kBAAkB,UAAQ,CAAC,CAAC,QAAQ,KAAK,QAAQ,QAAQ,MAAM;AACrE,IAAM,eAAe;AACrB,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,IAAI,UAAU;AACZ,WAAO,KAAK,aAAa,aAAa,KAAK,aAAa;AAAA,EAC1D;AAAA,EACA,IAAI,MAAM,MAAM;AACd,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO,MAAM;AACX,QAAI,KAAK,YAAY,OAAO,KAAK,aAAa,UAAU;AACtD,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,SAAS,KAAK,MAAM,cAAc,KAAK,OAAO;AAAA,EAC5D;AAAA,EACA,QAAQ,KAAK;AACX,UAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,UAAM,WAAW,KAAK,KAAK,SAAS,CAAC;AACrC,UAAM,wBAAwB,SAAS,MAAM,MAAM,EAAE,CAAC;AACtD,YAAQ,cAAc,KAAK,qBAAqB,KAAK,CAAC,EAAE,GAAG,CAAC;AAAA,EAC9D;AAAA,EACA,WAAW,MAAM;AACf,QAAI,gBAAgB,KAAK,IAAI,GAAG;AAC9B,aAAO;AAAA,IACT;AACA,UAAM,MAAM,KAAK,YAAY,KAAK,OAAO;AACzC,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,UAAM,YAAY,KAAK,QAAQ,GAAG;AAClC,QAAI,gBAAgB,KAAK,GAAG,KAAK,6CAA6C,KAAK,SAAS,GAAG;AAC7F,aAAO;AAAA,IACT,WAAW,SAAS,KAAK,GAAG,GAAG;AAE7B,aAAO;AAAA,IACT,WAAW,WAAW;AAEpB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY,MAAM;AAChB,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,eAAe,CAAC,KAAK,YAAY,CAAC,KAAK,KAAK;AACnD,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa,MAAM;AACjB,QAAI,CAAC,gBAAgB,KAAK,IAAI,KAAK,CAAC,KAAK,SAAS,WAAW;AAC3D,aAAO,GAAG,EAAE;AAAA,IACd;AACA,UAAM,SAAS,KAAK,IAAI,cAAc,QAAQ;AAC9C,WAAO,QAAQ;AACf,WAAO,SAAS;AAChB,WAAO,MAAM,UAAU,4CAA4C,YAAY,eAAe,YAAY;AAC1G,SAAK,IAAI,KAAK,YAAY,MAAM;AAChC,UAAM,MAAM,OAAO,WAAW,IAAI;AAClC,UAAM,MAAM,IAAI,MAAM;AACtB,UAAM,YAAY,IAAI,gBAAgB,IAAI;AAC1C,QAAI,MAAM;AACV,WAAO,UAAU,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM;AAC3C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,YAAY;AAChB,UAAI,aAAa;AACjB,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,QAAQ,QAAQ;AAClB,qBAAa,UAAU,eAAe;AACtC,kBAAU,EAAE,aAAa,aAAa;AAAA,MACxC,OAAO;AACL,oBAAY,SAAS,eAAe;AACpC,kBAAU,EAAE,YAAY,cAAc;AAAA,MACxC;AACA,UAAI;AACF,YAAI,UAAU,KAAK,SAAS,SAAS,WAAW,UAAU;AAAA,MAC5D,QAAQ;AAAA,MAAC;AACT,YAAM,UAAU,OAAO,UAAU;AACjC,WAAK,IAAI,KAAK,YAAY,MAAM;AAChC,UAAI,gBAAgB,SAAS;AAC7B,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,SAAS,WAAW;AAC5B;AAAA,IACF;AACA,UAAM,MAAM;AACZ,QAAI,CAAC,KAAK,WAAW,OAAO,aAAa,eAAe,OAAO,QAAQ,eAAe,CAAC,IAAI,cAAc,CAAC,IAAI,MAAM;AAClH;AAAA,IACF;AACA,SAAK,KAAK,OAAO,UAAQ,KAAK,yBAAyB,QAAQ,KAAK,aAAa,MAAS,EAAE,QAAQ,UAAQ;AAC1G,WAAK,WAAW;AAIhB,YAAM,YAAY,KAAK,cAAc,KAAK,YAAY,IAAI,IAAI,KAAK,aAAa,KAAK,aAAa,GAAG,KAAK,UAAU,KAAK,QAAQ,CAAC;AAClI,WAAK,OAAO,kBAAkB,MAAM;AAClC,iBAAS,UAAU,aAAW;AAC5B,eAAK,OAAO,IAAI,MAAM;AACpB,iBAAK,WAAW;AAChB,iBAAK,cAAc;AAAA,UACrB,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,CAAC,EAAE,KAAK,MAAM,oBAAoB,KAAK,WAAW;AAAA,EAC3D;AAAA,EACA,UAAU;AACR,SAAK,KAAK,QAAQ,UAAQ;AACxB,WAAK,cAAc,KAAK,WAAW;AACnC,WAAK,UAAU,KAAK,OAAO,IAAI;AAC/B,WAAK,YAAY,OAAO,KAAK,cAAc,WAAW,KAAK,MAAM,KAAK,SAAS,IAAI,KAAK;AACxF,WAAK,aAAa,KAAK,iBAAiB,KAAK,eAAe,IAAI,IAAI,KAAK,WAAW,IAAI;AACxF,WAAK,WAAW,KAAK,YAAY,IAAI;AACrC,WAAK,eAAe,KAAK,aAAa,IAAI;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EACA,cAAc,MAAM,GAAG;AACrB,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,MAAE,eAAe;AACjB,WAAO,KAAK,UAAU,IAAI;AAAA,EAC5B;AAAA,EACA,aAAa,MAAM,GAAG;AACpB,MAAE,eAAe;AACjB,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,IAAI;AAAA,IACpB;AACA;AAAA,EACF;AAAA,EACA,eAAe,MAAM;AACnB,QAAI,OAAO,KAAK,eAAe,YAAY;AACzC,WAAK,WAAW,IAAI;AAAA,IACtB,WAAW,KAAK,KAAK;AACnB,aAAO,KAAK,KAAK,GAAG;AAAA,IACtB;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,KAAK,KAAK,QAAQ,UAAU;AACtC,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,OAAO,CAAC;AACb,SAAK,SAAS,CAAC;AACf,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,gBAAgB;AACd,SAAK,QAAQ;AACb,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAA0B,kBAAqB,iBAAiB,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,MAAM,GAAM,kBAAuB,QAAQ,CAAC;AAAA,IAC1L;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,MAC9B,WAAW,CAAC,GAAG,iBAAiB;AAAA,MAChC,UAAU;AAAA,MACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,uBAAuB,IAAI,QAAQ,KAAK,EAAE,wBAAwB,IAAI,aAAa,MAAM,EAAE,2BAA2B,IAAI,aAAa,SAAS,EAAE,gCAAgC,IAAI,aAAa,cAAc;AAAA,QAClO;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,QACX,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,KAAK;AAAA,MACP;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS,SAAS,GAAG,CAAC,cAAc,IAAI,GAAG,gBAAgB,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,SAAS,gCAAgC,GAAG,MAAM,GAAG,CAAC,SAAS,iCAAiC,GAAG,MAAM,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,SAAS,kCAAkC,GAAG,6BAA6B,GAAG,cAAc,GAAG,CAAC,SAAS,kCAAkC,UAAU,UAAU,OAAO,uBAAuB,GAAG,6BAA6B,QAAQ,SAAS,GAAG,cAAc,GAAG,CAAC,SAAS,wBAAwB,GAAG,iBAAiB,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,UAAU,UAAU,OAAO,uBAAuB,GAAG,kCAAkC,GAAG,SAAS,MAAM,GAAG,CAAC,SAAS,8BAA8B,GAAG,OAAO,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,8BAA8B,GAAG,KAAK,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,WAAW,IAAI,GAAG,UAAU,GAAG,iBAAiB,GAAG,CAAC,WAAW,IAAI,UAAU,SAAS,GAAG,CAAC,WAAW,IAAI,GAAG,QAAQ,GAAG,CAAC,WAAW,IAAI,WAAW,WAAW,GAAG,QAAQ,GAAG,CAAC,QAAQ,UAAU,aAAa,IAAI,UAAU,QAAQ,UAAU,SAAS,SAAS,yCAAyC,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,QAAQ,UAAU,aAAa,IAAI,UAAU,QAAQ,UAAU,SAAS,GAAG,yCAAyC,GAAG,OAAO,GAAG,CAAC,WAAW,IAAI,UAAU,QAAQ,GAAG,CAAC,WAAW,IAAI,UAAU,UAAU,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,UAAU,UAAU,OAAO,uBAAuB,SAAS,6BAA6B,GAAG,QAAQ,SAAS,GAAG,MAAM,GAAG,CAAC,SAAS,6BAA6B,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,UAAU,UAAU,OAAO,uBAAuB,GAAG,6BAA6B,GAAG,SAAS,MAAM,GAAG,CAAC,GAAG,6BAA6B,GAAG,OAAO,GAAG,CAAC,GAAG,8BAA8B,GAAG,CAAC,UAAU,UAAU,OAAO,uBAAuB,GAAG,QAAQ,WAAW,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,UAAU,UAAU,OAAO,uBAAuB,GAAG,SAAS,QAAQ,SAAS,GAAG,CAAC,WAAW,IAAI,UAAU,KAAK,GAAG,CAAC,GAAG,+BAA+B,GAAG,CAAC,UAAU,QAAQ,GAAG,aAAa,cAAc,eAAe,CAAC;AAAA,MACh5E,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,sCAAsC,IAAI,IAAI,OAAO,CAAC;AAAA,QACzE;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,IAAI,IAAI;AAAA,QACnC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,iBAAoB,oBAAoB,UAAU,kBAAkB,MAAM,iBAAiB,cAAc,cAAiB,iBAAiB,gBAAmB,mBAAsB,4BAA6B,SAAS,kBAAqB,mBAAmB;AAAA,MAC1R,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,QAAQ,aAAa,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,UAC5D,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,GAAG,QAAQ,KAAK,MAAM;AAAA,UACrB,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,QAAQ,KAAK,MAAM;AAAA,UAC9C,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY,CAAC,QAAQ,aAAa,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,QAC7D,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC,GAAG,QAAQ,KAAK,MAAM;AAAA,QACrB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,QAAQ,KAAK,MAAM;AAAA,QAC9C,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MACP,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,+BAA+B;AAAA,QAC/B,gCAAgC;AAAA,QAChC,mCAAmC;AAAA,QACnC,wCAAwC;AAAA,MAC1C;AAAA,MACA,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,SAAS,iBAAiB,UAAU,kBAAkB,MAAM,iBAAiB,cAAc,cAAc,gBAAgB,SAAS,gBAAgB;AAAA,MAC5J,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,IAAI,iBAAiB,OAAO;AAC1B,SAAK,kBAAkB,OAAO,UAAU,YAAY,UAAU,KAAK,IAAI;AAAA,EACzE;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa;AACX,QAAI,OAAO,KAAK,qBAAqB,aAAa,KAAK,kBAAkB;AACvE,WAAK,mBAAmB;AAAA,QACtB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,MACpB;AAAA,IACF;AAEA,UAAM,UAAU,KAAK,SAAS,MAAM;AACpC,QAAI,KAAK,cAAc,KAAK,UAAU,KAAK,QAAQ,UAAU,OAAK,EAAE,SAAS,OAAO,MAAM,IAAI;AAC5F,cAAQ,KAAK;AAAA,QACX,MAAM;AAAA,QACN,IAAI,cAAY,SAAS,MAAM,CAAC,KAAK,OAAO;AAAA,MAC9C,CAAC;AAAA,IACH;AACA,QAAI,KAAK,SAAS,KAAK,QAAQ,UAAU,OAAK,EAAE,SAAS,MAAM,MAAM,IAAI;AACvE,cAAQ,KAAK;AAAA,QACX,MAAM;AAAA,QACN,IAAI,cAAY,SAAS,OAAO,OAAK,EAAE,OAAO,QAAQ,KAAK,MAAM;AAAA,MACnE,CAAC;AAAA,IACH;AACA,QAAI,KAAK,cAAc,KAAK,WAAW,SAAS,KAAK,QAAQ,UAAU,OAAK,EAAE,SAAS,MAAM,MAAM,IAAI;AACrG,YAAM,QAAQ,KAAK,WAAW,MAAM,GAAG;AACvC,cAAQ,KAAK;AAAA,QACX,MAAM;AAAA,QACN,IAAI,cAAY,SAAS,OAAO,OAAK,CAAC,MAAM,QAAQ,EAAE,IAAI,CAAC;AAAA,MAC7D,CAAC;AAAA,IACH;AACA,SAAK,cAAc;AAAA,MACjB,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB,uBAAuB,KAAK;AAAA,MAC5B,cAAc,KAAK;AAAA,MACnB,eAAe,KAAK;AAAA,MACpB,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,MAAM,KAAK;AAAA,MACX,UAAU,KAAK;AAAA,MACf,iBAAiB,KAAK;AAAA,MACtB;AAAA,MACA,eAAe,KAAK;AAAA,MACpB,SAAS,KAAK;AAAA,MACd,YAAY,KAAK;AAAA,MACjB,WAAW,KAAK;AAAA,MAChB,SAAS,KAAK;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,YAAY,QAAQA,WAAU,KAAK,MAAM,gBAAgB;AACvD,SAAK,SAAS;AACd,SAAK,WAAWA;AAChB,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,iBAAiB;AACtB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,MAAM;AAEX,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,0BAA0B;AAC/B,SAAK,WAAW,CAAC;AACjB,SAAK,aAAa,CAAC;AACnB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,oBAAoB;AACzB,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,UAAU,UAAQ;AACrB,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa,CAAC;AAAA,MACrB;AACA,YAAM,aAAa,KAAK,aAAa,IAAI;AACzC,iBAAW,SAAS;AACpB,WAAK,aAAa,KAAK,WAAW,OAAO,UAAU;AACnD,WAAK,iBAAiB,KAAK,KAAK,UAAU;AAC1C,WAAK,SAAS,KAAK;AAAA,QACjB,MAAM;AAAA,QACN,UAAU,KAAK;AAAA,QACf,MAAM;AAAA,MACR,CAAC;AACD,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,aAAa,CAAC,GAAG,SAAS;AAC7B,YAAM,WAAW,KAAK;AACtB,YAAM,aAAa,KAAK,YAAY,MAAM,QAAQ;AAClD,iBAAW,UAAU,EAAE;AACvB,WAAK,SAAS,KAAK;AAAA,QACjB,OAAO;AAAA,QACP,MAAM,mBACD;AAAA,QAEL,UAAU,KAAK;AAAA,QACf,MAAM;AAAA,MACR,CAAC;AACD,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,YAAY,CAAC,KAAK,SAAS;AAC9B,YAAM,WAAW,KAAK;AACtB,YAAM,aAAa,KAAK,YAAY,MAAM,QAAQ;AAClD,iBAAW,SAAS;AACpB,iBAAW,WAAW;AACtB,WAAK,SAAS,KAAK;AAAA,QACjB,MAAM,mBACD;AAAA,QAEL;AAAA,QACA,MAAM;AAAA,MACR,CAAC;AACD,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,UAAU,CAAC,KAAK,SAAS;AAC5B,YAAM,WAAW,KAAK;AACtB,YAAM,aAAa,KAAK,YAAY,MAAM,QAAQ;AAClD,iBAAW,QAAQ;AACnB,iBAAW,SAAS;AACpB,WAAK,SAAS,KAAK;AAAA,QACjB,MAAM,mBACD;AAAA,QAEL;AAAA,QACA,MAAM;AAAA,MACR,CAAC;AACD,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,WAAW,UAAQ;AACtB,WAAK,WAAW,MAAM,IAAI;AAC1B,WAAK,SAAS;AACd,YAAM,QAAQ,OAAO,KAAK,aAAa,aAAa,KAAK,SAAS,IAAI,IAAI,KAAK,YAAY,OAAO,OAAO,KAAK;AAC9G,OAAC,iBAAiB,aAAa,QAAQ,GAAG,KAAK,GAAG,KAAK,OAAO,SAAO,GAAG,CAAC,EAAE,UAAU,MAAM;AACzF,aAAK,aAAa,KAAK,eAAe,MAAM,KAAK,UAAU;AAC3D,aAAK,SAAS,KAAK;AAAA,UACjB;AAAA,UACA,UAAU,KAAK;AAAA,UACf,MAAM;AAAA,QACR,CAAC;AACD,aAAK,iBAAiB,KAAK,KAAK,UAAU;AAC1C,aAAK,IAAI,cAAc;AAAA,MACzB,CAAC;AAAA,IACH;AAGA,SAAK,YAAY;AACjB,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA;AAAA,EAEA,aAAa,MAAM;AACjB,WAAO;AAAA,MACL,cAAc,KAAK;AAAA,MACnB,kBAAkB,KAAK;AAAA,MACvB,MAAM,KAAK,YAAY,KAAK;AAAA,MAC5B,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,KAAK,KAAK;AAAA,MACV,UAAU,KAAK;AAAA,MACf,OAAO,KAAK;AAAA,MACZ,SAAS;AAAA,MACT,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,YAAY,MAAM,UAAU;AAC1B,WAAO,SAAS,OAAO,UAAQ,KAAK,QAAQ,KAAK,GAAG,EAAE,CAAC;AAAA,EACzD;AAAA,EACA,eAAe,MAAM,UAAU;AAC7B,WAAO,SAAS,OAAO,UAAQ,KAAK,QAAQ,KAAK,GAAG;AAAA,EACtD;AAAA;AAAA,EAEA,SAAS,GAAG;AACV,QAAI,EAAE,SAAS,KAAK,WAAW;AAC7B;AAAA,IACF;AACA,SAAK,YAAY,EAAE;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA,EAGA,oBAAoB;AAClB,SAAK,IAAI,cAAc;AACvB,SAAK,UAAU,cAAc;AAAA,EAC/B;AAAA,EACA,cAAc;AACZ,QAAI,SAAS,CAAC;AACd,QAAI,KAAK,WAAW,QAAQ;AAC1B,UAAI,KAAK,WAAW,KAAK,UAAQ,KAAK,WAAW,WAAW,GAAG;AAC7D,eAAO,KAAK,GAAG,KAAK,SAAS,iBAAiB;AAAA,MAChD;AACA,UAAI,KAAK,cAAc,YAAY;AACjC,eAAO,KAAK,GAAG,KAAK,SAAS,aAAa;AAAA,MAC5C;AAAA,IACF,OAAO;AACL,eAAS,CAAC,GAAG,KAAK,SAAS,WAAW,KAAK,UAAU,EAAE;AAAA,IACzD;AACA,SAAK,YAAY,CAAC,KAAK,WAAW,GAAG,KAAK,SAAS,IAAI,KAAK,MAAM,IAAI,GAAG,QAAQ,KAAK,cAAc,GAAG,KAAK,SAAS,eAAe,IAAI,KAAK,QAAQ,SAAS,GAAG,KAAK,SAAS,UAAU,EAAE,EAAE,OAAO,UAAQ,CAAC,CAAC,IAAI;AAClN,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,YAAY;AACjB,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,KAAK,aAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACpE,WAAK,SAAS,KAAK,KAAK,cAAc,QAAQ;AAC9C,WAAK,kBAAkB;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAEhB,SAAK,OAAO,kBAAkB,MAAM,UAAU,KAAK,SAAS,MAAM,MAAM,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC1H,YAAM,eAAe;AACrB,YAAM,gBAAgB;AAAA,IACxB,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,EAAE,YAAY;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAqB,MAAM,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,iBAAiB,GAAM,kBAAuB,aAAa,GAAM,kBAAuB,gBAAgB,CAAC,CAAC;AAAA,IACzO;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,MACzB,WAAW,SAAS,wBAAwB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,QACjE;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,mCAAmC,IAAI,eAAe,cAAc;AAAA,QACrF;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,aAAa;AAAA,QACb,yBAAyB;AAAA,QACzB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,mBAAmB;AAAA,QACnB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,kBAAkB;AAAA,MACpB;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,kBAAkB;AAAA,MACpB;AAAA,MACA,UAAU,CAAC,UAAU;AAAA,MACrB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,WAAW,UAAU,YAAY,SAAS,SAAS,cAAc,eAAe,kBAAkB,aAAa,YAAY,cAAc,OAAO,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,UAAU,YAAY,SAAS,SAAS,cAAc,eAAe,kBAAkB,aAAa,YAAY,cAAc,KAAK,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,iBAAiB,IAAI,GAAG,SAAS,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,QAAQ,YAAY,aAAa,SAAS,GAAG,CAAC,iBAAiB,IAAI,GAAG,kBAAkB,GAAG,SAAS,GAAG,CAAC,GAAG,2BAA2B,CAAC;AAAA,MACjtB,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,0CAA0C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,0CAA0C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,2CAA2C,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,0CAA0C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,0CAA0C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QACnlB;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,YAAe,YAAY,CAAC;AAClC,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,WAAW,MAAM,EAAE,YAAY,SAAS;AAAA,QACpE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,uBAAuB,MAAM,kBAAkB,SAAS,oBAAoB;AAAA,MAC3F,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,YAAY,CAAC,GAAG,kBAAkB,WAAW,WAAW,MAAM;AAC1E,WAAW,CAAC,YAAY,CAAC,GAAG,kBAAkB,WAAW,UAAU,MAAM;AACzE,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,eAAe,MAAM;AAC/E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,2BAA2B,MAAM;AAC3F,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,gBAAgB,MAAM;AAChF,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,qBAAqB,MAAM;AAAA,CACpF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,2CAA2C;AAAA,MAC7C;AAAA,MACA,SAAS,CAAC,uBAAuB,MAAM,kBAAkB,SAAS,oBAAoB;AAAA,MACtF,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,QACnB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,QACjB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,mBAAmB,sBAAsB,qBAAqB;AAAA,MACxE,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,mBAAmB,qBAAqB;AAAA,IACpD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,mBAAmB,sBAAsB,qBAAqB;AAAA,MACxE,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["document"]}