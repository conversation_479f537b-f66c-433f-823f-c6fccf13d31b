/* Task Management Component Styles */

/* Theme-aware component styling */
:host {
  display: block;
  width: 100%;
  height: 100%;
}

/* Custom scrollbar for task list */
:host ::-webkit-scrollbar {
  width: 6px;
}

:host ::-webkit-scrollbar-track {
  background: var(--background-light-gray);
  border-radius: var(--border-radius-small);
}

:host ::-webkit-scrollbar-thumb {
  background: var(--hover-blue-gray);
  border-radius: var(--border-radius-small);
}

:host ::-webkit-scrollbar-thumb:hover {
  background: var(--primary-purple);
}

/* Line clamp utility for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Enhanced card hover effects */
.group:hover {
  transform: translateY(-4px);
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid var(--primary-purple);
  outline-offset: 2px;
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Status indicator animations */
.status-indicator {
  position: relative;
  overflow: hidden;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.group:hover .status-indicator::before {
  left: 100%;
}

/* Button security enhancements */
button {
  position: relative;
  overflow: hidden;
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.3s;
}

button:hover::before {
  left: 100%;
}

/* Prevent text selection on buttons */
button {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Secure form styling */
button[type="button"] {
  cursor: pointer;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Theme transition support */
* {
  transition: background-color var(--transition-default),
              color var(--transition-default),
              border-color var(--transition-default);
}

/* Dark theme specific adjustments */
:host-context(.dark) {
  color-scheme: dark;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  button {
    border: 2px solid currentColor;
  }

  .border {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .group:hover {
    transform: none;
  }
}
