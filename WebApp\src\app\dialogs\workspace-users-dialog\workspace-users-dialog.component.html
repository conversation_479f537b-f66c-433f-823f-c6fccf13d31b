<div class="flex flex-col relative   w-full h-[70vh] overflow-auto">
  <!-- Table Container -->
  <div
    class=" rounded-lg shadow-sm border border-[var(--hover-blue-gray)] border-opacity-30 overflow-hidden">
    <div class="overflow-x-auto h-[60vh]">
      <table class="w-full text-sm  overflow-auto text-[var(--text-dark)]">
        <thead>
          <tr class="bg-[var(--hover-blue-gray)] border-b border-[var(--hover-blue-gray)] text-[var(--text-dark)] font-medium text-sm">
            <th class="px-4 py-3 text-left font-semibold">Name</th>
            <th class="px-4 py-3 text-left font-semibold">Email</th>
            <th class="px-4 py-3 text-left font-semibold">Roles</th>
            <th class="px-4 py-3 text-left font-semibold">Skills</th>
            <th class="px-4 py-3 text-center font-semibold" *ngIf="isAdmin">Action</th>
          </tr>
        </thead>
        <tbody class="border-l border-r border-b !border-[var(--hover-blue-gray)]">
          <tr *ngFor="let user of paginatedUsers; let i = index"
            class="border-b border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] hover:bg-opacity-30 transition-all duration-200">
            <td class="px-4 py-3">{{ user.name }}</td>
            <td class="px-4 py-3">{{ user.email }}</td>
            <td class="px-4 py-3">{{ user.roles.join(', ') }}</td>
            <td class="px-4 py-3">{{ user.skills }}</td>
            <td class="px-4 py-3 text-center" *ngIf="isAdmin">
              <button (click)="removeUser(user)"
                class="action-button w-8 h-8 rounded-md bg-[#FEE2E2] hover:bg-[#FECACA] transition-all duration-200 flex items-center justify-center border-none shadow-sm"
                title="Remove User">
                <i class="ri-delete-bin-line text-red-500 text-base"></i>
              </button>
            </td>
          </tr>
          <tr *ngIf="paginatedUsers.length === 0">
            <td [attr.colspan]="isAdmin ? 5 : 4" class="px-4 py-6 text-center text-[var(--text-medium-gray)]">
              No users available.
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="absolute bottom-0 w-full">

      <div class="flex items-center justify-between  ">
        <button (click)="closeDialog()"
          class="px-4 py-2 text-sm font-medium border-none text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md hover:bg-[var(--hover-blue-gray)] hover:bg-opacity-30 transition-all duration-200">
          Close
        </button>
        <button (click)="addUser()" *ngIf="isAdmin"
          class="px-4 py-2 text-sm font-medium border-none text-white bg-[var(--primary-purple)] rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center gap-2">
          <i class="ri-user-add-line"></i>
          Add User
        </button>
      </div>
    </div>
  </div>

  <!-- Pagination and Buttons -->
  <!-- <div
    class="flex flex-col sm:flex-row justify-between items-center mt-4 px-4 py-3 bg-[var(--background-white)] rounded-md shadow-sm border border-[var(--hover-blue-gray)]">
    <div class="flex items-center text-sm text-[var(--text-medium-gray)] mb-4 sm:mb-0">
      <ng-container *ngIf="filteredUsers.length > 0">
        <span>Showing</span>
        <span class="font-medium text-[var(--text-dark)] mx-1">{{ ((currentPage - 1) * pageSize) + 1 }}</span>
        <span>to</span>
        <span class="font-medium text-[var(--text-dark)] mx-1">{{ ((currentPage - 1) * pageSize) + paginatedUsers.length
          }}</span>
        <span>of</span>
        <span class="font-medium text-[var(--text-dark)] mx-1">{{ filteredUsers.length }}</span>
        <span>users</span>
      </ng-container>
      <ng-container *ngIf="filteredUsers.length === 0">
        <span>No users to display</span>
      </ng-container>
    </div>

    <div class="flex items-center gap-4">
      <div class="hidden sm:flex items-center mr-6 space-x-2">
        <span class="text-sm text-[var(--text-medium-gray)]">Rows per page:</span>
        <div class="relative w-auto">
          <button id="pageSizeDropdownBtn" (click)="togglePageSizeDropdown()"
            class="appearance-none h-7 rounded-md text-sm px-2 pr-7 py-0 text-center border border-gray-300 bg-[var(--background-white)] focus:outline-none focus:ring-1 cursor-pointer">
            {{ pageSize || 'Select Page Size' }}
          </button>
          <div *ngIf="isPageSizeDropdownOpen" id="pageSizeDropdownMenu"
            class="absolute mt-1 w-full bg-[var(--secondary-purple)] !border-2 !border-gray-300 rounded-md shadow-lg z-10"
            [ngClass]="{'bottom-full mb-1 border': isDropdownAbove, 'top-full border mt-1': !isDropdownAbove}">
            <div (click)="selectPageSize(5)"
              class="px-2 py-1 text-sm cursor-pointer text-center border hover:bg-[var(--primary-purple)] rounded-md hover:text-white">
              5</div>
            <div (click)="selectPageSize(10)"
              class="px-2 py-1 text-sm cursor-pointer text-center rounded-md hover:bg-[var(--primary-purple)] hover:text-white">
              10</div>
            <div (click)="selectPageSize(20)"
              class="px-2 py-1 text-sm cursor-pointer text-center rounded-md hover:bg-[var(--primary-purple)] hover:text-white">
              20</div>
            <div (click)="selectPageSize(50)"
              class="px-2 py-1 text-sm cursor-pointer text-center rounded-md hover:bg-[var(--primary-purple)] hover:text-white">
              50</div>
          </div>
          <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-1 pointer-events-none">
            <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)] text-sm"></i>
          </div>
        </div>
      </div>

      <div class="flex items-center space-x-1">
        <button (click)="goToPage(1)" [disabled]="currentPage === 1 || totalPages <= 1"
          class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
          aria-label="First page">
          <i class="ri-skip-back-mini-line text-sm"></i>
        </button>
        <button (click)="previousPage()" [disabled]="currentPage === 1 || totalPages <= 1"
          class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
          aria-label="Previous page">
          <i class="ri-arrow-left-s-line text-sm"></i>
        </button>
        <div class="flex items-center space-x-1">
          <button *ngIf="currentPage > 2 && totalPages > 3" (click)="goToPage(1)"
            class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
            1
          </button>
          <span *ngIf="currentPage > 3 && totalPages > 4"
            class="w-7 h-7 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>
          <button *ngIf="currentPage > 1 && totalPages > 1" (click)="goToPage(currentPage - 1)"
            class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
            {{ currentPage - 1 }}
          </button>
          <button
            class="w-7 h-7 flex items-center justify-center rounded-md bg-[var(--primary-purple)] text-white font-medium border-none">
            {{ currentPage }}
          </button>
          <button *ngIf="currentPage < totalPages && totalPages > 1" (click)="goToPage(currentPage + 1)"
            class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
            {{ currentPage + 1 }}
          </button>
          <span *ngIf="currentPage < totalPages - 2 && totalPages > 4"
            class="w-7 h-7 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>
          <button *ngIf="currentPage < totalPages - 1 && totalPages > 3" (click)="goToPage(totalPages)"
            class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
            {{ totalPages }}
          </button>
        </div>
        <button (click)="nextPage()" [disabled]="currentPage === totalPages || totalPages <= 1"
          class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
          aria-label="Next page">
          <i class="ri-arrow-right-s-line text-sm"></i>
        </button>
        <button (click)="goToPage(totalPages)" [disabled]="currentPage === totalPages || totalPages <= 1"
          class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
          aria-label="Last page">
          <i class="ri-skip-forward-mini-line text-sm"></i>
        </button>
      </div>
    </div>


  </div> -->
</div>
