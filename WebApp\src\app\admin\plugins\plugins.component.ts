import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { PluginServiceProxy, PluginResponseDto, PluginRequestDto, OpenApiPluginRequestDto } from '../../../shared/service-proxies/service-proxies';
import { CommonModule } from '@angular/common';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { forkJoin } from 'rxjs';
import { AddOrEditPluginComponent } from './add-or-edit-plugin/add-or-edit-plugin.component';
import { FormsModule } from '@angular/forms';
import { NzModalService } from 'ng-zorro-antd/modal';
import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';

@Component({
  selector: 'app-plugins',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzTagModule,
    NzIconModule,
    NzButtonModule,
    NzToolTipModule,
    NzModalModule,
    AddOrEditPluginComponent,
    FormsModule,
    SpinnerComponent
  ],
  templateUrl: './plugins.component.html',
  styleUrl: './plugins.component.css'
})
export class PluginsComponent implements OnInit {
  plugins: PluginResponseDto[] = [];
  filteredPlugins: PluginResponseDto[] = [];
  loading = true;
  isCreateModalVisible = false;
  selectedPlugin: PluginResponseDto | null = null;
  searchQuery: string = '';

  constructor(
    private router: Router,
    private pluginService: PluginServiceProxy,
    private message: NzMessageService,
    private modal: NzModalService
  ) { }

  ngOnInit() {
    this.loadAllPlugins();
  }

  loadAllPlugins() {
    this.loading = true;

    // Using forkJoin to fetch both regular and OpenAI plugins
    forkJoin({
      regular: this.pluginService.getAll(),
      openAi: this.pluginService.getAllOpenAiPlugins()
    }).subscribe({
      next: (result) => {
        // Combine and deduplicate plugins based on ID
        const allPlugins = [...result.regular, ...result.openAi];
        this.plugins = Array.from(
          new Map(allPlugins.map(plugin => [plugin.id, plugin])).values()
        );
        this.filteredPlugins = [...this.plugins]; // Initialize filtered plugins
        this.loading = false;
      },
      error: (error) => {
        this.message.error('Failed to load plugins');
        this.loading = false;
        console.error('Error loading plugins:', error);
      }
    });
  }

  /**
   * Filter plugins based on search query
   */
  filterPlugins() {
    if (!this.searchQuery) {
      this.filteredPlugins = [...this.plugins];
    } else {
      const query = this.searchQuery.toLowerCase();
      this.filteredPlugins = this.plugins.filter(plugin =>
        (plugin.pluginName?.toLowerCase().includes(query) || false) ||
        (plugin.type?.toLowerCase().includes(query) || false) ||
        (plugin.url?.toLowerCase().includes(query) || false) ||
        (plugin.functions?.toLowerCase().includes(query) || false)
      );
    }
  }

  syncPlugins() {
    this.loading = true;
    this.pluginService.syncPlugins().subscribe({
      next: (plugins) => {
        this.plugins = plugins;
        this.loading = false;
        this.message.success('Plugins synchronized successfully');
      },
      error: (error) => {
        this.message.error('Failed to sync plugins');
        this.loading = false;
        console.error('Error syncing plugins:', error);
      }
    });
  }

  resyncOpenApiPlugin(pluginName: string) {
    this.loading = true;
    this.pluginService.resyncOpenApiPlugin(pluginName).subscribe({
      next: (plugin) => {
        // Update the plugin in the list
        const index = this.plugins.findIndex(p => p.pluginName === pluginName);
        if (index !== -1) {
          this.plugins[index] = plugin;
        }
        this.loading = false;
        this.message.success(`Plugin ${pluginName} resynced successfully`);
      },
      error: (error) => {
        this.message.error(`Failed to resync plugin ${pluginName}`);
        this.loading = false;
        console.error('Error resyncing plugin:', error);
      }
    });
  }

  deletePlugin(plugin: PluginResponseDto) {
    this.pluginService.delete(plugin.id).subscribe({
      next: () => {
        this.message.success(`Plugin ${plugin.pluginName} deleted successfully`);
        console.log(this.plugins);

        this.plugins = this.plugins.filter(p => p.pluginName !== plugin.pluginName);
        this.filteredPlugins = this.plugins.filter(p => p.pluginName !== plugin.pluginName);
        console.log(this.plugins);
      },
      error: (error) => {
        this.message.error(`Failed to delete plugin ${plugin.pluginName}`);
        console.error('Error deleting plugin:', error);
      }
    });
  }

  navigateToDetails(pluginName: string) {
    this.router.navigate(['settings/plugins/', pluginName]);
  }

  openCreateModal() {
    console.log('Opening create modal');
    const modalRef = this.modal.create({
      nzContent: AddOrEditPluginComponent,
      nzFooter: null,
      nzClosable: true,
      nzMaskClosable: false,
      nzWidth: 600
    });

    modalRef.afterClose.subscribe((result) => {
      if (result) {
        this.pluginService.createFromOpenApi(new OpenApiPluginRequestDto({
          pluginName: result.pluginName,
          url: result.openApiUrl,
        })).subscribe({
          next: (plugin) => {
            this.message.success('Plugin created successfully');
            this.loadAllPlugins();
          },
          error: (error) => {
            this.message.error('Failed to create plugin');
            console.error('Error creating plugin:', error);
          }
        });
      }
    });
  }

  openEditModal(plugin: PluginResponseDto) {
    console.log('Opening edit modal');
    this.selectedPlugin = plugin;
    this.isCreateModalVisible = true;
    console.log('Modal state:', {
      isCreateModalVisible: this.isCreateModalVisible,
      selectedPlugin: this.selectedPlugin
    });
  }

  handleModalVisibleChange(visible: boolean) {
    console.log('Modal visibility changed:', visible);
    this.isCreateModalVisible = visible;
    if (!visible) {
      this.selectedPlugin = null;
    }
    console.log('Updated modal state:', {
      isCreateModalVisible: this.isCreateModalVisible,
      selectedPlugin: this.selectedPlugin
    });
  }

  handlePluginCreated(plugin: PluginResponseDto) {
    console.log('Plugin created:', plugin);
    this.loadAllPlugins();
    this.isCreateModalVisible = false;
  }

  handlePluginUpdated(plugin: PluginResponseDto) {
    console.log('Plugin updated:', plugin);
    this.loadAllPlugins();
    this.isCreateModalVisible = false;
  }

  getPluginTypeColor(type: string | undefined): string {
    if (!type) return 'default';
    switch (type.toLowerCase()) {
      case 'openapi':
        return 'blue';
      case 'customplugin':
        return 'green';
      default:
        return 'default';
    }
  }

  formatDate(date: any): string {
    if (!date) return 'N/A';
    return new Date(date.toString()).toLocaleDateString();
  }

  getFunctionList(functions: string | undefined): string[] {
    if (!functions) return [];
    return functions.split('\r\n');
  }

  createPlugin(): void {
    this.modal.create({
      nzTitle: 'Create Plugin',
      nzContent: AddOrEditPluginComponent,
      nzOkText: 'Create',
      nzCancelText: 'Cancel',
      nzOnOk: () => {
        this.loadAllPlugins();
      }
    });
  }
}


