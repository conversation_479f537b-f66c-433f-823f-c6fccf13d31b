import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter, OnInit, AfterViewInit, OnChanges, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { firstValueFrom } from 'rxjs';
import {
  DocsServiceProxy,
  CreateDocsDto,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import EditorJS from '@editorjs/editorjs';
import Header from '@editorjs/header';
import List from '@editorjs/list';
import Quote from '@editorjs/quote';
import CodeTool from '@editorjs/code';
import Delimiter from '@editorjs/delimiter';
import InlineCode from '@editorjs/inline-code';
import Table from '@editorjs/table';
import { DocumentSyncService } from '../../shared/services/document-sync.service';

@Component({
  selector: 'app-save-to-notes-dialog',
  standalone: true,
  imports: [CommonModule, FormsModule, ServiceProxyModule],
  templateUrl: './save-to-notes-dialog.component.html',
  styleUrl: './save-to-notes-dialog.component.css',
  encapsulation: ViewEncapsulation.None
})
export class SaveToNotesDialogComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() visible: boolean = false;
  @Input() prompt: string = '';
  @Input() response: string = '';
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() saveComplete = new EventEmitter<any>();

  title: string = '';
  isSaving: boolean = false;
  private editor: EditorJS | null = null;
  private editorInitialized = false;

  constructor(
    private docsService: DocsServiceProxy,
    private documentSyncService: DocumentSyncService
  ) { }

  ngOnInit(): void {
    // Set default title based on prompt
    this.updateTitle();
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log('SaveToNotesDialog ngOnChanges:', changes);

    // Update title when prompt changes
    if (changes['prompt']) {
      console.log('Prompt changed:', changes['prompt'].currentValue);
      this.updateTitle();
    }

    // Initialize editor when dialog becomes visible
    if (changes['visible'] && changes['visible'].currentValue === true) {
      console.log('Dialog becoming visible, initializing editor...');
      console.log('Current prompt:', this.prompt);
      console.log('Current response:', this.response);
      setTimeout(() => {
        this.initializeEditor();
      }, 100);
    } else if (changes['visible'] && changes['visible'].currentValue === false) {
      console.log('Dialog becoming hidden, destroying editor...');
      this.destroyEditor();
    }
  }

  private updateTitle(): void {
    console.log('Updating title with prompt:', this.prompt);

    if (this.prompt && this.prompt.trim()) {
      // Clean up the prompt and create a meaningful title
      let cleanPrompt = this.prompt.trim()
        .replace(/[\r\n]+/g, ' ') // Replace line breaks with spaces
        .replace(/\s+/g, ' ') // Replace multiple spaces with single space
        .replace(/[^\w\s\-_.?!]/g, '') // Remove special characters except basic punctuation
        .trim();

      // Generate title based on content
      if (cleanPrompt.length > 60) {
        // Find a good break point (sentence end or word boundary)
        const sentences = cleanPrompt.split(/[.!?]+/);
        if (sentences.length > 1 && sentences[0].length <= 50) {
          this.title = sentences[0].trim() + (sentences[0].endsWith('.') ? '' : '...');
        } else {
          // Find word boundary
          const words = cleanPrompt.split(' ');
          let title = '';
          for (const word of words) {
            if ((title + ' ' + word).length > 50) break;
            title = title ? title + ' ' + word : word;
          }
          this.title = title + '...';
        }
      } else {
        this.title = cleanPrompt;
      }

      // Ensure title starts with capital letter
      if (this.title.length > 0) {
        this.title = this.title.charAt(0).toUpperCase() + this.title.slice(1);
      }
    } else {
      // Fallback title
      this.title = 'AI Conversation Note - ' + new Date().toLocaleDateString();
    }

    console.log('Title set to:', this.title);
  }

  ngAfterViewInit(): void {
    // Component view is initialized - editor will be initialized via ngOnChanges when visible becomes true
  }

  private initializeEditor(): void {
    if (this.editor || this.editorInitialized) {
      this.destroyEditor(); // Destroy existing editor first
    }

    const editorElement = document.getElementById('notes-editor');
    if (!editorElement) {
      console.error('Element with ID "notes-editor" is missing.');
      setTimeout(() => {
        this.initializeEditor();
      }, 100);
      return;
    }

    console.log('Initializing EditorJS with prompt:', this.prompt, 'response:', this.response);

    try {
      const editorData = this.convertResponseToEditorJS(this.response);
      console.log('Initializing EditorJS with data:', editorData);

      this.editor = new EditorJS({
        holder: 'notes-editor',
        minHeight: 300,
        placeholder: 'Start editing your note here...',
        tools: {
          header: {
            class: Header as any,
            inlineToolbar: true,
            config: {
              levels: [1, 2, 3, 4],
              defaultLevel: 2
            }
          },
          list: {
            class: List as any,
            inlineToolbar: true,
            config: {
              defaultStyle: 'unordered'
            }
          },
          quote: {
            class: Quote as any,
            inlineToolbar: true,
            config: {
              quotePlaceholder: 'Enter a quote...',
              captionPlaceholder: 'Quote author'
            }
          },
          code: CodeTool as any,
          delimiter: Delimiter as any,
          inlineCode: InlineCode as any,
          table: {
            class: Table as any,
            inlineToolbar: true,
            config: {
              rows: 2,
              cols: 3,
              withHeadings: true
            }
          }
        },
        data: editorData,
        onReady: () => {
          console.log('EditorJS is ready for notes dialog');
          this.editorInitialized = true;

          // Add some custom styling to the editor
          const editorElement = document.getElementById('notes-editor');
          if (editorElement) {
            editorElement.classList.add('dialog-enter');
          }
        },
        onChange: (api, event) => {
          console.log('Editor content changed', event);
        }
      });
    } catch (error) {
      console.error('Error initializing EditorJS:', error);
      // Retry initialization after a delay
      setTimeout(() => {
        this.editorInitialized = false;
        this.initializeEditor();
      }, 500);
    }
  }

  private convertResponseToEditorJS(responseText: string): any {
    console.log('Converting response to EditorJS format:', responseText);

    if (!responseText) {
      return {
        blocks: [
          {
            type: 'paragraph',
            data: {
              text: 'Start editing your note here...'
            }
          }
        ]
      };
    }

    const blocks = [];

    // Add prompt as header if available
    if (this.prompt && this.prompt.trim()) {
      blocks.push({
        type: 'header',
        data: {
          text: '🤔 Your Question',
          level: 2
        }
      });

      // Split prompt into paragraphs if it's long
      const promptParagraphs = this.prompt.split('\n').filter(p => p.trim());
      promptParagraphs.forEach(paragraph => {
        blocks.push({
          type: 'paragraph',
          data: {
            text: paragraph.trim()
          }
        });
      });

      blocks.push({
        type: 'delimiter',
        data: {}
      });

      blocks.push({
        type: 'header',
        data: {
          text: '💡 AI Response',
          level: 2
        }
      });
    }

    // Parse response text more intelligently
    const sections = this.parseResponseText(responseText);

    sections.forEach(section => {
      blocks.push(section);
    });

    // Add a final note if no content
    if (blocks.length === 0) {
      blocks.push({
        type: 'paragraph',
        data: {
          text: 'Your conversation content will appear here. Start editing to customize your note!'
        }
      });
    }

    return { blocks };
  }

  private parseResponseText(text: string): any[] {
    const blocks = [];
    const lines = text.split('\n');
    let currentSection = '';
    let inCodeBlock = false;
    let codeLanguage = '';
    let codeContent = '';
    let listItems = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // Handle code blocks
      if (trimmedLine.startsWith('```')) {
        if (inCodeBlock) {
          // End of code block
          blocks.push({
            type: 'code',
            data: {
              code: codeContent.trim(),
              language: codeLanguage
            }
          });
          inCodeBlock = false;
          codeContent = '';
          codeLanguage = '';
        } else {
          // Start of code block
          inCodeBlock = true;
          codeLanguage = trimmedLine.substring(3).trim();
        }
        continue;
      }

      if (inCodeBlock) {
        codeContent += line + '\n';
        continue;
      }

      // Handle headers
      if (trimmedLine.match(/^#{1,6}\s+/)) {
        // Flush any pending content
        if (currentSection.trim()) {
          this.addParagraphBlock(blocks, currentSection.trim());
          currentSection = '';
        }
        if (listItems.length > 0) {
          this.addListBlock(blocks, listItems);
          listItems = [];
        }

        const headerMatch = trimmedLine.match(/^(#{1,6})\s+(.+)/);
        if (headerMatch) {
          blocks.push({
            type: 'header',
            data: {
              text: headerMatch[2],
              level: Math.min(headerMatch[1].length, 4)
            }
          });
        }
        continue;
      }

      // Handle lists
      if (trimmedLine.match(/^[-*+]\s+/) || trimmedLine.match(/^\d+\.\s+/)) {
        // Flush any pending content
        if (currentSection.trim()) {
          this.addParagraphBlock(blocks, currentSection.trim());
          currentSection = '';
        }

        const listMatch = trimmedLine.match(/^(?:[-*+]|\d+\.)\s+(.+)/);
        if (listMatch) {
          listItems.push(listMatch[1]);
        }
        continue;
      }

      // Handle quotes
      if (trimmedLine.startsWith('>')) {
        // Flush any pending content
        if (currentSection.trim()) {
          this.addParagraphBlock(blocks, currentSection.trim());
          currentSection = '';
        }
        if (listItems.length > 0) {
          this.addListBlock(blocks, listItems);
          listItems = [];
        }

        blocks.push({
          type: 'quote',
          data: {
            text: trimmedLine.substring(1).trim(),
            caption: ''
          }
        });
        continue;
      }

      // Handle empty lines
      if (!trimmedLine) {
        if (currentSection.trim()) {
          this.addParagraphBlock(blocks, currentSection.trim());
          currentSection = '';
        }
        if (listItems.length > 0) {
          this.addListBlock(blocks, listItems);
          listItems = [];
        }
        continue;
      }

      // Regular text line
      if (listItems.length > 0) {
        this.addListBlock(blocks, listItems);
        listItems = [];
      }

      currentSection += (currentSection ? '\n' : '') + line;
    }

    // Flush any remaining content
    if (currentSection.trim()) {
      this.addParagraphBlock(blocks, currentSection.trim());
    }
    if (listItems.length > 0) {
      this.addListBlock(blocks, listItems);
    }

    return blocks;
  }

  private addParagraphBlock(blocks: any[], text: string): void {
    if (text.trim()) {
      blocks.push({
        type: 'paragraph',
        data: {
          text: text.trim()
        }
      });
    }
  }

  private addListBlock(blocks: any[], items: string[]): void {
    if (items.length > 0) {
      blocks.push({
        type: 'list',
        data: {
          style: 'unordered',
          items: items // EditorJS List plugin expects an array of strings
        }
      });
    }
  }

  private destroyEditor(): void {
    if (this.editor) {
      this.editor.destroy();
      this.editor = null;
      this.editorInitialized = false;
    }
  }

  async saveNote(): Promise<void> {
    if (!this.title.trim()) {
      this.showErrorNotification('Please enter a title for your note.');
      return;
    }

    if (this.title.trim().length < 3) {
      this.showErrorNotification('Title must be at least 3 characters long.');
      return;
    }

    this.isSaving = true;

    try {
      // Get content from editor
      let contentToSave = '';

      if (this.editor) {
        const editorData = await this.editor.save();
        contentToSave = JSON.stringify(editorData);
      } else {
        // Fallback: create basic EditorJS structure
        const fallbackData = this.convertResponseToEditorJS(this.response);
        contentToSave = JSON.stringify(fallbackData);
      }

      // Create the document DTO for "My Notes" workspace
      const docsCreate = new CreateDocsDto({
        id: 0, // New document
        title: this.title,
        content: contentToSave,
        workspaceName: 'My Notes', // Save to "My Notes" workspace
        filesToAdd: '',
        filesToDelete: []
      });

      console.log('Saving note to My Notes:', docsCreate);

      // Save the document using the existing authenticated DocsServiceProxy
      const result = await firstValueFrom(
        this.docsService.createOrUpdate(docsCreate)
      );
      console.log('Note saved successfully:', result);

      // Notify document sync service to refresh sidebar
      this.documentSyncService.notifyDocumentUpdated({
        type: 'created',
        document: result,
        workspaceName: 'My Notes'
      });

      // Emit success event
      this.saveComplete.emit(result);

      // Close dialog
      this.close();

      // Show success notification with better UX
      this.showSuccessNotification('Note saved successfully to My Notes!');

    } catch (error) {
      console.error('Error saving note:', error);
      this.showErrorNotification('Failed to save note. Please try again.');
    } finally {
      this.isSaving = false;
    }
  }

  private showSuccessNotification(message: string): void {
    // Create a temporary success notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg flex items-center gap-3 transform translate-x-full opacity-0 transition-all duration-300';
    notification.innerHTML = `
      <i class="ri-check-line text-xl"></i>
      <span>${message}</span>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.classList.remove('translate-x-full', 'opacity-0');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
      notification.classList.add('translate-x-full', 'opacity-0');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  private showErrorNotification(message: string): void {
    // Create a temporary error notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 z-50 bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg flex items-center gap-3 transform translate-x-full opacity-0 transition-all duration-300';
    notification.innerHTML = `
      <i class="ri-error-warning-line text-xl"></i>
      <span>${message}</span>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.classList.remove('translate-x-full', 'opacity-0');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
      notification.classList.add('translate-x-full', 'opacity-0');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 4000);
  }

  close(): void {
    this.visible = false;
    this.visibleChange.emit(false);
    this.destroyEditor();
  }

  onBackdropClick(event: Event): void {
    if (event.target === event.currentTarget) {
      this.close();
    }
  }
}
