import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { ModelDetailsServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { RemoveProviderPrefixPipe } from '../../../../shared/pipes/remove-provider-prefix.pipe';
// import { EmbeddingConfigServiceProxy, ModelDetailsServiceProxy } from '../../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-change-active-model',
  standalone: true,
  imports: [CommonModule, FormsModule, RemoveProviderPrefixPipe],
  template: `
    <div class="p-medium">
      <div class="flex justify-end  gap-2">
        <button
          (click)="onCancel()"
          class="px-3 p-1 bg-[var(--primary-purple)] rounded-md border-none  hover:bg-[var(--secondary-purple)] text-white transition-[var(--transition-default)]t"
        >
          X
        </button>
      </div>
      <h2 class="text-lg font-semibold mb-4 text-text-dark">
        Select Active Model
      </h2>

      <div class="space-y-2">
        <div
          *ngFor="let model of filteredModels"
          class="flex items-center justify-between p-2 border border-hover-blue-gray rounded-small
                    hover:bg-background-light-gray cursor-pointer transition-default"
          (click)="selectModel(model)"
        >
          <div class="flex items-center gap-3">
            <span class="text-body text-text-dark">{{
              model.modelName | removeProviderPrefix
            }}</span>
            <span class="text-sm text-gray-500">{{ model.provider }}</span>
          </div>
          <div class="flex items-center gap-2">
            <i class="ri-checkbox-circle-line text-lg text-green-600"></i>
          </div>
        </div>
      </div>


    </div>
  `,
  styles: [
    `
      :host {
        display: block;
      }
    `,
  ],
})
export class ChangeActiveModelComponent {
  embeddingModels: any[] = [];
  filteredModels: any[] = [];
  @Output() modelChanged = new EventEmitter<any>();

  constructor(
    private modal: NzModalRef,
    // private _embeddingService: EmbeddingConfigServiceProxy,
    private _modelDetails: ModelDetailsServiceProxy
  ) {}

  ngOnInit() {
    this.loadEmbeddingModels();
  }

  loadEmbeddingModels() {
    this._modelDetails.getAllEmbedding().subscribe((res) => {
      if (res) {
        // Filter out the active model
        this.filteredModels = res.filter((model) => !model.isEmbeddingActive);
      }
    });
  }

  selectModel(model: any) {
    this._modelDetails.setEmbeddingToTrue(model.modelName).subscribe(
      (response: any) => {
        if (!response.isError) {
          this.modelChanged.emit(model);
          this.modal.close(model);
        }
      },
      (error: Error) => {
        console.error('Error setting active model:', error);
      }
    );
  }

  onCancel() {
    this.modal.close();
  }
}
