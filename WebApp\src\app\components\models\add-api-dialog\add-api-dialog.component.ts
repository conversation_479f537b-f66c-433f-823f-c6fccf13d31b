import { Component, EventEmitter, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ApiCredentialsServiceProxy, ResponseMessage } from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { FormsModule } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-add-api-dialog',
  standalone: true,
  imports: [CommonModule, ServiceProxyModule, FormsModule],
  templateUrl: './add-api-dialog.component.html',
  styleUrl: './add-api-dialog.component.css'
})
export class AddApiDialogComponent implements OnInit {
  @Output() apiAdded = new EventEmitter<any>();
  @Output() dialogClosed = new EventEmitter<void>();

  apiData: any = {
    tokenUrl: '',
    apiKey: '',
    hasCustomModels: false,
    customModels: [],
    customModelsText: ''
  };

  // For No API toggle
  noApiMode: boolean = false;

  isCredentialsValid = false;
  isValidating = false;
  isTokenUrlValid = false;
  validationMessage = '';

  constructor(
    private apiCredentialsService: ApiCredentialsServiceProxy,
    private nzMessageService: NzMessageService
  ) { }

  ngOnInit() {
  }

  async validateCredentials() {
    // Reset validation state
    this.isValidating = true;
    this.isCredentialsValid = false;
    this.validationMessage = '';

    // Validate token URL first
    if (!this.apiData.tokenUrl || this.apiData.tokenUrl.trim() === '') {
      this.isValidating = false;
      this.validationMessage = 'Token URL is required';
      this.nzMessageService.error(this.validationMessage);
      return;
    }

    // Basic URL validation
    try {
      // Check if it's a valid URL format
      new URL(this.apiData.tokenUrl);
      this.isTokenUrlValid = true;
    } catch (e) {
      this.isValidating = false;
      this.isTokenUrlValid = false;
      this.validationMessage = 'Please enter a valid URL';
      this.nzMessageService.error(this.validationMessage);
      return;
    }

    // Always check validation first
    if (!this.apiData.tokenUrl || this.apiData.tokenUrl.trim() === '') {
      this.isValidating = false;
      this.validationMessage = 'Token URL is required';
      this.nzMessageService.error(this.validationMessage);
      return;
    }

    // Validate API key for normal mode
    if (!this.apiData.apiKey || this.apiData.apiKey.trim() === '') {
      this.isValidating = false;
      this.validationMessage = 'API Key is required';
      this.nzMessageService.error(this.validationMessage);
      return;
    }

    try {
      let res: any = await this.apiCredentialsService
        .validate(this.apiData)
        .toPromise();

      this.isValidating = false;

      if (!res.isError) {
        this.isCredentialsValid = true;
        this.validationMessage = 'Credentials validated successfully';
        this.nzMessageService.success(this.validationMessage);
      } else {
        this.isCredentialsValid = false;
        this.validationMessage = res.message || 'Invalid credentials';
        this.nzMessageService.error(this.validationMessage);
      }
    } catch (error: any) {
      this.isValidating = false;
      this.isCredentialsValid = false;
      this.validationMessage = error.message || 'Validation failed';
      this.nzMessageService.error(this.validationMessage);
    }
  }

  saveApi() {
    // Double-check validation before saving
    if (!this.isCredentialsValid) {
      this.nzMessageService.error('Please validate your credentials before saving');
      return;
    }

    // Show loading message
    const loadingMessage = this.nzMessageService.loading('Saving API credentials...');

    this.apiCredentialsService.create(this.apiData).subscribe({
      next: (res: any) => {
        if (res) {
          this.nzMessageService.success(res.message || 'API credentials saved successfully');
          this.apiAdded.emit(this.apiData);
          this.resetForm();
        }
      },
      error: (error: any) => {
        this.nzMessageService.error(error.message || 'Failed to save API credentials');
      },
      complete: () => {
        // Remove loading message
        this.nzMessageService.remove(loadingMessage.messageId);
      }
    });
  }

  resetForm() {
    this.apiData = {
      tokenUrl: '',
      apiKey: '',
      hasCustomModels: false,
      customModels: [],
      customModelsText: ''
    };
    this.isCredentialsValid = false;
    this.isTokenUrlValid = false;
    this.validationMessage = '';
    this.isValidating = false;
    this.noApiMode = false;
    this.dialogClosed.emit();
  }

  /**
   * Toggle No API mode and set API key accordingly
   */
  toggleNoApiMode() {
    if (this.noApiMode) {
      // Set API key to "no api" when toggle is enabled
      this.apiData.apiKey = "no api";

      // Only auto-validate if token URL is valid
      if (this.apiData.tokenUrl && this.isTokenUrlValid) {
        this.validationMessage = 'No API mode is valid';
      } else {
        // Token URL still needs validation
        this.isCredentialsValid = false;
      }
    } else {
      // Clear API key when toggle is disabled
      this.apiData.apiKey = "";
      this.isCredentialsValid = false;
      this.validationMessage = '';
    }
  }

  /**
   * Update custom models array from text input
   */
  updateCustomModels() {
    if (!this.apiData.customModelsText) {
      this.apiData.customModels = [];
      return;
    }

    // Split by newline and filter out empty lines
    this.apiData.customModels = this.apiData.customModelsText
      .split('\n')
      .map((model: string) => model.trim())
      .filter((model: string | any[]) => model.length > 0);
  }

  onCancel() {
    this.dialogClosed.emit();
  }
}
