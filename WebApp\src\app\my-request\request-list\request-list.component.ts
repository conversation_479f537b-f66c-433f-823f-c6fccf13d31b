import { Component, OnInit, inject } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { getRemoteServiceBaseUrl } from '../../app.config';
import {
  ProjectsServiceProxy,
  ProjectViewDto,
} from '../../../shared/service-proxies/service-proxies';
import { AuthService } from '../../../shared/services/auth.service';
import { DeleteDialogComponent } from '../../dialogs/delete-dialog/delete-dialog.component';
import { ThemeService } from '../../../shared/services/theam.service';
// import { CalendarViewComponent } from '../../user/dialog/calendar-view/calendar-view.component';

@Component({
  selector: 'app-request-list',
  standalone: true,
  imports: [CommonModule, DatePipe, FormsModule, RouterModule, DeleteDialogComponent],
  templateUrl: './request-list.component.html',
  styleUrl: './request-list.component.css',
  providers:[ProjectsServiceProxy, AuthService]
})
export class RequestListComponent implements OnInit {
  baseUrl: string = getRemoteServiceBaseUrl();
  searchQuery: string = '';
  products: ProjectViewDto[] = [];
  filteredProducts: ProjectViewDto[] = [];
  originalProducts: ProjectViewDto[] = [];
  first: number = 0;
  activeFilter: string = 'All';
  viewType: 'table' | 'card' = 'table';
  isLoading: boolean = false;
  showDialog = false;
  itemId: number | null = null;

  // New properties for workspace-like functionality
  workspaceType: string = 'allProjects';
  isCalendarView: boolean = false;
  showAllPastDue: boolean = true;
  userEmail: string | null = null;
  isAllRequestPage: boolean = false;

  // Calendar properties
  currentDate = new Date();
  selectedDate: Date | null = null;
  calendar: Date[][] = [];
  weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  expandedDateTasks: { [key: string]: boolean } = {};

  // Theme service
  themeService = inject(ThemeService);

  constructor(
    // public dialogService:DialogService,
    private router: Router,
    private _projectServices: ProjectsServiceProxy,
    private auth: AuthService
  ) {}

  get isAdmin() {
    return this.auth.isAdmin();
  }

  ngOnInit(): void {
    // Check if we're on the all-request page
    this.isAllRequestPage = this.router.url.includes('/all-request');

    // Get user email for filtering
    const user = this.auth.getUser();
    this.userEmail = user?.email ?? null;

    // Initialize calendar
    this.generateCalendar();

    this.LoadAllData();
  }
  LoadAllData() {
    this.isLoading = true;
    this._projectServices.getAllForUSer().subscribe(
      (res) => {
        console.log(res);
        this.products = res;
        this.originalProducts = res;
        this.filteredProducts = res;
        this.isLoading = false;

        // Apply initial workspace filtering if on all-request page
        if (this.isAllRequestPage) {
          this.loadAllProjects();
        }
      },
      () => {
        this.isLoading = false;
      }
    );
  }

  isImage(fileName: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif']; // Add more image extensions as needed
    const fileExtension = fileName
      .substring(fileName.lastIndexOf('.'))
      .toLowerCase();
    return imageExtensions.includes(fileExtension);
  }
  searchProjects(event: any) {
    this.filteredProducts = this.products.filter((project) =>
      project.subject
        ?.toLowerCase()
        .includes(this.searchQuery.toLocaleLowerCase())
    );
  }
  filterProjectsByStatus(status: string) {
    this.activeFilter = status;
    if (status === 'All') {
      this.filteredProducts = this.products;
      return;
    }
    this.filteredProducts = this.products.filter(
      (project) => project.status === status
    );
  }
  // toasterService=inject(ToasterService)

  deleteClicked(event: MouseEvent, id: number) {
    event.stopPropagation();

    this.itemId = id;
    this.showDialog = true;
  }
  onDeleteConfirmed(confirmed: Boolean) {
    if (confirmed && this.itemId != null) {
      this.deleteItem(this.itemId);
    }
    this.showDialog = false;
    this.itemId = null;
  }

  onDeleteCancelled() {
    this.showDialog = false;
    this.itemId = null;
  }

  deleteItem(id: number) {
    this._projectServices.delete(id).subscribe((res) => {
      if (res) {
        this.LoadAllData();
      }
    });
  }

  editProject(id: number) {
    this.router.navigate(['/my-request/edit-request', id]);
  }

  goToProject(id: number) {
    if (id === 0) {
      this.router.navigate(['/my-request/add-request']);
    } else {
      this.router.navigate(['/my-request/details', id]);
    }
  }

  next() {
    this.first += 5;
  }

  prev() {
    this.first -= 5;
  }

  reset() {
    this.first = 0;
  }

  isFirstPage(): boolean {
    return this.first === 0;
  }

  isLastPage(): boolean {
    return this.first === this.products.length - 5;
  }

  pageChange(event: any) {
    this.first = event.first;
  }

  getOpenProjects(): number {
    return this.products.filter((p) => p.status === 'Open').length;
  }

  getCompletedProjects(): number {
    return this.products.filter((p) => p.status === 'Completed').length;
  }

  getInProgressProjects(): number {
    return this.products.filter((p) => p.status === 'In Progress').length;
  }

  isOverdue(project: ProjectViewDto): boolean {
    const dueDate = new Date(project.completionDate.toJSDate());
    return dueDate < new Date() && project.status !== 'Completed';
  }

  toggleView(type: 'card' | 'table') {
    this.viewType = type;
  }

  // New workspace-style methods
  loadAllProjects() {
    this.activeFilter = 'All';
    switch (this.workspaceType) {
      case 'allProjects':
        this.isCalendarView = false;
        // Show all non-overdue, non-completed projects by default
        this.products = this.originalProducts.filter(
          (project) => !this.isPastDue(project)
        );
        this.filteredProducts = this.products;
        break;

      case 'assigned':
        this.isCalendarView = false;
        // Filter for current user's non-overdue, non-completed projects
        this.products = this.originalProducts.filter(
          (project) =>
            project.assignedEmail === this.userEmail &&
            !this.isPastDue(project)
        );
        this.filteredProducts = this.products;
        break;

      case 'dueProject':
        this.isCalendarView = false;
        // Filter for overdue projects
        this.products = this.originalProducts.filter(
          (project) => this.isPastDue(project)
        );
        this.filteredProducts = this.products;
        break;

      case 'calendar':
        this.isCalendarView = true;
        break;

      default:
        this.products = this.originalProducts;
        this.filteredProducts = this.products;
        break;
    }
  }

  isPastDue(item: ProjectViewDto): boolean {
    const dueDate = new Date(item.completionDate.toJSDate());
    const now = new Date();
    return dueDate < now && item.status?.toLowerCase() !== 'completed';
  }

  onAllProjects() {
    this.workspaceType = 'allProjects';
    this.loadAllProjects();
  }

  onAssigned() {
    this.workspaceType = 'assigned';
    this.loadAllProjects();
  }

  onOverDue() {
    this.workspaceType = 'dueProject';
    this.loadAllProjects();
  }

  onCalendar() {
    this.workspaceType = 'calendar';
    this.isCalendarView = true;
    this.generateCalendar();
  }

  togglePastDueFilter() {
    this.showAllPastDue = !this.showAllPastDue;
  }

  getProjectStats() {
    const myOverdue = this.originalProducts.filter(
      (project) =>
        project.assignedEmail === this.userEmail && this.isPastDue(project)
    ).length;

    const totalOverdue = this.originalProducts.filter((project) =>
      this.isPastDue(project)
    ).length;

    const myAssigned = this.originalProducts.filter(
      (project) => project.assignedEmail === this.userEmail
    ).length;

    const totalProjects = this.originalProducts.length;

    return {
      myOverdue,
      totalOverdue,
      myAssigned,
      totalProjects,
    };
  }

  // Calendar methods
  generateCalendar() {
    const firstDay = new Date(
      this.currentDate.getFullYear(),
      this.currentDate.getMonth(),
      1
    );
    const lastDay = new Date(
      this.currentDate.getFullYear(),
      this.currentDate.getMonth() + 1,
      0
    );
    const weeks: any[][] = [];
    let currentWeek: (Date | null)[] = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay.getDay(); i++) {
      currentWeek.push(null);
    }

    // Add all days of the month
    for (let day = 1; day <= lastDay.getDate(); day++) {
      if (currentWeek.length === 7) {
        weeks.push(currentWeek);
        currentWeek = [];
      }
      currentWeek.push(
        new Date(
          this.currentDate.getFullYear(),
          this.currentDate.getMonth(),
          day
        )
      );
    }

    // Fill the last week with empty cells if needed
    while (currentWeek.length < 7) {
      currentWeek.push(null);
    }
    weeks.push(currentWeek);

    this.calendar = weeks;
  }

  previousMonth() {
    this.currentDate = new Date(
      this.currentDate.getFullYear(),
      this.currentDate.getMonth() - 1
    );
    this.generateCalendar();
  }

  nextMonth() {
    this.currentDate = new Date(
      this.currentDate.getFullYear(),
      this.currentDate.getMonth() + 1
    );
    this.generateCalendar();
  }

  getTasksForDate(date: Date): ProjectViewDto[] {
    if (!date) return [];
    const dateStr = date.toISOString().split('T')[0];
    return this.originalProducts.filter((project) => {
      const projectDate = new Date(project.completionDate.toJSDate()).toISOString().split('T')[0];
      return projectDate === dateStr;
    });
  }

  getTasksForCurrentMonth(): number {
    const firstDay = new Date(
      this.currentDate.getFullYear(),
      this.currentDate.getMonth(),
      1
    );
    const lastDay = new Date(
      this.currentDate.getFullYear(),
      this.currentDate.getMonth() + 1,
      0
    );

    return this.originalProducts.filter(project => {
      const projectDate = new Date(project.completionDate.toJSDate());
      return projectDate >= firstDay && projectDate <= lastDay;
    }).length;
  }

  isToday(date: Date | null): boolean {
    if (!date) return false;
    const today = new Date();
    return date.toDateString() === today.toDateString();
  }

  getDateKey(date: Date | null): string {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  }

  toggleTaskList(date: Date) {
    if (!date) return;
    const dateKey = this.getDateKey(date);
    this.expandedDateTasks[dateKey] = !this.expandedDateTasks[dateKey];
  }

  // Additional helper methods for stats
  getInProgressCount(): number {
    return this.originalProducts.filter(project =>
      project.status?.toLowerCase() === 'in progress'
    ).length;
  }

  getOpenCount(): number {
    return this.originalProducts.filter(project =>
      project.status?.toLowerCase() === 'open' ||
      project.status?.toLowerCase() === 'pending'
    ).length;
  }

  getCompletedCount(): number {
    return this.originalProducts.filter(project =>
      project.status?.toLowerCase() === 'completed'
    ).length;
  }
}
