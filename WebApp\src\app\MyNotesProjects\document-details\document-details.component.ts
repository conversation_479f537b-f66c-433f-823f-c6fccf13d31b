import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { NotesService, Note } from '../services/notes.service';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

interface EditorBlock {
  id: string;
  type: string;
  data: {
    text?: string;
    level?: number;
    style?: string;
    items?: Array<string | {
      content: string;
      items: any[];
    }>;
    code?: string;
    content?: string[][];
    withHeadings?: boolean;
    link?: string;
    meta?: {
      image?: {
        url?: string;
      };
      title?: string;
      description?: string;
    };
  };
}

interface EditorData {
  time: number;
  blocks: EditorBlock[];
  version: string;
}

@Component({
  selector: 'app-document-details',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './document-details.component.html',
  styleUrls: ['./document-details.component.css']
})
export class DocumentDetailsComponent implements OnInit {
  note: Note | null = null;
  formattedContent: SafeHtml = '';
  @Output() favoriteToggled = new EventEmitter<Note>();
  @Output() recentNoteUpdated = new EventEmitter<any>();
  documentId!: number;
  document!: any; // Define a proper interface if available
  isLoading: boolean = true;

  constructor(
    private route: ActivatedRoute,
    private notesService: NotesService,
    private sanitizer: DomSanitizer,
    private router: Router
  ) { }

  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.documentId = +id;
        this.fetchDocumentDetails();
      }
    });
  }

  fetchDocumentDetails(): void {
    this.notesService.getNoteById(this.documentId).subscribe({
      next: (doc) => {
        this.document = doc;
        this.isLoading = false;
        if (doc.content) {
          this.formatContent(doc.content);
        }

        // Call updateRecentlyOpened after the note is loaded
        this.notesService.updateRecentlyOpened(this.document.id).subscribe({
          next: (updatedNote) => {
            console.log('Recently opened updated:', updatedNote);
            this.note = updatedNote;
            // Emit event to trigger recent notes reload
            this.recentNoteUpdated.emit();
          },
          error: (error) => {
            console.error('Error updating recently opened:', error);
          }
        });
      },
      error: (error) => {
        console.error('Error fetching document:', error);
        this.isLoading = false;
      }
    });
  }

  toggleFavorite() {
    if (this.note?.id) {
      this.notesService.toggleFavorite(this.note.id).subscribe({
        next: (updatedNote) => {
          console.log(updatedNote);
          if (this.note) {
            this.note.isFavourite = updatedNote.isFavourite;
            this.favoriteToggled.emit(this.note);
          }
        },
        error: (error) => {
          console.error('Error toggling favorite:', error);
        }
      });
    }
  }

  private formatContent(content: string) {
    try {
      console.log('Raw content:', content); // Debug log
      const editorData: EditorData = JSON.parse(content);
      console.log('Parsed editorData:', editorData); // Debug log
      let html = '';

      editorData.blocks.forEach((block, blockIndex) => {
        console.log(`Block ${blockIndex}:`, block); // Debug log
        switch (block.type) {
          case 'paragraph':
            html += `<p class="mb-4">${block.data.text || ''}</p>`;
            break;
          case 'header':
            const level = block.data.level || 1;
            html += `<h${level} class="text-${4 - level}xl font-bold mb-4 text-[var(--text-color)]">${block.data.text || ''}</h${level}>`;
            break;
          case 'list':
            console.log('List block data:', block.data); // Debug log
            const listType = block.data.style === 'ordered' ? 'ol' : 'ul';
            const listClass = block.data.style === 'ordered' ? 'list-decimal' : 'list-disc';
            html += `<${listType} class="${listClass} pl-6 mb-4">`;

            if (block.data.items && Array.isArray(block.data.items)) {
              block.data.items.forEach((item, index) => {
                console.log(`List item ${index}:`, item, typeof item); // Debug log
                let itemText = '';

                if (typeof item === 'string') {
                  itemText = item;
                } else if (typeof item === 'object' && item !== null) {
                  // Try different possible properties
                  if ('content' in item) {
                    itemText = (item as any).content;
                  } else if ('text' in item) {
                    itemText = (item as any).text;
                  } else if ('value' in item) {
                    itemText = (item as any).value;
                  } else {
                    // Last resort: try to extract meaningful text from object
                    const objStr = JSON.stringify(item);
                    console.warn('Converting object to string for list item:', objStr);
                    itemText = `Content: ${objStr}`;
                  }
                } else {
                  itemText = String(item || '');
                }

                html += `<li class="mb-2">${itemText}</li>`;
              });
            } else {
              console.warn('List block has no items or items is not an array:', block.data);
              html += `<li class="mb-2">No list items found</li>`;
            }

            html += `</${listType}>`;
            break;
          case 'quote':
            html += `<blockquote class="border-l-4 border-blue-500 pl-4 italic mb-4 text-gray-600">${block.data.text}</blockquote>`;
            break;
          case 'code':
            html += `<pre class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mb-4 overflow-x-auto"><code class="text-sm">${block.data.code}</code></pre>`;
            break;
          case 'delimiter':
            html += `<div class="text-center mb-4"><span class="text-2xl text-gray-400">* * *</span></div>`;
            break;
          case 'table':
            html += '<table class="table-auto border-collapse border border-gray-300 mb-4 w-full">';
            if (block.data.content) {
              block.data.content.forEach((row: string[], index: number) => {
                const isHeader = index === 0 && block.data.withHeadings;
                html += `<tr${isHeader ? ' class="bg-gray-100 dark:bg-gray-700"' : ''}>`;
                row.forEach(cell => {
                  const tag = isHeader ? 'th' : 'td';
                  html += `<${tag} class="border border-gray-300 px-4 py-2">${cell}</${tag}>`;
                });
                html += '</tr>';
              });
            }
            html += '</table>';
            break;
          case 'link':
            // Format link block
            html += `
              <div class="mb-4 p-4 border rounded-lg border-gray-200 dark:border-gray-700">
                <a href="${block.data.link}" target="_blank" rel="noopener noreferrer"
                   class="flex items-start no-underline">
                  ${block.data.meta?.image?.url ? `
                    <div class="flex-shrink-0 mr-4">
                      <img src="${block.data.meta.image.url}" alt=""
                           class="w-16 h-16 object-cover rounded">
                    </div>
                  ` : ''}
                  <div class="flex-grow">
                    <h3 class="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-1">
                      ${block.data.meta?.title || block.data.link}
                    </h3>
                    ${block.data.meta?.description ? `
                      <p class="text-gray-600 dark:text-gray-300 text-sm line-clamp-2">
                        ${block.data.meta.description}
                      </p>
                    ` : ''}
                    <span class="text-gray-500 dark:text-gray-400 text-xs">
                      ${block.data.link ? new URL(block.data.link).hostname : ''}
                    </span>
                  </div>
                </a>
              </div>
            `;
            break;
          // Add more cases for other block types as needed
        }
      });

      this.formattedContent = this.sanitizer.bypassSecurityTrustHtml(html);
    } catch (e) {
      console.error('Error parsing content:', e);
      this.formattedContent = 'Error displaying content';
    }
  }


  editDocument(id: number) {
    this.router.navigate(['editor', id]);
  }

}
