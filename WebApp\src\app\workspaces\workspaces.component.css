
/* Button styles */
.action-button {
  opacity: 0.9;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.action-button:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

tr:hover .action-button {
  opacity: 1;
}

/* Dark mode button styles */
:host-context(.dark) .action-button {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

:host-context(.dark) .action-button:first-child {
  background-color: #68ca8e;
}

:host-context(.dark) .action-button:first-child:hover {
  background-color: var(--primary-purple);
  color:#FFF;
}

:host-context(.dark) .action-button:last-child {
  background-color: #742A2A;
}

:host-context(.dark) .action-button:last-child:hover {
  background-color: #9B2C2C;
}

 .action-button:last-child {
  background-color: #eb5e5e;
  color:red;
}
 .action-button:last-child:hover {
  background-color: #eb5e5e;
  color:red;
}
