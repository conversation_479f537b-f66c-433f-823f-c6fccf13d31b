import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { ProjectTask, TaskServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { ThemeService } from '../../../shared/services/theam.service';

@Component({
  selector: 'app-task-management',
  templateUrl: './task-management.component.html',
  styleUrls: ['./task-management.component.css'],
  imports: [FormsModule, CommonModule, ServiceProxyModule],
  standalone: true,
  providers: [TaskServiceProxy]
})
export class TaskManagementComponent implements OnInit {
  tabs: string[] = ['All', 'Today', 'Upcoming', 'Past Due'];
  activeTab: string = 'All';
  projects: ProjectTask[] = [];
  originalProjects: ProjectTask[] = [];
  filteredProjects: ProjectTask[] = [];
  workspaceId: number = 0;
  isLoading: boolean = false;

  // Inject services
  themeService = inject(ThemeService);

  constructor(private _taskServices: TaskServiceProxy, private router: Router, private route: ActivatedRoute) {}

  ngOnInit(): void {
    this.route.params.subscribe(() => {
      const urlSegments = this.router.url.split('/');
      this.workspaceId = +urlSegments[urlSegments.indexOf('workspace') + 1]; // Get the workspace ID from the URL
      console.log(this.workspaceId);
      if (this.workspaceId) {
        this.loadTasks();
      }
    });
    this.loadTasks();
  }

  loadTasks(): void {
    this.isLoading = true;
    this._taskServices.getProjectTasksByWorkspacesId(this.workspaceId).subscribe({
      next: (res: ProjectTask[]) => {
        this.projects = res;
        this.originalProjects = res;
        this.filterProjects();
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      }
    });

    this._taskServices.getTodayProjectTasks().subscribe({
      next: (res: ProjectTask[]) => {
        if (this.activeTab === 'Today') {
          this.filteredProjects = res;
        }
      }
    });

    this._taskServices.getUpcomingProjectTasks().subscribe({
      next: (res: ProjectTask[]) => {
        if (this.activeTab === 'Upcoming') {
          this.filteredProjects = res;
        }
      }
    });

    this._taskServices.getPastDueProjectTasks().subscribe({
      next: (res: ProjectTask[]) => {
        if (this.activeTab === 'Past Due') {
          this.filteredProjects = res;
        }
      }
    });
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
    this.filterProjects();
  }

  filterProjects(): void {
    this.isLoading = true;
    switch (this.activeTab) {
      case 'Today':
        this._taskServices.getTodayProjectTasks().subscribe({
          next: (res: ProjectTask[]) => {
            this.filteredProjects = res;
            this.isLoading = false;
          },
          error: () => {
            this.isLoading = false;
          }
        });
        break;
      case 'Upcoming':
        this._taskServices.getUpcomingProjectTasks().subscribe({
          next: (res: ProjectTask[]) => {
            this.filteredProjects = res;
            this.isLoading = false;
          },
          error: () => {
            this.isLoading = false;
          }
        });
        break;
      case 'Past Due':
        this._taskServices.getPastDueProjectTasks().subscribe({
          next: (res: ProjectTask[]) => {
            this.filteredProjects = res;
            this.isLoading = false;
          },
          error: () => {
            this.isLoading = false;
          }
        });
        break;
      default:
        this.filteredProjects = this.originalProjects;
        this.isLoading = false;
        break;
    }
  }

  isOverdue(project: ProjectTask): boolean {
    const today = new Date();
    const completionDate = new Date(project.dueDate.toString());
    return completionDate < today;
  }

  viewProject(projectId: number): void {
    this.router.navigate(['/projects', projectId]);
  }

  /**
   * TrackBy function for tab buttons to improve performance
   */
  trackByTab(_index: number, tab: string): string {
    return tab;
  }

  /**
   * TrackBy function for project items to improve performance
   */
  trackByProject(_index: number, project: ProjectTask): number {
    return project.id;
  }
}
