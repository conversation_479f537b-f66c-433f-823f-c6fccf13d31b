{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-empty.mjs"], "sourcesContent": ["import * as i2$1 from '@angular/cdk/portal';\nimport { TemplatePortal, ComponentPortal, PortalModule } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, TemplateRef, Type, Injector, NgModule } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil, startWith } from 'rxjs/operators';\nimport * as i2 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i1 from 'ng-zorro-antd/i18n';\nimport * as i1$1 from 'ng-zorro-antd/core/config';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction NzEmptyComponent_Conditional_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.nzNotFoundImage, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.isContentString ? ctx_r0.nzNotFoundContent : \"empty\");\n  }\n}\nfunction NzEmptyComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzEmptyComponent_Conditional_1_ng_container_0_Template, 2, 2, \"ng-container\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzNotFoundImage);\n  }\n}\nfunction NzEmptyComponent_Conditional_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-empty-simple\");\n  }\n}\nfunction NzEmptyComponent_Conditional_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-empty-default\");\n  }\n}\nfunction NzEmptyComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzEmptyComponent_Conditional_2_Conditional_0_Template, 1, 0, \"nz-empty-simple\")(1, NzEmptyComponent_Conditional_2_Conditional_1_Template, 1, 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, ctx_r0.nzNotFoundImage === \"simple\" ? 0 : 1);\n  }\n}\nfunction NzEmptyComponent_Conditional_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isContentString ? ctx_r0.nzNotFoundContent : ctx_r0.locale[\"description\"], \" \");\n  }\n}\nfunction NzEmptyComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 1);\n    i0.ɵɵtemplate(1, NzEmptyComponent_Conditional_3_ng_container_1_Template, 2, 1, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzNotFoundContent);\n  }\n}\nfunction NzEmptyComponent_Conditional_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.nzNotFoundFooter, \" \");\n  }\n}\nfunction NzEmptyComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, NzEmptyComponent_Conditional_4_ng_container_1_Template, 2, 1, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzNotFoundFooter);\n  }\n}\nfunction NzEmbedEmptyComponent_Conditional_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.content, \" \");\n  }\n}\nfunction NzEmbedEmptyComponent_Conditional_0_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzEmbedEmptyComponent_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzEmbedEmptyComponent_Conditional_0_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"cdkPortalOutlet\", ctx_r0.contentPortal);\n  }\n}\nfunction NzEmbedEmptyComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzEmbedEmptyComponent_Conditional_0_Conditional_0_Template, 1, 1)(1, NzEmbedEmptyComponent_Conditional_0_Conditional_1_Template, 1, 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, ctx_r0.contentType === \"string\" ? 0 : 1);\n  }\n}\nfunction NzEmbedEmptyComponent_Conditional_1_Conditional_0_Case_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-empty\", 1);\n  }\n}\nfunction NzEmbedEmptyComponent_Conditional_1_Conditional_0_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-empty\", 2);\n  }\n}\nfunction NzEmbedEmptyComponent_Conditional_1_Conditional_0_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-empty\");\n  }\n}\nfunction NzEmbedEmptyComponent_Conditional_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzEmbedEmptyComponent_Conditional_1_Conditional_0_Case_0_Template, 1, 0)(1, NzEmbedEmptyComponent_Conditional_1_Conditional_0_Case_1_Template, 1, 0)(2, NzEmbedEmptyComponent_Conditional_1_Conditional_0_Case_2_Template, 1, 0);\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(0, (tmp_2_0 = ctx_r0.size) === \"normal\" ? 0 : tmp_2_0 === \"small\" ? 1 : 2);\n  }\n}\nfunction NzEmbedEmptyComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzEmbedEmptyComponent_Conditional_1_Conditional_0_Template, 3, 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, ctx_r0.specificContent !== null ? 0 : -1);\n  }\n}\nconst NZ_EMPTY_COMPONENT_NAME = new InjectionToken('nz-empty-component-name');\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzEmptyDefaultComponent {\n  static {\n    this.ɵfac = function NzEmptyDefaultComponent_Factory(t) {\n      return new (t || NzEmptyDefaultComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzEmptyDefaultComponent,\n      selectors: [[\"nz-empty-default\"]],\n      exportAs: [\"nzEmptyDefault\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 0,\n      consts: [[\"width\", \"184\", \"height\", \"152\", \"viewBox\", \"0 0 184 152\", \"xmlns\", \"http://www.w3.org/2000/svg\", 1, \"ant-empty-img-default\"], [\"fill\", \"none\", \"fill-rule\", \"evenodd\"], [\"transform\", \"translate(24 31.67)\"], [\"cx\", \"67.797\", \"cy\", \"106.89\", \"rx\", \"67.797\", \"ry\", \"12.668\", 1, \"ant-empty-img-default-ellipse\"], [\"d\", \"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z\", 1, \"ant-empty-img-default-path-1\"], [\"d\", \"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z\", \"transform\", \"translate(13.56)\", 1, \"ant-empty-img-default-path-2\"], [\"d\", \"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z\", 1, \"ant-empty-img-default-path-3\"], [\"d\", \"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z\", 1, \"ant-empty-img-default-path-4\"], [\"d\", \"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z\", 1, \"ant-empty-img-default-path-5\"], [\"transform\", \"translate(149.65 15.383)\", 1, \"ant-empty-img-default-g\"], [\"cx\", \"20.654\", \"cy\", \"3.167\", \"rx\", \"2.849\", \"ry\", \"2.815\"], [\"d\", \"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z\"]],\n      template: function NzEmptyDefaultComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(0, \"svg\", 0)(1, \"g\", 1)(2, \"g\", 2);\n          i0.ɵɵelement(3, \"ellipse\", 3)(4, \"path\", 4)(5, \"path\", 5)(6, \"path\", 6)(7, \"path\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"path\", 8);\n          i0.ɵɵelementStart(9, \"g\", 9);\n          i0.ɵɵelement(10, \"ellipse\", 10)(11, \"path\", 11);\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzEmptyDefaultComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-empty-default',\n      exportAs: 'nzEmptyDefault',\n      standalone: true,\n      template: `\n    <svg\n      class=\"ant-empty-img-default\"\n      width=\"184\"\n      height=\"152\"\n      viewBox=\"0 0 184 152\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <g fill=\"none\" fill-rule=\"evenodd\">\n        <g transform=\"translate(24 31.67)\">\n          <ellipse class=\"ant-empty-img-default-ellipse\" cx=\"67.797\" cy=\"106.89\" rx=\"67.797\" ry=\"12.668\" />\n          <path\n            class=\"ant-empty-img-default-path-1\"\n            d=\"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z\"\n          />\n          <path\n            class=\"ant-empty-img-default-path-2\"\n            d=\"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z\"\n            transform=\"translate(13.56)\"\n          />\n          <path\n            class=\"ant-empty-img-default-path-3\"\n            d=\"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z\"\n          />\n          <path\n            class=\"ant-empty-img-default-path-4\"\n            d=\"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z\"\n          />\n        </g>\n        <path\n          class=\"ant-empty-img-default-path-5\"\n          d=\"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z\"\n        />\n        <g class=\"ant-empty-img-default-g\" transform=\"translate(149.65 15.383)\">\n          <ellipse cx=\"20.654\" cy=\"3.167\" rx=\"2.849\" ry=\"2.815\" />\n          <path d=\"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z\" />\n        </g>\n      </g>\n    </svg>\n  `\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzEmptySimpleComponent {\n  static {\n    this.ɵfac = function NzEmptySimpleComponent_Factory(t) {\n      return new (t || NzEmptySimpleComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzEmptySimpleComponent,\n      selectors: [[\"nz-empty-simple\"]],\n      exportAs: [\"nzEmptySimple\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 0,\n      consts: [[\"width\", \"64\", \"height\", \"41\", \"viewBox\", \"0 0 64 41\", \"xmlns\", \"http://www.w3.org/2000/svg\", 1, \"ant-empty-img-simple\"], [\"transform\", \"translate(0 1)\", \"fill\", \"none\", \"fill-rule\", \"evenodd\"], [\"cx\", \"32\", \"cy\", \"33\", \"rx\", \"32\", \"ry\", \"7\", 1, \"ant-empty-img-simple-ellipse\"], [\"fill-rule\", \"nonzero\", 1, \"ant-empty-img-simple-g\"], [\"d\", \"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z\"], [\"d\", \"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z\", 1, \"ant-empty-img-simple-path\"]],\n      template: function NzEmptySimpleComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(0, \"svg\", 0)(1, \"g\", 1);\n          i0.ɵɵelement(2, \"ellipse\", 2);\n          i0.ɵɵelementStart(3, \"g\", 3);\n          i0.ɵɵelement(4, \"path\", 4)(5, \"path\", 5);\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzEmptySimpleComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-empty-simple',\n      exportAs: 'nzEmptySimple',\n      template: `\n    <svg class=\"ant-empty-img-simple\" width=\"64\" height=\"41\" viewBox=\"0 0 64 41\" xmlns=\"http://www.w3.org/2000/svg\">\n      <g transform=\"translate(0 1)\" fill=\"none\" fill-rule=\"evenodd\">\n        <ellipse class=\"ant-empty-img-simple-ellipse\" cx=\"32\" cy=\"33\" rx=\"32\" ry=\"7\" />\n        <g class=\"ant-empty-img-simple-g\" fill-rule=\"nonzero\">\n          <path\n            d=\"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z\"\n          />\n          <path\n            d=\"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z\"\n            class=\"ant-empty-img-simple-path\"\n          />\n        </g>\n      </g>\n    </svg>\n  `,\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst NzEmptyDefaultImages = ['default', 'simple'];\nclass NzEmptyComponent {\n  constructor(i18n, cdr) {\n    this.i18n = i18n;\n    this.cdr = cdr;\n    this.nzNotFoundImage = 'default';\n    this.isContentString = false;\n    this.isImageBuildIn = true;\n    this.destroy$ = new Subject();\n  }\n  ngOnChanges(changes) {\n    const {\n      nzNotFoundContent,\n      nzNotFoundImage\n    } = changes;\n    if (nzNotFoundContent) {\n      const content = nzNotFoundContent.currentValue;\n      this.isContentString = typeof content === 'string';\n    }\n    if (nzNotFoundImage) {\n      const image = nzNotFoundImage.currentValue || 'default';\n      this.isImageBuildIn = NzEmptyDefaultImages.findIndex(i => i === image) > -1;\n    }\n  }\n  ngOnInit() {\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Empty');\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzEmptyComponent_Factory(t) {\n      return new (t || NzEmptyComponent)(i0.ɵɵdirectiveInject(i1.NzI18nService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzEmptyComponent,\n      selectors: [[\"nz-empty\"]],\n      hostAttrs: [1, \"ant-empty\"],\n      inputs: {\n        nzNotFoundImage: \"nzNotFoundImage\",\n        nzNotFoundContent: \"nzNotFoundContent\",\n        nzNotFoundFooter: \"nzNotFoundFooter\"\n      },\n      exportAs: [\"nzEmpty\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"ant-empty-image\"], [1, \"ant-empty-description\"], [1, \"ant-empty-footer\"], [4, \"nzStringTemplateOutlet\"], [3, \"src\", \"alt\"]],\n      template: function NzEmptyComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, NzEmptyComponent_Conditional_1_Template, 1, 1, \"ng-container\")(2, NzEmptyComponent_Conditional_2_Template, 2, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, NzEmptyComponent_Conditional_3_Template, 2, 1, \"p\", 1)(4, NzEmptyComponent_Conditional_4_Template, 2, 1, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, !ctx.isImageBuildIn ? 1 : 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(3, ctx.nzNotFoundContent !== null ? 3 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(4, ctx.nzNotFoundFooter ? 4 : -1);\n        }\n      },\n      dependencies: [NzOutletModule, i2.NzStringTemplateOutletDirective, NzEmptyDefaultComponent, NzEmptySimpleComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzEmptyComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-empty',\n      exportAs: 'nzEmpty',\n      template: `\n    <div class=\"ant-empty-image\">\n      @if (!isImageBuildIn) {\n        <ng-container *nzStringTemplateOutlet=\"nzNotFoundImage\">\n          <img [src]=\"nzNotFoundImage\" [alt]=\"isContentString ? nzNotFoundContent : 'empty'\" />\n        </ng-container>\n      } @else {\n        @if (nzNotFoundImage === 'simple') {\n          <nz-empty-simple />\n        } @else {\n          <nz-empty-default />\n        }\n      }\n    </div>\n    @if (nzNotFoundContent !== null) {\n      <p class=\"ant-empty-description\">\n        <ng-container *nzStringTemplateOutlet=\"nzNotFoundContent\">\n          {{ isContentString ? nzNotFoundContent : locale['description'] }}\n        </ng-container>\n      </p>\n    }\n\n    @if (nzNotFoundFooter) {\n      <div class=\"ant-empty-footer\">\n        <ng-container *nzStringTemplateOutlet=\"nzNotFoundFooter\">\n          {{ nzNotFoundFooter }}\n        </ng-container>\n      </div>\n    }\n  `,\n      host: {\n        class: 'ant-empty'\n      },\n      imports: [NzOutletModule, NzEmptyDefaultComponent, NzEmptySimpleComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.NzI18nService\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    nzNotFoundImage: [{\n      type: Input\n    }],\n    nzNotFoundContent: [{\n      type: Input\n    }],\n    nzNotFoundFooter: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getEmptySize(componentName) {\n  switch (componentName) {\n    case 'table':\n    case 'list':\n      return 'normal';\n    case 'select':\n    case 'tree-select':\n    case 'cascader':\n    case 'transfer':\n      return 'small';\n    default:\n      return '';\n  }\n}\nclass NzEmbedEmptyComponent {\n  constructor(configService, viewContainerRef, cdr, injector) {\n    this.configService = configService;\n    this.viewContainerRef = viewContainerRef;\n    this.cdr = cdr;\n    this.injector = injector;\n    this.contentType = 'string';\n    this.size = '';\n    this.destroy$ = new Subject();\n  }\n  ngOnChanges(changes) {\n    if (changes.nzComponentName) {\n      this.size = getEmptySize(changes.nzComponentName.currentValue);\n    }\n    if (changes.specificContent && !changes.specificContent.isFirstChange()) {\n      this.content = changes.specificContent.currentValue;\n      this.renderEmpty();\n    }\n  }\n  ngOnInit() {\n    this.subscribeDefaultEmptyContentChange();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  renderEmpty() {\n    const content = this.content;\n    if (typeof content === 'string') {\n      this.contentType = 'string';\n    } else if (content instanceof TemplateRef) {\n      const context = {\n        $implicit: this.nzComponentName\n      };\n      this.contentType = 'template';\n      this.contentPortal = new TemplatePortal(content, this.viewContainerRef, context);\n    } else if (content instanceof Type) {\n      const injector = Injector.create({\n        parent: this.injector,\n        providers: [{\n          provide: NZ_EMPTY_COMPONENT_NAME,\n          useValue: this.nzComponentName\n        }]\n      });\n      this.contentType = 'component';\n      this.contentPortal = new ComponentPortal(content, this.viewContainerRef, injector);\n    } else {\n      this.contentType = 'string';\n      this.contentPortal = undefined;\n    }\n    this.cdr.detectChanges();\n  }\n  subscribeDefaultEmptyContentChange() {\n    this.configService.getConfigChangeEventForComponent('empty').pipe(startWith(true), takeUntil(this.destroy$)).subscribe(() => {\n      this.content = this.specificContent || this.getUserDefaultEmptyContent();\n      this.renderEmpty();\n    });\n  }\n  getUserDefaultEmptyContent() {\n    return (this.configService.getConfigForComponent('empty') || {}).nzDefaultEmptyContent;\n  }\n  static {\n    this.ɵfac = function NzEmbedEmptyComponent_Factory(t) {\n      return new (t || NzEmbedEmptyComponent)(i0.ɵɵdirectiveInject(i1$1.NzConfigService), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzEmbedEmptyComponent,\n      selectors: [[\"nz-embed-empty\"]],\n      inputs: {\n        nzComponentName: \"nzComponentName\",\n        specificContent: \"specificContent\"\n      },\n      exportAs: [\"nzEmbedEmpty\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 1,\n      consts: [[3, \"cdkPortalOutlet\"], [\"nzNotFoundImage\", \"simple\", 1, \"ant-empty-normal\"], [\"nzNotFoundImage\", \"simple\", 1, \"ant-empty-small\"]],\n      template: function NzEmbedEmptyComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzEmbedEmptyComponent_Conditional_0_Template, 2, 1)(1, NzEmbedEmptyComponent_Conditional_1_Template, 1, 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.content ? 0 : 1);\n        }\n      },\n      dependencies: [NzEmptyComponent, PortalModule, i2$1.CdkPortalOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzEmbedEmptyComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-embed-empty',\n      exportAs: 'nzEmbedEmpty',\n      template: `\n    @if (content) {\n      @if (contentType === 'string') {\n        {{ content }}\n      } @else {\n        <ng-template [cdkPortalOutlet]=\"contentPortal\" />\n      }\n    } @else {\n      @if (specificContent !== null) {\n        @switch (size) {\n          @case ('normal') {\n            <nz-empty class=\"ant-empty-normal\" nzNotFoundImage=\"simple\" />\n          }\n          @case ('small') {\n            <nz-empty class=\"ant-empty-small\" nzNotFoundImage=\"simple\" />\n          }\n          @default {\n            <nz-empty />\n          }\n        }\n      }\n    }\n  `,\n      imports: [NzEmptyComponent, PortalModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$1.NzConfigService\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Injector\n  }], {\n    nzComponentName: [{\n      type: Input\n    }],\n    specificContent: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzEmptyModule {\n  static {\n    this.ɵfac = function NzEmptyModule_Factory(t) {\n      return new (t || NzEmptyModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzEmptyModule,\n      imports: [NzEmptyComponent, NzEmbedEmptyComponent, NzEmptyDefaultComponent, NzEmptySimpleComponent],\n      exports: [NzEmptyComponent, NzEmbedEmptyComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzEmptyComponent, NzEmbedEmptyComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzEmptyModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzEmptyComponent, NzEmbedEmptyComponent, NzEmptyDefaultComponent, NzEmptySimpleComponent],\n      exports: [NzEmptyComponent, NzEmbedEmptyComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NZ_EMPTY_COMPONENT_NAME, NzEmbedEmptyComponent, NzEmptyComponent, NzEmptyDefaultComponent, NzEmptyModule, NzEmptySimpleComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,OAAO,CAAC;AACxB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,OAAO,iBAAoB,aAAa,EAAE,OAAO,OAAO,kBAAkB,OAAO,oBAAoB,OAAO;AAAA,EACnI;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,gBAAgB,CAAC;AAAA,EAClG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,0BAA0B,OAAO,eAAe;AAAA,EAChE;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB;AAAA,EACnC;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB;AAAA,EACpC;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,iBAAiB,EAAE,GAAG,uDAAuD,GAAG,CAAC;AAAA,EACjK;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,GAAG,OAAO,oBAAoB,WAAW,IAAI,CAAC;AAAA,EACjE;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,kBAAkB,OAAO,oBAAoB,OAAO,OAAO,aAAa,GAAG,GAAG;AAAA,EAClH;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,gBAAgB,CAAC;AAChG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,iBAAiB;AAAA,EAClE;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,kBAAkB,GAAG;AAAA,EACzD;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,gBAAgB,CAAC;AAChG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,gBAAgB;AAAA,EACjE;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,mBAAmB,KAAK,OAAO,SAAS,GAAG;AAAA,EAChD;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AAAC;AAC5F,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,eAAe,CAAC;AAAA,EACnH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,mBAAmB,OAAO,aAAa;AAAA,EACvD;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4DAA4D,GAAG,CAAC,EAAE,GAAG,4DAA4D,GAAG,CAAC;AAAA,EACxJ;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,GAAG,OAAO,gBAAgB,WAAW,IAAI,CAAC;AAAA,EAC7D;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,YAAY,CAAC;AAAA,EAC/B;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,YAAY,CAAC;AAAA,EAC/B;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,UAAU;AAAA,EAC5B;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mEAAmE,GAAG,CAAC,EAAE,GAAG,mEAAmE,GAAG,CAAC,EAAE,GAAG,mEAAmE,GAAG,CAAC;AAAA,EAClP;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,cAAc,IAAI,UAAU,OAAO,UAAU,WAAW,IAAI,YAAY,UAAU,IAAI,CAAC;AAAA,EAC5F;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4DAA4D,GAAG,CAAC;AAAA,EACnF;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,GAAG,OAAO,oBAAoB,OAAO,IAAI,EAAE;AAAA,EAC9D;AACF;AACA,IAAM,0BAA0B,IAAI,eAAe,yBAAyB;AAM5E,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAAyB;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,UAAU,CAAC,gBAAgB;AAAA,MAC3B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,SAAS,OAAO,UAAU,OAAO,WAAW,eAAe,SAAS,8BAA8B,GAAG,uBAAuB,GAAG,CAAC,QAAQ,QAAQ,aAAa,SAAS,GAAG,CAAC,aAAa,qBAAqB,GAAG,CAAC,MAAM,UAAU,MAAM,UAAU,MAAM,UAAU,MAAM,UAAU,GAAG,+BAA+B,GAAG,CAAC,KAAK,gJAAgJ,GAAG,8BAA8B,GAAG,CAAC,KAAK,4IAA4I,aAAa,oBAAoB,GAAG,8BAA8B,GAAG,CAAC,KAAK,2FAA2F,GAAG,8BAA8B,GAAG,CAAC,KAAK,wjBAAwjB,GAAG,8BAA8B,GAAG,CAAC,KAAK,0OAA0O,GAAG,8BAA8B,GAAG,CAAC,aAAa,4BAA4B,GAAG,yBAAyB,GAAG,CAAC,MAAM,UAAU,MAAM,SAAS,MAAM,SAAS,MAAM,OAAO,GAAG,CAAC,KAAK,sDAAsD,CAAC;AAAA,MACh5D,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe;AAClB,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC;AACnD,UAAG,UAAU,GAAG,WAAW,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AACpF,UAAG,aAAa;AAChB,UAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,UAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,UAAG,UAAU,IAAI,WAAW,EAAE,EAAE,IAAI,QAAQ,EAAE;AAC9C,UAAG,aAAa,EAAE,EAAE;AAAA,QACtB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAwCZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAAwB;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,SAAS,MAAM,UAAU,MAAM,WAAW,aAAa,SAAS,8BAA8B,GAAG,sBAAsB,GAAG,CAAC,aAAa,kBAAkB,QAAQ,QAAQ,aAAa,SAAS,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK,GAAG,8BAA8B,GAAG,CAAC,aAAa,WAAW,GAAG,wBAAwB,GAAG,CAAC,KAAK,+GAA+G,GAAG,CAAC,KAAK,iPAAiP,GAAG,2BAA2B,CAAC;AAAA,MACtuB,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe;AAClB,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC;AACxC,UAAG,UAAU,GAAG,WAAW,CAAC;AAC5B,UAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,UAAG,UAAU,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AACvC,UAAG,aAAa,EAAE,EAAE;AAAA,QACtB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAgBV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,uBAAuB,CAAC,WAAW,QAAQ;AACjD,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,MAAM,KAAK;AACrB,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,mBAAmB;AACrB,YAAM,UAAU,kBAAkB;AAClC,WAAK,kBAAkB,OAAO,YAAY;AAAA,IAC5C;AACA,QAAI,iBAAiB;AACnB,YAAM,QAAQ,gBAAgB,gBAAgB;AAC9C,WAAK,iBAAiB,qBAAqB,UAAU,OAAK,MAAM,KAAK,IAAI;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,KAAK,aAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACpE,WAAK,SAAS,KAAK,KAAK,cAAc,OAAO;AAC7C,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAqB,kBAAqB,aAAa,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,IACvH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,MACxB,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,QAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,QACnB,kBAAkB;AAAA,MACpB;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,OAAO,KAAK,CAAC;AAAA,MACxI,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,cAAc,EAAE,GAAG,yCAAyC,GAAG,CAAC;AAChI,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,KAAK,CAAC,EAAE,GAAG,yCAAyC,GAAG,GAAG,OAAO,CAAC;AAAA,QACpI;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,CAAC,IAAI,iBAAiB,IAAI,CAAC;AAC/C,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,GAAG,IAAI,sBAAsB,OAAO,IAAI,EAAE;AAC3D,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,mBAAmB,IAAI,EAAE;AAAA,QACnD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,iCAAiC,yBAAyB,sBAAsB;AAAA,MAClH,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA8BV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,gBAAgB,yBAAyB,sBAAsB;AAAA,MACzE,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,SAAS,aAAa,eAAe;AACnC,UAAQ,eAAe;AAAA,IACrB,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AACA,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,eAAe,kBAAkB,KAAK,UAAU;AAC1D,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AACxB,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,OAAO;AACZ,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,iBAAiB;AAC3B,WAAK,OAAO,aAAa,QAAQ,gBAAgB,YAAY;AAAA,IAC/D;AACA,QAAI,QAAQ,mBAAmB,CAAC,QAAQ,gBAAgB,cAAc,GAAG;AACvE,WAAK,UAAU,QAAQ,gBAAgB;AACvC,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,mCAAmC;AAAA,EAC1C;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,cAAc;AACZ,UAAM,UAAU,KAAK;AACrB,QAAI,OAAO,YAAY,UAAU;AAC/B,WAAK,cAAc;AAAA,IACrB,WAAW,mBAAmB,aAAa;AACzC,YAAM,UAAU;AAAA,QACd,WAAW,KAAK;AAAA,MAClB;AACA,WAAK,cAAc;AACnB,WAAK,gBAAgB,IAAI,eAAe,SAAS,KAAK,kBAAkB,OAAO;AAAA,IACjF,WAAW,mBAAmB,MAAM;AAClC,YAAM,WAAW,SAAS,OAAO;AAAA,QAC/B,QAAQ,KAAK;AAAA,QACb,WAAW,CAAC;AAAA,UACV,SAAS;AAAA,UACT,UAAU,KAAK;AAAA,QACjB,CAAC;AAAA,MACH,CAAC;AACD,WAAK,cAAc;AACnB,WAAK,gBAAgB,IAAI,gBAAgB,SAAS,KAAK,kBAAkB,QAAQ;AAAA,IACnF,OAAO;AACL,WAAK,cAAc;AACnB,WAAK,gBAAgB;AAAA,IACvB;AACA,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA,EACA,qCAAqC;AACnC,SAAK,cAAc,iCAAiC,OAAO,EAAE,KAAK,UAAU,IAAI,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC3H,WAAK,UAAU,KAAK,mBAAmB,KAAK,2BAA2B;AACvE,WAAK,YAAY;AAAA,IACnB,CAAC;AAAA,EACH;AAAA,EACA,6BAA6B;AAC3B,YAAQ,KAAK,cAAc,sBAAsB,OAAO,KAAK,CAAC,GAAG;AAAA,EACnE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAA0B,kBAAuB,eAAe,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,QAAQ,CAAC;AAAA,IAC9M;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,MAC9B,QAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,iBAAiB,GAAG,CAAC,mBAAmB,UAAU,GAAG,kBAAkB,GAAG,CAAC,mBAAmB,UAAU,GAAG,iBAAiB,CAAC;AAAA,MAC1I,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,8CAA8C,GAAG,CAAC,EAAE,GAAG,8CAA8C,GAAG,CAAC;AAAA,QAC5H;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,GAAG,IAAI,UAAU,IAAI,CAAC;AAAA,QACzC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,kBAAkB,cAAmB,eAAe;AAAA,MACnE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuBV,SAAS,CAAC,kBAAkB,YAAY;AAAA,MACxC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,kBAAkB,uBAAuB,yBAAyB,sBAAsB;AAAA,MAClG,SAAS,CAAC,kBAAkB,qBAAqB;AAAA,IACnD,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,kBAAkB,qBAAqB;AAAA,IACnD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB,uBAAuB,yBAAyB,sBAAsB;AAAA,MAClG,SAAS,CAAC,kBAAkB,qBAAqB;AAAA,IACnD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}