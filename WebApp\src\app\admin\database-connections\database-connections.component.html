<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 65px);">
  <!-- Main Container -->
  <div class="flex-1 overflow-hidden flex flex-col">
    <!-- Header - Teams-style with compact vertical spacing -->
    <div
      class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50">
      <!-- Left side with title and count -->
      <div class="flex items-center gap-2">
        <i class="ri-database-2-line text-[var(--primary-purple)] text-xl"></i>
        <h1 class="text-lg font-medium text-[var(--text-dark)]">Database Connections</h1>
        <div
          class="inline-flex items-center justify-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]">
          {{ filteredConnectionsGetter.length }}
        </div>
      </div>

      <!-- Right side with controls -->
      <div class="flex items-center gap-2">
        <!-- Search Input - Teams-style -->
        <div class="relative w-full sm:w-56 flex items-center">
          <div class="absolute inset-y-0 left-0 flex items-center justify-center pl-2 pointer-events-none">
            <i class="ri-search-line text-[var(--text-medium-gray)] text-sm"></i>
          </div>
          <input type="text" placeholder="Search connections..." [(ngModel)]="searchTerm"
            (ngModelChange)="filterConnections()"
            class="w-full h-8 px-3 py-1 pl-8 text-sm border-none text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200" />
          <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-2" *ngIf="searchTerm">
            <button (click)="searchTerm = ''; filterConnections()"
              class="text-[var(--text-medium-gray)] border-none hover:text-[var(--text-dark)] cursor-pointer bg-transparent transition-colors focus:outline-none">
              <i class="ri-close-line text-sm"></i>
            </button>
          </div>
        </div>

        <!-- Add Connection Button - Teams-style -->
        <button (click)="showCreateModal()"
          class="h-8 px-3 py-1 bg-[var(--primary-purple)] border-none text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center justify-center gap-1">
          <i class="ri-add-line"></i>
          <span>Add Connection</span>
        </button>
      </div>
    </div>

    <!-- Connections Table -->
    <div class="flex-1 overflow-y-auto px-4 pt-4">
      <!-- Loading Spinner -->
      <div *ngIf="loading" class="relative min-h-[300px]">
        <app-spinner message="Loading connections..." [overlay]="false"></app-spinner>
      </div>

      <div *ngIf="!loading"
        class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30">
        <div class="overflow-x-auto">
          <table class="w-full text-sm text-[var(--text-dark)]">
            <thead>
              <tr class="bg-[var(--hover-blue-gray)] border-b border-[var(--hover-blue-gray)]">
                <th class="px-4 py-3 text-left font-semibold">
                  <div class="flex items-center">
                    <span>Name</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left font-semibold">
                  <div class="flex items-center">
                    <span>Database Type</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left font-semibold">
                  <div class="flex items-center">
                    <span>Status</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left font-semibold">
                  <div class="flex items-center">
                    <span>Schema Status</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left font-semibold">
                  <div class="flex items-center">
                    <span>Last Extraction</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left font-semibold">
                  <div class="flex items-center">
                    <span>Created</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-center font-semibold">
                  <div class="flex items-center justify-center">
                    <span>Actions</span>
                  </div>
                </th>
              </tr>
            </thead>

            <tbody>
              <tr *ngFor="let connection of paginatedConnections; trackBy: trackByConnectionId"
                class="border-b border-[var(--hover-blue-gray)] border-opacity-30 hover:bg-[var(--hover-blue-gray)] hover:bg-opacity-30 transition-colors duration-200">
                <td class="px-4 py-3">
                  <div class="flex flex-col">
                    <span class="font-medium text-[var(--text-dark)]">{{ connection.name }}</span>
                    <span class="text-xs text-[var(--text-medium-gray)] mt-0.5" *ngIf="connection.description">{{
                      connection.description }}</span>
                  </div>
                </td>

                <td class="px-4 py-3">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 dark:text-blue-200 text-blue-800">
                    {{ connection.databaseType }}
                  </span>
                </td>

                <td class="px-4 py-3">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" [ngClass]="{
                          'bg-green-100 dark:bg-green-900 dark:text-green-200 text-green-800': connection.isActive,
                          'bg-red-100 dark:bg-red-900 dark:text-red-200 text-red-800': !connection.isActive
                        }">
                    <span class="w-1.5 h-1.5 rounded-full mr-1.5" [ngClass]="{
                            'bg-green-500': connection.isActive,
                            'bg-red-500': !connection.isActive
                          }"></span>
                    {{ connection.isActive ? 'Active' : 'Inactive' }}
                  </span>
                </td>

                <td class="px-4 py-3">
                  <span class="inline-flex items-center px-2 py-1 rounded-full dark:text-green-200 dark:bg-green-900 text-xs font-medium"
                    [ngClass]="getStatusClasses(connection)">
                    {{ getStatusText(connection) }}
                  </span>
                  <div *ngIf="connection.schemaExtractionError" class="text-xs text-red-500 mt-1 max-w-xs truncate">
                    {{ connection.schemaExtractionError }}
                  </div>
                </td>

                <td class="px-4 py-3">
                  <span *ngIf="connection.lastSchemaExtraction" class="text-[var(--text-dark)]">
                    {{ getDateString(connection.lastSchemaExtraction) | date:'short' }}
                  </span>
                  <span *ngIf="!connection.lastSchemaExtraction" class="text-[var(--text-medium-gray)]">Never</span>
                </td>

                <td class="px-4 py-3 text-[var(--text-dark)]">
                  {{ getDateString(connection.createdAt) | date:'short' }}
                </td>

                <td class="px-4 py-3">
                  <div class="flex items-center justify-center gap-1">
                    <button (click)="testConnection(connection)"
                      class="w-8 h-8 flex items-center justify-center rounded-md border-none bg-blue-50 dark:bg-blue-800 dark:text-blue-200 text-blue-600 hover:bg-blue-100 transition-colors duration-200"
                      title="Test Connection">
                      <i class="ri-wifi-line text-sm"></i>
                    </button>

                    <button (click)="extractSchema(connection)" [disabled]="!connection.isActive"
                      class="w-8 h-8 flex items-center justify-center border-none rounded-md bg-green-50 text-green-600  dark:bg-green-800 dark:text-green-200 hover:bg-green-100 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Extract Schema">
                      <i class="ri-database-2-line text-sm"></i>
                    </button>

                    <button (click)="showEditModal(connection)"
                      class="w-8 h-8 flex items-center justify-center rounded-md border-none  dark:bg-gray-800 dark:text-gray-200 bg-gray-50 text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                      title="Edit">
                      <i class="ri-edit-line text-sm"></i>
                    </button>

                    <button (click)="confirmDelete(connection)"
                      class="w-8 h-8 flex items-center justify-center rounded-md bg-red-50 border-none  dark:bg-red-800 dark:text-red-200 text-red-600 hover:bg-red-100 transition-colors duration-200"
                      title="Delete">
                      <i class="ri-delete-bin-line text-sm"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div *ngIf="filteredConnectionsGetter.length === 0" class="text-center py-12">
          <i class="ri-database-2-line text-4xl text-[var(--text-medium-gray)] mb-4"></i>
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-2">No database connections found</h3>
          <p class="text-[var(--text-medium-gray)] mb-4">
            <span *ngIf="searchTerm">No connections match your search criteria.</span>
            <span *ngIf="!searchTerm">Get started by creating your first database connection.</span>
          </p>
          <button *ngIf="!searchTerm" (click)="showCreateModal()"
            class="px-4 py-2 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-200">
            <i class="ri-add-line mr-1"></i>
            Add Your First Connection
          </button>
        </div>
      </div>

      <!-- Pagination -->
      <div
        class="pagination-container flex flex-col sm:flex-row justify-between items-center mt-4 mb-4 px-4 py-3 bg-[var(--background-white)] rounded-md shadow-sm border border-[var(--hover-blue-gray)]"
        *ngIf="!loading && filteredConnectionsGetter.length > 0">
        <div class="text-sm text-[var(--text-medium-gray)] mb-4 sm:mb-0 flex items-center">
          <ng-container *ngIf="filteredConnectionsGetter.length > 0">
            <span>Showing</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">{{ ((currentPage - 1) * pageSize) + 1 }}</span>
            <span>to</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">{{ ((currentPage - 1) * pageSize) + paginatedConnections.length }}</span>
            <span>of</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">{{ filteredConnectionsGetter.length }}</span>
            <span>connections</span>
          </ng-container>
          <ng-container *ngIf="filteredConnectionsGetter.length === 0">
            <span>No connections to display</span>
          </ng-container>
        </div>

        <div class="flex items-center">
          <div class="hidden sm:flex items-center mr-6 space-x-2">
            <span class="text-sm text-[var(--text-medium-gray)]">Rows per page:</span>
            <div class="relative w-auto">
              <button (click)="paginatiionDropdown()" class="appearance-none h-7 bg-[var(--background-white)] rounded-md text-sm px-2 pr-7 py-0 text-center border border-gray-300 focus:outline-none focus:ring-1 cursor-pointer">
                {{ pageSize || 'Select Page Size' }}
              </button>
              <div *ngIf="isDropdownAbove || isPageLenthOpen" class="absolute mt-1 w-full bg-[var(--secondary-purple)] !border-2 !border-gray-300 rounded-md shadow-lg z-10" [ngClass]="{'bottom-full mb-1 border': isDropdownAbove, 'top-full border mt-1': !isDropdownAbove}">
                <div (click)="selectPageSize(5)" class="px-2 py-1 text-sm cursor-pointer text-center border hover:bg-[var(--primary-purple)] rounded-md hover:text-white">5</div>
                <div (click)="selectPageSize(10)" class="px-2 py-1 text-sm cursor-pointer text-center rounded-md hover:bg-[var(--primary-purple)] hover:text-white">10</div>
                <div (click)="selectPageSize(20)" class="px-2 py-1 text-sm cursor-pointer text-center rounded-md hover:bg-[var(--primary-purple)] hover:text-white">20</div>
                <div (click)="selectPageSize(50)" class="px-2 py-1 text-sm cursor-pointer text-center rounded-md hover:bg-[var(--primary-purple)] hover:text-white">50</div>
              </div>
              <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-1 pointer-events-none">
                <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)] text-sm"></i>
              </div>
            </div>
          </div>

          <div class="flex items-center space-x-1" *ngIf="totalPages > 0">
            <button (click)="goToPage(1)" [disabled]="currentPage === 1 || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="First page">
              <i class="ri-skip-back-mini-line text-sm"></i>
            </button>

            <button (click)="previousPage()" [disabled]="currentPage === 1 || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="Previous page">
              <i class="ri-arrow-left-s-line text-sm"></i>
            </button>

            <div class="flex items-center space-x-1">
              <button *ngIf="currentPage > 2 && totalPages > 3" (click)="goToPage(1)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                1
              </button>

              <span *ngIf="currentPage > 3 && totalPages > 4"
                class="w-7 h-7 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>

              <button *ngIf="currentPage > 1 && totalPages > 1" (click)="goToPage(currentPage - 1)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                {{ currentPage - 1 }}
              </button>

              <button
                class="w-7 h-7 flex items-center justify-center rounded-md bg-[var(--primary-purple)] text-white font-medium border-none">
                {{ currentPage }}
              </button>

              <button *ngIf="currentPage < totalPages && totalPages > 1" (click)="goToPage(currentPage + 1)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                {{ currentPage + 1 }}
              </button>

              <span *ngIf="currentPage < totalPages - 2 && totalPages > 4"
                class="w-7 h-7 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>

              <button *ngIf="currentPage < totalPages - 1 && totalPages > 3" (click)="goToPage(totalPages)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                {{ totalPages }}
              </button>
            </div>

            <button (click)="nextPage()" [disabled]="currentPage === totalPages || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="Next page">
              <i class="ri-arrow-right-s-line text-sm"></i>
            </button>

            <button (click)="goToPage(totalPages)" [disabled]="currentPage === totalPages || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="Last page">
              <i class="ri-skip-forward-mini-line text-sm"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create/Edit Modal -->
<div *ngIf="isModalVisible"
  class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
  <div class="bg-[var(--background-white)] rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
    <!-- Modal Header -->
    <div class="flex items-center justify-between p-6 border-b border-[var(--hover-blue-gray)]">
      <div class="flex items-center gap-3">
        <i class="ri-database-2-line text-[var(--primary-purple)] text-xl"></i>
        <h2 class="text-lg font-semibold text-[var(--text-dark)]">
          {{ isEditMode ? 'Edit Database Connection' : 'Create Database Connection' }}
        </h2>
      </div>
      <button (click)="handleCancel()"
        class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-[var(--hover-blue-gray)] transition-all duration-200 border-none bg-transparent">
        <i class="ri-close-line text-[var(--text-medium-gray)] text-xl"></i>
      </button>
    </div>

    <!-- Modal Body -->
    <div class="px-6">
      <form [formGroup]="connectionForm" class="space-y-4">

        <!-- Connection Name -->
        <div>
          <label for="name" class="text-sm font-medium text-[var(--text-dark)] mb-1 block">
            Connection Name <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <i class="ri-database-line text-[var(--text-medium-gray)]"></i>
            </div>
            <input id="name" formControlName="name"
              class="w-full h-10 pl-10 pr-4 py-2 text-sm  p-3 rounded-md border border-[var(--hover-blue-gray)] focus:border-none focus:outline-[var(--primary-purple)] bg-[var(--background-light-gray)] outline-none transition-[var(--transition-default)]"
              placeholder="Enter connection name" maxlength="100" />
          </div>
        </div>

        <!-- Database Type -->
        <div>
          <label for="databaseType" class="text-sm font-medium text-[var(--text-dark)] mb-1 block">
            Database Type <span class="text-red-500">*</span>
          </label>
          <div class="relative w-auto">
            <button (click)="toggleDropdown()" type="button" class="h-10 pl-10 pr-4 py-2 text-sm w-full p-3  rounded-md border border-[var(--hover-blue-gray)] bg-[var(--background-light-gray)] text-[var(--text-dark)] text-left
                       focus:outline-none focus:ring-1 focus:ring-[var(--primary-purple)] hover:bg-[var(--primary-purple)]  transition-[var(--transition-default)]">
              {{ selectedLabel || 'Select database type' }}
            </button>
            <div *ngIf="isOpen" class="absolute mt-1 w-full bg-[var(--background-light-gray)] text-[var(--text-dark)] hover:bg-[var(--primary-purple)]  transition-[var(--transition-default)] border border-gray-300 rounded-md shadow-lg z-10">
              <div *ngFor="let type of databaseTypes" (click)="selectOption(type.value, $event)" class="px-3 py-1 text-sm cursor-pointer border  hover:bg-[var(--primary-purple)] hover:text-white">
                {{ type.label }}
              </div>
            </div>
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none z-10">
              <i class="ri-server-line text-[var(--text-medium-gray)]"></i>
            </div>
            <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-2 pointer-events-none">
              <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)] text-sm"></i>
            </div>
          </div>
        </div>

        <!-- Connection String -->
        <div>
          <label for="connectionString" class="text-sm font-medium text-[var(--text-dark)] mb-1 block">
            Connection String <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <textarea id="connectionString" formControlName="connectionString" rows="3"
              class="w-full px-3 py-2 text-sm rounded-md border border-[var(--hover-blue-gray)] focus:border-none focus:outline-[var(--primary-purple)] bg-[var(--background-light-gray)] outline-none transition-[var(--transition-default)"
              placeholder="Server=localhost;Database=MyDB;User ID=user;Password=****;"></textarea>
          </div>
          <p class="text-xs text-[var(--text-medium-gray)] mt-1">
            Example: {{ getConnectionStringExample() }}
          </p>
        </div>

        <!-- Test Connection Section -->
        <div class="bg-[var(--hover-blue-gray)] bg-opacity-30 rounded-lg p-4 border border-[var(--hover-blue-gray)]">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-2">
              <i class="ri-wifi-line text-[var(--primary-purple)]"></i>
              <span class="text-sm font-medium text-[var(--text-dark)]">Test Connection</span>
            </div>
            <button type="button" (click)="testConnectionFromForm()"
              [disabled]="!canTestConnection() || testingConnection"
              class="px-3 py-1.5 bg-[var(--primary-purple)] text-white text-xs font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1">
              <i class="ri-wifi-line" *ngIf="!testingConnection"></i>
              <i class="ri-loader-4-line animate-spin" *ngIf="testingConnection"></i>
              {{ testingConnection ? 'Testing...' : 'Test Connection' }}
            </button>
          </div>

          <!-- Test Result -->
          <div *ngIf="testResult" class="flex items-center gap-2 text-sm">
            <i class="ri-checkbox-circle-line text-green-600" *ngIf="testResult.success"></i>
            <i class="ri-close-circle-line text-red-600" *ngIf="!testResult.success"></i>
            <span [ngClass]="{
              'text-green-600': testResult.success,
              'text-red-600': !testResult.success
            }">
              {{ testResult.message }}
            </span>
          </div>

          <p class="text-xs text-[var(--text-medium-gray)] mt-2">
            Test the connection before saving to ensure it works correctly.
          </p>
        </div>

        <!-- Description -->
        <div>
          <label for="description" class="text-sm font-medium text-[var(--text-dark)] mb-1 block">
            Description
          </label>
          <div class="relative">
            <textarea id="description" formControlName="description" rows="2" maxlength="500"
              class="w-full px-3 py-2 text-sm rounded-md border border-[var(--hover-blue-gray)] focus:border-none focus:outline-[var(--primary-purple)] bg-[var(--background-light-gray)] outline-none transition-[var(--transition-default)"
              placeholder="Optional description for this connection"></textarea>
          </div>
        </div>

        <!-- Active Status -->
        <div class="flex items-center gap-3">
          <label class="flex items-center gap-2 cursor-pointer">
            <input type="checkbox" formControlName="isActive"
              class="w-4 h-4 text-[var(--primary-purple)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded focus:ring-[var(--primary-purple)] focus:ring-2 " />
            <span class="text-sm font-medium text-[var(--text-dark)]">Active Connection</span>
          </label>
          <span class="text-xs text-[var(--text-medium-gray)]">
            Only active connections can be used for schema extraction and agents
          </span>
        </div>

      </form>
    </div>

    <!-- Modal Footer -->
    <div
      class="flex items-center justify-between p-6 border-t border-[var(--hover-blue-gray)] bg-[var(--hover-blue-gray)] bg-opacity-20">
      <div class="text-xs text-[var(--text-medium-gray)]">
        <span *ngIf="!isEditMode && testResult?.success" class="text-green-600">
          <i class="ri-checkbox-circle-line mr-1"></i>
          Connection tested successfully
        </span>
      </div>

      <div class="flex items-center gap-3">
        <button type="button" (click)="handleCancel()"
          class="px-4 py-2 text-sm font-medium text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md hover:bg-[var(--hover-blue-gray)] hover:bg-opacity-30 transition-all duration-200">
          Cancel
        </button>
        <button type="button" (click)="handleOk()"
          [disabled]="!connectionForm.valid || (!isEditMode && !testResult?.success)"
          class="px-4 py-2 text-sm font-medium text-white bg-[var(--primary-purple)] rounded-md hover:bg-opacity-90 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2">
          <i class="ri-save-line"></i>
          {{ isEditMode ? 'Update Connection' : 'Create Connection' }}
        </button>
      </div>
    </div>
  </div>
</div>
