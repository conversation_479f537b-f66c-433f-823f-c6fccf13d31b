{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-slider.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport { RIGHT_ARROW, UP_ARROW, LEFT_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport { NgStyle, NgForOf, NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Component, ChangeDetectionStrategy, ViewEncapsulation, ViewChild, Input, EventEmitter, forwardRef, Optional, ViewChildren, Output, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject, fromEvent, merge } from 'rxjs';\nimport { takeUntil, filter, tap, map, distinctUntilChanged } from 'rxjs/operators';\nimport { InputBoolean, InputNumber, ensureNumberInRange, silentEvent, getPrecision, getPercent, getElementOffset, isNil, arraysEqual } from 'ng-zorro-antd/core/util';\nimport * as i2 from 'ng-zorro-antd/tooltip';\nimport { NzTooltipDirective, NzToolTipModule } from 'ng-zorro-antd/tooltip';\nimport * as i2$1 from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"handle\"];\nconst _c1 = a0 => ({\n  $implicit: a0\n});\nfunction NzSliderMarksComponent_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 1);\n  }\n  if (rf & 2) {\n    const attr_r1 = ctx.$implicit;\n    i0.ɵɵclassProp(\"ant-slider-mark-active\", attr_r1.active);\n    i0.ɵɵproperty(\"ngStyle\", attr_r1.style)(\"innerHTML\", attr_r1.label, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NzSliderStepComponent_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 1);\n  }\n  if (rf & 2) {\n    const mark_r1 = ctx.$implicit;\n    i0.ɵɵclassProp(\"ant-slider-dot-active\", mark_r1.active);\n    i0.ɵɵproperty(\"ngStyle\", mark_r1.style);\n  }\n}\nfunction NzSliderComponent_nz_slider_step_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-slider-step\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"vertical\", ctx_r0.nzVertical)(\"min\", ctx_r0.nzMin)(\"max\", ctx_r0.nzMax)(\"lowerBound\", ctx_r0.bounds.lower)(\"upperBound\", ctx_r0.bounds.upper)(\"marksArray\", ctx_r0.marksArray)(\"included\", ctx_r0.nzIncluded)(\"reverse\", ctx_r0.nzReverse);\n  }\n}\nfunction NzSliderComponent_nz_slider_handle_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-slider-handle\", 5);\n    i0.ɵɵlistener(\"focusin\", function NzSliderComponent_nz_slider_handle_3_Template_nz_slider_handle_focusin_0_listener() {\n      const handleIndex_r3 = i0.ɵɵrestoreView(_r2).index;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onHandleFocusIn(handleIndex_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const handle_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"vertical\", ctx_r0.nzVertical)(\"reverse\", ctx_r0.nzReverse)(\"offset\", handle_r4.offset)(\"value\", handle_r4.value)(\"active\", handle_r4.active)(\"tooltipFormatter\", ctx_r0.nzTipFormatter)(\"tooltipVisible\", ctx_r0.nzTooltipVisible)(\"tooltipPlacement\", ctx_r0.nzTooltipPlacement)(\"dir\", ctx_r0.dir);\n  }\n}\nfunction NzSliderComponent_nz_slider_marks_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-slider-marks\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"vertical\", ctx_r0.nzVertical)(\"min\", ctx_r0.nzMin)(\"max\", ctx_r0.nzMax)(\"lowerBound\", ctx_r0.bounds.lower)(\"upperBound\", ctx_r0.bounds.upper)(\"marksArray\", ctx_r0.marksArray)(\"included\", ctx_r0.nzIncluded)(\"reverse\", ctx_r0.nzReverse);\n  }\n}\nclass NzSliderService {\n  constructor() {\n    this.isDragging = false;\n  }\n  static {\n    this.ɵfac = function NzSliderService_Factory(t) {\n      return new (t || NzSliderService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzSliderService,\n      factory: NzSliderService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSliderService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass NzSliderHandleComponent {\n  constructor(sliderService, cdr) {\n    this.sliderService = sliderService;\n    this.cdr = cdr;\n    this.tooltipVisible = 'default';\n    this.active = false;\n    this.dir = 'ltr';\n    this.style = {};\n    this.enterHandle = () => {\n      if (!this.sliderService.isDragging) {\n        this.toggleTooltip(true);\n        this.updateTooltipPosition();\n        this.cdr.detectChanges();\n      }\n    };\n    this.leaveHandle = () => {\n      if (!this.sliderService.isDragging) {\n        this.toggleTooltip(false);\n        this.cdr.detectChanges();\n      }\n    };\n  }\n  ngOnChanges(changes) {\n    const {\n      offset,\n      value,\n      active,\n      tooltipVisible,\n      reverse,\n      dir\n    } = changes;\n    if (offset || reverse || dir) {\n      this.updateStyle();\n    }\n    if (value) {\n      this.updateTooltipTitle();\n      this.updateTooltipPosition();\n    }\n    if (active) {\n      if (active.currentValue) {\n        this.toggleTooltip(true);\n      } else {\n        this.toggleTooltip(false);\n      }\n    }\n    if (tooltipVisible?.currentValue === 'always') {\n      Promise.resolve().then(() => this.toggleTooltip(true, true));\n    }\n  }\n  focus() {\n    this.handleEl?.nativeElement.focus();\n  }\n  toggleTooltip(show, force = false) {\n    if (!force && (this.tooltipVisible !== 'default' || !this.tooltip)) {\n      return;\n    }\n    if (show) {\n      this.tooltip?.show();\n    } else {\n      this.tooltip?.hide();\n    }\n  }\n  updateTooltipTitle() {\n    if (this.tooltipFormatter) {\n      this.tooltipTitle = typeof this.tooltipFormatter === 'function' ? this.tooltipFormatter(this.value) : this.tooltipFormatter;\n    } else {\n      this.tooltipTitle = `${this.value}`;\n    }\n  }\n  updateTooltipPosition() {\n    if (this.tooltip) {\n      Promise.resolve().then(() => this.tooltip?.updatePosition());\n    }\n  }\n  updateStyle() {\n    const vertical = this.vertical;\n    const reverse = this.reverse;\n    const offset = this.offset;\n    const positionStyle = vertical ? {\n      [reverse ? 'top' : 'bottom']: `${offset}%`,\n      [reverse ? 'bottom' : 'top']: 'auto',\n      transform: reverse ? null : `translateY(+50%)`\n    } : {\n      ...this.getHorizontalStylePosition(),\n      transform: `translateX(${reverse ? this.dir === 'rtl' ? '-' : '+' : this.dir === 'rtl' ? '+' : '-'}50%)`\n    };\n    this.style = positionStyle;\n    this.cdr.markForCheck();\n  }\n  getHorizontalStylePosition() {\n    let left = this.reverse ? 'auto' : `${this.offset}%`;\n    let right = this.reverse ? `${this.offset}%` : 'auto';\n    if (this.dir === 'rtl') {\n      const tmp = left;\n      left = right;\n      right = tmp;\n    }\n    return {\n      left,\n      right\n    };\n  }\n  static {\n    this.ɵfac = function NzSliderHandleComponent_Factory(t) {\n      return new (t || NzSliderHandleComponent)(i0.ɵɵdirectiveInject(NzSliderService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSliderHandleComponent,\n      selectors: [[\"nz-slider-handle\"]],\n      viewQuery: function NzSliderHandleComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(NzTooltipDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.handleEl = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tooltip = _t.first);\n        }\n      },\n      hostBindings: function NzSliderHandleComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mouseenter\", function NzSliderHandleComponent_mouseenter_HostBindingHandler() {\n            return ctx.enterHandle();\n          })(\"mouseleave\", function NzSliderHandleComponent_mouseleave_HostBindingHandler() {\n            return ctx.leaveHandle();\n          });\n        }\n      },\n      inputs: {\n        vertical: \"vertical\",\n        reverse: \"reverse\",\n        offset: \"offset\",\n        value: \"value\",\n        tooltipVisible: \"tooltipVisible\",\n        tooltipPlacement: \"tooltipPlacement\",\n        tooltipFormatter: \"tooltipFormatter\",\n        active: \"active\",\n        dir: \"dir\"\n      },\n      exportAs: [\"nzSliderHandle\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 7,\n      consts: [[\"handle\", \"\"], [\"tabindex\", \"0\", \"nz-tooltip\", \"\", 1, \"ant-slider-handle\", 3, \"ngStyle\", \"nzTooltipTitle\", \"nzTooltipTitleContext\", \"nzTooltipTrigger\", \"nzTooltipPlacement\"]],\n      template: function NzSliderHandleComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 1, 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"nzTooltipTitle\", ctx.tooltipFormatter === null || ctx.tooltipVisible === \"never\" ? null : ctx.tooltipTitle)(\"nzTooltipTitleContext\", i0.ɵɵpureFunction1(5, _c1, ctx.value))(\"nzTooltipTrigger\", null)(\"nzTooltipPlacement\", ctx.tooltipPlacement);\n        }\n      },\n      dependencies: [NzToolTipModule, i2.NzTooltipDirective, NgStyle],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzSliderHandleComponent.prototype, \"active\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSliderHandleComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-slider-handle',\n      exportAs: 'nzSliderHandle',\n      preserveWhitespaces: false,\n      template: `\n    <div\n      #handle\n      class=\"ant-slider-handle\"\n      tabindex=\"0\"\n      nz-tooltip\n      [ngStyle]=\"style\"\n      [nzTooltipTitle]=\"tooltipFormatter === null || tooltipVisible === 'never' ? null : tooltipTitle\"\n      [nzTooltipTitleContext]=\"{ $implicit: value }\"\n      [nzTooltipTrigger]=\"null\"\n      [nzTooltipPlacement]=\"tooltipPlacement\"\n    ></div>\n  `,\n      host: {\n        '(mouseenter)': 'enterHandle()',\n        '(mouseleave)': 'leaveHandle()'\n      },\n      imports: [NzToolTipModule, NgStyle],\n      standalone: true\n    }]\n  }], () => [{\n    type: NzSliderService\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    handleEl: [{\n      type: ViewChild,\n      args: ['handle', {\n        static: false\n      }]\n    }],\n    tooltip: [{\n      type: ViewChild,\n      args: [NzTooltipDirective, {\n        static: false\n      }]\n    }],\n    vertical: [{\n      type: Input\n    }],\n    reverse: [{\n      type: Input\n    }],\n    offset: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    tooltipVisible: [{\n      type: Input\n    }],\n    tooltipPlacement: [{\n      type: Input\n    }],\n    tooltipFormatter: [{\n      type: Input\n    }],\n    active: [{\n      type: Input\n    }],\n    dir: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSliderMarksComponent {\n  constructor() {\n    this.lowerBound = null;\n    this.upperBound = null;\n    this.marksArray = [];\n    this.vertical = false;\n    this.included = false;\n    this.marks = [];\n  }\n  ngOnChanges(changes) {\n    const {\n      marksArray,\n      lowerBound,\n      upperBound,\n      reverse\n    } = changes;\n    if (marksArray || reverse) {\n      this.buildMarks();\n    }\n    if (marksArray || lowerBound || upperBound || reverse) {\n      this.togglePointActive();\n    }\n  }\n  trackById(_index, mark) {\n    return mark.value;\n  }\n  buildMarks() {\n    const range = this.max - this.min;\n    this.marks = this.marksArray.map(mark => {\n      const {\n        value,\n        offset,\n        config\n      } = mark;\n      const style = this.getMarkStyles(value, range, config);\n      const label = isConfigObject(config) ? config.label : config;\n      return {\n        label,\n        offset,\n        style,\n        value,\n        config,\n        active: false\n      };\n    });\n  }\n  getMarkStyles(value, range, config) {\n    let style;\n    const markValue = this.reverse ? this.max + this.min - value : value;\n    if (this.vertical) {\n      style = {\n        marginBottom: '-50%',\n        bottom: `${(markValue - this.min) / range * 100}%`\n      };\n    } else {\n      style = {\n        transform: `translate3d(-50%, 0, 0)`,\n        left: `${(markValue - this.min) / range * 100}%`\n      };\n    }\n    if (isConfigObject(config) && config.style) {\n      style = {\n        ...style,\n        ...config.style\n      };\n    }\n    return style;\n  }\n  togglePointActive() {\n    if (this.marks && this.lowerBound !== null && this.upperBound !== null) {\n      this.marks.forEach(mark => {\n        const value = mark.value;\n        const isActive = !this.included && value === this.upperBound || this.included && value <= this.upperBound && value >= this.lowerBound;\n        mark.active = isActive;\n      });\n    }\n  }\n  static {\n    this.ɵfac = function NzSliderMarksComponent_Factory(t) {\n      return new (t || NzSliderMarksComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSliderMarksComponent,\n      selectors: [[\"nz-slider-marks\"]],\n      hostAttrs: [1, \"ant-slider-mark\"],\n      inputs: {\n        lowerBound: \"lowerBound\",\n        upperBound: \"upperBound\",\n        marksArray: \"marksArray\",\n        min: \"min\",\n        max: \"max\",\n        vertical: \"vertical\",\n        included: \"included\",\n        reverse: \"reverse\"\n      },\n      exportAs: [\"nzSliderMarks\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 2,\n      consts: [[\"class\", \"ant-slider-mark-text\", 3, \"ant-slider-mark-active\", \"ngStyle\", \"innerHTML\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"ant-slider-mark-text\", 3, \"ngStyle\", \"innerHTML\"]],\n      template: function NzSliderMarksComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzSliderMarksComponent_span_0_Template, 1, 4, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngForOf\", ctx.marks)(\"ngForTrackBy\", ctx.trackById);\n        }\n      },\n      dependencies: [NgStyle, NgForOf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzSliderMarksComponent.prototype, \"vertical\", void 0);\n__decorate([InputBoolean()], NzSliderMarksComponent.prototype, \"included\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSliderMarksComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      preserveWhitespaces: false,\n      selector: 'nz-slider-marks',\n      exportAs: 'nzSliderMarks',\n      template: `\n    <span\n      class=\"ant-slider-mark-text\"\n      *ngFor=\"let attr of marks; trackBy: trackById\"\n      [class.ant-slider-mark-active]=\"attr.active\"\n      [ngStyle]=\"attr.style!\"\n      [innerHTML]=\"attr.label\"\n    ></span>\n  `,\n      imports: [NgStyle, NgForOf],\n      standalone: true,\n      host: {\n        class: 'ant-slider-mark'\n      }\n    }]\n  }], null, {\n    lowerBound: [{\n      type: Input\n    }],\n    upperBound: [{\n      type: Input\n    }],\n    marksArray: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    vertical: [{\n      type: Input\n    }],\n    included: [{\n      type: Input\n    }],\n    reverse: [{\n      type: Input\n    }]\n  });\n})();\nfunction isConfigObject(config) {\n  return typeof config !== 'string';\n}\nclass NzSliderStepComponent {\n  constructor() {\n    this.lowerBound = null;\n    this.upperBound = null;\n    this.marksArray = [];\n    this.vertical = false;\n    this.included = false;\n    this.steps = [];\n  }\n  ngOnChanges(changes) {\n    const {\n      marksArray,\n      lowerBound,\n      upperBound,\n      reverse\n    } = changes;\n    if (marksArray || reverse) {\n      this.buildSteps();\n    }\n    if (marksArray || lowerBound || upperBound || reverse) {\n      this.togglePointActive();\n    }\n  }\n  trackById(_index, step) {\n    return step.value;\n  }\n  buildSteps() {\n    const orient = this.vertical ? 'bottom' : 'left';\n    this.steps = this.marksArray.map(mark => {\n      const {\n        value,\n        config\n      } = mark;\n      let offset = mark.offset;\n      const range = this.max - this.min;\n      if (this.reverse) {\n        offset = (this.max - value) / range * 100;\n      }\n      return {\n        value,\n        offset,\n        config,\n        active: false,\n        style: {\n          [orient]: `${offset}%`,\n          transform: this.vertical ? 'translateY(50%)' : 'translateX(-50%)'\n        }\n      };\n    });\n  }\n  togglePointActive() {\n    if (this.steps && this.lowerBound !== null && this.upperBound !== null) {\n      this.steps.forEach(step => {\n        const value = step.value;\n        const isActive = !this.included && value === this.upperBound || this.included && value <= this.upperBound && value >= this.lowerBound;\n        step.active = isActive;\n      });\n    }\n  }\n  static {\n    this.ɵfac = function NzSliderStepComponent_Factory(t) {\n      return new (t || NzSliderStepComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSliderStepComponent,\n      selectors: [[\"nz-slider-step\"]],\n      hostAttrs: [1, \"ant-slider-step\"],\n      inputs: {\n        lowerBound: \"lowerBound\",\n        upperBound: \"upperBound\",\n        marksArray: \"marksArray\",\n        min: \"min\",\n        max: \"max\",\n        vertical: \"vertical\",\n        included: \"included\",\n        reverse: \"reverse\"\n      },\n      exportAs: [\"nzSliderStep\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 2,\n      consts: [[\"class\", \"ant-slider-dot\", 3, \"ant-slider-dot-active\", \"ngStyle\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"ant-slider-dot\", 3, \"ngStyle\"]],\n      template: function NzSliderStepComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzSliderStepComponent_span_0_Template, 1, 3, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngForOf\", ctx.steps)(\"ngForTrackBy\", ctx.trackById);\n        }\n      },\n      dependencies: [NgStyle, NgForOf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzSliderStepComponent.prototype, \"vertical\", void 0);\n__decorate([InputBoolean()], NzSliderStepComponent.prototype, \"included\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSliderStepComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-slider-step',\n      exportAs: 'nzSliderStep',\n      preserveWhitespaces: false,\n      template: `\n    <span\n      class=\"ant-slider-dot\"\n      *ngFor=\"let mark of steps; trackBy: trackById\"\n      [class.ant-slider-dot-active]=\"mark.active\"\n      [ngStyle]=\"mark.style!\"\n    ></span>\n  `,\n      imports: [NgStyle, NgForOf],\n      standalone: true,\n      host: {\n        class: 'ant-slider-step'\n      }\n    }]\n  }], null, {\n    lowerBound: [{\n      type: Input\n    }],\n    upperBound: [{\n      type: Input\n    }],\n    marksArray: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    vertical: [{\n      type: Input\n    }],\n    included: [{\n      type: Input\n    }],\n    reverse: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSliderTrackComponent {\n  constructor() {\n    this.offset = 0;\n    this.reverse = false;\n    this.dir = 'ltr';\n    this.length = 0;\n    this.vertical = false;\n    this.included = false;\n    this.style = {};\n  }\n  ngOnChanges() {\n    const vertical = this.vertical;\n    const reverse = this.reverse;\n    const visibility = this.included ? 'visible' : 'hidden';\n    const offset = this.offset;\n    const length = this.length;\n    const positonStyle = vertical ? {\n      [reverse ? 'top' : 'bottom']: `${offset}%`,\n      [reverse ? 'bottom' : 'top']: 'auto',\n      height: `${length}%`,\n      visibility\n    } : {\n      ...this.getHorizontalStylePosition(),\n      width: `${length}%`,\n      visibility\n    };\n    this.style = positonStyle;\n  }\n  getHorizontalStylePosition() {\n    let left = this.reverse ? 'auto' : `${this.offset}%`;\n    let right = this.reverse ? `${this.offset}%` : 'auto';\n    if (this.dir === 'rtl') {\n      const tmp = left;\n      left = right;\n      right = tmp;\n    }\n    return {\n      left,\n      right\n    };\n  }\n  static {\n    this.ɵfac = function NzSliderTrackComponent_Factory(t) {\n      return new (t || NzSliderTrackComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSliderTrackComponent,\n      selectors: [[\"nz-slider-track\"]],\n      inputs: {\n        offset: \"offset\",\n        reverse: \"reverse\",\n        dir: \"dir\",\n        length: \"length\",\n        vertical: \"vertical\",\n        included: \"included\"\n      },\n      exportAs: [\"nzSliderTrack\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[1, \"ant-slider-track\", 3, \"ngStyle\"]],\n      template: function NzSliderTrackComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngStyle\", ctx.style);\n        }\n      },\n      dependencies: [NgStyle],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputNumber()], NzSliderTrackComponent.prototype, \"offset\", void 0);\n__decorate([InputBoolean()], NzSliderTrackComponent.prototype, \"reverse\", void 0);\n__decorate([InputNumber()], NzSliderTrackComponent.prototype, \"length\", void 0);\n__decorate([InputBoolean()], NzSliderTrackComponent.prototype, \"vertical\", void 0);\n__decorate([InputBoolean()], NzSliderTrackComponent.prototype, \"included\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSliderTrackComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-slider-track',\n      exportAs: 'nzSliderTrack',\n      preserveWhitespaces: false,\n      template: ` <div class=\"ant-slider-track\" [ngStyle]=\"style\"></div> `,\n      imports: [NgStyle],\n      standalone: true\n    }]\n  }], null, {\n    offset: [{\n      type: Input\n    }],\n    reverse: [{\n      type: Input\n    }],\n    dir: [{\n      type: Input\n    }],\n    length: [{\n      type: Input\n    }],\n    vertical: [{\n      type: Input\n    }],\n    included: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSliderComponent {\n  constructor(slider, sliderService, cdr, platform, directionality) {\n    this.slider = slider;\n    this.sliderService = sliderService;\n    this.cdr = cdr;\n    this.platform = platform;\n    this.directionality = directionality;\n    this.nzDisabled = false;\n    this.nzDots = false;\n    this.nzIncluded = true;\n    this.nzRange = false;\n    this.nzVertical = false;\n    this.nzReverse = false;\n    this.nzMarks = null;\n    this.nzMax = 100;\n    this.nzMin = 0;\n    this.nzStep = 1;\n    this.nzTooltipVisible = 'default';\n    this.nzTooltipPlacement = 'top';\n    this.nzOnAfterChange = new EventEmitter();\n    this.value = null;\n    this.cacheSliderStart = null;\n    this.cacheSliderLength = null;\n    this.activeValueIndex = undefined; // Current activated handle's index ONLY for range=true\n    this.track = {\n      offset: null,\n      length: null\n    }; // Track's offset and length\n    this.handles = []; // Handles' offset\n    this.marksArray = null; // \"steps\" in array type with more data & FILTER out the invalid mark\n    this.bounds = {\n      lower: null,\n      upper: null\n    }; // now for nz-slider-step\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.isNzDisableFirstChange = true;\n  }\n  ngOnInit() {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n      this.updateTrackAndHandles();\n      this.onValueChange(this.getValue(true));\n    });\n    this.handles = generateHandlers(this.nzRange ? 2 : 1);\n    this.marksArray = this.nzMarks ? this.generateMarkItems(this.nzMarks) : null;\n    this.bindDraggingHandlers();\n    this.toggleDragDisabled(this.nzDisabled);\n    if (this.getValue() === null) {\n      this.setValue(this.formatValue(null));\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      nzDisabled,\n      nzMarks,\n      nzRange\n    } = changes;\n    if (nzDisabled && !nzDisabled.firstChange) {\n      this.toggleDragDisabled(nzDisabled.currentValue);\n    } else if (nzMarks && !nzMarks.firstChange) {\n      this.marksArray = this.nzMarks ? this.generateMarkItems(this.nzMarks) : null;\n    } else if (nzRange && !nzRange.firstChange) {\n      this.handles = generateHandlers(nzRange.currentValue ? 2 : 1);\n      this.setValue(this.formatValue(null));\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribeDrag();\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  writeValue(val) {\n    this.setValue(val, true);\n  }\n  onValueChange(_value) {}\n  onTouched() {}\n  registerOnChange(fn) {\n    this.onValueChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || isDisabled;\n    this.isNzDisableFirstChange = false;\n    this.toggleDragDisabled(this.nzDisabled);\n    this.cdr.markForCheck();\n  }\n  /**\n   * Event handler is only triggered when a slider handler is focused.\n   */\n  onKeyDown(e) {\n    if (this.nzDisabled) {\n      return;\n    }\n    const code = e.keyCode;\n    const isIncrease = code === RIGHT_ARROW || code === UP_ARROW;\n    const isDecrease = code === LEFT_ARROW || code === DOWN_ARROW;\n    if (!(isIncrease || isDecrease)) {\n      return;\n    }\n    e.preventDefault();\n    let step = (isDecrease ? -this.nzStep : this.nzStep) * (this.nzReverse ? -1 : 1);\n    step = this.dir === 'rtl' ? step * -1 : step;\n    const newVal = this.nzRange ? this.value[this.activeValueIndex] + step : this.value + step;\n    this.setActiveValue(ensureNumberInRange(newVal, this.nzMin, this.nzMax));\n    this.nzOnAfterChange.emit(this.getValue(true));\n  }\n  onHandleFocusIn(index) {\n    this.activeValueIndex = index;\n  }\n  setValue(value, isWriteValue = false) {\n    if (isWriteValue) {\n      this.value = this.formatValue(value);\n      this.updateTrackAndHandles();\n    } else if (!valuesEqual(this.value, value)) {\n      this.value = value;\n      this.updateTrackAndHandles();\n      this.onValueChange(this.getValue(true));\n    }\n  }\n  getValue(cloneAndSort = false) {\n    if (cloneAndSort && this.value && isValueRange(this.value)) {\n      return [...this.value].sort((a, b) => a - b);\n    }\n    return this.value;\n  }\n  /**\n   * Clone & sort current value and convert them to offsets, then return the new one.\n   */\n  getValueToOffset(value) {\n    let normalizedValue = value;\n    if (typeof normalizedValue === 'undefined') {\n      normalizedValue = this.getValue(true);\n    }\n    return isValueRange(normalizedValue) ? normalizedValue.map(val => this.valueToOffset(val)) : this.valueToOffset(normalizedValue);\n  }\n  /**\n   * Find the closest value to be activated.\n   */\n  setActiveValueIndex(pointerValue) {\n    const value = this.getValue();\n    if (isValueRange(value)) {\n      let minimal = null;\n      let gap;\n      let activeIndex = -1;\n      value.forEach((val, index) => {\n        gap = Math.abs(pointerValue - val);\n        if (minimal === null || gap < minimal) {\n          minimal = gap;\n          activeIndex = index;\n        }\n      });\n      this.activeValueIndex = activeIndex;\n      this.handlerComponents.toArray()[activeIndex].focus();\n    } else {\n      this.handlerComponents.toArray()[0].focus();\n    }\n  }\n  setActiveValue(pointerValue) {\n    if (isValueRange(this.value)) {\n      const newValue = [...this.value];\n      newValue[this.activeValueIndex] = pointerValue;\n      this.setValue(newValue);\n    } else {\n      this.setValue(pointerValue);\n    }\n  }\n  /**\n   * Update track and handles' position and length.\n   */\n  updateTrackAndHandles() {\n    const value = this.getValue();\n    const offset = this.getValueToOffset(value);\n    const valueSorted = this.getValue(true);\n    const offsetSorted = this.getValueToOffset(valueSorted);\n    const boundParts = isValueRange(valueSorted) ? valueSorted : [0, valueSorted];\n    const trackParts = isValueRange(offsetSorted) ? [offsetSorted[0], offsetSorted[1] - offsetSorted[0]] : [0, offsetSorted];\n    this.handles.forEach((handle, index) => {\n      handle.offset = isValueRange(offset) ? offset[index] : offset;\n      handle.value = isValueRange(value) ? value[index] : value || 0;\n    });\n    [this.bounds.lower, this.bounds.upper] = boundParts;\n    [this.track.offset, this.track.length] = trackParts;\n    this.cdr.markForCheck();\n  }\n  onDragStart(value) {\n    this.toggleDragMoving(true);\n    this.cacheSliderProperty();\n    this.setActiveValueIndex(this.getLogicalValue(value));\n    this.setActiveValue(this.getLogicalValue(value));\n    this.showHandleTooltip(this.nzRange ? this.activeValueIndex : 0);\n  }\n  onDragMove(value) {\n    this.setActiveValue(this.getLogicalValue(value));\n    this.cdr.markForCheck();\n  }\n  getLogicalValue(value) {\n    if (this.nzReverse) {\n      if (!this.nzVertical && this.dir === 'rtl') {\n        return value;\n      }\n      return this.nzMax - value + this.nzMin;\n    }\n    if (!this.nzVertical && this.dir === 'rtl') {\n      return this.nzMax - value + this.nzMin;\n    }\n    return value;\n  }\n  onDragEnd() {\n    this.nzOnAfterChange.emit(this.getValue(true));\n    this.toggleDragMoving(false);\n    this.cacheSliderProperty(true);\n    this.hideAllHandleTooltip();\n    this.cdr.markForCheck();\n  }\n  /**\n   * Create user interactions handles.\n   */\n  bindDraggingHandlers() {\n    if (!this.platform.isBrowser) {\n      return;\n    }\n    const pluckFunc = keys => event => keys.reduce((acc, key) => acc[key] || acc, event);\n    const sliderDOM = this.slider.nativeElement;\n    const orientField = this.nzVertical ? 'pageY' : 'pageX';\n    const mouse = {\n      start: 'mousedown',\n      move: 'mousemove',\n      end: 'mouseup',\n      pluckKey: [orientField]\n    };\n    const touch = {\n      start: 'touchstart',\n      move: 'touchmove',\n      end: 'touchend',\n      pluckKey: ['touches', '0', orientField],\n      filter: e => e instanceof TouchEvent\n    };\n    [mouse, touch].forEach(source => {\n      const {\n        start,\n        move,\n        end,\n        pluckKey,\n        filter: filterFunc = () => true\n      } = source;\n      source.startPlucked$ = fromEvent(sliderDOM, start).pipe(filter(filterFunc), tap(silentEvent), map(pluckFunc(pluckKey)), map(position => this.findClosestValue(position)));\n      source.end$ = fromEvent(document, end);\n      source.moveResolved$ = fromEvent(document, move).pipe(filter(filterFunc), tap(silentEvent), map(pluckFunc(pluckKey)), distinctUntilChanged(), map(position => this.findClosestValue(position)), distinctUntilChanged(), takeUntil(source.end$));\n    });\n    this.dragStart$ = merge(mouse.startPlucked$, touch.startPlucked$);\n    this.dragMove$ = merge(mouse.moveResolved$, touch.moveResolved$);\n    this.dragEnd$ = merge(mouse.end$, touch.end$);\n  }\n  subscribeDrag(periods = ['start', 'move', 'end']) {\n    if (periods.indexOf('start') !== -1 && this.dragStart$ && !this.dragStart_) {\n      this.dragStart_ = this.dragStart$.subscribe(this.onDragStart.bind(this));\n    }\n    if (periods.indexOf('move') !== -1 && this.dragMove$ && !this.dragMove_) {\n      this.dragMove_ = this.dragMove$.subscribe(this.onDragMove.bind(this));\n    }\n    if (periods.indexOf('end') !== -1 && this.dragEnd$ && !this.dragEnd_) {\n      this.dragEnd_ = this.dragEnd$.subscribe(this.onDragEnd.bind(this));\n    }\n  }\n  unsubscribeDrag(periods = ['start', 'move', 'end']) {\n    if (periods.indexOf('start') !== -1 && this.dragStart_) {\n      this.dragStart_.unsubscribe();\n      this.dragStart_ = null;\n    }\n    if (periods.indexOf('move') !== -1 && this.dragMove_) {\n      this.dragMove_.unsubscribe();\n      this.dragMove_ = null;\n    }\n    if (periods.indexOf('end') !== -1 && this.dragEnd_) {\n      this.dragEnd_.unsubscribe();\n      this.dragEnd_ = null;\n    }\n  }\n  toggleDragMoving(movable) {\n    const periods = ['move', 'end'];\n    if (movable) {\n      this.sliderService.isDragging = true;\n      this.subscribeDrag(periods);\n    } else {\n      this.sliderService.isDragging = false;\n      this.unsubscribeDrag(periods);\n    }\n  }\n  toggleDragDisabled(disabled) {\n    if (disabled) {\n      this.unsubscribeDrag();\n    } else {\n      this.subscribeDrag(['start']);\n    }\n  }\n  findClosestValue(position) {\n    const sliderStart = this.getSliderStartPosition();\n    const sliderLength = this.getSliderLength();\n    const ratio = ensureNumberInRange((position - sliderStart) / sliderLength, 0, 1);\n    const val = (this.nzMax - this.nzMin) * (this.nzVertical ? 1 - ratio : ratio) + this.nzMin;\n    const points = this.nzMarks === null ? [] : Object.keys(this.nzMarks).map(parseFloat).sort((a, b) => a - b);\n    if (this.nzStep !== 0 && !this.nzDots) {\n      const closestOne = Math.round(val / this.nzStep) * this.nzStep;\n      points.push(closestOne);\n    }\n    const gaps = points.map(point => Math.abs(val - point));\n    const closest = points[gaps.indexOf(Math.min(...gaps))];\n    // return parseFloat(closest.toFixed(getPrecision(this.nzStep)));\n    return this.nzStep === 0 ? closest : parseFloat(closest.toFixed(getPrecision(this.nzStep)));\n  }\n  valueToOffset(value) {\n    return getPercent(this.nzMin, this.nzMax, value);\n  }\n  getSliderStartPosition() {\n    if (this.cacheSliderStart !== null) {\n      return this.cacheSliderStart;\n    }\n    const offset = getElementOffset(this.slider.nativeElement);\n    return this.nzVertical ? offset.top : offset.left;\n  }\n  getSliderLength() {\n    if (this.cacheSliderLength !== null) {\n      return this.cacheSliderLength;\n    }\n    const sliderDOM = this.slider.nativeElement;\n    return this.nzVertical ? sliderDOM.clientHeight : sliderDOM.clientWidth;\n  }\n  /**\n   * Cache DOM layout/reflow operations for performance (may not necessary?)\n   */\n  cacheSliderProperty(remove = false) {\n    this.cacheSliderStart = remove ? null : this.getSliderStartPosition();\n    this.cacheSliderLength = remove ? null : this.getSliderLength();\n  }\n  formatValue(value) {\n    if (isNil(value)) {\n      return this.nzRange ? [this.nzMin, this.nzMax] : this.nzMin;\n    } else if (assertValueValid(value, this.nzRange)) {\n      return isValueRange(value) ? value.map(val => ensureNumberInRange(val, this.nzMin, this.nzMax)) : ensureNumberInRange(value, this.nzMin, this.nzMax);\n    } else {\n      return this.nzDefaultValue ? this.nzDefaultValue : this.nzRange ? [this.nzMin, this.nzMax] : this.nzMin;\n    }\n  }\n  /**\n   * Show one handle's tooltip and hide others'.\n   */\n  showHandleTooltip(handleIndex = 0) {\n    this.handles.forEach((handle, index) => {\n      handle.active = index === handleIndex;\n    });\n  }\n  hideAllHandleTooltip() {\n    this.handles.forEach(handle => handle.active = false);\n  }\n  generateMarkItems(marks) {\n    const marksArray = [];\n    for (const key in marks) {\n      if (marks.hasOwnProperty(key)) {\n        const mark = marks[key];\n        const val = typeof key === 'number' ? key : parseFloat(key);\n        if (val >= this.nzMin && val <= this.nzMax) {\n          marksArray.push({\n            value: val,\n            offset: this.valueToOffset(val),\n            config: mark\n          });\n        }\n      }\n    }\n    return marksArray.length ? marksArray : null;\n  }\n  static {\n    this.ɵfac = function NzSliderComponent_Factory(t) {\n      return new (t || NzSliderComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NzSliderService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2$1.Platform), i0.ɵɵdirectiveInject(i3.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSliderComponent,\n      selectors: [[\"nz-slider\"]],\n      viewQuery: function NzSliderComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(NzSliderHandleComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.handlerComponents = _t);\n        }\n      },\n      hostAttrs: [1, \"ant-slider\"],\n      hostVars: 8,\n      hostBindings: function NzSliderComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function NzSliderComponent_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-slider-rtl\", ctx.dir === \"rtl\")(\"ant-slider-disabled\", ctx.nzDisabled)(\"ant-slider-vertical\", ctx.nzVertical)(\"ant-slider-with-marks\", ctx.marksArray);\n        }\n      },\n      inputs: {\n        nzDisabled: \"nzDisabled\",\n        nzDots: \"nzDots\",\n        nzIncluded: \"nzIncluded\",\n        nzRange: \"nzRange\",\n        nzVertical: \"nzVertical\",\n        nzReverse: \"nzReverse\",\n        nzDefaultValue: \"nzDefaultValue\",\n        nzMarks: \"nzMarks\",\n        nzMax: \"nzMax\",\n        nzMin: \"nzMin\",\n        nzStep: \"nzStep\",\n        nzTooltipVisible: \"nzTooltipVisible\",\n        nzTooltipPlacement: \"nzTooltipPlacement\",\n        nzTipFormatter: \"nzTipFormatter\"\n      },\n      outputs: {\n        nzOnAfterChange: \"nzOnAfterChange\"\n      },\n      exportAs: [\"nzSlider\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzSliderComponent),\n        multi: true\n      }, NzSliderService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 9,\n      consts: [[1, \"ant-slider-rail\"], [3, \"vertical\", \"included\", \"offset\", \"length\", \"reverse\", \"dir\"], [3, \"vertical\", \"min\", \"max\", \"lowerBound\", \"upperBound\", \"marksArray\", \"included\", \"reverse\", 4, \"ngIf\"], [3, \"vertical\", \"reverse\", \"offset\", \"value\", \"active\", \"tooltipFormatter\", \"tooltipVisible\", \"tooltipPlacement\", \"dir\", \"focusin\", 4, \"ngFor\", \"ngForOf\"], [3, \"vertical\", \"min\", \"max\", \"lowerBound\", \"upperBound\", \"marksArray\", \"included\", \"reverse\"], [3, \"focusin\", \"vertical\", \"reverse\", \"offset\", \"value\", \"active\", \"tooltipFormatter\", \"tooltipVisible\", \"tooltipPlacement\", \"dir\"]],\n      template: function NzSliderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 0)(1, \"nz-slider-track\", 1);\n          i0.ɵɵtemplate(2, NzSliderComponent_nz_slider_step_2_Template, 1, 8, \"nz-slider-step\", 2)(3, NzSliderComponent_nz_slider_handle_3_Template, 1, 9, \"nz-slider-handle\", 3)(4, NzSliderComponent_nz_slider_marks_4_Template, 1, 8, \"nz-slider-marks\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"vertical\", ctx.nzVertical)(\"included\", ctx.nzIncluded)(\"offset\", ctx.track.offset)(\"length\", ctx.track.length)(\"reverse\", ctx.nzReverse)(\"dir\", ctx.dir);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.marksArray);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.handles);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.marksArray);\n        }\n      },\n      dependencies: [NzSliderTrackComponent, NzSliderStepComponent, NzSliderHandleComponent, NgForOf, NzSliderMarksComponent, NgIf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzSliderComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzSliderComponent.prototype, \"nzDots\", void 0);\n__decorate([InputBoolean()], NzSliderComponent.prototype, \"nzIncluded\", void 0);\n__decorate([InputBoolean()], NzSliderComponent.prototype, \"nzRange\", void 0);\n__decorate([InputBoolean()], NzSliderComponent.prototype, \"nzVertical\", void 0);\n__decorate([InputBoolean()], NzSliderComponent.prototype, \"nzReverse\", void 0);\n__decorate([InputNumber()], NzSliderComponent.prototype, \"nzMax\", void 0);\n__decorate([InputNumber()], NzSliderComponent.prototype, \"nzMin\", void 0);\n__decorate([InputNumber()], NzSliderComponent.prototype, \"nzStep\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSliderComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-slider',\n      exportAs: 'nzSlider',\n      preserveWhitespaces: false,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzSliderComponent),\n        multi: true\n      }, NzSliderService],\n      template: `\n    <div class=\"ant-slider-rail\"></div>\n    <nz-slider-track\n      [vertical]=\"nzVertical\"\n      [included]=\"nzIncluded\"\n      [offset]=\"track.offset!\"\n      [length]=\"track.length!\"\n      [reverse]=\"nzReverse\"\n      [dir]=\"dir\"\n    ></nz-slider-track>\n    <nz-slider-step\n      *ngIf=\"marksArray\"\n      [vertical]=\"nzVertical\"\n      [min]=\"nzMin\"\n      [max]=\"nzMax\"\n      [lowerBound]=\"$any(bounds.lower)\"\n      [upperBound]=\"$any(bounds.upper)\"\n      [marksArray]=\"marksArray\"\n      [included]=\"nzIncluded\"\n      [reverse]=\"nzReverse\"\n    ></nz-slider-step>\n    <nz-slider-handle\n      *ngFor=\"let handle of handles; index as handleIndex\"\n      [vertical]=\"nzVertical\"\n      [reverse]=\"nzReverse\"\n      [offset]=\"handle.offset!\"\n      [value]=\"handle.value!\"\n      [active]=\"handle.active\"\n      [tooltipFormatter]=\"nzTipFormatter\"\n      [tooltipVisible]=\"nzTooltipVisible\"\n      [tooltipPlacement]=\"nzTooltipPlacement\"\n      [dir]=\"dir\"\n      (focusin)=\"onHandleFocusIn(handleIndex)\"\n    ></nz-slider-handle>\n    <nz-slider-marks\n      *ngIf=\"marksArray\"\n      [vertical]=\"nzVertical\"\n      [min]=\"nzMin\"\n      [max]=\"nzMax\"\n      [lowerBound]=\"$any(bounds.lower)\"\n      [upperBound]=\"$any(bounds.upper)\"\n      [marksArray]=\"marksArray\"\n      [included]=\"nzIncluded\"\n      [reverse]=\"nzReverse\"\n    ></nz-slider-marks>\n  `,\n      imports: [NzSliderTrackComponent, NzSliderStepComponent, NzSliderHandleComponent, NgForOf, NzSliderMarksComponent, NgIf],\n      standalone: true,\n      host: {\n        class: 'ant-slider',\n        '[class.ant-slider-rtl]': `dir === 'rtl'`,\n        '[class.ant-slider-disabled]': 'nzDisabled',\n        '[class.ant-slider-vertical]': 'nzVertical',\n        '[class.ant-slider-with-marks]': 'marksArray',\n        '(keydown)': 'onKeyDown($event)'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: NzSliderService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2$1.Platform\n  }, {\n    type: i3.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    handlerComponents: [{\n      type: ViewChildren,\n      args: [NzSliderHandleComponent]\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzDots: [{\n      type: Input\n    }],\n    nzIncluded: [{\n      type: Input\n    }],\n    nzRange: [{\n      type: Input\n    }],\n    nzVertical: [{\n      type: Input\n    }],\n    nzReverse: [{\n      type: Input\n    }],\n    nzDefaultValue: [{\n      type: Input\n    }],\n    nzMarks: [{\n      type: Input\n    }],\n    nzMax: [{\n      type: Input\n    }],\n    nzMin: [{\n      type: Input\n    }],\n    nzStep: [{\n      type: Input\n    }],\n    nzTooltipVisible: [{\n      type: Input\n    }],\n    nzTooltipPlacement: [{\n      type: Input\n    }],\n    nzTipFormatter: [{\n      type: Input\n    }],\n    nzOnAfterChange: [{\n      type: Output\n    }]\n  });\n})();\nfunction getValueTypeNotMatchError() {\n  return new Error(`The \"nzRange\" can't match the \"ngModel\"'s type, please check these properties: \"nzRange\", \"ngModel\", \"nzDefaultValue\".`);\n}\nfunction isValueRange(value) {\n  if (value instanceof Array) {\n    return value.length === 2;\n  } else {\n    return false;\n  }\n}\nfunction generateHandlers(amount) {\n  return Array(amount).fill(0).map(() => ({\n    offset: null,\n    value: null,\n    active: false\n  }));\n}\n/**\n * Check if value is valid and throw error if value-type/range not match.\n */\nfunction assertValueValid(value, isRange) {\n  if (!isValueRange(value) && isNaN(value) || isValueRange(value) && value.some(v => isNaN(v))) {\n    return false;\n  }\n  return assertValueTypeMatch(value, isRange);\n}\n/**\n * Assert that if `this.nzRange` is `true`, value is also a range, vice versa.\n */\nfunction assertValueTypeMatch(value, isRange = false) {\n  if (isValueRange(value) !== isRange) {\n    throw getValueTypeNotMatchError();\n  }\n  return true;\n}\nfunction valuesEqual(valA, valB) {\n  if (typeof valA !== typeof valB) {\n    return false;\n  }\n  return isValueRange(valA) && isValueRange(valB) ? arraysEqual(valA, valB) : valA === valB;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSliderModule {\n  static {\n    this.ɵfac = function NzSliderModule_Factory(t) {\n      return new (t || NzSliderModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzSliderModule,\n      imports: [NzSliderComponent, NzSliderTrackComponent, NzSliderHandleComponent, NzSliderStepComponent, NzSliderMarksComponent],\n      exports: [NzSliderComponent, NzSliderTrackComponent, NzSliderHandleComponent, NzSliderStepComponent, NzSliderMarksComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzSliderComponent, NzSliderHandleComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSliderModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzSliderComponent, NzSliderTrackComponent, NzSliderHandleComponent, NzSliderStepComponent, NzSliderMarksComponent],\n      exports: [NzSliderComponent, NzSliderTrackComponent, NzSliderHandleComponent, NzSliderStepComponent, NzSliderMarksComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMarks {}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzMarks, NzSliderComponent, NzSliderModule, NzSliderHandleComponent as ɵNzSliderHandleComponent, NzSliderMarksComponent as ɵNzSliderMarksComponent, NzSliderService as ɵNzSliderService, NzSliderStepComponent as ɵNzSliderStepComponent, NzSliderTrackComponent as ɵNzSliderTrackComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,YAAY,0BAA0B,QAAQ,MAAM;AACvD,IAAG,WAAW,WAAW,QAAQ,KAAK,EAAE,aAAa,QAAQ,OAAU,cAAc;AAAA,EACvF;AACF;AACA,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,YAAY,yBAAyB,QAAQ,MAAM;AACtD,IAAG,WAAW,WAAW,QAAQ,KAAK;AAAA,EACxC;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB,CAAC;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,UAAU,EAAE,OAAO,OAAO,KAAK,EAAE,OAAO,OAAO,KAAK,EAAE,cAAc,OAAO,OAAO,KAAK,EAAE,cAAc,OAAO,OAAO,KAAK,EAAE,cAAc,OAAO,UAAU,EAAE,YAAY,OAAO,UAAU,EAAE,WAAW,OAAO,SAAS;AAAA,EAC1P;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,oBAAoB,CAAC;AAC1C,IAAG,WAAW,WAAW,SAAS,oFAAoF;AACpH,YAAM,iBAAoB,cAAc,GAAG,EAAE;AAC7C,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,cAAc,CAAC;AAAA,IAC9D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,UAAU,EAAE,WAAW,OAAO,SAAS,EAAE,UAAU,UAAU,MAAM,EAAE,SAAS,UAAU,KAAK,EAAE,UAAU,UAAU,MAAM,EAAE,oBAAoB,OAAO,cAAc,EAAE,kBAAkB,OAAO,gBAAgB,EAAE,oBAAoB,OAAO,kBAAkB,EAAE,OAAO,OAAO,GAAG;AAAA,EACpT;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,mBAAmB,CAAC;AAAA,EACtC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,UAAU,EAAE,OAAO,OAAO,KAAK,EAAE,OAAO,OAAO,KAAK,EAAE,cAAc,OAAO,OAAO,KAAK,EAAE,cAAc,OAAO,OAAO,KAAK,EAAE,cAAc,OAAO,UAAU,EAAE,YAAY,OAAO,UAAU,EAAE,WAAW,OAAO,SAAS;AAAA,EAC1P;AACF;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,eAAe,KAAK;AAC9B,SAAK,gBAAgB;AACrB,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,QAAQ,CAAC;AACd,SAAK,cAAc,MAAM;AACvB,UAAI,CAAC,KAAK,cAAc,YAAY;AAClC,aAAK,cAAc,IAAI;AACvB,aAAK,sBAAsB;AAC3B,aAAK,IAAI,cAAc;AAAA,MACzB;AAAA,IACF;AACA,SAAK,cAAc,MAAM;AACvB,UAAI,CAAC,KAAK,cAAc,YAAY;AAClC,aAAK,cAAc,KAAK;AACxB,aAAK,IAAI,cAAc;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,UAAU,WAAW,KAAK;AAC5B,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,OAAO;AACT,WAAK,mBAAmB;AACxB,WAAK,sBAAsB;AAAA,IAC7B;AACA,QAAI,QAAQ;AACV,UAAI,OAAO,cAAc;AACvB,aAAK,cAAc,IAAI;AAAA,MACzB,OAAO;AACL,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,gBAAgB,iBAAiB,UAAU;AAC7C,cAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,cAAc,MAAM,IAAI,CAAC;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,QAAQ;AACN,SAAK,UAAU,cAAc,MAAM;AAAA,EACrC;AAAA,EACA,cAAc,MAAM,QAAQ,OAAO;AACjC,QAAI,CAAC,UAAU,KAAK,mBAAmB,aAAa,CAAC,KAAK,UAAU;AAClE;AAAA,IACF;AACA,QAAI,MAAM;AACR,WAAK,SAAS,KAAK;AAAA,IACrB,OAAO;AACL,WAAK,SAAS,KAAK;AAAA,IACrB;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,kBAAkB;AACzB,WAAK,eAAe,OAAO,KAAK,qBAAqB,aAAa,KAAK,iBAAiB,KAAK,KAAK,IAAI,KAAK;AAAA,IAC7G,OAAO;AACL,WAAK,eAAe,GAAG,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,QAAI,KAAK,SAAS;AAChB,cAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,SAAS,eAAe,CAAC;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,cAAc;AACZ,UAAM,WAAW,KAAK;AACtB,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,gBAAgB,WAAW;AAAA,MAC/B,CAAC,UAAU,QAAQ,QAAQ,GAAG,GAAG,MAAM;AAAA,MACvC,CAAC,UAAU,WAAW,KAAK,GAAG;AAAA,MAC9B,WAAW,UAAU,OAAO;AAAA,IAC9B,IAAI,iCACC,KAAK,2BAA2B,IADjC;AAAA,MAEF,WAAW,cAAc,UAAU,KAAK,QAAQ,QAAQ,MAAM,MAAM,KAAK,QAAQ,QAAQ,MAAM,GAAG;AAAA,IACpG;AACA,SAAK,QAAQ;AACb,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,6BAA6B;AAC3B,QAAI,OAAO,KAAK,UAAU,SAAS,GAAG,KAAK,MAAM;AACjD,QAAI,QAAQ,KAAK,UAAU,GAAG,KAAK,MAAM,MAAM;AAC/C,QAAI,KAAK,QAAQ,OAAO;AACtB,YAAM,MAAM;AACZ,aAAO;AACP,cAAQ;AAAA,IACV;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAA4B,kBAAkB,eAAe,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,IAC7H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,WAAW,SAAS,8BAA8B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,oBAAoB,CAAC;AAAA,QACtC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAAA,QAChE;AAAA,MACF;AAAA,MACA,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,cAAc,SAAS,wDAAwD;AAC3F,mBAAO,IAAI,YAAY;AAAA,UACzB,CAAC,EAAE,cAAc,SAAS,wDAAwD;AAChF,mBAAO,IAAI,YAAY;AAAA,UACzB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,QAClB,QAAQ;AAAA,QACR,KAAK;AAAA,MACP;AAAA,MACA,UAAU,CAAC,gBAAgB;AAAA,MAC3B,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,YAAY,KAAK,cAAc,IAAI,GAAG,qBAAqB,GAAG,WAAW,kBAAkB,yBAAyB,oBAAoB,oBAAoB,CAAC;AAAA,MACvL,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,GAAG,OAAO,GAAG,CAAC;AAAA,QAC7B;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,IAAI,KAAK,EAAE,kBAAkB,IAAI,qBAAqB,QAAQ,IAAI,mBAAmB,UAAU,OAAO,IAAI,YAAY,EAAE,yBAA4B,gBAAgB,GAAG,KAAK,IAAI,KAAK,CAAC,EAAE,oBAAoB,IAAI,EAAE,sBAAsB,IAAI,gBAAgB;AAAA,QACvR;AAAA,MACF;AAAA,MACA,cAAc,CAAC,iBAAoB,oBAAoB,OAAO;AAAA,MAC9D,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,wBAAwB,WAAW,UAAU,MAAM;AAAA,CAC/E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,MAAM;AAAA,QACJ,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB;AAAA,MACA,SAAS,CAAC,iBAAiB,OAAO;AAAA,MAClC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,aAAa,CAAC;AACnB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,QAAQ,CAAC;AAAA,EAChB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,cAAc,SAAS;AACzB,WAAK,WAAW;AAAA,IAClB;AACA,QAAI,cAAc,cAAc,cAAc,SAAS;AACrD,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,UAAU,QAAQ,MAAM;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa;AACX,UAAM,QAAQ,KAAK,MAAM,KAAK;AAC9B,SAAK,QAAQ,KAAK,WAAW,IAAI,UAAQ;AACvC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,QAAQ,KAAK,cAAc,OAAO,OAAO,MAAM;AACrD,YAAM,QAAQ,eAAe,MAAM,IAAI,OAAO,QAAQ;AACtD,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc,OAAO,OAAO,QAAQ;AAClC,QAAI;AACJ,UAAM,YAAY,KAAK,UAAU,KAAK,MAAM,KAAK,MAAM,QAAQ;AAC/D,QAAI,KAAK,UAAU;AACjB,cAAQ;AAAA,QACN,cAAc;AAAA,QACd,QAAQ,IAAI,YAAY,KAAK,OAAO,QAAQ,GAAG;AAAA,MACjD;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,QACN,WAAW;AAAA,QACX,MAAM,IAAI,YAAY,KAAK,OAAO,QAAQ,GAAG;AAAA,MAC/C;AAAA,IACF;AACA,QAAI,eAAe,MAAM,KAAK,OAAO,OAAO;AAC1C,cAAQ,kCACH,QACA,OAAO;AAAA,IAEd;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,SAAS,KAAK,eAAe,QAAQ,KAAK,eAAe,MAAM;AACtE,WAAK,MAAM,QAAQ,UAAQ;AACzB,cAAM,QAAQ,KAAK;AACnB,cAAM,WAAW,CAAC,KAAK,YAAY,UAAU,KAAK,cAAc,KAAK,YAAY,SAAS,KAAK,cAAc,SAAS,KAAK;AAC3H,aAAK,SAAS;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAAwB;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,WAAW,CAAC,GAAG,iBAAiB;AAAA,MAChC,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,SAAS,wBAAwB,GAAG,0BAA0B,WAAW,aAAa,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,GAAG,wBAAwB,GAAG,WAAW,WAAW,CAAC;AAAA,MAC9L,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,QAAQ,CAAC;AAAA,QAC1E;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,IAAI,KAAK,EAAE,gBAAgB,IAAI,SAAS;AAAA,QACnE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,OAAO;AAAA,MAC/B,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,uBAAuB,WAAW,YAAY,MAAM;AACjF,WAAW,CAAC,aAAa,CAAC,GAAG,uBAAuB,WAAW,YAAY,MAAM;AAAA,CAChF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,qBAAqB;AAAA,MACrB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASV,SAAS,CAAC,SAAS,OAAO;AAAA,MAC1B,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,eAAe,QAAQ;AAC9B,SAAO,OAAO,WAAW;AAC3B;AACA,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,aAAa,CAAC;AACnB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,QAAQ,CAAC;AAAA,EAChB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,cAAc,SAAS;AACzB,WAAK,WAAW;AAAA,IAClB;AACA,QAAI,cAAc,cAAc,cAAc,SAAS;AACrD,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,UAAU,QAAQ,MAAM;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa;AACX,UAAM,SAAS,KAAK,WAAW,WAAW;AAC1C,SAAK,QAAQ,KAAK,WAAW,IAAI,UAAQ;AACvC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,SAAS,KAAK;AAClB,YAAM,QAAQ,KAAK,MAAM,KAAK;AAC9B,UAAI,KAAK,SAAS;AAChB,kBAAU,KAAK,MAAM,SAAS,QAAQ;AAAA,MACxC;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,OAAO;AAAA,UACL,CAAC,MAAM,GAAG,GAAG,MAAM;AAAA,UACnB,WAAW,KAAK,WAAW,oBAAoB;AAAA,QACjD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,SAAS,KAAK,eAAe,QAAQ,KAAK,eAAe,MAAM;AACtE,WAAK,MAAM,QAAQ,UAAQ;AACzB,cAAM,QAAQ,KAAK;AACnB,cAAM,WAAW,CAAC,KAAK,YAAY,UAAU,KAAK,cAAc,KAAK,YAAY,SAAS,KAAK,cAAc,SAAS,KAAK;AAC3H,aAAK,SAAS;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAAuB;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,MAC9B,WAAW,CAAC,GAAG,iBAAiB;AAAA,MAChC,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,SAAS,kBAAkB,GAAG,yBAAyB,WAAW,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,GAAG,kBAAkB,GAAG,SAAS,CAAC;AAAA,MACvJ,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,uCAAuC,GAAG,GAAG,QAAQ,CAAC;AAAA,QACzE;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,IAAI,KAAK,EAAE,gBAAgB,IAAI,SAAS;AAAA,QACnE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,OAAO;AAAA,MAC/B,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,sBAAsB,WAAW,YAAY,MAAM;AAChF,WAAW,CAAC,aAAa,CAAC,GAAG,sBAAsB,WAAW,YAAY,MAAM;AAAA,CAC/E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQV,SAAS,CAAC,SAAS,OAAO;AAAA,MAC1B,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,QAAQ,CAAC;AAAA,EAChB;AAAA,EACA,cAAc;AACZ,UAAM,WAAW,KAAK;AACtB,UAAM,UAAU,KAAK;AACrB,UAAM,aAAa,KAAK,WAAW,YAAY;AAC/C,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,KAAK;AACpB,UAAM,eAAe,WAAW;AAAA,MAC9B,CAAC,UAAU,QAAQ,QAAQ,GAAG,GAAG,MAAM;AAAA,MACvC,CAAC,UAAU,WAAW,KAAK,GAAG;AAAA,MAC9B,QAAQ,GAAG,MAAM;AAAA,MACjB;AAAA,IACF,IAAI,iCACC,KAAK,2BAA2B,IADjC;AAAA,MAEF,OAAO,GAAG,MAAM;AAAA,MAChB;AAAA,IACF;AACA,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,6BAA6B;AAC3B,QAAI,OAAO,KAAK,UAAU,SAAS,GAAG,KAAK,MAAM;AACjD,QAAI,QAAQ,KAAK,UAAU,GAAG,KAAK,MAAM,MAAM;AAC/C,QAAI,KAAK,QAAQ,OAAO;AACtB,YAAM,MAAM;AACZ,aAAO;AACP,cAAQ;AAAA,IACV;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAAwB;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MACA,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,oBAAoB,GAAG,SAAS,CAAC;AAAA,MAC9C,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,GAAG,OAAO,CAAC;AAAA,QAC1B;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,IAAI,KAAK;AAAA,QACpC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,OAAO;AAAA,MACtB,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,YAAY,CAAC,GAAG,uBAAuB,WAAW,UAAU,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,uBAAuB,WAAW,WAAW,MAAM;AAChF,WAAW,CAAC,YAAY,CAAC,GAAG,uBAAuB,WAAW,UAAU,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,uBAAuB,WAAW,YAAY,MAAM;AACjF,WAAW,CAAC,aAAa,CAAC,GAAG,uBAAuB,WAAW,YAAY,MAAM;AAAA,CAChF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,UAAU;AAAA,MACV,SAAS,CAAC,OAAO;AAAA,MACjB,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,QAAQ,eAAe,KAAK,UAAU,gBAAgB;AAChE,SAAK,SAAS;AACd,SAAK,gBAAgB;AACrB,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,mBAAmB;AACxB,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,QAAQ;AACb,SAAK,mBAAmB;AACxB,SAAK,oBAAoB;AACzB,SAAK,mBAAmB;AACxB,SAAK,QAAQ;AAAA,MACX,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AACA,SAAK,UAAU,CAAC;AAChB,SAAK,aAAa;AAClB,SAAK,SAAS;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AACA,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,WAAW;AACT,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AACvB,WAAK,sBAAsB;AAC3B,WAAK,cAAc,KAAK,SAAS,IAAI,CAAC;AAAA,IACxC,CAAC;AACD,SAAK,UAAU,iBAAiB,KAAK,UAAU,IAAI,CAAC;AACpD,SAAK,aAAa,KAAK,UAAU,KAAK,kBAAkB,KAAK,OAAO,IAAI;AACxE,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB,KAAK,UAAU;AACvC,QAAI,KAAK,SAAS,MAAM,MAAM;AAC5B,WAAK,SAAS,KAAK,YAAY,IAAI,CAAC;AAAA,IACtC;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,cAAc,CAAC,WAAW,aAAa;AACzC,WAAK,mBAAmB,WAAW,YAAY;AAAA,IACjD,WAAW,WAAW,CAAC,QAAQ,aAAa;AAC1C,WAAK,aAAa,KAAK,UAAU,KAAK,kBAAkB,KAAK,OAAO,IAAI;AAAA,IAC1E,WAAW,WAAW,CAAC,QAAQ,aAAa;AAC1C,WAAK,UAAU,iBAAiB,QAAQ,eAAe,IAAI,CAAC;AAC5D,WAAK,SAAS,KAAK,YAAY,IAAI,CAAC;AAAA,IACtC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB;AACrB,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,WAAW,KAAK;AACd,SAAK,SAAS,KAAK,IAAI;AAAA,EACzB;AAAA,EACA,cAAc,QAAQ;AAAA,EAAC;AAAA,EACvB,YAAY;AAAA,EAAC;AAAA,EACb,iBAAiB,IAAI;AACnB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,YAAY;AAC3B,SAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,SAAK,yBAAyB;AAC9B,SAAK,mBAAmB,KAAK,UAAU;AACvC,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,GAAG;AACX,QAAI,KAAK,YAAY;AACnB;AAAA,IACF;AACA,UAAM,OAAO,EAAE;AACf,UAAM,aAAa,SAAS,eAAe,SAAS;AACpD,UAAM,aAAa,SAAS,cAAc,SAAS;AACnD,QAAI,EAAE,cAAc,aAAa;AAC/B;AAAA,IACF;AACA,MAAE,eAAe;AACjB,QAAI,QAAQ,aAAa,CAAC,KAAK,SAAS,KAAK,WAAW,KAAK,YAAY,KAAK;AAC9E,WAAO,KAAK,QAAQ,QAAQ,OAAO,KAAK;AACxC,UAAM,SAAS,KAAK,UAAU,KAAK,MAAM,KAAK,gBAAgB,IAAI,OAAO,KAAK,QAAQ;AACtF,SAAK,eAAe,oBAAoB,QAAQ,KAAK,OAAO,KAAK,KAAK,CAAC;AACvE,SAAK,gBAAgB,KAAK,KAAK,SAAS,IAAI,CAAC;AAAA,EAC/C;AAAA,EACA,gBAAgB,OAAO;AACrB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,SAAS,OAAO,eAAe,OAAO;AACpC,QAAI,cAAc;AAChB,WAAK,QAAQ,KAAK,YAAY,KAAK;AACnC,WAAK,sBAAsB;AAAA,IAC7B,WAAW,CAAC,YAAY,KAAK,OAAO,KAAK,GAAG;AAC1C,WAAK,QAAQ;AACb,WAAK,sBAAsB;AAC3B,WAAK,cAAc,KAAK,SAAS,IAAI,CAAC;AAAA,IACxC;AAAA,EACF;AAAA,EACA,SAAS,eAAe,OAAO;AAC7B,QAAI,gBAAgB,KAAK,SAAS,aAAa,KAAK,KAAK,GAAG;AAC1D,aAAO,CAAC,GAAG,KAAK,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,IAC7C;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,OAAO;AACtB,QAAI,kBAAkB;AACtB,QAAI,OAAO,oBAAoB,aAAa;AAC1C,wBAAkB,KAAK,SAAS,IAAI;AAAA,IACtC;AACA,WAAO,aAAa,eAAe,IAAI,gBAAgB,IAAI,SAAO,KAAK,cAAc,GAAG,CAAC,IAAI,KAAK,cAAc,eAAe;AAAA,EACjI;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB,cAAc;AAChC,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI,aAAa,KAAK,GAAG;AACvB,UAAI,UAAU;AACd,UAAI;AACJ,UAAI,cAAc;AAClB,YAAM,QAAQ,CAAC,KAAK,UAAU;AAC5B,cAAM,KAAK,IAAI,eAAe,GAAG;AACjC,YAAI,YAAY,QAAQ,MAAM,SAAS;AACrC,oBAAU;AACV,wBAAc;AAAA,QAChB;AAAA,MACF,CAAC;AACD,WAAK,mBAAmB;AACxB,WAAK,kBAAkB,QAAQ,EAAE,WAAW,EAAE,MAAM;AAAA,IACtD,OAAO;AACL,WAAK,kBAAkB,QAAQ,EAAE,CAAC,EAAE,MAAM;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,eAAe,cAAc;AAC3B,QAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,YAAM,WAAW,CAAC,GAAG,KAAK,KAAK;AAC/B,eAAS,KAAK,gBAAgB,IAAI;AAClC,WAAK,SAAS,QAAQ;AAAA,IACxB,OAAO;AACL,WAAK,SAAS,YAAY;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,wBAAwB;AACtB,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,SAAS,KAAK,iBAAiB,KAAK;AAC1C,UAAM,cAAc,KAAK,SAAS,IAAI;AACtC,UAAM,eAAe,KAAK,iBAAiB,WAAW;AACtD,UAAM,aAAa,aAAa,WAAW,IAAI,cAAc,CAAC,GAAG,WAAW;AAC5E,UAAM,aAAa,aAAa,YAAY,IAAI,CAAC,aAAa,CAAC,GAAG,aAAa,CAAC,IAAI,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,YAAY;AACvH,SAAK,QAAQ,QAAQ,CAAC,QAAQ,UAAU;AACtC,aAAO,SAAS,aAAa,MAAM,IAAI,OAAO,KAAK,IAAI;AACvD,aAAO,QAAQ,aAAa,KAAK,IAAI,MAAM,KAAK,IAAI,SAAS;AAAA,IAC/D,CAAC;AACD,KAAC,KAAK,OAAO,OAAO,KAAK,OAAO,KAAK,IAAI;AACzC,KAAC,KAAK,MAAM,QAAQ,KAAK,MAAM,MAAM,IAAI;AACzC,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,iBAAiB,IAAI;AAC1B,SAAK,oBAAoB;AACzB,SAAK,oBAAoB,KAAK,gBAAgB,KAAK,CAAC;AACpD,SAAK,eAAe,KAAK,gBAAgB,KAAK,CAAC;AAC/C,SAAK,kBAAkB,KAAK,UAAU,KAAK,mBAAmB,CAAC;AAAA,EACjE;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,eAAe,KAAK,gBAAgB,KAAK,CAAC;AAC/C,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,gBAAgB,OAAO;AACrB,QAAI,KAAK,WAAW;AAClB,UAAI,CAAC,KAAK,cAAc,KAAK,QAAQ,OAAO;AAC1C,eAAO;AAAA,MACT;AACA,aAAO,KAAK,QAAQ,QAAQ,KAAK;AAAA,IACnC;AACA,QAAI,CAAC,KAAK,cAAc,KAAK,QAAQ,OAAO;AAC1C,aAAO,KAAK,QAAQ,QAAQ,KAAK;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY;AACV,SAAK,gBAAgB,KAAK,KAAK,SAAS,IAAI,CAAC;AAC7C,SAAK,iBAAiB,KAAK;AAC3B,SAAK,oBAAoB,IAAI;AAC7B,SAAK,qBAAqB;AAC1B,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB;AACrB,QAAI,CAAC,KAAK,SAAS,WAAW;AAC5B;AAAA,IACF;AACA,UAAM,YAAY,UAAQ,WAAS,KAAK,OAAO,CAAC,KAAK,QAAQ,IAAI,GAAG,KAAK,KAAK,KAAK;AACnF,UAAM,YAAY,KAAK,OAAO;AAC9B,UAAM,cAAc,KAAK,aAAa,UAAU;AAChD,UAAM,QAAQ;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,KAAK;AAAA,MACL,UAAU,CAAC,WAAW;AAAA,IACxB;AACA,UAAM,QAAQ;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,KAAK;AAAA,MACL,UAAU,CAAC,WAAW,KAAK,WAAW;AAAA,MACtC,QAAQ,OAAK,aAAa;AAAA,IAC5B;AACA,KAAC,OAAO,KAAK,EAAE,QAAQ,YAAU;AAC/B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ,aAAa,MAAM;AAAA,MAC7B,IAAI;AACJ,aAAO,gBAAgB,UAAU,WAAW,KAAK,EAAE,KAAK,OAAO,UAAU,GAAG,IAAI,WAAW,GAAG,IAAI,UAAU,QAAQ,CAAC,GAAG,IAAI,cAAY,KAAK,iBAAiB,QAAQ,CAAC,CAAC;AACxK,aAAO,OAAO,UAAU,UAAU,GAAG;AACrC,aAAO,gBAAgB,UAAU,UAAU,IAAI,EAAE,KAAK,OAAO,UAAU,GAAG,IAAI,WAAW,GAAG,IAAI,UAAU,QAAQ,CAAC,GAAG,qBAAqB,GAAG,IAAI,cAAY,KAAK,iBAAiB,QAAQ,CAAC,GAAG,qBAAqB,GAAG,UAAU,OAAO,IAAI,CAAC;AAAA,IAChP,CAAC;AACD,SAAK,aAAa,MAAM,MAAM,eAAe,MAAM,aAAa;AAChE,SAAK,YAAY,MAAM,MAAM,eAAe,MAAM,aAAa;AAC/D,SAAK,WAAW,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAC9C;AAAA,EACA,cAAc,UAAU,CAAC,SAAS,QAAQ,KAAK,GAAG;AAChD,QAAI,QAAQ,QAAQ,OAAO,MAAM,MAAM,KAAK,cAAc,CAAC,KAAK,YAAY;AAC1E,WAAK,aAAa,KAAK,WAAW,UAAU,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA,IACzE;AACA,QAAI,QAAQ,QAAQ,MAAM,MAAM,MAAM,KAAK,aAAa,CAAC,KAAK,WAAW;AACvE,WAAK,YAAY,KAAK,UAAU,UAAU,KAAK,WAAW,KAAK,IAAI,CAAC;AAAA,IACtE;AACA,QAAI,QAAQ,QAAQ,KAAK,MAAM,MAAM,KAAK,YAAY,CAAC,KAAK,UAAU;AACpE,WAAK,WAAW,KAAK,SAAS,UAAU,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,IACnE;AAAA,EACF;AAAA,EACA,gBAAgB,UAAU,CAAC,SAAS,QAAQ,KAAK,GAAG;AAClD,QAAI,QAAQ,QAAQ,OAAO,MAAM,MAAM,KAAK,YAAY;AACtD,WAAK,WAAW,YAAY;AAC5B,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,QAAQ,QAAQ,MAAM,MAAM,MAAM,KAAK,WAAW;AACpD,WAAK,UAAU,YAAY;AAC3B,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,QAAQ,QAAQ,KAAK,MAAM,MAAM,KAAK,UAAU;AAClD,WAAK,SAAS,YAAY;AAC1B,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,iBAAiB,SAAS;AACxB,UAAM,UAAU,CAAC,QAAQ,KAAK;AAC9B,QAAI,SAAS;AACX,WAAK,cAAc,aAAa;AAChC,WAAK,cAAc,OAAO;AAAA,IAC5B,OAAO;AACL,WAAK,cAAc,aAAa;AAChC,WAAK,gBAAgB,OAAO;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,mBAAmB,UAAU;AAC3B,QAAI,UAAU;AACZ,WAAK,gBAAgB;AAAA,IACvB,OAAO;AACL,WAAK,cAAc,CAAC,OAAO,CAAC;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,iBAAiB,UAAU;AACzB,UAAM,cAAc,KAAK,uBAAuB;AAChD,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,QAAQ,qBAAqB,WAAW,eAAe,cAAc,GAAG,CAAC;AAC/E,UAAM,OAAO,KAAK,QAAQ,KAAK,UAAU,KAAK,aAAa,IAAI,QAAQ,SAAS,KAAK;AACrF,UAAM,SAAS,KAAK,YAAY,OAAO,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AAC1G,QAAI,KAAK,WAAW,KAAK,CAAC,KAAK,QAAQ;AACrC,YAAM,aAAa,KAAK,MAAM,MAAM,KAAK,MAAM,IAAI,KAAK;AACxD,aAAO,KAAK,UAAU;AAAA,IACxB;AACA,UAAM,OAAO,OAAO,IAAI,WAAS,KAAK,IAAI,MAAM,KAAK,CAAC;AACtD,UAAM,UAAU,OAAO,KAAK,QAAQ,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;AAEtD,WAAO,KAAK,WAAW,IAAI,UAAU,WAAW,QAAQ,QAAQ,aAAa,KAAK,MAAM,CAAC,CAAC;AAAA,EAC5F;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,WAAW,KAAK,OAAO,KAAK,OAAO,KAAK;AAAA,EACjD;AAAA,EACA,yBAAyB;AACvB,QAAI,KAAK,qBAAqB,MAAM;AAClC,aAAO,KAAK;AAAA,IACd;AACA,UAAM,SAAS,iBAAiB,KAAK,OAAO,aAAa;AACzD,WAAO,KAAK,aAAa,OAAO,MAAM,OAAO;AAAA,EAC/C;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,sBAAsB,MAAM;AACnC,aAAO,KAAK;AAAA,IACd;AACA,UAAM,YAAY,KAAK,OAAO;AAC9B,WAAO,KAAK,aAAa,UAAU,eAAe,UAAU;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB,SAAS,OAAO;AAClC,SAAK,mBAAmB,SAAS,OAAO,KAAK,uBAAuB;AACpE,SAAK,oBAAoB,SAAS,OAAO,KAAK,gBAAgB;AAAA,EAChE;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,MAAM,KAAK,GAAG;AAChB,aAAO,KAAK,UAAU,CAAC,KAAK,OAAO,KAAK,KAAK,IAAI,KAAK;AAAA,IACxD,WAAW,iBAAiB,OAAO,KAAK,OAAO,GAAG;AAChD,aAAO,aAAa,KAAK,IAAI,MAAM,IAAI,SAAO,oBAAoB,KAAK,KAAK,OAAO,KAAK,KAAK,CAAC,IAAI,oBAAoB,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,IACrJ,OAAO;AACL,aAAO,KAAK,iBAAiB,KAAK,iBAAiB,KAAK,UAAU,CAAC,KAAK,OAAO,KAAK,KAAK,IAAI,KAAK;AAAA,IACpG;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,cAAc,GAAG;AACjC,SAAK,QAAQ,QAAQ,CAAC,QAAQ,UAAU;AACtC,aAAO,SAAS,UAAU;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB;AACrB,SAAK,QAAQ,QAAQ,YAAU,OAAO,SAAS,KAAK;AAAA,EACtD;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,aAAa,CAAC;AACpB,eAAW,OAAO,OAAO;AACvB,UAAI,MAAM,eAAe,GAAG,GAAG;AAC7B,cAAM,OAAO,MAAM,GAAG;AACtB,cAAM,MAAM,OAAO,QAAQ,WAAW,MAAM,WAAW,GAAG;AAC1D,YAAI,OAAO,KAAK,SAAS,OAAO,KAAK,OAAO;AAC1C,qBAAW,KAAK;AAAA,YACd,OAAO;AAAA,YACP,QAAQ,KAAK,cAAc,GAAG;AAAA,YAC9B,QAAQ;AAAA,UACV,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,WAAO,WAAW,SAAS,aAAa;AAAA,EAC1C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAqB,UAAU,GAAM,kBAAkB,eAAe,GAAM,kBAAqB,iBAAiB,GAAM,kBAAuB,QAAQ,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IAC7O;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,MACzB,WAAW,SAAS,wBAAwB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,yBAAyB,CAAC;AAAA,QAC3C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB;AAAA,QACvE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,YAAY;AAAA,MAC3B,UAAU;AAAA,MACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,SAAS,6CAA6C,QAAQ;AACrF,mBAAO,IAAI,UAAU,MAAM;AAAA,UAC7B,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,kBAAkB,IAAI,QAAQ,KAAK,EAAE,uBAAuB,IAAI,UAAU,EAAE,uBAAuB,IAAI,UAAU,EAAE,yBAAyB,IAAI,UAAU;AAAA,QAC3K;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,MAClB;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,UAAU;AAAA,MACrB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,kBAAiB;AAAA,QAC/C,OAAO;AAAA,MACT,GAAG,eAAe,CAAC,GAAM,sBAAyB,mBAAmB;AAAA,MACrE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,YAAY,YAAY,UAAU,UAAU,WAAW,KAAK,GAAG,CAAC,GAAG,YAAY,OAAO,OAAO,cAAc,cAAc,cAAc,YAAY,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,WAAW,UAAU,SAAS,UAAU,oBAAoB,kBAAkB,oBAAoB,OAAO,WAAW,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,YAAY,OAAO,OAAO,cAAc,cAAc,cAAc,YAAY,SAAS,GAAG,CAAC,GAAG,WAAW,YAAY,WAAW,UAAU,SAAS,UAAU,oBAAoB,kBAAkB,oBAAoB,KAAK,CAAC;AAAA,MAC9kB,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,GAAG,OAAO,CAAC,EAAE,GAAG,mBAAmB,CAAC;AACjD,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,kBAAkB,CAAC,EAAE,GAAG,+CAA+C,GAAG,GAAG,oBAAoB,CAAC,EAAE,GAAG,8CAA8C,GAAG,GAAG,mBAAmB,CAAC;AAAA,QACrP;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,YAAY,IAAI,UAAU,EAAE,YAAY,IAAI,UAAU,EAAE,UAAU,IAAI,MAAM,MAAM,EAAE,UAAU,IAAI,MAAM,MAAM,EAAE,WAAW,IAAI,SAAS,EAAE,OAAO,IAAI,GAAG;AACtK,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,UAAU;AACpC,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,OAAO;AACpC,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,UAAU;AAAA,QACtC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,wBAAwB,uBAAuB,yBAAyB,SAAS,wBAAwB,IAAI;AAAA,MAC5H,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,UAAU,MAAM;AAC1E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,WAAW,MAAM;AAC3E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,aAAa,MAAM;AAC7E,WAAW,CAAC,YAAY,CAAC,GAAG,kBAAkB,WAAW,SAAS,MAAM;AACxE,WAAW,CAAC,YAAY,CAAC,GAAG,kBAAkB,WAAW,SAAS,MAAM;AACxE,WAAW,CAAC,YAAY,CAAC,GAAG,kBAAkB,WAAW,UAAU,MAAM;AAAA,CACxE,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,iBAAiB;AAAA,QAC/C,OAAO;AAAA,MACT,GAAG,eAAe;AAAA,MAClB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA8CV,SAAS,CAAC,wBAAwB,uBAAuB,yBAAyB,SAAS,wBAAwB,IAAI;AAAA,MACvH,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,+BAA+B;AAAA,QAC/B,+BAA+B;AAAA,QAC/B,iCAAiC;AAAA,QACjC,aAAa;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,4BAA4B;AACnC,SAAO,IAAI,MAAM,wHAAwH;AAC3I;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,iBAAiB,OAAO;AAC1B,WAAO,MAAM,WAAW;AAAA,EAC1B,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,iBAAiB,QAAQ;AAChC,SAAO,MAAM,MAAM,EAAE,KAAK,CAAC,EAAE,IAAI,OAAO;AAAA,IACtC,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,EAAE;AACJ;AAIA,SAAS,iBAAiB,OAAO,SAAS;AACxC,MAAI,CAAC,aAAa,KAAK,KAAK,MAAM,KAAK,KAAK,aAAa,KAAK,KAAK,MAAM,KAAK,OAAK,MAAM,CAAC,CAAC,GAAG;AAC5F,WAAO;AAAA,EACT;AACA,SAAO,qBAAqB,OAAO,OAAO;AAC5C;AAIA,SAAS,qBAAqB,OAAO,UAAU,OAAO;AACpD,MAAI,aAAa,KAAK,MAAM,SAAS;AACnC,UAAM,0BAA0B;AAAA,EAClC;AACA,SAAO;AACT;AACA,SAAS,YAAY,MAAM,MAAM;AAC/B,MAAI,OAAO,SAAS,OAAO,MAAM;AAC/B,WAAO;AAAA,EACT;AACA,SAAO,aAAa,IAAI,KAAK,aAAa,IAAI,IAAI,YAAY,MAAM,IAAI,IAAI,SAAS;AACvF;AAMA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,mBAAmB,wBAAwB,yBAAyB,uBAAuB,sBAAsB;AAAA,MAC3H,SAAS,CAAC,mBAAmB,wBAAwB,yBAAyB,uBAAuB,sBAAsB;AAAA,IAC7H,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,mBAAmB,uBAAuB;AAAA,IACtD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,mBAAmB,wBAAwB,yBAAyB,uBAAuB,sBAAsB;AAAA,MAC3H,SAAS,CAAC,mBAAmB,wBAAwB,yBAAyB,uBAAuB,sBAAsB;AAAA,IAC7H,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,UAAN,MAAc;AAAC;", "names": []}