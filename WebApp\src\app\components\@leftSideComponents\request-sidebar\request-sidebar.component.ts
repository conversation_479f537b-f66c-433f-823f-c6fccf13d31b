import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ThemeService } from '../../../../shared/services/theam.service';

@Component({
  selector: 'app-request-sidebar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './request-sidebar.component.html',
  styleUrl: './request-sidebar.component.css'
})
export class RequestSidebarComponent {
  router = inject(Router);
  themeService = inject(ThemeService);
  activeRequestTab = 'request-list';

  constructor() {
    // Initialize based on current URL
    const currentUrl = this.router.url;
    if (currentUrl.includes('/all-request')) {
      this.activeRequestTab = 'all-request';
    } else if (currentUrl.includes('/my-request')) {
      const urlParts = currentUrl.split('/');
      if (urlParts.length > 2) {
        this.activeRequestTab = urlParts[2];
      }
    }
  }

  isRequestTabActive(tab: string): boolean {
    return this.activeRequestTab === tab;
  }
  navigateToRequestTab(event: Event, section: string) {
    event.stopPropagation();

    // Update the active tab
    this.activeRequestTab = section;

    // Navigate to the appropriate request section
    if (section === 'request-list') {
      this.router.navigate(['/my-request']);
    } else if (section === 'all-request') {
      this.router.navigate(['/all-request']);
    } else {
      this.router.navigate(['/my-request', section]);
    }
  }
}
