/* Hover effects for cards */
.group {
  transition: all 0.3s ease;
  border: 1px solid var(--hover-blue-gray);
}

.group:hover {
  transform: translateX(4px);
  border-color: var(--primary-purple);
}

/* Animation for fade in effect */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Card actions visibility */
.card-actions {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.api-card:hover .card-actions {
  opacity: 1;
}
::ng-deep .custom-checkbox {
  /* Remove default browser styling */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;

  /* Size and basic styling */
  width: 16px;
  height: 16px;
  background-color: var(--background-white, #ffffff); /* Unchecked background */
  border: 2px solid var(--hover-blue-gray, #cbd5e1); /* Unchecked border */
  border-radius: 2px; /* Subtle rounding */
  position: relative; /* For checkmark positioning */
  cursor: pointer;
  display: inline-block; /* Ensure visibility */
}

::ng-deep .custom-checkbox:checked {
  background-color: var(--primary-purple); /* Checked background */
  border-color: var(--primary-purple); /* Checked border */
}

::ng-deep .custom-checkbox:checked::before {
  content: '\2713'; /* Unicode for checkmark */
  color: white; /* Checkmark color */
  font-size: 12px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); /* Center checkmark */
  line-height: 1;
}

::ng-deep .custom-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-purple, #8b5cf6); /* Focus ring */
}
