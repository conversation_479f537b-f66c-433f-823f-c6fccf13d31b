/* History button styles */
button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

button:disabled:hover {
  background-color: var(--primary-purple);
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

button:disabled i {
  animation: pulse 1.5s infinite;
}

/* Scrollbar styles for better UX */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #4a4a4a;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Add to existing styles */
:host ::ng-deep {
  .prose {
    color: #fff;
  }

  .prose h3 {
    color: #fff;
    font-size: 1.1rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .prose ul {
    list-style-type: none;
    padding-left: 1rem;
  }

  .prose li {
    position: relative;
    padding-left: 1rem;
  }

  .prose li::before {
    content: "•";
    position: absolute;
    left: -0.5rem;
    color: #6B7280;
  }

  .prose strong {
    color: #E5E7EB;
    font-weight: 600;
  }

  .prose p {
    margin-bottom: 0.75rem;
  }

  .prose pre {
    background-color: #1F1F1F !important;
    border: 1px solid #2A2A2A;
  }

  .prose code {
    background-color: #2A2A2A;
    padding: 2px 4px;
    border-radius: 4px;
    font-size: 0.875em;
  }

  .prose pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    color: inherit;
  }

  .prose a {
    color: #60A5FA;
    text-decoration: none;
  }

  .prose a:hover {
    text-decoration: underline;
  }

  .prose blockquote {
    border-left-color: #2A2A2A;
    color: #9CA3AF;
  }

  .prose table {
    width: 100%;
    border-collapse: collapse;
  }

  .prose th,
  .prose td {
    border-color: #2A2A2A;
    padding: 0.5rem;
  }

  .prose th {
    background-color: #1F1F1F;
  }
}

/* Drawer styles for sidebars */
.ant-drawer-body {
  padding: 0;
  padding-left: 5px !important;
}

::ng-deep code.language-json {
  text-wrap: auto;
}

/* SQL View Styles */
.sql-view-container {
  width: 100%;
}

.sql-code-container {
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
}

.sql-code-container pre {
  margin: 0;
  padding: 0;
}

.sql-code-container code {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

::ng-deep .language-sql {
  text-wrap: auto;
  line-height: 1.5;
  white-space: pre;
  overflow-x: auto;
  max-width: 100%;
}

/* Syntax highlighting for SQL */
::ng-deep code.language-sql .keyword {
  color: #569CD6;
}

::ng-deep code.language-sql .function {
  color: #DCDCAA;
}

::ng-deep code.language-sql .string {
  color: #CE9178;
}

::ng-deep code.language-sql .number {
  color: #B5CEA8;
}

::ng-deep code.language-sql .operator {
  color: #D4D4D4;
}

::ng-deep code.language-sql .comment {
  color: #6A9955;
}

/* Enhanced Blog View Styles */
.blog-view-container {
  margin-top: 1.5rem;
  margin-bottom: 2rem;
}

/* Blog Header Enhancements */
.blog-header {
  position: relative;
  background: linear-gradient(135deg, var(--primary-purple) 0%, var(--secondary-purple) 100%);
}

.blog-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

/* Blog Body Enhancements */
.blog-body {
  background: linear-gradient(180deg, var(--background-white) 0%, rgba(var(--primary-purple-rgb), 0.02) 100%);
}

/* Blog Footer Enhancements */
.blog-footer {
  background: linear-gradient(90deg, var(--hover-blue-gray) 0%, var(--background-light-gray) 50%, var(--hover-blue-gray) 100%);
  backdrop-filter: blur(10px);
}

/* Enhanced Button Styles */
.blog-content button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.blog-content button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.blog-content button:hover::before {
  left: 100%;
}

/* Word Count Animation */
@keyframes countUp {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.word-count {
  animation: countUp 0.3s ease-out;
}

::ng-deep markdown h1, ::ng-deep markdown h2, ::ng-deep markdown h3, ::ng-deep markdown h4, ::ng-deep markdown h5, ::ng-deep markdown h6 {
  color: var(--primary-purple);
}
/* Enhance Markdown for Blog View */
:host ::ng-deep .markdown h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--primary-purple) !important;
}

:host ::ng-deep .markdown h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--secondary-purple);
  border-bottom: 1px solid var(--hover-blue-gray);
  padding-bottom: 0.25rem;
}

:host ::ng-deep .markdown h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: var(--primary-purple);
}

:host ::ng-deep .markdown p {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: var(--text-dark);
}

:host ::ng-deep .markdown ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
}

:host ::ng-deep .markdown ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
}

:host ::ng-deep .markdown li {
  margin-bottom: 0.25rem;
  color: var(--text-dark);
}

:host ::ng-deep .markdown a {
  color: var(--primary-purple);
  text-decoration: underline;
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
}

::ng-deep .markdown h1{
  font-size: 1.75rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--primary-purple);
}

:host ::ng-deep .markdown a:hover {
  color: var(--secondary-purple);
  text-decoration-thickness: 2px;
}

:host ::ng-deep .markdown blockquote {
  border-left: 4px solid var(--hover-blue-gray);
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: var(--text-medium-gray);
}

:host ::ng-deep .markdown img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius-small);
  margin: 1rem 0;
}

:host ::ng-deep .markdown code {
  background-color: var(--hover-blue-gray);
  padding: 0.125rem 0.25rem;
  border-radius: var(--border-radius-small);
  font-family: monospace;
  font-size: 0.875rem;
  color: var(--primary-purple);
}

:host ::ng-deep .markdown pre {
  background-color: var(--hover-blue-gray);
  padding: 1rem;
  border-radius: var(--border-radius-small);
  overflow-x: auto;
  margin: 1rem 0;
}

:host ::ng-deep .markdown pre code {
  background-color: transparent;
  padding: 0;
  color: var(--text-dark);
}

:host ::ng-deep .markdown table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

:host ::ng-deep .markdown th,
:host ::ng-deep .markdown td {
  border: 1px solid var(--hover-blue-gray);
  padding: 0.5rem;
}

:host ::ng-deep .markdown th {
  background-color: var(--hover-blue-gray);
  font-weight: 600;
}

/* Email View Styles */
.email-view-container {
  margin: 16px 0;
  border-radius: var(--border-radius-medium);
  overflow: hidden;
}

.email-content {
  background-color: var(--background-white);
  color: var(--text-dark);
}

.email-content .form-header {
  background-color: var(--primary-purple);
  color: var(--background-white);
}

.email-content .form-footer {
  background-color: var(--hover-blue-gray);
  border-top: 1px solid var(--hover-blue-gray);
}

.email-content input,
.email-content textarea {
  background-color: var(--background-white);
  color: var(--text-dark);
  border: 1px solid var(--hover-blue-gray);
}

.email-content input::placeholder,
.email-content textarea::placeholder {
  color: var(--text-medium-gray);
  opacity: 0.7;
}

.email-content input:focus,
.email-content textarea:focus {
  outline: none;
  border-color: var(--primary-purple);
  box-shadow: 0 0 0 1px var(--primary-purple);
}

.email-content textarea {
  resize: vertical;
}

/* Dark mode specific styles */
:host-context(.dark) .email-content {
  background-color: #2A2A2A;
  border-color: #333333;
}

:host-context(.dark) .email-content input,
:host-context(.dark) .email-content textarea {
  background-color: #1E1E1E;
  border-color: #333333;
  color: var(--text-dark);
}

:host-context(.dark) .email-content .form-footer {
  background-color: #1E1E1E;
  border-color: #333333;
}

:host-context(.dark) .email-content .form-header {
  background-color: rgb(16, 163, 127);
  /* Dark mode primary color */
}

:host-context(.dark) .email-content .copy-button {
  background-color: #333333;
  color: #E0E0E0;
}

:host-context(.dark) .email-content .copy-button:hover {
  background-color: #444444;
}

:host-context(.dark) .email-content input:focus,
:host-context(.dark) .email-content textarea:focus {
  box-shadow: 0 0 0 1px rgb(16, 163, 127);
  border-color: rgb(16, 163, 127);
}

/* Left accent for input fields in dark mode */
:host-context(.dark) .email-content .input-accent {
  border-left: 3px solid rgb(16, 163, 127);
}

/* SQL Results Modal Styles */
:host ::ng-deep .sql-results-modal {
  .ant-modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .sql-results-container {
    width: 100%;
    overflow-x: auto;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 16px;
    font-size: 14px;
  }

  th {
    background-color: #f5f5f5;
    padding: 8px 12px;
    text-align: left;
    font-weight: 600;
    border: 1px solid #e8e8e8;
  }

  td {
    padding: 8px 12px;
    border: 1px solid #e8e8e8;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  tbody tr:nth-child(odd) {
    background-color: #fafafa;
  }

  tbody tr:hover {
    background-color: #f0f0f0;
  }
}

/* Markdown formatting for chat responses */
:host ::ng-deep .response-container {
  /* Typography */
  font-size: 1rem;
  line-height: 1.6;

  /* Element spacing */
  p {
    margin-bottom: 1rem;
  }

  h1, h2, h3, h4, h5, h6 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--text-dark);
  }

  /* Lists */
  ul, ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
  }

  li {
    margin-bottom: 0.25rem;
  }

  /* Code formatting */
  code {
    background-color: var(--hover-blue-gray);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.9em;
  }

  pre {
    background-color: var(--hover-blue-gray);
    padding: 1rem;
    border-radius: var(--border-radius-small);
    overflow-x: auto;
    margin: 1rem 0;
  }

  pre code {
    background-color: transparent;
    padding: 0;
    color: var(--text-dark);
  }

  /* Tables */
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
  }

  th, td {
    border: 1px solid var(--hover-blue-gray);
    padding: 0.5rem;
  }

  th {
    background-color: var(--hover-blue-gray);
    font-weight: 600;
  }

  /* Links */
  a {
    color: var(--primary-purple);
    text-decoration: underline;
    text-decoration-thickness: 1px;
  }

  a:hover {
    color: var(--secondary-purple);
  }

  /* Blockquotes */
  blockquote {
    border-left: 4px solid var(--hover-blue-gray);
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: var(--text-medium-gray);
  }
}

::ng-deep code {
  text-wrap: auto;
}

/* Utility classes for chat component */
.width-indicator {
  position: absolute;
  top: 5px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--primary-purple);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 100;
}

/* Chat UI specific styles */
.right-sidebar-area, .main-content-area {
  transition: width 0.3s ease;
}

/* Animation for loading dots */
@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* Scrollable areas */
.sidebar-container, .chat-container {
  height: 100%;
  overflow-y: auto;
}

/* Response animation */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.response-container {
  animation: fadeIn 0.3s ease-in;
}

/* Hover effects for buttons */
button {
  transition: all 0.2s ease;
}

/* Dark mode adjustments */
:host-context(.dark) .response-container {
  color: #e0e0e0;
}

:host-context(.dark) code {
  background-color: #2a2a2a;
  color: #e0e0e0;
}

:host-context(.dark) pre {
  background-color: #1e1e1e;
  border: 1px solid #333;
}

/* Enhanced User Dropdown Styling */
select.user-select {
  transition: all 0.2s ease;
}

select.user-select:focus {
  transform: scale(1.02);
}

.user-dropdown-wrapper {
  position: relative;
}

.user-dropdown-wrapper:before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: var(--border-radius-small);
  background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.user-dropdown-wrapper:hover:before {
  opacity: 0.3;
}

/* Pulsing animation for selected user */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--primary-purple-rgb), 0.7);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(var(--primary-purple-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary-purple-rgb), 0);
  }
}

.selected-user-badge {
  animation: pulse 2s infinite;
}

/* Enhanced dropdown option styling */
select.user-select option {
  padding: 8px;
  background-color: var(--background-white);
  color: var(--text-dark);
}

select.user-select option:hover,
select.user-select option:focus {
  background-color: var(--hover-blue-gray);
  color: var(--primary-purple);
}

/* Shine effect on user dropdown */
.user-dropdown-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-25deg);
  transition: all 0.75s;
}

.user-dropdown-wrapper:hover::after {
  left: 100%;
}

/* User count badge animation */
@keyframes scaleIn {
  0% { transform: scale(0); opacity: 0; }
  70% { transform: scale(1.1); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

.user-count-badge {
  animation: scaleIn 0.3s ease-out forwards;
}
