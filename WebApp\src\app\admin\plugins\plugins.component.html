<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 65px);">
  <!-- Main Container -->
  <div class="flex-1 overflow-hidden flex flex-col">
    <!-- Header - Teams-style with compact vertical spacing -->
    <div class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50">
      <!-- Left side with title and count -->
      <div class="flex items-center gap-2">
        <i class="ri-plug-2-line text-[var(--primary-purple)] text-xl"></i>
        <h1 class="text-lg font-medium text-[var(--text-dark)]">Plugins</h1>
        <div class="inline-flex items-center justify-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]">
          {{ filteredPlugins.length }}
        </div>
      </div>

      <!-- Right side with controls -->
      <div class="flex items-center gap-2">
        <!-- Search Input - Teams-style -->
        <div class="relative w-full sm:w-56 flex items-center">
          <div class="absolute inset-y-0 left-0 flex items-center justify-center pl-2 pointer-events-none">
            <i class="ri-search-line text-[var(--text-medium-gray)] text-sm"></i>
          </div>
          <input
            type="text"
            placeholder="Search plugins..."
            [(ngModel)]="searchQuery"
            (ngModelChange)="filterPlugins()"
            class="w-full h-8 px-3 py-1 pl-8 text-sm text-[var(--text-dark)] border-none bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200"
          />
          <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-2" *ngIf="searchQuery">
            <button
              (click)="searchQuery = ''; filterPlugins()"
              class="text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] border-none bg-transparent transition-colors focus:outline-none"
            >
              <i class="ri-close-line text-sm"></i>
            </button>
          </div>
        </div>

        <!-- Action Buttons - Teams-style -->
        <div class="flex items-center gap-2">
          <button
            (click)="syncPlugins()"
            class="h-8 w-8 flex items-center justify-center bg-[var(--hover-blue-gray)] border-none text-[var(--text-dark)] rounded-md hover:bg-[var(--secondary-purple)] transition-all duration-200"
            [disabled]="loading"
            title="Sync All Plugins"
          >
            <i class="ri-refresh-line text-sm" [class.animate-spin]="loading"></i>
          </button>

          <button
            (click)="openCreateModal()"
            class="h-8 px-3 py-1 bg-[var(--primary-purple)] text-white text-sm border-none font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center justify-center gap-1"
          >
            <i class="ri-add-line"></i>
            <span>Add Plugin</span>
          </button>
        </div>
      </div>
    </div>

  <div class="flex-1 overflow-y-auto px-4 pt-4">
    <!-- Loading Spinner -->
    <div *ngIf="loading" class="relative min-h-[300px]">
      <app-spinner message="Loading plugins..." [overlay]="false"></app-spinner>
    </div>

    <div *ngIf="!loading" class="plugins-list space-y-4">
    @for (plugin of filteredPlugins; track plugin.id) {
      <div class="plugin-item bg-[var(--background-white)] rounded-[var(--border-radius-large)] shadow-[var(--shadow-default)] p-4 hover:shadow-[var(--shadow-hover)] transition-all duration-300 cursor-pointer relative group"
           (click)="navigateToDetails(plugin.pluginName!)">
        <div class="plugin-header flex items-center justify-between mb-3">
          <div class="flex items-center gap-2">
            <i class="ri-plug-2-line text-[var(--primary-purple)] text-xl"></i>
            <h3 class="text-base font-medium text-[var(--text-dark)] truncate max-w-[180px]">{{ plugin.pluginName }}</h3>
          </div>
          <div class="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
            @if (plugin.type?.toLowerCase() === 'openapi') {
              <button (click)="resyncOpenApiPlugin(plugin.pluginName!); $event.stopPropagation()"
                      class="flex items-center gap-1 px-2 py-1 text-xs bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-colors duration-300 font-[var(--font-weight-medium)] border-none"
                      [disabled]="loading">
                <i class="ri-refresh-line"></i>
                Resync
              </button>
            }
            <button (click)="openEditModal(plugin); $event.stopPropagation()"
                    class="flex items-center gap-1 px-2 py-1 text-xs bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-colors duration-300 font-[var(--font-weight-medium)] border-none"
                    [disabled]="loading">
              <i class="ri-edit-line"></i>
              Edit
            </button>
            <button (click)="deletePlugin(plugin); $event.stopPropagation()"
                    class="flex items-center gap-1 px-2 py-1 text-xs bg-red-100 text-red-600 dark:bg-red-800 dark:text-red-200 rounded-[var(--border-radius-small)] hover:bg-red-200 transition-colors duration-300 font-[var(--font-weight-medium)] border-none"
                    [disabled]="loading">
              <i class="ri-delete-bin-line"></i>
              Delete
            </button>
            <nz-tag [nzColor]="getPluginTypeColor(plugin.type)">{{ plugin.type }}</nz-tag>
          </div>
        </div>

        <div class="plugin-content">
          <div class="functions-section mb-3">
            <h4 class="text-sm font-[var(--font-weight-medium)] text-[var(--text-secondary)] mb-2">Available Functions</h4>
            <div class="functions-list space-y-1">
              @for (func of getFunctionList(plugin.functions); track func) {
                <div class="function-item text-sm text-[var(--text-dark)] bg-[var(--hover-blue-gray)] p-2 rounded-[var(--border-radius-small)] font-[var(--font-weight-regular)]">
                  {{ func }}
                </div>
              }
            </div>
          </div>

          @if (plugin.url) {
            <div class="url-section mb-3">
              <h4 class="text-sm font-[var(--font-weight-medium)] text-[var(--text-secondary)] mb-1">API URL</h4>
              <a [href]="plugin.url"
                 target="_blank"
                 class="text-sm text-[var(--primary-purple)] hover:underline break-all font-[var(--font-weight-regular)]"
                 (click)="$event.stopPropagation()">
                {{ plugin.url }}
              </a>
            </div>
          }

          <div class="dates-section text-xs text-[var(--text-secondary)] flex items-center gap-4 font-[var(--font-weight-regular)]">
            <div class="flex items-center gap-2">
              <i class="ri-time-line"></i>
              <span>Created: {{ formatDate(plugin.createdDate) }}</span>
            </div>
            @if (plugin.lastModifiedDate) {
              <div class="flex items-center gap-2">
                <i class="ri-edit-line"></i>
                <span>Modified: {{ formatDate(plugin.lastModifiedDate) }}</span>
              </div>
            }
          </div>
        </div>
      </div>
    }
  </div>

  @if (loading) {
    <div class="loading-container flex items-center justify-center min-h-[200px]">
      <i class="ri-loader-4-line text-[var(--primary-purple)] text-2xl animate-spin"></i>
    </div>
  }

  @if (!loading && filteredPlugins.length === 0 && plugins.length > 0) {
    <div class="empty-state text-center py-12 bg-[var(--background-white)] rounded-[var(--border-radius-large)] shadow-[var(--shadow-default)]">
      <i class="ri-search-line text-[var(--text-secondary)] text-4xl mb-4"></i>
      <p class="text-[var(--text-secondary)] font-[var(--font-weight-medium)]">No plugins match your search</p>
      <button (click)="searchQuery = ''; filterPlugins()" class="mt-4 px-4 py-2 bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] rounded-[var(--border-radius-small)] text-[var(--text-dark)] font-[var(--font-weight-medium)] transition-colors duration-300">
        Clear Search
      </button>
    </div>
  }

  @if (!loading && plugins.length === 0) {
    <div class="empty-state text-center py-12 bg-[var(--background-white)] rounded-[var(--border-radius-large)] shadow-[var(--shadow-default)]">
      <i class="ri-plug-2-line text-[var(--text-secondary)] text-4xl mb-4"></i>
      <p class="text-[var(--text-secondary)] font-[var(--font-weight-medium)]">No plugins available</p>
    </div>
  }
    </div>
  </div>
</div>
