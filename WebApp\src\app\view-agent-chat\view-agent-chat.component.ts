import { Component } from '@angular/core';
import { Router, RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-view-agent-chat',
  standalone: true,
  imports: [RouterOutlet, CommonModule, RouterLink, RouterLinkActive],
  templateUrl: './view-agent-chat.component.html',
  styleUrl: './view-agent-chat.component.css'
})
export class ViewAgentChatComponent {
  isAdmin = true;
  isWorkspaceChat = true;
  isAgentChat = false;
  type = 'workspace';
  workspaceName: string = '';
  isNotWorkspaceChat = false;

  constructor(private router: Router) { }

  ngOnInit(): void {
    // Subscribe to router events to update chat type based on URL
    this.router.events.subscribe(() => {
      const url = this.router.url;
      this.isWorkspaceChat = url.includes('workspace');
      this.isAgentChat = url.includes('agent');
      this.isNotWorkspaceChat = url.includes('settings/workspace');

      // Extract workspace name from URL if present
      const match = url.match(/chat\/workspace\/([^\/]+)/);
      if (match) {
        this.workspaceName = decodeURIComponent(match[1]);
        this.isWorkspaceChat = true;
        // You can use workspaceName as needed
        // console.log('Workspace Name:', this.workspaceName);
      }

    });

    // Initialize values on component load

  }
}
