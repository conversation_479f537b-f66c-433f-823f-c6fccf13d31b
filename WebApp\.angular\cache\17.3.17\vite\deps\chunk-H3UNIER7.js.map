{"version": 3, "sources": ["../../../../../node_modules/@angular/common/fesm2022/common.mjs"], "sourcesContent": ["/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, inject, Optional, Inject, EventEmitter, ɵɵinject, ɵfindLocaleData, ɵLocaleDataIndex, ɵgetLocaleCurrencyCode, ɵgetLocalePluralCase, LOCALE_ID, ɵregisterLocaleData, ɵstringify, Directive, Input, createNgModule, NgModuleRef, ɵRuntimeError, ɵformatRuntimeError, Host, Attribute, RendererStyleFlags2, untracked, ɵisPromise, ɵisSubscribable, <PERSON><PERSON>, DEFAULT_CURRENCY_CODE, NgModule, Version, ɵɵdefineInjectable, PLATFORM_ID, ɵIMAGE_CONFIG, Renderer2, ElementRef, Injector, ɵperformanceMarkFeature, NgZone, ChangeDetectorRef, numberAttribute, booleanAttribute, ɵIMAGE_CONFIG_DEFAULTS, ɵunwrapSafeValue } from '@angular/core';\nexport { ɵIMAGE_CONFIG as IMAGE_CONFIG } from '@angular/core';\nlet _DOM = null;\nfunction getDOM() {\n  return _DOM;\n}\nfunction setRootDomAdapter(adapter) {\n  _DOM ??= adapter;\n}\n/* tslint:disable:requireParameterType */\n/**\n * Provides DOM operations in an environment-agnostic way.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass DomAdapter {}\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\nclass PlatformNavigation {\n  static {\n    this.ɵfac = function PlatformNavigation_Factory(t) {\n      return new (t || PlatformNavigation)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PlatformNavigation,\n      factory: () => (() => window.navigation)(),\n      providedIn: 'platform'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformNavigation, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform',\n      useFactory: () => window.navigation\n    }]\n  }], null, null);\n})();\n\n/**\n * A DI Token representing the main rendering context.\n * In a browser and SSR this is the DOM Document.\n * When using SSR, that document is created by [Domino](https://github.com/angular/domino).\n *\n * @publicApi\n */\nconst DOCUMENT = new InjectionToken(ngDevMode ? 'DocumentToken' : '');\n\n/**\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * `PlatformLocation` encapsulates all calls to DOM APIs, which allows the Router to be\n * platform-agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that Angular supports. For example, `@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `@angular/platform-server` provides\n * one suitable for use with server-side rendering.\n *\n * The `PlatformLocation` class is used directly by all implementations of {@link LocationStrategy}\n * when they need to interact with the DOM APIs like pushState, popState, etc.\n *\n * {@link LocationStrategy} in turn is used by the {@link Location} service which is used directly\n * by the {@link Router} in order to navigate between routes. Since all interactions between {@link\n * Router} /\n * {@link Location} / {@link LocationStrategy} and DOM APIs flow through the `PlatformLocation`\n * class, they are all platform-agnostic.\n *\n * @publicApi\n */\nclass PlatformLocation {\n  historyGo(relativePosition) {\n    throw new Error(ngDevMode ? 'Not implemented' : '');\n  }\n  static {\n    this.ɵfac = function PlatformLocation_Factory(t) {\n      return new (t || PlatformLocation)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PlatformLocation,\n      factory: () => (() => inject(BrowserPlatformLocation))(),\n      providedIn: 'platform'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformLocation, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform',\n      useFactory: () => inject(BrowserPlatformLocation)\n    }]\n  }], null, null);\n})();\n/**\n * @description\n * Indicates when a location is initialized.\n *\n * @publicApi\n */\nconst LOCATION_INITIALIZED = new InjectionToken(ngDevMode ? 'Location Initialized' : '');\n/**\n * `PlatformLocation` encapsulates all of the direct calls to platform APIs.\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * @publicApi\n */\nclass BrowserPlatformLocation extends PlatformLocation {\n  constructor() {\n    super();\n    this._doc = inject(DOCUMENT);\n    this._location = window.location;\n    this._history = window.history;\n  }\n  getBaseHrefFromDOM() {\n    return getDOM().getBaseHref(this._doc);\n  }\n  onPopState(fn) {\n    const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n    window.addEventListener('popstate', fn, false);\n    return () => window.removeEventListener('popstate', fn);\n  }\n  onHashChange(fn) {\n    const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n    window.addEventListener('hashchange', fn, false);\n    return () => window.removeEventListener('hashchange', fn);\n  }\n  get href() {\n    return this._location.href;\n  }\n  get protocol() {\n    return this._location.protocol;\n  }\n  get hostname() {\n    return this._location.hostname;\n  }\n  get port() {\n    return this._location.port;\n  }\n  get pathname() {\n    return this._location.pathname;\n  }\n  get search() {\n    return this._location.search;\n  }\n  get hash() {\n    return this._location.hash;\n  }\n  set pathname(newPath) {\n    this._location.pathname = newPath;\n  }\n  pushState(state, title, url) {\n    this._history.pushState(state, title, url);\n  }\n  replaceState(state, title, url) {\n    this._history.replaceState(state, title, url);\n  }\n  forward() {\n    this._history.forward();\n  }\n  back() {\n    this._history.back();\n  }\n  historyGo(relativePosition = 0) {\n    this._history.go(relativePosition);\n  }\n  getState() {\n    return this._history.state;\n  }\n  static {\n    this.ɵfac = function BrowserPlatformLocation_Factory(t) {\n      return new (t || BrowserPlatformLocation)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BrowserPlatformLocation,\n      factory: () => (() => new BrowserPlatformLocation())(),\n      providedIn: 'platform'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserPlatformLocation, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform',\n      useFactory: () => new BrowserPlatformLocation()\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Joins two parts of a URL with a slash if needed.\n *\n * @param start  URL string\n * @param end    URL string\n *\n *\n * @returns The joined URL string.\n */\nfunction joinWithSlash(start, end) {\n  if (start.length == 0) {\n    return end;\n  }\n  if (end.length == 0) {\n    return start;\n  }\n  let slashes = 0;\n  if (start.endsWith('/')) {\n    slashes++;\n  }\n  if (end.startsWith('/')) {\n    slashes++;\n  }\n  if (slashes == 2) {\n    return start + end.substring(1);\n  }\n  if (slashes == 1) {\n    return start + end;\n  }\n  return start + '/' + end;\n}\n/**\n * Removes a trailing slash from a URL string if needed.\n * Looks for the first occurrence of either `#`, `?`, or the end of the\n * line as `/` characters and removes the trailing slash if one exists.\n *\n * @param url URL string.\n *\n * @returns The URL string, modified if needed.\n */\nfunction stripTrailingSlash(url) {\n  const match = url.match(/#|\\?|$/);\n  const pathEndIdx = match && match.index || url.length;\n  const droppedSlashIdx = pathEndIdx - (url[pathEndIdx - 1] === '/' ? 1 : 0);\n  return url.slice(0, droppedSlashIdx) + url.slice(pathEndIdx);\n}\n/**\n * Normalizes URL parameters by prepending with `?` if needed.\n *\n * @param  params String of URL parameters.\n *\n * @returns The normalized URL parameters string.\n */\nfunction normalizeQueryParams(params) {\n  return params && params[0] !== '?' ? '?' + params : params;\n}\n\n/**\n * Enables the `Location` service to read route state from the browser's URL.\n * Angular provides two strategies:\n * `HashLocationStrategy` and `PathLocationStrategy`.\n *\n * Applications should use the `Router` or `Location` services to\n * interact with application route state.\n *\n * For instance, `HashLocationStrategy` produces URLs like\n * <code class=\"no-auto-link\">http://example.com#/foo</code>,\n * and `PathLocationStrategy` produces\n * <code class=\"no-auto-link\">http://example.com/foo</code> as an equivalent URL.\n *\n * See these two classes for more.\n *\n * @publicApi\n */\nclass LocationStrategy {\n  historyGo(relativePosition) {\n    throw new Error(ngDevMode ? 'Not implemented' : '');\n  }\n  static {\n    this.ɵfac = function LocationStrategy_Factory(t) {\n      return new (t || LocationStrategy)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LocationStrategy,\n      factory: () => (() => inject(PathLocationStrategy))(),\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LocationStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => inject(PathLocationStrategy)\n    }]\n  }], null, null);\n})();\n/**\n * A predefined [DI token](guide/glossary#di-token) for the base href\n * to be used with the `PathLocationStrategy`.\n * The base href is the URL prefix that should be preserved when generating\n * and recognizing URLs.\n *\n * @usageNotes\n *\n * The following example shows how to use this token to configure the root app injector\n * with a base href value, so that the DI framework can supply the dependency anywhere in the app.\n *\n * ```typescript\n * import {NgModule} from '@angular/core';\n * import {APP_BASE_HREF} from '@angular/common';\n *\n * @NgModule({\n *   providers: [{provide: APP_BASE_HREF, useValue: '/my/app'}]\n * })\n * class AppModule {}\n * ```\n *\n * @publicApi\n */\nconst APP_BASE_HREF = new InjectionToken(ngDevMode ? 'appBaseHref' : '');\n/**\n * @description\n * A {@link LocationStrategy} used to configure the {@link Location} service to\n * represent its state in the\n * [path](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax) of the\n * browser's URL.\n *\n * If you're using `PathLocationStrategy`, you may provide a {@link APP_BASE_HREF}\n * or add a `<base href>` element to the document to override the default.\n *\n * For instance, if you provide an `APP_BASE_HREF` of `'/my/app/'` and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`. To ensure all relative URIs resolve correctly,\n * the `<base href>` and/or `APP_BASE_HREF` should end with a `/`.\n *\n * Similarly, if you add `<base href='/my/app/'/>` to the document and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n *\n * Note that when using `PathLocationStrategy`, neither the query nor\n * the fragment in the `<base href>` will be preserved, as outlined\n * by the [RFC](https://tools.ietf.org/html/rfc3986#section-5.2.2).\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\nclass PathLocationStrategy extends LocationStrategy {\n  constructor(_platformLocation, href) {\n    super();\n    this._platformLocation = _platformLocation;\n    this._removeListenerFns = [];\n    this._baseHref = href ?? this._platformLocation.getBaseHrefFromDOM() ?? inject(DOCUMENT).location?.origin ?? '';\n  }\n  /** @nodoc */\n  ngOnDestroy() {\n    while (this._removeListenerFns.length) {\n      this._removeListenerFns.pop()();\n    }\n  }\n  onPopState(fn) {\n    this._removeListenerFns.push(this._platformLocation.onPopState(fn), this._platformLocation.onHashChange(fn));\n  }\n  getBaseHref() {\n    return this._baseHref;\n  }\n  prepareExternalUrl(internal) {\n    return joinWithSlash(this._baseHref, internal);\n  }\n  path(includeHash = false) {\n    const pathname = this._platformLocation.pathname + normalizeQueryParams(this._platformLocation.search);\n    const hash = this._platformLocation.hash;\n    return hash && includeHash ? `${pathname}${hash}` : pathname;\n  }\n  pushState(state, title, url, queryParams) {\n    const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n    this._platformLocation.pushState(state, title, externalUrl);\n  }\n  replaceState(state, title, url, queryParams) {\n    const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n    this._platformLocation.replaceState(state, title, externalUrl);\n  }\n  forward() {\n    this._platformLocation.forward();\n  }\n  back() {\n    this._platformLocation.back();\n  }\n  getState() {\n    return this._platformLocation.getState();\n  }\n  historyGo(relativePosition = 0) {\n    this._platformLocation.historyGo?.(relativePosition);\n  }\n  static {\n    this.ɵfac = function PathLocationStrategy_Factory(t) {\n      return new (t || PathLocationStrategy)(i0.ɵɵinject(PlatformLocation), i0.ɵɵinject(APP_BASE_HREF, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PathLocationStrategy,\n      factory: PathLocationStrategy.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PathLocationStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: PlatformLocation\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [APP_BASE_HREF]\n    }]\n  }], null);\n})();\n\n/**\n * @description\n * A {@link LocationStrategy} used to configure the {@link Location} service to\n * represent its state in the\n * [hash fragment](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax)\n * of the browser's URL.\n *\n * For instance, if you call `location.go('/foo')`, the browser's URL will become\n * `example.com#/foo`.\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/location/ts/hash_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\nclass HashLocationStrategy extends LocationStrategy {\n  constructor(_platformLocation, _baseHref) {\n    super();\n    this._platformLocation = _platformLocation;\n    this._baseHref = '';\n    this._removeListenerFns = [];\n    if (_baseHref != null) {\n      this._baseHref = _baseHref;\n    }\n  }\n  /** @nodoc */\n  ngOnDestroy() {\n    while (this._removeListenerFns.length) {\n      this._removeListenerFns.pop()();\n    }\n  }\n  onPopState(fn) {\n    this._removeListenerFns.push(this._platformLocation.onPopState(fn), this._platformLocation.onHashChange(fn));\n  }\n  getBaseHref() {\n    return this._baseHref;\n  }\n  path(includeHash = false) {\n    // the hash value is always prefixed with a `#`\n    // and if it is empty then it will stay empty\n    const path = this._platformLocation.hash ?? '#';\n    return path.length > 0 ? path.substring(1) : path;\n  }\n  prepareExternalUrl(internal) {\n    const url = joinWithSlash(this._baseHref, internal);\n    return url.length > 0 ? '#' + url : url;\n  }\n  pushState(state, title, path, queryParams) {\n    let url = this.prepareExternalUrl(path + normalizeQueryParams(queryParams));\n    if (url.length == 0) {\n      url = this._platformLocation.pathname;\n    }\n    this._platformLocation.pushState(state, title, url);\n  }\n  replaceState(state, title, path, queryParams) {\n    let url = this.prepareExternalUrl(path + normalizeQueryParams(queryParams));\n    if (url.length == 0) {\n      url = this._platformLocation.pathname;\n    }\n    this._platformLocation.replaceState(state, title, url);\n  }\n  forward() {\n    this._platformLocation.forward();\n  }\n  back() {\n    this._platformLocation.back();\n  }\n  getState() {\n    return this._platformLocation.getState();\n  }\n  historyGo(relativePosition = 0) {\n    this._platformLocation.historyGo?.(relativePosition);\n  }\n  static {\n    this.ɵfac = function HashLocationStrategy_Factory(t) {\n      return new (t || HashLocationStrategy)(i0.ɵɵinject(PlatformLocation), i0.ɵɵinject(APP_BASE_HREF, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HashLocationStrategy,\n      factory: HashLocationStrategy.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HashLocationStrategy, [{\n    type: Injectable\n  }], () => [{\n    type: PlatformLocation\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [APP_BASE_HREF]\n    }]\n  }], null);\n})();\n\n/**\n * @description\n *\n * A service that applications can use to interact with a browser's URL.\n *\n * Depending on the `LocationStrategy` used, `Location` persists\n * to the URL's path or the URL's hash segment.\n *\n * @usageNotes\n *\n * It's better to use the `Router.navigate()` service to trigger route changes. Use\n * `Location` only if you need to interact with or create normalized URLs outside of\n * routing.\n *\n * `Location` is responsible for normalizing the URL against the application's base href.\n * A normalized URL is absolute from the URL host, includes the application's base href, and has no\n * trailing slash:\n * - `/my/app/user/123` is normalized\n * - `my/app/user/123` **is not** normalized\n * - `/my/app/user/123/` **is not** normalized\n *\n * ### Example\n *\n * <code-example path='common/location/ts/path_location_component.ts'\n * region='LocationComponent'></code-example>\n *\n * @publicApi\n */\nclass Location {\n  constructor(locationStrategy) {\n    /** @internal */\n    this._subject = new EventEmitter();\n    /** @internal */\n    this._urlChangeListeners = [];\n    /** @internal */\n    this._urlChangeSubscription = null;\n    this._locationStrategy = locationStrategy;\n    const baseHref = this._locationStrategy.getBaseHref();\n    // Note: This class's interaction with base HREF does not fully follow the rules\n    // outlined in the spec https://www.freesoft.org/CIE/RFC/1808/18.htm.\n    // Instead of trying to fix individual bugs with more and more code, we should\n    // investigate using the URL constructor and providing the base as a second\n    // argument.\n    // https://developer.mozilla.org/en-US/docs/Web/API/URL/URL#parameters\n    this._basePath = _stripOrigin(stripTrailingSlash(_stripIndexHtml(baseHref)));\n    this._locationStrategy.onPopState(ev => {\n      this._subject.emit({\n        'url': this.path(true),\n        'pop': true,\n        'state': ev.state,\n        'type': ev.type\n      });\n    });\n  }\n  /** @nodoc */\n  ngOnDestroy() {\n    this._urlChangeSubscription?.unsubscribe();\n    this._urlChangeListeners = [];\n  }\n  /**\n   * Normalizes the URL path for this location.\n   *\n   * @param includeHash True to include an anchor fragment in the path.\n   *\n   * @returns The normalized URL path.\n   */\n  // TODO: vsavkin. Remove the boolean flag and always include hash once the deprecated router is\n  // removed.\n  path(includeHash = false) {\n    return this.normalize(this._locationStrategy.path(includeHash));\n  }\n  /**\n   * Reports the current state of the location history.\n   * @returns The current value of the `history.state` object.\n   */\n  getState() {\n    return this._locationStrategy.getState();\n  }\n  /**\n   * Normalizes the given path and compares to the current normalized path.\n   *\n   * @param path The given URL path.\n   * @param query Query parameters.\n   *\n   * @returns True if the given URL path is equal to the current normalized path, false\n   * otherwise.\n   */\n  isCurrentPathEqualTo(path, query = '') {\n    return this.path() == this.normalize(path + normalizeQueryParams(query));\n  }\n  /**\n   * Normalizes a URL path by stripping any trailing slashes.\n   *\n   * @param url String representing a URL.\n   *\n   * @returns The normalized URL string.\n   */\n  normalize(url) {\n    return Location.stripTrailingSlash(_stripBasePath(this._basePath, _stripIndexHtml(url)));\n  }\n  /**\n   * Normalizes an external URL path.\n   * If the given URL doesn't begin with a leading slash (`'/'`), adds one\n   * before normalizing. Adds a hash if `HashLocationStrategy` is\n   * in use, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n   *\n   * @param url String representing a URL.\n   *\n   * @returns  A normalized platform-specific URL.\n   */\n  prepareExternalUrl(url) {\n    if (url && url[0] !== '/') {\n      url = '/' + url;\n    }\n    return this._locationStrategy.prepareExternalUrl(url);\n  }\n  // TODO: rename this method to pushState\n  /**\n   * Changes the browser's URL to a normalized version of a given URL, and pushes a\n   * new item onto the platform's history.\n   *\n   * @param path  URL path to normalize.\n   * @param query Query parameters.\n   * @param state Location history state.\n   *\n   */\n  go(path, query = '', state = null) {\n    this._locationStrategy.pushState(state, '', path, query);\n    this._notifyUrlChangeListeners(this.prepareExternalUrl(path + normalizeQueryParams(query)), state);\n  }\n  /**\n   * Changes the browser's URL to a normalized version of the given URL, and replaces\n   * the top item on the platform's history stack.\n   *\n   * @param path  URL path to normalize.\n   * @param query Query parameters.\n   * @param state Location history state.\n   */\n  replaceState(path, query = '', state = null) {\n    this._locationStrategy.replaceState(state, '', path, query);\n    this._notifyUrlChangeListeners(this.prepareExternalUrl(path + normalizeQueryParams(query)), state);\n  }\n  /**\n   * Navigates forward in the platform's history.\n   */\n  forward() {\n    this._locationStrategy.forward();\n  }\n  /**\n   * Navigates back in the platform's history.\n   */\n  back() {\n    this._locationStrategy.back();\n  }\n  /**\n   * Navigate to a specific page from session history, identified by its relative position to the\n   * current page.\n   *\n   * @param relativePosition  Position of the target page in the history relative to the current\n   *     page.\n   * A negative value moves backwards, a positive value moves forwards, e.g. `location.historyGo(2)`\n   * moves forward two pages and `location.historyGo(-2)` moves back two pages. When we try to go\n   * beyond what's stored in the history session, we stay in the current page. Same behaviour occurs\n   * when `relativePosition` equals 0.\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/History_API#Moving_to_a_specific_point_in_history\n   */\n  historyGo(relativePosition = 0) {\n    this._locationStrategy.historyGo?.(relativePosition);\n  }\n  /**\n   * Registers a URL change listener. Use to catch updates performed by the Angular\n   * framework that are not detectible through \"popstate\" or \"hashchange\" events.\n   *\n   * @param fn The change handler function, which take a URL and a location history state.\n   * @returns A function that, when executed, unregisters a URL change listener.\n   */\n  onUrlChange(fn) {\n    this._urlChangeListeners.push(fn);\n    this._urlChangeSubscription ??= this.subscribe(v => {\n      this._notifyUrlChangeListeners(v.url, v.state);\n    });\n    return () => {\n      const fnIndex = this._urlChangeListeners.indexOf(fn);\n      this._urlChangeListeners.splice(fnIndex, 1);\n      if (this._urlChangeListeners.length === 0) {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeSubscription = null;\n      }\n    };\n  }\n  /** @internal */\n  _notifyUrlChangeListeners(url = '', state) {\n    this._urlChangeListeners.forEach(fn => fn(url, state));\n  }\n  /**\n   * Subscribes to the platform's `popState` events.\n   *\n   * Note: `Location.go()` does not trigger the `popState` event in the browser. Use\n   * `Location.onUrlChange()` to subscribe to URL changes instead.\n   *\n   * @param value Event that is triggered when the state history changes.\n   * @param exception The exception to throw.\n   *\n   * @see [onpopstate](https://developer.mozilla.org/en-US/docs/Web/API/WindowEventHandlers/onpopstate)\n   *\n   * @returns Subscribed events.\n   */\n  subscribe(onNext, onThrow, onReturn) {\n    return this._subject.subscribe({\n      next: onNext,\n      error: onThrow,\n      complete: onReturn\n    });\n  }\n  /**\n   * Normalizes URL parameters by prepending with `?` if needed.\n   *\n   * @param  params String of URL parameters.\n   *\n   * @returns The normalized URL parameters string.\n   */\n  static {\n    this.normalizeQueryParams = normalizeQueryParams;\n  }\n  /**\n   * Joins two parts of a URL with a slash if needed.\n   *\n   * @param start  URL string\n   * @param end    URL string\n   *\n   *\n   * @returns The joined URL string.\n   */\n  static {\n    this.joinWithSlash = joinWithSlash;\n  }\n  /**\n   * Removes a trailing slash from a URL string if needed.\n   * Looks for the first occurrence of either `#`, `?`, or the end of the\n   * line as `/` characters and removes the trailing slash if one exists.\n   *\n   * @param url URL string.\n   *\n   * @returns The URL string, modified if needed.\n   */\n  static {\n    this.stripTrailingSlash = stripTrailingSlash;\n  }\n  static {\n    this.ɵfac = function Location_Factory(t) {\n      return new (t || Location)(i0.ɵɵinject(LocationStrategy));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Location,\n      factory: () => createLocation(),\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Location, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      // See #23917\n      useFactory: createLocation\n    }]\n  }], () => [{\n    type: LocationStrategy\n  }], null);\n})();\nfunction createLocation() {\n  return new Location(ɵɵinject(LocationStrategy));\n}\nfunction _stripBasePath(basePath, url) {\n  if (!basePath || !url.startsWith(basePath)) {\n    return url;\n  }\n  const strippedUrl = url.substring(basePath.length);\n  if (strippedUrl === '' || ['/', ';', '?', '#'].includes(strippedUrl[0])) {\n    return strippedUrl;\n  }\n  return url;\n}\nfunction _stripIndexHtml(url) {\n  return url.replace(/\\/index.html$/, '');\n}\nfunction _stripOrigin(baseHref) {\n  // DO NOT REFACTOR! Previously, this check looked like this:\n  // `/^(https?:)?\\/\\//.test(baseHref)`, but that resulted in\n  // syntactically incorrect code after Closure Compiler minification.\n  // This was likely caused by a bug in Closure Compiler, but\n  // for now, the check is rewritten to use `new RegExp` instead.\n  const isAbsoluteUrl = new RegExp('^(https?:)?//').test(baseHref);\n  if (isAbsoluteUrl) {\n    const [, pathname] = baseHref.split(/\\/\\/[^\\/]+/);\n    return pathname;\n  }\n  return baseHref;\n}\n\n/** @internal */\nconst CURRENCIES_EN = {\n  \"ADP\": [undefined, undefined, 0],\n  \"AFN\": [undefined, \"؋\", 0],\n  \"ALL\": [undefined, undefined, 0],\n  \"AMD\": [undefined, \"֏\", 2],\n  \"AOA\": [undefined, \"Kz\"],\n  \"ARS\": [undefined, \"$\"],\n  \"AUD\": [\"A$\", \"$\"],\n  \"AZN\": [undefined, \"₼\"],\n  \"BAM\": [undefined, \"KM\"],\n  \"BBD\": [undefined, \"$\"],\n  \"BDT\": [undefined, \"৳\"],\n  \"BHD\": [undefined, undefined, 3],\n  \"BIF\": [undefined, undefined, 0],\n  \"BMD\": [undefined, \"$\"],\n  \"BND\": [undefined, \"$\"],\n  \"BOB\": [undefined, \"Bs\"],\n  \"BRL\": [\"R$\"],\n  \"BSD\": [undefined, \"$\"],\n  \"BWP\": [undefined, \"P\"],\n  \"BYN\": [undefined, undefined, 2],\n  \"BYR\": [undefined, undefined, 0],\n  \"BZD\": [undefined, \"$\"],\n  \"CAD\": [\"CA$\", \"$\", 2],\n  \"CHF\": [undefined, undefined, 2],\n  \"CLF\": [undefined, undefined, 4],\n  \"CLP\": [undefined, \"$\", 0],\n  \"CNY\": [\"CN¥\", \"¥\"],\n  \"COP\": [undefined, \"$\", 2],\n  \"CRC\": [undefined, \"₡\", 2],\n  \"CUC\": [undefined, \"$\"],\n  \"CUP\": [undefined, \"$\"],\n  \"CZK\": [undefined, \"Kč\", 2],\n  \"DJF\": [undefined, undefined, 0],\n  \"DKK\": [undefined, \"kr\", 2],\n  \"DOP\": [undefined, \"$\"],\n  \"EGP\": [undefined, \"E£\"],\n  \"ESP\": [undefined, \"₧\", 0],\n  \"EUR\": [\"€\"],\n  \"FJD\": [undefined, \"$\"],\n  \"FKP\": [undefined, \"£\"],\n  \"GBP\": [\"£\"],\n  \"GEL\": [undefined, \"₾\"],\n  \"GHS\": [undefined, \"GH₵\"],\n  \"GIP\": [undefined, \"£\"],\n  \"GNF\": [undefined, \"FG\", 0],\n  \"GTQ\": [undefined, \"Q\"],\n  \"GYD\": [undefined, \"$\", 2],\n  \"HKD\": [\"HK$\", \"$\"],\n  \"HNL\": [undefined, \"L\"],\n  \"HRK\": [undefined, \"kn\"],\n  \"HUF\": [undefined, \"Ft\", 2],\n  \"IDR\": [undefined, \"Rp\", 2],\n  \"ILS\": [\"₪\"],\n  \"INR\": [\"₹\"],\n  \"IQD\": [undefined, undefined, 0],\n  \"IRR\": [undefined, undefined, 0],\n  \"ISK\": [undefined, \"kr\", 0],\n  \"ITL\": [undefined, undefined, 0],\n  \"JMD\": [undefined, \"$\"],\n  \"JOD\": [undefined, undefined, 3],\n  \"JPY\": [\"¥\", undefined, 0],\n  \"KHR\": [undefined, \"៛\"],\n  \"KMF\": [undefined, \"CF\", 0],\n  \"KPW\": [undefined, \"₩\", 0],\n  \"KRW\": [\"₩\", undefined, 0],\n  \"KWD\": [undefined, undefined, 3],\n  \"KYD\": [undefined, \"$\"],\n  \"KZT\": [undefined, \"₸\"],\n  \"LAK\": [undefined, \"₭\", 0],\n  \"LBP\": [undefined, \"L£\", 0],\n  \"LKR\": [undefined, \"Rs\"],\n  \"LRD\": [undefined, \"$\"],\n  \"LTL\": [undefined, \"Lt\"],\n  \"LUF\": [undefined, undefined, 0],\n  \"LVL\": [undefined, \"Ls\"],\n  \"LYD\": [undefined, undefined, 3],\n  \"MGA\": [undefined, \"Ar\", 0],\n  \"MGF\": [undefined, undefined, 0],\n  \"MMK\": [undefined, \"K\", 0],\n  \"MNT\": [undefined, \"₮\", 2],\n  \"MRO\": [undefined, undefined, 0],\n  \"MUR\": [undefined, \"Rs\", 2],\n  \"MXN\": [\"MX$\", \"$\"],\n  \"MYR\": [undefined, \"RM\"],\n  \"NAD\": [undefined, \"$\"],\n  \"NGN\": [undefined, \"₦\"],\n  \"NIO\": [undefined, \"C$\"],\n  \"NOK\": [undefined, \"kr\", 2],\n  \"NPR\": [undefined, \"Rs\"],\n  \"NZD\": [\"NZ$\", \"$\"],\n  \"OMR\": [undefined, undefined, 3],\n  \"PHP\": [\"₱\"],\n  \"PKR\": [undefined, \"Rs\", 2],\n  \"PLN\": [undefined, \"zł\"],\n  \"PYG\": [undefined, \"₲\", 0],\n  \"RON\": [undefined, \"lei\"],\n  \"RSD\": [undefined, undefined, 0],\n  \"RUB\": [undefined, \"₽\"],\n  \"RWF\": [undefined, \"RF\", 0],\n  \"SBD\": [undefined, \"$\"],\n  \"SEK\": [undefined, \"kr\", 2],\n  \"SGD\": [undefined, \"$\"],\n  \"SHP\": [undefined, \"£\"],\n  \"SLE\": [undefined, undefined, 2],\n  \"SLL\": [undefined, undefined, 0],\n  \"SOS\": [undefined, undefined, 0],\n  \"SRD\": [undefined, \"$\"],\n  \"SSP\": [undefined, \"£\"],\n  \"STD\": [undefined, undefined, 0],\n  \"STN\": [undefined, \"Db\"],\n  \"SYP\": [undefined, \"£\", 0],\n  \"THB\": [undefined, \"฿\"],\n  \"TMM\": [undefined, undefined, 0],\n  \"TND\": [undefined, undefined, 3],\n  \"TOP\": [undefined, \"T$\"],\n  \"TRL\": [undefined, undefined, 0],\n  \"TRY\": [undefined, \"₺\"],\n  \"TTD\": [undefined, \"$\"],\n  \"TWD\": [\"NT$\", \"$\", 2],\n  \"TZS\": [undefined, undefined, 2],\n  \"UAH\": [undefined, \"₴\"],\n  \"UGX\": [undefined, undefined, 0],\n  \"USD\": [\"$\"],\n  \"UYI\": [undefined, undefined, 0],\n  \"UYU\": [undefined, \"$\"],\n  \"UYW\": [undefined, undefined, 4],\n  \"UZS\": [undefined, undefined, 2],\n  \"VEF\": [undefined, \"Bs\", 2],\n  \"VND\": [\"₫\", undefined, 0],\n  \"VUV\": [undefined, undefined, 0],\n  \"XAF\": [\"FCFA\", undefined, 0],\n  \"XCD\": [\"EC$\", \"$\"],\n  \"XOF\": [\"F CFA\", undefined, 0],\n  \"XPF\": [\"CFPF\", undefined, 0],\n  \"XXX\": [\"¤\"],\n  \"YER\": [undefined, undefined, 0],\n  \"ZAR\": [undefined, \"R\"],\n  \"ZMK\": [undefined, undefined, 0],\n  \"ZMW\": [undefined, \"ZK\"],\n  \"ZWD\": [undefined, undefined, 0]\n};\n\n/**\n * Format styles that can be used to represent numbers.\n * @see {@link getLocaleNumberFormat}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nvar NumberFormatStyle;\n(function (NumberFormatStyle) {\n  NumberFormatStyle[NumberFormatStyle[\"Decimal\"] = 0] = \"Decimal\";\n  NumberFormatStyle[NumberFormatStyle[\"Percent\"] = 1] = \"Percent\";\n  NumberFormatStyle[NumberFormatStyle[\"Currency\"] = 2] = \"Currency\";\n  NumberFormatStyle[NumberFormatStyle[\"Scientific\"] = 3] = \"Scientific\";\n})(NumberFormatStyle || (NumberFormatStyle = {}));\n/**\n * Plurality cases used for translating plurals to different languages.\n *\n * @see {@link NgPlural}\n * @see {@link NgPluralCase}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nvar Plural;\n(function (Plural) {\n  Plural[Plural[\"Zero\"] = 0] = \"Zero\";\n  Plural[Plural[\"One\"] = 1] = \"One\";\n  Plural[Plural[\"Two\"] = 2] = \"Two\";\n  Plural[Plural[\"Few\"] = 3] = \"Few\";\n  Plural[Plural[\"Many\"] = 4] = \"Many\";\n  Plural[Plural[\"Other\"] = 5] = \"Other\";\n})(Plural || (Plural = {}));\n/**\n * Context-dependant translation forms for strings.\n * Typically the standalone version is for the nominative form of the word,\n * and the format version is used for the genitive case.\n * @see [CLDR website](http://cldr.unicode.org/translation/date-time-1/date-time#TOC-Standalone-vs.-Format-Styles)\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nvar FormStyle;\n(function (FormStyle) {\n  FormStyle[FormStyle[\"Format\"] = 0] = \"Format\";\n  FormStyle[FormStyle[\"Standalone\"] = 1] = \"Standalone\";\n})(FormStyle || (FormStyle = {}));\n/**\n * String widths available for translations.\n * The specific character widths are locale-specific.\n * Examples are given for the word \"Sunday\" in English.\n *\n * @publicApi\n */\nvar TranslationWidth;\n(function (TranslationWidth) {\n  /** 1 character for `en-US`. For example: 'S' */\n  TranslationWidth[TranslationWidth[\"Narrow\"] = 0] = \"Narrow\";\n  /** 3 characters for `en-US`. For example: 'Sun' */\n  TranslationWidth[TranslationWidth[\"Abbreviated\"] = 1] = \"Abbreviated\";\n  /** Full length for `en-US`. For example: \"Sunday\" */\n  TranslationWidth[TranslationWidth[\"Wide\"] = 2] = \"Wide\";\n  /** 2 characters for `en-US`, For example: \"Su\" */\n  TranslationWidth[TranslationWidth[\"Short\"] = 3] = \"Short\";\n})(TranslationWidth || (TranslationWidth = {}));\n/**\n * String widths available for date-time formats.\n * The specific character widths are locale-specific.\n * Examples are given for `en-US`.\n *\n * @see {@link getLocaleDateFormat}\n * @see {@link getLocaleTimeFormat}\n * @see {@link getLocaleDateTimeFormat}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n * @publicApi\n */\nvar FormatWidth;\n(function (FormatWidth) {\n  /**\n   * For `en-US`, `'M/d/yy, h:mm a'`\n   * (Example: `6/15/15, 9:03 AM`)\n   */\n  FormatWidth[FormatWidth[\"Short\"] = 0] = \"Short\";\n  /**\n   * For `en-US`, `'MMM d, y, h:mm:ss a'`\n   * (Example: `Jun 15, 2015, 9:03:01 AM`)\n   */\n  FormatWidth[FormatWidth[\"Medium\"] = 1] = \"Medium\";\n  /**\n   * For `en-US`, `'MMMM d, y, h:mm:ss a z'`\n   * (Example: `June 15, 2015 at 9:03:01 AM GMT+1`)\n   */\n  FormatWidth[FormatWidth[\"Long\"] = 2] = \"Long\";\n  /**\n   * For `en-US`, `'EEEE, MMMM d, y, h:mm:ss a zzzz'`\n   * (Example: `Monday, June 15, 2015 at 9:03:01 AM GMT+01:00`)\n   */\n  FormatWidth[FormatWidth[\"Full\"] = 3] = \"Full\";\n})(FormatWidth || (FormatWidth = {}));\n// This needs to be an object literal, rather than an enum, because TypeScript 5.4+\n// doesn't allow numeric keys and we have `Infinity` and `NaN`.\n/**\n * Symbols that can be used to replace placeholders in number patterns.\n * Examples are based on `en-US` values.\n *\n * @see {@link getLocaleNumberSymbol}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n * @object-literal-as-enum\n */\nconst NumberSymbol = {\n  /**\n   * Decimal separator.\n   * For `en-US`, the dot character.\n   * Example: 2,345`.`67\n   */\n  Decimal: 0,\n  /**\n   * Grouping separator, typically for thousands.\n   * For `en-US`, the comma character.\n   * Example: 2`,`345.67\n   */\n  Group: 1,\n  /**\n   * List-item separator.\n   * Example: \"one, two, and three\"\n   */\n  List: 2,\n  /**\n   * Sign for percentage (out of 100).\n   * Example: 23.4%\n   */\n  PercentSign: 3,\n  /**\n   * Sign for positive numbers.\n   * Example: +23\n   */\n  PlusSign: 4,\n  /**\n   * Sign for negative numbers.\n   * Example: -23\n   */\n  MinusSign: 5,\n  /**\n   * Computer notation for exponential value (n times a power of 10).\n   * Example: 1.2E3\n   */\n  Exponential: 6,\n  /**\n   * Human-readable format of exponential.\n   * Example: 1.2x103\n   */\n  SuperscriptingExponent: 7,\n  /**\n   * Sign for permille (out of 1000).\n   * Example: 23.4‰\n   */\n  PerMille: 8,\n  /**\n   * Infinity, can be used with plus and minus.\n   * Example: ∞, +∞, -∞\n   */\n  Infinity: 9,\n  /**\n   * Not a number.\n   * Example: NaN\n   */\n  NaN: 10,\n  /**\n   * Symbol used between time units.\n   * Example: 10:52\n   */\n  TimeSeparator: 11,\n  /**\n   * Decimal separator for currency values (fallback to `Decimal`).\n   * Example: $2,345.67\n   */\n  CurrencyDecimal: 12,\n  /**\n   * Group separator for currency values (fallback to `Group`).\n   * Example: $2,345.67\n   */\n  CurrencyGroup: 13\n};\n/**\n * The value for each day of the week, based on the `en-US` locale\n *\n * @publicApi\n */\nvar WeekDay;\n(function (WeekDay) {\n  WeekDay[WeekDay[\"Sunday\"] = 0] = \"Sunday\";\n  WeekDay[WeekDay[\"Monday\"] = 1] = \"Monday\";\n  WeekDay[WeekDay[\"Tuesday\"] = 2] = \"Tuesday\";\n  WeekDay[WeekDay[\"Wednesday\"] = 3] = \"Wednesday\";\n  WeekDay[WeekDay[\"Thursday\"] = 4] = \"Thursday\";\n  WeekDay[WeekDay[\"Friday\"] = 5] = \"Friday\";\n  WeekDay[WeekDay[\"Saturday\"] = 6] = \"Saturday\";\n})(WeekDay || (WeekDay = {}));\n/**\n * Retrieves the locale ID from the currently loaded locale.\n * The loaded locale could be, for example, a global one rather than a regional one.\n * @param locale A locale code, such as `fr-FR`.\n * @returns The locale code. For example, `fr`.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleId(locale) {\n  return ɵfindLocaleData(locale)[ɵLocaleDataIndex.LocaleId];\n}\n/**\n * Retrieves day period strings for the given locale.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns An array of localized period strings. For example, `[AM, PM]` for `en-US`.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleDayPeriods(locale, formStyle, width) {\n  const data = ɵfindLocaleData(locale);\n  const amPmData = [data[ɵLocaleDataIndex.DayPeriodsFormat], data[ɵLocaleDataIndex.DayPeriodsStandalone]];\n  const amPm = getLastDefinedValue(amPmData, formStyle);\n  return getLastDefinedValue(amPm, width);\n}\n/**\n * Retrieves days of the week for the given locale, using the Gregorian calendar.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns An array of localized name strings.\n * For example,`[Sunday, Monday, ... Saturday]` for `en-US`.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleDayNames(locale, formStyle, width) {\n  const data = ɵfindLocaleData(locale);\n  const daysData = [data[ɵLocaleDataIndex.DaysFormat], data[ɵLocaleDataIndex.DaysStandalone]];\n  const days = getLastDefinedValue(daysData, formStyle);\n  return getLastDefinedValue(days, width);\n}\n/**\n * Retrieves months of the year for the given locale, using the Gregorian calendar.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns An array of localized name strings.\n * For example,  `[January, February, ...]` for `en-US`.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleMonthNames(locale, formStyle, width) {\n  const data = ɵfindLocaleData(locale);\n  const monthsData = [data[ɵLocaleDataIndex.MonthsFormat], data[ɵLocaleDataIndex.MonthsStandalone]];\n  const months = getLastDefinedValue(monthsData, formStyle);\n  return getLastDefinedValue(months, width);\n}\n/**\n * Retrieves Gregorian-calendar eras for the given locale.\n * @param locale A locale code for the locale format rules to use.\n * @param width The required character width.\n\n * @returns An array of localized era strings.\n * For example, `[AD, BC]` for `en-US`.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleEraNames(locale, width) {\n  const data = ɵfindLocaleData(locale);\n  const erasData = data[ɵLocaleDataIndex.Eras];\n  return getLastDefinedValue(erasData, width);\n}\n/**\n * Retrieves the first day of the week for the given locale.\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns A day index number, using the 0-based week-day index for `en-US`\n * (Sunday = 0, Monday = 1, ...).\n * For example, for `fr-FR`, returns 1 to indicate that the first day is Monday.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleFirstDayOfWeek(locale) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.FirstDayOfWeek];\n}\n/**\n * Range of week days that are considered the week-end for the given locale.\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns The range of day values, `[startDay, endDay]`.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleWeekEndRange(locale) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.WeekendRange];\n}\n/**\n * Retrieves a localized date-value formatting string.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param width The format type.\n * @returns The localized formatting string.\n * @see {@link FormatWidth}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleDateFormat(locale, width) {\n  const data = ɵfindLocaleData(locale);\n  return getLastDefinedValue(data[ɵLocaleDataIndex.DateFormat], width);\n}\n/**\n * Retrieves a localized time-value formatting string.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param width The format type.\n * @returns The localized formatting string.\n * @see {@link FormatWidth}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n\n * @publicApi\n */\nfunction getLocaleTimeFormat(locale, width) {\n  const data = ɵfindLocaleData(locale);\n  return getLastDefinedValue(data[ɵLocaleDataIndex.TimeFormat], width);\n}\n/**\n * Retrieves a localized date-time formatting string.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param width The format type.\n * @returns The localized formatting string.\n * @see {@link FormatWidth}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleDateTimeFormat(locale, width) {\n  const data = ɵfindLocaleData(locale);\n  const dateTimeFormatData = data[ɵLocaleDataIndex.DateTimeFormat];\n  return getLastDefinedValue(dateTimeFormatData, width);\n}\n/**\n * Retrieves a localized number symbol that can be used to replace placeholders in number formats.\n * @param locale The locale code.\n * @param symbol The symbol to localize. Must be one of `NumberSymbol`.\n * @returns The character for the localized symbol.\n * @see {@link NumberSymbol}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleNumberSymbol(locale, symbol) {\n  const data = ɵfindLocaleData(locale);\n  const res = data[ɵLocaleDataIndex.NumberSymbols][symbol];\n  if (typeof res === 'undefined') {\n    if (symbol === NumberSymbol.CurrencyDecimal) {\n      return data[ɵLocaleDataIndex.NumberSymbols][NumberSymbol.Decimal];\n    } else if (symbol === NumberSymbol.CurrencyGroup) {\n      return data[ɵLocaleDataIndex.NumberSymbols][NumberSymbol.Group];\n    }\n  }\n  return res;\n}\n/**\n * Retrieves a number format for a given locale.\n *\n * Numbers are formatted using patterns, like `#,###.00`. For example, the pattern `#,###.00`\n * when used to format the number 12345.678 could result in \"12'345,678\". That would happen if the\n * grouping separator for your language is an apostrophe, and the decimal separator is a comma.\n *\n * <b>Important:</b> The characters `.` `,` `0` `#` (and others below) are special placeholders\n * that stand for the decimal separator, and so on, and are NOT real characters.\n * You must NOT \"translate\" the placeholders. For example, don't change `.` to `,` even though in\n * your language the decimal point is written with a comma. The symbols should be replaced by the\n * local equivalents, using the appropriate `NumberSymbol` for your language.\n *\n * Here are the special characters used in number patterns:\n *\n * | Symbol | Meaning |\n * |--------|---------|\n * | . | Replaced automatically by the character used for the decimal point. |\n * | , | Replaced by the \"grouping\" (thousands) separator. |\n * | 0 | Replaced by a digit (or zero if there aren't enough digits). |\n * | # | Replaced by a digit (or nothing if there aren't enough). |\n * | ¤ | Replaced by a currency symbol, such as $ or USD. |\n * | % | Marks a percent format. The % symbol may change position, but must be retained. |\n * | E | Marks a scientific format. The E symbol may change position, but must be retained. |\n * | ' | Special characters used as literal characters are quoted with ASCII single quotes. |\n *\n * @param locale A locale code for the locale format rules to use.\n * @param type The type of numeric value to be formatted (such as `Decimal` or `Currency`.)\n * @returns The localized format string.\n * @see {@link NumberFormatStyle}\n * @see [CLDR website](http://cldr.unicode.org/translation/number-patterns)\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleNumberFormat(locale, type) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.NumberFormats][type];\n}\n/**\n * Retrieves the symbol used to represent the currency for the main country\n * corresponding to a given locale. For example, '$' for `en-US`.\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns The localized symbol character,\n * or `null` if the main country cannot be determined.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleCurrencySymbol(locale) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.CurrencySymbol] || null;\n}\n/**\n * Retrieves the name of the currency for the main country corresponding\n * to a given locale. For example, 'US Dollar' for `en-US`.\n * @param locale A locale code for the locale format rules to use.\n * @returns The currency name,\n * or `null` if the main country cannot be determined.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleCurrencyName(locale) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.CurrencyName] || null;\n}\n/**\n * Retrieves the default currency code for the given locale.\n *\n * The default is defined as the first currency which is still in use.\n *\n * @param locale The code of the locale whose currency code we want.\n * @returns The code of the default currency for the given locale.\n *\n * @publicApi\n */\nfunction getLocaleCurrencyCode(locale) {\n  return ɵgetLocaleCurrencyCode(locale);\n}\n/**\n * Retrieves the currency values for a given locale.\n * @param locale A locale code for the locale format rules to use.\n * @returns The currency values.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n */\nfunction getLocaleCurrencies(locale) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.Currencies];\n}\n/**\n * @alias core/ɵgetLocalePluralCase\n * @publicApi\n */\nconst getLocalePluralCase = ɵgetLocalePluralCase;\nfunction checkFullData(data) {\n  if (!data[ɵLocaleDataIndex.ExtraData]) {\n    throw new Error(`Missing extra locale data for the locale \"${data[ɵLocaleDataIndex.LocaleId]}\". Use \"registerLocaleData\" to load new data. See the \"I18n guide\" on angular.io to know more.`);\n  }\n}\n/**\n * Retrieves locale-specific rules used to determine which day period to use\n * when more than one period is defined for a locale.\n *\n * There is a rule for each defined day period. The\n * first rule is applied to the first day period and so on.\n * Fall back to AM/PM when no rules are available.\n *\n * A rule can specify a period as time range, or as a single time value.\n *\n * This functionality is only available when you have loaded the full locale data.\n * See the [\"I18n guide\"](guide/i18n-common-format-data-locale).\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns The rules for the locale, a single time value or array of *from-time, to-time*,\n * or null if no periods are available.\n *\n * @see {@link getLocaleExtraDayPeriods}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleExtraDayPeriodRules(locale) {\n  const data = ɵfindLocaleData(locale);\n  checkFullData(data);\n  const rules = data[ɵLocaleDataIndex.ExtraData][2 /* ɵExtraLocaleDataIndex.ExtraDayPeriodsRules */] || [];\n  return rules.map(rule => {\n    if (typeof rule === 'string') {\n      return extractTime(rule);\n    }\n    return [extractTime(rule[0]), extractTime(rule[1])];\n  });\n}\n/**\n * Retrieves locale-specific day periods, which indicate roughly how a day is broken up\n * in different languages.\n * For example, for `en-US`, periods are morning, noon, afternoon, evening, and midnight.\n *\n * This functionality is only available when you have loaded the full locale data.\n * See the [\"I18n guide\"](guide/i18n-common-format-data-locale).\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns The translated day-period strings.\n * @see {@link getLocaleExtraDayPeriodRules}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLocaleExtraDayPeriods(locale, formStyle, width) {\n  const data = ɵfindLocaleData(locale);\n  checkFullData(data);\n  const dayPeriodsData = [data[ɵLocaleDataIndex.ExtraData][0 /* ɵExtraLocaleDataIndex.ExtraDayPeriodFormats */], data[ɵLocaleDataIndex.ExtraData][1 /* ɵExtraLocaleDataIndex.ExtraDayPeriodStandalone */]];\n  const dayPeriods = getLastDefinedValue(dayPeriodsData, formStyle) || [];\n  return getLastDefinedValue(dayPeriods, width) || [];\n}\n/**\n * Retrieves the writing direction of a specified locale\n * @param locale A locale code for the locale format rules to use.\n * @publicApi\n * @returns 'rtl' or 'ltr'\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n */\nfunction getLocaleDirection(locale) {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.Directionality];\n}\n/**\n * Retrieves the first value that is defined in an array, going backwards from an index position.\n *\n * To avoid repeating the same data (as when the \"format\" and \"standalone\" forms are the same)\n * add the first value to the locale data arrays, and add other values only if they are different.\n *\n * @param data The data array to retrieve from.\n * @param index A 0-based index into the array to start from.\n * @returns The value immediately before the given index position.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getLastDefinedValue(data, index) {\n  for (let i = index; i > -1; i--) {\n    if (typeof data[i] !== 'undefined') {\n      return data[i];\n    }\n  }\n  throw new Error('Locale data API: locale data undefined');\n}\n/**\n * Extracts the hours and minutes from a string like \"15:45\"\n */\nfunction extractTime(time) {\n  const [h, m] = time.split(':');\n  return {\n    hours: +h,\n    minutes: +m\n  };\n}\n/**\n * Retrieves the currency symbol for a given currency code.\n *\n * For example, for the default `en-US` locale, the code `USD` can\n * be represented by the narrow symbol `$` or the wide symbol `US$`.\n *\n * @param code The currency code.\n * @param format The format, `wide` or `narrow`.\n * @param locale A locale code for the locale format rules to use.\n *\n * @returns The symbol, or the currency code if no symbol is available.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getCurrencySymbol(code, format, locale = 'en') {\n  const currency = getLocaleCurrencies(locale)[code] || CURRENCIES_EN[code] || [];\n  const symbolNarrow = currency[1 /* ɵCurrencyIndex.SymbolNarrow */];\n  if (format === 'narrow' && typeof symbolNarrow === 'string') {\n    return symbolNarrow;\n  }\n  return currency[0 /* ɵCurrencyIndex.Symbol */] || code;\n}\n// Most currencies have cents, that's why the default is 2\nconst DEFAULT_NB_OF_CURRENCY_DIGITS = 2;\n/**\n * Reports the number of decimal digits for a given currency.\n * The value depends upon the presence of cents in that particular currency.\n *\n * @param code The currency code.\n * @returns The number of decimal digits, typically 0 or 2.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction getNumberOfCurrencyDigits(code) {\n  let digits;\n  const currency = CURRENCIES_EN[code];\n  if (currency) {\n    digits = currency[2 /* ɵCurrencyIndex.NbOfDigits */];\n  }\n  return typeof digits === 'number' ? digits : DEFAULT_NB_OF_CURRENCY_DIGITS;\n}\nconst ISO8601_DATE_REGEX = /^(\\d{4,})-?(\\d\\d)-?(\\d\\d)(?:T(\\d\\d)(?::?(\\d\\d)(?::?(\\d\\d)(?:\\.(\\d+))?)?)?(Z|([+-])(\\d\\d):?(\\d\\d))?)?$/;\n//    1        2       3         4          5          6          7          8  9     10      11\nconst NAMED_FORMATS = {};\nconst DATE_FORMATS_SPLIT = /((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\\s\\S]*)/;\nvar ZoneWidth;\n(function (ZoneWidth) {\n  ZoneWidth[ZoneWidth[\"Short\"] = 0] = \"Short\";\n  ZoneWidth[ZoneWidth[\"ShortGMT\"] = 1] = \"ShortGMT\";\n  ZoneWidth[ZoneWidth[\"Long\"] = 2] = \"Long\";\n  ZoneWidth[ZoneWidth[\"Extended\"] = 3] = \"Extended\";\n})(ZoneWidth || (ZoneWidth = {}));\nvar DateType;\n(function (DateType) {\n  DateType[DateType[\"FullYear\"] = 0] = \"FullYear\";\n  DateType[DateType[\"Month\"] = 1] = \"Month\";\n  DateType[DateType[\"Date\"] = 2] = \"Date\";\n  DateType[DateType[\"Hours\"] = 3] = \"Hours\";\n  DateType[DateType[\"Minutes\"] = 4] = \"Minutes\";\n  DateType[DateType[\"Seconds\"] = 5] = \"Seconds\";\n  DateType[DateType[\"FractionalSeconds\"] = 6] = \"FractionalSeconds\";\n  DateType[DateType[\"Day\"] = 7] = \"Day\";\n})(DateType || (DateType = {}));\nvar TranslationType;\n(function (TranslationType) {\n  TranslationType[TranslationType[\"DayPeriods\"] = 0] = \"DayPeriods\";\n  TranslationType[TranslationType[\"Days\"] = 1] = \"Days\";\n  TranslationType[TranslationType[\"Months\"] = 2] = \"Months\";\n  TranslationType[TranslationType[\"Eras\"] = 3] = \"Eras\";\n})(TranslationType || (TranslationType = {}));\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a date according to locale rules.\n *\n * @param value The date to format, as a Date, or a number (milliseconds since UTC epoch)\n * or an [ISO date-time string](https://www.w3.org/TR/NOTE-datetime).\n * @param format The date-time components to include. See `DatePipe` for details.\n * @param locale A locale code for the locale format rules to use.\n * @param timezone The time zone. A time zone offset from GMT (such as `'+0430'`),\n * or a standard UTC/GMT or continental US time zone abbreviation.\n * If not specified, uses host system settings.\n *\n * @returns The formatted date string.\n *\n * @see {@link DatePipe}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction formatDate(value, format, locale, timezone) {\n  let date = toDate(value);\n  const namedFormat = getNamedFormat(locale, format);\n  format = namedFormat || format;\n  let parts = [];\n  let match;\n  while (format) {\n    match = DATE_FORMATS_SPLIT.exec(format);\n    if (match) {\n      parts = parts.concat(match.slice(1));\n      const part = parts.pop();\n      if (!part) {\n        break;\n      }\n      format = part;\n    } else {\n      parts.push(format);\n      break;\n    }\n  }\n  let dateTimezoneOffset = date.getTimezoneOffset();\n  if (timezone) {\n    dateTimezoneOffset = timezoneToOffset(timezone, dateTimezoneOffset);\n    date = convertTimezoneToLocal(date, timezone, true);\n  }\n  let text = '';\n  parts.forEach(value => {\n    const dateFormatter = getDateFormatter(value);\n    text += dateFormatter ? dateFormatter(date, locale, dateTimezoneOffset) : value === \"''\" ? \"'\" : value.replace(/(^'|'$)/g, '').replace(/''/g, \"'\");\n  });\n  return text;\n}\n/**\n * Create a new Date object with the given date value, and the time set to midnight.\n *\n * We cannot use `new Date(year, month, date)` because it maps years between 0 and 99 to 1900-1999.\n * See: https://github.com/angular/angular/issues/40377\n *\n * Note that this function returns a Date object whose time is midnight in the current locale's\n * timezone. In the future we might want to change this to be midnight in UTC, but this would be a\n * considerable breaking change.\n */\nfunction createDate(year, month, date) {\n  // The `newDate` is set to midnight (UTC) on January 1st 1970.\n  // - In PST this will be December 31st 1969 at 4pm.\n  // - In GMT this will be January 1st 1970 at 1am.\n  // Note that they even have different years, dates and months!\n  const newDate = new Date(0);\n  // `setFullYear()` allows years like 0001 to be set correctly. This function does not\n  // change the internal time of the date.\n  // Consider calling `setFullYear(2019, 8, 20)` (September 20, 2019).\n  // - In PST this will now be September 20, 2019 at 4pm\n  // - In GMT this will now be September 20, 2019 at 1am\n  newDate.setFullYear(year, month, date);\n  // We want the final date to be at local midnight, so we reset the time.\n  // - In PST this will now be September 20, 2019 at 12am\n  // - In GMT this will now be September 20, 2019 at 12am\n  newDate.setHours(0, 0, 0);\n  return newDate;\n}\nfunction getNamedFormat(locale, format) {\n  const localeId = getLocaleId(locale);\n  NAMED_FORMATS[localeId] ??= {};\n  if (NAMED_FORMATS[localeId][format]) {\n    return NAMED_FORMATS[localeId][format];\n  }\n  let formatValue = '';\n  switch (format) {\n    case 'shortDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Short);\n      break;\n    case 'mediumDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Medium);\n      break;\n    case 'longDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Long);\n      break;\n    case 'fullDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Full);\n      break;\n    case 'shortTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Short);\n      break;\n    case 'mediumTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Medium);\n      break;\n    case 'longTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Long);\n      break;\n    case 'fullTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Full);\n      break;\n    case 'short':\n      const shortTime = getNamedFormat(locale, 'shortTime');\n      const shortDate = getNamedFormat(locale, 'shortDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Short), [shortTime, shortDate]);\n      break;\n    case 'medium':\n      const mediumTime = getNamedFormat(locale, 'mediumTime');\n      const mediumDate = getNamedFormat(locale, 'mediumDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Medium), [mediumTime, mediumDate]);\n      break;\n    case 'long':\n      const longTime = getNamedFormat(locale, 'longTime');\n      const longDate = getNamedFormat(locale, 'longDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Long), [longTime, longDate]);\n      break;\n    case 'full':\n      const fullTime = getNamedFormat(locale, 'fullTime');\n      const fullDate = getNamedFormat(locale, 'fullDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Full), [fullTime, fullDate]);\n      break;\n  }\n  if (formatValue) {\n    NAMED_FORMATS[localeId][format] = formatValue;\n  }\n  return formatValue;\n}\nfunction formatDateTime(str, opt_values) {\n  if (opt_values) {\n    str = str.replace(/\\{([^}]+)}/g, function (match, key) {\n      return opt_values != null && key in opt_values ? opt_values[key] : match;\n    });\n  }\n  return str;\n}\nfunction padNumber(num, digits, minusSign = '-', trim, negWrap) {\n  let neg = '';\n  if (num < 0 || negWrap && num <= 0) {\n    if (negWrap) {\n      num = -num + 1;\n    } else {\n      num = -num;\n      neg = minusSign;\n    }\n  }\n  let strNum = String(num);\n  while (strNum.length < digits) {\n    strNum = '0' + strNum;\n  }\n  if (trim) {\n    strNum = strNum.slice(strNum.length - digits);\n  }\n  return neg + strNum;\n}\nfunction formatFractionalSeconds(milliseconds, digits) {\n  const strMs = padNumber(milliseconds, 3);\n  return strMs.substring(0, digits);\n}\n/**\n * Returns a date formatter that transforms a date into its locale digit representation\n */\nfunction dateGetter(name, size, offset = 0, trim = false, negWrap = false) {\n  return function (date, locale) {\n    let part = getDatePart(name, date);\n    if (offset > 0 || part > -offset) {\n      part += offset;\n    }\n    if (name === DateType.Hours) {\n      if (part === 0 && offset === -12) {\n        part = 12;\n      }\n    } else if (name === DateType.FractionalSeconds) {\n      return formatFractionalSeconds(part, size);\n    }\n    const localeMinus = getLocaleNumberSymbol(locale, NumberSymbol.MinusSign);\n    return padNumber(part, size, localeMinus, trim, negWrap);\n  };\n}\nfunction getDatePart(part, date) {\n  switch (part) {\n    case DateType.FullYear:\n      return date.getFullYear();\n    case DateType.Month:\n      return date.getMonth();\n    case DateType.Date:\n      return date.getDate();\n    case DateType.Hours:\n      return date.getHours();\n    case DateType.Minutes:\n      return date.getMinutes();\n    case DateType.Seconds:\n      return date.getSeconds();\n    case DateType.FractionalSeconds:\n      return date.getMilliseconds();\n    case DateType.Day:\n      return date.getDay();\n    default:\n      throw new Error(`Unknown DateType value \"${part}\".`);\n  }\n}\n/**\n * Returns a date formatter that transforms a date into its locale string representation\n */\nfunction dateStrGetter(name, width, form = FormStyle.Format, extended = false) {\n  return function (date, locale) {\n    return getDateTranslation(date, locale, name, width, form, extended);\n  };\n}\n/**\n * Returns the locale translation of a date for a given form, type and width\n */\nfunction getDateTranslation(date, locale, name, width, form, extended) {\n  switch (name) {\n    case TranslationType.Months:\n      return getLocaleMonthNames(locale, form, width)[date.getMonth()];\n    case TranslationType.Days:\n      return getLocaleDayNames(locale, form, width)[date.getDay()];\n    case TranslationType.DayPeriods:\n      const currentHours = date.getHours();\n      const currentMinutes = date.getMinutes();\n      if (extended) {\n        const rules = getLocaleExtraDayPeriodRules(locale);\n        const dayPeriods = getLocaleExtraDayPeriods(locale, form, width);\n        const index = rules.findIndex(rule => {\n          if (Array.isArray(rule)) {\n            // morning, afternoon, evening, night\n            const [from, to] = rule;\n            const afterFrom = currentHours >= from.hours && currentMinutes >= from.minutes;\n            const beforeTo = currentHours < to.hours || currentHours === to.hours && currentMinutes < to.minutes;\n            // We must account for normal rules that span a period during the day (e.g. 6am-9am)\n            // where `from` is less (earlier) than `to`. But also rules that span midnight (e.g.\n            // 10pm - 5am) where `from` is greater (later!) than `to`.\n            //\n            // In the first case the current time must be BOTH after `from` AND before `to`\n            // (e.g. 8am is after 6am AND before 10am).\n            //\n            // In the second case the current time must be EITHER after `from` OR before `to`\n            // (e.g. 4am is before 5am but not after 10pm; and 11pm is not before 5am but it is\n            // after 10pm).\n            if (from.hours < to.hours) {\n              if (afterFrom && beforeTo) {\n                return true;\n              }\n            } else if (afterFrom || beforeTo) {\n              return true;\n            }\n          } else {\n            // noon or midnight\n            if (rule.hours === currentHours && rule.minutes === currentMinutes) {\n              return true;\n            }\n          }\n          return false;\n        });\n        if (index !== -1) {\n          return dayPeriods[index];\n        }\n      }\n      // if no rules for the day periods, we use am/pm by default\n      return getLocaleDayPeriods(locale, form, width)[currentHours < 12 ? 0 : 1];\n    case TranslationType.Eras:\n      return getLocaleEraNames(locale, width)[date.getFullYear() <= 0 ? 0 : 1];\n    default:\n      // This default case is not needed by TypeScript compiler, as the switch is exhaustive.\n      // However Closure Compiler does not understand that and reports an error in typed mode.\n      // The `throw new Error` below works around the problem, and the unexpected: never variable\n      // makes sure tsc still checks this code is unreachable.\n      const unexpected = name;\n      throw new Error(`unexpected translation type ${unexpected}`);\n  }\n}\n/**\n * Returns a date formatter that transforms a date and an offset into a timezone with ISO8601 or\n * GMT format depending on the width (eg: short = +0430, short:GMT = GMT+4, long = GMT+04:30,\n * extended = +04:30)\n */\nfunction timeZoneGetter(width) {\n  return function (date, locale, offset) {\n    const zone = -1 * offset;\n    const minusSign = getLocaleNumberSymbol(locale, NumberSymbol.MinusSign);\n    const hours = zone > 0 ? Math.floor(zone / 60) : Math.ceil(zone / 60);\n    switch (width) {\n      case ZoneWidth.Short:\n        return (zone >= 0 ? '+' : '') + padNumber(hours, 2, minusSign) + padNumber(Math.abs(zone % 60), 2, minusSign);\n      case ZoneWidth.ShortGMT:\n        return 'GMT' + (zone >= 0 ? '+' : '') + padNumber(hours, 1, minusSign);\n      case ZoneWidth.Long:\n        return 'GMT' + (zone >= 0 ? '+' : '') + padNumber(hours, 2, minusSign) + ':' + padNumber(Math.abs(zone % 60), 2, minusSign);\n      case ZoneWidth.Extended:\n        if (offset === 0) {\n          return 'Z';\n        } else {\n          return (zone >= 0 ? '+' : '') + padNumber(hours, 2, minusSign) + ':' + padNumber(Math.abs(zone % 60), 2, minusSign);\n        }\n      default:\n        throw new Error(`Unknown zone width \"${width}\"`);\n    }\n  };\n}\nconst JANUARY = 0;\nconst THURSDAY = 4;\nfunction getFirstThursdayOfYear(year) {\n  const firstDayOfYear = createDate(year, JANUARY, 1).getDay();\n  return createDate(year, 0, 1 + (firstDayOfYear <= THURSDAY ? THURSDAY : THURSDAY + 7) - firstDayOfYear);\n}\n/**\n *  ISO Week starts on day 1 (Monday) and ends with day 0 (Sunday)\n */\nfunction getThursdayThisIsoWeek(datetime) {\n  // getDay returns 0-6 range with sunday as 0.\n  const currentDay = datetime.getDay();\n  // On a Sunday, read the previous Thursday since ISO weeks start on Monday.\n  const deltaToThursday = currentDay === 0 ? -3 : THURSDAY - currentDay;\n  return createDate(datetime.getFullYear(), datetime.getMonth(), datetime.getDate() + deltaToThursday);\n}\nfunction weekGetter(size, monthBased = false) {\n  return function (date, locale) {\n    let result;\n    if (monthBased) {\n      const nbDaysBefore1stDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1).getDay() - 1;\n      const today = date.getDate();\n      result = 1 + Math.floor((today + nbDaysBefore1stDayOfMonth) / 7);\n    } else {\n      const thisThurs = getThursdayThisIsoWeek(date);\n      // Some days of a year are part of next year according to ISO 8601.\n      // Compute the firstThurs from the year of this week's Thursday\n      const firstThurs = getFirstThursdayOfYear(thisThurs.getFullYear());\n      const diff = thisThurs.getTime() - firstThurs.getTime();\n      result = 1 + Math.round(diff / 6.048e8); // 6.048e8 ms per week\n    }\n    return padNumber(result, size, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  };\n}\n/**\n * Returns a date formatter that provides the week-numbering year for the input date.\n */\nfunction weekNumberingYearGetter(size, trim = false) {\n  return function (date, locale) {\n    const thisThurs = getThursdayThisIsoWeek(date);\n    const weekNumberingYear = thisThurs.getFullYear();\n    return padNumber(weekNumberingYear, size, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign), trim);\n  };\n}\nconst DATE_FORMATS = {};\n// Based on CLDR formats:\n// See complete list: http://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n// See also explanations: http://cldr.unicode.org/translation/date-time\n// TODO(ocombe): support all missing cldr formats: U, Q, D, F, e, j, J, C, A, v, V, X, x\nfunction getDateFormatter(format) {\n  if (DATE_FORMATS[format]) {\n    return DATE_FORMATS[format];\n  }\n  let formatter;\n  switch (format) {\n    // Era name (AD/BC)\n    case 'G':\n    case 'GG':\n    case 'GGG':\n      formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Abbreviated);\n      break;\n    case 'GGGG':\n      formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Wide);\n      break;\n    case 'GGGGG':\n      formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Narrow);\n      break;\n    // 1 digit representation of the year, e.g. (AD 1 => 1, AD 199 => 199)\n    case 'y':\n      formatter = dateGetter(DateType.FullYear, 1, 0, false, true);\n      break;\n    // 2 digit representation of the year, padded (00-99). (e.g. AD 2001 => 01, AD 2010 => 10)\n    case 'yy':\n      formatter = dateGetter(DateType.FullYear, 2, 0, true, true);\n      break;\n    // 3 digit representation of the year, padded (000-999). (e.g. AD 2001 => 01, AD 2010 => 10)\n    case 'yyy':\n      formatter = dateGetter(DateType.FullYear, 3, 0, false, true);\n      break;\n    // 4 digit representation of the year (e.g. AD 1 => 0001, AD 2010 => 2010)\n    case 'yyyy':\n      formatter = dateGetter(DateType.FullYear, 4, 0, false, true);\n      break;\n    // 1 digit representation of the week-numbering year, e.g. (AD 1 => 1, AD 199 => 199)\n    case 'Y':\n      formatter = weekNumberingYearGetter(1);\n      break;\n    // 2 digit representation of the week-numbering year, padded (00-99). (e.g. AD 2001 => 01, AD\n    // 2010 => 10)\n    case 'YY':\n      formatter = weekNumberingYearGetter(2, true);\n      break;\n    // 3 digit representation of the week-numbering year, padded (000-999). (e.g. AD 1 => 001, AD\n    // 2010 => 2010)\n    case 'YYY':\n      formatter = weekNumberingYearGetter(3);\n      break;\n    // 4 digit representation of the week-numbering year (e.g. AD 1 => 0001, AD 2010 => 2010)\n    case 'YYYY':\n      formatter = weekNumberingYearGetter(4);\n      break;\n    // Month of the year (1-12), numeric\n    case 'M':\n    case 'L':\n      formatter = dateGetter(DateType.Month, 1, 1);\n      break;\n    case 'MM':\n    case 'LL':\n      formatter = dateGetter(DateType.Month, 2, 1);\n      break;\n    // Month of the year (January, ...), string, format\n    case 'MMM':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Abbreviated);\n      break;\n    case 'MMMM':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Wide);\n      break;\n    case 'MMMMM':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Narrow);\n      break;\n    // Month of the year (January, ...), string, standalone\n    case 'LLL':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Abbreviated, FormStyle.Standalone);\n      break;\n    case 'LLLL':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Wide, FormStyle.Standalone);\n      break;\n    case 'LLLLL':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Narrow, FormStyle.Standalone);\n      break;\n    // Week of the year (1, ... 52)\n    case 'w':\n      formatter = weekGetter(1);\n      break;\n    case 'ww':\n      formatter = weekGetter(2);\n      break;\n    // Week of the month (1, ...)\n    case 'W':\n      formatter = weekGetter(1, true);\n      break;\n    // Day of the month (1-31)\n    case 'd':\n      formatter = dateGetter(DateType.Date, 1);\n      break;\n    case 'dd':\n      formatter = dateGetter(DateType.Date, 2);\n      break;\n    // Day of the Week StandAlone (1, 1, Mon, Monday, M, Mo)\n    case 'c':\n    case 'cc':\n      formatter = dateGetter(DateType.Day, 1);\n      break;\n    case 'ccc':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Abbreviated, FormStyle.Standalone);\n      break;\n    case 'cccc':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Wide, FormStyle.Standalone);\n      break;\n    case 'ccccc':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Narrow, FormStyle.Standalone);\n      break;\n    case 'cccccc':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Short, FormStyle.Standalone);\n      break;\n    // Day of the Week\n    case 'E':\n    case 'EE':\n    case 'EEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Abbreviated);\n      break;\n    case 'EEEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Wide);\n      break;\n    case 'EEEEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Narrow);\n      break;\n    case 'EEEEEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Short);\n      break;\n    // Generic period of the day (am-pm)\n    case 'a':\n    case 'aa':\n    case 'aaa':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Abbreviated);\n      break;\n    case 'aaaa':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Wide);\n      break;\n    case 'aaaaa':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Narrow);\n      break;\n    // Extended period of the day (midnight, at night, ...), standalone\n    case 'b':\n    case 'bb':\n    case 'bbb':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Abbreviated, FormStyle.Standalone, true);\n      break;\n    case 'bbbb':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Wide, FormStyle.Standalone, true);\n      break;\n    case 'bbbbb':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Narrow, FormStyle.Standalone, true);\n      break;\n    // Extended period of the day (midnight, night, ...), standalone\n    case 'B':\n    case 'BB':\n    case 'BBB':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Abbreviated, FormStyle.Format, true);\n      break;\n    case 'BBBB':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Wide, FormStyle.Format, true);\n      break;\n    case 'BBBBB':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Narrow, FormStyle.Format, true);\n      break;\n    // Hour in AM/PM, (1-12)\n    case 'h':\n      formatter = dateGetter(DateType.Hours, 1, -12);\n      break;\n    case 'hh':\n      formatter = dateGetter(DateType.Hours, 2, -12);\n      break;\n    // Hour of the day (0-23)\n    case 'H':\n      formatter = dateGetter(DateType.Hours, 1);\n      break;\n    // Hour in day, padded (00-23)\n    case 'HH':\n      formatter = dateGetter(DateType.Hours, 2);\n      break;\n    // Minute of the hour (0-59)\n    case 'm':\n      formatter = dateGetter(DateType.Minutes, 1);\n      break;\n    case 'mm':\n      formatter = dateGetter(DateType.Minutes, 2);\n      break;\n    // Second of the minute (0-59)\n    case 's':\n      formatter = dateGetter(DateType.Seconds, 1);\n      break;\n    case 'ss':\n      formatter = dateGetter(DateType.Seconds, 2);\n      break;\n    // Fractional second\n    case 'S':\n      formatter = dateGetter(DateType.FractionalSeconds, 1);\n      break;\n    case 'SS':\n      formatter = dateGetter(DateType.FractionalSeconds, 2);\n      break;\n    case 'SSS':\n      formatter = dateGetter(DateType.FractionalSeconds, 3);\n      break;\n    // Timezone ISO8601 short format (-0430)\n    case 'Z':\n    case 'ZZ':\n    case 'ZZZ':\n      formatter = timeZoneGetter(ZoneWidth.Short);\n      break;\n    // Timezone ISO8601 extended format (-04:30)\n    case 'ZZZZZ':\n      formatter = timeZoneGetter(ZoneWidth.Extended);\n      break;\n    // Timezone GMT short format (GMT+4)\n    case 'O':\n    case 'OO':\n    case 'OOO':\n    // Should be location, but fallback to format O instead because we don't have the data yet\n    case 'z':\n    case 'zz':\n    case 'zzz':\n      formatter = timeZoneGetter(ZoneWidth.ShortGMT);\n      break;\n    // Timezone GMT long format (GMT+0430)\n    case 'OOOO':\n    case 'ZZZZ':\n    // Should be location, but fallback to format O instead because we don't have the data yet\n    case 'zzzz':\n      formatter = timeZoneGetter(ZoneWidth.Long);\n      break;\n    default:\n      return null;\n  }\n  DATE_FORMATS[format] = formatter;\n  return formatter;\n}\nfunction timezoneToOffset(timezone, fallback) {\n  // Support: IE 11 only, Edge 13-15+\n  // IE/Edge do not \"understand\" colon (`:`) in timezone\n  timezone = timezone.replace(/:/g, '');\n  const requestedTimezoneOffset = Date.parse('Jan 01, 1970 00:00:00 ' + timezone) / 60000;\n  return isNaN(requestedTimezoneOffset) ? fallback : requestedTimezoneOffset;\n}\nfunction addDateMinutes(date, minutes) {\n  date = new Date(date.getTime());\n  date.setMinutes(date.getMinutes() + minutes);\n  return date;\n}\nfunction convertTimezoneToLocal(date, timezone, reverse) {\n  const reverseValue = reverse ? -1 : 1;\n  const dateTimezoneOffset = date.getTimezoneOffset();\n  const timezoneOffset = timezoneToOffset(timezone, dateTimezoneOffset);\n  return addDateMinutes(date, reverseValue * (timezoneOffset - dateTimezoneOffset));\n}\n/**\n * Converts a value to date.\n *\n * Supported input formats:\n * - `Date`\n * - number: timestamp\n * - string: numeric (e.g. \"1234\"), ISO and date strings in a format supported by\n *   [Date.parse()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/parse).\n *   Note: ISO strings without time return a date without timeoffset.\n *\n * Throws if unable to convert to a date.\n */\nfunction toDate(value) {\n  if (isDate(value)) {\n    return value;\n  }\n  if (typeof value === 'number' && !isNaN(value)) {\n    return new Date(value);\n  }\n  if (typeof value === 'string') {\n    value = value.trim();\n    if (/^(\\d{4}(-\\d{1,2}(-\\d{1,2})?)?)$/.test(value)) {\n      /* For ISO Strings without time the day, month and year must be extracted from the ISO String\n      before Date creation to avoid time offset and errors in the new Date.\n      If we only replace '-' with ',' in the ISO String (\"2015,01,01\"), and try to create a new\n      date, some browsers (e.g. IE 9) will throw an invalid Date error.\n      If we leave the '-' (\"2015-01-01\") and try to create a new Date(\"2015-01-01\") the timeoffset\n      is applied.\n      Note: ISO months are 0 for January, 1 for February, ... */\n      const [y, m = 1, d = 1] = value.split('-').map(val => +val);\n      return createDate(y, m - 1, d);\n    }\n    const parsedNb = parseFloat(value);\n    // any string that only contains numbers, like \"1234\" but not like \"1234hello\"\n    if (!isNaN(value - parsedNb)) {\n      return new Date(parsedNb);\n    }\n    let match;\n    if (match = value.match(ISO8601_DATE_REGEX)) {\n      return isoStringToDate(match);\n    }\n  }\n  const date = new Date(value);\n  if (!isDate(date)) {\n    throw new Error(`Unable to convert \"${value}\" into a date`);\n  }\n  return date;\n}\n/**\n * Converts a date in ISO8601 to a Date.\n * Used instead of `Date.parse` because of browser discrepancies.\n */\nfunction isoStringToDate(match) {\n  const date = new Date(0);\n  let tzHour = 0;\n  let tzMin = 0;\n  // match[8] means that the string contains \"Z\" (UTC) or a timezone like \"+01:00\" or \"+0100\"\n  const dateSetter = match[8] ? date.setUTCFullYear : date.setFullYear;\n  const timeSetter = match[8] ? date.setUTCHours : date.setHours;\n  // if there is a timezone defined like \"+01:00\" or \"+0100\"\n  if (match[9]) {\n    tzHour = Number(match[9] + match[10]);\n    tzMin = Number(match[9] + match[11]);\n  }\n  dateSetter.call(date, Number(match[1]), Number(match[2]) - 1, Number(match[3]));\n  const h = Number(match[4] || 0) - tzHour;\n  const m = Number(match[5] || 0) - tzMin;\n  const s = Number(match[6] || 0);\n  // The ECMAScript specification (https://www.ecma-international.org/ecma-262/5.1/#sec-*********)\n  // defines that `DateTime` milliseconds should always be rounded down, so that `999.9ms`\n  // becomes `999ms`.\n  const ms = Math.floor(parseFloat('0.' + (match[7] || 0)) * 1000);\n  timeSetter.call(date, h, m, s, ms);\n  return date;\n}\nfunction isDate(value) {\n  return value instanceof Date && !isNaN(value.valueOf());\n}\nconst NUMBER_FORMAT_REGEXP = /^(\\d+)?\\.((\\d+)(-(\\d+))?)?$/;\nconst MAX_DIGITS = 22;\nconst DECIMAL_SEP = '.';\nconst ZERO_CHAR = '0';\nconst PATTERN_SEP = ';';\nconst GROUP_SEP = ',';\nconst DIGIT_CHAR = '#';\nconst CURRENCY_CHAR = '¤';\nconst PERCENT_CHAR = '%';\n/**\n * Transforms a number to a locale string based on a style and a format.\n */\nfunction formatNumberToLocaleString(value, pattern, locale, groupSymbol, decimalSymbol, digitsInfo, isPercent = false) {\n  let formattedText = '';\n  let isZero = false;\n  if (!isFinite(value)) {\n    formattedText = getLocaleNumberSymbol(locale, NumberSymbol.Infinity);\n  } else {\n    let parsedNumber = parseNumber(value);\n    if (isPercent) {\n      parsedNumber = toPercent(parsedNumber);\n    }\n    let minInt = pattern.minInt;\n    let minFraction = pattern.minFrac;\n    let maxFraction = pattern.maxFrac;\n    if (digitsInfo) {\n      const parts = digitsInfo.match(NUMBER_FORMAT_REGEXP);\n      if (parts === null) {\n        throw new Error(`${digitsInfo} is not a valid digit info`);\n      }\n      const minIntPart = parts[1];\n      const minFractionPart = parts[3];\n      const maxFractionPart = parts[5];\n      if (minIntPart != null) {\n        minInt = parseIntAutoRadix(minIntPart);\n      }\n      if (minFractionPart != null) {\n        minFraction = parseIntAutoRadix(minFractionPart);\n      }\n      if (maxFractionPart != null) {\n        maxFraction = parseIntAutoRadix(maxFractionPart);\n      } else if (minFractionPart != null && minFraction > maxFraction) {\n        maxFraction = minFraction;\n      }\n    }\n    roundNumber(parsedNumber, minFraction, maxFraction);\n    let digits = parsedNumber.digits;\n    let integerLen = parsedNumber.integerLen;\n    const exponent = parsedNumber.exponent;\n    let decimals = [];\n    isZero = digits.every(d => !d);\n    // pad zeros for small numbers\n    for (; integerLen < minInt; integerLen++) {\n      digits.unshift(0);\n    }\n    // pad zeros for small numbers\n    for (; integerLen < 0; integerLen++) {\n      digits.unshift(0);\n    }\n    // extract decimals digits\n    if (integerLen > 0) {\n      decimals = digits.splice(integerLen, digits.length);\n    } else {\n      decimals = digits;\n      digits = [0];\n    }\n    // format the integer digits with grouping separators\n    const groups = [];\n    if (digits.length >= pattern.lgSize) {\n      groups.unshift(digits.splice(-pattern.lgSize, digits.length).join(''));\n    }\n    while (digits.length > pattern.gSize) {\n      groups.unshift(digits.splice(-pattern.gSize, digits.length).join(''));\n    }\n    if (digits.length) {\n      groups.unshift(digits.join(''));\n    }\n    formattedText = groups.join(getLocaleNumberSymbol(locale, groupSymbol));\n    // append the decimal digits\n    if (decimals.length) {\n      formattedText += getLocaleNumberSymbol(locale, decimalSymbol) + decimals.join('');\n    }\n    if (exponent) {\n      formattedText += getLocaleNumberSymbol(locale, NumberSymbol.Exponential) + '+' + exponent;\n    }\n  }\n  if (value < 0 && !isZero) {\n    formattedText = pattern.negPre + formattedText + pattern.negSuf;\n  } else {\n    formattedText = pattern.posPre + formattedText + pattern.posSuf;\n  }\n  return formattedText;\n}\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a number as currency using locale rules.\n *\n * @param value The number to format.\n * @param locale A locale code for the locale format rules to use.\n * @param currency A string containing the currency symbol or its name,\n * such as \"$\" or \"Canadian Dollar\". Used in output string, but does not affect the operation\n * of the function.\n * @param currencyCode The [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217)\n * currency code, such as `USD` for the US dollar and `EUR` for the euro.\n * Used to determine the number of digits in the decimal part.\n * @param digitsInfo Decimal representation options, specified by a string in the following format:\n * `{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}`. See `DecimalPipe` for more details.\n *\n * @returns The formatted currency value.\n *\n * @see {@link formatNumber}\n * @see {@link DecimalPipe}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction formatCurrency(value, locale, currency, currencyCode, digitsInfo) {\n  const format = getLocaleNumberFormat(locale, NumberFormatStyle.Currency);\n  const pattern = parseNumberFormat(format, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  pattern.minFrac = getNumberOfCurrencyDigits(currencyCode);\n  pattern.maxFrac = pattern.minFrac;\n  const res = formatNumberToLocaleString(value, pattern, locale, NumberSymbol.CurrencyGroup, NumberSymbol.CurrencyDecimal, digitsInfo);\n  return res.replace(CURRENCY_CHAR, currency)\n  // if we have 2 time the currency character, the second one is ignored\n  .replace(CURRENCY_CHAR, '')\n  // If there is a spacing between currency character and the value and\n  // the currency character is suppressed by passing an empty string, the\n  // spacing character would remain as part of the string. Then we\n  // should remove it.\n  .trim();\n}\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a number as a percentage according to locale rules.\n *\n * @param value The number to format.\n * @param locale A locale code for the locale format rules to use.\n * @param digitsInfo Decimal representation options, specified by a string in the following format:\n * `{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}`. See `DecimalPipe` for more details.\n *\n * @returns The formatted percentage value.\n *\n * @see {@link formatNumber}\n * @see {@link DecimalPipe}\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n * @publicApi\n *\n */\nfunction formatPercent(value, locale, digitsInfo) {\n  const format = getLocaleNumberFormat(locale, NumberFormatStyle.Percent);\n  const pattern = parseNumberFormat(format, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  const res = formatNumberToLocaleString(value, pattern, locale, NumberSymbol.Group, NumberSymbol.Decimal, digitsInfo, true);\n  return res.replace(new RegExp(PERCENT_CHAR, 'g'), getLocaleNumberSymbol(locale, NumberSymbol.PercentSign));\n}\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a number as text, with group sizing, separator, and other\n * parameters based on the locale.\n *\n * @param value The number to format.\n * @param locale A locale code for the locale format rules to use.\n * @param digitsInfo Decimal representation options, specified by a string in the following format:\n * `{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}`. See `DecimalPipe` for more details.\n *\n * @returns The formatted text string.\n * @see [Internationalization (i18n) Guide](/guide/i18n-overview)\n *\n * @publicApi\n */\nfunction formatNumber(value, locale, digitsInfo) {\n  const format = getLocaleNumberFormat(locale, NumberFormatStyle.Decimal);\n  const pattern = parseNumberFormat(format, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  return formatNumberToLocaleString(value, pattern, locale, NumberSymbol.Group, NumberSymbol.Decimal, digitsInfo);\n}\nfunction parseNumberFormat(format, minusSign = '-') {\n  const p = {\n    minInt: 1,\n    minFrac: 0,\n    maxFrac: 0,\n    posPre: '',\n    posSuf: '',\n    negPre: '',\n    negSuf: '',\n    gSize: 0,\n    lgSize: 0\n  };\n  const patternParts = format.split(PATTERN_SEP);\n  const positive = patternParts[0];\n  const negative = patternParts[1];\n  const positiveParts = positive.indexOf(DECIMAL_SEP) !== -1 ? positive.split(DECIMAL_SEP) : [positive.substring(0, positive.lastIndexOf(ZERO_CHAR) + 1), positive.substring(positive.lastIndexOf(ZERO_CHAR) + 1)],\n    integer = positiveParts[0],\n    fraction = positiveParts[1] || '';\n  p.posPre = integer.substring(0, integer.indexOf(DIGIT_CHAR));\n  for (let i = 0; i < fraction.length; i++) {\n    const ch = fraction.charAt(i);\n    if (ch === ZERO_CHAR) {\n      p.minFrac = p.maxFrac = i + 1;\n    } else if (ch === DIGIT_CHAR) {\n      p.maxFrac = i + 1;\n    } else {\n      p.posSuf += ch;\n    }\n  }\n  const groups = integer.split(GROUP_SEP);\n  p.gSize = groups[1] ? groups[1].length : 0;\n  p.lgSize = groups[2] || groups[1] ? (groups[2] || groups[1]).length : 0;\n  if (negative) {\n    const trunkLen = positive.length - p.posPre.length - p.posSuf.length,\n      pos = negative.indexOf(DIGIT_CHAR);\n    p.negPre = negative.substring(0, pos).replace(/'/g, '');\n    p.negSuf = negative.slice(pos + trunkLen).replace(/'/g, '');\n  } else {\n    p.negPre = minusSign + p.posPre;\n    p.negSuf = p.posSuf;\n  }\n  return p;\n}\n// Transforms a parsed number into a percentage by multiplying it by 100\nfunction toPercent(parsedNumber) {\n  // if the number is 0, don't do anything\n  if (parsedNumber.digits[0] === 0) {\n    return parsedNumber;\n  }\n  // Getting the current number of decimals\n  const fractionLen = parsedNumber.digits.length - parsedNumber.integerLen;\n  if (parsedNumber.exponent) {\n    parsedNumber.exponent += 2;\n  } else {\n    if (fractionLen === 0) {\n      parsedNumber.digits.push(0, 0);\n    } else if (fractionLen === 1) {\n      parsedNumber.digits.push(0);\n    }\n    parsedNumber.integerLen += 2;\n  }\n  return parsedNumber;\n}\n/**\n * Parses a number.\n * Significant bits of this parse algorithm came from https://github.com/MikeMcl/big.js/\n */\nfunction parseNumber(num) {\n  let numStr = Math.abs(num) + '';\n  let exponent = 0,\n    digits,\n    integerLen;\n  let i, j, zeros;\n  // Decimal point?\n  if ((integerLen = numStr.indexOf(DECIMAL_SEP)) > -1) {\n    numStr = numStr.replace(DECIMAL_SEP, '');\n  }\n  // Exponential form?\n  if ((i = numStr.search(/e/i)) > 0) {\n    // Work out the exponent.\n    if (integerLen < 0) integerLen = i;\n    integerLen += +numStr.slice(i + 1);\n    numStr = numStr.substring(0, i);\n  } else if (integerLen < 0) {\n    // There was no decimal point or exponent so it is an integer.\n    integerLen = numStr.length;\n  }\n  // Count the number of leading zeros.\n  for (i = 0; numStr.charAt(i) === ZERO_CHAR; i++) {\n    /* empty */\n  }\n  if (i === (zeros = numStr.length)) {\n    // The digits are all zero.\n    digits = [0];\n    integerLen = 1;\n  } else {\n    // Count the number of trailing zeros\n    zeros--;\n    while (numStr.charAt(zeros) === ZERO_CHAR) zeros--;\n    // Trailing zeros are insignificant so ignore them\n    integerLen -= i;\n    digits = [];\n    // Convert string to array of digits without leading/trailing zeros.\n    for (j = 0; i <= zeros; i++, j++) {\n      digits[j] = Number(numStr.charAt(i));\n    }\n  }\n  // If the number overflows the maximum allowed digits then use an exponent.\n  if (integerLen > MAX_DIGITS) {\n    digits = digits.splice(0, MAX_DIGITS - 1);\n    exponent = integerLen - 1;\n    integerLen = 1;\n  }\n  return {\n    digits,\n    exponent,\n    integerLen\n  };\n}\n/**\n * Round the parsed number to the specified number of decimal places\n * This function changes the parsedNumber in-place\n */\nfunction roundNumber(parsedNumber, minFrac, maxFrac) {\n  if (minFrac > maxFrac) {\n    throw new Error(`The minimum number of digits after fraction (${minFrac}) is higher than the maximum (${maxFrac}).`);\n  }\n  let digits = parsedNumber.digits;\n  let fractionLen = digits.length - parsedNumber.integerLen;\n  const fractionSize = Math.min(Math.max(minFrac, fractionLen), maxFrac);\n  // The index of the digit to where rounding is to occur\n  let roundAt = fractionSize + parsedNumber.integerLen;\n  let digit = digits[roundAt];\n  if (roundAt > 0) {\n    // Drop fractional digits beyond `roundAt`\n    digits.splice(Math.max(parsedNumber.integerLen, roundAt));\n    // Set non-fractional digits beyond `roundAt` to 0\n    for (let j = roundAt; j < digits.length; j++) {\n      digits[j] = 0;\n    }\n  } else {\n    // We rounded to zero so reset the parsedNumber\n    fractionLen = Math.max(0, fractionLen);\n    parsedNumber.integerLen = 1;\n    digits.length = Math.max(1, roundAt = fractionSize + 1);\n    digits[0] = 0;\n    for (let i = 1; i < roundAt; i++) digits[i] = 0;\n  }\n  if (digit >= 5) {\n    if (roundAt - 1 < 0) {\n      for (let k = 0; k > roundAt; k--) {\n        digits.unshift(0);\n        parsedNumber.integerLen++;\n      }\n      digits.unshift(1);\n      parsedNumber.integerLen++;\n    } else {\n      digits[roundAt - 1]++;\n    }\n  }\n  // Pad out with zeros to get the required fraction length\n  for (; fractionLen < Math.max(0, fractionSize); fractionLen++) digits.push(0);\n  let dropTrailingZeros = fractionSize !== 0;\n  // Minimal length = nb of decimals required + current nb of integers\n  // Any number besides that is optional and can be removed if it's a trailing 0\n  const minLen = minFrac + parsedNumber.integerLen;\n  // Do any carrying, e.g. a digit was rounded up to 10\n  const carry = digits.reduceRight(function (carry, d, i, digits) {\n    d = d + carry;\n    digits[i] = d < 10 ? d : d - 10; // d % 10\n    if (dropTrailingZeros) {\n      // Do not keep meaningless fractional trailing zeros (e.g. 15.52000 --> 15.52)\n      if (digits[i] === 0 && i >= minLen) {\n        digits.pop();\n      } else {\n        dropTrailingZeros = false;\n      }\n    }\n    return d >= 10 ? 1 : 0; // Math.floor(d / 10);\n  }, 0);\n  if (carry) {\n    digits.unshift(carry);\n    parsedNumber.integerLen++;\n  }\n}\nfunction parseIntAutoRadix(text) {\n  const result = parseInt(text);\n  if (isNaN(result)) {\n    throw new Error('Invalid integer literal when parsing ' + text);\n  }\n  return result;\n}\n\n/**\n * @publicApi\n */\nclass NgLocalization {\n  static {\n    this.ɵfac = function NgLocalization_Factory(t) {\n      return new (t || NgLocalization)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NgLocalization,\n      factory: function NgLocalization_Factory(t) {\n        let r = null;\n        if (t) {\n          r = new t();\n        } else {\n          r = (locale => new NgLocaleLocalization(locale))(i0.ɵɵinject(LOCALE_ID));\n        }\n        return r;\n      },\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgLocalization, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: locale => new NgLocaleLocalization(locale),\n      deps: [LOCALE_ID]\n    }]\n  }], null, null);\n})();\n/**\n * Returns the plural category for a given value.\n * - \"=value\" when the case exists,\n * - the plural category otherwise\n */\nfunction getPluralCategory(value, cases, ngLocalization, locale) {\n  let key = `=${value}`;\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n  key = ngLocalization.getPluralCategory(value, locale);\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n  if (cases.indexOf('other') > -1) {\n    return 'other';\n  }\n  throw new Error(`No plural message found for value \"${value}\"`);\n}\n/**\n * Returns the plural case based on the locale\n *\n * @publicApi\n */\nclass NgLocaleLocalization extends NgLocalization {\n  constructor(locale) {\n    super();\n    this.locale = locale;\n  }\n  getPluralCategory(value, locale) {\n    const plural = getLocalePluralCase(locale || this.locale)(value);\n    switch (plural) {\n      case Plural.Zero:\n        return 'zero';\n      case Plural.One:\n        return 'one';\n      case Plural.Two:\n        return 'two';\n      case Plural.Few:\n        return 'few';\n      case Plural.Many:\n        return 'many';\n      default:\n        return 'other';\n    }\n  }\n  static {\n    this.ɵfac = function NgLocaleLocalization_Factory(t) {\n      return new (t || NgLocaleLocalization)(i0.ɵɵinject(LOCALE_ID));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NgLocaleLocalization,\n      factory: NgLocaleLocalization.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgLocaleLocalization, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LOCALE_ID]\n    }]\n  }], null);\n})();\n\n/**\n * Register global data to be used internally by Angular. See the\n * [\"I18n guide\"](guide/i18n-common-format-data-locale) to know how to import additional locale\n * data.\n *\n * The signature registerLocaleData(data: any, extraData?: any) is deprecated since v5.1\n *\n * @publicApi\n */\nfunction registerLocaleData(data, localeId, extraData) {\n  return ɵregisterLocaleData(data, localeId, extraData);\n}\nfunction parseCookieValue(cookieStr, name) {\n  name = encodeURIComponent(name);\n  for (const cookie of cookieStr.split(';')) {\n    const eqIndex = cookie.indexOf('=');\n    const [cookieName, cookieValue] = eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)];\n    if (cookieName.trim() === name) {\n      return decodeURIComponent(cookieValue);\n    }\n  }\n  return null;\n}\nconst WS_REGEXP = /\\s+/;\nconst EMPTY_ARRAY = [];\n/**\n * @ngModule CommonModule\n *\n * @usageNotes\n * ```\n *     <some-element [ngClass]=\"'first second'\">...</some-element>\n *\n *     <some-element [ngClass]=\"['first', 'second']\">...</some-element>\n *\n *     <some-element [ngClass]=\"{'first': true, 'second': true, 'third': false}\">...</some-element>\n *\n *     <some-element [ngClass]=\"stringExp|arrayExp|objExp\">...</some-element>\n *\n *     <some-element [ngClass]=\"{'class1 class2 class3' : true}\">...</some-element>\n * ```\n *\n * @description\n *\n * Adds and removes CSS classes on an HTML element.\n *\n * The CSS classes are updated as follows, depending on the type of the expression evaluation:\n * - `string` - the CSS classes listed in the string (space delimited) are added,\n * - `Array` - the CSS classes declared as Array elements are added,\n * - `Object` - keys are CSS classes that get added when the expression given in the value\n *              evaluates to a truthy value, otherwise they are removed.\n *\n * @publicApi\n */\nclass NgClass {\n  constructor(_ngEl, _renderer) {\n    this._ngEl = _ngEl;\n    this._renderer = _renderer;\n    this.initialClasses = EMPTY_ARRAY;\n    this.stateMap = new Map();\n  }\n  set klass(value) {\n    this.initialClasses = value != null ? value.trim().split(WS_REGEXP) : EMPTY_ARRAY;\n  }\n  set ngClass(value) {\n    this.rawClass = typeof value === 'string' ? value.trim().split(WS_REGEXP) : value;\n  }\n  /*\n  The NgClass directive uses the custom change detection algorithm for its inputs. The custom\n  algorithm is necessary since inputs are represented as complex object or arrays that need to be\n  deeply-compared.\n     This algorithm is perf-sensitive since NgClass is used very frequently and its poor performance\n  might negatively impact runtime performance of the entire change detection cycle. The design of\n  this algorithm is making sure that:\n  - there is no unnecessary DOM manipulation (CSS classes are added / removed from the DOM only when\n  needed), even if references to bound objects change;\n  - there is no memory allocation if nothing changes (even relatively modest memory allocation\n  during the change detection cycle can result in GC pauses for some of the CD cycles).\n     The algorithm works by iterating over the set of bound classes, staring with [class] binding and\n  then going over [ngClass] binding. For each CSS class name:\n  - check if it was seen before (this information is tracked in the state map) and if its value\n  changed;\n  - mark it as \"touched\" - names that are not marked are not present in the latest set of binding\n  and we can remove such class name from the internal data structures;\n     After iteration over all the CSS class names we've got data structure with all the information\n  necessary to synchronize changes to the DOM - it is enough to iterate over the state map, flush\n  changes to the DOM and reset internal data structures so those are ready for the next change\n  detection cycle.\n   */\n  ngDoCheck() {\n    // classes from the [class] binding\n    for (const klass of this.initialClasses) {\n      this._updateState(klass, true);\n    }\n    // classes from the [ngClass] binding\n    const rawClass = this.rawClass;\n    if (Array.isArray(rawClass) || rawClass instanceof Set) {\n      for (const klass of rawClass) {\n        this._updateState(klass, true);\n      }\n    } else if (rawClass != null) {\n      for (const klass of Object.keys(rawClass)) {\n        this._updateState(klass, Boolean(rawClass[klass]));\n      }\n    }\n    this._applyStateDiff();\n  }\n  _updateState(klass, nextEnabled) {\n    const state = this.stateMap.get(klass);\n    if (state !== undefined) {\n      if (state.enabled !== nextEnabled) {\n        state.changed = true;\n        state.enabled = nextEnabled;\n      }\n      state.touched = true;\n    } else {\n      this.stateMap.set(klass, {\n        enabled: nextEnabled,\n        changed: true,\n        touched: true\n      });\n    }\n  }\n  _applyStateDiff() {\n    for (const stateEntry of this.stateMap) {\n      const klass = stateEntry[0];\n      const state = stateEntry[1];\n      if (state.changed) {\n        this._toggleClass(klass, state.enabled);\n        state.changed = false;\n      } else if (!state.touched) {\n        // A class that was previously active got removed from the new collection of classes -\n        // remove from the DOM as well.\n        if (state.enabled) {\n          this._toggleClass(klass, false);\n        }\n        this.stateMap.delete(klass);\n      }\n      state.touched = false;\n    }\n  }\n  _toggleClass(klass, enabled) {\n    if (ngDevMode) {\n      if (typeof klass !== 'string') {\n        throw new Error(`NgClass can only toggle CSS classes expressed as strings, got ${ɵstringify(klass)}`);\n      }\n    }\n    klass = klass.trim();\n    if (klass.length > 0) {\n      klass.split(WS_REGEXP).forEach(klass => {\n        if (enabled) {\n          this._renderer.addClass(this._ngEl.nativeElement, klass);\n        } else {\n          this._renderer.removeClass(this._ngEl.nativeElement, klass);\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function NgClass_Factory(t) {\n      return new (t || NgClass)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgClass,\n      selectors: [[\"\", \"ngClass\", \"\"]],\n      inputs: {\n        klass: [i0.ɵɵInputFlags.None, \"class\", \"klass\"],\n        ngClass: \"ngClass\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgClass, [{\n    type: Directive,\n    args: [{\n      selector: '[ngClass]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }], {\n    klass: [{\n      type: Input,\n      args: ['class']\n    }],\n    ngClass: [{\n      type: Input,\n      args: ['ngClass']\n    }]\n  });\n})();\n\n/**\n * Instantiates a {@link Component} type and inserts its Host View into the current View.\n * `NgComponentOutlet` provides a declarative approach for dynamic component creation.\n *\n * `NgComponentOutlet` requires a component type, if a falsy value is set the view will clear and\n * any existing component will be destroyed.\n *\n * @usageNotes\n *\n * ### Fine tune control\n *\n * You can control the component creation process by using the following optional attributes:\n *\n * * `ngComponentOutletInputs`: Optional component inputs object, which will be bind to the\n * component.\n *\n * * `ngComponentOutletInjector`: Optional custom {@link Injector} that will be used as parent for\n * the Component. Defaults to the injector of the current view container.\n *\n * * `ngComponentOutletContent`: Optional list of projectable nodes to insert into the content\n * section of the component, if it exists.\n *\n * * `ngComponentOutletNgModule`: Optional NgModule class reference to allow loading another\n * module dynamically, then loading a component from that module.\n *\n * * `ngComponentOutletNgModuleFactory`: Deprecated config option that allows providing optional\n * NgModule factory to allow loading another module dynamically, then loading a component from that\n * module. Use `ngComponentOutletNgModule` instead.\n *\n * ### Syntax\n *\n * Simple\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression\"></ng-container>\n * ```\n *\n * With inputs\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   inputs: inputsExpression;\">\n * </ng-container>\n * ```\n *\n * Customized injector/content\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   injector: injectorExpression;\n *                                   content: contentNodesExpression;\">\n * </ng-container>\n * ```\n *\n * Customized NgModule reference\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   ngModule: ngModuleClass;\">\n * </ng-container>\n * ```\n *\n * ### A simple example\n *\n * {@example common/ngComponentOutlet/ts/module.ts region='SimpleExample'}\n *\n * A more complete example with additional options:\n *\n * {@example common/ngComponentOutlet/ts/module.ts region='CompleteExample'}\n *\n * @publicApi\n * @ngModule CommonModule\n */\nclass NgComponentOutlet {\n  constructor(_viewContainerRef) {\n    this._viewContainerRef = _viewContainerRef;\n    this.ngComponentOutlet = null;\n    /**\n     * A helper data structure that allows us to track inputs that were part of the\n     * ngComponentOutletInputs expression. Tracking inputs is necessary for proper removal of ones\n     * that are no longer referenced.\n     */\n    this._inputsUsed = new Map();\n  }\n  _needToReCreateNgModuleInstance(changes) {\n    // Note: square brackets property accessor is safe for Closure compiler optimizations (the\n    // `changes` argument of the `ngOnChanges` lifecycle hook retains the names of the fields that\n    // were changed).\n    return changes['ngComponentOutletNgModule'] !== undefined || changes['ngComponentOutletNgModuleFactory'] !== undefined;\n  }\n  _needToReCreateComponentInstance(changes) {\n    // Note: square brackets property accessor is safe for Closure compiler optimizations (the\n    // `changes` argument of the `ngOnChanges` lifecycle hook retains the names of the fields that\n    // were changed).\n    return changes['ngComponentOutlet'] !== undefined || changes['ngComponentOutletContent'] !== undefined || changes['ngComponentOutletInjector'] !== undefined || this._needToReCreateNgModuleInstance(changes);\n  }\n  /** @nodoc */\n  ngOnChanges(changes) {\n    if (this._needToReCreateComponentInstance(changes)) {\n      this._viewContainerRef.clear();\n      this._inputsUsed.clear();\n      this._componentRef = undefined;\n      if (this.ngComponentOutlet) {\n        const injector = this.ngComponentOutletInjector || this._viewContainerRef.parentInjector;\n        if (this._needToReCreateNgModuleInstance(changes)) {\n          this._moduleRef?.destroy();\n          if (this.ngComponentOutletNgModule) {\n            this._moduleRef = createNgModule(this.ngComponentOutletNgModule, getParentInjector(injector));\n          } else if (this.ngComponentOutletNgModuleFactory) {\n            this._moduleRef = this.ngComponentOutletNgModuleFactory.create(getParentInjector(injector));\n          } else {\n            this._moduleRef = undefined;\n          }\n        }\n        this._componentRef = this._viewContainerRef.createComponent(this.ngComponentOutlet, {\n          injector,\n          ngModuleRef: this._moduleRef,\n          projectableNodes: this.ngComponentOutletContent\n        });\n      }\n    }\n  }\n  /** @nodoc */\n  ngDoCheck() {\n    if (this._componentRef) {\n      if (this.ngComponentOutletInputs) {\n        for (const inputName of Object.keys(this.ngComponentOutletInputs)) {\n          this._inputsUsed.set(inputName, true);\n        }\n      }\n      this._applyInputStateDiff(this._componentRef);\n    }\n  }\n  /** @nodoc */\n  ngOnDestroy() {\n    this._moduleRef?.destroy();\n  }\n  _applyInputStateDiff(componentRef) {\n    for (const [inputName, touched] of this._inputsUsed) {\n      if (!touched) {\n        // The input that was previously active no longer exists and needs to be set to undefined.\n        componentRef.setInput(inputName, undefined);\n        this._inputsUsed.delete(inputName);\n      } else {\n        // Since touched is true, it can be asserted that the inputs object is not empty.\n        componentRef.setInput(inputName, this.ngComponentOutletInputs[inputName]);\n        this._inputsUsed.set(inputName, false);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function NgComponentOutlet_Factory(t) {\n      return new (t || NgComponentOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgComponentOutlet,\n      selectors: [[\"\", \"ngComponentOutlet\", \"\"]],\n      inputs: {\n        ngComponentOutlet: \"ngComponentOutlet\",\n        ngComponentOutletInputs: \"ngComponentOutletInputs\",\n        ngComponentOutletInjector: \"ngComponentOutletInjector\",\n        ngComponentOutletContent: \"ngComponentOutletContent\",\n        ngComponentOutletNgModule: \"ngComponentOutletNgModule\",\n        ngComponentOutletNgModuleFactory: \"ngComponentOutletNgModuleFactory\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgComponentOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[ngComponentOutlet]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }], {\n    ngComponentOutlet: [{\n      type: Input\n    }],\n    ngComponentOutletInputs: [{\n      type: Input\n    }],\n    ngComponentOutletInjector: [{\n      type: Input\n    }],\n    ngComponentOutletContent: [{\n      type: Input\n    }],\n    ngComponentOutletNgModule: [{\n      type: Input\n    }],\n    ngComponentOutletNgModuleFactory: [{\n      type: Input\n    }]\n  });\n})();\n// Helper function that returns an Injector instance of a parent NgModule.\nfunction getParentInjector(injector) {\n  const parentNgModule = injector.get(NgModuleRef);\n  return parentNgModule.injector;\n}\n\n/**\n * @publicApi\n */\nclass NgForOfContext {\n  constructor($implicit, ngForOf, index, count) {\n    this.$implicit = $implicit;\n    this.ngForOf = ngForOf;\n    this.index = index;\n    this.count = count;\n  }\n  get first() {\n    return this.index === 0;\n  }\n  get last() {\n    return this.index === this.count - 1;\n  }\n  get even() {\n    return this.index % 2 === 0;\n  }\n  get odd() {\n    return !this.even;\n  }\n}\n/**\n * A [structural directive](guide/structural-directives) that renders\n * a template for each item in a collection.\n * The directive is placed on an element, which becomes the parent\n * of the cloned templates.\n *\n * The `ngForOf` directive is generally used in the\n * [shorthand form](guide/structural-directives#asterisk) `*ngFor`.\n * In this form, the template to be rendered for each iteration is the content\n * of an anchor element containing the directive.\n *\n * The following example shows the shorthand syntax with some options,\n * contained in an `<li>` element.\n *\n * ```\n * <li *ngFor=\"let item of items; index as i; trackBy: trackByFn\">...</li>\n * ```\n *\n * The shorthand form expands into a long form that uses the `ngForOf` selector\n * on an `<ng-template>` element.\n * The content of the `<ng-template>` element is the `<li>` element that held the\n * short-form directive.\n *\n * Here is the expanded version of the short-form example.\n *\n * ```\n * <ng-template ngFor let-item [ngForOf]=\"items\" let-i=\"index\" [ngForTrackBy]=\"trackByFn\">\n *   <li>...</li>\n * </ng-template>\n * ```\n *\n * Angular automatically expands the shorthand syntax as it compiles the template.\n * The context for each embedded view is logically merged to the current component\n * context according to its lexical position.\n *\n * When using the shorthand syntax, Angular allows only [one structural directive\n * on an element](guide/structural-directives#one-per-element).\n * If you want to iterate conditionally, for example,\n * put the `*ngIf` on a container element that wraps the `*ngFor` element.\n * For further discussion, see\n * [Structural Directives](guide/structural-directives#one-per-element).\n *\n * @usageNotes\n *\n * ### Local variables\n *\n * `NgForOf` provides exported values that can be aliased to local variables.\n * For example:\n *\n *  ```\n * <li *ngFor=\"let user of users; index as i; first as isFirst\">\n *    {{i}}/{{users.length}}. {{user}} <span *ngIf=\"isFirst\">default</span>\n * </li>\n * ```\n *\n * The following exported values can be aliased to local variables:\n *\n * - `$implicit: T`: The value of the individual items in the iterable (`ngForOf`).\n * - `ngForOf: NgIterable<T>`: The value of the iterable expression. Useful when the expression is\n * more complex then a property access, for example when using the async pipe (`userStreams |\n * async`).\n * - `index: number`: The index of the current item in the iterable.\n * - `count: number`: The length of the iterable.\n * - `first: boolean`: True when the item is the first item in the iterable.\n * - `last: boolean`: True when the item is the last item in the iterable.\n * - `even: boolean`: True when the item has an even index in the iterable.\n * - `odd: boolean`: True when the item has an odd index in the iterable.\n *\n * ### Change propagation\n *\n * When the contents of the iterator changes, `NgForOf` makes the corresponding changes to the DOM:\n *\n * * When an item is added, a new instance of the template is added to the DOM.\n * * When an item is removed, its template instance is removed from the DOM.\n * * When items are reordered, their respective templates are reordered in the DOM.\n *\n * Angular uses object identity to track insertions and deletions within the iterator and reproduce\n * those changes in the DOM. This has important implications for animations and any stateful\n * controls that are present, such as `<input>` elements that accept user input. Inserted rows can\n * be animated in, deleted rows can be animated out, and unchanged rows retain any unsaved state\n * such as user input.\n * For more on animations, see [Transitions and Triggers](guide/transition-and-triggers).\n *\n * The identities of elements in the iterator can change while the data does not.\n * This can happen, for example, if the iterator is produced from an RPC to the server, and that\n * RPC is re-run. Even if the data hasn't changed, the second response produces objects with\n * different identities, and Angular must tear down the entire DOM and rebuild it (as if all old\n * elements were deleted and all new elements inserted).\n *\n * To avoid this expensive operation, you can customize the default tracking algorithm.\n * by supplying the `trackBy` option to `NgForOf`.\n * `trackBy` takes a function that has two arguments: `index` and `item`.\n * If `trackBy` is given, Angular tracks changes by the return value of the function.\n *\n * @see [Structural Directives](guide/structural-directives)\n * @ngModule CommonModule\n * @publicApi\n */\nclass NgForOf {\n  /**\n   * The value of the iterable expression, which can be used as a\n   * [template input variable](guide/structural-directives#shorthand).\n   */\n  set ngForOf(ngForOf) {\n    this._ngForOf = ngForOf;\n    this._ngForOfDirty = true;\n  }\n  /**\n   * Specifies a custom `TrackByFunction` to compute the identity of items in an iterable.\n   *\n   * If a custom `TrackByFunction` is not provided, `NgForOf` will use the item's [object\n   * identity](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is)\n   * as the key.\n   *\n   * `NgForOf` uses the computed key to associate items in an iterable with DOM elements\n   * it produces for these items.\n   *\n   * A custom `TrackByFunction` is useful to provide good user experience in cases when items in an\n   * iterable rendered using `NgForOf` have a natural identifier (for example, custom ID or a\n   * primary key), and this iterable could be updated with new object instances that still\n   * represent the same underlying entity (for example, when data is re-fetched from the server,\n   * and the iterable is recreated and re-rendered, but most of the data is still the same).\n   *\n   * @see {@link TrackByFunction}\n   */\n  set ngForTrackBy(fn) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n      console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}. ` + `See https://angular.io/api/common/NgForOf#change-propagation for more information.`);\n    }\n    this._trackByFn = fn;\n  }\n  get ngForTrackBy() {\n    return this._trackByFn;\n  }\n  constructor(_viewContainer, _template, _differs) {\n    this._viewContainer = _viewContainer;\n    this._template = _template;\n    this._differs = _differs;\n    this._ngForOf = null;\n    this._ngForOfDirty = true;\n    this._differ = null;\n  }\n  /**\n   * A reference to the template that is stamped out for each item in the iterable.\n   * @see [template reference variable](guide/template-reference-variables)\n   */\n  set ngForTemplate(value) {\n    // TODO(TS2.1): make TemplateRef<Partial<NgForRowOf<T>>> once we move to TS v2.1\n    // The current type is too restrictive; a template that just uses index, for example,\n    // should be acceptable.\n    if (value) {\n      this._template = value;\n    }\n  }\n  /**\n   * Applies the changes when needed.\n   * @nodoc\n   */\n  ngDoCheck() {\n    if (this._ngForOfDirty) {\n      this._ngForOfDirty = false;\n      // React on ngForOf changes only once all inputs have been initialized\n      const value = this._ngForOf;\n      if (!this._differ && value) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          try {\n            // CAUTION: this logic is duplicated for production mode below, as the try-catch\n            // is only present in development builds.\n            this._differ = this._differs.find(value).create(this.ngForTrackBy);\n          } catch {\n            let errorMessage = `Cannot find a differ supporting object '${value}' of type '` + `${getTypeName(value)}'. NgFor only supports binding to Iterables, such as Arrays.`;\n            if (typeof value === 'object') {\n              errorMessage += ' Did you mean to use the keyvalue pipe?';\n            }\n            throw new ɵRuntimeError(-2200 /* RuntimeErrorCode.NG_FOR_MISSING_DIFFER */, errorMessage);\n          }\n        } else {\n          // CAUTION: this logic is duplicated for development mode above, as the try-catch\n          // is only present in development builds.\n          this._differ = this._differs.find(value).create(this.ngForTrackBy);\n        }\n      }\n    }\n    if (this._differ) {\n      const changes = this._differ.diff(this._ngForOf);\n      if (changes) this._applyChanges(changes);\n    }\n  }\n  _applyChanges(changes) {\n    const viewContainer = this._viewContainer;\n    changes.forEachOperation((item, adjustedPreviousIndex, currentIndex) => {\n      if (item.previousIndex == null) {\n        // NgForOf is never \"null\" or \"undefined\" here because the differ detected\n        // that a new item needs to be inserted from the iterable. This implies that\n        // there is an iterable value for \"_ngForOf\".\n        viewContainer.createEmbeddedView(this._template, new NgForOfContext(item.item, this._ngForOf, -1, -1), currentIndex === null ? undefined : currentIndex);\n      } else if (currentIndex == null) {\n        viewContainer.remove(adjustedPreviousIndex === null ? undefined : adjustedPreviousIndex);\n      } else if (adjustedPreviousIndex !== null) {\n        const view = viewContainer.get(adjustedPreviousIndex);\n        viewContainer.move(view, currentIndex);\n        applyViewChange(view, item);\n      }\n    });\n    for (let i = 0, ilen = viewContainer.length; i < ilen; i++) {\n      const viewRef = viewContainer.get(i);\n      const context = viewRef.context;\n      context.index = i;\n      context.count = ilen;\n      context.ngForOf = this._ngForOf;\n    }\n    changes.forEachIdentityChange(record => {\n      const viewRef = viewContainer.get(record.currentIndex);\n      applyViewChange(viewRef, record);\n    });\n  }\n  /**\n   * Asserts the correct type of the context for the template that `NgForOf` will render.\n   *\n   * The presence of this method is a signal to the Ivy template type-check compiler that the\n   * `NgForOf` structural directive renders its template with a specific context type.\n   */\n  static ngTemplateContextGuard(dir, ctx) {\n    return true;\n  }\n  static {\n    this.ɵfac = function NgForOf_Factory(t) {\n      return new (t || NgForOf)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgForOf,\n      selectors: [[\"\", \"ngFor\", \"\", \"ngForOf\", \"\"]],\n      inputs: {\n        ngForOf: \"ngForOf\",\n        ngForTrackBy: \"ngForTrackBy\",\n        ngForTemplate: \"ngForTemplate\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgForOf, [{\n    type: Directive,\n    args: [{\n      selector: '[ngFor][ngForOf]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.TemplateRef\n  }, {\n    type: i0.IterableDiffers\n  }], {\n    ngForOf: [{\n      type: Input\n    }],\n    ngForTrackBy: [{\n      type: Input\n    }],\n    ngForTemplate: [{\n      type: Input\n    }]\n  });\n})();\nfunction applyViewChange(view, record) {\n  view.context.$implicit = record.item;\n}\nfunction getTypeName(type) {\n  return type['name'] || typeof type;\n}\n\n/**\n * A structural directive that conditionally includes a template based on the value of\n * an expression coerced to Boolean.\n * When the expression evaluates to true, Angular renders the template\n * provided in a `then` clause, and when  false or null,\n * Angular renders the template provided in an optional `else` clause. The default\n * template for the `else` clause is blank.\n *\n * A [shorthand form](guide/structural-directives#asterisk) of the directive,\n * `*ngIf=\"condition\"`, is generally used, provided\n * as an attribute of the anchor element for the inserted template.\n * Angular expands this into a more explicit version, in which the anchor element\n * is contained in an `<ng-template>` element.\n *\n * Simple form with shorthand syntax:\n *\n * ```\n * <div *ngIf=\"condition\">Content to render when condition is true.</div>\n * ```\n *\n * Simple form with expanded syntax:\n *\n * ```\n * <ng-template [ngIf]=\"condition\"><div>Content to render when condition is\n * true.</div></ng-template>\n * ```\n *\n * Form with an \"else\" block:\n *\n * ```\n * <div *ngIf=\"condition; else elseBlock\">Content to render when condition is true.</div>\n * <ng-template #elseBlock>Content to render when condition is false.</ng-template>\n * ```\n *\n * Shorthand form with \"then\" and \"else\" blocks:\n *\n * ```\n * <div *ngIf=\"condition; then thenBlock else elseBlock\"></div>\n * <ng-template #thenBlock>Content to render when condition is true.</ng-template>\n * <ng-template #elseBlock>Content to render when condition is false.</ng-template>\n * ```\n *\n * Form with storing the value locally:\n *\n * ```\n * <div *ngIf=\"condition as value; else elseBlock\">{{value}}</div>\n * <ng-template #elseBlock>Content to render when value is null.</ng-template>\n * ```\n *\n * @usageNotes\n *\n * The `*ngIf` directive is most commonly used to conditionally show an inline template,\n * as seen in the following  example.\n * The default `else` template is blank.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfSimple'}\n *\n * ### Showing an alternative template using `else`\n *\n * To display a template when `expression` evaluates to false, use an `else` template\n * binding as shown in the following example.\n * The `else` binding points to an `<ng-template>`  element labeled `#elseBlock`.\n * The template can be defined anywhere in the component view, but is typically placed right after\n * `ngIf` for readability.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfElse'}\n *\n * ### Using an external `then` template\n *\n * In the previous example, the then-clause template is specified inline, as the content of the\n * tag that contains the `ngIf` directive. You can also specify a template that is defined\n * externally, by referencing a labeled `<ng-template>` element. When you do this, you can\n * change which template to use at runtime, as shown in the following example.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfThenElse'}\n *\n * ### Storing a conditional result in a variable\n *\n * You might want to show a set of properties from the same object. If you are waiting\n * for asynchronous data, the object can be undefined.\n * In this case, you can use `ngIf` and store the result of the condition in a local\n * variable as shown in the following example.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfAs'}\n *\n * This code uses only one `AsyncPipe`, so only one subscription is created.\n * The conditional statement stores the result of `userStream|async` in the local variable `user`.\n * You can then bind the local `user` repeatedly.\n *\n * The conditional displays the data only if `userStream` returns a value,\n * so you don't need to use the\n * safe-navigation-operator (`?.`)\n * to guard against null values when accessing properties.\n * You can display an alternative template while waiting for the data.\n *\n * ### Shorthand syntax\n *\n * The shorthand syntax `*ngIf` expands into two separate template specifications\n * for the \"then\" and \"else\" clauses. For example, consider the following shorthand statement,\n * that is meant to show a loading page while waiting for data to be loaded.\n *\n * ```\n * <div class=\"hero-list\" *ngIf=\"heroes else loading\">\n *  ...\n * </div>\n *\n * <ng-template #loading>\n *  <div>Loading...</div>\n * </ng-template>\n * ```\n *\n * You can see that the \"else\" clause references the `<ng-template>`\n * with the `#loading` label, and the template for the \"then\" clause\n * is provided as the content of the anchor element.\n *\n * However, when Angular expands the shorthand syntax, it creates\n * another `<ng-template>` tag, with `ngIf` and `ngIfElse` directives.\n * The anchor element containing the template for the \"then\" clause becomes\n * the content of this unlabeled `<ng-template>` tag.\n *\n * ```\n * <ng-template [ngIf]=\"heroes\" [ngIfElse]=\"loading\">\n *  <div class=\"hero-list\">\n *   ...\n *  </div>\n * </ng-template>\n *\n * <ng-template #loading>\n *  <div>Loading...</div>\n * </ng-template>\n * ```\n *\n * The presence of the implicit template object has implications for the nesting of\n * structural directives. For more on this subject, see\n * [Structural Directives](guide/structural-directives#one-per-element).\n *\n * @ngModule CommonModule\n * @publicApi\n */\nclass NgIf {\n  constructor(_viewContainer, templateRef) {\n    this._viewContainer = _viewContainer;\n    this._context = new NgIfContext();\n    this._thenTemplateRef = null;\n    this._elseTemplateRef = null;\n    this._thenViewRef = null;\n    this._elseViewRef = null;\n    this._thenTemplateRef = templateRef;\n  }\n  /**\n   * The Boolean expression to evaluate as the condition for showing a template.\n   */\n  set ngIf(condition) {\n    this._context.$implicit = this._context.ngIf = condition;\n    this._updateView();\n  }\n  /**\n   * A template to show if the condition expression evaluates to true.\n   */\n  set ngIfThen(templateRef) {\n    assertTemplate('ngIfThen', templateRef);\n    this._thenTemplateRef = templateRef;\n    this._thenViewRef = null; // clear previous view if any.\n    this._updateView();\n  }\n  /**\n   * A template to show if the condition expression evaluates to false.\n   */\n  set ngIfElse(templateRef) {\n    assertTemplate('ngIfElse', templateRef);\n    this._elseTemplateRef = templateRef;\n    this._elseViewRef = null; // clear previous view if any.\n    this._updateView();\n  }\n  _updateView() {\n    if (this._context.$implicit) {\n      if (!this._thenViewRef) {\n        this._viewContainer.clear();\n        this._elseViewRef = null;\n        if (this._thenTemplateRef) {\n          this._thenViewRef = this._viewContainer.createEmbeddedView(this._thenTemplateRef, this._context);\n        }\n      }\n    } else {\n      if (!this._elseViewRef) {\n        this._viewContainer.clear();\n        this._thenViewRef = null;\n        if (this._elseTemplateRef) {\n          this._elseViewRef = this._viewContainer.createEmbeddedView(this._elseTemplateRef, this._context);\n        }\n      }\n    }\n  }\n  /**\n   * Asserts the correct type of the context for the template that `NgIf` will render.\n   *\n   * The presence of this method is a signal to the Ivy template type-check compiler that the\n   * `NgIf` structural directive renders its template with a specific context type.\n   */\n  static ngTemplateContextGuard(dir, ctx) {\n    return true;\n  }\n  static {\n    this.ɵfac = function NgIf_Factory(t) {\n      return new (t || NgIf)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgIf,\n      selectors: [[\"\", \"ngIf\", \"\"]],\n      inputs: {\n        ngIf: \"ngIf\",\n        ngIfThen: \"ngIfThen\",\n        ngIfElse: \"ngIfElse\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgIf, [{\n    type: Directive,\n    args: [{\n      selector: '[ngIf]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.TemplateRef\n  }], {\n    ngIf: [{\n      type: Input\n    }],\n    ngIfThen: [{\n      type: Input\n    }],\n    ngIfElse: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @publicApi\n */\nclass NgIfContext {\n  constructor() {\n    this.$implicit = null;\n    this.ngIf = null;\n  }\n}\nfunction assertTemplate(property, templateRef) {\n  const isTemplateRefOrNull = !!(!templateRef || templateRef.createEmbeddedView);\n  if (!isTemplateRefOrNull) {\n    throw new Error(`${property} must be a TemplateRef, but received '${ɵstringify(templateRef)}'.`);\n  }\n}\n\n/**\n * A constant indicating a type of comparison that NgSwitch uses to match cases. Extracted to a\n * separate file to facilitate G3 patches.\n */\nconst NG_SWITCH_USE_STRICT_EQUALS = true;\nclass SwitchView {\n  constructor(_viewContainerRef, _templateRef) {\n    this._viewContainerRef = _viewContainerRef;\n    this._templateRef = _templateRef;\n    this._created = false;\n  }\n  create() {\n    this._created = true;\n    this._viewContainerRef.createEmbeddedView(this._templateRef);\n  }\n  destroy() {\n    this._created = false;\n    this._viewContainerRef.clear();\n  }\n  enforceState(created) {\n    if (created && !this._created) {\n      this.create();\n    } else if (!created && this._created) {\n      this.destroy();\n    }\n  }\n}\n/**\n * @ngModule CommonModule\n *\n * @description\n * The `[ngSwitch]` directive on a container specifies an expression to match against.\n * The expressions to match are provided by `ngSwitchCase` directives on views within the container.\n * - Every view that matches is rendered.\n * - If there are no matches, a view with the `ngSwitchDefault` directive is rendered.\n * - Elements within the `[NgSwitch]` statement but outside of any `NgSwitchCase`\n * or `ngSwitchDefault` directive are preserved at the location.\n *\n * @usageNotes\n * Define a container element for the directive, and specify the switch expression\n * to match against as an attribute:\n *\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n * ```\n *\n * Within the container, `*ngSwitchCase` statements specify the match expressions\n * as attributes. Include `*ngSwitchDefault` as the final case.\n *\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *    <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n * ...\n *    <some-element *ngSwitchDefault>...</some-element>\n * </container-element>\n * ```\n *\n * ### Usage Examples\n *\n * The following example shows how to use more than one case to display the same view:\n *\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <!-- the same view can be shown in more than one case -->\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *   <some-element *ngSwitchCase=\"match_expression_2\">...</some-element>\n *   <some-other-element *ngSwitchCase=\"match_expression_3\">...</some-other-element>\n *   <!--default case when there are no matches -->\n *   <some-element *ngSwitchDefault>...</some-element>\n * </container-element>\n * ```\n *\n * The following example shows how cases can be nested:\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *       <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *       <some-element *ngSwitchCase=\"match_expression_2\">...</some-element>\n *       <some-other-element *ngSwitchCase=\"match_expression_3\">...</some-other-element>\n *       <ng-container *ngSwitchCase=\"match_expression_3\">\n *         <!-- use a ng-container to group multiple root nodes -->\n *         <inner-element></inner-element>\n *         <inner-other-element></inner-other-element>\n *       </ng-container>\n *       <some-element *ngSwitchDefault>...</some-element>\n *     </container-element>\n * ```\n *\n * @publicApi\n * @see {@link NgSwitchCase}\n * @see {@link NgSwitchDefault}\n * @see [Structural Directives](guide/structural-directives)\n *\n */\nclass NgSwitch {\n  constructor() {\n    this._defaultViews = [];\n    this._defaultUsed = false;\n    this._caseCount = 0;\n    this._lastCaseCheckIndex = 0;\n    this._lastCasesMatched = false;\n  }\n  set ngSwitch(newValue) {\n    this._ngSwitch = newValue;\n    if (this._caseCount === 0) {\n      this._updateDefaultCases(true);\n    }\n  }\n  /** @internal */\n  _addCase() {\n    return this._caseCount++;\n  }\n  /** @internal */\n  _addDefault(view) {\n    this._defaultViews.push(view);\n  }\n  /** @internal */\n  _matchCase(value) {\n    const matched = NG_SWITCH_USE_STRICT_EQUALS ? value === this._ngSwitch : value == this._ngSwitch;\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && matched !== (value == this._ngSwitch)) {\n      console.warn(ɵformatRuntimeError(2001 /* RuntimeErrorCode.EQUALITY_NG_SWITCH_DIFFERENCE */, 'As of Angular v17 the NgSwitch directive uses strict equality comparison === instead of == to match different cases. ' + `Previously the case value \"${stringifyValue(value)}\" matched switch expression value \"${stringifyValue(this._ngSwitch)}\", but this is no longer the case with the stricter equality check. ` + 'Your comparison results return different results using === vs. == and you should adjust your ngSwitch expression and / or values to conform with the strict equality requirements.'));\n    }\n    this._lastCasesMatched ||= matched;\n    this._lastCaseCheckIndex++;\n    if (this._lastCaseCheckIndex === this._caseCount) {\n      this._updateDefaultCases(!this._lastCasesMatched);\n      this._lastCaseCheckIndex = 0;\n      this._lastCasesMatched = false;\n    }\n    return matched;\n  }\n  _updateDefaultCases(useDefault) {\n    if (this._defaultViews.length > 0 && useDefault !== this._defaultUsed) {\n      this._defaultUsed = useDefault;\n      for (const defaultView of this._defaultViews) {\n        defaultView.enforceState(useDefault);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function NgSwitch_Factory(t) {\n      return new (t || NgSwitch)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgSwitch,\n      selectors: [[\"\", \"ngSwitch\", \"\"]],\n      inputs: {\n        ngSwitch: \"ngSwitch\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgSwitch, [{\n    type: Directive,\n    args: [{\n      selector: '[ngSwitch]',\n      standalone: true\n    }]\n  }], null, {\n    ngSwitch: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @ngModule CommonModule\n *\n * @description\n * Provides a switch case expression to match against an enclosing `ngSwitch` expression.\n * When the expressions match, the given `NgSwitchCase` template is rendered.\n * If multiple match expressions match the switch expression value, all of them are displayed.\n *\n * @usageNotes\n *\n * Within a switch container, `*ngSwitchCase` statements specify the match expressions\n * as attributes. Include `*ngSwitchDefault` as the final case.\n *\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *   ...\n *   <some-element *ngSwitchDefault>...</some-element>\n * </container-element>\n * ```\n *\n * Each switch-case statement contains an in-line HTML template or template reference\n * that defines the subtree to be selected if the value of the match expression\n * matches the value of the switch expression.\n *\n * As of Angular v17 the NgSwitch directive uses strict equality comparison (`===`) instead of\n * loose equality (`==`) to match different cases.\n *\n * @publicApi\n * @see {@link NgSwitch}\n * @see {@link NgSwitchDefault}\n *\n */\nclass NgSwitchCase {\n  constructor(viewContainer, templateRef, ngSwitch) {\n    this.ngSwitch = ngSwitch;\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !ngSwitch) {\n      throwNgSwitchProviderNotFoundError('ngSwitchCase', 'NgSwitchCase');\n    }\n    ngSwitch._addCase();\n    this._view = new SwitchView(viewContainer, templateRef);\n  }\n  /**\n   * Performs case matching. For internal use only.\n   * @nodoc\n   */\n  ngDoCheck() {\n    this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase));\n  }\n  static {\n    this.ɵfac = function NgSwitchCase_Factory(t) {\n      return new (t || NgSwitchCase)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(NgSwitch, 9));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgSwitchCase,\n      selectors: [[\"\", \"ngSwitchCase\", \"\"]],\n      inputs: {\n        ngSwitchCase: \"ngSwitchCase\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgSwitchCase, [{\n    type: Directive,\n    args: [{\n      selector: '[ngSwitchCase]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.TemplateRef\n  }, {\n    type: NgSwitch,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Host\n    }]\n  }], {\n    ngSwitchCase: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @ngModule CommonModule\n *\n * @description\n *\n * Creates a view that is rendered when no `NgSwitchCase` expressions\n * match the `NgSwitch` expression.\n * This statement should be the final case in an `NgSwitch`.\n *\n * @publicApi\n * @see {@link NgSwitch}\n * @see {@link NgSwitchCase}\n *\n */\nclass NgSwitchDefault {\n  constructor(viewContainer, templateRef, ngSwitch) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !ngSwitch) {\n      throwNgSwitchProviderNotFoundError('ngSwitchDefault', 'NgSwitchDefault');\n    }\n    ngSwitch._addDefault(new SwitchView(viewContainer, templateRef));\n  }\n  static {\n    this.ɵfac = function NgSwitchDefault_Factory(t) {\n      return new (t || NgSwitchDefault)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(NgSwitch, 9));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgSwitchDefault,\n      selectors: [[\"\", \"ngSwitchDefault\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgSwitchDefault, [{\n    type: Directive,\n    args: [{\n      selector: '[ngSwitchDefault]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.TemplateRef\n  }, {\n    type: NgSwitch,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Host\n    }]\n  }], null);\n})();\nfunction throwNgSwitchProviderNotFoundError(attrName, directiveName) {\n  throw new ɵRuntimeError(2000 /* RuntimeErrorCode.PARENT_NG_SWITCH_NOT_FOUND */, `An element with the \"${attrName}\" attribute ` + `(matching the \"${directiveName}\" directive) must be located inside an element with the \"ngSwitch\" attribute ` + `(matching \"NgSwitch\" directive)`);\n}\nfunction stringifyValue(value) {\n  return typeof value === 'string' ? `'${value}'` : String(value);\n}\n\n/**\n * @ngModule CommonModule\n *\n * @usageNotes\n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">there is nothing</ng-template>\n *   <ng-template ngPluralCase=\"=1\">there is one</ng-template>\n *   <ng-template ngPluralCase=\"few\">there are a few</ng-template>\n * </some-element>\n * ```\n *\n * @description\n *\n * Adds / removes DOM sub-trees based on a numeric value. Tailored for pluralization.\n *\n * Displays DOM sub-trees that match the switch expression value, or failing that, DOM sub-trees\n * that match the switch expression's pluralization category.\n *\n * To use this directive you must provide a container element that sets the `[ngPlural]` attribute\n * to a switch expression. Inner elements with a `[ngPluralCase]` will display based on their\n * expression:\n * - if `[ngPluralCase]` is set to a value starting with `=`, it will only display if the value\n *   matches the switch expression exactly,\n * - otherwise, the view will be treated as a \"category match\", and will only display if exact\n *   value matches aren't found and the value maps to its category for the defined locale.\n *\n * See http://cldr.unicode.org/index/cldr-spec/plural-rules\n *\n * @publicApi\n */\nclass NgPlural {\n  constructor(_localization) {\n    this._localization = _localization;\n    this._caseViews = {};\n  }\n  set ngPlural(value) {\n    this._updateView(value);\n  }\n  addCase(value, switchView) {\n    this._caseViews[value] = switchView;\n  }\n  _updateView(switchValue) {\n    this._clearViews();\n    const cases = Object.keys(this._caseViews);\n    const key = getPluralCategory(switchValue, cases, this._localization);\n    this._activateView(this._caseViews[key]);\n  }\n  _clearViews() {\n    if (this._activeView) this._activeView.destroy();\n  }\n  _activateView(view) {\n    if (view) {\n      this._activeView = view;\n      this._activeView.create();\n    }\n  }\n  static {\n    this.ɵfac = function NgPlural_Factory(t) {\n      return new (t || NgPlural)(i0.ɵɵdirectiveInject(NgLocalization));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgPlural,\n      selectors: [[\"\", \"ngPlural\", \"\"]],\n      inputs: {\n        ngPlural: \"ngPlural\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgPlural, [{\n    type: Directive,\n    args: [{\n      selector: '[ngPlural]',\n      standalone: true\n    }]\n  }], () => [{\n    type: NgLocalization\n  }], {\n    ngPlural: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @ngModule CommonModule\n *\n * @description\n *\n * Creates a view that will be added/removed from the parent {@link NgPlural} when the\n * given expression matches the plural expression according to CLDR rules.\n *\n * @usageNotes\n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">...</ng-template>\n *   <ng-template ngPluralCase=\"other\">...</ng-template>\n * </some-element>\n *```\n *\n * See {@link NgPlural} for more details and example.\n *\n * @publicApi\n */\nclass NgPluralCase {\n  constructor(value, template, viewContainer, ngPlural) {\n    this.value = value;\n    const isANumber = !isNaN(Number(value));\n    ngPlural.addCase(isANumber ? `=${value}` : value, new SwitchView(viewContainer, template));\n  }\n  static {\n    this.ɵfac = function NgPluralCase_Factory(t) {\n      return new (t || NgPluralCase)(i0.ɵɵinjectAttribute('ngPluralCase'), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(NgPlural, 1));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgPluralCase,\n      selectors: [[\"\", \"ngPluralCase\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgPluralCase, [{\n    type: Directive,\n    args: [{\n      selector: '[ngPluralCase]',\n      standalone: true\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['ngPluralCase']\n    }]\n  }, {\n    type: i0.TemplateRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: NgPlural,\n    decorators: [{\n      type: Host\n    }]\n  }], null);\n})();\n\n/**\n * @ngModule CommonModule\n *\n * @usageNotes\n *\n * Set the font of the containing element to the result of an expression.\n *\n * ```\n * <some-element [ngStyle]=\"{'font-style': styleExp}\">...</some-element>\n * ```\n *\n * Set the width of the containing element to a pixel value returned by an expression.\n *\n * ```\n * <some-element [ngStyle]=\"{'max-width.px': widthExp}\">...</some-element>\n * ```\n *\n * Set a collection of style values using an expression that returns key-value pairs.\n *\n * ```\n * <some-element [ngStyle]=\"objExp\">...</some-element>\n * ```\n *\n * @description\n *\n * An attribute directive that updates styles for the containing HTML element.\n * Sets one or more style properties, specified as colon-separated key-value pairs.\n * The key is a style name, with an optional `.<unit>` suffix\n * (such as 'top.px', 'font-style.em').\n * The value is an expression to be evaluated.\n * The resulting non-null value, expressed in the given unit,\n * is assigned to the given style property.\n * If the result of evaluation is null, the corresponding style is removed.\n *\n * @publicApi\n */\nclass NgStyle {\n  constructor(_ngEl, _differs, _renderer) {\n    this._ngEl = _ngEl;\n    this._differs = _differs;\n    this._renderer = _renderer;\n    this._ngStyle = null;\n    this._differ = null;\n  }\n  set ngStyle(values) {\n    this._ngStyle = values;\n    if (!this._differ && values) {\n      this._differ = this._differs.find(values).create();\n    }\n  }\n  ngDoCheck() {\n    if (this._differ) {\n      const changes = this._differ.diff(this._ngStyle);\n      if (changes) {\n        this._applyChanges(changes);\n      }\n    }\n  }\n  _setStyle(nameAndUnit, value) {\n    const [name, unit] = nameAndUnit.split('.');\n    const flags = name.indexOf('-') === -1 ? undefined : RendererStyleFlags2.DashCase;\n    if (value != null) {\n      this._renderer.setStyle(this._ngEl.nativeElement, name, unit ? `${value}${unit}` : value, flags);\n    } else {\n      this._renderer.removeStyle(this._ngEl.nativeElement, name, flags);\n    }\n  }\n  _applyChanges(changes) {\n    changes.forEachRemovedItem(record => this._setStyle(record.key, null));\n    changes.forEachAddedItem(record => this._setStyle(record.key, record.currentValue));\n    changes.forEachChangedItem(record => this._setStyle(record.key, record.currentValue));\n  }\n  static {\n    this.ɵfac = function NgStyle_Factory(t) {\n      return new (t || NgStyle)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.KeyValueDiffers), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgStyle,\n      selectors: [[\"\", \"ngStyle\", \"\"]],\n      inputs: {\n        ngStyle: \"ngStyle\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgStyle, [{\n    type: Directive,\n    args: [{\n      selector: '[ngStyle]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.KeyValueDiffers\n  }, {\n    type: i0.Renderer2\n  }], {\n    ngStyle: [{\n      type: Input,\n      args: ['ngStyle']\n    }]\n  });\n})();\n\n/**\n * @ngModule CommonModule\n *\n * @description\n *\n * Inserts an embedded view from a prepared `TemplateRef`.\n *\n * You can attach a context object to the `EmbeddedViewRef` by setting `[ngTemplateOutletContext]`.\n * `[ngTemplateOutletContext]` should be an object, the object's keys will be available for binding\n * by the local template `let` declarations.\n *\n * @usageNotes\n * ```\n * <ng-container *ngTemplateOutlet=\"templateRefExp; context: contextExp\"></ng-container>\n * ```\n *\n * Using the key `$implicit` in the context object will set its value as default.\n *\n * ### Example\n *\n * {@example common/ngTemplateOutlet/ts/module.ts region='NgTemplateOutlet'}\n *\n * @publicApi\n */\nclass NgTemplateOutlet {\n  constructor(_viewContainerRef) {\n    this._viewContainerRef = _viewContainerRef;\n    this._viewRef = null;\n    /**\n     * A context object to attach to the {@link EmbeddedViewRef}. This should be an\n     * object, the object's keys will be available for binding by the local template `let`\n     * declarations.\n     * Using the key `$implicit` in the context object will set its value as default.\n     */\n    this.ngTemplateOutletContext = null;\n    /**\n     * A string defining the template reference and optionally the context object for the template.\n     */\n    this.ngTemplateOutlet = null;\n    /** Injector to be used within the embedded view. */\n    this.ngTemplateOutletInjector = null;\n  }\n  ngOnChanges(changes) {\n    if (this._shouldRecreateView(changes)) {\n      const viewContainerRef = this._viewContainerRef;\n      if (this._viewRef) {\n        viewContainerRef.remove(viewContainerRef.indexOf(this._viewRef));\n      }\n      // If there is no outlet, clear the destroyed view ref.\n      if (!this.ngTemplateOutlet) {\n        this._viewRef = null;\n        return;\n      }\n      // Create a context forward `Proxy` that will always bind to the user-specified context,\n      // without having to destroy and re-create views whenever the context changes.\n      const viewContext = this._createContextForwardProxy();\n      this._viewRef = viewContainerRef.createEmbeddedView(this.ngTemplateOutlet, viewContext, {\n        injector: this.ngTemplateOutletInjector ?? undefined\n      });\n    }\n  }\n  /**\n   * We need to re-create existing embedded view if either is true:\n   * - the outlet changed.\n   * - the injector changed.\n   */\n  _shouldRecreateView(changes) {\n    return !!changes['ngTemplateOutlet'] || !!changes['ngTemplateOutletInjector'];\n  }\n  /**\n   * For a given outlet instance, we create a proxy object that delegates\n   * to the user-specified context. This allows changing, or swapping out\n   * the context object completely without having to destroy/re-create the view.\n   */\n  _createContextForwardProxy() {\n    return new Proxy({}, {\n      set: (_target, prop, newValue) => {\n        if (!this.ngTemplateOutletContext) {\n          return false;\n        }\n        return Reflect.set(this.ngTemplateOutletContext, prop, newValue);\n      },\n      get: (_target, prop, receiver) => {\n        if (!this.ngTemplateOutletContext) {\n          return undefined;\n        }\n        return Reflect.get(this.ngTemplateOutletContext, prop, receiver);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function NgTemplateOutlet_Factory(t) {\n      return new (t || NgTemplateOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgTemplateOutlet,\n      selectors: [[\"\", \"ngTemplateOutlet\", \"\"]],\n      inputs: {\n        ngTemplateOutletContext: \"ngTemplateOutletContext\",\n        ngTemplateOutlet: \"ngTemplateOutlet\",\n        ngTemplateOutletInjector: \"ngTemplateOutletInjector\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgTemplateOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[ngTemplateOutlet]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }], {\n    ngTemplateOutletContext: [{\n      type: Input\n    }],\n    ngTemplateOutlet: [{\n      type: Input\n    }],\n    ngTemplateOutletInjector: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * A collection of Angular directives that are likely to be used in each and every Angular\n * application.\n */\nconst COMMON_DIRECTIVES = [NgClass, NgComponentOutlet, NgForOf, NgIf, NgTemplateOutlet, NgStyle, NgSwitch, NgSwitchCase, NgSwitchDefault, NgPlural, NgPluralCase];\nfunction invalidPipeArgumentError(type, value) {\n  return new ɵRuntimeError(2100 /* RuntimeErrorCode.INVALID_PIPE_ARGUMENT */, ngDevMode && `InvalidPipeArgument: '${value}' for pipe '${ɵstringify(type)}'`);\n}\nclass SubscribableStrategy {\n  createSubscription(async, updateLatestValue) {\n    // Subscription can be side-effectful, and we don't want any signal reads which happen in the\n    // side effect of the subscription to be tracked by a component's template when that\n    // subscription is triggered via the async pipe. So we wrap the subscription in `untracked` to\n    // decouple from the current reactive context.\n    //\n    // `untracked` also prevents signal _writes_ which happen in the subscription side effect from\n    // being treated as signal writes during the template evaluation (which throws errors).\n    return untracked(() => async.subscribe({\n      next: updateLatestValue,\n      error: e => {\n        throw e;\n      }\n    }));\n  }\n  dispose(subscription) {\n    // See the comment in `createSubscription` above on the use of `untracked`.\n    untracked(() => subscription.unsubscribe());\n  }\n}\nclass PromiseStrategy {\n  createSubscription(async, updateLatestValue) {\n    return async.then(updateLatestValue, e => {\n      throw e;\n    });\n  }\n  dispose(subscription) {}\n}\nconst _promiseStrategy = new PromiseStrategy();\nconst _subscribableStrategy = new SubscribableStrategy();\n/**\n * @ngModule CommonModule\n * @description\n *\n * Unwraps a value from an asynchronous primitive.\n *\n * The `async` pipe subscribes to an `Observable` or `Promise` and returns the latest value it has\n * emitted. When a new value is emitted, the `async` pipe marks the component to be checked for\n * changes. When the component gets destroyed, the `async` pipe unsubscribes automatically to avoid\n * potential memory leaks. When the reference of the expression changes, the `async` pipe\n * automatically unsubscribes from the old `Observable` or `Promise` and subscribes to the new one.\n *\n * @usageNotes\n *\n * ### Examples\n *\n * This example binds a `Promise` to the view. Clicking the `Resolve` button resolves the\n * promise.\n *\n * {@example common/pipes/ts/async_pipe.ts region='AsyncPipePromise'}\n *\n * It's also possible to use `async` with Observables. The example below binds the `time` Observable\n * to the view. The Observable continuously updates the view with the current time.\n *\n * {@example common/pipes/ts/async_pipe.ts region='AsyncPipeObservable'}\n *\n * @publicApi\n */\nclass AsyncPipe {\n  constructor(ref) {\n    this._latestValue = null;\n    this.markForCheckOnValueUpdate = true;\n    this._subscription = null;\n    this._obj = null;\n    this._strategy = null;\n    // Assign `ref` into `this._ref` manually instead of declaring `_ref` in the constructor\n    // parameter list, as the type of `this._ref` includes `null` unlike the type of `ref`.\n    this._ref = ref;\n  }\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._dispose();\n    }\n    // Clear the `ChangeDetectorRef` and its association with the view data, to mitigate\n    // potential memory leaks in Observables that could otherwise cause the view data to\n    // be retained.\n    // https://github.com/angular/angular/issues/17624\n    this._ref = null;\n  }\n  transform(obj) {\n    if (!this._obj) {\n      if (obj) {\n        try {\n          // Only call `markForCheck` if the value is updated asynchronously.\n          // Synchronous updates _during_ subscription should not wastefully mark for check -\n          // this value is already going to be returned from the transform function.\n          this.markForCheckOnValueUpdate = false;\n          this._subscribe(obj);\n        } finally {\n          this.markForCheckOnValueUpdate = true;\n        }\n      }\n      return this._latestValue;\n    }\n    if (obj !== this._obj) {\n      this._dispose();\n      return this.transform(obj);\n    }\n    return this._latestValue;\n  }\n  _subscribe(obj) {\n    this._obj = obj;\n    this._strategy = this._selectStrategy(obj);\n    this._subscription = this._strategy.createSubscription(obj, value => this._updateLatestValue(obj, value));\n  }\n  _selectStrategy(obj) {\n    if (ɵisPromise(obj)) {\n      return _promiseStrategy;\n    }\n    if (ɵisSubscribable(obj)) {\n      return _subscribableStrategy;\n    }\n    throw invalidPipeArgumentError(AsyncPipe, obj);\n  }\n  _dispose() {\n    // Note: `dispose` is only called if a subscription has been initialized before, indicating\n    // that `this._strategy` is also available.\n    this._strategy.dispose(this._subscription);\n    this._latestValue = null;\n    this._subscription = null;\n    this._obj = null;\n  }\n  _updateLatestValue(async, value) {\n    if (async === this._obj) {\n      this._latestValue = value;\n      if (this.markForCheckOnValueUpdate) {\n        this._ref?.markForCheck();\n      }\n    }\n  }\n  static {\n    this.ɵfac = function AsyncPipe_Factory(t) {\n      return new (t || AsyncPipe)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"async\",\n      type: AsyncPipe,\n      pure: false,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AsyncPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'async',\n      pure: false,\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\n\n/**\n * Transforms text to all lower case.\n *\n * @see {@link UpperCasePipe}\n * @see {@link TitleCasePipe}\n * @usageNotes\n *\n * The following example defines a view that allows the user to enter\n * text, and then uses the pipe to convert the input text to all lower case.\n *\n * <code-example path=\"common/pipes/ts/lowerupper_pipe.ts\" region='LowerUpperPipe'></code-example>\n *\n * @ngModule CommonModule\n * @publicApi\n */\nclass LowerCasePipe {\n  transform(value) {\n    if (value == null) return null;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(LowerCasePipe, value);\n    }\n    return value.toLowerCase();\n  }\n  static {\n    this.ɵfac = function LowerCasePipe_Factory(t) {\n      return new (t || LowerCasePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"lowercase\",\n      type: LowerCasePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LowerCasePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'lowercase',\n      standalone: true\n    }]\n  }], null, null);\n})();\n//\n// Regex below matches any Unicode word and number compatible with ES5. In ES2018 the same result\n// can be achieved by using /[0-9\\p{L}]\\S*/gu and also known as Unicode Property Escapes\n// (https://2ality.com/2017/07/regexp-unicode-property-escapes.html). Since there is no\n// transpilation of this functionality down to ES5 without external tool, the only solution is\n// to use already transpiled form. Example can be found here -\n// https://mothereff.in/regexpu#input=var+regex+%3D+%2F%5B0-9%5Cp%7BL%7D%5D%5CS*%2Fgu%3B%0A%0A&unicodePropertyEscape=1\n//\nconst unicodeWordMatch = /(?:[0-9A-Za-z\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u0870-\\u0887\\u0889-\\u088E\\u08A0-\\u08C9\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C5D\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D04-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E86-\\u0E8A\\u0E8C-\\u0EA3\\u0EA5\\u0EA7-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16F1-\\u16F8\\u1700-\\u1711\\u171F-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1878\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4C\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CE9-\\u1CEC\\u1CEE-\\u1CF3\\u1CF5\\u1CF6\\u1CFA\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184\\u2C00-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BF\\u31F0-\\u31FF\\u3400-\\u4DBF\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7CA\\uA7D0\\uA7D1\\uA7D3\\uA7D5-\\uA7D9\\uA7F2-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA8FE\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB69\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF40\\uDF42-\\uDF49\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDD70-\\uDD7A\\uDD7C-\\uDD8A\\uDD8C-\\uDD92\\uDD94\\uDD95\\uDD97-\\uDDA1\\uDDA3-\\uDDB1\\uDDB3-\\uDDB9\\uDDBB\\uDDBC\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67\\uDF80-\\uDF85\\uDF87-\\uDFB0\\uDFB2-\\uDFBA]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD23\\uDE80-\\uDEA9\\uDEB0\\uDEB1\\uDF00-\\uDF1C\\uDF27\\uDF30-\\uDF45\\uDF70-\\uDF81\\uDFB0-\\uDFC4\\uDFE0-\\uDFF6]|\\uD804[\\uDC03-\\uDC37\\uDC71\\uDC72\\uDC75\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD44\\uDD47\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC5F-\\uDC61\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDEB8\\uDF00-\\uDF1A\\uDF40-\\uDF46]|\\uD806[\\uDC00-\\uDC2B\\uDCA0-\\uDCDF\\uDCFF-\\uDD06\\uDD09\\uDD0C-\\uDD13\\uDD15\\uDD16\\uDD18-\\uDD2F\\uDD3F\\uDD41\\uDDA0-\\uDDA7\\uDDAA-\\uDDD0\\uDDE1\\uDDE3\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE89\\uDE9D\\uDEB0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD89\\uDD98\\uDEE0-\\uDEF2\\uDFB0]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC80-\\uDD43]|\\uD80B[\\uDF90-\\uDFF0]|[\\uD80C\\uD81C-\\uD820\\uD822\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879\\uD880-\\uD883][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE70-\\uDEBE\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDE40-\\uDE7F\\uDF00-\\uDF4A\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1\\uDFE3]|\\uD821[\\uDC00-\\uDFF7]|\\uD823[\\uDC00-\\uDCD5\\uDD00-\\uDD08]|\\uD82B[\\uDFF0-\\uDFF3\\uDFF5-\\uDFFB\\uDFFD\\uDFFE]|\\uD82C[\\uDC00-\\uDD22\\uDD50-\\uDD52\\uDD64-\\uDD67\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD837[\\uDF00-\\uDF1E]|\\uD838[\\uDD00-\\uDD2C\\uDD37-\\uDD3D\\uDD4E\\uDE90-\\uDEAD\\uDEC0-\\uDEEB]|\\uD839[\\uDFE0-\\uDFE6\\uDFE8-\\uDFEB\\uDFED\\uDFEE\\uDFF0-\\uDFFE]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43\\uDD4B]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDEDF\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF38\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uD884[\\uDC00-\\uDF4A])\\S*/g;\n/**\n * Transforms text to title case.\n * Capitalizes the first letter of each word and transforms the\n * rest of the word to lower case.\n * Words are delimited by any whitespace character, such as a space, tab, or line-feed character.\n *\n * @see {@link LowerCasePipe}\n * @see {@link UpperCasePipe}\n *\n * @usageNotes\n * The following example shows the result of transforming various strings into title case.\n *\n * <code-example path=\"common/pipes/ts/titlecase_pipe.ts\" region='TitleCasePipe'></code-example>\n *\n * @ngModule CommonModule\n * @publicApi\n */\nclass TitleCasePipe {\n  transform(value) {\n    if (value == null) return null;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(TitleCasePipe, value);\n    }\n    return value.replace(unicodeWordMatch, txt => txt[0].toUpperCase() + txt.slice(1).toLowerCase());\n  }\n  static {\n    this.ɵfac = function TitleCasePipe_Factory(t) {\n      return new (t || TitleCasePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"titlecase\",\n      type: TitleCasePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TitleCasePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'titlecase',\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Transforms text to all upper case.\n * @see {@link LowerCasePipe}\n * @see {@link TitleCasePipe}\n *\n * @ngModule CommonModule\n * @publicApi\n */\nclass UpperCasePipe {\n  transform(value) {\n    if (value == null) return null;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(UpperCasePipe, value);\n    }\n    return value.toUpperCase();\n  }\n  static {\n    this.ɵfac = function UpperCasePipe_Factory(t) {\n      return new (t || UpperCasePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"uppercase\",\n      type: UpperCasePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UpperCasePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'uppercase',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * The default date format of Angular date pipe, which corresponds to the following format:\n * `'MMM d,y'` (e.g. `Jun 15, 2015`)\n */\nconst DEFAULT_DATE_FORMAT = 'mediumDate';\n\n/**\n * Optionally-provided default timezone to use for all instances of `DatePipe` (such as `'+0430'`).\n * If the value isn't provided, the `DatePipe` will use the end-user's local system timezone.\n *\n * @deprecated use DATE_PIPE_DEFAULT_OPTIONS token to configure DatePipe\n */\nconst DATE_PIPE_DEFAULT_TIMEZONE = new InjectionToken(ngDevMode ? 'DATE_PIPE_DEFAULT_TIMEZONE' : '');\n/**\n * DI token that allows to provide default configuration for the `DatePipe` instances in an\n * application. The value is an object which can include the following fields:\n * - `dateFormat`: configures the default date format. If not provided, the `DatePipe`\n * will use the 'mediumDate' as a value.\n * - `timezone`: configures the default timezone. If not provided, the `DatePipe` will\n * use the end-user's local system timezone.\n *\n * @see {@link DatePipeConfig}\n *\n * @usageNotes\n *\n * Various date pipe default values can be overwritten by providing this token with\n * the value that has this interface.\n *\n * For example:\n *\n * Override the default date format by providing a value using the token:\n * ```typescript\n * providers: [\n *   {provide: DATE_PIPE_DEFAULT_OPTIONS, useValue: {dateFormat: 'shortDate'}}\n * ]\n * ```\n *\n * Override the default timezone by providing a value using the token:\n * ```typescript\n * providers: [\n *   {provide: DATE_PIPE_DEFAULT_OPTIONS, useValue: {timezone: '-1200'}}\n * ]\n * ```\n */\nconst DATE_PIPE_DEFAULT_OPTIONS = new InjectionToken(ngDevMode ? 'DATE_PIPE_DEFAULT_OPTIONS' : '');\n// clang-format off\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a date value according to locale rules.\n *\n * `DatePipe` is executed only when it detects a pure change to the input value.\n * A pure change is either a change to a primitive input value\n * (such as `String`, `Number`, `Boolean`, or `Symbol`),\n * or a changed object reference (such as `Date`, `Array`, `Function`, or `Object`).\n *\n * Note that mutating a `Date` object does not cause the pipe to be rendered again.\n * To ensure that the pipe is executed, you must create a new `Date` object.\n *\n * Only the `en-US` locale data comes with Angular. To localize dates\n * in another language, you must import the corresponding locale data.\n * See the [I18n guide](guide/i18n-common-format-data-locale) for more information.\n *\n * The time zone of the formatted value can be specified either by passing it in as the second\n * parameter of the pipe, or by setting the default through the `DATE_PIPE_DEFAULT_OPTIONS`\n * injection token. The value that is passed in as the second parameter takes precedence over\n * the one defined using the injection token.\n *\n * @see {@link formatDate}\n *\n *\n * @usageNotes\n *\n * The result of this pipe is not reevaluated when the input is mutated. To avoid the need to\n * reformat the date on every change-detection cycle, treat the date as an immutable object\n * and change the reference when the pipe needs to run again.\n *\n * ### Pre-defined format options\n *\n * | Option        | Equivalent to                       | Examples (given in `en-US` locale)              |\n * |---------------|-------------------------------------|-------------------------------------------------|\n * | `'short'`     | `'M/d/yy, h:mm a'`                  | `6/15/15, 9:03 AM`                              |\n * | `'medium'`    | `'MMM d, y, h:mm:ss a'`             | `Jun 15, 2015, 9:03:01 AM`                      |\n * | `'long'`      | `'MMMM d, y, h:mm:ss a z'`          | `June 15, 2015 at 9:03:01 AM GMT+1`             |\n * | `'full'`      | `'EEEE, MMMM d, y, h:mm:ss a zzzz'` | `Monday, June 15, 2015 at 9:03:01 AM GMT+01:00` |\n * | `'shortDate'` | `'M/d/yy'`                          | `6/15/15`                                       |\n * | `'mediumDate'`| `'MMM d, y'`                        | `Jun 15, 2015`                                  |\n * | `'longDate'`  | `'MMMM d, y'`                       | `June 15, 2015`                                 |\n * | `'fullDate'`  | `'EEEE, MMMM d, y'`                 | `Monday, June 15, 2015`                         |\n * | `'shortTime'` | `'h:mm a'`                          | `9:03 AM`                                       |\n * | `'mediumTime'`| `'h:mm:ss a'`                       | `9:03:01 AM`                                    |\n * | `'longTime'`  | `'h:mm:ss a z'`                     | `9:03:01 AM GMT+1`                              |\n * | `'fullTime'`  | `'h:mm:ss a zzzz'`                  | `9:03:01 AM GMT+01:00`                          |\n *\n * ### Custom format options\n *\n * You can construct a format string using symbols to specify the components\n * of a date-time value, as described in the following table.\n * Format details depend on the locale.\n * Fields marked with (*) are only available in the extra data set for the given locale.\n *\n *  | Field type              | Format      | Description                                                   | Example Value                                              |\n *  |-------------------------|-------------|---------------------------------------------------------------|------------------------------------------------------------|\n *  | Era                     | G, GG & GGG | Abbreviated                                                   | AD                                                         |\n *  |                         | GGGG        | Wide                                                          | Anno Domini                                                |\n *  |                         | GGGGG       | Narrow                                                        | A                                                          |\n *  | Year                    | y           | Numeric: minimum digits                                       | 2, 20, 201, 2017, 20173                                    |\n *  |                         | yy          | Numeric: 2 digits + zero padded                               | 02, 20, 01, 17, 73                                         |\n *  |                         | yyy         | Numeric: 3 digits + zero padded                               | 002, 020, 201, 2017, 20173                                 |\n *  |                         | yyyy        | Numeric: 4 digits or more + zero padded                       | 0002, 0020, 0201, 2017, 20173                              |\n *  | ISO Week-numbering year | Y           | Numeric: minimum digits                                       | 2, 20, 201, 2017, 20173                                    |\n *  |                         | YY          | Numeric: 2 digits + zero padded                               | 02, 20, 01, 17, 73                                         |\n *  |                         | YYY         | Numeric: 3 digits + zero padded                               | 002, 020, 201, 2017, 20173                                 |\n *  |                         | YYYY        | Numeric: 4 digits or more + zero padded                       | 0002, 0020, 0201, 2017, 20173                              |\n *  | Month                   | M           | Numeric: 1 digit                                              | 9, 12                                                      |\n *  |                         | MM          | Numeric: 2 digits + zero padded                               | 09, 12                                                     |\n *  |                         | MMM         | Abbreviated                                                   | Sep                                                        |\n *  |                         | MMMM        | Wide                                                          | September                                                  |\n *  |                         | MMMMM       | Narrow                                                        | S                                                          |\n *  | Month standalone        | L           | Numeric: 1 digit                                              | 9, 12                                                      |\n *  |                         | LL          | Numeric: 2 digits + zero padded                               | 09, 12                                                     |\n *  |                         | LLL         | Abbreviated                                                   | Sep                                                        |\n *  |                         | LLLL        | Wide                                                          | September                                                  |\n *  |                         | LLLLL       | Narrow                                                        | S                                                          |\n *  | ISO Week of year        | w           | Numeric: minimum digits                                       | 1... 53                                                    |\n *  |                         | ww          | Numeric: 2 digits + zero padded                               | 01... 53                                                   |\n *  | Week of month           | W           | Numeric: 1 digit                                              | 1... 5                                                     |\n *  | Day of month            | d           | Numeric: minimum digits                                       | 1                                                          |\n *  |                         | dd          | Numeric: 2 digits + zero padded                               | 01                                                         |\n *  | Week day                | E, EE & EEE | Abbreviated                                                   | Tue                                                        |\n *  |                         | EEEE        | Wide                                                          | Tuesday                                                    |\n *  |                         | EEEEE       | Narrow                                                        | T                                                          |\n *  |                         | EEEEEE      | Short                                                         | Tu                                                         |\n *  | Week day standalone     | c, cc       | Numeric: 1 digit                                              | 2                                                          |\n *  |                         | ccc         | Abbreviated                                                   | Tue                                                        |\n *  |                         | cccc        | Wide                                                          | Tuesday                                                    |\n *  |                         | ccccc       | Narrow                                                        | T                                                          |\n *  |                         | cccccc      | Short                                                         | Tu                                                         |\n *  | Period                  | a, aa & aaa | Abbreviated                                                   | am/pm or AM/PM                                             |\n *  |                         | aaaa        | Wide (fallback to `a` when missing)                           | ante meridiem/post meridiem                                |\n *  |                         | aaaaa       | Narrow                                                        | a/p                                                        |\n *  | Period*                 | B, BB & BBB | Abbreviated                                                   | mid.                                                       |\n *  |                         | BBBB        | Wide                                                          | am, pm, midnight, noon, morning, afternoon, evening, night |\n *  |                         | BBBBB       | Narrow                                                        | md                                                         |\n *  | Period standalone*      | b, bb & bbb | Abbreviated                                                   | mid.                                                       |\n *  |                         | bbbb        | Wide                                                          | am, pm, midnight, noon, morning, afternoon, evening, night |\n *  |                         | bbbbb       | Narrow                                                        | md                                                         |\n *  | Hour 1-12               | h           | Numeric: minimum digits                                       | 1, 12                                                      |\n *  |                         | hh          | Numeric: 2 digits + zero padded                               | 01, 12                                                     |\n *  | Hour 0-23               | H           | Numeric: minimum digits                                       | 0, 23                                                      |\n *  |                         | HH          | Numeric: 2 digits + zero padded                               | 00, 23                                                     |\n *  | Minute                  | m           | Numeric: minimum digits                                       | 8, 59                                                      |\n *  |                         | mm          | Numeric: 2 digits + zero padded                               | 08, 59                                                     |\n *  | Second                  | s           | Numeric: minimum digits                                       | 0... 59                                                    |\n *  |                         | ss          | Numeric: 2 digits + zero padded                               | 00... 59                                                   |\n *  | Fractional seconds      | S           | Numeric: 1 digit                                              | 0... 9                                                     |\n *  |                         | SS          | Numeric: 2 digits + zero padded                               | 00... 99                                                   |\n *  |                         | SSS         | Numeric: 3 digits + zero padded (= milliseconds)              | 000... 999                                                 |\n *  | Zone                    | z, zz & zzz | Short specific non location format (fallback to O)            | GMT-8                                                      |\n *  |                         | zzzz        | Long specific non location format (fallback to OOOO)          | GMT-08:00                                                  |\n *  |                         | Z, ZZ & ZZZ | ISO8601 basic format                                          | -0800                                                      |\n *  |                         | ZZZZ        | Long localized GMT format                                     | GMT-8:00                                                   |\n *  |                         | ZZZZZ       | ISO8601 extended format + Z indicator for offset 0 (= XXXXX)  | -08:00                                                     |\n *  |                         | O, OO & OOO | Short localized GMT format                                    | GMT-8                                                      |\n *  |                         | OOOO        | Long localized GMT format                                     | GMT-08:00                                                  |\n *\n *\n * ### Format examples\n *\n * These examples transform a date into various formats,\n * assuming that `dateObj` is a JavaScript `Date` object for\n * year: 2015, month: 6, day: 15, hour: 21, minute: 43, second: 11,\n * given in the local time for the `en-US` locale.\n *\n * ```\n * {{ dateObj | date }}               // output is 'Jun 15, 2015'\n * {{ dateObj | date:'medium' }}      // output is 'Jun 15, 2015, 9:43:11 PM'\n * {{ dateObj | date:'shortTime' }}   // output is '9:43 PM'\n * {{ dateObj | date:'mm:ss' }}       // output is '43:11'\n * {{ dateObj | date:\"MMM dd, yyyy 'at' hh:mm a\" }}  // output is 'Jun 15, 2015 at 09:43 PM'\n * ```\n *\n * ### Usage example\n *\n * The following component uses a date pipe to display the current date in different formats.\n *\n * ```\n * @Component({\n *  selector: 'date-pipe',\n *  template: `<div>\n *    <p>Today is {{today | date}}</p>\n *    <p>Or if you prefer, {{today | date:'fullDate'}}</p>\n *    <p>The time is {{today | date:'h:mm a z'}}</p>\n *  </div>`\n * })\n * // Get the current date and time as a date-time value.\n * export class DatePipeComponent {\n *   today: number = Date.now();\n * }\n * ```\n *\n * @publicApi\n */\n// clang-format on\nclass DatePipe {\n  constructor(locale, defaultTimezone, defaultOptions) {\n    this.locale = locale;\n    this.defaultTimezone = defaultTimezone;\n    this.defaultOptions = defaultOptions;\n  }\n  transform(value, format, timezone, locale) {\n    if (value == null || value === '' || value !== value) return null;\n    try {\n      const _format = format ?? this.defaultOptions?.dateFormat ?? DEFAULT_DATE_FORMAT;\n      const _timezone = timezone ?? this.defaultOptions?.timezone ?? this.defaultTimezone ?? undefined;\n      return formatDate(value, _format, locale || this.locale, _timezone);\n    } catch (error) {\n      throw invalidPipeArgumentError(DatePipe, error.message);\n    }\n  }\n  static {\n    this.ɵfac = function DatePipe_Factory(t) {\n      return new (t || DatePipe)(i0.ɵɵdirectiveInject(LOCALE_ID, 16), i0.ɵɵdirectiveInject(DATE_PIPE_DEFAULT_TIMEZONE, 24), i0.ɵɵdirectiveInject(DATE_PIPE_DEFAULT_OPTIONS, 24));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"date\",\n      type: DatePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DatePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'date',\n      standalone: true\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LOCALE_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DATE_PIPE_DEFAULT_TIMEZONE]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DATE_PIPE_DEFAULT_OPTIONS]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\nconst _INTERPOLATION_REGEXP = /#/g;\n/**\n * @ngModule CommonModule\n * @description\n *\n * Maps a value to a string that pluralizes the value according to locale rules.\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/pipes/ts/i18n_pipe.ts region='I18nPluralPipeComponent'}\n *\n * @publicApi\n */\nclass I18nPluralPipe {\n  constructor(_localization) {\n    this._localization = _localization;\n  }\n  /**\n   * @param value the number to be formatted\n   * @param pluralMap an object that mimics the ICU format, see\n   * https://unicode-org.github.io/icu/userguide/format_parse/messages/.\n   * @param locale a `string` defining the locale to use (uses the current {@link LOCALE_ID} by\n   * default).\n   */\n  transform(value, pluralMap, locale) {\n    if (value == null) return '';\n    if (typeof pluralMap !== 'object' || pluralMap === null) {\n      throw invalidPipeArgumentError(I18nPluralPipe, pluralMap);\n    }\n    const key = getPluralCategory(value, Object.keys(pluralMap), this._localization, locale);\n    return pluralMap[key].replace(_INTERPOLATION_REGEXP, value.toString());\n  }\n  static {\n    this.ɵfac = function I18nPluralPipe_Factory(t) {\n      return new (t || I18nPluralPipe)(i0.ɵɵdirectiveInject(NgLocalization, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"i18nPlural\",\n      type: I18nPluralPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(I18nPluralPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'i18nPlural',\n      standalone: true\n    }]\n  }], () => [{\n    type: NgLocalization\n  }], null);\n})();\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Generic selector that displays the string that matches the current value.\n *\n * If none of the keys of the `mapping` match the `value`, then the content\n * of the `other` key is returned when present, otherwise an empty string is returned.\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/pipes/ts/i18n_pipe.ts region='I18nSelectPipeComponent'}\n *\n * @publicApi\n */\nclass I18nSelectPipe {\n  /**\n   * @param value a string to be internationalized.\n   * @param mapping an object that indicates the text that should be displayed\n   * for different values of the provided `value`.\n   */\n  transform(value, mapping) {\n    if (value == null) return '';\n    if (typeof mapping !== 'object' || typeof value !== 'string') {\n      throw invalidPipeArgumentError(I18nSelectPipe, mapping);\n    }\n    if (mapping.hasOwnProperty(value)) {\n      return mapping[value];\n    }\n    if (mapping.hasOwnProperty('other')) {\n      return mapping['other'];\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function I18nSelectPipe_Factory(t) {\n      return new (t || I18nSelectPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"i18nSelect\",\n      type: I18nSelectPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(I18nSelectPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'i18nSelect',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Converts a value into its JSON-format representation.  Useful for debugging.\n *\n * @usageNotes\n *\n * The following component uses a JSON pipe to convert an object\n * to JSON format, and displays the string in both formats for comparison.\n *\n * {@example common/pipes/ts/json_pipe.ts region='JsonPipe'}\n *\n * @publicApi\n */\nclass JsonPipe {\n  /**\n   * @param value A value of any type to convert into a JSON-format string.\n   */\n  transform(value) {\n    return JSON.stringify(value, null, 2);\n  }\n  static {\n    this.ɵfac = function JsonPipe_Factory(t) {\n      return new (t || JsonPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"json\",\n      type: JsonPipe,\n      pure: false,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'json',\n      pure: false,\n      standalone: true\n    }]\n  }], null, null);\n})();\nfunction makeKeyValuePair(key, value) {\n  return {\n    key: key,\n    value: value\n  };\n}\n/**\n * @ngModule CommonModule\n * @description\n *\n * Transforms Object or Map into an array of key value pairs.\n *\n * The output array will be ordered by keys.\n * By default the comparator will be by Unicode point value.\n * You can optionally pass a compareFn if your keys are complex types.\n *\n * @usageNotes\n * ### Examples\n *\n * This examples show how an Object or a Map can be iterated by ngFor with the use of this\n * keyvalue pipe.\n *\n * {@example common/pipes/ts/keyvalue_pipe.ts region='KeyValuePipe'}\n *\n * @publicApi\n */\nclass KeyValuePipe {\n  constructor(differs) {\n    this.differs = differs;\n    this.keyValues = [];\n    this.compareFn = defaultComparator;\n  }\n  transform(input, compareFn = defaultComparator) {\n    if (!input || !(input instanceof Map) && typeof input !== 'object') {\n      return null;\n    }\n    // make a differ for whatever type we've been passed in\n    this.differ ??= this.differs.find(input).create();\n    const differChanges = this.differ.diff(input);\n    const compareFnChanged = compareFn !== this.compareFn;\n    if (differChanges) {\n      this.keyValues = [];\n      differChanges.forEachItem(r => {\n        this.keyValues.push(makeKeyValuePair(r.key, r.currentValue));\n      });\n    }\n    if (differChanges || compareFnChanged) {\n      this.keyValues.sort(compareFn);\n      this.compareFn = compareFn;\n    }\n    return this.keyValues;\n  }\n  static {\n    this.ɵfac = function KeyValuePipe_Factory(t) {\n      return new (t || KeyValuePipe)(i0.ɵɵdirectiveInject(i0.KeyValueDiffers, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"keyvalue\",\n      type: KeyValuePipe,\n      pure: false,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeyValuePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'keyvalue',\n      pure: false,\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.KeyValueDiffers\n  }], null);\n})();\nfunction defaultComparator(keyValueA, keyValueB) {\n  const a = keyValueA.key;\n  const b = keyValueB.key;\n  // if same exit with 0;\n  if (a === b) return 0;\n  // make sure that undefined are at the end of the sort.\n  if (a === undefined) return 1;\n  if (b === undefined) return -1;\n  // make sure that nulls are at the end of the sort.\n  if (a === null) return 1;\n  if (b === null) return -1;\n  if (typeof a == 'string' && typeof b == 'string') {\n    return a < b ? -1 : 1;\n  }\n  if (typeof a == 'number' && typeof b == 'number') {\n    return a - b;\n  }\n  if (typeof a == 'boolean' && typeof b == 'boolean') {\n    return a < b ? -1 : 1;\n  }\n  // `a` and `b` are of different types. Compare their string values.\n  const aString = String(a);\n  const bString = String(b);\n  return aString == bString ? 0 : aString < bString ? -1 : 1;\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a value according to digit options and locale rules.\n * Locale determines group sizing and separator,\n * decimal point character, and other locale-specific configurations.\n *\n * @see {@link formatNumber}\n *\n * @usageNotes\n *\n * ### digitsInfo\n *\n * The value's decimal representation is specified by the `digitsInfo`\n * parameter, written in the following format:<br>\n *\n * ```\n * {minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}\n * ```\n *\n *  - `minIntegerDigits`:\n * The minimum number of integer digits before the decimal point.\n * Default is 1.\n *\n * - `minFractionDigits`:\n * The minimum number of digits after the decimal point.\n * Default is 0.\n *\n *  - `maxFractionDigits`:\n * The maximum number of digits after the decimal point.\n * Default is 3.\n *\n * If the formatted value is truncated it will be rounded using the \"to-nearest\" method:\n *\n * ```\n * {{3.6 | number: '1.0-0'}}\n * <!--will output '4'-->\n *\n * {{-3.6 | number:'1.0-0'}}\n * <!--will output '-4'-->\n * ```\n *\n * ### locale\n *\n * `locale` will format a value according to locale rules.\n * Locale determines group sizing and separator,\n * decimal point character, and other locale-specific configurations.\n *\n * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n *\n * See [Setting your app locale](guide/i18n-common-locale-id).\n *\n * ### Example\n *\n * The following code shows how the pipe transforms values\n * according to various format specifications,\n * where the caller's default locale is `en-US`.\n *\n * <code-example path=\"common/pipes/ts/number_pipe.ts\" region='NumberPipe'></code-example>\n *\n * @publicApi\n */\nclass DecimalPipe {\n  constructor(_locale) {\n    this._locale = _locale;\n  }\n  /**\n   * @param value The value to be formatted.\n   * @param digitsInfo Sets digit and decimal representation.\n   * [See more](#digitsinfo).\n   * @param locale Specifies what locale format rules to use.\n   * [See more](#locale).\n   */\n  transform(value, digitsInfo, locale) {\n    if (!isValue(value)) return null;\n    locale ||= this._locale;\n    try {\n      const num = strToNumber(value);\n      return formatNumber(num, locale, digitsInfo);\n    } catch (error) {\n      throw invalidPipeArgumentError(DecimalPipe, error.message);\n    }\n  }\n  static {\n    this.ɵfac = function DecimalPipe_Factory(t) {\n      return new (t || DecimalPipe)(i0.ɵɵdirectiveInject(LOCALE_ID, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"number\",\n      type: DecimalPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DecimalPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'number',\n      standalone: true\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LOCALE_ID]\n    }]\n  }], null);\n})();\n/**\n * @ngModule CommonModule\n * @description\n *\n * Transforms a number to a percentage\n * string, formatted according to locale rules that determine group sizing and\n * separator, decimal-point character, and other locale-specific\n * configurations.\n *\n * @see {@link formatPercent}\n *\n * @usageNotes\n * The following code shows how the pipe transforms numbers\n * into text strings, according to various format specifications,\n * where the caller's default locale is `en-US`.\n *\n * <code-example path=\"common/pipes/ts/percent_pipe.ts\" region='PercentPipe'></code-example>\n *\n * @publicApi\n */\nclass PercentPipe {\n  constructor(_locale) {\n    this._locale = _locale;\n  }\n  /**\n   *\n   * @param value The number to be formatted as a percentage.\n   * @param digitsInfo Decimal representation options, specified by a string\n   * in the following format:<br>\n   * <code>{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}</code>.\n   *   - `minIntegerDigits`: The minimum number of integer digits before the decimal point.\n   * Default is `1`.\n   *   - `minFractionDigits`: The minimum number of digits after the decimal point.\n   * Default is `0`.\n   *   - `maxFractionDigits`: The maximum number of digits after the decimal point.\n   * Default is `0`.\n   * @param locale A locale code for the locale format rules to use.\n   * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n   * See [Setting your app locale](guide/i18n-common-locale-id).\n   */\n  transform(value, digitsInfo, locale) {\n    if (!isValue(value)) return null;\n    locale ||= this._locale;\n    try {\n      const num = strToNumber(value);\n      return formatPercent(num, locale, digitsInfo);\n    } catch (error) {\n      throw invalidPipeArgumentError(PercentPipe, error.message);\n    }\n  }\n  static {\n    this.ɵfac = function PercentPipe_Factory(t) {\n      return new (t || PercentPipe)(i0.ɵɵdirectiveInject(LOCALE_ID, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"percent\",\n      type: PercentPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PercentPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'percent',\n      standalone: true\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LOCALE_ID]\n    }]\n  }], null);\n})();\n/**\n * @ngModule CommonModule\n * @description\n *\n * Transforms a number to a currency string, formatted according to locale rules\n * that determine group sizing and separator, decimal-point character,\n * and other locale-specific configurations.\n *\n *\n * @see {@link getCurrencySymbol}\n * @see {@link formatCurrency}\n *\n * @usageNotes\n * The following code shows how the pipe transforms numbers\n * into text strings, according to various format specifications,\n * where the caller's default locale is `en-US`.\n *\n * <code-example path=\"common/pipes/ts/currency_pipe.ts\" region='CurrencyPipe'></code-example>\n *\n * @publicApi\n */\nclass CurrencyPipe {\n  constructor(_locale, _defaultCurrencyCode = 'USD') {\n    this._locale = _locale;\n    this._defaultCurrencyCode = _defaultCurrencyCode;\n  }\n  /**\n   *\n   * @param value The number to be formatted as currency.\n   * @param currencyCode The [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) currency code,\n   * such as `USD` for the US dollar and `EUR` for the euro. The default currency code can be\n   * configured using the `DEFAULT_CURRENCY_CODE` injection token.\n   * @param display The format for the currency indicator. One of the following:\n   *   - `code`: Show the code (such as `USD`).\n   *   - `symbol`(default): Show the symbol (such as `$`).\n   *   - `symbol-narrow`: Use the narrow symbol for locales that have two symbols for their\n   * currency.\n   * For example, the Canadian dollar CAD has the symbol `CA$` and the symbol-narrow `$`. If the\n   * locale has no narrow symbol, uses the standard symbol for the locale.\n   *   - String: Use the given string value instead of a code or a symbol.\n   * For example, an empty string will suppress the currency & symbol.\n   *   - Boolean (marked deprecated in v5): `true` for symbol and false for `code`.\n   *\n   * @param digitsInfo Decimal representation options, specified by a string\n   * in the following format:<br>\n   * <code>{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}</code>.\n   *   - `minIntegerDigits`: The minimum number of integer digits before the decimal point.\n   * Default is `1`.\n   *   - `minFractionDigits`: The minimum number of digits after the decimal point.\n   * Default is `2`.\n   *   - `maxFractionDigits`: The maximum number of digits after the decimal point.\n   * Default is `2`.\n   * If not provided, the number will be formatted with the proper amount of digits,\n   * depending on what the [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) specifies.\n   * For example, the Canadian dollar has 2 digits, whereas the Chilean peso has none.\n   * @param locale A locale code for the locale format rules to use.\n   * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n   * See [Setting your app locale](guide/i18n-common-locale-id).\n   */\n  transform(value, currencyCode = this._defaultCurrencyCode, display = 'symbol', digitsInfo, locale) {\n    if (!isValue(value)) return null;\n    locale ||= this._locale;\n    if (typeof display === 'boolean') {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && console && console.warn) {\n        console.warn(`Warning: the currency pipe has been changed in Angular v5. The symbolDisplay option (third parameter) is now a string instead of a boolean. The accepted values are \"code\", \"symbol\" or \"symbol-narrow\".`);\n      }\n      display = display ? 'symbol' : 'code';\n    }\n    let currency = currencyCode || this._defaultCurrencyCode;\n    if (display !== 'code') {\n      if (display === 'symbol' || display === 'symbol-narrow') {\n        currency = getCurrencySymbol(currency, display === 'symbol' ? 'wide' : 'narrow', locale);\n      } else {\n        currency = display;\n      }\n    }\n    try {\n      const num = strToNumber(value);\n      return formatCurrency(num, locale, currency, currencyCode, digitsInfo);\n    } catch (error) {\n      throw invalidPipeArgumentError(CurrencyPipe, error.message);\n    }\n  }\n  static {\n    this.ɵfac = function CurrencyPipe_Factory(t) {\n      return new (t || CurrencyPipe)(i0.ɵɵdirectiveInject(LOCALE_ID, 16), i0.ɵɵdirectiveInject(DEFAULT_CURRENCY_CODE, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"currency\",\n      type: CurrencyPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CurrencyPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'currency',\n      standalone: true\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LOCALE_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DEFAULT_CURRENCY_CODE]\n    }]\n  }], null);\n})();\nfunction isValue(value) {\n  return !(value == null || value === '' || value !== value);\n}\n/**\n * Transforms a string into a number (if needed).\n */\nfunction strToNumber(value) {\n  // Convert strings to numbers\n  if (typeof value === 'string' && !isNaN(Number(value) - parseFloat(value))) {\n    return Number(value);\n  }\n  if (typeof value !== 'number') {\n    throw new Error(`${value} is not a number`);\n  }\n  return value;\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Creates a new `Array` or `String` containing a subset (slice) of the elements.\n *\n * @usageNotes\n *\n * All behavior is based on the expected behavior of the JavaScript API `Array.prototype.slice()`\n * and `String.prototype.slice()`.\n *\n * When operating on an `Array`, the returned `Array` is always a copy even when all\n * the elements are being returned.\n *\n * When operating on a blank value, the pipe returns the blank value.\n *\n * ### List Example\n *\n * This `ngFor` example:\n *\n * {@example common/pipes/ts/slice_pipe.ts region='SlicePipe_list'}\n *\n * produces the following:\n *\n * ```html\n * <li>b</li>\n * <li>c</li>\n * ```\n *\n * ### String Examples\n *\n * {@example common/pipes/ts/slice_pipe.ts region='SlicePipe_string'}\n *\n * @publicApi\n */\nclass SlicePipe {\n  transform(value, start, end) {\n    if (value == null) return null;\n    if (!this.supports(value)) {\n      throw invalidPipeArgumentError(SlicePipe, value);\n    }\n    return value.slice(start, end);\n  }\n  supports(obj) {\n    return typeof obj === 'string' || Array.isArray(obj);\n  }\n  static {\n    this.ɵfac = function SlicePipe_Factory(t) {\n      return new (t || SlicePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"slice\",\n      type: SlicePipe,\n      pure: false,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SlicePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'slice',\n      pure: false,\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * @module\n * @description\n * This module provides a set of common Pipes.\n */\n/**\n * A collection of Angular pipes that are likely to be used in each and every application.\n */\nconst COMMON_PIPES = [AsyncPipe, UpperCasePipe, LowerCasePipe, JsonPipe, SlicePipe, DecimalPipe, PercentPipe, TitleCasePipe, CurrencyPipe, DatePipe, I18nPluralPipe, I18nSelectPipe, KeyValuePipe];\n\n// Note: This does not contain the location providers,\n// as they need some platform specific implementations to work.\n/**\n * Exports all the basic Angular directives and pipes,\n * such as `NgIf`, `NgForOf`, `DecimalPipe`, and so on.\n * Re-exported by `BrowserModule`, which is included automatically in the root\n * `AppModule` when you create a new app with the CLI `new` command.\n *\n * @publicApi\n */\nclass CommonModule {\n  static {\n    this.ɵfac = function CommonModule_Factory(t) {\n      return new (t || CommonModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CommonModule,\n      imports: [NgClass, NgComponentOutlet, NgForOf, NgIf, NgTemplateOutlet, NgStyle, NgSwitch, NgSwitchCase, NgSwitchDefault, NgPlural, NgPluralCase, AsyncPipe, UpperCasePipe, LowerCasePipe, JsonPipe, SlicePipe, DecimalPipe, PercentPipe, TitleCasePipe, CurrencyPipe, DatePipe, I18nPluralPipe, I18nSelectPipe, KeyValuePipe],\n      exports: [NgClass, NgComponentOutlet, NgForOf, NgIf, NgTemplateOutlet, NgStyle, NgSwitch, NgSwitchCase, NgSwitchDefault, NgPlural, NgPluralCase, AsyncPipe, UpperCasePipe, LowerCasePipe, JsonPipe, SlicePipe, DecimalPipe, PercentPipe, TitleCasePipe, CurrencyPipe, DatePipe, I18nPluralPipe, I18nSelectPipe, KeyValuePipe]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CommonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [COMMON_DIRECTIVES, COMMON_PIPES],\n      exports: [COMMON_DIRECTIVES, COMMON_PIPES]\n    }]\n  }], null, null);\n})();\nconst PLATFORM_BROWSER_ID = 'browser';\nconst PLATFORM_SERVER_ID = 'server';\nconst PLATFORM_WORKER_APP_ID = 'browserWorkerApp';\nconst PLATFORM_WORKER_UI_ID = 'browserWorkerUi';\n/**\n * Returns whether a platform id represents a browser platform.\n * @publicApi\n */\nfunction isPlatformBrowser(platformId) {\n  return platformId === PLATFORM_BROWSER_ID;\n}\n/**\n * Returns whether a platform id represents a server platform.\n * @publicApi\n */\nfunction isPlatformServer(platformId) {\n  return platformId === PLATFORM_SERVER_ID;\n}\n/**\n * Returns whether a platform id represents a web worker app platform.\n * @publicApi\n * @deprecated This function serves no purpose since the removal of the Webworker platform. It will\n *     always return `false`.\n */\nfunction isPlatformWorkerApp(platformId) {\n  return platformId === PLATFORM_WORKER_APP_ID;\n}\n/**\n * Returns whether a platform id represents a web worker UI platform.\n * @publicApi\n * @deprecated This function serves no purpose since the removal of the Webworker platform. It will\n *     always return `false`.\n */\nfunction isPlatformWorkerUi(platformId) {\n  return platformId === PLATFORM_WORKER_UI_ID;\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('17.3.12');\n\n/**\n * Defines a scroll position manager. Implemented by `BrowserViewportScroller`.\n *\n * @publicApi\n */\nclass ViewportScroller {\n  // De-sugared tree-shakable injection\n  // See #23917\n  /** @nocollapse */\n  static {\n    this.ɵprov = ɵɵdefineInjectable({\n      token: ViewportScroller,\n      providedIn: 'root',\n      factory: () => isPlatformBrowser(inject(PLATFORM_ID)) ? new BrowserViewportScroller(inject(DOCUMENT), window) : new NullViewportScroller()\n    });\n  }\n}\n/**\n * Manages the scroll position for a browser window.\n */\nclass BrowserViewportScroller {\n  constructor(document, window) {\n    this.document = document;\n    this.window = window;\n    this.offset = () => [0, 0];\n  }\n  /**\n   * Configures the top offset used when scrolling to an anchor.\n   * @param offset A position in screen coordinates (a tuple with x and y values)\n   * or a function that returns the top offset position.\n   *\n   */\n  setOffset(offset) {\n    if (Array.isArray(offset)) {\n      this.offset = () => offset;\n    } else {\n      this.offset = offset;\n    }\n  }\n  /**\n   * Retrieves the current scroll position.\n   * @returns The position in screen coordinates.\n   */\n  getScrollPosition() {\n    return [this.window.scrollX, this.window.scrollY];\n  }\n  /**\n   * Sets the scroll position.\n   * @param position The new position in screen coordinates.\n   */\n  scrollToPosition(position) {\n    this.window.scrollTo(position[0], position[1]);\n  }\n  /**\n   * Scrolls to an element and attempts to focus the element.\n   *\n   * Note that the function name here is misleading in that the target string may be an ID for a\n   * non-anchor element.\n   *\n   * @param target The ID of an element or name of the anchor.\n   *\n   * @see https://html.spec.whatwg.org/#the-indicated-part-of-the-document\n   * @see https://html.spec.whatwg.org/#scroll-to-fragid\n   */\n  scrollToAnchor(target) {\n    const elSelected = findAnchorFromDocument(this.document, target);\n    if (elSelected) {\n      this.scrollToElement(elSelected);\n      // After scrolling to the element, the spec dictates that we follow the focus steps for the\n      // target. Rather than following the robust steps, simply attempt focus.\n      //\n      // @see https://html.spec.whatwg.org/#get-the-focusable-area\n      // @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLOrForeignElement/focus\n      // @see https://html.spec.whatwg.org/#focusable-area\n      elSelected.focus();\n    }\n  }\n  /**\n   * Disables automatic scroll restoration provided by the browser.\n   */\n  setHistoryScrollRestoration(scrollRestoration) {\n    this.window.history.scrollRestoration = scrollRestoration;\n  }\n  /**\n   * Scrolls to an element using the native offset and the specified offset set on this scroller.\n   *\n   * The offset can be used when we know that there is a floating header and scrolling naively to an\n   * element (ex: `scrollIntoView`) leaves the element hidden behind the floating header.\n   */\n  scrollToElement(el) {\n    const rect = el.getBoundingClientRect();\n    const left = rect.left + this.window.pageXOffset;\n    const top = rect.top + this.window.pageYOffset;\n    const offset = this.offset();\n    this.window.scrollTo(left - offset[0], top - offset[1]);\n  }\n}\nfunction findAnchorFromDocument(document, target) {\n  const documentResult = document.getElementById(target) || document.getElementsByName(target)[0];\n  if (documentResult) {\n    return documentResult;\n  }\n  // `getElementById` and `getElementsByName` won't pierce through the shadow DOM so we\n  // have to traverse the DOM manually and do the lookup through the shadow roots.\n  if (typeof document.createTreeWalker === 'function' && document.body && typeof document.body.attachShadow === 'function') {\n    const treeWalker = document.createTreeWalker(document.body, NodeFilter.SHOW_ELEMENT);\n    let currentNode = treeWalker.currentNode;\n    while (currentNode) {\n      const shadowRoot = currentNode.shadowRoot;\n      if (shadowRoot) {\n        // Note that `ShadowRoot` doesn't support `getElementsByName`\n        // so we have to fall back to `querySelector`.\n        const result = shadowRoot.getElementById(target) || shadowRoot.querySelector(`[name=\"${target}\"]`);\n        if (result) {\n          return result;\n        }\n      }\n      currentNode = treeWalker.nextNode();\n    }\n  }\n  return null;\n}\n/**\n * Provides an empty implementation of the viewport scroller.\n */\nclass NullViewportScroller {\n  /**\n   * Empty implementation\n   */\n  setOffset(offset) {}\n  /**\n   * Empty implementation\n   */\n  getScrollPosition() {\n    return [0, 0];\n  }\n  /**\n   * Empty implementation\n   */\n  scrollToPosition(position) {}\n  /**\n   * Empty implementation\n   */\n  scrollToAnchor(anchor) {}\n  /**\n   * Empty implementation\n   */\n  setHistoryScrollRestoration(scrollRestoration) {}\n}\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n */\nclass XhrFactory {}\n\n/**\n * Value (out of 100) of the requested quality for placeholder images.\n */\nconst PLACEHOLDER_QUALITY = '20';\n\n// Converts a string that represents a URL into a URL class instance.\nfunction getUrl(src, win) {\n  // Don't use a base URL is the URL is absolute.\n  return isAbsoluteUrl(src) ? new URL(src) : new URL(src, win.location.href);\n}\n// Checks whether a URL is absolute (i.e. starts with `http://` or `https://`).\nfunction isAbsoluteUrl(src) {\n  return /^https?:\\/\\//.test(src);\n}\n// Given a URL, extract the hostname part.\n// If a URL is a relative one - the URL is returned as is.\nfunction extractHostname(url) {\n  return isAbsoluteUrl(url) ? new URL(url).hostname : url;\n}\nfunction isValidPath(path) {\n  const isString = typeof path === 'string';\n  if (!isString || path.trim() === '') {\n    return false;\n  }\n  // Calling new URL() will throw if the path string is malformed\n  try {\n    const url = new URL(path);\n    return true;\n  } catch {\n    return false;\n  }\n}\nfunction normalizePath(path) {\n  return path.endsWith('/') ? path.slice(0, -1) : path;\n}\nfunction normalizeSrc(src) {\n  return src.startsWith('/') ? src.slice(1) : src;\n}\n\n/**\n * Noop image loader that does no transformation to the original src and just returns it as is.\n * This loader is used as a default one if more specific logic is not provided in an app config.\n *\n * @see {@link ImageLoader}\n * @see {@link NgOptimizedImage}\n */\nconst noopImageLoader = config => config.src;\n/**\n * Injection token that configures the image loader function.\n *\n * @see {@link ImageLoader}\n * @see {@link NgOptimizedImage}\n * @publicApi\n */\nconst IMAGE_LOADER = new InjectionToken(ngDevMode ? 'ImageLoader' : '', {\n  providedIn: 'root',\n  factory: () => noopImageLoader\n});\n/**\n * Internal helper function that makes it easier to introduce custom image loaders for the\n * `NgOptimizedImage` directive. It is enough to specify a URL builder function to obtain full DI\n * configuration for a given loader: a DI token corresponding to the actual loader function, plus DI\n * tokens managing preconnect check functionality.\n * @param buildUrlFn a function returning a full URL based on loader's configuration\n * @param exampleUrls example of full URLs for a given loader (used in error messages)\n * @returns a set of DI providers corresponding to the configured image loader\n */\nfunction createImageLoader(buildUrlFn, exampleUrls) {\n  return function provideImageLoader(path) {\n    if (!isValidPath(path)) {\n      throwInvalidPathError(path, exampleUrls || []);\n    }\n    // The trailing / is stripped (if provided) to make URL construction (concatenation) easier in\n    // the individual loader functions.\n    path = normalizePath(path);\n    const loaderFn = config => {\n      if (isAbsoluteUrl(config.src)) {\n        // Image loader functions expect an image file name (e.g. `my-image.png`)\n        // or a relative path + a file name (e.g. `/a/b/c/my-image.png`) as an input,\n        // so the final absolute URL can be constructed.\n        // When an absolute URL is provided instead - the loader can not\n        // build a final URL, thus the error is thrown to indicate that.\n        throwUnexpectedAbsoluteUrlError(path, config.src);\n      }\n      return buildUrlFn(path, {\n        ...config,\n        src: normalizeSrc(config.src)\n      });\n    };\n    const providers = [{\n      provide: IMAGE_LOADER,\n      useValue: loaderFn\n    }];\n    return providers;\n  };\n}\nfunction throwInvalidPathError(path, exampleUrls) {\n  throw new ɵRuntimeError(2959 /* RuntimeErrorCode.INVALID_LOADER_ARGUMENTS */, ngDevMode && `Image loader has detected an invalid path (\\`${path}\\`). ` + `To fix this, supply a path using one of the following formats: ${exampleUrls.join(' or ')}`);\n}\nfunction throwUnexpectedAbsoluteUrlError(path, url) {\n  throw new ɵRuntimeError(2959 /* RuntimeErrorCode.INVALID_LOADER_ARGUMENTS */, ngDevMode && `Image loader has detected a \\`<img>\\` tag with an invalid \\`ngSrc\\` attribute: ${url}. ` + `This image loader expects \\`ngSrc\\` to be a relative URL - ` + `however the provided value is an absolute URL. ` + `To fix this, provide \\`ngSrc\\` as a path relative to the base URL ` + `configured for this loader (\\`${path}\\`).`);\n}\n\n/**\n * Function that generates an ImageLoader for [Cloudflare Image\n * Resizing](https://developers.cloudflare.com/images/image-resizing/) and turns it into an Angular\n * provider. Note: Cloudflare has multiple image products - this provider is specifically for\n * Cloudflare Image Resizing; it will not work with Cloudflare Images or Cloudflare Polish.\n *\n * @param path Your domain name, e.g. https://mysite.com\n * @returns Provider that provides an ImageLoader function\n *\n * @publicApi\n */\nconst provideCloudflareLoader = createImageLoader(createCloudflareUrl, ngDevMode ? ['https://<ZONE>/cdn-cgi/image/<OPTIONS>/<SOURCE-IMAGE>'] : undefined);\nfunction createCloudflareUrl(path, config) {\n  let params = `format=auto`;\n  if (config.width) {\n    params += `,width=${config.width}`;\n  }\n  // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n  if (config.isPlaceholder) {\n    params += `,quality=${PLACEHOLDER_QUALITY}`;\n  }\n  // Cloudflare image URLs format:\n  // https://developers.cloudflare.com/images/image-resizing/url-format/\n  return `${path}/cdn-cgi/image/${params}/${config.src}`;\n}\n\n/**\n * Name and URL tester for Cloudinary.\n */\nconst cloudinaryLoaderInfo = {\n  name: 'Cloudinary',\n  testUrl: isCloudinaryUrl\n};\nconst CLOUDINARY_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.cloudinary\\.com\\/.+/;\n/**\n * Tests whether a URL is from Cloudinary CDN.\n */\nfunction isCloudinaryUrl(url) {\n  return CLOUDINARY_LOADER_REGEX.test(url);\n}\n/**\n * Function that generates an ImageLoader for Cloudinary and turns it into an Angular provider.\n *\n * @param path Base URL of your Cloudinary images\n * This URL should match one of the following formats:\n * https://res.cloudinary.com/mysite\n * https://mysite.cloudinary.com\n * https://subdomain.mysite.com\n * @returns Set of providers to configure the Cloudinary loader.\n *\n * @publicApi\n */\nconst provideCloudinaryLoader = createImageLoader(createCloudinaryUrl, ngDevMode ? ['https://res.cloudinary.com/mysite', 'https://mysite.cloudinary.com', 'https://subdomain.mysite.com'] : undefined);\nfunction createCloudinaryUrl(path, config) {\n  // Cloudinary image URLformat:\n  // https://cloudinary.com/documentation/image_transformations#transformation_url_structure\n  // Example of a Cloudinary image URL:\n  // https://res.cloudinary.com/mysite/image/upload/c_scale,f_auto,q_auto,w_600/marketing/tile-topics-m.png\n  // For a placeholder image, we use the lowest image setting available to reduce the load time\n  // else we use the auto size\n  const quality = config.isPlaceholder ? 'q_auto:low' : 'q_auto';\n  let params = `f_auto,${quality}`;\n  if (config.width) {\n    params += `,w_${config.width}`;\n  }\n  return `${path}/image/upload/${params}/${config.src}`;\n}\n\n/**\n * Name and URL tester for ImageKit.\n */\nconst imageKitLoaderInfo = {\n  name: 'ImageKit',\n  testUrl: isImageKitUrl\n};\nconst IMAGE_KIT_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.imagekit\\.io\\/.+/;\n/**\n * Tests whether a URL is from ImageKit CDN.\n */\nfunction isImageKitUrl(url) {\n  return IMAGE_KIT_LOADER_REGEX.test(url);\n}\n/**\n * Function that generates an ImageLoader for ImageKit and turns it into an Angular provider.\n *\n * @param path Base URL of your ImageKit images\n * This URL should match one of the following formats:\n * https://ik.imagekit.io/myaccount\n * https://subdomain.mysite.com\n * @returns Set of providers to configure the ImageKit loader.\n *\n * @publicApi\n */\nconst provideImageKitLoader = createImageLoader(createImagekitUrl, ngDevMode ? ['https://ik.imagekit.io/mysite', 'https://subdomain.mysite.com'] : undefined);\nfunction createImagekitUrl(path, config) {\n  // Example of an ImageKit image URL:\n  // https://ik.imagekit.io/demo/tr:w-300,h-300/medium_cafe_B1iTdD0C.jpg\n  const {\n    src,\n    width\n  } = config;\n  const params = [];\n  if (width) {\n    params.push(`w-${width}`);\n  }\n  // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n  if (config.isPlaceholder) {\n    params.push(`q-${PLACEHOLDER_QUALITY}`);\n  }\n  const urlSegments = params.length ? [path, `tr:${params.join(',')}`, src] : [path, src];\n  const url = new URL(urlSegments.join('/'));\n  return url.href;\n}\n\n/**\n * Name and URL tester for Imgix.\n */\nconst imgixLoaderInfo = {\n  name: 'Imgix',\n  testUrl: isImgixUrl\n};\nconst IMGIX_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.imgix\\.net\\/.+/;\n/**\n * Tests whether a URL is from Imgix CDN.\n */\nfunction isImgixUrl(url) {\n  return IMGIX_LOADER_REGEX.test(url);\n}\n/**\n * Function that generates an ImageLoader for Imgix and turns it into an Angular provider.\n *\n * @param path path to the desired Imgix origin,\n * e.g. https://somepath.imgix.net or https://images.mysite.com\n * @returns Set of providers to configure the Imgix loader.\n *\n * @publicApi\n */\nconst provideImgixLoader = createImageLoader(createImgixUrl, ngDevMode ? ['https://somepath.imgix.net/'] : undefined);\nfunction createImgixUrl(path, config) {\n  const url = new URL(`${path}/${config.src}`);\n  // This setting ensures the smallest allowable format is set.\n  url.searchParams.set('auto', 'format');\n  if (config.width) {\n    url.searchParams.set('w', config.width.toString());\n  }\n  // When requesting a placeholder image we ask a low quality image to reduce the load time.\n  if (config.isPlaceholder) {\n    url.searchParams.set('q', PLACEHOLDER_QUALITY);\n  }\n  return url.href;\n}\n\n/**\n * Name and URL tester for Netlify.\n */\nconst netlifyLoaderInfo = {\n  name: 'Netlify',\n  testUrl: isNetlifyUrl\n};\nconst NETLIFY_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.netlify\\.app\\/.+/;\n/**\n * Tests whether a URL is from a Netlify site. This won't catch sites with a custom domain,\n * but it's a good start for sites in development. This is only used to warn users who haven't\n * configured an image loader.\n */\nfunction isNetlifyUrl(url) {\n  return NETLIFY_LOADER_REGEX.test(url);\n}\n/**\n * Function that generates an ImageLoader for Netlify and turns it into an Angular provider.\n *\n * @param path optional URL of the desired Netlify site. Defaults to the current site.\n * @returns Set of providers to configure the Netlify loader.\n *\n * @publicApi\n */\nfunction provideNetlifyLoader(path) {\n  if (path && !isValidPath(path)) {\n    throw new ɵRuntimeError(2959 /* RuntimeErrorCode.INVALID_LOADER_ARGUMENTS */, ngDevMode && `Image loader has detected an invalid path (\\`${path}\\`). ` + `To fix this, supply either the full URL to the Netlify site, or leave it empty to use the current site.`);\n  }\n  if (path) {\n    const url = new URL(path);\n    path = url.origin;\n  }\n  const loaderFn = config => {\n    return createNetlifyUrl(config, path);\n  };\n  const providers = [{\n    provide: IMAGE_LOADER,\n    useValue: loaderFn\n  }];\n  return providers;\n}\nconst validParams = new Map([['height', 'h'], ['fit', 'fit'], ['quality', 'q'], ['q', 'q'], ['position', 'position']]);\nfunction createNetlifyUrl(config, path) {\n  // Note: `path` can be undefined, in which case we use a fake one to construct a `URL` instance.\n  const url = new URL(path ?? 'https://a/');\n  url.pathname = '/.netlify/images';\n  if (!isAbsoluteUrl(config.src) && !config.src.startsWith('/')) {\n    config.src = '/' + config.src;\n  }\n  url.searchParams.set('url', config.src);\n  if (config.width) {\n    url.searchParams.set('w', config.width.toString());\n  }\n  // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n  // If the quality is specified in the loader config - always use provided value.\n  const configQuality = config.loaderParams?.['quality'] ?? config.loaderParams?.['q'];\n  if (config.isPlaceholder && !configQuality) {\n    url.searchParams.set('q', PLACEHOLDER_QUALITY);\n  }\n  for (const [param, value] of Object.entries(config.loaderParams ?? {})) {\n    if (validParams.has(param)) {\n      url.searchParams.set(validParams.get(param), value.toString());\n    } else {\n      if (ngDevMode) {\n        console.warn(ɵformatRuntimeError(2959 /* RuntimeErrorCode.INVALID_LOADER_ARGUMENTS */, `The Netlify image loader has detected an \\`<img>\\` tag with the unsupported attribute \"\\`${param}\\`\".`));\n      }\n    }\n  }\n  // The \"a\" hostname is used for relative URLs, so we can remove it from the final URL.\n  return url.hostname === 'a' ? url.href.replace(url.origin, '') : url.href;\n}\n\n// Assembles directive details string, useful for error messages.\nfunction imgDirectiveDetails(ngSrc, includeNgSrc = true) {\n  const ngSrcInfo = includeNgSrc ? `(activated on an <img> element with the \\`ngSrc=\"${ngSrc}\"\\`) ` : '';\n  return `The NgOptimizedImage directive ${ngSrcInfo}has detected that`;\n}\n\n/**\n * Asserts that the application is in development mode. Throws an error if the application is in\n * production mode. This assert can be used to make sure that there is no dev-mode code invoked in\n * the prod mode accidentally.\n */\nfunction assertDevMode(checkName) {\n  if (!ngDevMode) {\n    throw new ɵRuntimeError(2958 /* RuntimeErrorCode.UNEXPECTED_DEV_MODE_CHECK_IN_PROD_MODE */, `Unexpected invocation of the ${checkName} in the prod mode. ` + `Please make sure that the prod mode is enabled for production builds.`);\n  }\n}\n\n/**\n * Observer that detects whether an image with `NgOptimizedImage`\n * is treated as a Largest Contentful Paint (LCP) element. If so,\n * asserts that the image has the `priority` attribute.\n *\n * Note: this is a dev-mode only class and it does not appear in prod bundles,\n * thus there is no `ngDevMode` use in the code.\n *\n * Based on https://web.dev/lcp/#measure-lcp-in-javascript.\n */\nclass LCPImageObserver {\n  constructor() {\n    // Map of full image URLs -> original `ngSrc` values.\n    this.images = new Map();\n    this.window = null;\n    this.observer = null;\n    assertDevMode('LCP checker');\n    const win = inject(DOCUMENT).defaultView;\n    if (typeof win !== 'undefined' && typeof PerformanceObserver !== 'undefined') {\n      this.window = win;\n      this.observer = this.initPerformanceObserver();\n    }\n  }\n  /**\n   * Inits PerformanceObserver and subscribes to LCP events.\n   * Based on https://web.dev/lcp/#measure-lcp-in-javascript\n   */\n  initPerformanceObserver() {\n    const observer = new PerformanceObserver(entryList => {\n      const entries = entryList.getEntries();\n      if (entries.length === 0) return;\n      // We use the latest entry produced by the `PerformanceObserver` as the best\n      // signal on which element is actually an LCP one. As an example, the first image to load on\n      // a page, by virtue of being the only thing on the page so far, is often a LCP candidate\n      // and gets reported by PerformanceObserver, but isn't necessarily the LCP element.\n      const lcpElement = entries[entries.length - 1];\n      // Cast to `any` due to missing `element` on the `LargestContentfulPaint` type of entry.\n      // See https://developer.mozilla.org/en-US/docs/Web/API/LargestContentfulPaint\n      const imgSrc = lcpElement.element?.src ?? '';\n      // Exclude `data:` and `blob:` URLs, since they are not supported by the directive.\n      if (imgSrc.startsWith('data:') || imgSrc.startsWith('blob:')) return;\n      const img = this.images.get(imgSrc);\n      if (!img) return;\n      if (!img.priority && !img.alreadyWarnedPriority) {\n        img.alreadyWarnedPriority = true;\n        logMissingPriorityError(imgSrc);\n      }\n      if (img.modified && !img.alreadyWarnedModified) {\n        img.alreadyWarnedModified = true;\n        logModifiedWarning(imgSrc);\n      }\n    });\n    observer.observe({\n      type: 'largest-contentful-paint',\n      buffered: true\n    });\n    return observer;\n  }\n  registerImage(rewrittenSrc, originalNgSrc, isPriority) {\n    if (!this.observer) return;\n    const newObservedImageState = {\n      priority: isPriority,\n      modified: false,\n      alreadyWarnedModified: false,\n      alreadyWarnedPriority: false\n    };\n    this.images.set(getUrl(rewrittenSrc, this.window).href, newObservedImageState);\n  }\n  unregisterImage(rewrittenSrc) {\n    if (!this.observer) return;\n    this.images.delete(getUrl(rewrittenSrc, this.window).href);\n  }\n  updateImage(originalSrc, newSrc) {\n    const originalUrl = getUrl(originalSrc, this.window).href;\n    const img = this.images.get(originalUrl);\n    if (img) {\n      img.modified = true;\n      this.images.set(getUrl(newSrc, this.window).href, img);\n      this.images.delete(originalUrl);\n    }\n  }\n  ngOnDestroy() {\n    if (!this.observer) return;\n    this.observer.disconnect();\n    this.images.clear();\n  }\n  static {\n    this.ɵfac = function LCPImageObserver_Factory(t) {\n      return new (t || LCPImageObserver)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LCPImageObserver,\n      factory: LCPImageObserver.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LCPImageObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nfunction logMissingPriorityError(ngSrc) {\n  const directiveDetails = imgDirectiveDetails(ngSrc);\n  console.error(ɵformatRuntimeError(2955 /* RuntimeErrorCode.LCP_IMG_MISSING_PRIORITY */, `${directiveDetails} this image is the Largest Contentful Paint (LCP) ` + `element but was not marked \"priority\". This image should be marked ` + `\"priority\" in order to prioritize its loading. ` + `To fix this, add the \"priority\" attribute.`));\n}\nfunction logModifiedWarning(ngSrc) {\n  const directiveDetails = imgDirectiveDetails(ngSrc);\n  console.warn(ɵformatRuntimeError(2964 /* RuntimeErrorCode.LCP_IMG_NGSRC_MODIFIED */, `${directiveDetails} this image is the Largest Contentful Paint (LCP) ` + `element and has had its \"ngSrc\" attribute modified. This can cause ` + `slower loading performance. It is recommended not to modify the \"ngSrc\" ` + `property on any image which could be the LCP element.`));\n}\n\n// Set of origins that are always excluded from the preconnect checks.\nconst INTERNAL_PRECONNECT_CHECK_BLOCKLIST = new Set(['localhost', '127.0.0.1', '0.0.0.0']);\n/**\n * Injection token to configure which origins should be excluded\n * from the preconnect checks. It can either be a single string or an array of strings\n * to represent a group of origins, for example:\n *\n * ```typescript\n *  {provide: PRECONNECT_CHECK_BLOCKLIST, useValue: 'https://your-domain.com'}\n * ```\n *\n * or:\n *\n * ```typescript\n *  {provide: PRECONNECT_CHECK_BLOCKLIST,\n *   useValue: ['https://your-domain-1.com', 'https://your-domain-2.com']}\n * ```\n *\n * @publicApi\n */\nconst PRECONNECT_CHECK_BLOCKLIST = new InjectionToken(ngDevMode ? 'PRECONNECT_CHECK_BLOCKLIST' : '');\n/**\n * Contains the logic to detect whether an image, marked with the \"priority\" attribute\n * has a corresponding `<link rel=\"preconnect\">` tag in the `document.head`.\n *\n * Note: this is a dev-mode only class, which should not appear in prod bundles,\n * thus there is no `ngDevMode` use in the code.\n */\nclass PreconnectLinkChecker {\n  constructor() {\n    this.document = inject(DOCUMENT);\n    /**\n     * Set of <link rel=\"preconnect\"> tags found on this page.\n     * The `null` value indicates that there was no DOM query operation performed.\n     */\n    this.preconnectLinks = null;\n    /*\n     * Keep track of all already seen origin URLs to avoid repeating the same check.\n     */\n    this.alreadySeen = new Set();\n    this.window = null;\n    this.blocklist = new Set(INTERNAL_PRECONNECT_CHECK_BLOCKLIST);\n    assertDevMode('preconnect link checker');\n    const win = this.document.defaultView;\n    if (typeof win !== 'undefined') {\n      this.window = win;\n    }\n    const blocklist = inject(PRECONNECT_CHECK_BLOCKLIST, {\n      optional: true\n    });\n    if (blocklist) {\n      this.populateBlocklist(blocklist);\n    }\n  }\n  populateBlocklist(origins) {\n    if (Array.isArray(origins)) {\n      deepForEach(origins, origin => {\n        this.blocklist.add(extractHostname(origin));\n      });\n    } else {\n      this.blocklist.add(extractHostname(origins));\n    }\n  }\n  /**\n   * Checks that a preconnect resource hint exists in the head for the\n   * given src.\n   *\n   * @param rewrittenSrc src formatted with loader\n   * @param originalNgSrc ngSrc value\n   */\n  assertPreconnect(rewrittenSrc, originalNgSrc) {\n    if (!this.window) return;\n    const imgUrl = getUrl(rewrittenSrc, this.window);\n    if (this.blocklist.has(imgUrl.hostname) || this.alreadySeen.has(imgUrl.origin)) return;\n    // Register this origin as seen, so we don't check it again later.\n    this.alreadySeen.add(imgUrl.origin);\n    // Note: we query for preconnect links only *once* and cache the results\n    // for the entire lifespan of an application, since it's unlikely that the\n    // list would change frequently. This allows to make sure there are no\n    // performance implications of making extra DOM lookups for each image.\n    this.preconnectLinks ??= this.queryPreconnectLinks();\n    if (!this.preconnectLinks.has(imgUrl.origin)) {\n      console.warn(ɵformatRuntimeError(2956 /* RuntimeErrorCode.PRIORITY_IMG_MISSING_PRECONNECT_TAG */, `${imgDirectiveDetails(originalNgSrc)} there is no preconnect tag present for this ` + `image. Preconnecting to the origin(s) that serve priority images ensures that these ` + `images are delivered as soon as possible. To fix this, please add the following ` + `element into the <head> of the document:\\n` + `  <link rel=\"preconnect\" href=\"${imgUrl.origin}\">`));\n    }\n  }\n  queryPreconnectLinks() {\n    const preconnectUrls = new Set();\n    const selector = 'link[rel=preconnect]';\n    const links = Array.from(this.document.querySelectorAll(selector));\n    for (let link of links) {\n      const url = getUrl(link.href, this.window);\n      preconnectUrls.add(url.origin);\n    }\n    return preconnectUrls;\n  }\n  ngOnDestroy() {\n    this.preconnectLinks?.clear();\n    this.alreadySeen.clear();\n  }\n  static {\n    this.ɵfac = function PreconnectLinkChecker_Factory(t) {\n      return new (t || PreconnectLinkChecker)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PreconnectLinkChecker,\n      factory: PreconnectLinkChecker.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PreconnectLinkChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Invokes a callback for each element in the array. Also invokes a callback\n * recursively for each nested array.\n */\nfunction deepForEach(input, fn) {\n  for (let value of input) {\n    Array.isArray(value) ? deepForEach(value, fn) : fn(value);\n  }\n}\n\n/**\n * In SSR scenarios, a preload `<link>` element is generated for priority images.\n * Having a large number of preload tags may negatively affect the performance,\n * so we warn developers (by throwing an error) if the number of preloaded images\n * is above a certain threshold. This const specifies this threshold.\n */\nconst DEFAULT_PRELOADED_IMAGES_LIMIT = 5;\n/**\n * Helps to keep track of priority images that already have a corresponding\n * preload tag (to avoid generating multiple preload tags with the same URL).\n *\n * This Set tracks the original src passed into the `ngSrc` input not the src after it has been\n * run through the specified `IMAGE_LOADER`.\n */\nconst PRELOADED_IMAGES = new InjectionToken('NG_OPTIMIZED_PRELOADED_IMAGES', {\n  providedIn: 'root',\n  factory: () => new Set()\n});\n\n/**\n * @description Contains the logic needed to track and add preload link tags to the `<head>` tag. It\n * will also track what images have already had preload link tags added so as to not duplicate link\n * tags.\n *\n * In dev mode this service will validate that the number of preloaded images does not exceed the\n * configured default preloaded images limit: {@link DEFAULT_PRELOADED_IMAGES_LIMIT}.\n */\nclass PreloadLinkCreator {\n  constructor() {\n    this.preloadedImages = inject(PRELOADED_IMAGES);\n    this.document = inject(DOCUMENT);\n  }\n  /**\n   * @description Add a preload `<link>` to the `<head>` of the `index.html` that is served from the\n   * server while using Angular Universal and SSR to kick off image loads for high priority images.\n   *\n   * The `sizes` (passed in from the user) and `srcset` (parsed and formatted from `ngSrcset`)\n   * properties used to set the corresponding attributes, `imagesizes` and `imagesrcset`\n   * respectively, on the preload `<link>` tag so that the correctly sized image is preloaded from\n   * the CDN.\n   *\n   * {@link https://web.dev/preload-responsive-images/#imagesrcset-and-imagesizes}\n   *\n   * @param renderer The `Renderer2` passed in from the directive\n   * @param src The original src of the image that is set on the `ngSrc` input.\n   * @param srcset The parsed and formatted srcset created from the `ngSrcset` input\n   * @param sizes The value of the `sizes` attribute passed in to the `<img>` tag\n   */\n  createPreloadLinkTag(renderer, src, srcset, sizes) {\n    if (ngDevMode) {\n      if (this.preloadedImages.size >= DEFAULT_PRELOADED_IMAGES_LIMIT) {\n        throw new ɵRuntimeError(2961 /* RuntimeErrorCode.TOO_MANY_PRELOADED_IMAGES */, ngDevMode && `The \\`NgOptimizedImage\\` directive has detected that more than ` + `${DEFAULT_PRELOADED_IMAGES_LIMIT} images were marked as priority. ` + `This might negatively affect an overall performance of the page. ` + `To fix this, remove the \"priority\" attribute from images with less priority.`);\n      }\n    }\n    if (this.preloadedImages.has(src)) {\n      return;\n    }\n    this.preloadedImages.add(src);\n    const preload = renderer.createElement('link');\n    renderer.setAttribute(preload, 'as', 'image');\n    renderer.setAttribute(preload, 'href', src);\n    renderer.setAttribute(preload, 'rel', 'preload');\n    renderer.setAttribute(preload, 'fetchpriority', 'high');\n    if (sizes) {\n      renderer.setAttribute(preload, 'imageSizes', sizes);\n    }\n    if (srcset) {\n      renderer.setAttribute(preload, 'imageSrcset', srcset);\n    }\n    renderer.appendChild(this.document.head, preload);\n  }\n  static {\n    this.ɵfac = function PreloadLinkCreator_Factory(t) {\n      return new (t || PreloadLinkCreator)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PreloadLinkCreator,\n      factory: PreloadLinkCreator.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PreloadLinkCreator, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * When a Base64-encoded image is passed as an input to the `NgOptimizedImage` directive,\n * an error is thrown. The image content (as a string) might be very long, thus making\n * it hard to read an error message if the entire string is included. This const defines\n * the number of characters that should be included into the error message. The rest\n * of the content is truncated.\n */\nconst BASE64_IMG_MAX_LENGTH_IN_ERROR = 50;\n/**\n * RegExpr to determine whether a src in a srcset is using width descriptors.\n * Should match something like: \"100w, 200w\".\n */\nconst VALID_WIDTH_DESCRIPTOR_SRCSET = /^((\\s*\\d+w\\s*(,|$)){1,})$/;\n/**\n * RegExpr to determine whether a src in a srcset is using density descriptors.\n * Should match something like: \"1x, 2x, 50x\". Also supports decimals like \"1.5x, 1.50x\".\n */\nconst VALID_DENSITY_DESCRIPTOR_SRCSET = /^((\\s*\\d+(\\.\\d+)?x\\s*(,|$)){1,})$/;\n/**\n * Srcset values with a density descriptor higher than this value will actively\n * throw an error. Such densities are not permitted as they cause image sizes\n * to be unreasonably large and slow down LCP.\n */\nconst ABSOLUTE_SRCSET_DENSITY_CAP = 3;\n/**\n * Used only in error message text to communicate best practices, as we will\n * only throw based on the slightly more conservative ABSOLUTE_SRCSET_DENSITY_CAP.\n */\nconst RECOMMENDED_SRCSET_DENSITY_CAP = 2;\n/**\n * Used in generating automatic density-based srcsets\n */\nconst DENSITY_SRCSET_MULTIPLIERS = [1, 2];\n/**\n * Used to determine which breakpoints to use on full-width images\n */\nconst VIEWPORT_BREAKPOINT_CUTOFF = 640;\n/**\n * Used to determine whether two aspect ratios are similar in value.\n */\nconst ASPECT_RATIO_TOLERANCE = 0.1;\n/**\n * Used to determine whether the image has been requested at an overly\n * large size compared to the actual rendered image size (after taking\n * into account a typical device pixel ratio). In pixels.\n */\nconst OVERSIZED_IMAGE_TOLERANCE = 1000;\n/**\n * Used to limit automatic srcset generation of very large sources for\n * fixed-size images. In pixels.\n */\nconst FIXED_SRCSET_WIDTH_LIMIT = 1920;\nconst FIXED_SRCSET_HEIGHT_LIMIT = 1080;\n/**\n * Default blur radius of the CSS filter used on placeholder images, in pixels\n */\nconst PLACEHOLDER_BLUR_AMOUNT = 15;\n/**\n * Used to warn or error when the user provides an overly large dataURL for the placeholder\n * attribute.\n * Character count of Base64 images is 1 character per byte, and base64 encoding is approximately\n * 33% larger than base images, so 4000 characters is around 3KB on disk and 10000 characters is\n * around 7.7KB. Experimentally, 4000 characters is about 20x20px in PNG or medium-quality JPEG\n * format, and 10,000 is around 50x50px, but there's quite a bit of variation depending on how the\n * image is saved.\n */\nconst DATA_URL_WARN_LIMIT = 4000;\nconst DATA_URL_ERROR_LIMIT = 10000;\n/** Info about built-in loaders we can test for. */\nconst BUILT_IN_LOADERS = [imgixLoaderInfo, imageKitLoaderInfo, cloudinaryLoaderInfo, netlifyLoaderInfo];\n/**\n * Directive that improves image loading performance by enforcing best practices.\n *\n * `NgOptimizedImage` ensures that the loading of the Largest Contentful Paint (LCP) image is\n * prioritized by:\n * - Automatically setting the `fetchpriority` attribute on the `<img>` tag\n * - Lazy loading non-priority images by default\n * - Automatically generating a preconnect link tag in the document head\n *\n * In addition, the directive:\n * - Generates appropriate asset URLs if a corresponding `ImageLoader` function is provided\n * - Automatically generates a srcset\n * - Requires that `width` and `height` are set\n * - Warns if `width` or `height` have been set incorrectly\n * - Warns if the image will be visually distorted when rendered\n *\n * @usageNotes\n * The `NgOptimizedImage` directive is marked as [standalone](guide/standalone-components) and can\n * be imported directly.\n *\n * Follow the steps below to enable and use the directive:\n * 1. Import it into the necessary NgModule or a standalone Component.\n * 2. Optionally provide an `ImageLoader` if you use an image hosting service.\n * 3. Update the necessary `<img>` tags in templates and replace `src` attributes with `ngSrc`.\n * Using a `ngSrc` allows the directive to control when the `src` gets set, which triggers an image\n * download.\n *\n * Step 1: import the `NgOptimizedImage` directive.\n *\n * ```typescript\n * import { NgOptimizedImage } from '@angular/common';\n *\n * // Include it into the necessary NgModule\n * @NgModule({\n *   imports: [NgOptimizedImage],\n * })\n * class AppModule {}\n *\n * // ... or a standalone Component\n * @Component({\n *   standalone: true\n *   imports: [NgOptimizedImage],\n * })\n * class MyStandaloneComponent {}\n * ```\n *\n * Step 2: configure a loader.\n *\n * To use the **default loader**: no additional code changes are necessary. The URL returned by the\n * generic loader will always match the value of \"src\". In other words, this loader applies no\n * transformations to the resource URL and the value of the `ngSrc` attribute will be used as is.\n *\n * To use an existing loader for a **third-party image service**: add the provider factory for your\n * chosen service to the `providers` array. In the example below, the Imgix loader is used:\n *\n * ```typescript\n * import {provideImgixLoader} from '@angular/common';\n *\n * // Call the function and add the result to the `providers` array:\n * providers: [\n *   provideImgixLoader(\"https://my.base.url/\"),\n * ],\n * ```\n *\n * The `NgOptimizedImage` directive provides the following functions:\n * - `provideCloudflareLoader`\n * - `provideCloudinaryLoader`\n * - `provideImageKitLoader`\n * - `provideImgixLoader`\n *\n * If you use a different image provider, you can create a custom loader function as described\n * below.\n *\n * To use a **custom loader**: provide your loader function as a value for the `IMAGE_LOADER` DI\n * token.\n *\n * ```typescript\n * import {IMAGE_LOADER, ImageLoaderConfig} from '@angular/common';\n *\n * // Configure the loader using the `IMAGE_LOADER` token.\n * providers: [\n *   {\n *      provide: IMAGE_LOADER,\n *      useValue: (config: ImageLoaderConfig) => {\n *        return `https://example.com/${config.src}-${config.width}.jpg}`;\n *      }\n *   },\n * ],\n * ```\n *\n * Step 3: update `<img>` tags in templates to use `ngSrc` instead of `src`.\n *\n * ```\n * <img ngSrc=\"logo.png\" width=\"200\" height=\"100\">\n * ```\n *\n * @publicApi\n */\nclass NgOptimizedImage {\n  constructor() {\n    this.imageLoader = inject(IMAGE_LOADER);\n    this.config = processConfig(inject(ɵIMAGE_CONFIG));\n    this.renderer = inject(Renderer2);\n    this.imgElement = inject(ElementRef).nativeElement;\n    this.injector = inject(Injector);\n    this.isServer = isPlatformServer(inject(PLATFORM_ID));\n    this.preloadLinkCreator = inject(PreloadLinkCreator);\n    // a LCP image observer - should be injected only in the dev mode\n    this.lcpObserver = ngDevMode ? this.injector.get(LCPImageObserver) : null;\n    /**\n     * Calculate the rewritten `src` once and store it.\n     * This is needed to avoid repetitive calculations and make sure the directive cleanup in the\n     * `ngOnDestroy` does not rely on the `IMAGE_LOADER` logic (which in turn can rely on some other\n     * instance that might be already destroyed).\n     */\n    this._renderedSrc = null;\n    /**\n     * Indicates whether this image should have a high priority.\n     */\n    this.priority = false;\n    /**\n     * Disables automatic srcset generation for this image.\n     */\n    this.disableOptimizedSrcset = false;\n    /**\n     * Sets the image to \"fill mode\", which eliminates the height/width requirement and adds\n     * styles such that the image fills its containing element.\n     */\n    this.fill = false;\n  }\n  /** @nodoc */\n  ngOnInit() {\n    ɵperformanceMarkFeature('NgOptimizedImage');\n    if (ngDevMode) {\n      const ngZone = this.injector.get(NgZone);\n      assertNonEmptyInput(this, 'ngSrc', this.ngSrc);\n      assertValidNgSrcset(this, this.ngSrcset);\n      assertNoConflictingSrc(this);\n      if (this.ngSrcset) {\n        assertNoConflictingSrcset(this);\n      }\n      assertNotBase64Image(this);\n      assertNotBlobUrl(this);\n      if (this.fill) {\n        assertEmptyWidthAndHeight(this);\n        // This leaves the Angular zone to avoid triggering unnecessary change detection cycles when\n        // `load` tasks are invoked on images.\n        ngZone.runOutsideAngular(() => assertNonZeroRenderedHeight(this, this.imgElement, this.renderer));\n      } else {\n        assertNonEmptyWidthAndHeight(this);\n        if (this.height !== undefined) {\n          assertGreaterThanZero(this, this.height, 'height');\n        }\n        if (this.width !== undefined) {\n          assertGreaterThanZero(this, this.width, 'width');\n        }\n        // Only check for distorted images when not in fill mode, where\n        // images may be intentionally stretched, cropped or letterboxed.\n        ngZone.runOutsideAngular(() => assertNoImageDistortion(this, this.imgElement, this.renderer));\n      }\n      assertValidLoadingInput(this);\n      if (!this.ngSrcset) {\n        assertNoComplexSizes(this);\n      }\n      assertValidPlaceholder(this, this.imageLoader);\n      assertNotMissingBuiltInLoader(this.ngSrc, this.imageLoader);\n      assertNoNgSrcsetWithoutLoader(this, this.imageLoader);\n      assertNoLoaderParamsWithoutLoader(this, this.imageLoader);\n      if (this.lcpObserver !== null) {\n        const ngZone = this.injector.get(NgZone);\n        ngZone.runOutsideAngular(() => {\n          this.lcpObserver.registerImage(this.getRewrittenSrc(), this.ngSrc, this.priority);\n        });\n      }\n      if (this.priority) {\n        const checker = this.injector.get(PreconnectLinkChecker);\n        checker.assertPreconnect(this.getRewrittenSrc(), this.ngSrc);\n      }\n    }\n    if (this.placeholder) {\n      this.removePlaceholderOnLoad(this.imgElement);\n    }\n    this.setHostAttributes();\n  }\n  setHostAttributes() {\n    // Must set width/height explicitly in case they are bound (in which case they will\n    // only be reflected and not found by the browser)\n    if (this.fill) {\n      this.sizes ||= '100vw';\n    } else {\n      this.setHostAttribute('width', this.width.toString());\n      this.setHostAttribute('height', this.height.toString());\n    }\n    this.setHostAttribute('loading', this.getLoadingBehavior());\n    this.setHostAttribute('fetchpriority', this.getFetchPriority());\n    // The `data-ng-img` attribute flags an image as using the directive, to allow\n    // for analysis of the directive's performance.\n    this.setHostAttribute('ng-img', 'true');\n    // The `src` and `srcset` attributes should be set last since other attributes\n    // could affect the image's loading behavior.\n    const rewrittenSrcset = this.updateSrcAndSrcset();\n    if (this.sizes) {\n      this.setHostAttribute('sizes', this.sizes);\n    }\n    if (this.isServer && this.priority) {\n      this.preloadLinkCreator.createPreloadLinkTag(this.renderer, this.getRewrittenSrc(), rewrittenSrcset, this.sizes);\n    }\n  }\n  /** @nodoc */\n  ngOnChanges(changes) {\n    if (ngDevMode) {\n      assertNoPostInitInputChange(this, changes, ['ngSrcset', 'width', 'height', 'priority', 'fill', 'loading', 'sizes', 'loaderParams', 'disableOptimizedSrcset']);\n    }\n    if (changes['ngSrc'] && !changes['ngSrc'].isFirstChange()) {\n      const oldSrc = this._renderedSrc;\n      this.updateSrcAndSrcset(true);\n      const newSrc = this._renderedSrc;\n      if (this.lcpObserver !== null && oldSrc && newSrc && oldSrc !== newSrc) {\n        const ngZone = this.injector.get(NgZone);\n        ngZone.runOutsideAngular(() => {\n          this.lcpObserver?.updateImage(oldSrc, newSrc);\n        });\n      }\n    }\n  }\n  callImageLoader(configWithoutCustomParams) {\n    let augmentedConfig = configWithoutCustomParams;\n    if (this.loaderParams) {\n      augmentedConfig.loaderParams = this.loaderParams;\n    }\n    return this.imageLoader(augmentedConfig);\n  }\n  getLoadingBehavior() {\n    if (!this.priority && this.loading !== undefined) {\n      return this.loading;\n    }\n    return this.priority ? 'eager' : 'lazy';\n  }\n  getFetchPriority() {\n    return this.priority ? 'high' : 'auto';\n  }\n  getRewrittenSrc() {\n    // ImageLoaderConfig supports setting a width property. However, we're not setting width here\n    // because if the developer uses rendered width instead of intrinsic width in the HTML width\n    // attribute, the image requested may be too small for 2x+ screens.\n    if (!this._renderedSrc) {\n      const imgConfig = {\n        src: this.ngSrc\n      };\n      // Cache calculated image src to reuse it later in the code.\n      this._renderedSrc = this.callImageLoader(imgConfig);\n    }\n    return this._renderedSrc;\n  }\n  getRewrittenSrcset() {\n    const widthSrcSet = VALID_WIDTH_DESCRIPTOR_SRCSET.test(this.ngSrcset);\n    const finalSrcs = this.ngSrcset.split(',').filter(src => src !== '').map(srcStr => {\n      srcStr = srcStr.trim();\n      const width = widthSrcSet ? parseFloat(srcStr) : parseFloat(srcStr) * this.width;\n      return `${this.callImageLoader({\n        src: this.ngSrc,\n        width\n      })} ${srcStr}`;\n    });\n    return finalSrcs.join(', ');\n  }\n  getAutomaticSrcset() {\n    if (this.sizes) {\n      return this.getResponsiveSrcset();\n    } else {\n      return this.getFixedSrcset();\n    }\n  }\n  getResponsiveSrcset() {\n    const {\n      breakpoints\n    } = this.config;\n    let filteredBreakpoints = breakpoints;\n    if (this.sizes?.trim() === '100vw') {\n      // Since this is a full-screen-width image, our srcset only needs to include\n      // breakpoints with full viewport widths.\n      filteredBreakpoints = breakpoints.filter(bp => bp >= VIEWPORT_BREAKPOINT_CUTOFF);\n    }\n    const finalSrcs = filteredBreakpoints.map(bp => `${this.callImageLoader({\n      src: this.ngSrc,\n      width: bp\n    })} ${bp}w`);\n    return finalSrcs.join(', ');\n  }\n  updateSrcAndSrcset(forceSrcRecalc = false) {\n    if (forceSrcRecalc) {\n      // Reset cached value, so that the followup `getRewrittenSrc()` call\n      // will recalculate it and update the cache.\n      this._renderedSrc = null;\n    }\n    const rewrittenSrc = this.getRewrittenSrc();\n    this.setHostAttribute('src', rewrittenSrc);\n    let rewrittenSrcset = undefined;\n    if (this.ngSrcset) {\n      rewrittenSrcset = this.getRewrittenSrcset();\n    } else if (this.shouldGenerateAutomaticSrcset()) {\n      rewrittenSrcset = this.getAutomaticSrcset();\n    }\n    if (rewrittenSrcset) {\n      this.setHostAttribute('srcset', rewrittenSrcset);\n    }\n    return rewrittenSrcset;\n  }\n  getFixedSrcset() {\n    const finalSrcs = DENSITY_SRCSET_MULTIPLIERS.map(multiplier => `${this.callImageLoader({\n      src: this.ngSrc,\n      width: this.width * multiplier\n    })} ${multiplier}x`);\n    return finalSrcs.join(', ');\n  }\n  shouldGenerateAutomaticSrcset() {\n    let oversizedImage = false;\n    if (!this.sizes) {\n      oversizedImage = this.width > FIXED_SRCSET_WIDTH_LIMIT || this.height > FIXED_SRCSET_HEIGHT_LIMIT;\n    }\n    return !this.disableOptimizedSrcset && !this.srcset && this.imageLoader !== noopImageLoader && !oversizedImage;\n  }\n  /**\n   * Returns an image url formatted for use with the CSS background-image property. Expects one of:\n   * * A base64 encoded image, which is wrapped and passed through.\n   * * A boolean. If true, calls the image loader to generate a small placeholder url.\n   */\n  generatePlaceholder(placeholderInput) {\n    const {\n      placeholderResolution\n    } = this.config;\n    if (placeholderInput === true) {\n      return `url(${this.callImageLoader({\n        src: this.ngSrc,\n        width: placeholderResolution,\n        isPlaceholder: true\n      })})`;\n    } else if (typeof placeholderInput === 'string' && placeholderInput.startsWith('data:')) {\n      return `url(${placeholderInput})`;\n    }\n    return null;\n  }\n  /**\n   * Determines if blur should be applied, based on an optional boolean\n   * property `blur` within the optional configuration object `placeholderConfig`.\n   */\n  shouldBlurPlaceholder(placeholderConfig) {\n    if (!placeholderConfig || !placeholderConfig.hasOwnProperty('blur')) {\n      return true;\n    }\n    return Boolean(placeholderConfig.blur);\n  }\n  removePlaceholderOnLoad(img) {\n    const callback = () => {\n      const changeDetectorRef = this.injector.get(ChangeDetectorRef);\n      removeLoadListenerFn();\n      removeErrorListenerFn();\n      this.placeholder = false;\n      changeDetectorRef.markForCheck();\n    };\n    const removeLoadListenerFn = this.renderer.listen(img, 'load', callback);\n    const removeErrorListenerFn = this.renderer.listen(img, 'error', callback);\n  }\n  /** @nodoc */\n  ngOnDestroy() {\n    if (ngDevMode) {\n      if (!this.priority && this._renderedSrc !== null && this.lcpObserver !== null) {\n        this.lcpObserver.unregisterImage(this._renderedSrc);\n      }\n    }\n  }\n  setHostAttribute(name, value) {\n    this.renderer.setAttribute(this.imgElement, name, value);\n  }\n  static {\n    this.ɵfac = function NgOptimizedImage_Factory(t) {\n      return new (t || NgOptimizedImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgOptimizedImage,\n      selectors: [[\"img\", \"ngSrc\", \"\"]],\n      hostVars: 18,\n      hostBindings: function NgOptimizedImage_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"position\", ctx.fill ? \"absolute\" : null)(\"width\", ctx.fill ? \"100%\" : null)(\"height\", ctx.fill ? \"100%\" : null)(\"inset\", ctx.fill ? \"0\" : null)(\"background-size\", ctx.placeholder ? \"cover\" : null)(\"background-position\", ctx.placeholder ? \"50% 50%\" : null)(\"background-repeat\", ctx.placeholder ? \"no-repeat\" : null)(\"background-image\", ctx.placeholder ? ctx.generatePlaceholder(ctx.placeholder) : null)(\"filter\", ctx.placeholder && ctx.shouldBlurPlaceholder(ctx.placeholderConfig) ? \"blur(15px)\" : null);\n        }\n      },\n      inputs: {\n        ngSrc: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"ngSrc\", \"ngSrc\", unwrapSafeUrl],\n        ngSrcset: \"ngSrcset\",\n        sizes: \"sizes\",\n        width: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"width\", \"width\", numberAttribute],\n        height: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"height\", \"height\", numberAttribute],\n        loading: \"loading\",\n        priority: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"priority\", \"priority\", booleanAttribute],\n        loaderParams: \"loaderParams\",\n        disableOptimizedSrcset: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableOptimizedSrcset\", \"disableOptimizedSrcset\", booleanAttribute],\n        fill: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"fill\", \"fill\", booleanAttribute],\n        placeholder: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"placeholder\", \"placeholder\", booleanOrDataUrlAttribute],\n        placeholderConfig: \"placeholderConfig\",\n        src: \"src\",\n        srcset: \"srcset\"\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgOptimizedImage, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'img[ngSrc]',\n      host: {\n        '[style.position]': 'fill ? \"absolute\" : null',\n        '[style.width]': 'fill ? \"100%\" : null',\n        '[style.height]': 'fill ? \"100%\" : null',\n        '[style.inset]': 'fill ? \"0\" : null',\n        '[style.background-size]': 'placeholder ? \"cover\" : null',\n        '[style.background-position]': 'placeholder ? \"50% 50%\" : null',\n        '[style.background-repeat]': 'placeholder ? \"no-repeat\" : null',\n        '[style.background-image]': 'placeholder ? generatePlaceholder(placeholder) : null',\n        '[style.filter]': `placeholder && shouldBlurPlaceholder(placeholderConfig) ? \"blur(${PLACEHOLDER_BLUR_AMOUNT}px)\" : null`\n      }\n    }]\n  }], null, {\n    ngSrc: [{\n      type: Input,\n      args: [{\n        required: true,\n        transform: unwrapSafeUrl\n      }]\n    }],\n    ngSrcset: [{\n      type: Input\n    }],\n    sizes: [{\n      type: Input\n    }],\n    width: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    height: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    loading: [{\n      type: Input\n    }],\n    priority: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    loaderParams: [{\n      type: Input\n    }],\n    disableOptimizedSrcset: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    fill: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input,\n      args: [{\n        transform: booleanOrDataUrlAttribute\n      }]\n    }],\n    placeholderConfig: [{\n      type: Input\n    }],\n    src: [{\n      type: Input\n    }],\n    srcset: [{\n      type: Input\n    }]\n  });\n})();\n/***** Helpers *****/\n/**\n * Sorts provided config breakpoints and uses defaults.\n */\nfunction processConfig(config) {\n  let sortedBreakpoints = {};\n  if (config.breakpoints) {\n    sortedBreakpoints.breakpoints = config.breakpoints.sort((a, b) => a - b);\n  }\n  return Object.assign({}, ɵIMAGE_CONFIG_DEFAULTS, config, sortedBreakpoints);\n}\n/***** Assert functions *****/\n/**\n * Verifies that there is no `src` set on a host element.\n */\nfunction assertNoConflictingSrc(dir) {\n  if (dir.src) {\n    throw new ɵRuntimeError(2950 /* RuntimeErrorCode.UNEXPECTED_SRC_ATTR */, `${imgDirectiveDetails(dir.ngSrc)} both \\`src\\` and \\`ngSrc\\` have been set. ` + `Supplying both of these attributes breaks lazy loading. ` + `The NgOptimizedImage directive sets \\`src\\` itself based on the value of \\`ngSrc\\`. ` + `To fix this, please remove the \\`src\\` attribute.`);\n  }\n}\n/**\n * Verifies that there is no `srcset` set on a host element.\n */\nfunction assertNoConflictingSrcset(dir) {\n  if (dir.srcset) {\n    throw new ɵRuntimeError(2951 /* RuntimeErrorCode.UNEXPECTED_SRCSET_ATTR */, `${imgDirectiveDetails(dir.ngSrc)} both \\`srcset\\` and \\`ngSrcset\\` have been set. ` + `Supplying both of these attributes breaks lazy loading. ` + `The NgOptimizedImage directive sets \\`srcset\\` itself based on the value of ` + `\\`ngSrcset\\`. To fix this, please remove the \\`srcset\\` attribute.`);\n  }\n}\n/**\n * Verifies that the `ngSrc` is not a Base64-encoded image.\n */\nfunction assertNotBase64Image(dir) {\n  let ngSrc = dir.ngSrc.trim();\n  if (ngSrc.startsWith('data:')) {\n    if (ngSrc.length > BASE64_IMG_MAX_LENGTH_IN_ERROR) {\n      ngSrc = ngSrc.substring(0, BASE64_IMG_MAX_LENGTH_IN_ERROR) + '...';\n    }\n    throw new ɵRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc, false)} \\`ngSrc\\` is a Base64-encoded string ` + `(${ngSrc}). NgOptimizedImage does not support Base64-encoded strings. ` + `To fix this, disable the NgOptimizedImage directive for this element ` + `by removing \\`ngSrc\\` and using a standard \\`src\\` attribute instead.`);\n  }\n}\n/**\n * Verifies that the 'sizes' only includes responsive values.\n */\nfunction assertNoComplexSizes(dir) {\n  let sizes = dir.sizes;\n  if (sizes?.match(/((\\)|,)\\s|^)\\d+px/)) {\n    throw new ɵRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc, false)} \\`sizes\\` was set to a string including ` + `pixel values. For automatic \\`srcset\\` generation, \\`sizes\\` must only include responsive ` + `values, such as \\`sizes=\"50vw\"\\` or \\`sizes=\"(min-width: 768px) 50vw, 100vw\"\\`. ` + `To fix this, modify the \\`sizes\\` attribute, or provide your own \\`ngSrcset\\` value directly.`);\n  }\n}\nfunction assertValidPlaceholder(dir, imageLoader) {\n  assertNoPlaceholderConfigWithoutPlaceholder(dir);\n  assertNoRelativePlaceholderWithoutLoader(dir, imageLoader);\n  assertNoOversizedDataUrl(dir);\n}\n/**\n * Verifies that placeholderConfig isn't being used without placeholder\n */\nfunction assertNoPlaceholderConfigWithoutPlaceholder(dir) {\n  if (dir.placeholderConfig && !dir.placeholder) {\n    throw new ɵRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc, false)} \\`placeholderConfig\\` options were provided for an ` + `image that does not use the \\`placeholder\\` attribute, and will have no effect.`);\n  }\n}\n/**\n * Warns if a relative URL placeholder is specified, but no loader is present to provide the small\n * image.\n */\nfunction assertNoRelativePlaceholderWithoutLoader(dir, imageLoader) {\n  if (dir.placeholder === true && imageLoader === noopImageLoader) {\n    throw new ɵRuntimeError(2963 /* RuntimeErrorCode.MISSING_NECESSARY_LOADER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`placeholder\\` attribute is set to true but ` + `no image loader is configured (i.e. the default one is being used), ` + `which would result in the same image being used for the primary image and its placeholder. ` + `To fix this, provide a loader or remove the \\`placeholder\\` attribute from the image.`);\n  }\n}\n/**\n * Warns or throws an error if an oversized dataURL placeholder is provided.\n */\nfunction assertNoOversizedDataUrl(dir) {\n  if (dir.placeholder && typeof dir.placeholder === 'string' && dir.placeholder.startsWith('data:')) {\n    if (dir.placeholder.length > DATA_URL_ERROR_LIMIT) {\n      throw new ɵRuntimeError(2965 /* RuntimeErrorCode.OVERSIZED_PLACEHOLDER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`placeholder\\` attribute is set to a data URL which is longer ` + `than ${DATA_URL_ERROR_LIMIT} characters. This is strongly discouraged, as large inline placeholders ` + `directly increase the bundle size of Angular and hurt page load performance. To fix this, generate ` + `a smaller data URL placeholder.`);\n    }\n    if (dir.placeholder.length > DATA_URL_WARN_LIMIT) {\n      console.warn(ɵformatRuntimeError(2965 /* RuntimeErrorCode.OVERSIZED_PLACEHOLDER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`placeholder\\` attribute is set to a data URL which is longer ` + `than ${DATA_URL_WARN_LIMIT} characters. This is discouraged, as large inline placeholders ` + `directly increase the bundle size of Angular and hurt page load performance. For better loading performance, ` + `generate a smaller data URL placeholder.`));\n    }\n  }\n}\n/**\n * Verifies that the `ngSrc` is not a Blob URL.\n */\nfunction assertNotBlobUrl(dir) {\n  const ngSrc = dir.ngSrc.trim();\n  if (ngSrc.startsWith('blob:')) {\n    throw new ɵRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} \\`ngSrc\\` was set to a blob URL (${ngSrc}). ` + `Blob URLs are not supported by the NgOptimizedImage directive. ` + `To fix this, disable the NgOptimizedImage directive for this element ` + `by removing \\`ngSrc\\` and using a regular \\`src\\` attribute instead.`);\n  }\n}\n/**\n * Verifies that the input is set to a non-empty string.\n */\nfunction assertNonEmptyInput(dir, name, value) {\n  const isString = typeof value === 'string';\n  const isEmptyString = isString && value.trim() === '';\n  if (!isString || isEmptyString) {\n    throw new ɵRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} \\`${name}\\` has an invalid value ` + `(\\`${value}\\`). To fix this, change the value to a non-empty string.`);\n  }\n}\n/**\n * Verifies that the `ngSrcset` is in a valid format, e.g. \"100w, 200w\" or \"1x, 2x\".\n */\nfunction assertValidNgSrcset(dir, value) {\n  if (value == null) return;\n  assertNonEmptyInput(dir, 'ngSrcset', value);\n  const stringVal = value;\n  const isValidWidthDescriptor = VALID_WIDTH_DESCRIPTOR_SRCSET.test(stringVal);\n  const isValidDensityDescriptor = VALID_DENSITY_DESCRIPTOR_SRCSET.test(stringVal);\n  if (isValidDensityDescriptor) {\n    assertUnderDensityCap(dir, stringVal);\n  }\n  const isValidSrcset = isValidWidthDescriptor || isValidDensityDescriptor;\n  if (!isValidSrcset) {\n    throw new ɵRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} \\`ngSrcset\\` has an invalid value (\\`${value}\\`). ` + `To fix this, supply \\`ngSrcset\\` using a comma-separated list of one or more width ` + `descriptors (e.g. \"100w, 200w\") or density descriptors (e.g. \"1x, 2x\").`);\n  }\n}\nfunction assertUnderDensityCap(dir, value) {\n  const underDensityCap = value.split(',').every(num => num === '' || parseFloat(num) <= ABSOLUTE_SRCSET_DENSITY_CAP);\n  if (!underDensityCap) {\n    throw new ɵRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the \\`ngSrcset\\` contains an unsupported image density:` + `\\`${value}\\`. NgOptimizedImage generally recommends a max image density of ` + `${RECOMMENDED_SRCSET_DENSITY_CAP}x but supports image densities up to ` + `${ABSOLUTE_SRCSET_DENSITY_CAP}x. The human eye cannot distinguish between image densities ` + `greater than ${RECOMMENDED_SRCSET_DENSITY_CAP}x - which makes them unnecessary for ` + `most use cases. Images that will be pinch-zoomed are typically the primary use case for ` + `${ABSOLUTE_SRCSET_DENSITY_CAP}x images. Please remove the high density descriptor and try again.`);\n  }\n}\n/**\n * Creates a `RuntimeError` instance to represent a situation when an input is set after\n * the directive has initialized.\n */\nfunction postInitInputChangeError(dir, inputName) {\n  let reason;\n  if (inputName === 'width' || inputName === 'height') {\n    reason = `Changing \\`${inputName}\\` may result in different attribute value ` + `applied to the underlying image element and cause layout shifts on a page.`;\n  } else {\n    reason = `Changing the \\`${inputName}\\` would have no effect on the underlying ` + `image element, because the resource loading has already occurred.`;\n  }\n  return new ɵRuntimeError(2953 /* RuntimeErrorCode.UNEXPECTED_INPUT_CHANGE */, `${imgDirectiveDetails(dir.ngSrc)} \\`${inputName}\\` was updated after initialization. ` + `The NgOptimizedImage directive will not react to this input change. ${reason} ` + `To fix this, either switch \\`${inputName}\\` to a static value ` + `or wrap the image element in an *ngIf that is gated on the necessary value.`);\n}\n/**\n * Verify that none of the listed inputs has changed.\n */\nfunction assertNoPostInitInputChange(dir, changes, inputs) {\n  inputs.forEach(input => {\n    const isUpdated = changes.hasOwnProperty(input);\n    if (isUpdated && !changes[input].isFirstChange()) {\n      if (input === 'ngSrc') {\n        // When the `ngSrc` input changes, we detect that only in the\n        // `ngOnChanges` hook, thus the `ngSrc` is already set. We use\n        // `ngSrc` in the error message, so we use a previous value, but\n        // not the updated one in it.\n        dir = {\n          ngSrc: changes[input].previousValue\n        };\n      }\n      throw postInitInputChangeError(dir, input);\n    }\n  });\n}\n/**\n * Verifies that a specified input is a number greater than 0.\n */\nfunction assertGreaterThanZero(dir, inputValue, inputName) {\n  const validNumber = typeof inputValue === 'number' && inputValue > 0;\n  const validString = typeof inputValue === 'string' && /^\\d+$/.test(inputValue.trim()) && parseInt(inputValue) > 0;\n  if (!validNumber && !validString) {\n    throw new ɵRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} \\`${inputName}\\` has an invalid value. ` + `To fix this, provide \\`${inputName}\\` as a number greater than 0.`);\n  }\n}\n/**\n * Verifies that the rendered image is not visually distorted. Effectively this is checking:\n * - Whether the \"width\" and \"height\" attributes reflect the actual dimensions of the image.\n * - Whether image styling is \"correct\" (see below for a longer explanation).\n */\nfunction assertNoImageDistortion(dir, img, renderer) {\n  const removeLoadListenerFn = renderer.listen(img, 'load', () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n    const computedStyle = window.getComputedStyle(img);\n    let renderedWidth = parseFloat(computedStyle.getPropertyValue('width'));\n    let renderedHeight = parseFloat(computedStyle.getPropertyValue('height'));\n    const boxSizing = computedStyle.getPropertyValue('box-sizing');\n    if (boxSizing === 'border-box') {\n      const paddingTop = computedStyle.getPropertyValue('padding-top');\n      const paddingRight = computedStyle.getPropertyValue('padding-right');\n      const paddingBottom = computedStyle.getPropertyValue('padding-bottom');\n      const paddingLeft = computedStyle.getPropertyValue('padding-left');\n      renderedWidth -= parseFloat(paddingRight) + parseFloat(paddingLeft);\n      renderedHeight -= parseFloat(paddingTop) + parseFloat(paddingBottom);\n    }\n    const renderedAspectRatio = renderedWidth / renderedHeight;\n    const nonZeroRenderedDimensions = renderedWidth !== 0 && renderedHeight !== 0;\n    const intrinsicWidth = img.naturalWidth;\n    const intrinsicHeight = img.naturalHeight;\n    const intrinsicAspectRatio = intrinsicWidth / intrinsicHeight;\n    const suppliedWidth = dir.width;\n    const suppliedHeight = dir.height;\n    const suppliedAspectRatio = suppliedWidth / suppliedHeight;\n    // Tolerance is used to account for the impact of subpixel rendering.\n    // Due to subpixel rendering, the rendered, intrinsic, and supplied\n    // aspect ratios of a correctly configured image may not exactly match.\n    // For example, a `width=4030 height=3020` image might have a rendered\n    // size of \"1062w, 796.48h\". (An aspect ratio of 1.334... vs. 1.333...)\n    const inaccurateDimensions = Math.abs(suppliedAspectRatio - intrinsicAspectRatio) > ASPECT_RATIO_TOLERANCE;\n    const stylingDistortion = nonZeroRenderedDimensions && Math.abs(intrinsicAspectRatio - renderedAspectRatio) > ASPECT_RATIO_TOLERANCE;\n    if (inaccurateDimensions) {\n      console.warn(ɵformatRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the aspect ratio of the image does not match ` + `the aspect ratio indicated by the width and height attributes. ` + `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h ` + `(aspect-ratio: ${round(intrinsicAspectRatio)}). \\nSupplied width and height attributes: ` + `${suppliedWidth}w x ${suppliedHeight}h (aspect-ratio: ${round(suppliedAspectRatio)}). ` + `\\nTo fix this, update the width and height attributes.`));\n    } else if (stylingDistortion) {\n      console.warn(ɵformatRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the aspect ratio of the rendered image ` + `does not match the image's intrinsic aspect ratio. ` + `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h ` + `(aspect-ratio: ${round(intrinsicAspectRatio)}). \\nRendered image size: ` + `${renderedWidth}w x ${renderedHeight}h (aspect-ratio: ` + `${round(renderedAspectRatio)}). \\nThis issue can occur if \"width\" and \"height\" ` + `attributes are added to an image without updating the corresponding ` + `image styling. To fix this, adjust image styling. In most cases, ` + `adding \"height: auto\" or \"width: auto\" to the image styling will fix ` + `this issue.`));\n    } else if (!dir.ngSrcset && nonZeroRenderedDimensions) {\n      // If `ngSrcset` hasn't been set, sanity check the intrinsic size.\n      const recommendedWidth = RECOMMENDED_SRCSET_DENSITY_CAP * renderedWidth;\n      const recommendedHeight = RECOMMENDED_SRCSET_DENSITY_CAP * renderedHeight;\n      const oversizedWidth = intrinsicWidth - recommendedWidth >= OVERSIZED_IMAGE_TOLERANCE;\n      const oversizedHeight = intrinsicHeight - recommendedHeight >= OVERSIZED_IMAGE_TOLERANCE;\n      if (oversizedWidth || oversizedHeight) {\n        console.warn(ɵformatRuntimeError(2960 /* RuntimeErrorCode.OVERSIZED_IMAGE */, `${imgDirectiveDetails(dir.ngSrc)} the intrinsic image is significantly ` + `larger than necessary. ` + `\\nRendered image size: ${renderedWidth}w x ${renderedHeight}h. ` + `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h. ` + `\\nRecommended intrinsic image size: ${recommendedWidth}w x ${recommendedHeight}h. ` + `\\nNote: Recommended intrinsic image size is calculated assuming a maximum DPR of ` + `${RECOMMENDED_SRCSET_DENSITY_CAP}. To improve loading time, resize the image ` + `or consider using the \"ngSrcset\" and \"sizes\" attributes.`));\n      }\n    }\n  });\n  // We only listen to the `error` event to remove the `load` event listener because it will not be\n  // fired if the image fails to load. This is done to prevent memory leaks in development mode\n  // because image elements aren't garbage-collected properly. It happens because zone.js stores the\n  // event listener directly on the element and closures capture `dir`.\n  const removeErrorListenerFn = renderer.listen(img, 'error', () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n  });\n}\n/**\n * Verifies that a specified input is set.\n */\nfunction assertNonEmptyWidthAndHeight(dir) {\n  let missingAttributes = [];\n  if (dir.width === undefined) missingAttributes.push('width');\n  if (dir.height === undefined) missingAttributes.push('height');\n  if (missingAttributes.length > 0) {\n    throw new ɵRuntimeError(2954 /* RuntimeErrorCode.REQUIRED_INPUT_MISSING */, `${imgDirectiveDetails(dir.ngSrc)} these required attributes ` + `are missing: ${missingAttributes.map(attr => `\"${attr}\"`).join(', ')}. ` + `Including \"width\" and \"height\" attributes will prevent image-related layout shifts. ` + `To fix this, include \"width\" and \"height\" attributes on the image tag or turn on ` + `\"fill\" mode with the \\`fill\\` attribute.`);\n  }\n}\n/**\n * Verifies that width and height are not set. Used in fill mode, where those attributes don't make\n * sense.\n */\nfunction assertEmptyWidthAndHeight(dir) {\n  if (dir.width || dir.height) {\n    throw new ɵRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the attributes \\`height\\` and/or \\`width\\` are present ` + `along with the \\`fill\\` attribute. Because \\`fill\\` mode causes an image to fill its containing ` + `element, the size attributes have no effect and should be removed.`);\n  }\n}\n/**\n * Verifies that the rendered image has a nonzero height. If the image is in fill mode, provides\n * guidance that this can be caused by the containing element's CSS position property.\n */\nfunction assertNonZeroRenderedHeight(dir, img, renderer) {\n  const removeLoadListenerFn = renderer.listen(img, 'load', () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n    const renderedHeight = img.clientHeight;\n    if (dir.fill && renderedHeight === 0) {\n      console.warn(ɵformatRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the height of the fill-mode image is zero. ` + `This is likely because the containing element does not have the CSS 'position' ` + `property set to one of the following: \"relative\", \"fixed\", or \"absolute\". ` + `To fix this problem, make sure the container element has the CSS 'position' ` + `property defined and the height of the element is not zero.`));\n    }\n  });\n  // See comments in the `assertNoImageDistortion`.\n  const removeErrorListenerFn = renderer.listen(img, 'error', () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n  });\n}\n/**\n * Verifies that the `loading` attribute is set to a valid input &\n * is not used on priority images.\n */\nfunction assertValidLoadingInput(dir) {\n  if (dir.loading && dir.priority) {\n    throw new ɵRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the \\`loading\\` attribute ` + `was used on an image that was marked \"priority\". ` + `Setting \\`loading\\` on priority images is not allowed ` + `because these images will always be eagerly loaded. ` + `To fix this, remove the “loading” attribute from the priority image.`);\n  }\n  const validInputs = ['auto', 'eager', 'lazy'];\n  if (typeof dir.loading === 'string' && !validInputs.includes(dir.loading)) {\n    throw new ɵRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the \\`loading\\` attribute ` + `has an invalid value (\\`${dir.loading}\\`). ` + `To fix this, provide a valid value (\"lazy\", \"eager\", or \"auto\").`);\n  }\n}\n/**\n * Warns if NOT using a loader (falling back to the generic loader) and\n * the image appears to be hosted on one of the image CDNs for which\n * we do have a built-in image loader. Suggests switching to the\n * built-in loader.\n *\n * @param ngSrc Value of the ngSrc attribute\n * @param imageLoader ImageLoader provided\n */\nfunction assertNotMissingBuiltInLoader(ngSrc, imageLoader) {\n  if (imageLoader === noopImageLoader) {\n    let builtInLoaderName = '';\n    for (const loader of BUILT_IN_LOADERS) {\n      if (loader.testUrl(ngSrc)) {\n        builtInLoaderName = loader.name;\n        break;\n      }\n    }\n    if (builtInLoaderName) {\n      console.warn(ɵformatRuntimeError(2962 /* RuntimeErrorCode.MISSING_BUILTIN_LOADER */, `NgOptimizedImage: It looks like your images may be hosted on the ` + `${builtInLoaderName} CDN, but your app is not using Angular's ` + `built-in loader for that CDN. We recommend switching to use ` + `the built-in by calling \\`provide${builtInLoaderName}Loader()\\` ` + `in your \\`providers\\` and passing it your instance's base URL. ` + `If you don't want to use the built-in loader, define a custom ` + `loader function using IMAGE_LOADER to silence this warning.`));\n    }\n  }\n}\n/**\n * Warns if ngSrcset is present and no loader is configured (i.e. the default one is being used).\n */\nfunction assertNoNgSrcsetWithoutLoader(dir, imageLoader) {\n  if (dir.ngSrcset && imageLoader === noopImageLoader) {\n    console.warn(ɵformatRuntimeError(2963 /* RuntimeErrorCode.MISSING_NECESSARY_LOADER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`ngSrcset\\` attribute is present but ` + `no image loader is configured (i.e. the default one is being used), ` + `which would result in the same image being used for all configured sizes. ` + `To fix this, provide a loader or remove the \\`ngSrcset\\` attribute from the image.`));\n  }\n}\n/**\n * Warns if loaderParams is present and no loader is configured (i.e. the default one is being\n * used).\n */\nfunction assertNoLoaderParamsWithoutLoader(dir, imageLoader) {\n  if (dir.loaderParams && imageLoader === noopImageLoader) {\n    console.warn(ɵformatRuntimeError(2963 /* RuntimeErrorCode.MISSING_NECESSARY_LOADER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`loaderParams\\` attribute is present but ` + `no image loader is configured (i.e. the default one is being used), ` + `which means that the loaderParams data will not be consumed and will not affect the URL. ` + `To fix this, provide a custom loader or remove the \\`loaderParams\\` attribute from the image.`));\n  }\n}\nfunction round(input) {\n  return Number.isInteger(input) ? input : input.toFixed(2);\n}\n// Transform function to handle SafeValue input for ngSrc. This doesn't do any sanitization,\n// as that is not needed for img.src and img.srcset. This transform is purely for compatibility.\nfunction unwrapSafeUrl(value) {\n  if (typeof value === 'string') {\n    return value;\n  }\n  return ɵunwrapSafeValue(value);\n}\n// Transform function to handle inputs which may be booleans, strings, or string representations\n// of boolean values. Used for the placeholder attribute.\nfunction booleanOrDataUrlAttribute(value) {\n  if (typeof value === 'string' && value.startsWith(`data:`)) {\n    return value;\n  }\n  return booleanAttribute(value);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { APP_BASE_HREF, AsyncPipe, BrowserPlatformLocation, CommonModule, CurrencyPipe, DATE_PIPE_DEFAULT_OPTIONS, DATE_PIPE_DEFAULT_TIMEZONE, DOCUMENT, DatePipe, DecimalPipe, FormStyle, FormatWidth, HashLocationStrategy, I18nPluralPipe, I18nSelectPipe, IMAGE_LOADER, JsonPipe, KeyValuePipe, LOCATION_INITIALIZED, Location, LocationStrategy, LowerCasePipe, NgClass, NgComponentOutlet, NgForOf as NgFor, NgForOf, NgForOfContext, NgIf, NgIfContext, NgLocaleLocalization, NgLocalization, NgOptimizedImage, NgPlural, NgPluralCase, NgStyle, NgSwitch, NgSwitchCase, NgSwitchDefault, NgTemplateOutlet, NumberFormatStyle, NumberSymbol, PRECONNECT_CHECK_BLOCKLIST, PathLocationStrategy, PercentPipe, PlatformLocation, Plural, SlicePipe, TitleCasePipe, TranslationWidth, UpperCasePipe, VERSION, ViewportScroller, WeekDay, XhrFactory, formatCurrency, formatDate, formatNumber, formatPercent, getCurrencySymbol, getLocaleCurrencyCode, getLocaleCurrencyName, getLocaleCurrencySymbol, getLocaleDateFormat, getLocaleDateTimeFormat, getLocaleDayNames, getLocaleDayPeriods, getLocaleDirection, getLocaleEraNames, getLocaleExtraDayPeriodRules, getLocaleExtraDayPeriods, getLocaleFirstDayOfWeek, getLocaleId, getLocaleMonthNames, getLocaleNumberFormat, getLocaleNumberSymbol, getLocalePluralCase, getLocaleTimeFormat, getLocaleWeekEndRange, getNumberOfCurrencyDigits, isPlatformBrowser, isPlatformServer, isPlatformWorkerApp, isPlatformWorkerUi, provideCloudflareLoader, provideCloudinaryLoader, provideImageKitLoader, provideImgixLoader, provideNetlifyLoader, registerLocaleData, DomAdapter as ɵDomAdapter, NullViewportScroller as ɵNullViewportScroller, PLATFORM_BROWSER_ID as ɵPLATFORM_BROWSER_ID, PLATFORM_SERVER_ID as ɵPLATFORM_SERVER_ID, PLATFORM_WORKER_APP_ID as ɵPLATFORM_WORKER_APP_ID, PLATFORM_WORKER_UI_ID as ɵPLATFORM_WORKER_UI_ID, PlatformNavigation as ɵPlatformNavigation, getDOM as ɵgetDOM, normalizeQueryParams as ɵnormalizeQueryParams, parseCookieValue as ɵparseCookieValue, setRootDomAdapter as ɵsetRootDomAdapter };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAI,OAAO;AACX,SAAS,SAAS;AAChB,SAAO;AACT;AACA,SAAS,kBAAkB,SAAS;AAClC,WAAS;AACX;AAQA,IAAM,aAAN,MAAiB;AAAC;AAMlB,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,OAAO,MAAM,OAAO,YAAY;AAAA,MACzC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,YAAY,MAAM,OAAO;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AASH,IAAM,WAAW,IAAI,eAAe,YAAY,kBAAkB,EAAE;AAwBpE,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,UAAU,kBAAkB;AAC1B,UAAM,IAAI,MAAM,YAAY,oBAAoB,EAAE;AAAA,EACpD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,OAAO,MAAM,OAAO,uBAAuB,GAAG;AAAA,MACvD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,YAAY,MAAM,OAAO,uBAAuB;AAAA,IAClD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,uBAAuB,IAAI,eAAe,YAAY,yBAAyB,EAAE;AAQvF,IAAM,0BAAN,MAAM,iCAAgC,iBAAiB;AAAA,EACrD,cAAc;AACZ,UAAM;AACN,SAAK,OAAO,OAAO,QAAQ;AAC3B,SAAK,YAAY,OAAO;AACxB,SAAK,WAAW,OAAO;AAAA,EACzB;AAAA,EACA,qBAAqB;AACnB,WAAO,OAAO,EAAE,YAAY,KAAK,IAAI;AAAA,EACvC;AAAA,EACA,WAAW,IAAI;AACb,UAAMA,UAAS,OAAO,EAAE,qBAAqB,KAAK,MAAM,QAAQ;AAChE,IAAAA,QAAO,iBAAiB,YAAY,IAAI,KAAK;AAC7C,WAAO,MAAMA,QAAO,oBAAoB,YAAY,EAAE;AAAA,EACxD;AAAA,EACA,aAAa,IAAI;AACf,UAAMA,UAAS,OAAO,EAAE,qBAAqB,KAAK,MAAM,QAAQ;AAChE,IAAAA,QAAO,iBAAiB,cAAc,IAAI,KAAK;AAC/C,WAAO,MAAMA,QAAO,oBAAoB,cAAc,EAAE;AAAA,EAC1D;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,SAAS,SAAS;AACpB,SAAK,UAAU,WAAW;AAAA,EAC5B;AAAA,EACA,UAAU,OAAO,OAAO,KAAK;AAC3B,SAAK,SAAS,UAAU,OAAO,OAAO,GAAG;AAAA,EAC3C;AAAA,EACA,aAAa,OAAO,OAAO,KAAK;AAC9B,SAAK,SAAS,aAAa,OAAO,OAAO,GAAG;AAAA,EAC9C;AAAA,EACA,UAAU;AACR,SAAK,SAAS,QAAQ;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,UAAU,mBAAmB,GAAG;AAC9B,SAAK,SAAS,GAAG,gBAAgB;AAAA,EACnC;AAAA,EACA,WAAW;AACT,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAAyB;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,OAAO,MAAM,IAAI,yBAAwB,GAAG;AAAA,MACrD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,YAAY,MAAM,IAAI,wBAAwB;AAAA,IAChD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAWH,SAAS,cAAc,OAAO,KAAK;AACjC,MAAI,MAAM,UAAU,GAAG;AACrB,WAAO;AAAA,EACT;AACA,MAAI,IAAI,UAAU,GAAG;AACnB,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACd,MAAI,MAAM,SAAS,GAAG,GAAG;AACvB;AAAA,EACF;AACA,MAAI,IAAI,WAAW,GAAG,GAAG;AACvB;AAAA,EACF;AACA,MAAI,WAAW,GAAG;AAChB,WAAO,QAAQ,IAAI,UAAU,CAAC;AAAA,EAChC;AACA,MAAI,WAAW,GAAG;AAChB,WAAO,QAAQ;AAAA,EACjB;AACA,SAAO,QAAQ,MAAM;AACvB;AAUA,SAAS,mBAAmB,KAAK;AAC/B,QAAM,QAAQ,IAAI,MAAM,QAAQ;AAChC,QAAM,aAAa,SAAS,MAAM,SAAS,IAAI;AAC/C,QAAM,kBAAkB,cAAc,IAAI,aAAa,CAAC,MAAM,MAAM,IAAI;AACxE,SAAO,IAAI,MAAM,GAAG,eAAe,IAAI,IAAI,MAAM,UAAU;AAC7D;AAQA,SAAS,qBAAqB,QAAQ;AACpC,SAAO,UAAU,OAAO,CAAC,MAAM,MAAM,MAAM,SAAS;AACtD;AAmBA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,UAAU,kBAAkB;AAC1B,UAAM,IAAI,MAAM,YAAY,oBAAoB,EAAE;AAAA,EACpD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,OAAO,MAAM,OAAO,oBAAoB,GAAG;AAAA,MACpD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,YAAY,MAAM,OAAO,oBAAoB;AAAA,IAC/C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAwBH,IAAM,gBAAgB,IAAI,eAAe,YAAY,gBAAgB,EAAE;AAgCvE,IAAM,uBAAN,MAAM,8BAA6B,iBAAiB;AAAA,EAClD,YAAY,mBAAmB,MAAM;AACnC,UAAM;AACN,SAAK,oBAAoB;AACzB,SAAK,qBAAqB,CAAC;AAC3B,SAAK,YAAY,QAAQ,KAAK,kBAAkB,mBAAmB,KAAK,OAAO,QAAQ,EAAE,UAAU,UAAU;AAAA,EAC/G;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,mBAAmB,QAAQ;AACrC,WAAK,mBAAmB,IAAI,EAAE;AAAA,IAChC;AAAA,EACF;AAAA,EACA,WAAW,IAAI;AACb,SAAK,mBAAmB,KAAK,KAAK,kBAAkB,WAAW,EAAE,GAAG,KAAK,kBAAkB,aAAa,EAAE,CAAC;AAAA,EAC7G;AAAA,EACA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,mBAAmB,UAAU;AAC3B,WAAO,cAAc,KAAK,WAAW,QAAQ;AAAA,EAC/C;AAAA,EACA,KAAK,cAAc,OAAO;AACxB,UAAM,WAAW,KAAK,kBAAkB,WAAW,qBAAqB,KAAK,kBAAkB,MAAM;AACrG,UAAM,OAAO,KAAK,kBAAkB;AACpC,WAAO,QAAQ,cAAc,GAAG,QAAQ,GAAG,IAAI,KAAK;AAAA,EACtD;AAAA,EACA,UAAU,OAAO,OAAO,KAAK,aAAa;AACxC,UAAM,cAAc,KAAK,mBAAmB,MAAM,qBAAqB,WAAW,CAAC;AACnF,SAAK,kBAAkB,UAAU,OAAO,OAAO,WAAW;AAAA,EAC5D;AAAA,EACA,aAAa,OAAO,OAAO,KAAK,aAAa;AAC3C,UAAM,cAAc,KAAK,mBAAmB,MAAM,qBAAqB,WAAW,CAAC;AACnF,SAAK,kBAAkB,aAAa,OAAO,OAAO,WAAW;AAAA,EAC/D;AAAA,EACA,UAAU;AACR,SAAK,kBAAkB,QAAQ;AAAA,EACjC;AAAA,EACA,OAAO;AACL,SAAK,kBAAkB,KAAK;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,WAAO,KAAK,kBAAkB,SAAS;AAAA,EACzC;AAAA,EACA,UAAU,mBAAmB,GAAG;AAC9B,SAAK,kBAAkB,YAAY,gBAAgB;AAAA,EACrD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,SAAS,gBAAgB,GAAM,SAAS,eAAe,CAAC,CAAC;AAAA,IACrG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAoBH,IAAM,uBAAN,MAAM,8BAA6B,iBAAiB;AAAA,EAClD,YAAY,mBAAmB,WAAW;AACxC,UAAM;AACN,SAAK,oBAAoB;AACzB,SAAK,YAAY;AACjB,SAAK,qBAAqB,CAAC;AAC3B,QAAI,aAAa,MAAM;AACrB,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,mBAAmB,QAAQ;AACrC,WAAK,mBAAmB,IAAI,EAAE;AAAA,IAChC;AAAA,EACF;AAAA,EACA,WAAW,IAAI;AACb,SAAK,mBAAmB,KAAK,KAAK,kBAAkB,WAAW,EAAE,GAAG,KAAK,kBAAkB,aAAa,EAAE,CAAC;AAAA,EAC7G;AAAA,EACA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,KAAK,cAAc,OAAO;AAGxB,UAAM,OAAO,KAAK,kBAAkB,QAAQ;AAC5C,WAAO,KAAK,SAAS,IAAI,KAAK,UAAU,CAAC,IAAI;AAAA,EAC/C;AAAA,EACA,mBAAmB,UAAU;AAC3B,UAAM,MAAM,cAAc,KAAK,WAAW,QAAQ;AAClD,WAAO,IAAI,SAAS,IAAI,MAAM,MAAM;AAAA,EACtC;AAAA,EACA,UAAU,OAAO,OAAO,MAAM,aAAa;AACzC,QAAI,MAAM,KAAK,mBAAmB,OAAO,qBAAqB,WAAW,CAAC;AAC1E,QAAI,IAAI,UAAU,GAAG;AACnB,YAAM,KAAK,kBAAkB;AAAA,IAC/B;AACA,SAAK,kBAAkB,UAAU,OAAO,OAAO,GAAG;AAAA,EACpD;AAAA,EACA,aAAa,OAAO,OAAO,MAAM,aAAa;AAC5C,QAAI,MAAM,KAAK,mBAAmB,OAAO,qBAAqB,WAAW,CAAC;AAC1E,QAAI,IAAI,UAAU,GAAG;AACnB,YAAM,KAAK,kBAAkB;AAAA,IAC/B;AACA,SAAK,kBAAkB,aAAa,OAAO,OAAO,GAAG;AAAA,EACvD;AAAA,EACA,UAAU;AACR,SAAK,kBAAkB,QAAQ;AAAA,EACjC;AAAA,EACA,OAAO;AACL,SAAK,kBAAkB,KAAK;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,WAAO,KAAK,kBAAkB,SAAS;AAAA,EACzC;AAAA,EACA,UAAU,mBAAmB,GAAG;AAC9B,SAAK,kBAAkB,YAAY,gBAAgB;AAAA,EACrD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,SAAS,gBAAgB,GAAM,SAAS,eAAe,CAAC,CAAC;AAAA,IACrG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,IAChC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AA8BH,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,YAAY,kBAAkB;AAE5B,SAAK,WAAW,IAAI,aAAa;AAEjC,SAAK,sBAAsB,CAAC;AAE5B,SAAK,yBAAyB;AAC9B,SAAK,oBAAoB;AACzB,UAAM,WAAW,KAAK,kBAAkB,YAAY;AAOpD,SAAK,YAAY,aAAa,mBAAmB,gBAAgB,QAAQ,CAAC,CAAC;AAC3E,SAAK,kBAAkB,WAAW,QAAM;AACtC,WAAK,SAAS,KAAK;AAAA,QACjB,OAAO,KAAK,KAAK,IAAI;AAAA,QACrB,OAAO;AAAA,QACP,SAAS,GAAG;AAAA,QACZ,QAAQ,GAAG;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,wBAAwB,YAAY;AACzC,SAAK,sBAAsB,CAAC;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,KAAK,cAAc,OAAO;AACxB,WAAO,KAAK,UAAU,KAAK,kBAAkB,KAAK,WAAW,CAAC;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK,kBAAkB,SAAS;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,qBAAqB,MAAM,QAAQ,IAAI;AACrC,WAAO,KAAK,KAAK,KAAK,KAAK,UAAU,OAAO,qBAAqB,KAAK,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,KAAK;AACb,WAAO,UAAS,mBAAmB,eAAe,KAAK,WAAW,gBAAgB,GAAG,CAAC,CAAC;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,mBAAmB,KAAK;AACtB,QAAI,OAAO,IAAI,CAAC,MAAM,KAAK;AACzB,YAAM,MAAM;AAAA,IACd;AACA,WAAO,KAAK,kBAAkB,mBAAmB,GAAG;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,GAAG,MAAM,QAAQ,IAAI,QAAQ,MAAM;AACjC,SAAK,kBAAkB,UAAU,OAAO,IAAI,MAAM,KAAK;AACvD,SAAK,0BAA0B,KAAK,mBAAmB,OAAO,qBAAqB,KAAK,CAAC,GAAG,KAAK;AAAA,EACnG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,MAAM,QAAQ,IAAI,QAAQ,MAAM;AAC3C,SAAK,kBAAkB,aAAa,OAAO,IAAI,MAAM,KAAK;AAC1D,SAAK,0BAA0B,KAAK,mBAAmB,OAAO,qBAAqB,KAAK,CAAC,GAAG,KAAK;AAAA,EACnG;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,SAAK,kBAAkB,QAAQ;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACL,SAAK,kBAAkB,KAAK;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,UAAU,mBAAmB,GAAG;AAC9B,SAAK,kBAAkB,YAAY,gBAAgB;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,IAAI;AACd,SAAK,oBAAoB,KAAK,EAAE;AAChC,SAAK,2BAA2B,KAAK,UAAU,OAAK;AAClD,WAAK,0BAA0B,EAAE,KAAK,EAAE,KAAK;AAAA,IAC/C,CAAC;AACD,WAAO,MAAM;AACX,YAAM,UAAU,KAAK,oBAAoB,QAAQ,EAAE;AACnD,WAAK,oBAAoB,OAAO,SAAS,CAAC;AAC1C,UAAI,KAAK,oBAAoB,WAAW,GAAG;AACzC,aAAK,wBAAwB,YAAY;AACzC,aAAK,yBAAyB;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,0BAA0B,MAAM,IAAI,OAAO;AACzC,SAAK,oBAAoB,QAAQ,QAAM,GAAG,KAAK,KAAK,CAAC;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,UAAU,QAAQ,SAAS,UAAU;AACnC,WAAO,KAAK,SAAS,UAAU;AAAA,MAC7B,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EAQA,OAAO;AACL,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EAUA,OAAO;AACL,SAAK,gBAAgB;AAAA,EACvB;AAAA,EAUA,OAAO;AACL,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iBAAiB,GAAG;AACvC,aAAO,KAAK,KAAK,WAAa,SAAS,gBAAgB,CAAC;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,MAAM,eAAe;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA;AAAA,MAEZ,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,iBAAiB;AACxB,SAAO,IAAI,SAAS,SAAS,gBAAgB,CAAC;AAChD;AACA,SAAS,eAAe,UAAU,KAAK;AACrC,MAAI,CAAC,YAAY,CAAC,IAAI,WAAW,QAAQ,GAAG;AAC1C,WAAO;AAAA,EACT;AACA,QAAM,cAAc,IAAI,UAAU,SAAS,MAAM;AACjD,MAAI,gBAAgB,MAAM,CAAC,KAAK,KAAK,KAAK,GAAG,EAAE,SAAS,YAAY,CAAC,CAAC,GAAG;AACvE,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,QAAQ,iBAAiB,EAAE;AACxC;AACA,SAAS,aAAa,UAAU;AAM9B,QAAMC,iBAAgB,IAAI,OAAO,eAAe,EAAE,KAAK,QAAQ;AAC/D,MAAIA,gBAAe;AACjB,UAAM,CAAC,EAAE,QAAQ,IAAI,SAAS,MAAM,YAAY;AAChD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGA,IAAM,gBAAgB;AAAA,EACpB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,MAAM,GAAG;AAAA,EACjB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,IAAI;AAAA,EACZ,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,OAAO,KAAK,CAAC;AAAA,EACrB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,QAAW,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,QAAW,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,QAAW,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,KAAK;AAAA,EACxB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,KAAK,QAAW,CAAC;AAAA,EACzB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,QAAW,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,KAAK,QAAW,CAAC;AAAA,EACzB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,QAAW,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,QAAW,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,QAAW,KAAK;AAAA,EACxB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,QAAW,KAAK,CAAC;AAAA,EACzB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,OAAO,KAAK,CAAC;AAAA,EACrB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,MAAM,CAAC;AAAA,EAC1B,OAAO,CAAC,KAAK,QAAW,CAAC;AAAA,EACzB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAQ,QAAW,CAAC;AAAA,EAC5B,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,SAAS,QAAW,CAAC;AAAA,EAC7B,OAAO,CAAC,QAAQ,QAAW,CAAC;AAAA,EAC5B,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,GAAG;AAAA,EACtB,OAAO,CAAC,QAAW,QAAW,CAAC;AAAA,EAC/B,OAAO,CAAC,QAAW,IAAI;AAAA,EACvB,OAAO,CAAC,QAAW,QAAW,CAAC;AACjC;AASA,IAAI;AAAA,CACH,SAAUC,oBAAmB;AAC5B,EAAAA,mBAAkBA,mBAAkB,SAAS,IAAI,CAAC,IAAI;AACtD,EAAAA,mBAAkBA,mBAAkB,SAAS,IAAI,CAAC,IAAI;AACtD,EAAAA,mBAAkBA,mBAAkB,UAAU,IAAI,CAAC,IAAI;AACvD,EAAAA,mBAAkBA,mBAAkB,YAAY,IAAI,CAAC,IAAI;AAC3D,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAUhD,IAAI;AAAA,CACH,SAAUC,SAAQ;AACjB,EAAAA,QAAOA,QAAO,MAAM,IAAI,CAAC,IAAI;AAC7B,EAAAA,QAAOA,QAAO,KAAK,IAAI,CAAC,IAAI;AAC5B,EAAAA,QAAOA,QAAO,KAAK,IAAI,CAAC,IAAI;AAC5B,EAAAA,QAAOA,QAAO,KAAK,IAAI,CAAC,IAAI;AAC5B,EAAAA,QAAOA,QAAO,MAAM,IAAI,CAAC,IAAI;AAC7B,EAAAA,QAAOA,QAAO,OAAO,IAAI,CAAC,IAAI;AAChC,GAAG,WAAW,SAAS,CAAC,EAAE;AAU1B,IAAI;AAAA,CACH,SAAUC,YAAW;AACpB,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACrC,EAAAA,WAAUA,WAAU,YAAY,IAAI,CAAC,IAAI;AAC3C,GAAG,cAAc,YAAY,CAAC,EAAE;AAQhC,IAAI;AAAA,CACH,SAAUC,mBAAkB;AAE3B,EAAAA,kBAAiBA,kBAAiB,QAAQ,IAAI,CAAC,IAAI;AAEnD,EAAAA,kBAAiBA,kBAAiB,aAAa,IAAI,CAAC,IAAI;AAExD,EAAAA,kBAAiBA,kBAAiB,MAAM,IAAI,CAAC,IAAI;AAEjD,EAAAA,kBAAiBA,kBAAiB,OAAO,IAAI,CAAC,IAAI;AACpD,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAY9C,IAAI;AAAA,CACH,SAAUC,cAAa;AAKtB,EAAAA,aAAYA,aAAY,OAAO,IAAI,CAAC,IAAI;AAKxC,EAAAA,aAAYA,aAAY,QAAQ,IAAI,CAAC,IAAI;AAKzC,EAAAA,aAAYA,aAAY,MAAM,IAAI,CAAC,IAAI;AAKvC,EAAAA,aAAYA,aAAY,MAAM,IAAI,CAAC,IAAI;AACzC,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAapC,IAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMT,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKN,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAKL,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,eAAe;AACjB;AAMA,IAAI;AAAA,CACH,SAAUC,UAAS;AAClB,EAAAA,SAAQA,SAAQ,QAAQ,IAAI,CAAC,IAAI;AACjC,EAAAA,SAAQA,SAAQ,QAAQ,IAAI,CAAC,IAAI;AACjC,EAAAA,SAAQA,SAAQ,SAAS,IAAI,CAAC,IAAI;AAClC,EAAAA,SAAQA,SAAQ,WAAW,IAAI,CAAC,IAAI;AACpC,EAAAA,SAAQA,SAAQ,UAAU,IAAI,CAAC,IAAI;AACnC,EAAAA,SAAQA,SAAQ,QAAQ,IAAI,CAAC,IAAI;AACjC,EAAAA,SAAQA,SAAQ,UAAU,IAAI,CAAC,IAAI;AACrC,GAAG,YAAY,UAAU,CAAC,EAAE;AAU5B,SAAS,YAAY,QAAQ;AAC3B,SAAO,eAAgB,MAAM,EAAE,gBAAiB,QAAQ;AAC1D;AAYA,SAAS,oBAAoB,QAAQ,WAAW,OAAO;AACrD,QAAM,OAAO,eAAgB,MAAM;AACnC,QAAM,WAAW,CAAC,KAAK,gBAAiB,gBAAgB,GAAG,KAAK,gBAAiB,oBAAoB,CAAC;AACtG,QAAM,OAAO,oBAAoB,UAAU,SAAS;AACpD,SAAO,oBAAoB,MAAM,KAAK;AACxC;AAaA,SAAS,kBAAkB,QAAQ,WAAW,OAAO;AACnD,QAAM,OAAO,eAAgB,MAAM;AACnC,QAAM,WAAW,CAAC,KAAK,gBAAiB,UAAU,GAAG,KAAK,gBAAiB,cAAc,CAAC;AAC1F,QAAM,OAAO,oBAAoB,UAAU,SAAS;AACpD,SAAO,oBAAoB,MAAM,KAAK;AACxC;AAaA,SAAS,oBAAoB,QAAQ,WAAW,OAAO;AACrD,QAAM,OAAO,eAAgB,MAAM;AACnC,QAAM,aAAa,CAAC,KAAK,gBAAiB,YAAY,GAAG,KAAK,gBAAiB,gBAAgB,CAAC;AAChG,QAAM,SAAS,oBAAoB,YAAY,SAAS;AACxD,SAAO,oBAAoB,QAAQ,KAAK;AAC1C;AAYA,SAAS,kBAAkB,QAAQ,OAAO;AACxC,QAAM,OAAO,eAAgB,MAAM;AACnC,QAAM,WAAW,KAAK,gBAAiB,IAAI;AAC3C,SAAO,oBAAoB,UAAU,KAAK;AAC5C;AAYA,SAAS,wBAAwB,QAAQ;AACvC,QAAM,OAAO,eAAgB,MAAM;AACnC,SAAO,KAAK,gBAAiB,cAAc;AAC7C;AAUA,SAAS,sBAAsB,QAAQ;AACrC,QAAM,OAAO,eAAgB,MAAM;AACnC,SAAO,KAAK,gBAAiB,YAAY;AAC3C;AAYA,SAAS,oBAAoB,QAAQ,OAAO;AAC1C,QAAM,OAAO,eAAgB,MAAM;AACnC,SAAO,oBAAoB,KAAK,gBAAiB,UAAU,GAAG,KAAK;AACrE;AAYA,SAAS,oBAAoB,QAAQ,OAAO;AAC1C,QAAM,OAAO,eAAgB,MAAM;AACnC,SAAO,oBAAoB,KAAK,gBAAiB,UAAU,GAAG,KAAK;AACrE;AAYA,SAAS,wBAAwB,QAAQ,OAAO;AAC9C,QAAM,OAAO,eAAgB,MAAM;AACnC,QAAM,qBAAqB,KAAK,gBAAiB,cAAc;AAC/D,SAAO,oBAAoB,oBAAoB,KAAK;AACtD;AAWA,SAAS,sBAAsB,QAAQ,QAAQ;AAC7C,QAAM,OAAO,eAAgB,MAAM;AACnC,QAAM,MAAM,KAAK,gBAAiB,aAAa,EAAE,MAAM;AACvD,MAAI,OAAO,QAAQ,aAAa;AAC9B,QAAI,WAAW,aAAa,iBAAiB;AAC3C,aAAO,KAAK,gBAAiB,aAAa,EAAE,aAAa,OAAO;AAAA,IAClE,WAAW,WAAW,aAAa,eAAe;AAChD,aAAO,KAAK,gBAAiB,aAAa,EAAE,aAAa,KAAK;AAAA,IAChE;AAAA,EACF;AACA,SAAO;AACT;AAoCA,SAAS,sBAAsB,QAAQ,MAAM;AAC3C,QAAM,OAAO,eAAgB,MAAM;AACnC,SAAO,KAAK,gBAAiB,aAAa,EAAE,IAAI;AAClD;AAYA,SAAS,wBAAwB,QAAQ;AACvC,QAAM,OAAO,eAAgB,MAAM;AACnC,SAAO,KAAK,gBAAiB,cAAc,KAAK;AAClD;AAWA,SAAS,sBAAsB,QAAQ;AACrC,QAAM,OAAO,eAAgB,MAAM;AACnC,SAAO,KAAK,gBAAiB,YAAY,KAAK;AAChD;AAWA,SAASC,uBAAsB,QAAQ;AACrC,SAAO,sBAAuB,MAAM;AACtC;AAOA,SAAS,oBAAoB,QAAQ;AACnC,QAAM,OAAO,eAAgB,MAAM;AACnC,SAAO,KAAK,gBAAiB,UAAU;AACzC;AAKA,IAAMC,uBAAsB;AAC5B,SAAS,cAAc,MAAM;AAC3B,MAAI,CAAC,KAAK,gBAAiB,SAAS,GAAG;AACrC,UAAM,IAAI,MAAM,6CAA6C,KAAK,gBAAiB,QAAQ,CAAC,gGAAgG;AAAA,EAC9L;AACF;AAuBA,SAAS,6BAA6B,QAAQ;AAC5C,QAAM,OAAO,eAAgB,MAAM;AACnC,gBAAc,IAAI;AAClB,QAAM,QAAQ,KAAK,gBAAiB,SAAS;AAAA,IAAE;AAAA;AAAA,EAAkD,KAAK,CAAC;AACvG,SAAO,MAAM,IAAI,UAAQ;AACvB,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,YAAY,IAAI;AAAA,IACzB;AACA,WAAO,CAAC,YAAY,KAAK,CAAC,CAAC,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC;AAAA,EACpD,CAAC;AACH;AAkBA,SAAS,yBAAyB,QAAQ,WAAW,OAAO;AAC1D,QAAM,OAAO,eAAgB,MAAM;AACnC,gBAAc,IAAI;AAClB,QAAM,iBAAiB,CAAC,KAAK,gBAAiB,SAAS;AAAA,IAAE;AAAA;AAAA,EAAmD,GAAG,KAAK,gBAAiB,SAAS;AAAA,IAAE;AAAA;AAAA,EAAsD,CAAC;AACvM,QAAM,aAAa,oBAAoB,gBAAgB,SAAS,KAAK,CAAC;AACtE,SAAO,oBAAoB,YAAY,KAAK,KAAK,CAAC;AACpD;AAQA,SAAS,mBAAmB,QAAQ;AAClC,QAAM,OAAO,eAAgB,MAAM;AACnC,SAAO,KAAK,gBAAiB,cAAc;AAC7C;AAcA,SAAS,oBAAoB,MAAM,OAAO;AACxC,WAAS,IAAI,OAAO,IAAI,IAAI,KAAK;AAC/B,QAAI,OAAO,KAAK,CAAC,MAAM,aAAa;AAClC,aAAO,KAAK,CAAC;AAAA,IACf;AAAA,EACF;AACA,QAAM,IAAI,MAAM,wCAAwC;AAC1D;AAIA,SAAS,YAAY,MAAM;AACzB,QAAM,CAAC,GAAG,CAAC,IAAI,KAAK,MAAM,GAAG;AAC7B,SAAO;AAAA,IACL,OAAO,CAAC;AAAA,IACR,SAAS,CAAC;AAAA,EACZ;AACF;AAgBA,SAAS,kBAAkB,MAAM,QAAQ,SAAS,MAAM;AACtD,QAAM,WAAW,oBAAoB,MAAM,EAAE,IAAI,KAAK,cAAc,IAAI,KAAK,CAAC;AAC9E,QAAM,eAAe;AAAA,IAAS;AAAA;AAAA,EAAmC;AACjE,MAAI,WAAW,YAAY,OAAO,iBAAiB,UAAU;AAC3D,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IAAS;AAAA;AAAA,EAA6B,KAAK;AACpD;AAEA,IAAM,gCAAgC;AAWtC,SAAS,0BAA0B,MAAM;AACvC,MAAI;AACJ,QAAM,WAAW,cAAc,IAAI;AACnC,MAAI,UAAU;AACZ,aAAS;AAAA,MAAS;AAAA;AAAA,IAAiC;AAAA,EACrD;AACA,SAAO,OAAO,WAAW,WAAW,SAAS;AAC/C;AACA,IAAM,qBAAqB;AAE3B,IAAM,gBAAgB,CAAC;AACvB,IAAM,qBAAqB;AAC3B,IAAI;AAAA,CACH,SAAUC,YAAW;AACpB,EAAAA,WAAUA,WAAU,OAAO,IAAI,CAAC,IAAI;AACpC,EAAAA,WAAUA,WAAU,UAAU,IAAI,CAAC,IAAI;AACvC,EAAAA,WAAUA,WAAU,MAAM,IAAI,CAAC,IAAI;AACnC,EAAAA,WAAUA,WAAU,UAAU,IAAI,CAAC,IAAI;AACzC,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI;AAAA,CACH,SAAUC,WAAU;AACnB,EAAAA,UAASA,UAAS,UAAU,IAAI,CAAC,IAAI;AACrC,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAClC,EAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACjC,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAClC,EAAAA,UAASA,UAAS,SAAS,IAAI,CAAC,IAAI;AACpC,EAAAA,UAASA,UAAS,SAAS,IAAI,CAAC,IAAI;AACpC,EAAAA,UAASA,UAAS,mBAAmB,IAAI,CAAC,IAAI;AAC9C,EAAAA,UAASA,UAAS,KAAK,IAAI,CAAC,IAAI;AAClC,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAI;AAAA,CACH,SAAUC,kBAAiB;AAC1B,EAAAA,iBAAgBA,iBAAgB,YAAY,IAAI,CAAC,IAAI;AACrD,EAAAA,iBAAgBA,iBAAgB,MAAM,IAAI,CAAC,IAAI;AAC/C,EAAAA,iBAAgBA,iBAAgB,QAAQ,IAAI,CAAC,IAAI;AACjD,EAAAA,iBAAgBA,iBAAgB,MAAM,IAAI,CAAC,IAAI;AACjD,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAsB5C,SAAS,WAAW,OAAO,QAAQ,QAAQ,UAAU;AACnD,MAAI,OAAO,OAAO,KAAK;AACvB,QAAM,cAAc,eAAe,QAAQ,MAAM;AACjD,WAAS,eAAe;AACxB,MAAI,QAAQ,CAAC;AACb,MAAI;AACJ,SAAO,QAAQ;AACb,YAAQ,mBAAmB,KAAK,MAAM;AACtC,QAAI,OAAO;AACT,cAAQ,MAAM,OAAO,MAAM,MAAM,CAAC,CAAC;AACnC,YAAM,OAAO,MAAM,IAAI;AACvB,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AACA,eAAS;AAAA,IACX,OAAO;AACL,YAAM,KAAK,MAAM;AACjB;AAAA,IACF;AAAA,EACF;AACA,MAAI,qBAAqB,KAAK,kBAAkB;AAChD,MAAI,UAAU;AACZ,yBAAqB,iBAAiB,UAAU,kBAAkB;AAClE,WAAO,uBAAuB,MAAM,UAAU,IAAI;AAAA,EACpD;AACA,MAAI,OAAO;AACX,QAAM,QAAQ,CAAAC,WAAS;AACrB,UAAM,gBAAgB,iBAAiBA,MAAK;AAC5C,YAAQ,gBAAgB,cAAc,MAAM,QAAQ,kBAAkB,IAAIA,WAAU,OAAO,MAAMA,OAAM,QAAQ,YAAY,EAAE,EAAE,QAAQ,OAAO,GAAG;AAAA,EACnJ,CAAC;AACD,SAAO;AACT;AAWA,SAAS,WAAW,MAAM,OAAO,MAAM;AAKrC,QAAM,UAAU,oBAAI,KAAK,CAAC;AAM1B,UAAQ,YAAY,MAAM,OAAO,IAAI;AAIrC,UAAQ,SAAS,GAAG,GAAG,CAAC;AACxB,SAAO;AACT;AACA,SAAS,eAAe,QAAQ,QAAQ;AACtC,QAAM,WAAW,YAAY,MAAM;AACnC,gBAAc,QAAQ,MAAM,CAAC;AAC7B,MAAI,cAAc,QAAQ,EAAE,MAAM,GAAG;AACnC,WAAO,cAAc,QAAQ,EAAE,MAAM;AAAA,EACvC;AACA,MAAI,cAAc;AAClB,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,oBAAc,oBAAoB,QAAQ,YAAY,KAAK;AAC3D;AAAA,IACF,KAAK;AACH,oBAAc,oBAAoB,QAAQ,YAAY,MAAM;AAC5D;AAAA,IACF,KAAK;AACH,oBAAc,oBAAoB,QAAQ,YAAY,IAAI;AAC1D;AAAA,IACF,KAAK;AACH,oBAAc,oBAAoB,QAAQ,YAAY,IAAI;AAC1D;AAAA,IACF,KAAK;AACH,oBAAc,oBAAoB,QAAQ,YAAY,KAAK;AAC3D;AAAA,IACF,KAAK;AACH,oBAAc,oBAAoB,QAAQ,YAAY,MAAM;AAC5D;AAAA,IACF,KAAK;AACH,oBAAc,oBAAoB,QAAQ,YAAY,IAAI;AAC1D;AAAA,IACF,KAAK;AACH,oBAAc,oBAAoB,QAAQ,YAAY,IAAI;AAC1D;AAAA,IACF,KAAK;AACH,YAAM,YAAY,eAAe,QAAQ,WAAW;AACpD,YAAM,YAAY,eAAe,QAAQ,WAAW;AACpD,oBAAc,eAAe,wBAAwB,QAAQ,YAAY,KAAK,GAAG,CAAC,WAAW,SAAS,CAAC;AACvG;AAAA,IACF,KAAK;AACH,YAAM,aAAa,eAAe,QAAQ,YAAY;AACtD,YAAM,aAAa,eAAe,QAAQ,YAAY;AACtD,oBAAc,eAAe,wBAAwB,QAAQ,YAAY,MAAM,GAAG,CAAC,YAAY,UAAU,CAAC;AAC1G;AAAA,IACF,KAAK;AACH,YAAM,WAAW,eAAe,QAAQ,UAAU;AAClD,YAAM,WAAW,eAAe,QAAQ,UAAU;AAClD,oBAAc,eAAe,wBAAwB,QAAQ,YAAY,IAAI,GAAG,CAAC,UAAU,QAAQ,CAAC;AACpG;AAAA,IACF,KAAK;AACH,YAAM,WAAW,eAAe,QAAQ,UAAU;AAClD,YAAM,WAAW,eAAe,QAAQ,UAAU;AAClD,oBAAc,eAAe,wBAAwB,QAAQ,YAAY,IAAI,GAAG,CAAC,UAAU,QAAQ,CAAC;AACpG;AAAA,EACJ;AACA,MAAI,aAAa;AACf,kBAAc,QAAQ,EAAE,MAAM,IAAI;AAAA,EACpC;AACA,SAAO;AACT;AACA,SAAS,eAAe,KAAK,YAAY;AACvC,MAAI,YAAY;AACd,UAAM,IAAI,QAAQ,eAAe,SAAU,OAAO,KAAK;AACrD,aAAO,cAAc,QAAQ,OAAO,aAAa,WAAW,GAAG,IAAI;AAAA,IACrE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,UAAU,KAAK,QAAQ,YAAY,KAAK,MAAM,SAAS;AAC9D,MAAI,MAAM;AACV,MAAI,MAAM,KAAK,WAAW,OAAO,GAAG;AAClC,QAAI,SAAS;AACX,YAAM,CAAC,MAAM;AAAA,IACf,OAAO;AACL,YAAM,CAAC;AACP,YAAM;AAAA,IACR;AAAA,EACF;AACA,MAAI,SAAS,OAAO,GAAG;AACvB,SAAO,OAAO,SAAS,QAAQ;AAC7B,aAAS,MAAM;AAAA,EACjB;AACA,MAAI,MAAM;AACR,aAAS,OAAO,MAAM,OAAO,SAAS,MAAM;AAAA,EAC9C;AACA,SAAO,MAAM;AACf;AACA,SAAS,wBAAwB,cAAc,QAAQ;AACrD,QAAM,QAAQ,UAAU,cAAc,CAAC;AACvC,SAAO,MAAM,UAAU,GAAG,MAAM;AAClC;AAIA,SAAS,WAAW,MAAM,MAAM,SAAS,GAAG,OAAO,OAAO,UAAU,OAAO;AACzE,SAAO,SAAU,MAAM,QAAQ;AAC7B,QAAI,OAAO,YAAY,MAAM,IAAI;AACjC,QAAI,SAAS,KAAK,OAAO,CAAC,QAAQ;AAChC,cAAQ;AAAA,IACV;AACA,QAAI,SAAS,SAAS,OAAO;AAC3B,UAAI,SAAS,KAAK,WAAW,KAAK;AAChC,eAAO;AAAA,MACT;AAAA,IACF,WAAW,SAAS,SAAS,mBAAmB;AAC9C,aAAO,wBAAwB,MAAM,IAAI;AAAA,IAC3C;AACA,UAAM,cAAc,sBAAsB,QAAQ,aAAa,SAAS;AACxE,WAAO,UAAU,MAAM,MAAM,aAAa,MAAM,OAAO;AAAA,EACzD;AACF;AACA,SAAS,YAAY,MAAM,MAAM;AAC/B,UAAQ,MAAM;AAAA,IACZ,KAAK,SAAS;AACZ,aAAO,KAAK,YAAY;AAAA,IAC1B,KAAK,SAAS;AACZ,aAAO,KAAK,SAAS;AAAA,IACvB,KAAK,SAAS;AACZ,aAAO,KAAK,QAAQ;AAAA,IACtB,KAAK,SAAS;AACZ,aAAO,KAAK,SAAS;AAAA,IACvB,KAAK,SAAS;AACZ,aAAO,KAAK,WAAW;AAAA,IACzB,KAAK,SAAS;AACZ,aAAO,KAAK,WAAW;AAAA,IACzB,KAAK,SAAS;AACZ,aAAO,KAAK,gBAAgB;AAAA,IAC9B,KAAK,SAAS;AACZ,aAAO,KAAK,OAAO;AAAA,IACrB;AACE,YAAM,IAAI,MAAM,2BAA2B,IAAI,IAAI;AAAA,EACvD;AACF;AAIA,SAAS,cAAc,MAAM,OAAO,OAAO,UAAU,QAAQ,WAAW,OAAO;AAC7E,SAAO,SAAU,MAAM,QAAQ;AAC7B,WAAO,mBAAmB,MAAM,QAAQ,MAAM,OAAO,MAAM,QAAQ;AAAA,EACrE;AACF;AAIA,SAAS,mBAAmB,MAAM,QAAQ,MAAM,OAAO,MAAM,UAAU;AACrE,UAAQ,MAAM;AAAA,IACZ,KAAK,gBAAgB;AACnB,aAAO,oBAAoB,QAAQ,MAAM,KAAK,EAAE,KAAK,SAAS,CAAC;AAAA,IACjE,KAAK,gBAAgB;AACnB,aAAO,kBAAkB,QAAQ,MAAM,KAAK,EAAE,KAAK,OAAO,CAAC;AAAA,IAC7D,KAAK,gBAAgB;AACnB,YAAM,eAAe,KAAK,SAAS;AACnC,YAAM,iBAAiB,KAAK,WAAW;AACvC,UAAI,UAAU;AACZ,cAAM,QAAQ,6BAA6B,MAAM;AACjD,cAAM,aAAa,yBAAyB,QAAQ,MAAM,KAAK;AAC/D,cAAM,QAAQ,MAAM,UAAU,UAAQ;AACpC,cAAI,MAAM,QAAQ,IAAI,GAAG;AAEvB,kBAAM,CAAC,MAAM,EAAE,IAAI;AACnB,kBAAM,YAAY,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AACvE,kBAAM,WAAW,eAAe,GAAG,SAAS,iBAAiB,GAAG,SAAS,iBAAiB,GAAG;AAW7F,gBAAI,KAAK,QAAQ,GAAG,OAAO;AACzB,kBAAI,aAAa,UAAU;AACzB,uBAAO;AAAA,cACT;AAAA,YACF,WAAW,aAAa,UAAU;AAChC,qBAAO;AAAA,YACT;AAAA,UACF,OAAO;AAEL,gBAAI,KAAK,UAAU,gBAAgB,KAAK,YAAY,gBAAgB;AAClE,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT,CAAC;AACD,YAAI,UAAU,IAAI;AAChB,iBAAO,WAAW,KAAK;AAAA,QACzB;AAAA,MACF;AAEA,aAAO,oBAAoB,QAAQ,MAAM,KAAK,EAAE,eAAe,KAAK,IAAI,CAAC;AAAA,IAC3E,KAAK,gBAAgB;AACnB,aAAO,kBAAkB,QAAQ,KAAK,EAAE,KAAK,YAAY,KAAK,IAAI,IAAI,CAAC;AAAA,IACzE;AAKE,YAAM,aAAa;AACnB,YAAM,IAAI,MAAM,+BAA+B,UAAU,EAAE;AAAA,EAC/D;AACF;AAMA,SAAS,eAAe,OAAO;AAC7B,SAAO,SAAU,MAAM,QAAQ,QAAQ;AACrC,UAAM,OAAO,KAAK;AAClB,UAAM,YAAY,sBAAsB,QAAQ,aAAa,SAAS;AACtE,UAAM,QAAQ,OAAO,IAAI,KAAK,MAAM,OAAO,EAAE,IAAI,KAAK,KAAK,OAAO,EAAE;AACpE,YAAQ,OAAO;AAAA,MACb,KAAK,UAAU;AACb,gBAAQ,QAAQ,IAAI,MAAM,MAAM,UAAU,OAAO,GAAG,SAAS,IAAI,UAAU,KAAK,IAAI,OAAO,EAAE,GAAG,GAAG,SAAS;AAAA,MAC9G,KAAK,UAAU;AACb,eAAO,SAAS,QAAQ,IAAI,MAAM,MAAM,UAAU,OAAO,GAAG,SAAS;AAAA,MACvE,KAAK,UAAU;AACb,eAAO,SAAS,QAAQ,IAAI,MAAM,MAAM,UAAU,OAAO,GAAG,SAAS,IAAI,MAAM,UAAU,KAAK,IAAI,OAAO,EAAE,GAAG,GAAG,SAAS;AAAA,MAC5H,KAAK,UAAU;AACb,YAAI,WAAW,GAAG;AAChB,iBAAO;AAAA,QACT,OAAO;AACL,kBAAQ,QAAQ,IAAI,MAAM,MAAM,UAAU,OAAO,GAAG,SAAS,IAAI,MAAM,UAAU,KAAK,IAAI,OAAO,EAAE,GAAG,GAAG,SAAS;AAAA,QACpH;AAAA,MACF;AACE,cAAM,IAAI,MAAM,uBAAuB,KAAK,GAAG;AAAA,IACnD;AAAA,EACF;AACF;AACA,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,SAAS,uBAAuB,MAAM;AACpC,QAAM,iBAAiB,WAAW,MAAM,SAAS,CAAC,EAAE,OAAO;AAC3D,SAAO,WAAW,MAAM,GAAG,KAAK,kBAAkB,WAAW,WAAW,WAAW,KAAK,cAAc;AACxG;AAIA,SAAS,uBAAuB,UAAU;AAExC,QAAM,aAAa,SAAS,OAAO;AAEnC,QAAM,kBAAkB,eAAe,IAAI,KAAK,WAAW;AAC3D,SAAO,WAAW,SAAS,YAAY,GAAG,SAAS,SAAS,GAAG,SAAS,QAAQ,IAAI,eAAe;AACrG;AACA,SAAS,WAAW,MAAM,aAAa,OAAO;AAC5C,SAAO,SAAU,MAAM,QAAQ;AAC7B,QAAI;AACJ,QAAI,YAAY;AACd,YAAM,4BAA4B,IAAI,KAAK,KAAK,YAAY,GAAG,KAAK,SAAS,GAAG,CAAC,EAAE,OAAO,IAAI;AAC9F,YAAM,QAAQ,KAAK,QAAQ;AAC3B,eAAS,IAAI,KAAK,OAAO,QAAQ,6BAA6B,CAAC;AAAA,IACjE,OAAO;AACL,YAAM,YAAY,uBAAuB,IAAI;AAG7C,YAAM,aAAa,uBAAuB,UAAU,YAAY,CAAC;AACjE,YAAM,OAAO,UAAU,QAAQ,IAAI,WAAW,QAAQ;AACtD,eAAS,IAAI,KAAK,MAAM,OAAO,MAAO;AAAA,IACxC;AACA,WAAO,UAAU,QAAQ,MAAM,sBAAsB,QAAQ,aAAa,SAAS,CAAC;AAAA,EACtF;AACF;AAIA,SAAS,wBAAwB,MAAM,OAAO,OAAO;AACnD,SAAO,SAAU,MAAM,QAAQ;AAC7B,UAAM,YAAY,uBAAuB,IAAI;AAC7C,UAAM,oBAAoB,UAAU,YAAY;AAChD,WAAO,UAAU,mBAAmB,MAAM,sBAAsB,QAAQ,aAAa,SAAS,GAAG,IAAI;AAAA,EACvG;AACF;AACA,IAAM,eAAe,CAAC;AAKtB,SAAS,iBAAiB,QAAQ;AAChC,MAAI,aAAa,MAAM,GAAG;AACxB,WAAO,aAAa,MAAM;AAAA,EAC5B;AACA,MAAI;AACJ,UAAQ,QAAQ;AAAA,IAEd,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,cAAc,gBAAgB,MAAM,iBAAiB,WAAW;AAC5E;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,MAAM,iBAAiB,IAAI;AACrE;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,MAAM,iBAAiB,MAAM;AACvE;AAAA,IAEF,KAAK;AACH,kBAAY,WAAW,SAAS,UAAU,GAAG,GAAG,OAAO,IAAI;AAC3D;AAAA,IAEF,KAAK;AACH,kBAAY,WAAW,SAAS,UAAU,GAAG,GAAG,MAAM,IAAI;AAC1D;AAAA,IAEF,KAAK;AACH,kBAAY,WAAW,SAAS,UAAU,GAAG,GAAG,OAAO,IAAI;AAC3D;AAAA,IAEF,KAAK;AACH,kBAAY,WAAW,SAAS,UAAU,GAAG,GAAG,OAAO,IAAI;AAC3D;AAAA,IAEF,KAAK;AACH,kBAAY,wBAAwB,CAAC;AACrC;AAAA,IAGF,KAAK;AACH,kBAAY,wBAAwB,GAAG,IAAI;AAC3C;AAAA,IAGF,KAAK;AACH,kBAAY,wBAAwB,CAAC;AACrC;AAAA,IAEF,KAAK;AACH,kBAAY,wBAAwB,CAAC;AACrC;AAAA,IAEF,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,WAAW,SAAS,OAAO,GAAG,CAAC;AAC3C;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,WAAW,SAAS,OAAO,GAAG,CAAC;AAC3C;AAAA,IAEF,KAAK;AACH,kBAAY,cAAc,gBAAgB,QAAQ,iBAAiB,WAAW;AAC9E;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,QAAQ,iBAAiB,IAAI;AACvE;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,QAAQ,iBAAiB,MAAM;AACzE;AAAA,IAEF,KAAK;AACH,kBAAY,cAAc,gBAAgB,QAAQ,iBAAiB,aAAa,UAAU,UAAU;AACpG;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,QAAQ,iBAAiB,MAAM,UAAU,UAAU;AAC7F;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,QAAQ,iBAAiB,QAAQ,UAAU,UAAU;AAC/F;AAAA,IAEF,KAAK;AACH,kBAAY,WAAW,CAAC;AACxB;AAAA,IACF,KAAK;AACH,kBAAY,WAAW,CAAC;AACxB;AAAA,IAEF,KAAK;AACH,kBAAY,WAAW,GAAG,IAAI;AAC9B;AAAA,IAEF,KAAK;AACH,kBAAY,WAAW,SAAS,MAAM,CAAC;AACvC;AAAA,IACF,KAAK;AACH,kBAAY,WAAW,SAAS,MAAM,CAAC;AACvC;AAAA,IAEF,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,WAAW,SAAS,KAAK,CAAC;AACtC;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,MAAM,iBAAiB,aAAa,UAAU,UAAU;AAClG;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,MAAM,iBAAiB,MAAM,UAAU,UAAU;AAC3F;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,MAAM,iBAAiB,QAAQ,UAAU,UAAU;AAC7F;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,MAAM,iBAAiB,OAAO,UAAU,UAAU;AAC5F;AAAA,IAEF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,cAAc,gBAAgB,MAAM,iBAAiB,WAAW;AAC5E;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,MAAM,iBAAiB,IAAI;AACrE;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,MAAM,iBAAiB,MAAM;AACvE;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,MAAM,iBAAiB,KAAK;AACtE;AAAA,IAEF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,cAAc,gBAAgB,YAAY,iBAAiB,WAAW;AAClF;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,YAAY,iBAAiB,IAAI;AAC3E;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,YAAY,iBAAiB,MAAM;AAC7E;AAAA,IAEF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,cAAc,gBAAgB,YAAY,iBAAiB,aAAa,UAAU,YAAY,IAAI;AAC9G;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,YAAY,iBAAiB,MAAM,UAAU,YAAY,IAAI;AACvG;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,YAAY,iBAAiB,QAAQ,UAAU,YAAY,IAAI;AACzG;AAAA,IAEF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,cAAc,gBAAgB,YAAY,iBAAiB,aAAa,UAAU,QAAQ,IAAI;AAC1G;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,YAAY,iBAAiB,MAAM,UAAU,QAAQ,IAAI;AACnG;AAAA,IACF,KAAK;AACH,kBAAY,cAAc,gBAAgB,YAAY,iBAAiB,QAAQ,UAAU,QAAQ,IAAI;AACrG;AAAA,IAEF,KAAK;AACH,kBAAY,WAAW,SAAS,OAAO,GAAG,GAAG;AAC7C;AAAA,IACF,KAAK;AACH,kBAAY,WAAW,SAAS,OAAO,GAAG,GAAG;AAC7C;AAAA,IAEF,KAAK;AACH,kBAAY,WAAW,SAAS,OAAO,CAAC;AACxC;AAAA,IAEF,KAAK;AACH,kBAAY,WAAW,SAAS,OAAO,CAAC;AACxC;AAAA,IAEF,KAAK;AACH,kBAAY,WAAW,SAAS,SAAS,CAAC;AAC1C;AAAA,IACF,KAAK;AACH,kBAAY,WAAW,SAAS,SAAS,CAAC;AAC1C;AAAA,IAEF,KAAK;AACH,kBAAY,WAAW,SAAS,SAAS,CAAC;AAC1C;AAAA,IACF,KAAK;AACH,kBAAY,WAAW,SAAS,SAAS,CAAC;AAC1C;AAAA,IAEF,KAAK;AACH,kBAAY,WAAW,SAAS,mBAAmB,CAAC;AACpD;AAAA,IACF,KAAK;AACH,kBAAY,WAAW,SAAS,mBAAmB,CAAC;AACpD;AAAA,IACF,KAAK;AACH,kBAAY,WAAW,SAAS,mBAAmB,CAAC;AACpD;AAAA,IAEF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,eAAe,UAAU,KAAK;AAC1C;AAAA,IAEF,KAAK;AACH,kBAAY,eAAe,UAAU,QAAQ;AAC7C;AAAA,IAEF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IAEL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,eAAe,UAAU,QAAQ;AAC7C;AAAA,IAEF,KAAK;AAAA,IACL,KAAK;AAAA,IAEL,KAAK;AACH,kBAAY,eAAe,UAAU,IAAI;AACzC;AAAA,IACF;AACE,aAAO;AAAA,EACX;AACA,eAAa,MAAM,IAAI;AACvB,SAAO;AACT;AACA,SAAS,iBAAiB,UAAU,UAAU;AAG5C,aAAW,SAAS,QAAQ,MAAM,EAAE;AACpC,QAAM,0BAA0B,KAAK,MAAM,2BAA2B,QAAQ,IAAI;AAClF,SAAO,MAAM,uBAAuB,IAAI,WAAW;AACrD;AACA,SAAS,eAAe,MAAM,SAAS;AACrC,SAAO,IAAI,KAAK,KAAK,QAAQ,CAAC;AAC9B,OAAK,WAAW,KAAK,WAAW,IAAI,OAAO;AAC3C,SAAO;AACT;AACA,SAAS,uBAAuB,MAAM,UAAU,SAAS;AACvD,QAAM,eAAe,UAAU,KAAK;AACpC,QAAM,qBAAqB,KAAK,kBAAkB;AAClD,QAAM,iBAAiB,iBAAiB,UAAU,kBAAkB;AACpE,SAAO,eAAe,MAAM,gBAAgB,iBAAiB,mBAAmB;AAClF;AAaA,SAAS,OAAO,OAAO;AACrB,MAAI,OAAO,KAAK,GAAG;AACjB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,UAAU,YAAY,CAAC,MAAM,KAAK,GAAG;AAC9C,WAAO,IAAI,KAAK,KAAK;AAAA,EACvB;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,YAAQ,MAAM,KAAK;AACnB,QAAI,kCAAkC,KAAK,KAAK,GAAG;AAQjD,YAAM,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE,IAAI,SAAO,CAAC,GAAG;AAC1D,aAAO,WAAW,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/B;AACA,UAAM,WAAW,WAAW,KAAK;AAEjC,QAAI,CAAC,MAAM,QAAQ,QAAQ,GAAG;AAC5B,aAAO,IAAI,KAAK,QAAQ;AAAA,IAC1B;AACA,QAAI;AACJ,QAAI,QAAQ,MAAM,MAAM,kBAAkB,GAAG;AAC3C,aAAO,gBAAgB,KAAK;AAAA,IAC9B;AAAA,EACF;AACA,QAAM,OAAO,IAAI,KAAK,KAAK;AAC3B,MAAI,CAAC,OAAO,IAAI,GAAG;AACjB,UAAM,IAAI,MAAM,sBAAsB,KAAK,eAAe;AAAA,EAC5D;AACA,SAAO;AACT;AAKA,SAAS,gBAAgB,OAAO;AAC9B,QAAM,OAAO,oBAAI,KAAK,CAAC;AACvB,MAAI,SAAS;AACb,MAAI,QAAQ;AAEZ,QAAM,aAAa,MAAM,CAAC,IAAI,KAAK,iBAAiB,KAAK;AACzD,QAAM,aAAa,MAAM,CAAC,IAAI,KAAK,cAAc,KAAK;AAEtD,MAAI,MAAM,CAAC,GAAG;AACZ,aAAS,OAAO,MAAM,CAAC,IAAI,MAAM,EAAE,CAAC;AACpC,YAAQ,OAAO,MAAM,CAAC,IAAI,MAAM,EAAE,CAAC;AAAA,EACrC;AACA,aAAW,KAAK,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC,CAAC,CAAC;AAC9E,QAAM,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI;AAClC,QAAM,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI;AAClC,QAAM,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC;AAI9B,QAAM,KAAK,KAAK,MAAM,WAAW,QAAQ,MAAM,CAAC,KAAK,EAAE,IAAI,GAAI;AAC/D,aAAW,KAAK,MAAM,GAAG,GAAG,GAAG,EAAE;AACjC,SAAO;AACT;AACA,SAAS,OAAO,OAAO;AACrB,SAAO,iBAAiB,QAAQ,CAAC,MAAM,MAAM,QAAQ,CAAC;AACxD;AACA,IAAM,uBAAuB;AAC7B,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,YAAY;AAClB,IAAM,cAAc;AACpB,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,gBAAgB;AACtB,IAAM,eAAe;AAIrB,SAAS,2BAA2B,OAAO,SAAS,QAAQ,aAAa,eAAe,YAAY,YAAY,OAAO;AACrH,MAAI,gBAAgB;AACpB,MAAI,SAAS;AACb,MAAI,CAAC,SAAS,KAAK,GAAG;AACpB,oBAAgB,sBAAsB,QAAQ,aAAa,QAAQ;AAAA,EACrE,OAAO;AACL,QAAI,eAAe,YAAY,KAAK;AACpC,QAAI,WAAW;AACb,qBAAe,UAAU,YAAY;AAAA,IACvC;AACA,QAAI,SAAS,QAAQ;AACrB,QAAI,cAAc,QAAQ;AAC1B,QAAI,cAAc,QAAQ;AAC1B,QAAI,YAAY;AACd,YAAM,QAAQ,WAAW,MAAM,oBAAoB;AACnD,UAAI,UAAU,MAAM;AAClB,cAAM,IAAI,MAAM,GAAG,UAAU,4BAA4B;AAAA,MAC3D;AACA,YAAM,aAAa,MAAM,CAAC;AAC1B,YAAM,kBAAkB,MAAM,CAAC;AAC/B,YAAM,kBAAkB,MAAM,CAAC;AAC/B,UAAI,cAAc,MAAM;AACtB,iBAAS,kBAAkB,UAAU;AAAA,MACvC;AACA,UAAI,mBAAmB,MAAM;AAC3B,sBAAc,kBAAkB,eAAe;AAAA,MACjD;AACA,UAAI,mBAAmB,MAAM;AAC3B,sBAAc,kBAAkB,eAAe;AAAA,MACjD,WAAW,mBAAmB,QAAQ,cAAc,aAAa;AAC/D,sBAAc;AAAA,MAChB;AAAA,IACF;AACA,gBAAY,cAAc,aAAa,WAAW;AAClD,QAAI,SAAS,aAAa;AAC1B,QAAI,aAAa,aAAa;AAC9B,UAAM,WAAW,aAAa;AAC9B,QAAI,WAAW,CAAC;AAChB,aAAS,OAAO,MAAM,OAAK,CAAC,CAAC;AAE7B,WAAO,aAAa,QAAQ,cAAc;AACxC,aAAO,QAAQ,CAAC;AAAA,IAClB;AAEA,WAAO,aAAa,GAAG,cAAc;AACnC,aAAO,QAAQ,CAAC;AAAA,IAClB;AAEA,QAAI,aAAa,GAAG;AAClB,iBAAW,OAAO,OAAO,YAAY,OAAO,MAAM;AAAA,IACpD,OAAO;AACL,iBAAW;AACX,eAAS,CAAC,CAAC;AAAA,IACb;AAEA,UAAM,SAAS,CAAC;AAChB,QAAI,OAAO,UAAU,QAAQ,QAAQ;AACnC,aAAO,QAAQ,OAAO,OAAO,CAAC,QAAQ,QAAQ,OAAO,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IACvE;AACA,WAAO,OAAO,SAAS,QAAQ,OAAO;AACpC,aAAO,QAAQ,OAAO,OAAO,CAAC,QAAQ,OAAO,OAAO,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IACtE;AACA,QAAI,OAAO,QAAQ;AACjB,aAAO,QAAQ,OAAO,KAAK,EAAE,CAAC;AAAA,IAChC;AACA,oBAAgB,OAAO,KAAK,sBAAsB,QAAQ,WAAW,CAAC;AAEtE,QAAI,SAAS,QAAQ;AACnB,uBAAiB,sBAAsB,QAAQ,aAAa,IAAI,SAAS,KAAK,EAAE;AAAA,IAClF;AACA,QAAI,UAAU;AACZ,uBAAiB,sBAAsB,QAAQ,aAAa,WAAW,IAAI,MAAM;AAAA,IACnF;AAAA,EACF;AACA,MAAI,QAAQ,KAAK,CAAC,QAAQ;AACxB,oBAAgB,QAAQ,SAAS,gBAAgB,QAAQ;AAAA,EAC3D,OAAO;AACL,oBAAgB,QAAQ,SAAS,gBAAgB,QAAQ;AAAA,EAC3D;AACA,SAAO;AACT;AA0BA,SAAS,eAAe,OAAO,QAAQ,UAAU,cAAc,YAAY;AACzE,QAAM,SAAS,sBAAsB,QAAQ,kBAAkB,QAAQ;AACvE,QAAM,UAAU,kBAAkB,QAAQ,sBAAsB,QAAQ,aAAa,SAAS,CAAC;AAC/F,UAAQ,UAAU,0BAA0B,YAAY;AACxD,UAAQ,UAAU,QAAQ;AAC1B,QAAM,MAAM,2BAA2B,OAAO,SAAS,QAAQ,aAAa,eAAe,aAAa,iBAAiB,UAAU;AACnI,SAAO,IAAI,QAAQ,eAAe,QAAQ,EAEzC,QAAQ,eAAe,EAAE,EAKzB,KAAK;AACR;AAoBA,SAAS,cAAc,OAAO,QAAQ,YAAY;AAChD,QAAM,SAAS,sBAAsB,QAAQ,kBAAkB,OAAO;AACtE,QAAM,UAAU,kBAAkB,QAAQ,sBAAsB,QAAQ,aAAa,SAAS,CAAC;AAC/F,QAAM,MAAM,2BAA2B,OAAO,SAAS,QAAQ,aAAa,OAAO,aAAa,SAAS,YAAY,IAAI;AACzH,SAAO,IAAI,QAAQ,IAAI,OAAO,cAAc,GAAG,GAAG,sBAAsB,QAAQ,aAAa,WAAW,CAAC;AAC3G;AAkBA,SAAS,aAAa,OAAO,QAAQ,YAAY;AAC/C,QAAM,SAAS,sBAAsB,QAAQ,kBAAkB,OAAO;AACtE,QAAM,UAAU,kBAAkB,QAAQ,sBAAsB,QAAQ,aAAa,SAAS,CAAC;AAC/F,SAAO,2BAA2B,OAAO,SAAS,QAAQ,aAAa,OAAO,aAAa,SAAS,UAAU;AAChH;AACA,SAAS,kBAAkB,QAAQ,YAAY,KAAK;AAClD,QAAM,IAAI;AAAA,IACR,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACA,QAAM,eAAe,OAAO,MAAM,WAAW;AAC7C,QAAM,WAAW,aAAa,CAAC;AAC/B,QAAM,WAAW,aAAa,CAAC;AAC/B,QAAM,gBAAgB,SAAS,QAAQ,WAAW,MAAM,KAAK,SAAS,MAAM,WAAW,IAAI,CAAC,SAAS,UAAU,GAAG,SAAS,YAAY,SAAS,IAAI,CAAC,GAAG,SAAS,UAAU,SAAS,YAAY,SAAS,IAAI,CAAC,CAAC,GAC7M,UAAU,cAAc,CAAC,GACzB,WAAW,cAAc,CAAC,KAAK;AACjC,IAAE,SAAS,QAAQ,UAAU,GAAG,QAAQ,QAAQ,UAAU,CAAC;AAC3D,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAM,KAAK,SAAS,OAAO,CAAC;AAC5B,QAAI,OAAO,WAAW;AACpB,QAAE,UAAU,EAAE,UAAU,IAAI;AAAA,IAC9B,WAAW,OAAO,YAAY;AAC5B,QAAE,UAAU,IAAI;AAAA,IAClB,OAAO;AACL,QAAE,UAAU;AAAA,IACd;AAAA,EACF;AACA,QAAM,SAAS,QAAQ,MAAM,SAAS;AACtC,IAAE,QAAQ,OAAO,CAAC,IAAI,OAAO,CAAC,EAAE,SAAS;AACzC,IAAE,SAAS,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG,SAAS;AACtE,MAAI,UAAU;AACZ,UAAM,WAAW,SAAS,SAAS,EAAE,OAAO,SAAS,EAAE,OAAO,QAC5D,MAAM,SAAS,QAAQ,UAAU;AACnC,MAAE,SAAS,SAAS,UAAU,GAAG,GAAG,EAAE,QAAQ,MAAM,EAAE;AACtD,MAAE,SAAS,SAAS,MAAM,MAAM,QAAQ,EAAE,QAAQ,MAAM,EAAE;AAAA,EAC5D,OAAO;AACL,MAAE,SAAS,YAAY,EAAE;AACzB,MAAE,SAAS,EAAE;AAAA,EACf;AACA,SAAO;AACT;AAEA,SAAS,UAAU,cAAc;AAE/B,MAAI,aAAa,OAAO,CAAC,MAAM,GAAG;AAChC,WAAO;AAAA,EACT;AAEA,QAAM,cAAc,aAAa,OAAO,SAAS,aAAa;AAC9D,MAAI,aAAa,UAAU;AACzB,iBAAa,YAAY;AAAA,EAC3B,OAAO;AACL,QAAI,gBAAgB,GAAG;AACrB,mBAAa,OAAO,KAAK,GAAG,CAAC;AAAA,IAC/B,WAAW,gBAAgB,GAAG;AAC5B,mBAAa,OAAO,KAAK,CAAC;AAAA,IAC5B;AACA,iBAAa,cAAc;AAAA,EAC7B;AACA,SAAO;AACT;AAKA,SAAS,YAAY,KAAK;AACxB,MAAI,SAAS,KAAK,IAAI,GAAG,IAAI;AAC7B,MAAI,WAAW,GACb,QACA;AACF,MAAI,GAAG,GAAG;AAEV,OAAK,aAAa,OAAO,QAAQ,WAAW,KAAK,IAAI;AACnD,aAAS,OAAO,QAAQ,aAAa,EAAE;AAAA,EACzC;AAEA,OAAK,IAAI,OAAO,OAAO,IAAI,KAAK,GAAG;AAEjC,QAAI,aAAa,EAAG,cAAa;AACjC,kBAAc,CAAC,OAAO,MAAM,IAAI,CAAC;AACjC,aAAS,OAAO,UAAU,GAAG,CAAC;AAAA,EAChC,WAAW,aAAa,GAAG;AAEzB,iBAAa,OAAO;AAAA,EACtB;AAEA,OAAK,IAAI,GAAG,OAAO,OAAO,CAAC,MAAM,WAAW,KAAK;AAAA,EAEjD;AACA,MAAI,OAAO,QAAQ,OAAO,SAAS;AAEjC,aAAS,CAAC,CAAC;AACX,iBAAa;AAAA,EACf,OAAO;AAEL;AACA,WAAO,OAAO,OAAO,KAAK,MAAM,UAAW;AAE3C,kBAAc;AACd,aAAS,CAAC;AAEV,SAAK,IAAI,GAAG,KAAK,OAAO,KAAK,KAAK;AAChC,aAAO,CAAC,IAAI,OAAO,OAAO,OAAO,CAAC,CAAC;AAAA,IACrC;AAAA,EACF;AAEA,MAAI,aAAa,YAAY;AAC3B,aAAS,OAAO,OAAO,GAAG,aAAa,CAAC;AACxC,eAAW,aAAa;AACxB,iBAAa;AAAA,EACf;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKA,SAAS,YAAY,cAAc,SAAS,SAAS;AACnD,MAAI,UAAU,SAAS;AACrB,UAAM,IAAI,MAAM,gDAAgD,OAAO,iCAAiC,OAAO,IAAI;AAAA,EACrH;AACA,MAAI,SAAS,aAAa;AAC1B,MAAI,cAAc,OAAO,SAAS,aAAa;AAC/C,QAAM,eAAe,KAAK,IAAI,KAAK,IAAI,SAAS,WAAW,GAAG,OAAO;AAErE,MAAI,UAAU,eAAe,aAAa;AAC1C,MAAI,QAAQ,OAAO,OAAO;AAC1B,MAAI,UAAU,GAAG;AAEf,WAAO,OAAO,KAAK,IAAI,aAAa,YAAY,OAAO,CAAC;AAExD,aAAS,IAAI,SAAS,IAAI,OAAO,QAAQ,KAAK;AAC5C,aAAO,CAAC,IAAI;AAAA,IACd;AAAA,EACF,OAAO;AAEL,kBAAc,KAAK,IAAI,GAAG,WAAW;AACrC,iBAAa,aAAa;AAC1B,WAAO,SAAS,KAAK,IAAI,GAAG,UAAU,eAAe,CAAC;AACtD,WAAO,CAAC,IAAI;AACZ,aAAS,IAAI,GAAG,IAAI,SAAS,IAAK,QAAO,CAAC,IAAI;AAAA,EAChD;AACA,MAAI,SAAS,GAAG;AACd,QAAI,UAAU,IAAI,GAAG;AACnB,eAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,eAAO,QAAQ,CAAC;AAChB,qBAAa;AAAA,MACf;AACA,aAAO,QAAQ,CAAC;AAChB,mBAAa;AAAA,IACf,OAAO;AACL,aAAO,UAAU,CAAC;AAAA,IACpB;AAAA,EACF;AAEA,SAAO,cAAc,KAAK,IAAI,GAAG,YAAY,GAAG,cAAe,QAAO,KAAK,CAAC;AAC5E,MAAI,oBAAoB,iBAAiB;AAGzC,QAAM,SAAS,UAAU,aAAa;AAEtC,QAAM,QAAQ,OAAO,YAAY,SAAUC,QAAO,GAAG,GAAGC,SAAQ;AAC9D,QAAI,IAAID;AACR,IAAAC,QAAO,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI;AAC7B,QAAI,mBAAmB;AAErB,UAAIA,QAAO,CAAC,MAAM,KAAK,KAAK,QAAQ;AAClC,QAAAA,QAAO,IAAI;AAAA,MACb,OAAO;AACL,4BAAoB;AAAA,MACtB;AAAA,IACF;AACA,WAAO,KAAK,KAAK,IAAI;AAAA,EACvB,GAAG,CAAC;AACJ,MAAI,OAAO;AACT,WAAO,QAAQ,KAAK;AACpB,iBAAa;AAAA,EACf;AACF;AACA,SAAS,kBAAkB,MAAM;AAC/B,QAAM,SAAS,SAAS,IAAI;AAC5B,MAAI,MAAM,MAAM,GAAG;AACjB,UAAM,IAAI,MAAM,0CAA0C,IAAI;AAAA,EAChE;AACA,SAAO;AACT;AAKA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,SAAS,uBAAuB,GAAG;AAC1C,YAAI,IAAI;AACR,YAAI,GAAG;AACL,cAAI,IAAI,EAAE;AAAA,QACZ,OAAO;AACL,eAAK,YAAU,IAAI,qBAAqB,MAAM,GAAM,SAAS,SAAS,CAAC;AAAA,QACzE;AACA,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,YAAY,YAAU,IAAI,qBAAqB,MAAM;AAAA,MACrD,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,SAAS,kBAAkB,OAAO,OAAO,gBAAgB,QAAQ;AAC/D,MAAI,MAAM,IAAI,KAAK;AACnB,MAAI,MAAM,QAAQ,GAAG,IAAI,IAAI;AAC3B,WAAO;AAAA,EACT;AACA,QAAM,eAAe,kBAAkB,OAAO,MAAM;AACpD,MAAI,MAAM,QAAQ,GAAG,IAAI,IAAI;AAC3B,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,OAAO,IAAI,IAAI;AAC/B,WAAO;AAAA,EACT;AACA,QAAM,IAAI,MAAM,sCAAsC,KAAK,GAAG;AAChE;AAMA,IAAM,uBAAN,MAAM,8BAA6B,eAAe;AAAA,EAChD,YAAY,QAAQ;AAClB,UAAM;AACN,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,kBAAkB,OAAO,QAAQ;AAC/B,UAAM,SAASN,qBAAoB,UAAU,KAAK,MAAM,EAAE,KAAK;AAC/D,YAAQ,QAAQ;AAAA,MACd,KAAK,OAAO;AACV,eAAO;AAAA,MACT,KAAK,OAAO;AACV,eAAO;AAAA,MACT,KAAK,OAAO;AACV,eAAO;AAAA,MACT,KAAK,OAAO;AACV,eAAO;AAAA,MACT,KAAK,OAAO;AACV,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,SAAS,SAAS,CAAC;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,IAChC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAWH,SAASO,oBAAmB,MAAM,UAAU,WAAW;AACrD,SAAO,mBAAoB,MAAM,UAAU,SAAS;AACtD;AACA,SAAS,iBAAiB,WAAW,MAAM;AACzC,SAAO,mBAAmB,IAAI;AAC9B,aAAW,UAAU,UAAU,MAAM,GAAG,GAAG;AACzC,UAAM,UAAU,OAAO,QAAQ,GAAG;AAClC,UAAM,CAAC,YAAY,WAAW,IAAI,WAAW,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,MAAM,GAAG,OAAO,GAAG,OAAO,MAAM,UAAU,CAAC,CAAC;AACrH,QAAI,WAAW,KAAK,MAAM,MAAM;AAC9B,aAAO,mBAAmB,WAAW;AAAA,IACvC;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,YAAY;AAClB,IAAM,cAAc,CAAC;AA6BrB,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,YAAY,OAAO,WAAW;AAC5B,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,WAAW,oBAAI,IAAI;AAAA,EAC1B;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,iBAAiB,SAAS,OAAO,MAAM,KAAK,EAAE,MAAM,SAAS,IAAI;AAAA,EACxE;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,OAAO,UAAU,WAAW,MAAM,KAAK,EAAE,MAAM,SAAS,IAAI;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,YAAY;AAEV,eAAW,SAAS,KAAK,gBAAgB;AACvC,WAAK,aAAa,OAAO,IAAI;AAAA,IAC/B;AAEA,UAAM,WAAW,KAAK;AACtB,QAAI,MAAM,QAAQ,QAAQ,KAAK,oBAAoB,KAAK;AACtD,iBAAW,SAAS,UAAU;AAC5B,aAAK,aAAa,OAAO,IAAI;AAAA,MAC/B;AAAA,IACF,WAAW,YAAY,MAAM;AAC3B,iBAAW,SAAS,OAAO,KAAK,QAAQ,GAAG;AACzC,aAAK,aAAa,OAAO,QAAQ,SAAS,KAAK,CAAC,CAAC;AAAA,MACnD;AAAA,IACF;AACA,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,aAAa,OAAO,aAAa;AAC/B,UAAM,QAAQ,KAAK,SAAS,IAAI,KAAK;AACrC,QAAI,UAAU,QAAW;AACvB,UAAI,MAAM,YAAY,aAAa;AACjC,cAAM,UAAU;AAChB,cAAM,UAAU;AAAA,MAClB;AACA,YAAM,UAAU;AAAA,IAClB,OAAO;AACL,WAAK,SAAS,IAAI,OAAO;AAAA,QACvB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,eAAW,cAAc,KAAK,UAAU;AACtC,YAAM,QAAQ,WAAW,CAAC;AAC1B,YAAM,QAAQ,WAAW,CAAC;AAC1B,UAAI,MAAM,SAAS;AACjB,aAAK,aAAa,OAAO,MAAM,OAAO;AACtC,cAAM,UAAU;AAAA,MAClB,WAAW,CAAC,MAAM,SAAS;AAGzB,YAAI,MAAM,SAAS;AACjB,eAAK,aAAa,OAAO,KAAK;AAAA,QAChC;AACA,aAAK,SAAS,OAAO,KAAK;AAAA,MAC5B;AACA,YAAM,UAAU;AAAA,IAClB;AAAA,EACF;AAAA,EACA,aAAa,OAAO,SAAS;AAC3B,QAAI,WAAW;AACb,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,IAAI,MAAM,iEAAiE,UAAW,KAAK,CAAC,EAAE;AAAA,MACtG;AAAA,IACF;AACA,YAAQ,MAAM,KAAK;AACnB,QAAI,MAAM,SAAS,GAAG;AACpB,YAAM,MAAM,SAAS,EAAE,QAAQ,CAAAC,WAAS;AACtC,YAAI,SAAS;AACX,eAAK,UAAU,SAAS,KAAK,MAAM,eAAeA,MAAK;AAAA,QACzD,OAAO;AACL,eAAK,UAAU,YAAY,KAAK,MAAM,eAAeA,MAAK;AAAA,QAC5D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gBAAgB,GAAG;AACtC,aAAO,KAAK,KAAK,UAAY,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,CAAC;AAAA,IACnG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/B,QAAQ;AAAA,QACN,OAAO,CAAI,WAAa,MAAM,SAAS,OAAO;AAAA,QAC9C,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAuEH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,mBAAmB;AAC7B,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AAMzB,SAAK,cAAc,oBAAI,IAAI;AAAA,EAC7B;AAAA,EACA,gCAAgC,SAAS;AAIvC,WAAO,QAAQ,2BAA2B,MAAM,UAAa,QAAQ,kCAAkC,MAAM;AAAA,EAC/G;AAAA,EACA,iCAAiC,SAAS;AAIxC,WAAO,QAAQ,mBAAmB,MAAM,UAAa,QAAQ,0BAA0B,MAAM,UAAa,QAAQ,2BAA2B,MAAM,UAAa,KAAK,gCAAgC,OAAO;AAAA,EAC9M;AAAA;AAAA,EAEA,YAAY,SAAS;AACnB,QAAI,KAAK,iCAAiC,OAAO,GAAG;AAClD,WAAK,kBAAkB,MAAM;AAC7B,WAAK,YAAY,MAAM;AACvB,WAAK,gBAAgB;AACrB,UAAI,KAAK,mBAAmB;AAC1B,cAAM,WAAW,KAAK,6BAA6B,KAAK,kBAAkB;AAC1E,YAAI,KAAK,gCAAgC,OAAO,GAAG;AACjD,eAAK,YAAY,QAAQ;AACzB,cAAI,KAAK,2BAA2B;AAClC,iBAAK,aAAa,eAAe,KAAK,2BAA2B,kBAAkB,QAAQ,CAAC;AAAA,UAC9F,WAAW,KAAK,kCAAkC;AAChD,iBAAK,aAAa,KAAK,iCAAiC,OAAO,kBAAkB,QAAQ,CAAC;AAAA,UAC5F,OAAO;AACL,iBAAK,aAAa;AAAA,UACpB;AAAA,QACF;AACA,aAAK,gBAAgB,KAAK,kBAAkB,gBAAgB,KAAK,mBAAmB;AAAA,UAClF;AAAA,UACA,aAAa,KAAK;AAAA,UAClB,kBAAkB,KAAK;AAAA,QACzB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,YAAY;AACV,QAAI,KAAK,eAAe;AACtB,UAAI,KAAK,yBAAyB;AAChC,mBAAW,aAAa,OAAO,KAAK,KAAK,uBAAuB,GAAG;AACjE,eAAK,YAAY,IAAI,WAAW,IAAI;AAAA,QACtC;AAAA,MACF;AACA,WAAK,qBAAqB,KAAK,aAAa;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,YAAY,QAAQ;AAAA,EAC3B;AAAA,EACA,qBAAqB,cAAc;AACjC,eAAW,CAAC,WAAW,OAAO,KAAK,KAAK,aAAa;AACnD,UAAI,CAAC,SAAS;AAEZ,qBAAa,SAAS,WAAW,MAAS;AAC1C,aAAK,YAAY,OAAO,SAAS;AAAA,MACnC,OAAO;AAEL,qBAAa,SAAS,WAAW,KAAK,wBAAwB,SAAS,CAAC;AACxE,aAAK,YAAY,IAAI,WAAW,KAAK;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAqB,gBAAgB,CAAC;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,MACzC,QAAQ;AAAA,QACN,mBAAmB;AAAA,QACnB,yBAAyB;AAAA,QACzB,2BAA2B;AAAA,QAC3B,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,kCAAkC;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,SAAS,kBAAkB,UAAU;AACnC,QAAM,iBAAiB,SAAS,IAAI,aAAW;AAC/C,SAAO,eAAe;AACxB;AAKA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,WAAW,SAAS,OAAO,OAAO;AAC5C,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,UAAU,KAAK,QAAQ;AAAA,EACrC;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ,MAAM;AAAA,EAC5B;AAAA,EACA,IAAI,MAAM;AACR,WAAO,CAAC,KAAK;AAAA,EACf;AACF;AAmGA,IAAM,UAAN,MAAM,SAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAChB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,IAAI,aAAa,IAAI;AACnB,SAAK,OAAO,cAAc,eAAe,cAAc,MAAM,QAAQ,OAAO,OAAO,YAAY;AAC7F,cAAQ,KAAK,4CAA4C,KAAK,UAAU,EAAE,CAAC,sFAA2F;AAAA,IACxK;AACA,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,gBAAgB,WAAW,UAAU;AAC/C,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc,OAAO;AAIvB,QAAI,OAAO;AACT,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,QAAI,KAAK,eAAe;AACtB,WAAK,gBAAgB;AAErB,YAAM,QAAQ,KAAK;AACnB,UAAI,CAAC,KAAK,WAAW,OAAO;AAC1B,YAAI,OAAO,cAAc,eAAe,WAAW;AACjD,cAAI;AAGF,iBAAK,UAAU,KAAK,SAAS,KAAK,KAAK,EAAE,OAAO,KAAK,YAAY;AAAA,UACnE,QAAQ;AACN,gBAAI,eAAe,2CAA2C,KAAK,cAAmB,YAAY,KAAK,CAAC;AACxG,gBAAI,OAAO,UAAU,UAAU;AAC7B,8BAAgB;AAAA,YAClB;AACA,kBAAM,IAAI,aAAc,OAAoD,YAAY;AAAA,UAC1F;AAAA,QACF,OAAO;AAGL,eAAK,UAAU,KAAK,SAAS,KAAK,KAAK,EAAE,OAAO,KAAK,YAAY;AAAA,QACnE;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,SAAS;AAChB,YAAM,UAAU,KAAK,QAAQ,KAAK,KAAK,QAAQ;AAC/C,UAAI,QAAS,MAAK,cAAc,OAAO;AAAA,IACzC;AAAA,EACF;AAAA,EACA,cAAc,SAAS;AACrB,UAAM,gBAAgB,KAAK;AAC3B,YAAQ,iBAAiB,CAAC,MAAM,uBAAuB,iBAAiB;AACtE,UAAI,KAAK,iBAAiB,MAAM;AAI9B,sBAAc,mBAAmB,KAAK,WAAW,IAAI,eAAe,KAAK,MAAM,KAAK,UAAU,IAAI,EAAE,GAAG,iBAAiB,OAAO,SAAY,YAAY;AAAA,MACzJ,WAAW,gBAAgB,MAAM;AAC/B,sBAAc,OAAO,0BAA0B,OAAO,SAAY,qBAAqB;AAAA,MACzF,WAAW,0BAA0B,MAAM;AACzC,cAAM,OAAO,cAAc,IAAI,qBAAqB;AACpD,sBAAc,KAAK,MAAM,YAAY;AACrC,wBAAgB,MAAM,IAAI;AAAA,MAC5B;AAAA,IACF,CAAC;AACD,aAAS,IAAI,GAAG,OAAO,cAAc,QAAQ,IAAI,MAAM,KAAK;AAC1D,YAAM,UAAU,cAAc,IAAI,CAAC;AACnC,YAAM,UAAU,QAAQ;AACxB,cAAQ,QAAQ;AAChB,cAAQ,QAAQ;AAChB,cAAQ,UAAU,KAAK;AAAA,IACzB;AACA,YAAQ,sBAAsB,YAAU;AACtC,YAAM,UAAU,cAAc,IAAI,OAAO,YAAY;AACrD,sBAAgB,SAAS,MAAM;AAAA,IACjC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,uBAAuB,KAAK,KAAK;AACtC,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gBAAgB,GAAG;AACtC,aAAO,KAAK,KAAK,UAAY,kBAAqB,gBAAgB,GAAM,kBAAqB,WAAW,GAAM,kBAAqB,eAAe,CAAC;AAAA,IACrJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,SAAS,IAAI,WAAW,EAAE,CAAC;AAAA,MAC5C,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,cAAc;AAAA,QACd,eAAe;AAAA,MACjB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,gBAAgB,MAAM,QAAQ;AACrC,OAAK,QAAQ,YAAY,OAAO;AAClC;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,MAAM,KAAK,OAAO;AAChC;AA6IA,IAAM,OAAN,MAAM,MAAK;AAAA,EACT,YAAY,gBAAgB,aAAa;AACvC,SAAK,iBAAiB;AACtB,SAAK,WAAW,IAAI,YAAY;AAChC,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,KAAK,WAAW;AAClB,SAAK,SAAS,YAAY,KAAK,SAAS,OAAO;AAC/C,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS,aAAa;AACxB,mBAAe,YAAY,WAAW;AACtC,SAAK,mBAAmB;AACxB,SAAK,eAAe;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS,aAAa;AACxB,mBAAe,YAAY,WAAW;AACtC,SAAK,mBAAmB;AACxB,SAAK,eAAe;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,SAAS,WAAW;AAC3B,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,eAAe,MAAM;AAC1B,aAAK,eAAe;AACpB,YAAI,KAAK,kBAAkB;AACzB,eAAK,eAAe,KAAK,eAAe,mBAAmB,KAAK,kBAAkB,KAAK,QAAQ;AAAA,QACjG;AAAA,MACF;AAAA,IACF,OAAO;AACL,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,eAAe,MAAM;AAC1B,aAAK,eAAe;AACpB,YAAI,KAAK,kBAAkB;AACzB,eAAK,eAAe,KAAK,eAAe,mBAAmB,KAAK,kBAAkB,KAAK,QAAQ;AAAA,QACjG;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,uBAAuB,KAAK,KAAK;AACtC,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,aAAa,GAAG;AACnC,aAAO,KAAK,KAAK,OAAS,kBAAqB,gBAAgB,GAAM,kBAAqB,WAAW,CAAC;AAAA,IACxG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC;AAAA,MAC5B,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,MAAM,CAAC;AAAA,IAC7E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,IAAM,cAAN,MAAkB;AAAA,EAChB,cAAc;AACZ,SAAK,YAAY;AACjB,SAAK,OAAO;AAAA,EACd;AACF;AACA,SAAS,eAAe,UAAU,aAAa;AAC7C,QAAM,sBAAsB,CAAC,EAAE,CAAC,eAAe,YAAY;AAC3D,MAAI,CAAC,qBAAqB;AACxB,UAAM,IAAI,MAAM,GAAG,QAAQ,yCAAyC,UAAW,WAAW,CAAC,IAAI;AAAA,EACjG;AACF;AAMA,IAAM,8BAA8B;AACpC,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,mBAAmB,cAAc;AAC3C,SAAK,oBAAoB;AACzB,SAAK,eAAe;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,SAAS;AACP,SAAK,WAAW;AAChB,SAAK,kBAAkB,mBAAmB,KAAK,YAAY;AAAA,EAC7D;AAAA,EACA,UAAU;AACR,SAAK,WAAW;AAChB,SAAK,kBAAkB,MAAM;AAAA,EAC/B;AAAA,EACA,aAAa,SAAS;AACpB,QAAI,WAAW,CAAC,KAAK,UAAU;AAC7B,WAAK,OAAO;AAAA,IACd,WAAW,CAAC,WAAW,KAAK,UAAU;AACpC,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AACF;AAmEA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,cAAc;AACZ,SAAK,gBAAgB,CAAC;AACtB,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,sBAAsB;AAC3B,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,IAAI,SAAS,UAAU;AACrB,SAAK,YAAY;AACjB,QAAI,KAAK,eAAe,GAAG;AACzB,WAAK,oBAAoB,IAAI;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,YAAY,MAAM;AAChB,SAAK,cAAc,KAAK,IAAI;AAAA,EAC9B;AAAA;AAAA,EAEA,WAAW,OAAO;AAChB,UAAM,UAAU,8BAA8B,UAAU,KAAK,YAAY,SAAS,KAAK;AACvF,SAAK,OAAO,cAAc,eAAe,cAAc,aAAa,SAAS,KAAK,YAAY;AAC5F,cAAQ,KAAK,mBAAoB,MAA2D,mJAAwJ,eAAe,KAAK,CAAC,sCAAsC,eAAe,KAAK,SAAS,CAAC,wPAA6P,CAAC;AAAA,IAC7kB;AACA,SAAK,sBAAsB;AAC3B,SAAK;AACL,QAAI,KAAK,wBAAwB,KAAK,YAAY;AAChD,WAAK,oBAAoB,CAAC,KAAK,iBAAiB;AAChD,WAAK,sBAAsB;AAC3B,WAAK,oBAAoB;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,YAAY;AAC9B,QAAI,KAAK,cAAc,SAAS,KAAK,eAAe,KAAK,cAAc;AACrE,WAAK,eAAe;AACpB,iBAAW,eAAe,KAAK,eAAe;AAC5C,oBAAY,aAAa,UAAU;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iBAAiB,GAAG;AACvC,aAAO,KAAK,KAAK,WAAU;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,MAChC,QAAQ;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAkCH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,eAAe,aAAa,UAAU;AAChD,SAAK,WAAW;AAChB,SAAK,OAAO,cAAc,eAAe,cAAc,CAAC,UAAU;AAChE,yCAAmC,gBAAgB,cAAc;AAAA,IACnE;AACA,aAAS,SAAS;AAClB,SAAK,QAAQ,IAAI,WAAW,eAAe,WAAW;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,SAAK,MAAM,aAAa,KAAK,SAAS,WAAW,KAAK,YAAY,CAAC;AAAA,EACrE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAiB,kBAAqB,gBAAgB,GAAM,kBAAqB,WAAW,GAAM,kBAAkB,UAAU,CAAC,CAAC;AAAA,IACnJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,MACpC,QAAQ;AAAA,QACN,cAAc;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAeH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,eAAe,aAAa,UAAU;AAChD,SAAK,OAAO,cAAc,eAAe,cAAc,CAAC,UAAU;AAChE,yCAAmC,mBAAmB,iBAAiB;AAAA,IACzE;AACA,aAAS,YAAY,IAAI,WAAW,eAAe,WAAW,CAAC;AAAA,EACjE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAoB,kBAAqB,gBAAgB,GAAM,kBAAqB,WAAW,GAAM,kBAAkB,UAAU,CAAC,CAAC;AAAA,IACtJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,MACvC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,mCAAmC,UAAU,eAAe;AACnE,QAAM,IAAI,aAAc,KAAwD,wBAAwB,QAAQ,8BAAmC,aAAa,8GAAmH;AACrR;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,OAAO,UAAU,WAAW,IAAI,KAAK,MAAM,OAAO,KAAK;AAChE;AAiCA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,YAAY,eAAe;AACzB,SAAK,gBAAgB;AACrB,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,KAAK;AAAA,EACxB;AAAA,EACA,QAAQ,OAAO,YAAY;AACzB,SAAK,WAAW,KAAK,IAAI;AAAA,EAC3B;AAAA,EACA,YAAY,aAAa;AACvB,SAAK,YAAY;AACjB,UAAM,QAAQ,OAAO,KAAK,KAAK,UAAU;AACzC,UAAM,MAAM,kBAAkB,aAAa,OAAO,KAAK,aAAa;AACpE,SAAK,cAAc,KAAK,WAAW,GAAG,CAAC;AAAA,EACzC;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,YAAa,MAAK,YAAY,QAAQ;AAAA,EACjD;AAAA,EACA,cAAc,MAAM;AAClB,QAAI,MAAM;AACR,WAAK,cAAc;AACnB,WAAK,YAAY,OAAO;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iBAAiB,GAAG;AACvC,aAAO,KAAK,KAAK,WAAa,kBAAkB,cAAc,CAAC;AAAA,IACjE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,MAChC,QAAQ;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAqBH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,OAAO,UAAU,eAAe,UAAU;AACpD,SAAK,QAAQ;AACb,UAAM,YAAY,CAAC,MAAM,OAAO,KAAK,CAAC;AACtC,aAAS,QAAQ,YAAY,IAAI,KAAK,KAAK,OAAO,IAAI,WAAW,eAAe,QAAQ,CAAC;AAAA,EAC3F;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAiB,kBAAkB,cAAc,GAAM,kBAAqB,WAAW,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,UAAU,CAAC,CAAC;AAAA,IACzL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,MACpC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAsCH,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,YAAY,OAAO,UAAU,WAAW;AACtC,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,IAAI,QAAQ,QAAQ;AAClB,SAAK,WAAW;AAChB,QAAI,CAAC,KAAK,WAAW,QAAQ;AAC3B,WAAK,UAAU,KAAK,SAAS,KAAK,MAAM,EAAE,OAAO;AAAA,IACnD;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,SAAS;AAChB,YAAM,UAAU,KAAK,QAAQ,KAAK,KAAK,QAAQ;AAC/C,UAAI,SAAS;AACX,aAAK,cAAc,OAAO;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,aAAa,OAAO;AAC5B,UAAM,CAAC,MAAM,IAAI,IAAI,YAAY,MAAM,GAAG;AAC1C,UAAM,QAAQ,KAAK,QAAQ,GAAG,MAAM,KAAK,SAAY,oBAAoB;AACzE,QAAI,SAAS,MAAM;AACjB,WAAK,UAAU,SAAS,KAAK,MAAM,eAAe,MAAM,OAAO,GAAG,KAAK,GAAG,IAAI,KAAK,OAAO,KAAK;AAAA,IACjG,OAAO;AACL,WAAK,UAAU,YAAY,KAAK,MAAM,eAAe,MAAM,KAAK;AAAA,IAClE;AAAA,EACF;AAAA,EACA,cAAc,SAAS;AACrB,YAAQ,mBAAmB,YAAU,KAAK,UAAU,OAAO,KAAK,IAAI,CAAC;AACrE,YAAQ,iBAAiB,YAAU,KAAK,UAAU,OAAO,KAAK,OAAO,YAAY,CAAC;AAClF,YAAQ,mBAAmB,YAAU,KAAK,UAAU,OAAO,KAAK,OAAO,YAAY,CAAC;AAAA,EACtF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gBAAgB,GAAG;AACtC,aAAO,KAAK,KAAK,UAAY,kBAAqB,UAAU,GAAM,kBAAqB,eAAe,GAAM,kBAAqB,SAAS,CAAC;AAAA,IAC7I;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/B,QAAQ;AAAA,QACN,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AA0BH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,mBAAmB;AAC7B,SAAK,oBAAoB;AACzB,SAAK,WAAW;AAOhB,SAAK,0BAA0B;AAI/B,SAAK,mBAAmB;AAExB,SAAK,2BAA2B;AAAA,EAClC;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,KAAK,oBAAoB,OAAO,GAAG;AACrC,YAAM,mBAAmB,KAAK;AAC9B,UAAI,KAAK,UAAU;AACjB,yBAAiB,OAAO,iBAAiB,QAAQ,KAAK,QAAQ,CAAC;AAAA,MACjE;AAEA,UAAI,CAAC,KAAK,kBAAkB;AAC1B,aAAK,WAAW;AAChB;AAAA,MACF;AAGA,YAAM,cAAc,KAAK,2BAA2B;AACpD,WAAK,WAAW,iBAAiB,mBAAmB,KAAK,kBAAkB,aAAa;AAAA,QACtF,UAAU,KAAK,4BAA4B;AAAA,MAC7C,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,SAAS;AAC3B,WAAO,CAAC,CAAC,QAAQ,kBAAkB,KAAK,CAAC,CAAC,QAAQ,0BAA0B;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B;AAC3B,WAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MACnB,KAAK,CAAC,SAAS,MAAM,aAAa;AAChC,YAAI,CAAC,KAAK,yBAAyB;AACjC,iBAAO;AAAA,QACT;AACA,eAAO,QAAQ,IAAI,KAAK,yBAAyB,MAAM,QAAQ;AAAA,MACjE;AAAA,MACA,KAAK,CAAC,SAAS,MAAM,aAAa;AAChC,YAAI,CAAC,KAAK,yBAAyB;AACjC,iBAAO;AAAA,QACT;AACA,eAAO,QAAQ,IAAI,KAAK,yBAAyB,MAAM,QAAQ;AAAA,MACjE;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAqB,kBAAqB,gBAAgB,CAAC;AAAA,IAC9E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,MACxC,QAAQ;AAAA,QACN,yBAAyB;AAAA,QACzB,kBAAkB;AAAA,QAClB,0BAA0B;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,oBAAoB,CAAC,SAAS,mBAAmB,SAAS,MAAM,kBAAkB,SAAS,UAAU,cAAc,iBAAiB,UAAU,YAAY;AAChK,SAAS,yBAAyB,MAAM,OAAO;AAC7C,SAAO,IAAI,aAAc,MAAmD,aAAa,yBAAyB,KAAK,eAAe,UAAW,IAAI,CAAC,GAAG;AAC3J;AACA,IAAM,uBAAN,MAA2B;AAAA,EACzB,mBAAmB,OAAO,mBAAmB;AAQ3C,WAAO,UAAU,MAAM,MAAM,UAAU;AAAA,MACrC,MAAM;AAAA,MACN,OAAO,OAAK;AACV,cAAM;AAAA,MACR;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,QAAQ,cAAc;AAEpB,cAAU,MAAM,aAAa,YAAY,CAAC;AAAA,EAC5C;AACF;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,mBAAmB,OAAO,mBAAmB;AAC3C,WAAO,MAAM,KAAK,mBAAmB,OAAK;AACxC,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,QAAQ,cAAc;AAAA,EAAC;AACzB;AACA,IAAM,mBAAmB,IAAI,gBAAgB;AAC7C,IAAM,wBAAwB,IAAI,qBAAqB;AA6BvD,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,YAAY,KAAK;AACf,SAAK,eAAe;AACpB,SAAK,4BAA4B;AACjC,SAAK,gBAAgB;AACrB,SAAK,OAAO;AACZ,SAAK,YAAY;AAGjB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,SAAS;AAAA,IAChB;AAKA,SAAK,OAAO;AAAA,EACd;AAAA,EACA,UAAU,KAAK;AACb,QAAI,CAAC,KAAK,MAAM;AACd,UAAI,KAAK;AACP,YAAI;AAIF,eAAK,4BAA4B;AACjC,eAAK,WAAW,GAAG;AAAA,QACrB,UAAE;AACA,eAAK,4BAA4B;AAAA,QACnC;AAAA,MACF;AACA,aAAO,KAAK;AAAA,IACd;AACA,QAAI,QAAQ,KAAK,MAAM;AACrB,WAAK,SAAS;AACd,aAAO,KAAK,UAAU,GAAG;AAAA,IAC3B;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW,KAAK;AACd,SAAK,OAAO;AACZ,SAAK,YAAY,KAAK,gBAAgB,GAAG;AACzC,SAAK,gBAAgB,KAAK,UAAU,mBAAmB,KAAK,WAAS,KAAK,mBAAmB,KAAK,KAAK,CAAC;AAAA,EAC1G;AAAA,EACA,gBAAgB,KAAK;AACnB,QAAI,UAAW,GAAG,GAAG;AACnB,aAAO;AAAA,IACT;AACA,QAAI,eAAgB,GAAG,GAAG;AACxB,aAAO;AAAA,IACT;AACA,UAAM,yBAAyB,YAAW,GAAG;AAAA,EAC/C;AAAA,EACA,WAAW;AAGT,SAAK,UAAU,QAAQ,KAAK,aAAa;AACzC,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,mBAAmB,OAAO,OAAO;AAC/B,QAAI,UAAU,KAAK,MAAM;AACvB,WAAK,eAAe;AACpB,UAAI,KAAK,2BAA2B;AAClC,aAAK,MAAM,aAAa;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kBAAkB,GAAG;AACxC,aAAO,KAAK,KAAK,YAAc,kBAAqB,mBAAmB,EAAE,CAAC;AAAA,IAC5E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAiBH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,UAAU,OAAO;AACf,QAAI,SAAS,KAAM,QAAO;AAC1B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,yBAAyB,gBAAe,KAAK;AAAA,IACrD;AACA,WAAO,MAAM,YAAY;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AASH,IAAM,mBAAmB;AAkBzB,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,UAAU,OAAO;AACf,QAAI,SAAS,KAAM,QAAO;AAC1B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,yBAAyB,gBAAe,KAAK;AAAA,IACrD;AACA,WAAO,MAAM,QAAQ,kBAAkB,SAAO,IAAI,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC,EAAE,YAAY,CAAC;AAAA,EACjG;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AASH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,UAAU,OAAO;AACf,QAAI,SAAS,KAAM,QAAO;AAC1B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,yBAAyB,gBAAe,KAAK;AAAA,IACrD;AACA,WAAO,MAAM,YAAY;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,sBAAsB;AAQ5B,IAAM,6BAA6B,IAAI,eAAe,YAAY,+BAA+B,EAAE;AAgCnG,IAAM,4BAA4B,IAAI,eAAe,YAAY,8BAA8B,EAAE;AAiKjG,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,YAAY,QAAQ,iBAAiB,gBAAgB;AACnD,SAAK,SAAS;AACd,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,UAAU,OAAO,QAAQ,UAAU,QAAQ;AACzC,QAAI,SAAS,QAAQ,UAAU,MAAM,UAAU,MAAO,QAAO;AAC7D,QAAI;AACF,YAAM,UAAU,UAAU,KAAK,gBAAgB,cAAc;AAC7D,YAAM,YAAY,YAAY,KAAK,gBAAgB,YAAY,KAAK,mBAAmB;AACvF,aAAO,WAAW,OAAO,SAAS,UAAU,KAAK,QAAQ,SAAS;AAAA,IACpE,SAAS,OAAO;AACd,YAAM,yBAAyB,WAAU,MAAM,OAAO;AAAA,IACxD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iBAAiB,GAAG;AACvC,aAAO,KAAK,KAAK,WAAa,kBAAkB,WAAW,EAAE,GAAM,kBAAkB,4BAA4B,EAAE,GAAM,kBAAkB,2BAA2B,EAAE,CAAC;AAAA,IAC3K;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,wBAAwB;AAe9B,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,eAAe;AACzB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,OAAO,WAAW,QAAQ;AAClC,QAAI,SAAS,KAAM,QAAO;AAC1B,QAAI,OAAO,cAAc,YAAY,cAAc,MAAM;AACvD,YAAM,yBAAyB,iBAAgB,SAAS;AAAA,IAC1D;AACA,UAAM,MAAM,kBAAkB,OAAO,OAAO,KAAK,SAAS,GAAG,KAAK,eAAe,MAAM;AACvF,WAAO,UAAU,GAAG,EAAE,QAAQ,uBAAuB,MAAM,SAAS,CAAC;AAAA,EACvE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAmB,kBAAkB,gBAAgB,EAAE,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAmBH,IAAM,iBAAN,MAAM,gBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnB,UAAU,OAAO,SAAS;AACxB,QAAI,SAAS,KAAM,QAAO;AAC1B,QAAI,OAAO,YAAY,YAAY,OAAO,UAAU,UAAU;AAC5D,YAAM,yBAAyB,iBAAgB,OAAO;AAAA,IACxD;AACA,QAAI,QAAQ,eAAe,KAAK,GAAG;AACjC,aAAO,QAAQ,KAAK;AAAA,IACtB;AACA,QAAI,QAAQ,eAAe,OAAO,GAAG;AACnC,aAAO,QAAQ,OAAO;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAiBH,IAAM,WAAN,MAAM,UAAS;AAAA;AAAA;AAAA;AAAA,EAIb,UAAU,OAAO;AACf,WAAO,KAAK,UAAU,OAAO,MAAM,CAAC;AAAA,EACtC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iBAAiB,GAAG;AACvC,aAAO,KAAK,KAAK,WAAU;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,iBAAiB,KAAK,OAAO;AACpC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAqBA,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,SAAS;AACnB,SAAK,UAAU;AACf,SAAK,YAAY,CAAC;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,UAAU,OAAO,YAAY,mBAAmB;AAC9C,QAAI,CAAC,SAAS,EAAE,iBAAiB,QAAQ,OAAO,UAAU,UAAU;AAClE,aAAO;AAAA,IACT;AAEA,SAAK,WAAW,KAAK,QAAQ,KAAK,KAAK,EAAE,OAAO;AAChD,UAAM,gBAAgB,KAAK,OAAO,KAAK,KAAK;AAC5C,UAAM,mBAAmB,cAAc,KAAK;AAC5C,QAAI,eAAe;AACjB,WAAK,YAAY,CAAC;AAClB,oBAAc,YAAY,OAAK;AAC7B,aAAK,UAAU,KAAK,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC;AAAA,MAC7D,CAAC;AAAA,IACH;AACA,QAAI,iBAAiB,kBAAkB;AACrC,WAAK,UAAU,KAAK,SAAS;AAC7B,WAAK,YAAY;AAAA,IACnB;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAiB,kBAAqB,iBAAiB,EAAE,CAAC;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,kBAAkB,WAAW,WAAW;AAC/C,QAAM,IAAI,UAAU;AACpB,QAAM,IAAI,UAAU;AAEpB,MAAI,MAAM,EAAG,QAAO;AAEpB,MAAI,MAAM,OAAW,QAAO;AAC5B,MAAI,MAAM,OAAW,QAAO;AAE5B,MAAI,MAAM,KAAM,QAAO;AACvB,MAAI,MAAM,KAAM,QAAO;AACvB,MAAI,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAChD,WAAO,IAAI,IAAI,KAAK;AAAA,EACtB;AACA,MAAI,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAChD,WAAO,IAAI;AAAA,EACb;AACA,MAAI,OAAO,KAAK,aAAa,OAAO,KAAK,WAAW;AAClD,WAAO,IAAI,IAAI,KAAK;AAAA,EACtB;AAEA,QAAM,UAAU,OAAO,CAAC;AACxB,QAAM,UAAU,OAAO,CAAC;AACxB,SAAO,WAAW,UAAU,IAAI,UAAU,UAAU,KAAK;AAC3D;AAiEA,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,OAAO,YAAY,QAAQ;AACnC,QAAI,CAAC,QAAQ,KAAK,EAAG,QAAO;AAC5B,eAAW,KAAK;AAChB,QAAI;AACF,YAAM,MAAM,YAAY,KAAK;AAC7B,aAAO,aAAa,KAAK,QAAQ,UAAU;AAAA,IAC7C,SAAS,OAAO;AACd,YAAM,yBAAyB,cAAa,MAAM,OAAO;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,GAAG;AAC1C,aAAO,KAAK,KAAK,cAAgB,kBAAkB,WAAW,EAAE,CAAC;AAAA,IACnE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAqBH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,UAAU,OAAO,YAAY,QAAQ;AACnC,QAAI,CAAC,QAAQ,KAAK,EAAG,QAAO;AAC5B,eAAW,KAAK;AAChB,QAAI;AACF,YAAM,MAAM,YAAY,KAAK;AAC7B,aAAO,cAAc,KAAK,QAAQ,UAAU;AAAA,IAC9C,SAAS,OAAO;AACd,YAAM,yBAAyB,cAAa,MAAM,OAAO;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,GAAG;AAC1C,aAAO,KAAK,KAAK,cAAgB,kBAAkB,WAAW,EAAE,CAAC;AAAA,IACnE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAsBH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,SAAS,uBAAuB,OAAO;AACjD,SAAK,UAAU;AACf,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkCA,UAAU,OAAO,eAAe,KAAK,sBAAsB,UAAU,UAAU,YAAY,QAAQ;AACjG,QAAI,CAAC,QAAQ,KAAK,EAAG,QAAO;AAC5B,eAAW,KAAK;AAChB,QAAI,OAAO,YAAY,WAAW;AAChC,WAAK,OAAO,cAAc,eAAe,cAAc,WAAW,QAAQ,MAAM;AAC9E,gBAAQ,KAAK,0MAA0M;AAAA,MACzN;AACA,gBAAU,UAAU,WAAW;AAAA,IACjC;AACA,QAAI,WAAW,gBAAgB,KAAK;AACpC,QAAI,YAAY,QAAQ;AACtB,UAAI,YAAY,YAAY,YAAY,iBAAiB;AACvD,mBAAW,kBAAkB,UAAU,YAAY,WAAW,SAAS,UAAU,MAAM;AAAA,MACzF,OAAO;AACL,mBAAW;AAAA,MACb;AAAA,IACF;AACA,QAAI;AACF,YAAM,MAAM,YAAY,KAAK;AAC7B,aAAO,eAAe,KAAK,QAAQ,UAAU,cAAc,UAAU;AAAA,IACvE,SAAS,OAAO;AACd,YAAM,yBAAyB,eAAc,MAAM,OAAO;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAiB,kBAAkB,WAAW,EAAE,GAAM,kBAAkB,uBAAuB,EAAE,CAAC;AAAA,IACrH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,QAAQ,OAAO;AACtB,SAAO,EAAE,SAAS,QAAQ,UAAU,MAAM,UAAU;AACtD;AAIA,SAAS,YAAY,OAAO;AAE1B,MAAI,OAAO,UAAU,YAAY,CAAC,MAAM,OAAO,KAAK,IAAI,WAAW,KAAK,CAAC,GAAG;AAC1E,WAAO,OAAO,KAAK;AAAA,EACrB;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI,MAAM,GAAG,KAAK,kBAAkB;AAAA,EAC5C;AACA,SAAO;AACT;AAqCA,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,UAAU,OAAO,OAAO,KAAK;AAC3B,QAAI,SAAS,KAAM,QAAO;AAC1B,QAAI,CAAC,KAAK,SAAS,KAAK,GAAG;AACzB,YAAM,yBAAyB,YAAW,KAAK;AAAA,IACjD;AACA,WAAO,MAAM,MAAM,OAAO,GAAG;AAAA,EAC/B;AAAA,EACA,SAAS,KAAK;AACZ,WAAO,OAAO,QAAQ,YAAY,MAAM,QAAQ,GAAG;AAAA,EACrD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kBAAkB,GAAG;AACxC,aAAO,KAAK,KAAK,YAAW;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAM,eAAe,CAAC,WAAW,eAAe,eAAe,UAAU,WAAW,aAAa,aAAa,eAAe,cAAc,UAAU,gBAAgB,gBAAgB,YAAY;AAYjM,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAc;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,SAAS,mBAAmB,SAAS,MAAM,kBAAkB,SAAS,UAAU,cAAc,iBAAiB,UAAU,cAAc,WAAW,eAAe,eAAe,UAAU,WAAW,aAAa,aAAa,eAAe,cAAc,UAAU,gBAAgB,gBAAgB,YAAY;AAAA,MAC5T,SAAS,CAAC,SAAS,mBAAmB,SAAS,MAAM,kBAAkB,SAAS,UAAU,cAAc,iBAAiB,UAAU,cAAc,WAAW,eAAe,eAAe,UAAU,WAAW,aAAa,aAAa,eAAe,cAAc,UAAU,gBAAgB,gBAAgB,YAAY;AAAA,IAC9T,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,mBAAmB,YAAY;AAAA,MACzC,SAAS,CAAC,mBAAmB,YAAY;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAsB;AAC5B,IAAM,qBAAqB;AAC3B,IAAM,yBAAyB;AAC/B,IAAM,wBAAwB;AAK9B,SAAS,kBAAkB,YAAY;AACrC,SAAO,eAAe;AACxB;AAKA,SAAS,iBAAiB,YAAY;AACpC,SAAO,eAAe;AACxB;AAOA,SAAS,oBAAoB,YAAY;AACvC,SAAO,eAAe;AACxB;AAOA,SAAS,mBAAmB,YAAY;AACtC,SAAO,eAAe;AACxB;AAUA,IAAM,UAAU,IAAI,QAAQ,SAAS;AAOrC,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EAIrB,OAAO;AACL,SAAK,QAAQ,mBAAmB;AAAA,MAC9B,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,SAAS,MAAM,kBAAkB,OAAO,WAAW,CAAC,IAAI,IAAI,wBAAwB,OAAO,QAAQ,GAAG,MAAM,IAAI,IAAI,qBAAqB;AAAA,IAC3I,CAAC;AAAA,EACH;AACF;AAIA,IAAM,0BAAN,MAA8B;AAAA,EAC5B,YAAY,UAAUjB,SAAQ;AAC5B,SAAK,WAAW;AAChB,SAAK,SAASA;AACd,SAAK,SAAS,MAAM,CAAC,GAAG,CAAC;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,WAAK,SAAS,MAAM;AAAA,IACtB,OAAO;AACL,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,WAAO,CAAC,KAAK,OAAO,SAAS,KAAK,OAAO,OAAO;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,UAAU;AACzB,SAAK,OAAO,SAAS,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,eAAe,QAAQ;AACrB,UAAM,aAAa,uBAAuB,KAAK,UAAU,MAAM;AAC/D,QAAI,YAAY;AACd,WAAK,gBAAgB,UAAU;AAO/B,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,4BAA4B,mBAAmB;AAC7C,SAAK,OAAO,QAAQ,oBAAoB;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,IAAI;AAClB,UAAM,OAAO,GAAG,sBAAsB;AACtC,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO;AACrC,UAAM,MAAM,KAAK,MAAM,KAAK,OAAO;AACnC,UAAM,SAAS,KAAK,OAAO;AAC3B,SAAK,OAAO,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,CAAC;AAAA,EACxD;AACF;AACA,SAAS,uBAAuB,UAAU,QAAQ;AAChD,QAAM,iBAAiB,SAAS,eAAe,MAAM,KAAK,SAAS,kBAAkB,MAAM,EAAE,CAAC;AAC9F,MAAI,gBAAgB;AAClB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,SAAS,qBAAqB,cAAc,SAAS,QAAQ,OAAO,SAAS,KAAK,iBAAiB,YAAY;AACxH,UAAM,aAAa,SAAS,iBAAiB,SAAS,MAAM,WAAW,YAAY;AACnF,QAAI,cAAc,WAAW;AAC7B,WAAO,aAAa;AAClB,YAAM,aAAa,YAAY;AAC/B,UAAI,YAAY;AAGd,cAAM,SAAS,WAAW,eAAe,MAAM,KAAK,WAAW,cAAc,UAAU,MAAM,IAAI;AACjG,YAAI,QAAQ;AACV,iBAAO;AAAA,QACT;AAAA,MACF;AACA,oBAAc,WAAW,SAAS;AAAA,IACpC;AAAA,EACF;AACA,SAAO;AACT;AAIA,IAAM,uBAAN,MAA2B;AAAA;AAAA;AAAA;AAAA,EAIzB,UAAU,QAAQ;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA,EAInB,oBAAoB;AAClB,WAAO,CAAC,GAAG,CAAC;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,UAAU;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA,EAI5B,eAAe,QAAQ;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA,EAIxB,4BAA4B,mBAAmB;AAAA,EAAC;AAClD;AAOA,IAAM,aAAN,MAAiB;AAAC;AAKlB,IAAM,sBAAsB;AAG5B,SAAS,OAAO,KAAK,KAAK;AAExB,SAAO,cAAc,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,SAAS,IAAI;AAC3E;AAEA,SAAS,cAAc,KAAK;AAC1B,SAAO,eAAe,KAAK,GAAG;AAChC;AAGA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,cAAc,GAAG,IAAI,IAAI,IAAI,GAAG,EAAE,WAAW;AACtD;AACA,SAAS,YAAY,MAAM;AACzB,QAAM,WAAW,OAAO,SAAS;AACjC,MAAI,CAAC,YAAY,KAAK,KAAK,MAAM,IAAI;AACnC,WAAO;AAAA,EACT;AAEA,MAAI;AACF,UAAM,MAAM,IAAI,IAAI,IAAI;AACxB,WAAO;AAAA,EACT,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,KAAK,SAAS,GAAG,IAAI,KAAK,MAAM,GAAG,EAAE,IAAI;AAClD;AACA,SAAS,aAAa,KAAK;AACzB,SAAO,IAAI,WAAW,GAAG,IAAI,IAAI,MAAM,CAAC,IAAI;AAC9C;AASA,IAAM,kBAAkB,YAAU,OAAO;AAQzC,IAAM,eAAe,IAAI,eAAe,YAAY,gBAAgB,IAAI;AAAA,EACtE,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AAUD,SAAS,kBAAkB,YAAY,aAAa;AAClD,SAAO,SAAS,mBAAmB,MAAM;AACvC,QAAI,CAAC,YAAY,IAAI,GAAG;AACtB,4BAAsB,MAAM,eAAe,CAAC,CAAC;AAAA,IAC/C;AAGA,WAAO,cAAc,IAAI;AACzB,UAAM,WAAW,YAAU;AACzB,UAAI,cAAc,OAAO,GAAG,GAAG;AAM7B,wCAAgC,MAAM,OAAO,GAAG;AAAA,MAClD;AACA,aAAO,WAAW,MAAM,iCACnB,SADmB;AAAA,QAEtB,KAAK,aAAa,OAAO,GAAG;AAAA,MAC9B,EAAC;AAAA,IACH;AACA,UAAM,YAAY,CAAC;AAAA,MACjB,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AACD,WAAO;AAAA,EACT;AACF;AACA,SAAS,sBAAsB,MAAM,aAAa;AAChD,QAAM,IAAI,aAAc,MAAsD,aAAa,gDAAgD,IAAI,uEAA4E,YAAY,KAAK,MAAM,CAAC,EAAE;AACvP;AACA,SAAS,gCAAgC,MAAM,KAAK;AAClD,QAAM,IAAI,aAAc,MAAsD,aAAa,kFAAkF,GAAG,+MAAmO,IAAI,MAAM;AAC/Z;AAaA,IAAM,0BAA0B,kBAAkB,qBAAqB,YAAY,CAAC,uDAAuD,IAAI,MAAS;AACxJ,SAAS,oBAAoB,MAAM,QAAQ;AACzC,MAAI,SAAS;AACb,MAAI,OAAO,OAAO;AAChB,cAAU,UAAU,OAAO,KAAK;AAAA,EAClC;AAEA,MAAI,OAAO,eAAe;AACxB,cAAU,YAAY,mBAAmB;AAAA,EAC3C;AAGA,SAAO,GAAG,IAAI,kBAAkB,MAAM,IAAI,OAAO,GAAG;AACtD;AAKA,IAAM,uBAAuB;AAAA,EAC3B,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,0BAA0B;AAIhC,SAAS,gBAAgB,KAAK;AAC5B,SAAO,wBAAwB,KAAK,GAAG;AACzC;AAaA,IAAM,0BAA0B,kBAAkB,qBAAqB,YAAY,CAAC,qCAAqC,iCAAiC,8BAA8B,IAAI,MAAS;AACrM,SAAS,oBAAoB,MAAM,QAAQ;AAOzC,QAAM,UAAU,OAAO,gBAAgB,eAAe;AACtD,MAAI,SAAS,UAAU,OAAO;AAC9B,MAAI,OAAO,OAAO;AAChB,cAAU,MAAM,OAAO,KAAK;AAAA,EAC9B;AACA,SAAO,GAAG,IAAI,iBAAiB,MAAM,IAAI,OAAO,GAAG;AACrD;AAKA,IAAM,qBAAqB;AAAA,EACzB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,yBAAyB;AAI/B,SAAS,cAAc,KAAK;AAC1B,SAAO,uBAAuB,KAAK,GAAG;AACxC;AAYA,IAAM,wBAAwB,kBAAkB,mBAAmB,YAAY,CAAC,iCAAiC,8BAA8B,IAAI,MAAS;AAC5J,SAAS,kBAAkB,MAAM,QAAQ;AAGvC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,SAAS,CAAC;AAChB,MAAI,OAAO;AACT,WAAO,KAAK,KAAK,KAAK,EAAE;AAAA,EAC1B;AAEA,MAAI,OAAO,eAAe;AACxB,WAAO,KAAK,KAAK,mBAAmB,EAAE;AAAA,EACxC;AACA,QAAM,cAAc,OAAO,SAAS,CAAC,MAAM,MAAM,OAAO,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG;AACtF,QAAM,MAAM,IAAI,IAAI,YAAY,KAAK,GAAG,CAAC;AACzC,SAAO,IAAI;AACb;AAKA,IAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,qBAAqB;AAI3B,SAAS,WAAW,KAAK;AACvB,SAAO,mBAAmB,KAAK,GAAG;AACpC;AAUA,IAAM,qBAAqB,kBAAkB,gBAAgB,YAAY,CAAC,6BAA6B,IAAI,MAAS;AACpH,SAAS,eAAe,MAAM,QAAQ;AACpC,QAAM,MAAM,IAAI,IAAI,GAAG,IAAI,IAAI,OAAO,GAAG,EAAE;AAE3C,MAAI,aAAa,IAAI,QAAQ,QAAQ;AACrC,MAAI,OAAO,OAAO;AAChB,QAAI,aAAa,IAAI,KAAK,OAAO,MAAM,SAAS,CAAC;AAAA,EACnD;AAEA,MAAI,OAAO,eAAe;AACxB,QAAI,aAAa,IAAI,KAAK,mBAAmB;AAAA,EAC/C;AACA,SAAO,IAAI;AACb;AAKA,IAAM,oBAAoB;AAAA,EACxB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,uBAAuB;AAM7B,SAAS,aAAa,KAAK;AACzB,SAAO,qBAAqB,KAAK,GAAG;AACtC;AASA,SAAS,qBAAqB,MAAM;AAClC,MAAI,QAAQ,CAAC,YAAY,IAAI,GAAG;AAC9B,UAAM,IAAI,aAAc,MAAsD,aAAa,gDAAgD,IAAI,8GAAmH;AAAA,EACpQ;AACA,MAAI,MAAM;AACR,UAAM,MAAM,IAAI,IAAI,IAAI;AACxB,WAAO,IAAI;AAAA,EACb;AACA,QAAM,WAAW,YAAU;AACzB,WAAO,iBAAiB,QAAQ,IAAI;AAAA,EACtC;AACA,QAAM,YAAY,CAAC;AAAA,IACjB,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACD,SAAO;AACT;AACA,IAAM,cAAc,oBAAI,IAAI,CAAC,CAAC,UAAU,GAAG,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,YAAY,UAAU,CAAC,CAAC;AACrH,SAAS,iBAAiB,QAAQ,MAAM;AAEtC,QAAM,MAAM,IAAI,IAAI,QAAQ,YAAY;AACxC,MAAI,WAAW;AACf,MAAI,CAAC,cAAc,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,WAAW,GAAG,GAAG;AAC7D,WAAO,MAAM,MAAM,OAAO;AAAA,EAC5B;AACA,MAAI,aAAa,IAAI,OAAO,OAAO,GAAG;AACtC,MAAI,OAAO,OAAO;AAChB,QAAI,aAAa,IAAI,KAAK,OAAO,MAAM,SAAS,CAAC;AAAA,EACnD;AAGA,QAAM,gBAAgB,OAAO,eAAe,SAAS,KAAK,OAAO,eAAe,GAAG;AACnF,MAAI,OAAO,iBAAiB,CAAC,eAAe;AAC1C,QAAI,aAAa,IAAI,KAAK,mBAAmB;AAAA,EAC/C;AACA,aAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,OAAO,gBAAgB,CAAC,CAAC,GAAG;AACtE,QAAI,YAAY,IAAI,KAAK,GAAG;AAC1B,UAAI,aAAa,IAAI,YAAY,IAAI,KAAK,GAAG,MAAM,SAAS,CAAC;AAAA,IAC/D,OAAO;AACL,UAAI,WAAW;AACb,gBAAQ,KAAK,mBAAoB,MAAsD,4FAA4F,KAAK,MAAM,CAAC;AAAA,MACjM;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,aAAa,MAAM,IAAI,KAAK,QAAQ,IAAI,QAAQ,EAAE,IAAI,IAAI;AACvE;AAGA,SAAS,oBAAoB,OAAO,eAAe,MAAM;AACvD,QAAM,YAAY,eAAe,oDAAoD,KAAK,UAAU;AACpG,SAAO,kCAAkC,SAAS;AACpD;AAOA,SAAS,cAAc,WAAW;AAChC,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,aAAc,MAAoE,gCAAgC,SAAS,0FAA+F;AAAA,EACtO;AACF;AAYA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AAEZ,SAAK,SAAS,oBAAI,IAAI;AACtB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,kBAAc,aAAa;AAC3B,UAAM,MAAM,OAAO,QAAQ,EAAE;AAC7B,QAAI,OAAO,QAAQ,eAAe,OAAO,wBAAwB,aAAa;AAC5E,WAAK,SAAS;AACd,WAAK,WAAW,KAAK,wBAAwB;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B;AACxB,UAAM,WAAW,IAAI,oBAAoB,eAAa;AACpD,YAAM,UAAU,UAAU,WAAW;AACrC,UAAI,QAAQ,WAAW,EAAG;AAK1B,YAAM,aAAa,QAAQ,QAAQ,SAAS,CAAC;AAG7C,YAAM,SAAS,WAAW,SAAS,OAAO;AAE1C,UAAI,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,OAAO,EAAG;AAC9D,YAAM,MAAM,KAAK,OAAO,IAAI,MAAM;AAClC,UAAI,CAAC,IAAK;AACV,UAAI,CAAC,IAAI,YAAY,CAAC,IAAI,uBAAuB;AAC/C,YAAI,wBAAwB;AAC5B,gCAAwB,MAAM;AAAA,MAChC;AACA,UAAI,IAAI,YAAY,CAAC,IAAI,uBAAuB;AAC9C,YAAI,wBAAwB;AAC5B,2BAAmB,MAAM;AAAA,MAC3B;AAAA,IACF,CAAC;AACD,aAAS,QAAQ;AAAA,MACf,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,cAAc,cAAc,eAAe,YAAY;AACrD,QAAI,CAAC,KAAK,SAAU;AACpB,UAAM,wBAAwB;AAAA,MAC5B,UAAU;AAAA,MACV,UAAU;AAAA,MACV,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,IACzB;AACA,SAAK,OAAO,IAAI,OAAO,cAAc,KAAK,MAAM,EAAE,MAAM,qBAAqB;AAAA,EAC/E;AAAA,EACA,gBAAgB,cAAc;AAC5B,QAAI,CAAC,KAAK,SAAU;AACpB,SAAK,OAAO,OAAO,OAAO,cAAc,KAAK,MAAM,EAAE,IAAI;AAAA,EAC3D;AAAA,EACA,YAAY,aAAa,QAAQ;AAC/B,UAAM,cAAc,OAAO,aAAa,KAAK,MAAM,EAAE;AACrD,UAAM,MAAM,KAAK,OAAO,IAAI,WAAW;AACvC,QAAI,KAAK;AACP,UAAI,WAAW;AACf,WAAK,OAAO,IAAI,OAAO,QAAQ,KAAK,MAAM,EAAE,MAAM,GAAG;AACrD,WAAK,OAAO,OAAO,WAAW;AAAA,IAChC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,SAAU;AACpB,SAAK,SAAS,WAAW;AACzB,SAAK,OAAO,MAAM;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,SAAS,wBAAwB,OAAO;AACtC,QAAM,mBAAmB,oBAAoB,KAAK;AAClD,UAAQ,MAAM,mBAAoB,MAAsD,GAAG,gBAAgB,gNAA+N,CAAC;AAC7U;AACA,SAAS,mBAAmB,OAAO;AACjC,QAAM,mBAAmB,oBAAoB,KAAK;AAClD,UAAQ,KAAK,mBAAoB,MAAoD,GAAG,gBAAgB,oPAAmQ,CAAC;AAC9W;AAGA,IAAM,sCAAsC,oBAAI,IAAI,CAAC,aAAa,aAAa,SAAS,CAAC;AAmBzF,IAAM,6BAA6B,IAAI,eAAe,YAAY,+BAA+B,EAAE;AAQnG,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,cAAc;AACZ,SAAK,WAAW,OAAO,QAAQ;AAK/B,SAAK,kBAAkB;AAIvB,SAAK,cAAc,oBAAI,IAAI;AAC3B,SAAK,SAAS;AACd,SAAK,YAAY,IAAI,IAAI,mCAAmC;AAC5D,kBAAc,yBAAyB;AACvC,UAAM,MAAM,KAAK,SAAS;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC9B,WAAK,SAAS;AAAA,IAChB;AACA,UAAM,YAAY,OAAO,4BAA4B;AAAA,MACnD,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,WAAW;AACb,WAAK,kBAAkB,SAAS;AAAA,IAClC;AAAA,EACF;AAAA,EACA,kBAAkB,SAAS;AACzB,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,kBAAY,SAAS,YAAU;AAC7B,aAAK,UAAU,IAAI,gBAAgB,MAAM,CAAC;AAAA,MAC5C,CAAC;AAAA,IACH,OAAO;AACL,WAAK,UAAU,IAAI,gBAAgB,OAAO,CAAC;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,cAAc,eAAe;AAC5C,QAAI,CAAC,KAAK,OAAQ;AAClB,UAAM,SAAS,OAAO,cAAc,KAAK,MAAM;AAC/C,QAAI,KAAK,UAAU,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY,IAAI,OAAO,MAAM,EAAG;AAEhF,SAAK,YAAY,IAAI,OAAO,MAAM;AAKlC,SAAK,oBAAoB,KAAK,qBAAqB;AACnD,QAAI,CAAC,KAAK,gBAAgB,IAAI,OAAO,MAAM,GAAG;AAC5C,cAAQ,KAAK,mBAAoB,MAAiE,GAAG,oBAAoB,aAAa,CAAC;AAAA,iCAAiT,OAAO,MAAM,IAAI,CAAC;AAAA,IAC5c;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,UAAM,iBAAiB,oBAAI,IAAI;AAC/B,UAAM,WAAW;AACjB,UAAM,QAAQ,MAAM,KAAK,KAAK,SAAS,iBAAiB,QAAQ,CAAC;AACjE,aAAS,QAAQ,OAAO;AACtB,YAAM,MAAM,OAAO,KAAK,MAAM,KAAK,MAAM;AACzC,qBAAe,IAAI,IAAI,MAAM;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,MAAM;AAC5B,SAAK,YAAY,MAAM;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAAuB;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,uBAAsB;AAAA,MAC/B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,SAAS,YAAY,OAAO,IAAI;AAC9B,WAAS,SAAS,OAAO;AACvB,UAAM,QAAQ,KAAK,IAAI,YAAY,OAAO,EAAE,IAAI,GAAG,KAAK;AAAA,EAC1D;AACF;AAQA,IAAM,iCAAiC;AAQvC,IAAM,mBAAmB,IAAI,eAAe,iCAAiC;AAAA,EAC3E,YAAY;AAAA,EACZ,SAAS,MAAM,oBAAI,IAAI;AACzB,CAAC;AAUD,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,kBAAkB,OAAO,gBAAgB;AAC9C,SAAK,WAAW,OAAO,QAAQ;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,qBAAqB,UAAU,KAAK,QAAQ,OAAO;AACjD,QAAI,WAAW;AACb,UAAI,KAAK,gBAAgB,QAAQ,gCAAgC;AAC/D,cAAM,IAAI,aAAc,MAAuD,aAAa,kEAAuE,8BAA8B,gLAA0L;AAAA,MAC7X;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB,IAAI,GAAG,GAAG;AACjC;AAAA,IACF;AACA,SAAK,gBAAgB,IAAI,GAAG;AAC5B,UAAM,UAAU,SAAS,cAAc,MAAM;AAC7C,aAAS,aAAa,SAAS,MAAM,OAAO;AAC5C,aAAS,aAAa,SAAS,QAAQ,GAAG;AAC1C,aAAS,aAAa,SAAS,OAAO,SAAS;AAC/C,aAAS,aAAa,SAAS,iBAAiB,MAAM;AACtD,QAAI,OAAO;AACT,eAAS,aAAa,SAAS,cAAc,KAAK;AAAA,IACpD;AACA,QAAI,QAAQ;AACV,eAAS,aAAa,SAAS,eAAe,MAAM;AAAA,IACtD;AACA,aAAS,YAAY,KAAK,SAAS,MAAM,OAAO;AAAA,EAClD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oBAAmB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AASH,IAAM,iCAAiC;AAKvC,IAAM,gCAAgC;AAKtC,IAAM,kCAAkC;AAMxC,IAAM,8BAA8B;AAKpC,IAAM,iCAAiC;AAIvC,IAAM,6BAA6B,CAAC,GAAG,CAAC;AAIxC,IAAM,6BAA6B;AAInC,IAAM,yBAAyB;AAM/B,IAAM,4BAA4B;AAKlC,IAAM,2BAA2B;AACjC,IAAM,4BAA4B;AAIlC,IAAM,0BAA0B;AAUhC,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAE7B,IAAM,mBAAmB,CAAC,iBAAiB,oBAAoB,sBAAsB,iBAAiB;AAmGtG,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AACZ,SAAK,cAAc,OAAO,YAAY;AACtC,SAAK,SAAS,cAAc,OAAO,YAAa,CAAC;AACjD,SAAK,WAAW,OAAO,SAAS;AAChC,SAAK,aAAa,OAAO,UAAU,EAAE;AACrC,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,WAAW,iBAAiB,OAAO,WAAW,CAAC;AACpD,SAAK,qBAAqB,OAAO,kBAAkB;AAEnD,SAAK,cAAc,YAAY,KAAK,SAAS,IAAI,gBAAgB,IAAI;AAOrE,SAAK,eAAe;AAIpB,SAAK,WAAW;AAIhB,SAAK,yBAAyB;AAK9B,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA,WAAW;AACT,2BAAwB,kBAAkB;AAC1C,QAAI,WAAW;AACb,YAAM,SAAS,KAAK,SAAS,IAAI,MAAM;AACvC,0BAAoB,MAAM,SAAS,KAAK,KAAK;AAC7C,0BAAoB,MAAM,KAAK,QAAQ;AACvC,6BAAuB,IAAI;AAC3B,UAAI,KAAK,UAAU;AACjB,kCAA0B,IAAI;AAAA,MAChC;AACA,2BAAqB,IAAI;AACzB,uBAAiB,IAAI;AACrB,UAAI,KAAK,MAAM;AACb,kCAA0B,IAAI;AAG9B,eAAO,kBAAkB,MAAM,4BAA4B,MAAM,KAAK,YAAY,KAAK,QAAQ,CAAC;AAAA,MAClG,OAAO;AACL,qCAA6B,IAAI;AACjC,YAAI,KAAK,WAAW,QAAW;AAC7B,gCAAsB,MAAM,KAAK,QAAQ,QAAQ;AAAA,QACnD;AACA,YAAI,KAAK,UAAU,QAAW;AAC5B,gCAAsB,MAAM,KAAK,OAAO,OAAO;AAAA,QACjD;AAGA,eAAO,kBAAkB,MAAM,wBAAwB,MAAM,KAAK,YAAY,KAAK,QAAQ,CAAC;AAAA,MAC9F;AACA,8BAAwB,IAAI;AAC5B,UAAI,CAAC,KAAK,UAAU;AAClB,6BAAqB,IAAI;AAAA,MAC3B;AACA,6BAAuB,MAAM,KAAK,WAAW;AAC7C,oCAA8B,KAAK,OAAO,KAAK,WAAW;AAC1D,oCAA8B,MAAM,KAAK,WAAW;AACpD,wCAAkC,MAAM,KAAK,WAAW;AACxD,UAAI,KAAK,gBAAgB,MAAM;AAC7B,cAAMkB,UAAS,KAAK,SAAS,IAAI,MAAM;AACvC,QAAAA,QAAO,kBAAkB,MAAM;AAC7B,eAAK,YAAY,cAAc,KAAK,gBAAgB,GAAG,KAAK,OAAO,KAAK,QAAQ;AAAA,QAClF,CAAC;AAAA,MACH;AACA,UAAI,KAAK,UAAU;AACjB,cAAM,UAAU,KAAK,SAAS,IAAI,qBAAqB;AACvD,gBAAQ,iBAAiB,KAAK,gBAAgB,GAAG,KAAK,KAAK;AAAA,MAC7D;AAAA,IACF;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,wBAAwB,KAAK,UAAU;AAAA,IAC9C;AACA,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,oBAAoB;AAGlB,QAAI,KAAK,MAAM;AACb,WAAK,UAAU;AAAA,IACjB,OAAO;AACL,WAAK,iBAAiB,SAAS,KAAK,MAAM,SAAS,CAAC;AACpD,WAAK,iBAAiB,UAAU,KAAK,OAAO,SAAS,CAAC;AAAA,IACxD;AACA,SAAK,iBAAiB,WAAW,KAAK,mBAAmB,CAAC;AAC1D,SAAK,iBAAiB,iBAAiB,KAAK,iBAAiB,CAAC;AAG9D,SAAK,iBAAiB,UAAU,MAAM;AAGtC,UAAM,kBAAkB,KAAK,mBAAmB;AAChD,QAAI,KAAK,OAAO;AACd,WAAK,iBAAiB,SAAS,KAAK,KAAK;AAAA,IAC3C;AACA,QAAI,KAAK,YAAY,KAAK,UAAU;AAClC,WAAK,mBAAmB,qBAAqB,KAAK,UAAU,KAAK,gBAAgB,GAAG,iBAAiB,KAAK,KAAK;AAAA,IACjH;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,SAAS;AACnB,QAAI,WAAW;AACb,kCAA4B,MAAM,SAAS,CAAC,YAAY,SAAS,UAAU,YAAY,QAAQ,WAAW,SAAS,gBAAgB,wBAAwB,CAAC;AAAA,IAC9J;AACA,QAAI,QAAQ,OAAO,KAAK,CAAC,QAAQ,OAAO,EAAE,cAAc,GAAG;AACzD,YAAM,SAAS,KAAK;AACpB,WAAK,mBAAmB,IAAI;AAC5B,YAAM,SAAS,KAAK;AACpB,UAAI,KAAK,gBAAgB,QAAQ,UAAU,UAAU,WAAW,QAAQ;AACtE,cAAM,SAAS,KAAK,SAAS,IAAI,MAAM;AACvC,eAAO,kBAAkB,MAAM;AAC7B,eAAK,aAAa,YAAY,QAAQ,MAAM;AAAA,QAC9C,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB,2BAA2B;AACzC,QAAI,kBAAkB;AACtB,QAAI,KAAK,cAAc;AACrB,sBAAgB,eAAe,KAAK;AAAA,IACtC;AACA,WAAO,KAAK,YAAY,eAAe;AAAA,EACzC;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,YAAY,KAAK,YAAY,QAAW;AAChD,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,WAAW,UAAU;AAAA,EACnC;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,WAAW,SAAS;AAAA,EAClC;AAAA,EACA,kBAAkB;AAIhB,QAAI,CAAC,KAAK,cAAc;AACtB,YAAM,YAAY;AAAA,QAChB,KAAK,KAAK;AAAA,MACZ;AAEA,WAAK,eAAe,KAAK,gBAAgB,SAAS;AAAA,IACpD;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,qBAAqB;AACnB,UAAM,cAAc,8BAA8B,KAAK,KAAK,QAAQ;AACpE,UAAM,YAAY,KAAK,SAAS,MAAM,GAAG,EAAE,OAAO,SAAO,QAAQ,EAAE,EAAE,IAAI,YAAU;AACjF,eAAS,OAAO,KAAK;AACrB,YAAM,QAAQ,cAAc,WAAW,MAAM,IAAI,WAAW,MAAM,IAAI,KAAK;AAC3E,aAAO,GAAG,KAAK,gBAAgB;AAAA,QAC7B,KAAK,KAAK;AAAA,QACV;AAAA,MACF,CAAC,CAAC,IAAI,MAAM;AAAA,IACd,CAAC;AACD,WAAO,UAAU,KAAK,IAAI;AAAA,EAC5B;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,OAAO;AACd,aAAO,KAAK,oBAAoB;AAAA,IAClC,OAAO;AACL,aAAO,KAAK,eAAe;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK;AACT,QAAI,sBAAsB;AAC1B,QAAI,KAAK,OAAO,KAAK,MAAM,SAAS;AAGlC,4BAAsB,YAAY,OAAO,QAAM,MAAM,0BAA0B;AAAA,IACjF;AACA,UAAM,YAAY,oBAAoB,IAAI,QAAM,GAAG,KAAK,gBAAgB;AAAA,MACtE,KAAK,KAAK;AAAA,MACV,OAAO;AAAA,IACT,CAAC,CAAC,IAAI,EAAE,GAAG;AACX,WAAO,UAAU,KAAK,IAAI;AAAA,EAC5B;AAAA,EACA,mBAAmB,iBAAiB,OAAO;AACzC,QAAI,gBAAgB;AAGlB,WAAK,eAAe;AAAA,IACtB;AACA,UAAM,eAAe,KAAK,gBAAgB;AAC1C,SAAK,iBAAiB,OAAO,YAAY;AACzC,QAAI,kBAAkB;AACtB,QAAI,KAAK,UAAU;AACjB,wBAAkB,KAAK,mBAAmB;AAAA,IAC5C,WAAW,KAAK,8BAA8B,GAAG;AAC/C,wBAAkB,KAAK,mBAAmB;AAAA,IAC5C;AACA,QAAI,iBAAiB;AACnB,WAAK,iBAAiB,UAAU,eAAe;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AACf,UAAM,YAAY,2BAA2B,IAAI,gBAAc,GAAG,KAAK,gBAAgB;AAAA,MACrF,KAAK,KAAK;AAAA,MACV,OAAO,KAAK,QAAQ;AAAA,IACtB,CAAC,CAAC,IAAI,UAAU,GAAG;AACnB,WAAO,UAAU,KAAK,IAAI;AAAA,EAC5B;AAAA,EACA,gCAAgC;AAC9B,QAAI,iBAAiB;AACrB,QAAI,CAAC,KAAK,OAAO;AACf,uBAAiB,KAAK,QAAQ,4BAA4B,KAAK,SAAS;AAAA,IAC1E;AACA,WAAO,CAAC,KAAK,0BAA0B,CAAC,KAAK,UAAU,KAAK,gBAAgB,mBAAmB,CAAC;AAAA,EAClG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,kBAAkB;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK;AACT,QAAI,qBAAqB,MAAM;AAC7B,aAAO,OAAO,KAAK,gBAAgB;AAAA,QACjC,KAAK,KAAK;AAAA,QACV,OAAO;AAAA,QACP,eAAe;AAAA,MACjB,CAAC,CAAC;AAAA,IACJ,WAAW,OAAO,qBAAqB,YAAY,iBAAiB,WAAW,OAAO,GAAG;AACvF,aAAO,OAAO,gBAAgB;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,mBAAmB;AACvC,QAAI,CAAC,qBAAqB,CAAC,kBAAkB,eAAe,MAAM,GAAG;AACnE,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,kBAAkB,IAAI;AAAA,EACvC;AAAA,EACA,wBAAwB,KAAK;AAC3B,UAAM,WAAW,MAAM;AACrB,YAAM,oBAAoB,KAAK,SAAS,IAAI,iBAAiB;AAC7D,2BAAqB;AACrB,4BAAsB;AACtB,WAAK,cAAc;AACnB,wBAAkB,aAAa;AAAA,IACjC;AACA,UAAM,uBAAuB,KAAK,SAAS,OAAO,KAAK,QAAQ,QAAQ;AACvE,UAAM,wBAAwB,KAAK,SAAS,OAAO,KAAK,SAAS,QAAQ;AAAA,EAC3E;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,WAAW;AACb,UAAI,CAAC,KAAK,YAAY,KAAK,iBAAiB,QAAQ,KAAK,gBAAgB,MAAM;AAC7E,aAAK,YAAY,gBAAgB,KAAK,YAAY;AAAA,MACpD;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,MAAM,OAAO;AAC5B,SAAK,SAAS,aAAa,KAAK,YAAY,MAAM,KAAK;AAAA,EACzD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,OAAO,SAAS,EAAE,CAAC;AAAA,MAChC,UAAU;AAAA,MACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,YAAY,IAAI,OAAO,aAAa,IAAI,EAAE,SAAS,IAAI,OAAO,SAAS,IAAI,EAAE,UAAU,IAAI,OAAO,SAAS,IAAI,EAAE,SAAS,IAAI,OAAO,MAAM,IAAI,EAAE,mBAAmB,IAAI,cAAc,UAAU,IAAI,EAAE,uBAAuB,IAAI,cAAc,YAAY,IAAI,EAAE,qBAAqB,IAAI,cAAc,cAAc,IAAI,EAAE,oBAAoB,IAAI,cAAc,IAAI,oBAAoB,IAAI,WAAW,IAAI,IAAI,EAAE,UAAU,IAAI,eAAe,IAAI,sBAAsB,IAAI,iBAAiB,IAAI,eAAe,IAAI;AAAA,QACvgB;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO,CAAI,WAAa,4BAA4B,SAAS,SAAS,aAAa;AAAA,QACnF,UAAU;AAAA,QACV,OAAO;AAAA,QACP,OAAO,CAAI,WAAa,4BAA4B,SAAS,SAAS,eAAe;AAAA,QACrF,QAAQ,CAAI,WAAa,4BAA4B,UAAU,UAAU,eAAe;AAAA,QACxF,SAAS;AAAA,QACT,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,gBAAgB;AAAA,QAC/F,cAAc;AAAA,QACd,wBAAwB,CAAI,WAAa,4BAA4B,0BAA0B,0BAA0B,gBAAgB;AAAA,QACzI,MAAM,CAAI,WAAa,4BAA4B,QAAQ,QAAQ,gBAAgB;AAAA,QACnF,aAAa,CAAI,WAAa,4BAA4B,eAAe,eAAe,yBAAyB;AAAA,QACjH,mBAAmB;AAAA,QACnB,KAAK;AAAA,QACL,QAAQ;AAAA,MACV;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA6B,oBAAoB;AAAA,IACjE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,oBAAoB;AAAA,QACpB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,QACjB,2BAA2B;AAAA,QAC3B,+BAA+B;AAAA,QAC/B,6BAA6B;AAAA,QAC7B,4BAA4B;AAAA,QAC5B,kBAAkB,mEAAmE,uBAAuB;AAAA,MAC9G;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,UAAU;AAAA,QACV,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,SAAS,cAAc,QAAQ;AAC7B,MAAI,oBAAoB,CAAC;AACzB,MAAI,OAAO,aAAa;AACtB,sBAAkB,cAAc,OAAO,YAAY,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,EACzE;AACA,SAAO,OAAO,OAAO,CAAC,GAAG,uBAAwB,QAAQ,iBAAiB;AAC5E;AAKA,SAAS,uBAAuB,KAAK;AACnC,MAAI,IAAI,KAAK;AACX,UAAM,IAAI,aAAc,MAAiD,GAAG,oBAAoB,IAAI,KAAK,CAAC,0OAAyP;AAAA,EACrW;AACF;AAIA,SAAS,0BAA0B,KAAK;AACtC,MAAI,IAAI,QAAQ;AACd,UAAM,IAAI,aAAc,MAAoD,GAAG,oBAAoB,IAAI,KAAK,CAAC,yPAAwQ;AAAA,EACvX;AACF;AAIA,SAAS,qBAAqB,KAAK;AACjC,MAAI,QAAQ,IAAI,MAAM,KAAK;AAC3B,MAAI,MAAM,WAAW,OAAO,GAAG;AAC7B,QAAI,MAAM,SAAS,gCAAgC;AACjD,cAAQ,MAAM,UAAU,GAAG,8BAA8B,IAAI;AAAA,IAC/D;AACA,UAAM,IAAI,aAAc,MAA2C,GAAG,oBAAoB,IAAI,OAAO,KAAK,CAAC,0CAA+C,KAAK,yMAAmN;AAAA,EACpX;AACF;AAIA,SAAS,qBAAqB,KAAK;AACjC,MAAI,QAAQ,IAAI;AAChB,MAAI,OAAO,MAAM,mBAAmB,GAAG;AACrC,UAAM,IAAI,aAAc,MAA2C,GAAG,oBAAoB,IAAI,OAAO,KAAK,CAAC,kTAAiU;AAAA,EAC9a;AACF;AACA,SAAS,uBAAuB,KAAK,aAAa;AAChD,8CAA4C,GAAG;AAC/C,2CAAyC,KAAK,WAAW;AACzD,2BAAyB,GAAG;AAC9B;AAIA,SAAS,4CAA4C,KAAK;AACxD,MAAI,IAAI,qBAAqB,CAAC,IAAI,aAAa;AAC7C,UAAM,IAAI,aAAc,MAA2C,GAAG,oBAAoB,IAAI,OAAO,KAAK,CAAC,qIAA0I;AAAA,EACvP;AACF;AAKA,SAAS,yCAAyC,KAAK,aAAa;AAClE,MAAI,IAAI,gBAAgB,QAAQ,gBAAgB,iBAAiB;AAC/D,UAAM,IAAI,aAAc,MAAsD,GAAG,oBAAoB,IAAI,KAAK,CAAC,wSAAuT;AAAA,EACxa;AACF;AAIA,SAAS,yBAAyB,KAAK;AACrC,MAAI,IAAI,eAAe,OAAO,IAAI,gBAAgB,YAAY,IAAI,YAAY,WAAW,OAAO,GAAG;AACjG,QAAI,IAAI,YAAY,SAAS,sBAAsB;AACjD,YAAM,IAAI,aAAc,MAAmD,GAAG,oBAAoB,IAAI,KAAK,CAAC,4EAAiF,oBAAoB,4MAAsN;AAAA,IACza;AACA,QAAI,IAAI,YAAY,SAAS,qBAAqB;AAChD,cAAQ,KAAK,mBAAoB,MAAmD,GAAG,oBAAoB,IAAI,KAAK,CAAC,4EAAiF,mBAAmB,sNAAgO,CAAC;AAAA,IAC5b;AAAA,EACF;AACF;AAIA,SAAS,iBAAiB,KAAK;AAC7B,QAAM,QAAQ,IAAI,MAAM,KAAK;AAC7B,MAAI,MAAM,WAAW,OAAO,GAAG;AAC7B,UAAM,IAAI,aAAc,MAA2C,GAAG,oBAAoB,IAAI,KAAK,CAAC,qCAAqC,KAAK,6MAA4N;AAAA,EAC5W;AACF;AAIA,SAAS,oBAAoB,KAAK,MAAM,OAAO;AAC7C,QAAM,WAAW,OAAO,UAAU;AAClC,QAAM,gBAAgB,YAAY,MAAM,KAAK,MAAM;AACnD,MAAI,CAAC,YAAY,eAAe;AAC9B,UAAM,IAAI,aAAc,MAA2C,GAAG,oBAAoB,IAAI,KAAK,CAAC,MAAM,IAAI,8BAAmC,KAAK,2DAA2D;AAAA,EACnN;AACF;AAIA,SAAS,oBAAoB,KAAK,OAAO;AACvC,MAAI,SAAS,KAAM;AACnB,sBAAoB,KAAK,YAAY,KAAK;AAC1C,QAAM,YAAY;AAClB,QAAM,yBAAyB,8BAA8B,KAAK,SAAS;AAC3E,QAAM,2BAA2B,gCAAgC,KAAK,SAAS;AAC/E,MAAI,0BAA0B;AAC5B,0BAAsB,KAAK,SAAS;AAAA,EACtC;AACA,QAAM,gBAAgB,0BAA0B;AAChD,MAAI,CAAC,eAAe;AAClB,UAAM,IAAI,aAAc,MAA2C,GAAG,oBAAoB,IAAI,KAAK,CAAC,yCAAyC,KAAK,iKAA2K;AAAA,EAC/T;AACF;AACA,SAAS,sBAAsB,KAAK,OAAO;AACzC,QAAM,kBAAkB,MAAM,MAAM,GAAG,EAAE,MAAM,SAAO,QAAQ,MAAM,WAAW,GAAG,KAAK,2BAA2B;AAClH,MAAI,CAAC,iBAAiB;AACpB,UAAM,IAAI,aAAc,MAA2C,GAAG,oBAAoB,IAAI,KAAK,CAAC,6DAAkE,KAAK,oEAAyE,8BAA8B,wCAA6C,2BAA2B,4EAAiF,8BAA8B,gIAA0I,2BAA2B,oEAAoE;AAAA,EACprB;AACF;AAKA,SAAS,yBAAyB,KAAK,WAAW;AAChD,MAAI;AACJ,MAAI,cAAc,WAAW,cAAc,UAAU;AACnD,aAAS,cAAc,SAAS;AAAA,EAClC,OAAO;AACL,aAAS,kBAAkB,SAAS;AAAA,EACtC;AACA,SAAO,IAAI,aAAc,MAAqD,GAAG,oBAAoB,IAAI,KAAK,CAAC,MAAM,SAAS,4GAAiH,MAAM,iCAAsC,SAAS,kGAAuG;AAC7Y;AAIA,SAAS,4BAA4B,KAAK,SAAS,QAAQ;AACzD,SAAO,QAAQ,WAAS;AACtB,UAAM,YAAY,QAAQ,eAAe,KAAK;AAC9C,QAAI,aAAa,CAAC,QAAQ,KAAK,EAAE,cAAc,GAAG;AAChD,UAAI,UAAU,SAAS;AAKrB,cAAM;AAAA,UACJ,OAAO,QAAQ,KAAK,EAAE;AAAA,QACxB;AAAA,MACF;AACA,YAAM,yBAAyB,KAAK,KAAK;AAAA,IAC3C;AAAA,EACF,CAAC;AACH;AAIA,SAAS,sBAAsB,KAAK,YAAY,WAAW;AACzD,QAAM,cAAc,OAAO,eAAe,YAAY,aAAa;AACnE,QAAM,cAAc,OAAO,eAAe,YAAY,QAAQ,KAAK,WAAW,KAAK,CAAC,KAAK,SAAS,UAAU,IAAI;AAChH,MAAI,CAAC,eAAe,CAAC,aAAa;AAChC,UAAM,IAAI,aAAc,MAA2C,GAAG,oBAAoB,IAAI,KAAK,CAAC,MAAM,SAAS,mDAAwD,SAAS,gCAAgC;AAAA,EACtN;AACF;AAMA,SAAS,wBAAwB,KAAK,KAAK,UAAU;AACnD,QAAM,uBAAuB,SAAS,OAAO,KAAK,QAAQ,MAAM;AAC9D,yBAAqB;AACrB,0BAAsB;AACtB,UAAM,gBAAgB,OAAO,iBAAiB,GAAG;AACjD,QAAI,gBAAgB,WAAW,cAAc,iBAAiB,OAAO,CAAC;AACtE,QAAI,iBAAiB,WAAW,cAAc,iBAAiB,QAAQ,CAAC;AACxE,UAAM,YAAY,cAAc,iBAAiB,YAAY;AAC7D,QAAI,cAAc,cAAc;AAC9B,YAAM,aAAa,cAAc,iBAAiB,aAAa;AAC/D,YAAM,eAAe,cAAc,iBAAiB,eAAe;AACnE,YAAM,gBAAgB,cAAc,iBAAiB,gBAAgB;AACrE,YAAM,cAAc,cAAc,iBAAiB,cAAc;AACjE,uBAAiB,WAAW,YAAY,IAAI,WAAW,WAAW;AAClE,wBAAkB,WAAW,UAAU,IAAI,WAAW,aAAa;AAAA,IACrE;AACA,UAAM,sBAAsB,gBAAgB;AAC5C,UAAM,4BAA4B,kBAAkB,KAAK,mBAAmB;AAC5E,UAAM,iBAAiB,IAAI;AAC3B,UAAM,kBAAkB,IAAI;AAC5B,UAAM,uBAAuB,iBAAiB;AAC9C,UAAM,gBAAgB,IAAI;AAC1B,UAAM,iBAAiB,IAAI;AAC3B,UAAM,sBAAsB,gBAAgB;AAM5C,UAAM,uBAAuB,KAAK,IAAI,sBAAsB,oBAAoB,IAAI;AACpF,UAAM,oBAAoB,6BAA6B,KAAK,IAAI,uBAAuB,mBAAmB,IAAI;AAC9G,QAAI,sBAAsB;AACxB,cAAQ,KAAK,mBAAoB,MAA2C,GAAG,oBAAoB,IAAI,KAAK,CAAC;AAAA,wBAAkJ,cAAc,OAAO,eAAe,oBAAyB,MAAM,oBAAoB,CAAC;AAAA,wCAAmD,aAAa,OAAO,cAAc,oBAAoB,MAAM,mBAAmB,CAAC;AAAA,qDAAgE,CAAC;AAAA,IAC7hB,WAAW,mBAAmB;AAC5B,cAAQ,KAAK,mBAAoB,MAA2C,GAAG,oBAAoB,IAAI,KAAK,CAAC;AAAA,wBAAgI,cAAc,OAAO,eAAe,oBAAyB,MAAM,oBAAoB,CAAC;AAAA,uBAAkC,aAAa,OAAO,cAAc,oBAAyB,MAAM,mBAAmB,CAAC;AAAA,mQAA6R,CAAC;AAAA,IAC5tB,WAAW,CAAC,IAAI,YAAY,2BAA2B;AAErD,YAAM,mBAAmB,iCAAiC;AAC1D,YAAM,oBAAoB,iCAAiC;AAC3D,YAAM,iBAAiB,iBAAiB,oBAAoB;AAC5D,YAAM,kBAAkB,kBAAkB,qBAAqB;AAC/D,UAAI,kBAAkB,iBAAiB;AACrC,gBAAQ,KAAK,mBAAoB,MAA6C,GAAG,oBAAoB,IAAI,KAAK,CAAC;AAAA,uBAAiG,aAAa,OAAO,cAAc;AAAA,wBAAmC,cAAc,OAAO,eAAe;AAAA,oCAA+C,gBAAgB,OAAO,iBAAiB;AAAA,iFAAiG,8BAA8B,sGAA2G,CAAC;AAAA,MAC7nB;AAAA,IACF;AAAA,EACF,CAAC;AAKD,QAAM,wBAAwB,SAAS,OAAO,KAAK,SAAS,MAAM;AAChE,yBAAqB;AACrB,0BAAsB;AAAA,EACxB,CAAC;AACH;AAIA,SAAS,6BAA6B,KAAK;AACzC,MAAI,oBAAoB,CAAC;AACzB,MAAI,IAAI,UAAU,OAAW,mBAAkB,KAAK,OAAO;AAC3D,MAAI,IAAI,WAAW,OAAW,mBAAkB,KAAK,QAAQ;AAC7D,MAAI,kBAAkB,SAAS,GAAG;AAChC,UAAM,IAAI,aAAc,MAAoD,GAAG,oBAAoB,IAAI,KAAK,CAAC,2CAAgD,kBAAkB,IAAI,UAAQ,IAAI,IAAI,GAAG,EAAE,KAAK,IAAI,CAAC,iNAAgO;AAAA,EACpb;AACF;AAKA,SAAS,0BAA0B,KAAK;AACtC,MAAI,IAAI,SAAS,IAAI,QAAQ;AAC3B,UAAM,IAAI,aAAc,MAA2C,GAAG,oBAAoB,IAAI,KAAK,CAAC,4NAAsO;AAAA,EAC5U;AACF;AAKA,SAAS,4BAA4B,KAAK,KAAK,UAAU;AACvD,QAAM,uBAAuB,SAAS,OAAO,KAAK,QAAQ,MAAM;AAC9D,yBAAqB;AACrB,0BAAsB;AACtB,UAAM,iBAAiB,IAAI;AAC3B,QAAI,IAAI,QAAQ,mBAAmB,GAAG;AACpC,cAAQ,KAAK,mBAAoB,MAA2C,GAAG,oBAAoB,IAAI,KAAK,CAAC,8UAAkW,CAAC;AAAA,IACld;AAAA,EACF,CAAC;AAED,QAAM,wBAAwB,SAAS,OAAO,KAAK,SAAS,MAAM;AAChE,yBAAqB;AACrB,0BAAsB;AAAA,EACxB,CAAC;AACH;AAKA,SAAS,wBAAwB,KAAK;AACpC,MAAI,IAAI,WAAW,IAAI,UAAU;AAC/B,UAAM,IAAI,aAAc,MAA2C,GAAG,oBAAoB,IAAI,KAAK,CAAC,4PAAgR;AAAA,EACtX;AACA,QAAM,cAAc,CAAC,QAAQ,SAAS,MAAM;AAC5C,MAAI,OAAO,IAAI,YAAY,YAAY,CAAC,YAAY,SAAS,IAAI,OAAO,GAAG;AACzE,UAAM,IAAI,aAAc,MAA2C,GAAG,oBAAoB,IAAI,KAAK,CAAC,sDAA2D,IAAI,OAAO,uEAA4E;AAAA,EACxP;AACF;AAUA,SAAS,8BAA8B,OAAO,aAAa;AACzD,MAAI,gBAAgB,iBAAiB;AACnC,QAAI,oBAAoB;AACxB,eAAW,UAAU,kBAAkB;AACrC,UAAI,OAAO,QAAQ,KAAK,GAAG;AACzB,4BAAoB,OAAO;AAC3B;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB;AACrB,cAAQ,KAAK,mBAAoB,MAAoD,oEAAyE,iBAAiB,0IAAoJ,iBAAiB,qMAAoN,CAAC;AAAA,IAC3iB;AAAA,EACF;AACF;AAIA,SAAS,8BAA8B,KAAK,aAAa;AACvD,MAAI,IAAI,YAAY,gBAAgB,iBAAiB;AACnD,YAAQ,KAAK,mBAAoB,MAAsD,GAAG,oBAAoB,IAAI,KAAK,CAAC,6QAA4R,CAAC;AAAA,EACvZ;AACF;AAKA,SAAS,kCAAkC,KAAK,aAAa;AAC3D,MAAI,IAAI,gBAAgB,gBAAgB,iBAAiB;AACvD,YAAQ,KAAK,mBAAoB,MAAsD,GAAG,oBAAoB,IAAI,KAAK,CAAC,2SAA0T,CAAC;AAAA,EACrb;AACF;AACA,SAAS,MAAM,OAAO;AACpB,SAAO,OAAO,UAAU,KAAK,IAAI,QAAQ,MAAM,QAAQ,CAAC;AAC1D;AAGA,SAAS,cAAc,OAAO;AAC5B,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AACA,SAAO,gBAAiB,KAAK;AAC/B;AAGA,SAAS,0BAA0B,OAAO;AACxC,MAAI,OAAO,UAAU,YAAY,MAAM,WAAW,OAAO,GAAG;AAC1D,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,KAAK;AAC/B;", "names": ["window", "isAbsoluteUrl", "NumberFormatStyle", "Plural", "FormStyle", "TranslationWidth", "FormatWidth", "WeekDay", "getLocaleCurrencyCode", "getLocalePluralCase", "ZoneWidth", "DateType", "TranslationType", "value", "carry", "digits", "registerLocaleData", "klass", "ngZone"]}