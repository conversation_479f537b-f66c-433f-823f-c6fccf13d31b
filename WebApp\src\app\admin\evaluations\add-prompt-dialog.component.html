<div class="dialog-container">
  <h2 mat-dialog-title class="dialog-title">
    <mat-icon class="title-icon">{{ isEditMode ? 'edit' : 'add_circle' }}</mat-icon>
    {{ dialogTitle }}
  </h2>

  <mat-dialog-content class="dialog-content">
    <form [formGroup]="promptForm" (ngSubmit)="onSubmit()" class="prompt-form">
      
      <!-- Agent Selection -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>
          <mat-icon matPrefix>smart_toy</mat-icon>
          Agent
        </mat-label>
        <mat-select formControlName="agentName" [disabled]="isEditMode">
          <mat-option value="">Select an agent</mat-option>
          <mat-option *ngFor="let agent of agents" [value]="agent.agentName">
            {{ agent.agentName }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="hasError('agentName')">
          {{ getErrorMessage('agentName') }}
        </mat-error>
      </mat-form-field>

      <!-- Prompt Input -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>
          <mat-icon matPrefix>description</mat-icon>
          Prompt
        </mat-label>
        <textarea 
          matInput 
          formControlName="prompt" 
          placeholder="Enter the prompt for evaluation"
          rows="4"
          cdkTextareaAutosize
          cdkAutosizeMinRows="4"
          cdkAutosizeMaxRows="8">
        </textarea>
        <mat-error *ngIf="hasError('prompt')">
          {{ getErrorMessage('prompt') }}
        </mat-error>
      </mat-form-field>

      <!-- Expected Output -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>
          <mat-icon matPrefix>target</mat-icon>
          Expected Output (Optional)
        </mat-label>
        <textarea 
          matInput 
          formControlName="expectedOutput" 
          placeholder="Enter the expected output for evaluation (optional)"
          rows="3"
          cdkTextareaAutosize
          cdkAutosizeMinRows="3"
          cdkAutosizeMaxRows="6">
        </textarea>
      </mat-form-field>

      <!-- Information Note -->
      <div class="info-note">
        <mat-icon class="info-icon">info</mat-icon>
        <div class="info-content">
          <p *ngIf="!isEditMode">
            Your prompt will be sent to the selected agent for execution. If you provide an expected output, 
            the system will automatically score the response.
          </p>
          <p *ngIf="isEditMode">
            Note: Editing will clear any previous execution results and scores.
          </p>
        </div>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions class="dialog-actions">
    <button 
      mat-button 
      type="button" 
      (click)="onCancel()"
      [disabled]="submitting"
      class="cancel-button">
      <mat-icon>close</mat-icon>
      Cancel
    </button>
    
    <button 
      mat-raised-button 
      color="primary" 
      type="submit"
      (click)="onSubmit()"
      [disabled]="promptForm.invalid || submitting"
      class="submit-button">
      <mat-spinner *ngIf="submitting" diameter="20" class="button-spinner"></mat-spinner>
      <mat-icon *ngIf="!submitting">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
      {{ submitButtonText }}
    </button>
  </mat-dialog-actions>
</div>
