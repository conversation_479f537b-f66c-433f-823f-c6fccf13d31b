<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 65px);">
  <!-- Main Container -->
  <div class="flex-1 overflow-hidden flex flex-col">
    <!-- Header - Teams-style with compact vertical spacing -->
    <div class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50">
      <!-- Left side with title and back button -->
      <div class="flex items-center gap-3">
        <button
          class="w-8 h-8 flex items-center justify-center rounded-md border-none cursor-pointer transition-all duration-200"
          style="background-color: var(--background-white); color: var(--text-medium-gray); border: 1px solid var(--hover-blue-gray);"
          onmouseover="this.style.backgroundColor='var(--hover-blue-gray)'"
          onmouseout="this.style.backgroundColor='var(--background-white)'"
          (click)="goBack()" title="Go Back">
          <i class="ri-arrow-left-line text-base"></i>
        </button>
        <div class="flex items-center gap-2">
          <i class="ri-add-box-line text-[var(--primary-purple)] text-xl"></i>
          <h1 class="text-lg font-medium text-[var(--text-dark)]">
            {{!requestId ? 'New Request' : (isEditMode ? 'Edit Request' : 'View Request')}}
          </h1>
        </div>
      </div>

      <!-- Right side with action buttons -->
      <div class="flex items-center gap-2">
        <button
          *ngIf="isEditMode || !requestId"
          class="h-8 px-3 py-1 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center justify-center gap-1"
          (click)="submitRequest()">
          <i class="ri-send-plane-line"></i>
          <span>{{!requestId ? 'Submit Request' : 'Update Request'}}</span>
        </button>
      </div>
    </div>

    <!-- Content Area -->
    <div class="flex-1 overflow-y-auto px-4 pt-4">
      <div class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30">

        <!-- Request Details Section -->
        <div class="p-6 border-b border-[var(--hover-blue-gray)]">
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-4">Request Details</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
             <!-- Subject -->
            <div class="flex flex-col gap-2 ">
              <label for="subject" class="text-sm font-medium text-[var(--text-dark)]">Subject</label>
              <input
                type="text"
                id="subject"
                [(ngModel)]="formData.subject"
                [readonly]="!isEditMode && requestId"
                placeholder="Enter request subject"
                class=" h-9 px-3 text-sm border border-[var(--hover-blue-gray)] rounded-md focus:outline-none focus:ring-1 focus:ring-[var(--primary-purple)] focus:border-[var(--primary-purple)] transition-all duration-200"
                style="background-color: var(--background-white); color: var(--text-dark);" />
            </div>


            <!-- Priority -->
            <div class="flex flex-col gap-2">
              <label for="priority" class="text-sm font-medium text-[var(--text-dark)]">Priority</label>
              <select
                id="priority"
                [(ngModel)]="formData.priority"

                class="w-full h-9 px-3 text-sm border border-[var(--hover-blue-gray)] rounded-md focus:outline-none focus:ring-1 focus:ring-[var(--primary-purple)] focus:border-[var(--primary-purple)] transition-all duration-200"
                style="background-color: var(--background-white); color: var(--text-dark);">
                <option value="">Select priority</option>
                <option value="Low">Low</option>
                <option value="Medium">Medium</option>
                <option value="High">High</option>
                <option value="Critical">Critical</option>
              </select>
            </div>


            <!-- Description -->
            <div class="flex flex-col gap-2 md:col-span-2">
              <label for="description" class="text-sm font-medium text-[var(--text-dark)]">Message</label>
              <textarea
                id="description"
                [(ngModel)]="formData.message"
                [readonly]="!isEditMode && requestId"
                placeholder="Describe your request in detail"
                rows="4"
                class="w-full px-3 py-2 text-sm border border-[var(--hover-blue-gray)] rounded-md focus:outline-none focus:ring-1 focus:ring-[var(--primary-purple)] focus:border-[var(--primary-purple)] transition-all duration-200 resize-y"
                style="background-color: var(--background-white); color: var(--text-dark);"></textarea>
            </div>

            <!-- Expected Hours (if applicable) -->
            <!-- <div class="flex flex-col gap-2">
              <label for="expectedHours" class="text-sm font-medium text-[var(--text-dark)]">Expected Hours</label>
              <input
                type="number"
                id="expectedHours"
                [(ngModel)]="formData.expectedHours"
                [readonly]="!isEditMode && requestId"
                placeholder="Estimated hours"
                min="0"
                class="w-full h-9 px-3 text-sm border border-[var(--hover-blue-gray)] rounded-md focus:outline-none focus:ring-1 focus:ring-[var(--primary-purple)] focus:border-[var(--primary-purple)] transition-all duration-200"
                style="background-color: var(--background-white); color: var(--text-dark);" />
            </div> -->

            <!-- Due Date -->
            <!-- <div class="flex flex-col gap-2">
              <label for="dueDate" class="text-sm font-medium text-[var(--text-dark)]">Due Date</label>
              <input
                type="date"
                id="dueDate"
                [(ngModel)]="formData.dueDate"
                [readonly]="!isEditMode && requestId"
                class="w-full h-9 px-3 text-sm border border-[var(--hover-blue-gray)] rounded-md focus:outline-none focus:ring-1 focus:ring-[var(--primary-purple)] focus:border-[var(--primary-purple)] transition-all duration-200"
                style="background-color: var(--background-white); color: var(--text-dark);" />
            </div> -->
          </div>
        </div>

        <!-- Attachments Section -->
        <div class="p-6 border-b border-[var(--hover-blue-gray)]">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-[var(--text-dark)]">Attachments</h3>
            <button
              *ngIf="isEditMode || !requestId"
              class="h-8 px-3 py-1 bg-[var(--background-light-gray)] text-[var(--text-medium-gray)] text-sm font-medium rounded-md hover:bg-[var(--hover-blue-gray)] transition-all duration-200 flex items-center justify-center gap-1"
              (click)="addFileInput()">
              <i class="ri-attachment-line"></i>
              <span>Add File</span>
            </button>
          </div>

          <!-- File Upload Area -->
          <div class="bg-[var(--background-light-gray)] rounded-lg p-4">
            <!-- File Input Area for New/Edit Mode -->
            <div *ngIf="(isEditMode || !requestId) && showFileInputs" class="mb-4">
              <div class="border-2 border-dashed border-[var(--hover-blue-gray)] rounded-lg p-6 text-center">
                <i class="ri-upload-cloud-2-line text-3xl text-[var(--text-medium-gray)] mb-2"></i>
                <p class="text-sm text-[var(--text-medium-gray)] mb-3">
                  Drag and drop files here or click to browse
                </p>
                <input
                  type="file"
                  id="fileInput"
                  (change)="onFileChange($event)"
                  multiple
                  accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
                  class="hidden" />
                <button
                  class="px-4 py-2 bg-[var(--primary-purple)] text-white text-sm rounded-md hover:bg-opacity-90 transition-all duration-200"
                  (click)="triggerFileInput()">
                  Choose Files
                </button>
              </div>
            </div>

            <!-- File List -->
            <div *ngIf="attachedFiles.length > 0" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              <div *ngFor="let file of attachedFiles; let i = index"
                   class="flex items-center gap-3 p-3 bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md">
                <div class="w-8 h-8 flex items-center justify-center rounded bg-[var(--background-light-gray)]">
                  <i class="ri-file-line text-[var(--text-medium-gray)]"></i>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-[var(--text-dark)] truncate">{{file.name}}</p>
                  <p class="text-xs text-[var(--text-medium-gray)]">{{formatFileSize(file.size)}}</p>
                </div>
                <button
                  *ngIf="isEditMode || !requestId"
                  class="w-6 h-6 flex items-center justify-center rounded text-red-500 hover:bg-red-50 transition-all duration-200"
                  (click)="removeFile(i)">
                  <i class="ri-close-line text-sm"></i>
                </button>
              </div>
            </div>
          </div>
        </div>



        <!-- Status Section (View Mode Only) -->
        <!-- <div *ngIf="requestId && !isEditMode" class="p-6 border-b border-[var(--hover-blue-gray)]">
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-4">Request Status</h3>
          <div class="flex items-center gap-4">
            <span
              class="inline-flex items-center justify-center px-3 py-1 rounded-full text-sm font-medium"
              [ngClass]="{
                'bg-green-100 text-green-800': formData.status === 'Completed',
                'bg-orange-100 text-orange-800': formData.status === 'In Progress',
                'bg-blue-100 text-blue-800': formData.status === 'Pending',
                'bg-red-100 text-red-800': formData.status === 'Rejected'
              }">
              {{formData.status}}
            </span>
            <span *ngIf="formData.assigned" class="text-sm text-[var(--text-medium-gray)]">
              Assigned to: <strong>{{formData.assignedTo}}</strong>
            </span>
          </div>
        </div> -->

        <!-- Comments Section (View Mode Only) -->
        <div *ngIf="requestId && !isEditMode" class="p-6">
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-4">Comments</h3>

          <!-- Add Comment -->
          <div class="flex gap-3 mb-6">
            <textarea
              [(ngModel)]="newComment"
              placeholder="Write your comment here..."
              rows="3"
              class="flex-1 px-3 py-2 text-sm border border-[var(--hover-blue-gray)] rounded-md focus:outline-none focus:ring-1 focus:ring-[var(--primary-purple)] resize-y"
              style="background-color: var(--background-white); color: var(--text-dark);"></textarea>
            <button
              class="self-end h-9 px-4 bg-[var(--primary-purple)] text-white text-sm rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center gap-1"
              (click)="addComment()">
              <i class="ri-send-plane-line"></i>
              Send
            </button>
          </div>

          <!-- Comments List -->
          <div class="space-y-4">
            <div *ngFor="let comment of comments"
                 class="p-4 bg-[var(--background-light-gray)] rounded-lg border border-[var(--hover-blue-gray)]">
              <div class="flex justify-between items-center mb-2">
                <div class="flex items-center gap-2">
                  <div class="w-6 h-6 rounded-full bg-[var(--primary-purple)] text-white text-xs flex items-center justify-center">
                    {{comment.author.charAt(0).toUpperCase()}}
                  </div>
                  <span class="text-sm font-medium text-[var(--text-dark)]">{{comment.author}}</span>
                </div>
                <span class="text-xs text-[var(--text-medium-gray)]">{{comment.timestamp | date:'MMM d, y h:mm a'}}</span>
              </div>
              <p class="text-sm text-[var(--text-dark)]">{{comment.message}}</p>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
