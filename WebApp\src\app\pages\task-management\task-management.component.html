<div class="min-h-screen p-6 transition-[var(--transition-default)]"
     [ngClass]="{
       'bg-[var(--background-light-gray)] text-[var(--text-dark)]': !themeService.isDarkMode(),
       'bg-[var(--background-white)] text-[var(--text-dark)]': themeService.isDarkMode()
     }">

  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <div class="flex items-center gap-3">
      <div class="w-1 h-8 bg-gradient-to-b from-[var(--primary-purple)] to-[var(--secondary-purple)] rounded-full"></div>
      <div>
        <h1 class="text-2xl font-semibold text-[var(--text-dark)]">{{ activeTab }} Tasks</h1>
        <p class="text-sm text-[var(--text-medium-gray)] mt-1">Manage and track your project tasks</p>
      </div>
    </div>

    <!-- Tab <PERSON><PERSON> with Secure Styling -->
    <div class="flex gap-2 bg-[var(--background-light-gray)] p-1 rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)]">
      <button
        *ngFor="let tab of tabs; trackBy: trackByTab"
        (click)="setActiveTab(tab)"
        type="button"
        class="relative px-4 py-2 text-sm font-medium rounded-[var(--border-radius-small)] transition-[var(--transition-default)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed"
        [ngClass]="{
          'bg-[var(--primary-purple)] text-white shadow-sm': activeTab === tab,
          'text-[var(--text-dark)] hover:bg-[var(--hover-blue-gray)] hover:text-[var(--primary-purple)]': activeTab !== tab
        }"
        [attr.aria-pressed]="activeTab === tab"
        [attr.aria-label]="'Switch to ' + tab + ' tasks'">
        {{ tab }}
        <span *ngIf="activeTab === tab" class="absolute inset-0 rounded-[var(--border-radius-small)] ring-2 ring-[var(--primary-purple)] ring-opacity-30"></span>
      </button>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="flex flex-col justify-center items-center py-16">
    <div class="relative">
      <div class="w-12 h-12 border-4 border-[var(--hover-blue-gray)] border-t-[var(--primary-purple)] rounded-full animate-spin"></div>
    </div>
    <p class="mt-4 text-sm text-[var(--text-medium-gray)] font-medium">Loading tasks...</p>
  </div>

  <!-- Project Grid -->
  <div *ngIf="!isLoading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <div *ngFor="let project of filteredProjects; trackBy: trackByProject"
      class="group relative bg-[var(--background-white)] rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)] shadow-[var(--box-shadow)] hover:shadow-[0_8px_25px_rgba(0,0,0,0.15)] transition-[var(--transition-default)] p-6 flex flex-col overflow-hidden">

      <!-- Status Indicator -->
      <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r"
           [ngClass]="{
             'from-green-500 to-green-400': project.isCompleted,
             'from-red-500 to-red-400': !project.isCompleted && isOverdue(project),
             'from-[var(--primary-purple)] to-[var(--secondary-purple)]': !project.isCompleted && !isOverdue(project)
           }"></div>

      <!-- Card Header -->
      <div class="flex justify-between items-start mb-4">
        <div class="flex-1 min-w-0">
          <h3 class="text-lg font-semibold text-[var(--text-dark)] line-clamp-2 group-hover:text-[var(--primary-purple)] transition-colors duration-200">
            {{ project.message || 'Untitled Task' }}
          </h3>
          <div class="flex items-center gap-2 mt-2">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  [ngClass]="{
                    'bg-green-100 text-green-800': project.isCompleted,
                    'bg-red-100 text-red-800': !project.isCompleted && isOverdue(project),
                    'bg-blue-100 text-blue-800': !project.isCompleted && !isOverdue(project)
                  }">
              <i class="w-2 h-2 rounded-full mr-1.5"
                 [ngClass]="{
                   'bg-green-500': project.isCompleted,
                   'bg-red-500': !project.isCompleted && isOverdue(project),
                   'bg-blue-500': !project.isCompleted && !isOverdue(project)
                 }"></i>
              {{ project.isCompleted ? 'Completed' : (isOverdue(project) ? 'Overdue' : 'In Progress') }}
            </span>
          </div>
        </div>
      </div>

      <!-- Card Content -->
      <div class="flex-grow space-y-3">
        <div class="flex items-center gap-3 p-3 bg-[var(--background-light-gray)] rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)]">
          <div class="flex-shrink-0">
            <i class="ri-calendar-line text-[var(--primary-purple)] text-lg"></i>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-[var(--text-dark)]">Due Date</p>
            <p class="text-sm font-semibold"
               [ngClass]="{
                 'text-green-600': project.isCompleted,
                 'text-red-600': !project.isCompleted && isOverdue(project),
                 'text-[var(--text-medium-gray)]': !project.isCompleted && !isOverdue(project)
               }">
              {{ project.dueDate.toISODate() || 'No due date' }}
            </p>
          </div>
        </div>

      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredProjects.length === 0"
      class="col-span-full flex flex-col items-center justify-center py-16 bg-[var(--background-light-gray)] border-2 border-dashed border-[var(--hover-blue-gray)] rounded-[var(--border-radius-large)]">
      <div class="w-16 h-16 bg-[var(--secondary-purple)] bg-opacity-20 rounded-full flex items-center justify-center mb-4">
        <i class="ri-task-line text-3xl text-[var(--primary-purple)]"></i>
      </div>
      <h3 class="text-lg font-semibold text-[var(--text-dark)] mb-2">No {{ activeTab.toLowerCase() }} tasks found</h3>
      <p class="text-sm text-[var(--text-medium-gray)] text-center max-w-md">
        {{ activeTab === 'All' ? 'You don\'t have any tasks yet. Create your first task to get started.' : 'No tasks match the current filter. Try selecting a different tab.' }}
      </p>
      <button
        type="button"
        class="mt-6 px-6 py-2 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:ring-opacity-50 transition-[var(--transition-default)]"
        (click)="setActiveTab('All')"
        *ngIf="activeTab !== 'All'">
        <i class="ri-refresh-line mr-2"></i>
        View All Tasks
      </button>
    </div>
  </div>
</div>
