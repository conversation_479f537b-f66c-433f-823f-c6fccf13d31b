<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 65px);">
  <!-- Main Container -->
  <div class="flex-1 overflow-hidden flex flex-col">
    <!-- Header - Teams-style with compact vertical spacing -->
    <div
      class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50">
      <!-- Left side with title and count -->
      <div class="flex items-center gap-2">
        <i class="ri-robot-line text-[var(--primary-purple)] text-xl"></i>
        <h1 class="text-lg font-medium text-[var(--text-dark)]">AI Agents</h1>
        <div
          class="inline-flex items-center justify-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]">
          {{ filteredAgents.length }}
        </div>
      </div>

      <!-- Right side with controls -->
      <div class="flex items-center gap-2">
        <!-- Search Input - Teams-style -->
        <div class="relative w-full sm:w-56 flex items-center">
          <div class="absolute inset-y-0 left-0 flex items-center justify-center pl-2 pointer-events-none">
            <i class="ri-search-line text-[var(--text-medium-gray)] text-sm"></i>
          </div>
          <input type="text" placeholder="Search agents..." [(ngModel)]="searchQuery" (ngModelChange)="filterAgents()"
            class="w-full h-8 px-3 py-1 pl-8 text-sm border-none text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200" />
          <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-2" *ngIf="searchQuery">
            <button (click)="searchQuery = ''; filterAgents()"
              class="text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] border-none bg-transparent transition-colors focus:outline-none">
              <i class="ri-close-line text-sm"></i>
            </button>
          </div>
        </div>

        <!-- Add Agent Button - Teams-style -->
        <button [routerLink]="['new']"
          class="h-8 px-3 py-1 bg-[var(--primary-purple)] border-none text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center justify-center gap-1">
          <i class="ri-add-line"></i>
          <span>Add Agent</span>
        </button>
      </div>
    </div>

    <!-- Stats Section -->
    <div class="px-4 pt-4">
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
        <div class="bg-[var(--background-white)] rounded-md p-4 border border-[var(--hover-blue-gray)]">
          <div class="flex items-center">
            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
              <i class="ri-robot-line text-blue-600"></i>
            </div>
            <div>
              <p class="text-sm text-[var(--text-medium-gray)]">Total Agents</p>
              <p class="text-xl font-semibold text-[var(--text-dark)]">{{ agents.length }}</p>
            </div>
          </div>
        </div>

        <div class="bg-[var(--background-white)] rounded-md p-4 border border-[var(--hover-blue-gray)]">
          <div class="flex items-center">
            <div class="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center mr-3">
              <i class="ri-shield-check-line text-amber-600"></i>
            </div>
            <div>
              <p class="text-sm text-[var(--text-medium-gray)]">System Agents</p>
              <p class="text-xl font-semibold text-[var(--text-dark)]">{{ getSystemAgentsCount() }}</p>
            </div>
          </div>
        </div>

        <div class="bg-[var(--background-white)] rounded-md p-4 border border-[var(--hover-blue-gray)]">
          <div class="flex items-center">
            <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
              <i class="ri-delete-bin-line text-green-600"></i>
            </div>
            <div>
              <p class="text-sm text-[var(--text-medium-gray)]">Deletable Agents</p>
              <p class="text-xl font-semibold text-[var(--text-dark)]">{{ getDeletableAgentsCount() }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Agent Cards Grid -->
    <div class="flex-1 overflow-y-auto px-4">
      <!-- Loading Spinner -->
      <div *ngIf="isLoading" class="relative min-h-[300px]">
        <app-spinner message="Loading agents..." [overlay]="false"></app-spinner>
      </div>

      <div *ngIf="!isLoading" class="rounded-lg overflow-hidden p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          <!-- Agent Card -->
          <div *ngFor="let agent of paginatedAgents; let i = index"
            class="bg-[var(--background-white)] rounded-md overflow-hidden group animate-fadeIn relative h-full flex flex-col cursor-pointer agent-card"
            [ngStyle]="{'animation-delay': (i * 0.05) + 's'}" [routerLink]="[agent.agentName]">
            <!-- System Agent Badge (Top Right, below action buttons) -->
            <div *ngIf="agent.isSystemAgent" class="absolute top-12 right-3 z-10">
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 border border-amber-200">
                <i class="ri-shield-check-line mr-1"></i>
                Protected
              </span>
            </div>

            <!-- Action Buttons (Top Right) -->
            <div
              class="absolute top-3 right-3 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
              <!-- System Agent Toggle Button (Only show for non-system agents) -->
              <button *ngIf="!agent.isSystemAgent" (click)="makeSystemAgent(agent.agentName); $event.stopPropagation()"
                class="action-button w-8 h-8 rounded-md transition-all duration-200 flex items-center justify-center border-none shadow-sm bg-gray-100 hover:bg-amber-100"
                title="Make System Agent (Protect from deletion)">
                <i class="ri-shield-line text-gray-600 hover:text-amber-600 text-base"></i>
              </button>

              <button [routerLink]="[agent.agentName]"
                class="action-button w-8 h-8 rounded-md  bg-[var(--primary-purple)] transition-all duration-200 flex items-center justify-center border-none shadow-sm"
                title="Edit Agent" (click)="$event.stopPropagation()">
                <i class="ri-edit-line text-white text-base"></i>
              </button>

              <button (click)="deleteAgent(agent.agentName); $event.stopPropagation()"
                class="action-button w-8 h-8 rounded-md transition-all duration-200 flex items-center justify-center border-none shadow-sm"
                [class]="agent.isSystemAgent ? 'bg-gray-200 cursor-not-allowed' : 'bg-[#FEE2E2] hover:bg-[#FECACA]'"
                [title]="agent.isSystemAgent ? 'Cannot delete system agent' : 'Delete Agent'"
                [disabled]="agent.isSystemAgent">
                <i class="text-base"
                  [class]="agent.isSystemAgent ? 'ri-delete-bin-6-line text-gray-400' : 'ri-delete-bin-6-line text-red-500'"></i>
              </button>
            </div>

            <!-- Card Content -->
            <div class="p-4 flex-1">
              <div class="flex items-start gap-3">
                <!-- Agent Icon -->
                <div
                  class="w-10 h-10 rounded-full bg-[var(--primary-purple)] flex items-center justify-center flex-shrink-0">
                  <i class="ri-robot-line text-white"></i>
                </div>

                <!-- Agent Info -->
                <div class="flex-1 min-w-0">
                  <!-- Agent Name and Model -->
                  <div class="flex flex-col">
                    <h3 class="text-base font-medium text-[var(--text-dark)] truncate max-w-[180px]"
                      title="{{ agent.agentName }}">
                      {{ agent.agentName }}
                    </h3>
                    <div class="flex items-center gap-2">
                      <span class="status-badge !bg-[var(--primary-purple)] model">
                        <i class="ri-ai-generate mr-1.5"></i>
                        <span>{{ agent.modelName | removeProviderPrefix }}</span>
                      </span>
                    </div>
                  </div>

                  <!-- Tools -->
                  <div class="mt-3">
                    <p class="text-xs text-[var(--text-medium-gray)] mb-1">Tools</p>
                    <div class="flex flex-wrap gap-1">
                      <span *ngFor="let tool of agent.tools"
                        class="status-badge  !bg-[var(--secondary-purple)] plugin max-w-[120px] overflow-hidden"
                        title="{{ tool }}">
                        <i class="ri-close-line mr-1.5 !text-black dark:!text-white"></i>
                        <span class="truncate text-black dark:text-white">{{ tool }}</span>
                      </span>
                      <span *ngIf="agent.tools.length === 0" class="text-xs text-[var(--text-medium-gray)]">
                        No tools associated
                      </span>
                    </div>
                  </div>

                  <!-- Instructions -->
                  <div class="mt-3">
                    <p class="text-xs text-[var(--text-medium-gray)] mb-1">Instructions</p>
                    <p class="text-xs text-[var(--text-dark)] line-clamp-2 break-words">
                      {{ agent.instructions }}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Card Footer -->
            <div
              class="px-4 py-3 border-t border-[var(--hover-blue-gray)] border-opacity-30 flex justify-between items-center">
              <div class="flex items-center gap-1">
                <span class="status-badge  !bg-[var(--primary-purple)] model text-xs">
                  <i class="ri-ai-generate mr-1.5"></i>
                  <span>{{ agent.modelName | removeProviderPrefix }}</span>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination Controls -->
        <div
          class="pagination-container flex flex-col sm:flex-row justify-between items-center mt-6 px-4 py-3 bg-[var(--background-white)] rounded-md shadow-sm border border-[var(--hover-blue-gray)]"
          *ngIf="!isLoading && filteredAgents.length > pageSize">
          <div class="text-sm text-[var(--text-medium-gray)] mb-4 sm:mb-0 flex items-center">
            <span>Showing</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">{{ ((currentPage - 1) * pageSize) + 1 }}</span>
            <span>to</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">{{ Math.min(currentPage * pageSize,
              filteredAgents.length) }}</span>
            <span>of</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">{{ filteredAgents.length }}</span>
            <span>agents</span>
          </div>

          <div class="flex items-center">
            <div class="hidden sm:flex items-center mr-6 space-x-2">
              <span class="text-sm text-[var(--text-medium-gray)]">Rows per page:</span>
              <div class="relative">
                <select [(ngModel)]="pageSize" (ngModelChange)="updatePagination()"
                  class="appearance-none h-8 bg-[var(--background-white)] border border-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md text-sm px-3 pr-8 py-1 text-center focus:outline-none focus:ring-1 focus:ring-[var(--primary-purple)]">
                  <option [ngValue]="6" class="text-center">6</option>
                  <option [ngValue]="9" class="text-center">9</option>
                  <option [ngValue]="12" class="text-center">12</option>
                  <option [ngValue]="15" class="text-center">15</option>
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-2 pointer-events-none">
                  <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)]"></i>
                </div>
              </div>
            </div>

            <div class="flex items-center space-x-1">
              <button (click)="goToPage(1)" [disabled]="currentPage === 1"
                class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
                aria-label="First page">
                <i class="ri-skip-back-mini-line"></i>
              </button>

              <button (click)="previousPage()" [disabled]="currentPage === 1"
                class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
                aria-label="Previous page">
                <i class="ri-arrow-left-s-line"></i>
              </button>

              <div class="flex items-center space-x-1">
                <button *ngIf="currentPage > 2" (click)="goToPage(1)"
                  class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                  1
                </button>

                <span *ngIf="currentPage > 3"
                  class="w-8 h-8 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>

                <button *ngIf="currentPage > 1" (click)="goToPage(currentPage - 1)"
                  class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                  {{ currentPage - 1 }}
                </button>

                <button
                  class="w-8 h-8 flex items-center justify-center rounded-md bg-[var(--primary-purple)] text-white font-medium border-none">
                  {{ currentPage }}
                </button>

                <button *ngIf="currentPage < totalPages" (click)="goToPage(currentPage + 1)"
                  class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                  {{ currentPage + 1 }}
                </button>

                <span *ngIf="currentPage < totalPages - 2"
                  class="w-8 h-8 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>

                <button *ngIf="currentPage < totalPages - 1" (click)="goToPage(totalPages)"
                  class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                  {{ totalPages }}
                </button>
              </div>

              <button (click)="nextPage()" [disabled]="currentPage === totalPages"
                class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
                aria-label="Next page">
                <i class="ri-arrow-right-s-line"></i>
              </button>

              <button (click)="goToPage(totalPages)" [disabled]="currentPage === totalPages"
                class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
                aria-label="Last page">
                <i class="ri-skip-forward-mini-line"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="!isLoading && agents.length === 0 && !showForm"
          class="flex flex-col items-center justify-center py-16 px-4">
          <div
            class="w-16 h-16 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center mb-4 border border-[var(--primary-purple)] border-opacity-20">
            <i class="ri-robot-line text-3xl text-[var(--primary-purple)]"></i>
          </div>
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-2">No AI Agents Yet</h3>
          <p class="text-[var(--text-medium-gray)] text-center max-w-md mb-6">
            Create your first AI agent to start automating tasks and enhancing your workflow.
          </p>
          <button [routerLink]="['new']"
            class="px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center gap-2">
            <i class="ri-add-line"></i>
            <span>Create Your First Agent</span>
          </button>
        </div>

        <!-- No Search Results -->
        <div *ngIf="!isLoading && agents.length > 0 && filteredAgents.length === 0"
          class="flex flex-col items-center justify-center py-16 px-4">
          <div
            class="w-16 h-16 rounded-full bg-[var(--hover-blue-gray)] flex items-center justify-center mb-4 border border-[var(--primary-purple)] border-opacity-20">
            <i class="ri-search-line text-3xl text-[var(--text-medium-gray)]"></i>
          </div>
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-2">No Results Found</h3>
          <p class="text-[var(--text-medium-gray)] text-center max-w-md mb-3">
            We couldn't find any agents matching your search criteria. Try adjusting your search terms or clear the
            search.
          </p>
          <button (click)="searchQuery = ''; filterAgents()"
            class="px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] border-none rounded-md hover:bg-opacity-80 transition-all duration-200 flex items-center gap-2">
            <i class="ri-refresh-line"></i>
            <span>Reset Search</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>