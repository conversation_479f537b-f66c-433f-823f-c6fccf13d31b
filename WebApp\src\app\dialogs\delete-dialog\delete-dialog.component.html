<div class="fixed inset-0 flex items-center bg-black/20 justify-center">
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-sm w-full p-6 text-center" (click)="$event.stopPropagation()">
    <!-- Icon -->
    <div class="flex items-center justify-center w-12 h-12 mx-auto rounded-full bg-red-100 dark:bg-red-900/50">
      <svg class="w-6 h-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
        stroke="currentColor" stroke-width="2">
        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </div>

    <!-- Title -->
    <h2 class="mt-4 text-xl font-semibold text-gray-800 dark:text-white">
      <!-- Are you sure? --> {{title}}
    </h2>

    <!-- Description -->
    <p class="mt-2 text-sm text-[#6c757d] dark:text-gray-400">
      {{message}} This process cannot be undone.
    </p>

    <!-- Buttons -->
    <div class="mt-6 flex justify-between gap-4">
      <button class="w-full py-2 px-4 text-sm font-semibold text-gray-800 dark:text-white bg-gray-200 dark:bg-gray-700 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600"
        (click)="cancelDelete()">
        Cancel
      </button>
      <button class="w-full py-2 px-4 text-sm font-semibold text-white bg-[#dc3545] rounded-lg hover:bg-[#b71c1c]"
        (click)="confirmDelete()">
        Delete
      </button>
    </div>
  </div>
</div>
