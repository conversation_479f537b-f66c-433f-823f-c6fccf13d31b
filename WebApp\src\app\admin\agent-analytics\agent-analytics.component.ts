import { Compo<PERSON>, OnInit, OnD<PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil, forkJoin } from 'rxjs';
import { DateTime } from 'luxon';

// Angular Material/ng-zorro imports
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';

// Services and models
import { ThemeService } from '../../../shared/services/theam.service';
import { AgentAnalyticsServiceProxy, AgentAnalytics, AgentAnalyticsSummary } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';

@Component({
  selector: 'app-agent-analytics',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzCardModule,
    NzStatisticModule,
    NzProgressModule,
    NzSelectModule,
    NzButtonModule,
    NzIconModule,
    NzTableModule,
    NzTagModule,
    NzSpinModule,
    NzDividerModule,
    NzAlertModule,
    NzDatePickerModule,
    ServiceProxyModule
  ],
  templateUrl: './agent-analytics.component.html',
  styleUrls: ['./agent-analytics.component.css']
})
export class AgentAnalyticsComponent implements OnInit, OnDestroy {
  // Injected services
  private themeService = inject(ThemeService);
  private router = inject(Router);
  private agentAnalyticsService = inject(AgentAnalyticsServiceProxy);
  private destroy$ = new Subject<void>();

  // Component state
  loading = false;
  hasError = false;
  summary: AgentAnalyticsSummary | null = null;
  agents: AgentAnalytics[] = [];

  // UI state
  activeTab: 'overview' | 'performance' = 'overview';

  ngOnInit(): void {
    this.loadData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load all analytics data
   */
  loadData(): void {
    this.loading = true;
    this.hasError = false;

    forkJoin({
      summary: this.agentAnalyticsService.summary(),
      agents: this.agentAnalyticsService.getAll()
    }).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (data) => {
        this.summary = data.summary;
        this.agents = data.agents;
        this.loading = false;
        this.hasError = false;
      },
      error: (error) => {
        console.error('Error loading analytics data:', error);
        this.loading = false;
        this.hasError = true;
      }
    });
  }

  /**
   * Handle tab change
   */
  onTabChange(tab: 'overview' | 'performance'): void {
    this.activeTab = tab;
  }

  /**
   * Refresh data
   */
  refreshData(): void {
    this.loadData();
  }

  /**
   * Get status color for agent
   */
  getStatusColor(status: string): string {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'red';
      case 'maintenance': return 'orange';
      default: return 'default';
    }
  }

  /**
   * Get performance color based on success rate
   */
  getPerformanceColor(successRate: number): string {
    if (successRate >= 95) return '#52c41a'; // Green
    if (successRate >= 85) return '#faad14'; // Orange
    return '#ff4d4f'; // Red
  }

  /**
   * Format duration for display
   */
  formatDuration(seconds: number): string {
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(1)}s`;
  }

  /**
   * Get performance text color based on success rate
   */
  getPerformanceTextColor(successRate: number): string {
    if (successRate >= 95) return 'text-green-600';
    if (successRate >= 85) return 'text-yellow-600';
    return 'text-red-600';
  }

  /**
   * Get relative time from DateTime
   */
  getRelativeTime(dateTime: DateTime): string {
    if (!dateTime || !dateTime.isValid) {
      return 'Unknown';
    }
    return dateTime.toRelative() || 'Unknown';
  }



  /**
   * Check if theme is dark mode
   */
  get isDarkMode(): boolean {
    return this.themeService.isDarkMode();
  }

  /**
   * Get theme-appropriate card class
   */
  get cardClass(): string {
    return this.isDarkMode
      ? 'bg-[var(--background-white)] border-[var(--hover-blue-gray)]'
      : 'bg-white border-gray-200';
  }

  /**
   * Get theme-appropriate text class
   */
  get textClass(): string {
    return this.isDarkMode ? 'text-[var(--text-dark)]' : 'text-gray-900';
  }

  /**
   * Get theme-appropriate secondary text class
   */
  get secondaryTextClass(): string {
    return this.isDarkMode ? 'text-[var(--text-medium-gray)]' : 'text-gray-600';
  }


}
