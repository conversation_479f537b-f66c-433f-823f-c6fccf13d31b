import { Component, Inject } from '@angular/core';
import { Router } from '@angular/router';
import {
  ModelDetailsServiceProxy,
  AgentDefinitionServiceProxy,
  AiServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSelectModule, NzSelectSizeType } from 'ng-zorro-antd/select';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-add-or-edit-agent-dialog',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    NzInputModule,
    NzIconModule,
    NzAutocompleteModule,
    NzIconModule,
    NzSelectModule,
    NzRadioModule,
    NzBreadCrumbModule,
    ServiceProxyModule,
  ],
  templateUrl: './add-or-edit-agent-dialog.component.html',
  styleUrl: './add-or-edit-agent-dialog.component.css',
})
export class AddOrEditAgentDialogComponent {
  agents: any[] = [];
  showForm: boolean = false;
  isEditing: boolean = false;
  currentAgent: any = {};
  modelSearchQuery: string = '';
  filteredModels: any[] = [];
  models: any = [];
  workspaceName: string = '';
  plugins: any[] = [];
  selectedPlugins: string[] = [];
  constructor(
    private modelDetailsService: ModelDetailsServiceProxy, // Inject your service here
    private agentService: AgentDefinitionServiceProxy,
    private router: Router,
    private aiService: AiServiceProxy,
    private modalRef: NzModalRef,

    @Inject(NZ_MODAL_DATA)
    public data: { isEditing: boolean; agent: any }
  ) { }
  ngOnInit(): void {
    // Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    // Add 'implements OnInit' to the class.
    let router = this.router.url.split('/');
    this.workspaceName = router[2];

    // If the URL path doesn't include a workspace or contains 'ai-settings', 
    // we're adding from the global settings
    if (!this.workspaceName || this.router.url.includes('ai-settings')) {
      this.workspaceName = "Default";
    }

    this.loadModels();
    this.loadPlugins();

    if (this.data.isEditing) {
      this.currentAgent = this.data.agent;
      this.modelSearchQuery = this.currentAgent.modelName;
      this.selectedPlugins = [...this.currentAgent.tools];
    } else {
      this.currentAgent = {
        name: '',
        description: '',
        modelName: '',
        tools: [],
        workspace: this.workspaceName,
        isSystemAgent: false
      };
    }
    console.log("Agent workspace set to:", this.workspaceName);
  }
  loadModels() {
    // Fetch models from the service and assign to filteredModels
    this.modelDetailsService.getAllActiveModel().subscribe((models: any) => {
      this.filteredModels = models;
      this.models = models;
    });
  }
  loadPlugins() {
    this.aiService.getPluginClassNames().subscribe((res: any) => {
      // this.plugins = res.message;
      // console.log(this.plugins);
      this.plugins = [...res.message]
    });
  }

  onChange(event: any) {
    const query = event.target.value.toLowerCase();
    // Filter local dummy models by query
    this.filteredModels = this.models.filter((m: any) =>
      m.modelName.toLowerCase().includes(query)
    );
  }

  updateModel(modelName: string) {
    this.currentAgent.modelName = modelName;
  }
  saveAgent() {
    this.currentAgent.tools = this.selectedPlugins;
    console.log(this.currentAgent);
    this.agentService
      .createOrUpdate(this.currentAgent)
      .subscribe((res: any) => {
        console.log(res);
        this.modalRef.close({ ...res });
      });
  }
  updateAgent() { }

  closeDialog() {
    this.modalRef.close();
  }
}
