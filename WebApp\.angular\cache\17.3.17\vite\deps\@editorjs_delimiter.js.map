{"version": 3, "sources": ["../../../../../node_modules/@editorjs/delimiter/dist/delimiter.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var e=document.createElement(\"style\");e.appendChild(document.createTextNode('.ce-delimiter{line-height:1.6em;width:100%;text-align:center}.ce-delimiter:before{display:inline-block;content:\"***\";font-size:30px;line-height:65px;height:30px;letter-spacing:.2em}')),document.head.appendChild(e)}}catch(t){console.error(\"vite-plugin-css-injected-by-js\",t)}})();\nconst r = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><line x1=\"6\" x2=\"10\" y1=\"12\" y2=\"12\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><line x1=\"14\" x2=\"18\" y1=\"12\" y2=\"12\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/></svg>';\n/**\n * Delimiter Block for the Editor.js.\n *\n * <AUTHOR> (<EMAIL>)\n * @copyright CodeX 2018\n * @license The MIT License (MIT)\n * @version 2.0.0\n */\nclass n {\n  /**\n   * Notify core that read-only mode is supported\n   * @return {boolean}\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Allow Tool to have no content\n   * @return {boolean}\n   */\n  static get contentless() {\n    return !0;\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   *\n   * @param {{data: DelimiterData, config: object, api: object}}\n   *   data — previously saved data\n   *   config - user config for Tool\n   *   api - Editor.js API\n   */\n  constructor({ data: t, config: s, api: e }) {\n    this.api = e, this._CSS = {\n      block: this.api.styles.block,\n      wrapper: \"ce-delimiter\"\n    }, this._element = this.drawView(), this.data = t;\n  }\n  /**\n   * Create Tool's view\n   * @return {HTMLDivElement}\n   * @private\n   */\n  drawView() {\n    let t = document.createElement(\"div\");\n    return t.classList.add(this._CSS.wrapper, this._CSS.block), t;\n  }\n  /**\n   * Return Tool's view\n   * @returns {HTMLDivElement}\n   * @public\n   */\n  render() {\n    return this._element;\n  }\n  /**\n   * Extract Tool's data from the view\n   * @param {HTMLDivElement} toolsContent - Paragraph tools rendered view\n   * @returns {DelimiterData} - saved data\n   * @public\n   */\n  save(t) {\n    return {};\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   *\n   * @return {{icon: string, title: string}}\n   */\n  static get toolbox() {\n    return {\n      icon: r,\n      title: \"Delimiter\"\n    };\n  }\n  /**\n   * Delimiter onPaste configuration\n   *\n   * @public\n   */\n  static get pasteConfig() {\n    return { tags: [\"HR\"] };\n  }\n  /**\n   * On paste callback that is fired from Editor\n   *\n   * @param {PasteEvent} event - event with pasted data\n   */\n  onPaste(t) {\n    this.data = {};\n  }\n}\nexport {\n  n as default\n};\n"], "mappings": ";;;CAAC,WAAU;AAAC;AAAa,MAAG;AAAC,QAAG,OAAO,WAAS,KAAI;AAAC,UAAI,IAAE,SAAS,cAAc,OAAO;AAAE,QAAE,YAAY,SAAS,eAAe,uLAAuL,CAAC,GAAE,SAAS,KAAK,YAAY,CAAC;AAAA,IAAC;AAAA,EAAC,SAAO,GAAE;AAAC,YAAQ,MAAM,kCAAiC,CAAC;AAAA,EAAC;AAAC,GAAG;AACvZ,IAAM,IAAI;AASV,IAAM,IAAN,MAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKN,WAAW,sBAAsB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,cAAc;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,EAAE,GAAG;AAC1C,SAAK,MAAM,GAAG,KAAK,OAAO;AAAA,MACxB,OAAO,KAAK,IAAI,OAAO;AAAA,MACvB,SAAS;AAAA,IACX,GAAG,KAAK,WAAW,KAAK,SAAS,GAAG,KAAK,OAAO;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,QAAI,IAAI,SAAS,cAAc,KAAK;AACpC,WAAO,EAAE,UAAU,IAAI,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK,GAAG;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,GAAG;AACN,WAAO,CAAC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,UAAU;AACnB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,cAAc;AACvB,WAAO,EAAE,MAAM,CAAC,IAAI,EAAE;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,GAAG;AACT,SAAK,OAAO,CAAC;AAAA,EACf;AACF;", "names": []}