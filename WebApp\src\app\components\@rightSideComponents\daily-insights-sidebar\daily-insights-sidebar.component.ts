import { Component, inject, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TogglingService } from '../../../toggling.service';
import { ChatListService } from '../../../services/chat-list.service';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-daily-insights-sidebar',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './daily-insights-sidebar.component.html',
  styleUrls: ['./daily-insights-sidebar.component.css']
})
export class DailyInsightsSidebarComponent {
  // Inject services
  router = inject(Router);
  togglingService = inject(TogglingService);
  chatListService = inject(ChatListService);
  nzMessageService = inject(NzMessageService);

  // Input properties
  @Input() workspaceName: string = '';

  // Properties for date/time display
  lastClickedItem: string = '';
  lastClickedTime: string = '';

  // Track the current active quick action
  activeQuickAction: string = '';

  /**
   * Update last clicked item and time
   */
  private updateLastClicked(itemName: string) {
    this.lastClickedItem = itemName;
    this.lastClickedTime = new Date().toLocaleString();
  }

  /**
   * Check if a quick action is active based on the current route
   */
  isQuickActionActive(action: string): boolean {
    const url = this.router.url;
    switch (action) {
      case 'new-chat':
        return url.startsWith('/daily-insight/new-chat');
      case 'workspaces':
        return url.startsWith('/daily-insight/workspaces');
      case 'project-summary':
        return url.startsWith('/daily-insight/project-summary');
      case 'task-management':
        return url.startsWith('/daily-insight/task-management');
      default:
        return false;
    }
  }

  /**
   * Navigate to chat page
   */
  goToChat() {
    this.updateLastClicked('New Chat');
    this.chatListService.chatId = 0;
    this.activeQuickAction = 'new-chat';
    this.router.navigate(['/daily-insight/new-chat']);
  }

  /**
   * Navigate to workspaces page
   */
  goToWorkspace() {
    this.updateLastClicked('Workspaces');
    this.chatListService.chatId = 0;
    this.activeQuickAction = 'workspaces';
    this.router.navigate(['/daily-insight/workspaces']);
  }

  /**
   * Navigate to project summary page
   */
  goToProjectSummary() {
    this.updateLastClicked('Project Summary');
    this.chatListService.chatId = 0;
    this.activeQuickAction = 'project-summary';
    this.router.navigate(['/daily-insight/project-summary']);
  }

  /**
   * Navigate to task management page
   */
  goToTaskManagement() {
    this.updateLastClicked('Task Management');
    this.chatListService.chatId = 0;
    this.activeQuickAction = 'task-management';
    this.router.navigate(['/daily-insight/task-management']);
  }

  /**
   * Refresh daily insights data
   */
  // refreshDailyInsights() {
  //   // Show loading indicator
  //   this.nzMessageService.loading('Refreshing daily insights...');

  //   // Show success message after a short delay (simulating refresh)
  //   // setTimeout(() => {
  //   //   this.nzMessageService.success('Daily insights refreshed');
  //   // }, 1000);
  // }
}
