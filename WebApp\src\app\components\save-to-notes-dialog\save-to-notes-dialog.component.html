<div *ngIf="visible"
  class="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-sm"
  (click)="onBackdropClick($event)">
  <div
    class="bg-white dark:bg-gray-800 rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden border border-gray-200 dark:border-gray-600 m-4"
    (click)="$event.stopPropagation()">
    <!-- Header -->
    <div
      class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
      <div class="flex items-center gap-3">
        <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
          <i class="ri-save-line text-white text-xl"></i>
        </div>
        <div>
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Save to My Notes</h2>
          <p class="text-gray-600 dark:text-gray-300 text-sm">Convert your AI conversation into a formatted note</p>
        </div>
      </div>
      <button (click)="close()"
        class="text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg p-2 transition-colors"
        title="Close dialog">
        <i class="ri-close-line text-xl"></i>
      </button>
    </div>

    <!-- Content -->
    <div class="p-4 overflow-y-auto max-h-[calc(90vh-214px)] bg-white dark:bg-gray-800">
      <!-- Title Input -->
      <div class="mb-4">
        <label for="noteTitle"
          class="block text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-2">
          <i class="ri-edit-line text-blue-500"></i>
          Note Title <span class="text-red-500">*</span>
        </label>
        <input id="noteTitle" type="text" [(ngModel)]="title" placeholder="Enter a title for your note..."
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all placeholder-gray-500"
          [disabled]="isSaving">
      </div>

      <!-- Content Editor -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-2">
          <i class="ri-quill-pen-line text-blue-500"></i>
          Note Content
        </label>
        <div class="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden bg-white dark:bg-gray-700">
          <div id="notes-editor" class="min-h-[300px] bg-white dark:bg-gray-700">
            <!-- Editor will be initialized here -->
          </div>
        </div>
        <div class="flex items-center justify-between mt-2">
          <p class="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-2">
            <i class="ri-information-line"></i>
            Rich text editor with enhanced formatting capabilities
          </p>
          <span
            class="px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300 rounded text-xs font-medium border border-gray-200 dark:border-gray-500">
            EditorJS
          </span>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div
      class="flex items-center justify-end gap-3 p-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
      <button (click)="close()" [disabled]="isSaving"
        class="px-6 py-2.5 text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-500 transition-all duration-200 font-medium disabled:opacity-50 flex items-center gap-2">
        <i class="ri-close-line"></i>
        Cancel
      </button>
      <button (click)="saveNote()" [disabled]="isSaving || !title.trim()"
        class="px-6 py-2.5 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-all duration-200 disabled:opacity-50 flex items-center gap-2 font-medium shadow-sm">
        <div *ngIf="isSaving" class="flex gap-1">
          <span class="w-1.5 h-1.5 bg-white rounded-full animate-bounce [animation-delay:-0.3s]"></span>
          <span class="w-1.5 h-1.5 bg-white rounded-full animate-bounce [animation-delay:-0.15s]"></span>
          <span class="w-1.5 h-1.5 bg-white rounded-full animate-bounce"></span>
        </div>
        <i *ngIf="!isSaving" class="ri-save-line"></i>
        <span>{{ isSaving ? 'Saving...' : 'Save to Notes' }}</span>
      </button>
    </div>
  </div>
</div>