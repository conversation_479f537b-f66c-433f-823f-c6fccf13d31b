{"version": 3, "sources": ["../../../../../node_modules/@editorjs/table/dist/table.mjs"], "sourcesContent": ["(function(){var r;\"use strict\";try{if(typeof document<\"u\"){var o=document.createElement(\"style\");o.nonce=(r=document.head.querySelector(\"meta[property=csp-nonce]\"))==null?void 0:r.content,o.appendChild(document.createTextNode('.tc-wrap{--color-background:#f9f9fb;--color-text-secondary:#7b7e89;--color-border:#e8e8eb;--cell-size:34px;--toolbox-icon-size:18px;--toolbox-padding:6px;--toolbox-aiming-field-size:calc(var(--toolbox-icon-size) + var(--toolbox-padding)*2);border-left:0;position:relative;height:100%;width:100%;margin-top:var(--toolbox-icon-size);box-sizing:border-box;display:grid;grid-template-columns:calc(100% - var(--cell-size)) var(--cell-size);z-index:0}.tc-wrap--readonly{grid-template-columns:100% var(--cell-size)}.tc-wrap svg{vertical-align:top}@media print{.tc-wrap{border-left-color:var(--color-border);border-left-style:solid;border-left-width:1px;grid-template-columns:100% var(--cell-size)}}@media print{.tc-wrap .tc-row:after{display:none}}.tc-table{position:relative;width:100%;height:100%;display:grid;font-size:14px;border-top:1px solid var(--color-border);line-height:1.4}.tc-table:after{width:calc(var(--cell-size));height:100%;left:calc(var(--cell-size)*-1);top:0}.tc-table:after,.tc-table:before{position:absolute;content:\"\"}.tc-table:before{width:100%;height:var(--toolbox-aiming-field-size);top:calc(var(--toolbox-aiming-field-size)*-1);left:0}.tc-table--heading .tc-row:first-child{font-weight:600;border-bottom:2px solid var(--color-border);position:sticky;top:0;z-index:2;background:var(--color-background)}.tc-table--heading .tc-row:first-child [contenteditable]:empty:before{content:attr(heading);color:var(--color-text-secondary)}.tc-table--heading .tc-row:first-child:after{bottom:-2px;border-bottom:2px solid var(--color-border)}.tc-add-column,.tc-add-row{display:flex;color:var(--color-text-secondary)}@media print{.tc-add{display:none}}.tc-add-column{display:grid;border-top:1px solid var(--color-border);grid-template-columns:var(--cell-size);grid-auto-rows:var(--cell-size);place-items:center}.tc-add-column svg{padding:5px;position:sticky;top:0;background-color:var(--color-background)}.tc-add-column--disabled{visibility:hidden}@media print{.tc-add-column{display:none}}.tc-add-row{height:var(--cell-size);align-items:center;padding-left:4px;position:relative}.tc-add-row--disabled{display:none}.tc-add-row:before{content:\"\";position:absolute;right:calc(var(--cell-size)*-1);width:var(--cell-size);height:100%}@media print{.tc-add-row{display:none}}.tc-add-column,.tc-add-row{transition:0s;cursor:pointer;will-change:background-color}.tc-add-column:hover,.tc-add-row:hover{transition:background-color .1s ease;background-color:var(--color-background)}.tc-add-row{margin-top:1px}.tc-add-row:hover:before{transition:.1s;background-color:var(--color-background)}.tc-row{display:grid;grid-template-columns:repeat(auto-fit,minmax(10px,1fr));position:relative;border-bottom:1px solid var(--color-border)}.tc-row:after{content:\"\";pointer-events:none;position:absolute;width:var(--cell-size);height:100%;bottom:-1px;right:calc(var(--cell-size)*-1);border-bottom:1px solid var(--color-border)}.tc-row--selected{background:var(--color-background)}.tc-row--selected:after{background:var(--color-background)}.tc-cell{border-right:1px solid var(--color-border);padding:6px 12px;overflow:hidden;outline:none;line-break:normal}.tc-cell--selected{background:var(--color-background)}.tc-wrap--readonly .tc-row:after{display:none}.tc-toolbox{--toolbox-padding:6px;--popover-margin:30px;--toggler-click-zone-size:30px;--toggler-dots-color:#7b7e89;--toggler-dots-color-hovered:#1d202b;position:absolute;cursor:pointer;z-index:1;opacity:0;transition:opacity .1s;will-change:left,opacity}.tc-toolbox--column{top:calc(var(--toggler-click-zone-size)*-1);transform:translate(calc(var(--toggler-click-zone-size)*-1/2));will-change:left,opacity}.tc-toolbox--row{left:calc(var(--popover-margin)*-1);transform:translateY(calc(var(--toggler-click-zone-size)*-1/2));margin-top:-1px;will-change:top,opacity}.tc-toolbox--showed{opacity:1}.tc-toolbox .tc-popover{position:absolute;top:0;left:var(--popover-margin)}.tc-toolbox__toggler{display:flex;align-items:center;justify-content:center;width:var(--toggler-click-zone-size);height:var(--toggler-click-zone-size);color:var(--toggler-dots-color);opacity:0;transition:opacity .15s ease;will-change:opacity}.tc-toolbox__toggler:hover{color:var(--toggler-dots-color-hovered)}.tc-toolbox__toggler svg{fill:currentColor}.tc-wrap:hover .tc-toolbox__toggler{opacity:1}.tc-settings .cdx-settings-button{width:50%;margin:0}.tc-popover{--color-border:#eaeaea;--color-background:#fff;--color-background-hover:rgba(232,232,235,.49);--color-background-confirm:#e24a4a;--color-background-confirm-hover:#d54040;--color-text-confirm:#fff;background:var(--color-background);border:1px solid var(--color-border);box-shadow:0 3px 15px -3px #0d142121;border-radius:6px;padding:6px;display:none;will-change:opacity,transform}.tc-popover--opened{display:block;animation:menuShowing .1s cubic-bezier(.215,.61,.355,1) forwards}.tc-popover__item{display:flex;align-items:center;padding:2px 14px 2px 2px;border-radius:5px;cursor:pointer;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;user-select:none}.tc-popover__item:hover{background:var(--color-background-hover)}.tc-popover__item:not(:last-of-type){margin-bottom:2px}.tc-popover__item-icon{display:inline-flex;width:26px;height:26px;align-items:center;justify-content:center;background:var(--color-background);border-radius:5px;border:1px solid var(--color-border);margin-right:8px}.tc-popover__item-label{line-height:22px;font-size:14px;font-weight:500}.tc-popover__item--confirm{background:var(--color-background-confirm);color:var(--color-text-confirm)}.tc-popover__item--confirm:hover{background-color:var(--color-background-confirm-hover)}.tc-popover__item--confirm .tc-popover__item-icon{background:var(--color-background-confirm);border-color:#0000001a}.tc-popover__item--confirm .tc-popover__item-icon svg{transition:transform .2s ease-in;transform:rotate(90deg) scale(1.2)}.tc-popover__item--hidden{display:none}@keyframes menuShowing{0%{opacity:0;transform:translateY(-8px) scale(.9)}70%{opacity:1;transform:translateY(2px)}to{transform:translateY(0)}}')),document.head.appendChild(o)}}catch(e){console.error(\"vite-plugin-css-injected-by-js\",e)}})();\nfunction c(d, t, e = {}) {\n  const o = document.createElement(d);\n  Array.isArray(t) ? o.classList.add(...t) : t && o.classList.add(t);\n  for (const i in e)\n    Object.prototype.hasOwnProperty.call(e, i) && (o[i] = e[i]);\n  return o;\n}\nfunction f(d) {\n  const t = d.getBoundingClientRect();\n  return {\n    y1: Math.floor(t.top + window.pageYOffset),\n    x1: Math.floor(t.left + window.pageXOffset),\n    x2: Math.floor(t.right + window.pageXOffset),\n    y2: Math.floor(t.bottom + window.pageYOffset)\n  };\n}\nfunction g(d, t) {\n  const e = f(d), o = f(t);\n  return {\n    fromTopBorder: o.y1 - e.y1,\n    fromLeftBorder: o.x1 - e.x1,\n    fromRightBorder: e.x2 - o.x2,\n    fromBottomBorder: e.y2 - o.y2\n  };\n}\nfunction k(d, t) {\n  const e = d.getBoundingClientRect(), { width: o, height: i, x: n, y: r } = e, { clientX: h, clientY: l } = t;\n  return {\n    width: o,\n    height: i,\n    x: h - n,\n    y: l - r\n  };\n}\nfunction m(d, t) {\n  return t.parentNode.insertBefore(d, t);\n}\nfunction C(d, t = !0) {\n  const e = document.createRange(), o = window.getSelection();\n  e.selectNodeContents(d), e.collapse(t), o.removeAllRanges(), o.addRange(e);\n}\nclass a {\n  /**\n   * @param {object} options - constructor options\n   * @param {PopoverItem[]} options.items - constructor options\n   */\n  constructor({ items: t }) {\n    this.items = t, this.wrapper = void 0, this.itemEls = [];\n  }\n  /**\n   * Set of CSS classnames used in popover\n   *\n   * @returns {object}\n   */\n  static get CSS() {\n    return {\n      popover: \"tc-popover\",\n      popoverOpened: \"tc-popover--opened\",\n      item: \"tc-popover__item\",\n      itemHidden: \"tc-popover__item--hidden\",\n      itemConfirmState: \"tc-popover__item--confirm\",\n      itemIcon: \"tc-popover__item-icon\",\n      itemLabel: \"tc-popover__item-label\"\n    };\n  }\n  /**\n   * Returns the popover element\n   *\n   * @returns {Element}\n   */\n  render() {\n    return this.wrapper = c(\"div\", a.CSS.popover), this.items.forEach((t, e) => {\n      const o = c(\"div\", a.CSS.item), i = c(\"div\", a.CSS.itemIcon, {\n        innerHTML: t.icon\n      }), n = c(\"div\", a.CSS.itemLabel, {\n        textContent: t.label\n      });\n      o.dataset.index = e, o.appendChild(i), o.appendChild(n), this.wrapper.appendChild(o), this.itemEls.push(o);\n    }), this.wrapper.addEventListener(\"click\", (t) => {\n      this.popoverClicked(t);\n    }), this.wrapper;\n  }\n  /**\n   * Popover wrapper click listener\n   * Used to delegate clicks in items\n   *\n   * @returns {void}\n   */\n  popoverClicked(t) {\n    const e = t.target.closest(`.${a.CSS.item}`);\n    if (!e)\n      return;\n    const o = e.dataset.index, i = this.items[o];\n    if (i.confirmationRequired && !this.hasConfirmationState(e)) {\n      this.setConfirmationState(e);\n      return;\n    }\n    i.onClick();\n  }\n  /**\n   * Enable the confirmation state on passed item\n   *\n   * @returns {void}\n   */\n  setConfirmationState(t) {\n    t.classList.add(a.CSS.itemConfirmState);\n  }\n  /**\n   * Disable the confirmation state on passed item\n   *\n   * @returns {void}\n   */\n  clearConfirmationState(t) {\n    t.classList.remove(a.CSS.itemConfirmState);\n  }\n  /**\n   * Check if passed item has the confirmation state\n   *\n   * @returns {boolean}\n   */\n  hasConfirmationState(t) {\n    return t.classList.contains(a.CSS.itemConfirmState);\n  }\n  /**\n   * Return an opening state\n   *\n   * @returns {boolean}\n   */\n  get opened() {\n    return this.wrapper.classList.contains(a.CSS.popoverOpened);\n  }\n  /**\n   * Opens the popover\n   *\n   * @returns {void}\n   */\n  open() {\n    this.items.forEach((t, e) => {\n      typeof t.hideIf == \"function\" && this.itemEls[e].classList.toggle(a.CSS.itemHidden, t.hideIf());\n    }), this.wrapper.classList.add(a.CSS.popoverOpened);\n  }\n  /**\n   * Closes the popover\n   *\n   * @returns {void}\n   */\n  close() {\n    this.wrapper.classList.remove(a.CSS.popoverOpened), this.itemEls.forEach((t) => {\n      this.clearConfirmationState(t);\n    });\n  }\n}\nconst R = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 9L10 12M10 12L7 15M10 12H4\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 9L14 12M14 12L17 15M14 12H20\"/></svg>', b = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M8 8L12 12M12 12L16 16M12 12L16 8M12 12L8 16\"/></svg>', x = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.8833 9.16666L18.2167 12.5M18.2167 12.5L14.8833 15.8333M18.2167 12.5H10.05C9.16594 12.5 8.31809 12.1488 7.69297 11.5237C7.06785 10.8986 6.71666 10.0507 6.71666 9.16666\"/></svg>', S = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.9167 14.9167L11.5833 18.25M11.5833 18.25L8.25 14.9167M11.5833 18.25L11.5833 10.0833C11.5833 9.19928 11.9345 8.35143 12.5596 7.72631C13.1848 7.10119 14.0326 6.75 14.9167 6.75\"/></svg>', y = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.13333 14.9167L12.4667 18.25M12.4667 18.25L15.8 14.9167M12.4667 18.25L12.4667 10.0833C12.4667 9.19928 12.1155 8.35143 11.4904 7.72631C10.8652 7.10119 10.0174 6.75 9.13333 6.75\"/></svg>', L = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.8833 15.8333L18.2167 12.5M18.2167 12.5L14.8833 9.16667M18.2167 12.5L10.05 12.5C9.16595 12.5 8.31811 12.8512 7.69299 13.4763C7.06787 14.1014 6.71667 14.9493 6.71667 15.8333\"/></svg>', M = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2.6\" d=\"M9.41 9.66H9.4\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2.6\" d=\"M14.6 9.66H14.59\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2.6\" d=\"M9.31 14.36H9.3\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2.6\" d=\"M14.6 14.36H14.59\"/></svg>', v = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 7V12M12 17V12M17 12H12M12 12H7\"/></svg>', O = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 9L20 12L17 15\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14 12H20\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 9L4 12L7 15\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 12H10\"/></svg>', T = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M5 10H19\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>', H = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M10 5V18.5\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M14 5V18.5\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M5 10H19\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M5 14H19\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>', A = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M10 5V18.5\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M5 10H19\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>';\nclass w {\n  /**\n   * Creates toolbox buttons and toolbox menus\n   *\n   * @param {Object} config\n   * @param {any} config.api - Editor.js api\n   * @param {PopoverItem[]} config.items - Editor.js api\n   * @param {function} config.onOpen - callback fired when the Popover is opening\n   * @param {function} config.onClose - callback fired when the Popover is closing\n   * @param {string} config.cssModifier - the modifier for the Toolbox. Allows to add some specific styles.\n   */\n  constructor({ api: t, items: e, onOpen: o, onClose: i, cssModifier: n = \"\" }) {\n    this.api = t, this.items = e, this.onOpen = o, this.onClose = i, this.cssModifier = n, this.popover = null, this.wrapper = this.createToolbox();\n  }\n  /**\n   * Style classes\n   */\n  static get CSS() {\n    return {\n      toolbox: \"tc-toolbox\",\n      toolboxShowed: \"tc-toolbox--showed\",\n      toggler: \"tc-toolbox__toggler\"\n    };\n  }\n  /**\n   * Returns rendered Toolbox element\n   */\n  get element() {\n    return this.wrapper;\n  }\n  /**\n   * Creating a toolbox to open menu for a manipulating columns\n   *\n   * @returns {Element}\n   */\n  createToolbox() {\n    const t = c(\"div\", [\n      w.CSS.toolbox,\n      this.cssModifier ? `${w.CSS.toolbox}--${this.cssModifier}` : \"\"\n    ]);\n    t.dataset.mutationFree = \"true\";\n    const e = this.createPopover(), o = this.createToggler();\n    return t.appendChild(o), t.appendChild(e), t;\n  }\n  /**\n   * Creates the Toggler\n   *\n   * @returns {Element}\n   */\n  createToggler() {\n    const t = c(\"div\", w.CSS.toggler, {\n      innerHTML: M\n    });\n    return t.addEventListener(\"click\", () => {\n      this.togglerClicked();\n    }), t;\n  }\n  /**\n   * Creates the Popover instance and render it\n   *\n   * @returns {Element}\n   */\n  createPopover() {\n    return this.popover = new a({\n      items: this.items\n    }), this.popover.render();\n  }\n  /**\n   * Toggler click handler. Opens/Closes the popover\n   *\n   * @returns {void}\n   */\n  togglerClicked() {\n    this.popover.opened ? (this.popover.close(), this.onClose()) : (this.popover.open(), this.onOpen());\n  }\n  /**\n   * Shows the Toolbox\n   *\n   * @param {function} computePositionMethod - method that returns the position coordinate\n   * @returns {void}\n   */\n  show(t) {\n    const e = t();\n    Object.entries(e).forEach(([o, i]) => {\n      this.wrapper.style[o] = i;\n    }), this.wrapper.classList.add(w.CSS.toolboxShowed);\n  }\n  /**\n   * Hides the Toolbox\n   *\n   * @returns {void}\n   */\n  hide() {\n    this.popover.close(), this.wrapper.classList.remove(w.CSS.toolboxShowed);\n  }\n}\nfunction B(d, t) {\n  let e = 0;\n  return function(...o) {\n    const i = (/* @__PURE__ */ new Date()).getTime();\n    if (!(i - e < d))\n      return e = i, t(...o);\n  };\n}\nconst s = {\n  wrapper: \"tc-wrap\",\n  wrapperReadOnly: \"tc-wrap--readonly\",\n  table: \"tc-table\",\n  row: \"tc-row\",\n  withHeadings: \"tc-table--heading\",\n  rowSelected: \"tc-row--selected\",\n  cell: \"tc-cell\",\n  cellSelected: \"tc-cell--selected\",\n  addRow: \"tc-add-row\",\n  addRowDisabled: \"tc-add-row--disabled\",\n  addColumn: \"tc-add-column\",\n  addColumnDisabled: \"tc-add-column--disabled\"\n};\nclass E {\n  /**\n   * Creates\n   *\n   * @constructor\n   * @param {boolean} readOnly - read-only mode flag\n   * @param {object} api - Editor.js API\n   * @param {TableData} data - Editor.js API\n   * @param {TableConfig} config - Editor.js API\n   */\n  constructor(t, e, o, i) {\n    this.readOnly = t, this.api = e, this.data = o, this.config = i, this.wrapper = null, this.table = null, this.toolboxColumn = this.createColumnToolbox(), this.toolboxRow = this.createRowToolbox(), this.createTableWrapper(), this.hoveredRow = 0, this.hoveredColumn = 0, this.selectedRow = 0, this.selectedColumn = 0, this.tunes = {\n      withHeadings: !1\n    }, this.resize(), this.fill(), this.focusedCell = {\n      row: 0,\n      column: 0\n    }, this.documentClicked = (n) => {\n      const r = n.target.closest(`.${s.table}`) !== null, h = n.target.closest(`.${s.wrapper}`) === null;\n      (r || h) && this.hideToolboxes();\n      const u = n.target.closest(`.${s.addRow}`), p = n.target.closest(`.${s.addColumn}`);\n      u && u.parentNode === this.wrapper ? (this.addRow(void 0, !0), this.hideToolboxes()) : p && p.parentNode === this.wrapper && (this.addColumn(void 0, !0), this.hideToolboxes());\n    }, this.readOnly || this.bindEvents();\n  }\n  /**\n   * Returns the rendered table wrapper\n   *\n   * @returns {Element}\n   */\n  getWrapper() {\n    return this.wrapper;\n  }\n  /**\n   * Hangs the necessary handlers to events\n   */\n  bindEvents() {\n    document.addEventListener(\"click\", this.documentClicked), this.table.addEventListener(\"mousemove\", B(150, (t) => this.onMouseMoveInTable(t)), { passive: !0 }), this.table.onkeypress = (t) => this.onKeyPressListener(t), this.table.addEventListener(\"keydown\", (t) => this.onKeyDownListener(t)), this.table.addEventListener(\"focusin\", (t) => this.focusInTableListener(t));\n  }\n  /**\n   * Configures and creates the toolbox for manipulating with columns\n   *\n   * @returns {Toolbox}\n   */\n  createColumnToolbox() {\n    return new w({\n      api: this.api,\n      cssModifier: \"column\",\n      items: [\n        {\n          label: this.api.i18n.t(\"Add column to left\"),\n          icon: S,\n          hideIf: () => this.numberOfColumns === this.config.maxcols,\n          onClick: () => {\n            this.addColumn(this.selectedColumn, !0), this.hideToolboxes();\n          }\n        },\n        {\n          label: this.api.i18n.t(\"Add column to right\"),\n          icon: y,\n          hideIf: () => this.numberOfColumns === this.config.maxcols,\n          onClick: () => {\n            this.addColumn(this.selectedColumn + 1, !0), this.hideToolboxes();\n          }\n        },\n        {\n          label: this.api.i18n.t(\"Delete column\"),\n          icon: b,\n          hideIf: () => this.numberOfColumns === 1,\n          confirmationRequired: !0,\n          onClick: () => {\n            this.deleteColumn(this.selectedColumn), this.hideToolboxes();\n          }\n        }\n      ],\n      onOpen: () => {\n        this.selectColumn(this.hoveredColumn), this.hideRowToolbox();\n      },\n      onClose: () => {\n        this.unselectColumn();\n      }\n    });\n  }\n  /**\n   * Configures and creates the toolbox for manipulating with rows\n   *\n   * @returns {Toolbox}\n   */\n  createRowToolbox() {\n    return new w({\n      api: this.api,\n      cssModifier: \"row\",\n      items: [\n        {\n          label: this.api.i18n.t(\"Add row above\"),\n          icon: L,\n          hideIf: () => this.numberOfRows === this.config.maxrows,\n          onClick: () => {\n            this.addRow(this.selectedRow, !0), this.hideToolboxes();\n          }\n        },\n        {\n          label: this.api.i18n.t(\"Add row below\"),\n          icon: x,\n          hideIf: () => this.numberOfRows === this.config.maxrows,\n          onClick: () => {\n            this.addRow(this.selectedRow + 1, !0), this.hideToolboxes();\n          }\n        },\n        {\n          label: this.api.i18n.t(\"Delete row\"),\n          icon: b,\n          hideIf: () => this.numberOfRows === 1,\n          confirmationRequired: !0,\n          onClick: () => {\n            this.deleteRow(this.selectedRow), this.hideToolboxes();\n          }\n        }\n      ],\n      onOpen: () => {\n        this.selectRow(this.hoveredRow), this.hideColumnToolbox();\n      },\n      onClose: () => {\n        this.unselectRow();\n      }\n    });\n  }\n  /**\n   * When you press enter it moves the cursor down to the next row\n   * or creates it if the click occurred on the last one\n   */\n  moveCursorToNextRow() {\n    this.focusedCell.row !== this.numberOfRows ? (this.focusedCell.row += 1, this.focusCell(this.focusedCell)) : (this.addRow(), this.focusedCell.row += 1, this.focusCell(this.focusedCell), this.updateToolboxesPosition(0, 0));\n  }\n  /**\n   * Get table cell by row and col index\n   *\n   * @param {number} row - cell row coordinate\n   * @param {number} column - cell column coordinate\n   * @returns {HTMLElement}\n   */\n  getCell(t, e) {\n    return this.table.querySelectorAll(`.${s.row}:nth-child(${t}) .${s.cell}`)[e - 1];\n  }\n  /**\n   * Get table row by index\n   *\n   * @param {number} row - row coordinate\n   * @returns {HTMLElement}\n   */\n  getRow(t) {\n    return this.table.querySelector(`.${s.row}:nth-child(${t})`);\n  }\n  /**\n   * The parent of the cell which is the row\n   *\n   * @param {HTMLElement} cell - cell element\n   * @returns {HTMLElement}\n   */\n  getRowByCell(t) {\n    return t.parentElement;\n  }\n  /**\n   * Ger row's first cell\n   *\n   * @param {Element} row - row to find its first cell\n   * @returns {Element}\n   */\n  getRowFirstCell(t) {\n    return t.querySelector(`.${s.cell}:first-child`);\n  }\n  /**\n   * Set the sell's content by row and column numbers\n   *\n   * @param {number} row - cell row coordinate\n   * @param {number} column - cell column coordinate\n   * @param {string} content - cell HTML content\n   */\n  setCellContent(t, e, o) {\n    const i = this.getCell(t, e);\n    i.innerHTML = o;\n  }\n  /**\n   * Add column in table on index place\n   * Add cells in each row\n   *\n   * @param {number} columnIndex - number in the array of columns, where new column to insert, -1 if insert at the end\n   * @param {boolean} [setFocus] - pass true to focus the first cell\n   */\n  addColumn(t = -1, e = !1) {\n    var n;\n    let o = this.numberOfColumns;\n    if (this.config && this.config.maxcols && this.numberOfColumns >= this.config.maxcols)\n      return;\n    for (let r = 1; r <= this.numberOfRows; r++) {\n      let h;\n      const l = this.createCell();\n      if (t > 0 && t <= o ? (h = this.getCell(r, t), m(l, h)) : h = this.getRow(r).appendChild(l), r === 1) {\n        const u = this.getCell(r, t > 0 ? t : o + 1);\n        u && e && C(u);\n      }\n    }\n    const i = this.wrapper.querySelector(`.${s.addColumn}`);\n    (n = this.config) != null && n.maxcols && this.numberOfColumns > this.config.maxcols - 1 && i && i.classList.add(s.addColumnDisabled), this.addHeadingAttrToFirstRow();\n  }\n  /**\n   * Add row in table on index place\n   *\n   * @param {number} index - number in the array of rows, where new column to insert, -1 if insert at the end\n   * @param {boolean} [setFocus] - pass true to focus the inserted row\n   * @returns {HTMLElement} row\n   */\n  addRow(t = -1, e = !1) {\n    let o, i = c(\"div\", s.row);\n    this.tunes.withHeadings && this.removeHeadingAttrFromFirstRow();\n    let n = this.numberOfColumns;\n    if (this.config && this.config.maxrows && this.numberOfRows >= this.config.maxrows && h)\n      return;\n    if (t > 0 && t <= this.numberOfRows) {\n      let l = this.getRow(t);\n      o = m(i, l);\n    } else\n      o = this.table.appendChild(i);\n    this.fillRow(o, n), this.tunes.withHeadings && this.addHeadingAttrToFirstRow();\n    const r = this.getRowFirstCell(o);\n    r && e && C(r);\n    const h = this.wrapper.querySelector(`.${s.addRow}`);\n    return this.config && this.config.maxrows && this.numberOfRows >= this.config.maxrows && h && h.classList.add(s.addRowDisabled), o;\n  }\n  /**\n   * Delete a column by index\n   *\n   * @param {number} index\n   */\n  deleteColumn(t) {\n    for (let o = 1; o <= this.numberOfRows; o++) {\n      const i = this.getCell(o, t);\n      if (!i)\n        return;\n      i.remove();\n    }\n    const e = this.wrapper.querySelector(`.${s.addColumn}`);\n    e && e.classList.remove(s.addColumnDisabled);\n  }\n  /**\n   * Delete a row by index\n   *\n   * @param {number} index\n   */\n  deleteRow(t) {\n    this.getRow(t).remove();\n    const e = this.wrapper.querySelector(`.${s.addRow}`);\n    e && e.classList.remove(s.addRowDisabled), this.addHeadingAttrToFirstRow();\n  }\n  /**\n   * Create a wrapper containing a table, toolboxes\n   * and buttons for adding rows and columns\n   *\n   * @returns {HTMLElement} wrapper - where all buttons for a table and the table itself will be\n   */\n  createTableWrapper() {\n    if (this.wrapper = c(\"div\", s.wrapper), this.table = c(\"div\", s.table), this.readOnly && this.wrapper.classList.add(s.wrapperReadOnly), this.wrapper.appendChild(this.toolboxRow.element), this.wrapper.appendChild(this.toolboxColumn.element), this.wrapper.appendChild(this.table), !this.readOnly) {\n      const t = c(\"div\", s.addColumn, {\n        innerHTML: v\n      }), e = c(\"div\", s.addRow, {\n        innerHTML: v\n      });\n      this.wrapper.appendChild(t), this.wrapper.appendChild(e);\n    }\n  }\n  /**\n   * Returns the size of the table based on initial data or config \"size\" property\n   *\n   * @return {{rows: number, cols: number}} - number of cols and rows\n   */\n  computeInitialSize() {\n    const t = this.data && this.data.content, e = Array.isArray(t), o = e ? t.length : !1, i = e ? t.length : void 0, n = o ? t[0].length : void 0, r = Number.parseInt(this.config && this.config.rows), h = Number.parseInt(this.config && this.config.cols), l = !isNaN(r) && r > 0 ? r : void 0, u = !isNaN(h) && h > 0 ? h : void 0;\n    return {\n      rows: i || l || 2,\n      cols: n || u || 2\n    };\n  }\n  /**\n   * Resize table to match config size or transmitted data size\n   *\n   * @return {{rows: number, cols: number}} - number of cols and rows\n   */\n  resize() {\n    const { rows: t, cols: e } = this.computeInitialSize();\n    for (let o = 0; o < t; o++)\n      this.addRow();\n    for (let o = 0; o < e; o++)\n      this.addColumn();\n  }\n  /**\n   * Fills the table with data passed to the constructor\n   *\n   * @returns {void}\n   */\n  fill() {\n    const t = this.data;\n    if (t && t.content)\n      for (let e = 0; e < t.content.length; e++)\n        for (let o = 0; o < t.content[e].length; o++)\n          this.setCellContent(e + 1, o + 1, t.content[e][o]);\n  }\n  /**\n   * Fills a row with cells\n   *\n   * @param {HTMLElement} row - row to fill\n   * @param {number} numberOfColumns - how many cells should be in a row\n   */\n  fillRow(t, e) {\n    for (let o = 1; o <= e; o++) {\n      const i = this.createCell();\n      t.appendChild(i);\n    }\n  }\n  /**\n   * Creating a cell element\n   *\n   * @return {Element}\n   */\n  createCell() {\n    return c(\"div\", s.cell, {\n      contentEditable: !this.readOnly\n    });\n  }\n  /**\n   * Get number of rows in the table\n   */\n  get numberOfRows() {\n    return this.table.childElementCount;\n  }\n  /**\n   * Get number of columns in the table\n   */\n  get numberOfColumns() {\n    return this.numberOfRows ? this.table.querySelectorAll(`.${s.row}:first-child .${s.cell}`).length : 0;\n  }\n  /**\n   * Is the column toolbox menu displayed or not\n   *\n   * @returns {boolean}\n   */\n  get isColumnMenuShowing() {\n    return this.selectedColumn !== 0;\n  }\n  /**\n   * Is the row toolbox menu displayed or not\n   *\n   * @returns {boolean}\n   */\n  get isRowMenuShowing() {\n    return this.selectedRow !== 0;\n  }\n  /**\n   * Recalculate position of toolbox icons\n   *\n   * @param {Event} event - mouse move event\n   */\n  onMouseMoveInTable(t) {\n    const { row: e, column: o } = this.getHoveredCell(t);\n    this.hoveredColumn = o, this.hoveredRow = e, this.updateToolboxesPosition();\n  }\n  /**\n   * Prevents default Enter behaviors\n   * Adds Shift+Enter processing\n   *\n   * @param {KeyboardEvent} event - keypress event\n   */\n  onKeyPressListener(t) {\n    if (t.key === \"Enter\") {\n      if (t.shiftKey)\n        return !0;\n      this.moveCursorToNextRow();\n    }\n    return t.key !== \"Enter\";\n  }\n  /**\n   * Prevents tab keydown event from bubbling\n   * so that it only works inside the table\n   *\n   * @param {KeyboardEvent} event - keydown event\n   */\n  onKeyDownListener(t) {\n    t.key === \"Tab\" && t.stopPropagation();\n  }\n  /**\n   * Set the coordinates of the cell that the focus has moved to\n   *\n   * @param {FocusEvent} event - focusin event\n   */\n  focusInTableListener(t) {\n    const e = t.target, o = this.getRowByCell(e);\n    this.focusedCell = {\n      row: Array.from(this.table.querySelectorAll(`.${s.row}`)).indexOf(o) + 1,\n      column: Array.from(o.querySelectorAll(`.${s.cell}`)).indexOf(e) + 1\n    };\n  }\n  /**\n   * Unselect row/column\n   * Close toolbox menu\n   * Hide toolboxes\n   *\n   * @returns {void}\n   */\n  hideToolboxes() {\n    this.hideRowToolbox(), this.hideColumnToolbox(), this.updateToolboxesPosition();\n  }\n  /**\n   * Unselect row, close toolbox\n   *\n   * @returns {void}\n   */\n  hideRowToolbox() {\n    this.unselectRow(), this.toolboxRow.hide();\n  }\n  /**\n   * Unselect column, close toolbox\n   *\n   * @returns {void}\n   */\n  hideColumnToolbox() {\n    this.unselectColumn(), this.toolboxColumn.hide();\n  }\n  /**\n   * Set the cursor focus to the focused cell\n   *\n   * @returns {void}\n   */\n  focusCell() {\n    this.focusedCellElem.focus();\n  }\n  /**\n   * Get current focused element\n   *\n   * @returns {HTMLElement} - focused cell\n   */\n  get focusedCellElem() {\n    const { row: t, column: e } = this.focusedCell;\n    return this.getCell(t, e);\n  }\n  /**\n   * Update toolboxes position\n   *\n   * @param {number} row - hovered row\n   * @param {number} column - hovered column\n   */\n  updateToolboxesPosition(t = this.hoveredRow, e = this.hoveredColumn) {\n    this.isColumnMenuShowing || e > 0 && e <= this.numberOfColumns && this.toolboxColumn.show(() => ({\n      left: `calc((100% - var(--cell-size)) / (${this.numberOfColumns} * 2) * (1 + (${e} - 1) * 2))`\n    })), this.isRowMenuShowing || t > 0 && t <= this.numberOfRows && this.toolboxRow.show(() => {\n      const o = this.getRow(t), { fromTopBorder: i } = g(this.table, o), { height: n } = o.getBoundingClientRect();\n      return {\n        top: `${Math.ceil(i + n / 2)}px`\n      };\n    });\n  }\n  /**\n   * Makes the first row headings\n   *\n   * @param {boolean} withHeadings - use headings row or not\n   */\n  setHeadingsSetting(t) {\n    this.tunes.withHeadings = t, t ? (this.table.classList.add(s.withHeadings), this.addHeadingAttrToFirstRow()) : (this.table.classList.remove(s.withHeadings), this.removeHeadingAttrFromFirstRow());\n  }\n  /**\n   * Adds an attribute for displaying the placeholder in the cell\n   */\n  addHeadingAttrToFirstRow() {\n    for (let t = 1; t <= this.numberOfColumns; t++) {\n      let e = this.getCell(1, t);\n      e && e.setAttribute(\"heading\", this.api.i18n.t(\"Heading\"));\n    }\n  }\n  /**\n   * Removes an attribute for displaying the placeholder in the cell\n   */\n  removeHeadingAttrFromFirstRow() {\n    for (let t = 1; t <= this.numberOfColumns; t++) {\n      let e = this.getCell(1, t);\n      e && e.removeAttribute(\"heading\");\n    }\n  }\n  /**\n   * Add effect of a selected row\n   *\n   * @param {number} index\n   */\n  selectRow(t) {\n    const e = this.getRow(t);\n    e && (this.selectedRow = t, e.classList.add(s.rowSelected));\n  }\n  /**\n   * Remove effect of a selected row\n   */\n  unselectRow() {\n    if (this.selectedRow <= 0)\n      return;\n    const t = this.table.querySelector(`.${s.rowSelected}`);\n    t && t.classList.remove(s.rowSelected), this.selectedRow = 0;\n  }\n  /**\n   * Add effect of a selected column\n   *\n   * @param {number} index\n   */\n  selectColumn(t) {\n    for (let e = 1; e <= this.numberOfRows; e++) {\n      const o = this.getCell(e, t);\n      o && o.classList.add(s.cellSelected);\n    }\n    this.selectedColumn = t;\n  }\n  /**\n   * Remove effect of a selected column\n   */\n  unselectColumn() {\n    if (this.selectedColumn <= 0)\n      return;\n    let t = this.table.querySelectorAll(`.${s.cellSelected}`);\n    Array.from(t).forEach((e) => {\n      e.classList.remove(s.cellSelected);\n    }), this.selectedColumn = 0;\n  }\n  /**\n   * Calculates the row and column that the cursor is currently hovering over\n   * The search was optimized from O(n) to O (log n) via bin search to reduce the number of calculations\n   *\n   * @param {Event} event - mousemove event\n   * @returns hovered cell coordinates as an integer row and column\n   */\n  getHoveredCell(t) {\n    let e = this.hoveredRow, o = this.hoveredColumn;\n    const { width: i, height: n, x: r, y: h } = k(this.table, t);\n    return r >= 0 && (o = this.binSearch(\n      this.numberOfColumns,\n      (l) => this.getCell(1, l),\n      ({ fromLeftBorder: l }) => r < l,\n      ({ fromRightBorder: l }) => r > i - l\n    )), h >= 0 && (e = this.binSearch(\n      this.numberOfRows,\n      (l) => this.getCell(l, 1),\n      ({ fromTopBorder: l }) => h < l,\n      ({ fromBottomBorder: l }) => h > n - l\n    )), {\n      row: e || this.hoveredRow,\n      column: o || this.hoveredColumn\n    };\n  }\n  /**\n   * Looks for the index of the cell the mouse is hovering over.\n   * Cells can be represented as ordered intervals with left and\n   * right (upper and lower for rows) borders inside the table, if the mouse enters it, then this is our index\n   *\n   * @param {number} numberOfCells - upper bound of binary search\n   * @param {function} getCell - function to take the currently viewed cell\n   * @param {function} beforeTheLeftBorder - determines the cursor position, to the left of the cell or not\n   * @param {function} afterTheRightBorder - determines the cursor position, to the right of the cell or not\n   * @returns {number}\n   */\n  binSearch(t, e, o, i) {\n    let n = 0, r = t + 1, h = 0, l;\n    for (; n < r - 1 && h < 10; ) {\n      l = Math.ceil((n + r) / 2);\n      const u = e(l), p = g(this.table, u);\n      if (o(p))\n        r = l;\n      else if (i(p))\n        n = l;\n      else\n        break;\n      h++;\n    }\n    return l;\n  }\n  /**\n   * Collects data from cells into a two-dimensional array\n   *\n   * @returns {string[][]}\n   */\n  getData() {\n    const t = [];\n    for (let e = 1; e <= this.numberOfRows; e++) {\n      const o = this.table.querySelector(`.${s.row}:nth-child(${e})`), i = Array.from(o.querySelectorAll(`.${s.cell}`));\n      i.every((r) => !r.textContent.trim()) || t.push(i.map((r) => r.innerHTML));\n    }\n    return t;\n  }\n  /**\n   * Remove listeners on the document\n   */\n  destroy() {\n    document.removeEventListener(\"click\", this.documentClicked);\n  }\n}\nclass F {\n  /**\n   * Notify core that read-only mode is supported\n   *\n   * @returns {boolean}\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Allow to press Enter inside the CodeTool textarea\n   *\n   * @returns {boolean}\n   * @public\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   *\n   * @param {TableConstructor} init\n   */\n  constructor({ data: t, config: e, api: o, readOnly: i, block: n }) {\n    this.api = o, this.readOnly = i, this.config = e, this.data = {\n      withHeadings: this.getConfig(\"withHeadings\", !1, t),\n      stretched: this.getConfig(\"stretched\", !1, t),\n      content: t && t.content ? t.content : []\n    }, this.table = null, this.block = n;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   *\n   * @returns {{icon: string, title: string}}\n   */\n  static get toolbox() {\n    return {\n      icon: A,\n      title: \"Table\"\n    };\n  }\n  /**\n   * Return Tool's view\n   *\n   * @returns {HTMLDivElement}\n   */\n  render() {\n    return this.table = new E(this.readOnly, this.api, this.data, this.config), this.container = c(\"div\", this.api.styles.block), this.container.appendChild(this.table.getWrapper()), this.table.setHeadingsSetting(this.data.withHeadings), this.container;\n  }\n  /**\n   * Returns plugin settings\n   *\n   * @returns {Array}\n   */\n  renderSettings() {\n    return [\n      {\n        label: this.api.i18n.t(\"With headings\"),\n        icon: T,\n        isActive: this.data.withHeadings,\n        closeOnActivate: !0,\n        toggle: !0,\n        onActivate: () => {\n          this.data.withHeadings = !0, this.table.setHeadingsSetting(this.data.withHeadings);\n        }\n      },\n      {\n        label: this.api.i18n.t(\"Without headings\"),\n        icon: H,\n        isActive: !this.data.withHeadings,\n        closeOnActivate: !0,\n        toggle: !0,\n        onActivate: () => {\n          this.data.withHeadings = !1, this.table.setHeadingsSetting(this.data.withHeadings);\n        }\n      },\n      {\n        label: this.data.stretched ? this.api.i18n.t(\"Collapse\") : this.api.i18n.t(\"Stretch\"),\n        icon: this.data.stretched ? R : O,\n        closeOnActivate: !0,\n        toggle: !0,\n        onActivate: () => {\n          this.data.stretched = !this.data.stretched, this.block.stretched = this.data.stretched;\n        }\n      }\n    ];\n  }\n  /**\n   * Extract table data from the view\n   *\n   * @returns {TableData} - saved data\n   */\n  save() {\n    const t = this.table.getData();\n    return {\n      withHeadings: this.data.withHeadings,\n      stretched: this.data.stretched,\n      content: t\n    };\n  }\n  /**\n   * Plugin destroyer\n   *\n   * @returns {void}\n   */\n  destroy() {\n    this.table.destroy();\n  }\n  /**\n   * A helper to get config value.\n   *\n   * @param {string} configName - the key to get from the config.\n   * @param {any} defaultValue - default value if config doesn't have passed key\n   * @param {object} savedData - previously saved data. If passed, the key will be got from there, otherwise from the config\n   * @returns {any} - config value.\n   */\n  getConfig(t, e = void 0, o = void 0) {\n    const i = this.data || o;\n    return i ? i[t] ? i[t] : e : this.config && this.config[t] ? this.config[t] : e;\n  }\n  /**\n   * Table onPaste configuration\n   *\n   * @public\n   */\n  static get pasteConfig() {\n    return { tags: [\"TABLE\", \"TR\", \"TH\", \"TD\"] };\n  }\n  /**\n   * On paste callback that is fired from Editor\n   *\n   * @param {PasteEvent} event - event with pasted data\n   */\n  onPaste(t) {\n    const e = t.detail.data, o = e.querySelector(\":scope > thead, tr:first-of-type th\"), n = Array.from(e.querySelectorAll(\"tr\")).map((r) => Array.from(r.querySelectorAll(\"th, td\")).map((l) => l.innerHTML));\n    this.data = {\n      withHeadings: o !== null,\n      content: n\n    }, this.table.wrapper && this.table.wrapper.replaceWith(this.render());\n  }\n}\nexport {\n  F as default\n};\n"], "mappings": ";;;CAAC,WAAU;AAAC,MAAI;AAAE;AAAa,MAAG;AAAC,QAAG,OAAO,WAAS,KAAI;AAAC,UAAI,IAAE,SAAS,cAAc,OAAO;AAAE,QAAE,SAAO,IAAE,SAAS,KAAK,cAAc,0BAA0B,MAAI,OAAK,SAAO,EAAE,SAAQ,EAAE,YAAY,SAAS,eAAe,o6LAAo6L,CAAC,GAAE,SAAS,KAAK,YAAY,CAAC;AAAA,IAAC;AAAA,EAAC,SAAO,GAAE;AAAC,YAAQ,MAAM,kCAAiC,CAAC;AAAA,EAAC;AAAC,GAAG;AACruM,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG;AACvB,QAAM,IAAI,SAAS,cAAc,CAAC;AAClC,QAAM,QAAQ,CAAC,IAAI,EAAE,UAAU,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,UAAU,IAAI,CAAC;AACjE,aAAW,KAAK;AACd,WAAO,UAAU,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAC3D,SAAO;AACT;AACA,SAAS,EAAE,GAAG;AACZ,QAAM,IAAI,EAAE,sBAAsB;AAClC,SAAO;AAAA,IACL,IAAI,KAAK,MAAM,EAAE,MAAM,OAAO,WAAW;AAAA,IACzC,IAAI,KAAK,MAAM,EAAE,OAAO,OAAO,WAAW;AAAA,IAC1C,IAAI,KAAK,MAAM,EAAE,QAAQ,OAAO,WAAW;AAAA,IAC3C,IAAI,KAAK,MAAM,EAAE,SAAS,OAAO,WAAW;AAAA,EAC9C;AACF;AACA,SAAS,EAAE,GAAG,GAAG;AACf,QAAM,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC;AACvB,SAAO;AAAA,IACL,eAAe,EAAE,KAAK,EAAE;AAAA,IACxB,gBAAgB,EAAE,KAAK,EAAE;AAAA,IACzB,iBAAiB,EAAE,KAAK,EAAE;AAAA,IAC1B,kBAAkB,EAAE,KAAK,EAAE;AAAA,EAC7B;AACF;AACA,SAAS,EAAE,GAAG,GAAG;AACf,QAAM,IAAI,EAAE,sBAAsB,GAAG,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,GAAG,EAAE,SAAS,GAAG,SAAS,EAAE,IAAI;AAC3G,SAAO;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT;AACF;AACA,SAAS,EAAE,GAAG,GAAG;AACf,SAAO,EAAE,WAAW,aAAa,GAAG,CAAC;AACvC;AACA,SAAS,EAAE,GAAG,IAAI,MAAI;AACpB,QAAM,IAAI,SAAS,YAAY,GAAG,IAAI,OAAO,aAAa;AAC1D,IAAE,mBAAmB,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,EAAE,gBAAgB,GAAG,EAAE,SAAS,CAAC;AAC3E;AACA,IAAM,IAAN,MAAM,GAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKN,YAAY,EAAE,OAAO,EAAE,GAAG;AACxB,SAAK,QAAQ,GAAG,KAAK,UAAU,QAAQ,KAAK,UAAU,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,MAAM;AACf,WAAO;AAAA,MACL,SAAS;AAAA,MACT,eAAe;AAAA,MACf,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,WAAO,KAAK,UAAU,EAAE,OAAO,GAAE,IAAI,OAAO,GAAG,KAAK,MAAM,QAAQ,CAAC,GAAG,MAAM;AAC1E,YAAM,IAAI,EAAE,OAAO,GAAE,IAAI,IAAI,GAAG,IAAI,EAAE,OAAO,GAAE,IAAI,UAAU;AAAA,QAC3D,WAAW,EAAE;AAAA,MACf,CAAC,GAAG,IAAI,EAAE,OAAO,GAAE,IAAI,WAAW;AAAA,QAChC,aAAa,EAAE;AAAA,MACjB,CAAC;AACD,QAAE,QAAQ,QAAQ,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,KAAK,QAAQ,YAAY,CAAC,GAAG,KAAK,QAAQ,KAAK,CAAC;AAAA,IAC3G,CAAC,GAAG,KAAK,QAAQ,iBAAiB,SAAS,CAAC,MAAM;AAChD,WAAK,eAAe,CAAC;AAAA,IACvB,CAAC,GAAG,KAAK;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,GAAG;AAChB,UAAM,IAAI,EAAE,OAAO,QAAQ,IAAI,GAAE,IAAI,IAAI,EAAE;AAC3C,QAAI,CAAC;AACH;AACF,UAAM,IAAI,EAAE,QAAQ,OAAO,IAAI,KAAK,MAAM,CAAC;AAC3C,QAAI,EAAE,wBAAwB,CAAC,KAAK,qBAAqB,CAAC,GAAG;AAC3D,WAAK,qBAAqB,CAAC;AAC3B;AAAA,IACF;AACA,MAAE,QAAQ;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,GAAG;AACtB,MAAE,UAAU,IAAI,GAAE,IAAI,gBAAgB;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,GAAG;AACxB,MAAE,UAAU,OAAO,GAAE,IAAI,gBAAgB;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,GAAG;AACtB,WAAO,EAAE,UAAU,SAAS,GAAE,IAAI,gBAAgB;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAS;AACX,WAAO,KAAK,QAAQ,UAAU,SAAS,GAAE,IAAI,aAAa;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACL,SAAK,MAAM,QAAQ,CAAC,GAAG,MAAM;AAC3B,aAAO,EAAE,UAAU,cAAc,KAAK,QAAQ,CAAC,EAAE,UAAU,OAAO,GAAE,IAAI,YAAY,EAAE,OAAO,CAAC;AAAA,IAChG,CAAC,GAAG,KAAK,QAAQ,UAAU,IAAI,GAAE,IAAI,aAAa;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,SAAK,QAAQ,UAAU,OAAO,GAAE,IAAI,aAAa,GAAG,KAAK,QAAQ,QAAQ,CAAC,MAAM;AAC9E,WAAK,uBAAuB,CAAC;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AACA,IAAM,IAAI;AAAV,IAAoX,IAAI;AAAxX,IAAulB,IAAI;AAA3lB,IAAg9B,IAAI;AAAp9B,IAAg1C,IAAI;AAAp1C,IAAgtD,IAAI;AAAptD,IAA8kE,IAAI;AAAllE,IAAyiF,IAAI;AAA7iF,IAAkwF,IAAI;AAAtwF,IAAuyG,IAAI;AAA3yG,IAAuiH,IAAI;AAA3iH,IAA49H,IAAI;AACh+H,IAAM,IAAN,MAAM,GAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWN,YAAY,EAAE,KAAK,GAAG,OAAO,GAAG,QAAQ,GAAG,SAAS,GAAG,aAAa,IAAI,GAAG,GAAG;AAC5E,SAAK,MAAM,GAAG,KAAK,QAAQ,GAAG,KAAK,SAAS,GAAG,KAAK,UAAU,GAAG,KAAK,cAAc,GAAG,KAAK,UAAU,MAAM,KAAK,UAAU,KAAK,cAAc;AAAA,EAChJ;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,MAAM;AACf,WAAO;AAAA,MACL,SAAS;AAAA,MACT,eAAe;AAAA,MACf,SAAS;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AACd,UAAM,IAAI,EAAE,OAAO;AAAA,MACjB,GAAE,IAAI;AAAA,MACN,KAAK,cAAc,GAAG,GAAE,IAAI,OAAO,KAAK,KAAK,WAAW,KAAK;AAAA,IAC/D,CAAC;AACD,MAAE,QAAQ,eAAe;AACzB,UAAM,IAAI,KAAK,cAAc,GAAG,IAAI,KAAK,cAAc;AACvD,WAAO,EAAE,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AACd,UAAM,IAAI,EAAE,OAAO,GAAE,IAAI,SAAS;AAAA,MAChC,WAAW;AAAA,IACb,CAAC;AACD,WAAO,EAAE,iBAAiB,SAAS,MAAM;AACvC,WAAK,eAAe;AAAA,IACtB,CAAC,GAAG;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AACd,WAAO,KAAK,UAAU,IAAI,EAAE;AAAA,MAC1B,OAAO,KAAK;AAAA,IACd,CAAC,GAAG,KAAK,QAAQ,OAAO;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB;AACf,SAAK,QAAQ,UAAU,KAAK,QAAQ,MAAM,GAAG,KAAK,QAAQ,MAAM,KAAK,QAAQ,KAAK,GAAG,KAAK,OAAO;AAAA,EACnG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,GAAG;AACN,UAAM,IAAI,EAAE;AACZ,WAAO,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACpC,WAAK,QAAQ,MAAM,CAAC,IAAI;AAAA,IAC1B,CAAC,GAAG,KAAK,QAAQ,UAAU,IAAI,GAAE,IAAI,aAAa;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACL,SAAK,QAAQ,MAAM,GAAG,KAAK,QAAQ,UAAU,OAAO,GAAE,IAAI,aAAa;AAAA,EACzE;AACF;AACA,SAAS,EAAE,GAAG,GAAG;AACf,MAAI,IAAI;AACR,SAAO,YAAY,GAAG;AACpB,UAAM,KAAqB,oBAAI,KAAK,GAAG,QAAQ;AAC/C,QAAI,EAAE,IAAI,IAAI;AACZ,aAAO,IAAI,GAAG,EAAE,GAAG,CAAC;AAAA,EACxB;AACF;AACA,IAAM,IAAI;AAAA,EACR,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,KAAK;AAAA,EACL,cAAc;AAAA,EACd,aAAa;AAAA,EACb,MAAM;AAAA,EACN,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,mBAAmB;AACrB;AACA,IAAM,IAAN,MAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUN,YAAY,GAAG,GAAG,GAAG,GAAG;AACtB,SAAK,WAAW,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,SAAS,GAAG,KAAK,UAAU,MAAM,KAAK,QAAQ,MAAM,KAAK,gBAAgB,KAAK,oBAAoB,GAAG,KAAK,aAAa,KAAK,iBAAiB,GAAG,KAAK,mBAAmB,GAAG,KAAK,aAAa,GAAG,KAAK,gBAAgB,GAAG,KAAK,cAAc,GAAG,KAAK,iBAAiB,GAAG,KAAK,QAAQ;AAAA,MACvU,cAAc;AAAA,IAChB,GAAG,KAAK,OAAO,GAAG,KAAK,KAAK,GAAG,KAAK,cAAc;AAAA,MAChD,KAAK;AAAA,MACL,QAAQ;AAAA,IACV,GAAG,KAAK,kBAAkB,CAAC,MAAM;AAC/B,YAAM,IAAI,EAAE,OAAO,QAAQ,IAAI,EAAE,KAAK,EAAE,MAAM,MAAM,IAAI,EAAE,OAAO,QAAQ,IAAI,EAAE,OAAO,EAAE,MAAM;AAC9F,OAAC,KAAK,MAAM,KAAK,cAAc;AAC/B,YAAM,IAAI,EAAE,OAAO,QAAQ,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,OAAO,QAAQ,IAAI,EAAE,SAAS,EAAE;AAClF,WAAK,EAAE,eAAe,KAAK,WAAW,KAAK,OAAO,QAAQ,IAAE,GAAG,KAAK,cAAc,KAAK,KAAK,EAAE,eAAe,KAAK,YAAY,KAAK,UAAU,QAAQ,IAAE,GAAG,KAAK,cAAc;AAAA,IAC/K,GAAG,KAAK,YAAY,KAAK,WAAW;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACX,aAAS,iBAAiB,SAAS,KAAK,eAAe,GAAG,KAAK,MAAM,iBAAiB,aAAa,EAAE,KAAK,CAAC,MAAM,KAAK,mBAAmB,CAAC,CAAC,GAAG,EAAE,SAAS,KAAG,CAAC,GAAG,KAAK,MAAM,aAAa,CAAC,MAAM,KAAK,mBAAmB,CAAC,GAAG,KAAK,MAAM,iBAAiB,WAAW,CAAC,MAAM,KAAK,kBAAkB,CAAC,CAAC,GAAG,KAAK,MAAM,iBAAiB,WAAW,CAAC,MAAM,KAAK,qBAAqB,CAAC,CAAC;AAAA,EACjX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB;AACpB,WAAO,IAAI,EAAE;AAAA,MACX,KAAK,KAAK;AAAA,MACV,aAAa;AAAA,MACb,OAAO;AAAA,QACL;AAAA,UACE,OAAO,KAAK,IAAI,KAAK,EAAE,oBAAoB;AAAA,UAC3C,MAAM;AAAA,UACN,QAAQ,MAAM,KAAK,oBAAoB,KAAK,OAAO;AAAA,UACnD,SAAS,MAAM;AACb,iBAAK,UAAU,KAAK,gBAAgB,IAAE,GAAG,KAAK,cAAc;AAAA,UAC9D;AAAA,QACF;AAAA,QACA;AAAA,UACE,OAAO,KAAK,IAAI,KAAK,EAAE,qBAAqB;AAAA,UAC5C,MAAM;AAAA,UACN,QAAQ,MAAM,KAAK,oBAAoB,KAAK,OAAO;AAAA,UACnD,SAAS,MAAM;AACb,iBAAK,UAAU,KAAK,iBAAiB,GAAG,IAAE,GAAG,KAAK,cAAc;AAAA,UAClE;AAAA,QACF;AAAA,QACA;AAAA,UACE,OAAO,KAAK,IAAI,KAAK,EAAE,eAAe;AAAA,UACtC,MAAM;AAAA,UACN,QAAQ,MAAM,KAAK,oBAAoB;AAAA,UACvC,sBAAsB;AAAA,UACtB,SAAS,MAAM;AACb,iBAAK,aAAa,KAAK,cAAc,GAAG,KAAK,cAAc;AAAA,UAC7D;AAAA,QACF;AAAA,MACF;AAAA,MACA,QAAQ,MAAM;AACZ,aAAK,aAAa,KAAK,aAAa,GAAG,KAAK,eAAe;AAAA,MAC7D;AAAA,MACA,SAAS,MAAM;AACb,aAAK,eAAe;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB;AACjB,WAAO,IAAI,EAAE;AAAA,MACX,KAAK,KAAK;AAAA,MACV,aAAa;AAAA,MACb,OAAO;AAAA,QACL;AAAA,UACE,OAAO,KAAK,IAAI,KAAK,EAAE,eAAe;AAAA,UACtC,MAAM;AAAA,UACN,QAAQ,MAAM,KAAK,iBAAiB,KAAK,OAAO;AAAA,UAChD,SAAS,MAAM;AACb,iBAAK,OAAO,KAAK,aAAa,IAAE,GAAG,KAAK,cAAc;AAAA,UACxD;AAAA,QACF;AAAA,QACA;AAAA,UACE,OAAO,KAAK,IAAI,KAAK,EAAE,eAAe;AAAA,UACtC,MAAM;AAAA,UACN,QAAQ,MAAM,KAAK,iBAAiB,KAAK,OAAO;AAAA,UAChD,SAAS,MAAM;AACb,iBAAK,OAAO,KAAK,cAAc,GAAG,IAAE,GAAG,KAAK,cAAc;AAAA,UAC5D;AAAA,QACF;AAAA,QACA;AAAA,UACE,OAAO,KAAK,IAAI,KAAK,EAAE,YAAY;AAAA,UACnC,MAAM;AAAA,UACN,QAAQ,MAAM,KAAK,iBAAiB;AAAA,UACpC,sBAAsB;AAAA,UACtB,SAAS,MAAM;AACb,iBAAK,UAAU,KAAK,WAAW,GAAG,KAAK,cAAc;AAAA,UACvD;AAAA,QACF;AAAA,MACF;AAAA,MACA,QAAQ,MAAM;AACZ,aAAK,UAAU,KAAK,UAAU,GAAG,KAAK,kBAAkB;AAAA,MAC1D;AAAA,MACA,SAAS,MAAM;AACb,aAAK,YAAY;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AACpB,SAAK,YAAY,QAAQ,KAAK,gBAAgB,KAAK,YAAY,OAAO,GAAG,KAAK,UAAU,KAAK,WAAW,MAAM,KAAK,OAAO,GAAG,KAAK,YAAY,OAAO,GAAG,KAAK,UAAU,KAAK,WAAW,GAAG,KAAK,wBAAwB,GAAG,CAAC;AAAA,EAC7N;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,GAAG,GAAG;AACZ,WAAO,KAAK,MAAM,iBAAiB,IAAI,EAAE,GAAG,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC;AAAA,EAClF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,GAAG;AACR,WAAO,KAAK,MAAM,cAAc,IAAI,EAAE,GAAG,cAAc,CAAC,GAAG;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,GAAG;AACd,WAAO,EAAE;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,GAAG;AACjB,WAAO,EAAE,cAAc,IAAI,EAAE,IAAI,cAAc;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,GAAG,GAAG,GAAG;AACtB,UAAM,IAAI,KAAK,QAAQ,GAAG,CAAC;AAC3B,MAAE,YAAY;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,IAAI,IAAI,IAAI,OAAI;AACxB,QAAI;AACJ,QAAI,IAAI,KAAK;AACb,QAAI,KAAK,UAAU,KAAK,OAAO,WAAW,KAAK,mBAAmB,KAAK,OAAO;AAC5E;AACF,aAAS,IAAI,GAAG,KAAK,KAAK,cAAc,KAAK;AAC3C,UAAI;AACJ,YAAM,IAAI,KAAK,WAAW;AAC1B,UAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,IAAI,KAAK,OAAO,CAAC,EAAE,YAAY,CAAC,GAAG,MAAM,GAAG;AACpG,cAAM,IAAI,KAAK,QAAQ,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC;AAC3C,aAAK,KAAK,EAAE,CAAC;AAAA,MACf;AAAA,IACF;AACA,UAAM,IAAI,KAAK,QAAQ,cAAc,IAAI,EAAE,SAAS,EAAE;AACtD,KAAC,IAAI,KAAK,WAAW,QAAQ,EAAE,WAAW,KAAK,kBAAkB,KAAK,OAAO,UAAU,KAAK,KAAK,EAAE,UAAU,IAAI,EAAE,iBAAiB,GAAG,KAAK,yBAAyB;AAAA,EACvK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,IAAI,IAAI,IAAI,OAAI;AACrB,QAAI,GAAG,IAAI,EAAE,OAAO,EAAE,GAAG;AACzB,SAAK,MAAM,gBAAgB,KAAK,8BAA8B;AAC9D,QAAI,IAAI,KAAK;AACb,QAAI,KAAK,UAAU,KAAK,OAAO,WAAW,KAAK,gBAAgB,KAAK,OAAO,WAAW;AACpF;AACF,QAAI,IAAI,KAAK,KAAK,KAAK,cAAc;AACnC,UAAI,IAAI,KAAK,OAAO,CAAC;AACrB,UAAI,EAAE,GAAG,CAAC;AAAA,IACZ;AACE,UAAI,KAAK,MAAM,YAAY,CAAC;AAC9B,SAAK,QAAQ,GAAG,CAAC,GAAG,KAAK,MAAM,gBAAgB,KAAK,yBAAyB;AAC7E,UAAM,IAAI,KAAK,gBAAgB,CAAC;AAChC,SAAK,KAAK,EAAE,CAAC;AACb,UAAM,IAAI,KAAK,QAAQ,cAAc,IAAI,EAAE,MAAM,EAAE;AACnD,WAAO,KAAK,UAAU,KAAK,OAAO,WAAW,KAAK,gBAAgB,KAAK,OAAO,WAAW,KAAK,EAAE,UAAU,IAAI,EAAE,cAAc,GAAG;AAAA,EACnI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,GAAG;AACd,aAAS,IAAI,GAAG,KAAK,KAAK,cAAc,KAAK;AAC3C,YAAM,IAAI,KAAK,QAAQ,GAAG,CAAC;AAC3B,UAAI,CAAC;AACH;AACF,QAAE,OAAO;AAAA,IACX;AACA,UAAM,IAAI,KAAK,QAAQ,cAAc,IAAI,EAAE,SAAS,EAAE;AACtD,SAAK,EAAE,UAAU,OAAO,EAAE,iBAAiB;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,GAAG;AACX,SAAK,OAAO,CAAC,EAAE,OAAO;AACtB,UAAM,IAAI,KAAK,QAAQ,cAAc,IAAI,EAAE,MAAM,EAAE;AACnD,SAAK,EAAE,UAAU,OAAO,EAAE,cAAc,GAAG,KAAK,yBAAyB;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAAqB;AACnB,QAAI,KAAK,UAAU,EAAE,OAAO,EAAE,OAAO,GAAG,KAAK,QAAQ,EAAE,OAAO,EAAE,KAAK,GAAG,KAAK,YAAY,KAAK,QAAQ,UAAU,IAAI,EAAE,eAAe,GAAG,KAAK,QAAQ,YAAY,KAAK,WAAW,OAAO,GAAG,KAAK,QAAQ,YAAY,KAAK,cAAc,OAAO,GAAG,KAAK,QAAQ,YAAY,KAAK,KAAK,GAAG,CAAC,KAAK,UAAU;AACrS,YAAM,IAAI,EAAE,OAAO,EAAE,WAAW;AAAA,QAC9B,WAAW;AAAA,MACb,CAAC,GAAG,IAAI,EAAE,OAAO,EAAE,QAAQ;AAAA,QACzB,WAAW;AAAA,MACb,CAAC;AACD,WAAK,QAAQ,YAAY,CAAC,GAAG,KAAK,QAAQ,YAAY,CAAC;AAAA,IACzD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB;AACnB,UAAM,IAAI,KAAK,QAAQ,KAAK,KAAK,SAAS,IAAI,MAAM,QAAQ,CAAC,GAAG,IAAI,IAAI,EAAE,SAAS,OAAI,IAAI,IAAI,EAAE,SAAS,QAAQ,IAAI,IAAI,EAAE,CAAC,EAAE,SAAS,QAAQ,IAAI,OAAO,SAAS,KAAK,UAAU,KAAK,OAAO,IAAI,GAAG,IAAI,OAAO,SAAS,KAAK,UAAU,KAAK,OAAO,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,IAAI;AAC9T,WAAO;AAAA,MACL,MAAM,KAAK,KAAK;AAAA,MAChB,MAAM,KAAK,KAAK;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,UAAM,EAAE,MAAM,GAAG,MAAM,EAAE,IAAI,KAAK,mBAAmB;AACrD,aAAS,IAAI,GAAG,IAAI,GAAG;AACrB,WAAK,OAAO;AACd,aAAS,IAAI,GAAG,IAAI,GAAG;AACrB,WAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACL,UAAM,IAAI,KAAK;AACf,QAAI,KAAK,EAAE;AACT,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,QAAQ;AACpC,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ;AACvC,eAAK,eAAe,IAAI,GAAG,IAAI,GAAG,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,GAAG,GAAG;AACZ,aAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3B,YAAM,IAAI,KAAK,WAAW;AAC1B,QAAE,YAAY,CAAC;AAAA,IACjB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACX,WAAO,EAAE,OAAO,EAAE,MAAM;AAAA,MACtB,iBAAiB,CAAC,KAAK;AAAA,IACzB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,eAAe;AACjB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,kBAAkB;AACpB,WAAO,KAAK,eAAe,KAAK,MAAM,iBAAiB,IAAI,EAAE,GAAG,iBAAiB,EAAE,IAAI,EAAE,EAAE,SAAS;AAAA,EACtG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,sBAAsB;AACxB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,mBAAmB;AACrB,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,GAAG;AACpB,UAAM,EAAE,KAAK,GAAG,QAAQ,EAAE,IAAI,KAAK,eAAe,CAAC;AACnD,SAAK,gBAAgB,GAAG,KAAK,aAAa,GAAG,KAAK,wBAAwB;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,GAAG;AACpB,QAAI,EAAE,QAAQ,SAAS;AACrB,UAAI,EAAE;AACJ,eAAO;AACT,WAAK,oBAAoB;AAAA,IAC3B;AACA,WAAO,EAAE,QAAQ;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,GAAG;AACnB,MAAE,QAAQ,SAAS,EAAE,gBAAgB;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,GAAG;AACtB,UAAM,IAAI,EAAE,QAAQ,IAAI,KAAK,aAAa,CAAC;AAC3C,SAAK,cAAc;AAAA,MACjB,KAAK,MAAM,KAAK,KAAK,MAAM,iBAAiB,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,CAAC,IAAI;AAAA,MACvE,QAAQ,MAAM,KAAK,EAAE,iBAAiB,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,CAAC,IAAI;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB;AACd,SAAK,eAAe,GAAG,KAAK,kBAAkB,GAAG,KAAK,wBAAwB;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB;AACf,SAAK,YAAY,GAAG,KAAK,WAAW,KAAK;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,SAAK,eAAe,GAAG,KAAK,cAAc,KAAK;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY;AACV,SAAK,gBAAgB,MAAM;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,kBAAkB;AACpB,UAAM,EAAE,KAAK,GAAG,QAAQ,EAAE,IAAI,KAAK;AACnC,WAAO,KAAK,QAAQ,GAAG,CAAC;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB,IAAI,KAAK,YAAY,IAAI,KAAK,eAAe;AACnE,SAAK,uBAAuB,IAAI,KAAK,KAAK,KAAK,mBAAmB,KAAK,cAAc,KAAK,OAAO;AAAA,MAC/F,MAAM,qCAAqC,KAAK,eAAe,iBAAiB,CAAC;AAAA,IACnF,EAAE,GAAG,KAAK,oBAAoB,IAAI,KAAK,KAAK,KAAK,gBAAgB,KAAK,WAAW,KAAK,MAAM;AAC1F,YAAM,IAAI,KAAK,OAAO,CAAC,GAAG,EAAE,eAAe,EAAE,IAAI,EAAE,KAAK,OAAO,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,sBAAsB;AAC3G,aAAO;AAAA,QACL,KAAK,GAAG,KAAK,KAAK,IAAI,IAAI,CAAC,CAAC;AAAA,MAC9B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,GAAG;AACpB,SAAK,MAAM,eAAe,GAAG,KAAK,KAAK,MAAM,UAAU,IAAI,EAAE,YAAY,GAAG,KAAK,yBAAyB,MAAM,KAAK,MAAM,UAAU,OAAO,EAAE,YAAY,GAAG,KAAK,8BAA8B;AAAA,EAClM;AAAA;AAAA;AAAA;AAAA,EAIA,2BAA2B;AACzB,aAAS,IAAI,GAAG,KAAK,KAAK,iBAAiB,KAAK;AAC9C,UAAI,IAAI,KAAK,QAAQ,GAAG,CAAC;AACzB,WAAK,EAAE,aAAa,WAAW,KAAK,IAAI,KAAK,EAAE,SAAS,CAAC;AAAA,IAC3D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,gCAAgC;AAC9B,aAAS,IAAI,GAAG,KAAK,KAAK,iBAAiB,KAAK;AAC9C,UAAI,IAAI,KAAK,QAAQ,GAAG,CAAC;AACzB,WAAK,EAAE,gBAAgB,SAAS;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,GAAG;AACX,UAAM,IAAI,KAAK,OAAO,CAAC;AACvB,UAAM,KAAK,cAAc,GAAG,EAAE,UAAU,IAAI,EAAE,WAAW;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB;AACF,UAAM,IAAI,KAAK,MAAM,cAAc,IAAI,EAAE,WAAW,EAAE;AACtD,SAAK,EAAE,UAAU,OAAO,EAAE,WAAW,GAAG,KAAK,cAAc;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,GAAG;AACd,aAAS,IAAI,GAAG,KAAK,KAAK,cAAc,KAAK;AAC3C,YAAM,IAAI,KAAK,QAAQ,GAAG,CAAC;AAC3B,WAAK,EAAE,UAAU,IAAI,EAAE,YAAY;AAAA,IACrC;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,KAAK,kBAAkB;AACzB;AACF,QAAI,IAAI,KAAK,MAAM,iBAAiB,IAAI,EAAE,YAAY,EAAE;AACxD,UAAM,KAAK,CAAC,EAAE,QAAQ,CAAC,MAAM;AAC3B,QAAE,UAAU,OAAO,EAAE,YAAY;AAAA,IACnC,CAAC,GAAG,KAAK,iBAAiB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,GAAG;AAChB,QAAI,IAAI,KAAK,YAAY,IAAI,KAAK;AAClC,UAAM,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,KAAK,OAAO,CAAC;AAC3D,WAAO,KAAK,MAAM,IAAI,KAAK;AAAA,MACzB,KAAK;AAAA,MACL,CAAC,MAAM,KAAK,QAAQ,GAAG,CAAC;AAAA,MACxB,CAAC,EAAE,gBAAgB,EAAE,MAAM,IAAI;AAAA,MAC/B,CAAC,EAAE,iBAAiB,EAAE,MAAM,IAAI,IAAI;AAAA,IACtC,IAAI,KAAK,MAAM,IAAI,KAAK;AAAA,MACtB,KAAK;AAAA,MACL,CAAC,MAAM,KAAK,QAAQ,GAAG,CAAC;AAAA,MACxB,CAAC,EAAE,eAAe,EAAE,MAAM,IAAI;AAAA,MAC9B,CAAC,EAAE,kBAAkB,EAAE,MAAM,IAAI,IAAI;AAAA,IACvC,IAAI;AAAA,MACF,KAAK,KAAK,KAAK;AAAA,MACf,QAAQ,KAAK,KAAK;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,UAAU,GAAG,GAAG,GAAG,GAAG;AACpB,QAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG;AAC7B,WAAO,IAAI,IAAI,KAAK,IAAI,MAAM;AAC5B,UAAI,KAAK,MAAM,IAAI,KAAK,CAAC;AACzB,YAAM,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,KAAK,OAAO,CAAC;AACnC,UAAI,EAAE,CAAC;AACL,YAAI;AAAA,eACG,EAAE,CAAC;AACV,YAAI;AAAA;AAEJ;AACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,UAAM,IAAI,CAAC;AACX,aAAS,IAAI,GAAG,KAAK,KAAK,cAAc,KAAK;AAC3C,YAAM,IAAI,KAAK,MAAM,cAAc,IAAI,EAAE,GAAG,cAAc,CAAC,GAAG,GAAG,IAAI,MAAM,KAAK,EAAE,iBAAiB,IAAI,EAAE,IAAI,EAAE,CAAC;AAChH,QAAE,MAAM,CAAC,MAAM,CAAC,EAAE,YAAY,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;AAAA,IAC3E;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,aAAS,oBAAoB,SAAS,KAAK,eAAe;AAAA,EAC5D;AACF;AACA,IAAM,IAAN,MAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMN,WAAW,sBAAsB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,mBAAmB;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,GAAG,UAAU,GAAG,OAAO,EAAE,GAAG;AACjE,SAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,SAAS,GAAG,KAAK,OAAO;AAAA,MAC5D,cAAc,KAAK,UAAU,gBAAgB,OAAI,CAAC;AAAA,MAClD,WAAW,KAAK,UAAU,aAAa,OAAI,CAAC;AAAA,MAC5C,SAAS,KAAK,EAAE,UAAU,EAAE,UAAU,CAAC;AAAA,IACzC,GAAG,KAAK,QAAQ,MAAM,KAAK,QAAQ;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,UAAU;AACnB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,WAAO,KAAK,QAAQ,IAAI,EAAE,KAAK,UAAU,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM,GAAG,KAAK,YAAY,EAAE,OAAO,KAAK,IAAI,OAAO,KAAK,GAAG,KAAK,UAAU,YAAY,KAAK,MAAM,WAAW,CAAC,GAAG,KAAK,MAAM,mBAAmB,KAAK,KAAK,YAAY,GAAG,KAAK;AAAA,EACjP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB;AACf,WAAO;AAAA,MACL;AAAA,QACE,OAAO,KAAK,IAAI,KAAK,EAAE,eAAe;AAAA,QACtC,MAAM;AAAA,QACN,UAAU,KAAK,KAAK;AAAA,QACpB,iBAAiB;AAAA,QACjB,QAAQ;AAAA,QACR,YAAY,MAAM;AAChB,eAAK,KAAK,eAAe,MAAI,KAAK,MAAM,mBAAmB,KAAK,KAAK,YAAY;AAAA,QACnF;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO,KAAK,IAAI,KAAK,EAAE,kBAAkB;AAAA,QACzC,MAAM;AAAA,QACN,UAAU,CAAC,KAAK,KAAK;AAAA,QACrB,iBAAiB;AAAA,QACjB,QAAQ;AAAA,QACR,YAAY,MAAM;AAChB,eAAK,KAAK,eAAe,OAAI,KAAK,MAAM,mBAAmB,KAAK,KAAK,YAAY;AAAA,QACnF;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO,KAAK,KAAK,YAAY,KAAK,IAAI,KAAK,EAAE,UAAU,IAAI,KAAK,IAAI,KAAK,EAAE,SAAS;AAAA,QACpF,MAAM,KAAK,KAAK,YAAY,IAAI;AAAA,QAChC,iBAAiB;AAAA,QACjB,QAAQ;AAAA,QACR,YAAY,MAAM;AAChB,eAAK,KAAK,YAAY,CAAC,KAAK,KAAK,WAAW,KAAK,MAAM,YAAY,KAAK,KAAK;AAAA,QAC/E;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACL,UAAM,IAAI,KAAK,MAAM,QAAQ;AAC7B,WAAO;AAAA,MACL,cAAc,KAAK,KAAK;AAAA,MACxB,WAAW,KAAK,KAAK;AAAA,MACrB,SAAS;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,SAAK,MAAM,QAAQ;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,GAAG,IAAI,QAAQ,IAAI,QAAQ;AACnC,UAAM,IAAI,KAAK,QAAQ;AACvB,WAAO,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,KAAK,UAAU,KAAK,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,cAAc;AACvB,WAAO,EAAE,MAAM,CAAC,SAAS,MAAM,MAAM,IAAI,EAAE;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,GAAG;AACT,UAAM,IAAI,EAAE,OAAO,MAAM,IAAI,EAAE,cAAc,qCAAqC,GAAG,IAAI,MAAM,KAAK,EAAE,iBAAiB,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,MAAM,KAAK,EAAE,iBAAiB,QAAQ,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;AACzM,SAAK,OAAO;AAAA,MACV,cAAc,MAAM;AAAA,MACpB,SAAS;AAAA,IACX,GAAG,KAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,YAAY,KAAK,OAAO,CAAC;AAAA,EACvE;AACF;", "names": []}