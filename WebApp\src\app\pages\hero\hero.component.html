<div class="h-[calc(100vh - 74px)] w-full bg-[var(--background-light-gray)]" style="height: calc(100vh - 74px);">

  <!-- Main content with splitter -->
  <as-split direction="horizontal" [gutterSize]="2" [useTransition]="true" (dragEnd)="onSplitDragEnd($event)"
    (dragProgress)="onSplitDragProgress($event)" (gutterDblClick)="onGutterDoubleClick($event)">

    <!-- Main Chat Container -->
    <as-split-area [size]="mainContentSplitSize" [minSize]="50" class="main-content-area">
      <div class="flex flex-col h-full transition-all duration-300 ease-in-out"> <!-- Chat Messages -->
        <div #chatContainer (scroll)="onChatScroll()" class="flex-1 overflow-y-auto px-2 py-4 sm:px-4 sm:py-6">
          <div class="w-[90%] mx-auto">
            <!-- Welcome Message when no chat history -->
            <div *ngIf="chatHistory?.history?.length === 0" class="mb-6 sm:mb-8 md:mb-10">
              <div class="flex flex-col items-center justify-center p-4 sm:p-6">
                <!-- Header -->
                <div class="mb-8 sm:mb-10 flex gap-2 items-center justify-center">
                  <div
                    class="flex items-center justify-center min-w-8 h-8 sm:min-w-10 sm:h-10 bg-[var(--primary-purple)] rounded-[var(--border-radius-small)]">
                    <i class="ri-sparkling-2-fill text-[var(--background-white)] text-lg sm:text-xl"></i>
                  </div>
                  <h1 class="font-[var(--font-weight-bold)] m-0 sm:text-[26px] md:text-[28px] text-[var(--text-dark)]">
                    Hello, {{auth.getUserName() || 'there'}}! How Can I Help You Today?
                  </h1>
                </div>

                <!-- Suggestion Cards -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 max-w-3xl w-full">
                  @for (suggestion of suggestionList; track $index) {
                  <div (click)="addSuggestionToChat(suggestion)" class="bg-[var(--hover-blue-gray)] p-4 rounded-[var(--border-radius-large)] shadow-[var(--box-shadow)] border-2
              {{ suggestion.isUserPrompt ? 'border-[var(--primary-purple)]' : 'border-transparent' }}
              hover:border-[var(--secondary-purple)] transition-[var(--transition-default)] cursor-pointer relative">
                    <p class="sm:text-[15px] md:text-[16px] text-[var(--text-dark)] pr-16">
                      {{ suggestion.text }} <i class="ri-arrow-right-s-line text-[var(--text-medium-gray)]"></i>
                    </p>
                  </div>
                  }
                </div>
              </div>
            </div>

            <!-- Existing chat messages -->
            <div *ngFor="let message of chatHistory.history" class="flex flex-col gap-4">
              <!-- User Message -->
              <div
                class="flex group gap-2 sm:gap-3 items-start justify-start bg-[var(--background-white)] rounded-[8px] shadow-[var(--box-shadow)] px-2 py-2 sm:px-4 sm:py-3">
                <div
                  class="h-8 w-8 sm:h-10 sm:w-10 bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] flex items-center justify-center">
                  <i class="ri-user-2-fill text-lg sm:text-xl text-[var(--primary-purple)]"></i>
                </div>
                <div class="flex flex-col w-full"> <span
                    class="text-[var(--text-medium-gray)] text-xs sm:text-sm px-1">You • {{
                    message.createdAt | relativeTime | async }}</span>
                  <div class="flex gap-1 justify-between" *ngIf="!message.editingMode">
                    <div
                      class="text-sm sm:text-[15px] md:text-[16px] text-[var(--text-dark)] p-2 sm:p-3 rounded-[8px] shadow-[var(--box-shadow)]">
                      <span #messageText>{{ message.message }}</span>
                    </div>
                    <button
                      class="p-2 outline-none border-none h-8 w-8 sm:h-10 sm:w-10 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)]
                opacity-0 group-hover:opacity-100 transition-all duration-300 ease-in-out hover:bg-[var(--secondary-purple)] mt-2 flex items-center justify-center cursor-pointer text-[var(--text-medium-gray)] hover:text-[var(--primary-purple)]"
                      (click)="message.editingMode = !message.editingMode; previousResponse = message.message">
                      <i class="ri-pencil-line text-lg sm:text-xl"></i>
                    </button>
                  </div>
                  <div
                    class="editmode flex items-center bg-[var(--hover-blue-gray)] p-2 sm:p-3 rounded-2xl w-full relative"
                    *ngIf="message.editingMode"> <textarea type="text" [(ngModel)]="message.message"
                      placeholder="What can I help you with, {{auth.getUserName() || 'there'}}? Type @ to mention an agent"
                      class="outline-none resize-none bg-transparent px-3 sm:px-4 border-none text-sm sm:text-[16px] md:text-[17px] flex-1 min-h-[80px] sm:min-h-[100px] max-h-[150px] sm:max-h-[200px] line-height-[var(--line-height)] text-[var(--text-dark)] placeholder-[var(--text-medium-gray)]"
                      (input)="adjustInputHeight()" (keydown)="handleKeyDown($event)"></textarea>
                    <div class="flex flex-col sm:flex-row gap-2 absolute right-2 bottom-2 sm:right-3 sm:bottom-3">
                      <button (click)="message.editingMode = false; message.message = previousResponse"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-[var(--text-dark)] hover:bg-[#2F2F2F] transition-[var(--transition-default)] text-xs sm:text-sm rounded-xl outline-none border-none cursor-pointer text-[var(--background-white)] min-h-[40px]">
                        Cancel
                      </button>
                      <button (click)="editMessage(message)"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-[var(--background-white)] hover:bg-[var(--hover-blue-gray)] text-xs sm:text-sm rounded-xl outline-none border-none cursor-pointer min-h-[40px] text-[var(--text-dark)]">
                        Send
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- AI Response -->
              <div class="flex w-full mb-4">
                <!-- <div class="w-full">
                  <div class="flex items-center justify-center"></div>
                </div> -->
                @if (message.responses?.length == 1) {
                <div class="flex-1 group w-full" *ngFor="let res of message.responses">
                  <div
                    class="bg-[var(--background-white)] rounded-[8px] shadow-[var(--box-shadow)] px-2 py-2 sm:px-4 sm:py-3">
                    <!-- Header: Icon and Timestamp -->
                    <div class="flex gap-2 w-full">
                      <div
                        class="flex items-center justify-center w-[40px] h-[40px] bg-[var(--primary-purple)] rounded-[var(--border-radius-small)]">
                        <i class="ri-sparkling-2-fill text-[var(--background-white)] text-lg sm:text-xl"></i>
                      </div>
                      <div class="flex justify-between mb-1" style="width: calc(100% - 40px);">
                        <div class="flex justify-center px-2 gap-2 flex-col w-full">
                          <div class="text-[var(--text-medium-gray)] text-xs sm:text-sm px-1 flex items-start gap-1">
                            <div class="flex gap-[1px] flex-col justify-center">
                              <div>
                                <span class="text-[var(--text-dark)]">{{message.agentName}} </span>
                              </div>
                              <div>{{message.workspaceName}} </div>
                            </div>
                            <div>
                              • {{ message.createdAt | relativeTime | async }}
                            </div>
                          </div>
                          <div class="text-sm sm:text-[15px] md:text-[16px] text-[var(--text-dark)]">
                            <!-- SQL View - Only show when response type is sqlView -->
                            <div *ngIf="res.responseType === 'sqlView'" class="sql-view-container">
                              <!-- Regular markdown content -->
                              <markdown
                                [data]="hasSqlContent(res.response) ? extractNonSqlContent(res.response) : res.response">
                              </markdown>

                              <!-- SQL Code Box (only shown if SQL content exists) -->
                              <div *ngIf="hasSqlContent(res.response)" class="sql-code-container mt-4 mb-4 relative">
                                <div class="bg-[#1E1E1E] rounded-md overflow-hidden">
                                  <div
                                    class="flex justify-between items-center px-3 py-2 bg-[#2A2A2A] border-b border-[#333333]">
                                    <span class="text-white text-xs font-medium">SQL Query</span>
                                    <div class="flex gap-2">
                                      <button (click)="copySqlContent(extractSqlContent(res.response))"
                                        class="text-xs bg-[#333333] hover:bg-[#444444] text-white px-2 py-1 rounded flex items-center gap-1 transition-colors">
                                        <i class="ri-file-copy-line text-xs"></i>
                                        <span>Copy</span>
                                      </button> <button
                                        (click)="openSqlConnectionDialog(extractSqlContent(res.response))"
                                        class="text-xs bg-[#0078D4] hover:bg-[#106EBE] text-white px-2 py-1 rounded flex items-center gap-1 transition-colors">
                                        <i class="ri-play-fill text-xs"></i>
                                        <span>Run SQL</span>
                                      </button>
                                    </div>
                                  </div>
                                  <div class="p-3 overflow-x-auto max-w-full">
                                    <pre
                                      class="text-white text-sm font-mono whitespace-pre"><code class="language-sql">{{extractSqlContent(res.response)}}</code></pre>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Blog View - Only show when response type is blogView -->
                            <div *ngIf="res.responseType === 'blogView'" class="blog-view-container">
                              <div
                                class="blog-content bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-large)]">

                                <!-- Blog Header with Gradient -->
                                <div
                                  class="blog-header relative bg-gradient-to-r from-[var(--primary-purple)] to-[var(--secondary-purple)] px-8 py-6">
                                  <!-- Decorative Pattern -->
                                  <div class="absolute inset-0 opacity-10">
                                    <div class="absolute top-4 right-4 w-20 h-20 border-2 border-white rounded-full">
                                    </div>
                                    <div
                                      class="absolute bottom-4 left-4 w-16 h-16 border border-white rounded-lg rotate-45">
                                    </div>
                                    <div class="absolute top-1/2 right-1/3 w-8 h-8 bg-white rounded-full opacity-20">
                                    </div>
                                  </div>

                                  <!-- Header Content -->
                                  <div class="relative z-10">
                                    <div class="flex items-start justify-between mb-3">
                                      <div class="flex items-center gap-2 text-white/80 text-sm">
                                        <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                                          <i class="ri-article-line text-white"></i>
                                        </div>
                                        <span class="font-medium">Blog Post</span>
                                      </div>
                                      <div
                                        class="flex items-center gap-1 text-white/70 text-xs bg-white/10 px-2 py-1 rounded-full">
                                        <i class="ri-time-line"></i>
                                        <span>{{ message.createdAt | date:'MMM d, y' }}</span>
                                      </div>
                                    </div>

                                    <h1 class="text-2xl font-bold text-white leading-tight mb-2">
                                      {{ extractBlogTitle(res.response) }}
                                    </h1>

                                    <div class="flex items-center gap-4 text-white/80 text-sm">
                                      <div class="flex items-center gap-1">
                                        <i class="ri-user-line"></i>
                                        <span>{{ message.agentName || 'AI Assistant' }}</span>
                                      </div>
                                      <div class="flex items-center gap-1">
                                        <i class="ri-calendar-line"></i>
                                        <span>{{ message.createdAt | date:'shortTime' }}</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- Blog Content with Enhanced Styling -->
                                <div class="blog-body p-8">
                                  <div class="markdown-blog prose prose-lg max-w-none">
                                    <markdown [data]="res.response"></markdown>
                                  </div>
                                </div>

                                <!-- Enhanced Blog Footer -->
                                <div
                                  class="blog-footer bg-gradient-to-r from-[var(--hover-blue-gray)] to-[var(--background-light-gray)] px-8 py-4 border-t border-[var(--hover-blue-gray)]">
                                  <div class="flex items-center justify-between">
                                    <!-- Left Side - Engagement Stats -->
                                    <div class="flex items-center gap-6 text-[var(--text-medium-gray)] text-sm">
                                      <div class="flex items-center gap-1">
                                        <i class="ri-eye-line"></i>
                                        <span>Ready to share</span>
                                      </div>
                                      <div class="flex items-center gap-1">
                                        <i class="ri-file-text-line"></i>
                                        <span>{{ (res.response.length / 5) | number:'1.0-0' }} words</span>
                                      </div>
                                    </div>

                                    <!-- Right Side - Action Buttons -->
                                    <div class="flex gap-3">
                                      <button (click)="copyBlogContent(res.response)"
                                        class="group bg-[var(--primary-purple)] text-[var(--background-white)] rounded-[6px] px-3 py-1.5 sm:px-4 sm:py-2 cursor-pointer hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] disabled:opacity-50 border-none outline-none flex items-center gap-1 min-h-[40px] sm:min-h-[48px]">
                                        <i
                                          class="ri-file-copy-line text-sm group-hover:scale-110 transition-transform"></i>
                                        <span class="font-medium">Copy</span>
                                      </button>

                                      <button (click)="openBlogShareDialog(res.response)"
                                        class="group bg-[var(--primary-purple)] text-[var(--background-white)] rounded-[6px] px-3 py-1.5 sm:px-4 sm:py-2 cursor-pointer hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] disabled:opacity-50 border-none outline-none flex items-center gap-1 min-h-[40px] sm:min-h-[48px]">
                                        <i class="ri-share-line text-sm group-hover:scale-110 transition-transform"></i>
                                        <span class="font-medium">Share</span>
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div> <!-- Email View - Only show when response type is emailView -->

                            <div *ngIf="res.responseType === 'emailView'" class="email-view-container">
                              <div
                                class="email-content border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] shadow-[var(--box-shadow)] overflow-hidden">
                                <!-- Email Form -->
                                <form class="p-5">
                                  <!-- Email Header -->
                                  <div class="form-header -m-5 p-5 mb-6">
                                    <div class="flex justify-between items-center">
                                      <h2 class="text-lg font-semibold text-[var(--background-white)]">
                                        <i class="ri-mail-send-line mr-2"></i>Compose Email
                                      </h2>
                                      <div class="flex items-center">
                                        <span class="text-sm text-white opacity-80">AI-generated draft ready to
                                          edit</span>
                                      </div>
                                    </div>
                                  </div>
                                  <!-- Subject Field -->
                                  <div class="mb-4">
                                    <div class="flex items-center mb-1">
                                      <label class="block text-sm font-medium text-[var(--text-dark)]"
                                        for="emailSubject">Subject</label>
                                      <div class="ml-2 text-xs text-[var(--text-medium-gray)]">(required)</div>
                                    </div>
                                    <input type="text" id="emailSubject" #emailSubject
                                      [value]="extractEmailSubject(res.response)"
                                      class="w-full p-2 rounded-[var(--border-radius-small)] focus:outline-none focus:border-[var(--primary-purple)]"
                                      placeholder="Enter email subject" />
                                  </div>

                                  <!-- To Field -->
                                  <div class="mb-4">
                                    <div class="flex items-center mb-1">
                                      <label class="block text-sm font-medium text-[var(--text-dark)]"
                                        for="emailTo">To</label>
                                      <div class="ml-2 text-xs text-[var(--text-medium-gray)]">(required)</div>
                                    </div>
                                    <div class="relative">
                                      <i
                                        class="ri-user-line absolute left-3 top-[28%] transform -translate-y-1/2 text-[var(--text-medium-gray)]"></i>
                                      <input type="text" id="emailTo" #emailTo [value]="extractEmailTo(res.response)"
                                        class="w-full p-2 pl-8 rounded-[var(--border-radius-small)] focus:outline-none focus:border-[var(--primary-purple)]"
                                        placeholder="<EMAIL> (for multiple use CC field)" />
                                    </div>
                                  </div>

                                  <!-- CC Field -->
                                  <div class="mb-4">
                                    <div class="flex items-center justify-between mb-1">
                                      <div class="flex items-center">
                                        <label class="block text-sm font-medium text-[var(--text-dark)]"
                                          for="emailCc">CC</label>
                                        <div class="ml-2 text-xs text-[var(--text-medium-gray)]">(optional)</div>
                                      </div>
                                    </div>
                                    <div class="relative">
                                      <i
                                        class="ri-user-shared-line absolute left-3 top-[28%] transform -translate-y-1/2 text-[var(--text-medium-gray)]"></i>
                                      <input type="text" id="emailCc" #emailCc [value]="extractEmailCc(res.response)"
                                        class="w-full p-2 pl-8 rounded-[var(--border-radius-small)] focus:outline-none focus:border-[var(--primary-purple)]"
                                        placeholder="<EMAIL>, <EMAIL>" />
                                    </div>
                                  </div>

                                  <!-- Email Body -->
                                  <div class="mb-4">
                                    <div class="flex items-center justify-between mb-1">
                                      <label class="block text-sm font-medium text-[var(--text-dark)]"
                                        for="emailBody">Message</label>
                                      <button type="button"
                                        class="text-xs text-[var(--primary-purple)] hover:text-[var(--secondary-purple)]"
                                        (click)="emailBody.value = extractEmailBody(res.response)">
                                        <i class="ri-refresh-line"></i> Reset to AI version
                                      </button>
                                    </div>
                                    <textarea id="emailBody" #emailBody [value]="extractEmailBody(res.response)"
                                      class="w-full p-3 rounded-[var(--border-radius-small)] focus:outline-none focus:border-[var(--primary-purple)] min-h-[180px] font-normal"
                                      placeholder="Enter your message here..."></textarea>
                                  </div>
                                </form>
                                <!-- Email Footer with Actions -->
                                <div class="form-footer px-5 py-3 flex justify-between items-center">
                                  <div class="text-[var(--text-medium-gray)] text-sm flex items-center">
                                    <i class="ri-information-line mr-1"></i>
                                    <span>Your email will be sent with HTML formatting for better readability.</span>
                                  </div>
                                  <div class="flex gap-2">
                                    <button type="button"
                                      (click)="copyEmailContent(emailSubject.value, emailTo.value, emailCc.value, emailBody.value)"
                                      class="copy-button text-sm px-3 py-2 rounded-[var(--border-radius-small)] flex items-center gap-1 transition-[var(--transition-default)] hover:bg-[var(--hover-blue-gray)]">
                                      <i class="ri-file-copy-line text-sm"></i>
                                      <span>Copy All</span>
                                    </button>
                                    <button type="button"
                                      (click)="sendEmail(emailSubject.value, emailTo.value, emailCc.value, emailBody.value)"
                                      class="text-sm bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] text-[var(--background-white)] px-3 py-2 rounded-[var(--border-radius-small)] flex items-center gap-1">
                                      <i class="ri-send-plane-fill text-sm"></i>
                                      <span>Send Email</span>
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div> <!-- Default View for other response types -->
                            <div
                              *ngIf="res.responseType !== 'sqlView' && res.responseType !== 'blogView' && res.responseType !== 'emailView' && !res.responseType">
                              <markdown [data]="res.response"></markdown>
                            </div>

                            <div class="flex items-center gap-2 mt-3">
                              <!-- Web Search Indicator (Clickable) -->
                              <div
                                (click)="toggleSearchResultsSidebar(chatSource.chatSourceDescriptions, chatSource.source)"
                                *ngFor="let chatSource of res.chatSources"
                                class="flex items-center bg-[#1E1E1E] text-white text-xs rounded-full px-2 py-1.5 border border-[#333333] cursor-pointer hover:bg-[#2A2A2A] transition-all duration-200"
                                title="Click to view {{chatSource.source}} references">
                                <i class="ri-global-line mr-1"></i>
                                <span>{{ chatSource.chatSourceDescriptions.length }} {{chatSource.source}}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Action Buttons Section -->
                    <div class="flex justify-start items-center ">
                      <div
                        class="flex flex-wrap items-center gap-1 p-1 rounded-md bg-[var(--background-white)] shadow-sm border border-[var(--hover-blue-gray)] *:border-none *:outline-none *:bg-transparent *:text-[16px] sm:*:text-[18px] *:cursor-pointer">
                        <button
                          class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                          (click)="res.copied = true; copyContent(res.response, res.copied)" title="Copy to clipboard">
                          <i
                            [class]="res.copied ? 'ri-check-line text-green-400' : 'ri-file-copy-line text-[var(--text-medium-gray)]'"></i>
                        </button>
                        <button
                          class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                          (click)="textToSpeech(res.response)" *ngIf="!isSpeaking" title="Read aloud">
                          <i class="ri-volume-up-line text-[var(--text-medium-gray)]"></i>
                        </button>
                        <button
                          class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                          (click)="stopSpeech()" *ngIf="isSpeaking" title="Stop reading">
                          <i class="ri-stop-circle-line text-[var(--text-medium-gray)]"></i>
                        </button>
                        <button
                          class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                          (click)="regenerateResponse(message)" title="Regenerate response">
                          <i
                            [class]="message.isRegenerating ? 'ri-restart-line text-[var(--text-medium-gray)] block animate-spin' : 'ri-restart-line text-[var(--text-medium-gray)]'"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                } @else {
                <!-- <h1 class="text-[200px] font-bold">Report Me</h1> -->
                }
              </div>
            </div>
            <div *ngIf="isMessageLoading" class="flex justify-start items-center gap-2 text-[var(--text-medium-gray)]">
              <div class="flex gap-1">
                <span
                  class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce [animation-delay:-0.3s]"></span>
                <span
                  class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce [animation-delay:-0.15s]"></span>
                <span class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce"></span>
              </div>
              <span class="text-xs sm:text-sm">Generating response...</span>
            </div>
          </div>
        </div> <!-- Scroll Down Arrow Button -->
        <div *ngIf="showScrollButton" (click)="scrollToBottom()"
          class="fixed cursor-pointer bottom-[208px] right-[70px] p-2 flex justify-center items-center bg-[var(--primary-purple)] rounded-full text-[var(--background-white)]"
          style="z-index: 9999;">
          <i class="ri-arrow-down-s-line text-xl "></i>
        </div> <!-- Input Section -->
        <div class="p-2 sm:p-4 border-t border-[var(--hover-blue-gray)] relative">
          <div class="w-[90%] mx-auto">
            <div
              class="flex relative items-end justify-start bg-[var(--background-white)] rounded-[8px] px-2 py-2 sm:px-4 sm:py-3 shadow-[var(--box-shadow)]">

              <!-- Left-Side Icons -->
              <div class="w-full flex items-center gap-2  left-0 ">
                <div class="flex items-center gap-2 absolute left-2 sm:left-4 z-10 bottom-2">
                  <!-- Tools icon with counter -->
                  <div class="relative ml-2">
                    <div (click)="toggleAgentSidebar()"
                      class="flex bg-[var(--dialog-bg)] items-center gap-2 cursor-pointer border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] px-2 py-1 transition-[var(--transition-default)] hover:bg-[var(--hover-blue-gray)]"
                      title="View available agents for this workspace">
                      <i class="ri-tools-fill text-[var(--text-dark)]"></i>
                      <span
                        class="text-xs font-semibold bg-[var(--primary-purple)] text-white rounded-full px-1.5 py-0.5 min-w-[20px] text-center">
                        {{workspaceAgents.length}}
                      </span>
                    </div>
                  </div>

                  <!-- Workspace selection dropdown -->
                  <div class="relative">
                    <div
                      class="flex bg-[var(--dialog-bg)] items-center gap-2 cursor-pointer border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] px-2 py-1 transition-[var(--transition-default)] hover:bg-[var(--hover-blue-gray)] shadow-[0_0_1px_var(--shadow-color)]"
                      (click)="toggleWorkspaceList.classList.toggle('opacity-0'); toggleWorkspaceList.classList.toggle('hidden')">
                      <span class="font-bold text-[var(--text-dark)]">{{selectedWorkspace}}</span>
                      <span class="text-[var(--font-size-body)]">
                        <i class="ri-arrow-down-s-line text-[var(--text-dark)]"></i>
                      </span>
                    </div>
                    <div #toggleWorkspaceList
                      class="bg-[var(--dialog-bg)] hidden opacity-0 absolute bottom-[calc(100%+0.5rem)] left-0 rounded-[var(--border-radius-large)] p-4 w-56 text-[var(--text-dark)] shadow-[var(--box-shadow)] max-h-[400px] overflow-hidden"
                      style="z-index: 1000;">
                      <div
                        class="space-y-2 overflow-y-scroll h-40 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:bg-[var(--hover-blue-gray)] [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-transparent hover:[&::-webkit-scrollbar-thumb]:bg-[var(--secondary-purple)] hover:[&::-webkit-scrollbar-thumb]:cursor-pointer">
                        <!-- Workspace options from API -->
                        <div
                          class="flex items-center max-h-40 overflow-hidden cursor-pointer p-2 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)] duration-300"
                          *ngFor="let workspace of workspaces"
                          (click)="selectWorkspace(workspace.title || ''); toggleWorkspaceList.classList.toggle('opacity-0'); toggleWorkspaceList.classList.toggle('hidden');">
                          <span class="font-[var(--font-family)]">{{workspace.title}}</span>
                          <span class="ml-auto text-[var(--primary-purple)]"
                            *ngIf="workspace.title === selectedWorkspace">
                            <i class="ri-check-line"></i>
                          </span>
                        </div>
                        <!-- Show message when no workspaces are available -->
                        <div *ngIf="workspaces.length === 0" class="p-2 text-[var(--text-medium-gray)]">
                          No workspaces available
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Selected Agent Indicator -->
                  <div *ngIf="selectedAgent" class="relative">
                    <div
                      class="flex bg-[var(--dialog-bg)] items-center gap-2 cursor-pointer border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] px-2 py-1 transition-[var(--transition-default)] hover:bg-[var(--hover-blue-gray)] shadow-[0_0_1px_var(--shadow-color)]"
                      title="Currently selected agent">
                      <i class="ri-user-2-line text-[var(--primary-purple)]"></i>
                      <span class="font-bold text-[var(--text-dark)]">
                        {{selectedAgent}}
                      </span>
                      <button (click)="clearSelectedAgent()"
                        class="text-[var(--text-medium-gray)] hover:text-[var(--primary-purple)]">
                        <i class="ri-close-line"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <!-- Textarea -->
                <div class="flex items-center gap-2 w-full pb-[40px] sm:pb-[50px] relative">
                  <textarea #chatInput [(ngModel)]="userInput.message" (input)="onInput($event)"
                    placeholder="What can I help you with, {{auth.getUserName() || 'there'}}? Type @ to mention an agent"
                    class="outline-none resize-none bg-transparent px-3 sm:px-4 border-none text-sm sm:text-[16px] md:text-[17px] flex-1 min-h-[80px] sm:min-h-[100px] max-h-[150px] sm:max-h-[200px] line-height-[var(--line-height)] text-[var(--text-dark)] placeholder-[var(--text-medium-gray)]"
                    autofocus (keydown)="handleKeyDown($event)"></textarea>

                  <!-- Agent Mention Dropdown -->
                  <div *ngIf="mentionDropdownVisible && mentionFilteredAgents.length > 0"
                    class="absolute bottom-[calc(100%+0.5rem)] left-0 bg-[var(--background-white)] rounded-[var(--border-radius-large)] shadow-[var(--box-shadow)] w-full max-w-[400px] z-50 border border-[var(--hover-blue-gray)]">
                    <div class="p-3 border-b border-[var(--hover-blue-gray)]">
                      <div class="flex items-center justify-between">
                        <h3 class="text-[var(--text-dark)] font-semibold">
                          <span *ngIf="selectedWorkspace">Agents for {{selectedWorkspace}}</span>
                          <span *ngIf="!selectedWorkspace">Select Agent</span>
                        </h3>
                        <span class="text-xs text-[var(--text-medium-gray)]">Use arrow keys or click to select</span>
                      </div>
                    </div>
                    <div
                      class="max-h-[300px] overflow-y-auto p-2 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:bg-[var(--hover-blue-gray)] [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-transparent">
                      <div *ngFor="let agent of mentionFilteredAgents; let i = index"
                        (click)="selectMentionedAgent(agent)"
                        class="mention-item p-3 cursor-pointer rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                        [ngClass]="{'bg-[var(--hover-blue-gray)]': selectedMentionIndex === i, 'hover:bg-[var(--hover-blue-gray)]': selectedMentionIndex !== i}">
                        <div class="flex items-start gap-2">
                          <div class="flex-shrink-0">
                            <i class="ri-user-2-line text-[var(--primary-purple)] text-lg"></i>
                          </div>
                          <div class="flex-1">
                            <p class="text-sm font-semibold text-[var(--text-dark)]">{{ agent.agentName }}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Prompt Dialog Popup -->
                  <div #promptDialog *ngIf="showPromptDialog"
                    class="absolute bottom-[calc(100%+0.5rem)] left-0 bg-[var(--background-white)] rounded-[var(--border-radius-large)] shadow-[var(--box-shadow)] w-full max-w-[600px] z-50 border border-[var(--hover-blue-gray)]">
                    <div class="p-3 border-b border-[var(--hover-blue-gray)]">
                      <div class="flex items-center justify-between">
                        <h3 class="text-[var(--text-dark)] font-semibold">Prompts</h3>
                        <span class="text-xs text-[var(--text-medium-gray)]">Type to search or use arrow keys to
                          navigate</span>
                      </div>
                    </div>
                    <div
                      class="max-h-[300px] overflow-y-auto p-2 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:bg-[var(--hover-blue-gray)] [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-transparent">
                      <div *ngIf="filteredPrompts.length === 0" class="p-4 text-center text-[var(--text-medium-gray)]">
                        No matching prompts found
                      </div>
                      <div *ngFor="let prompt of filteredPrompts; let i = index" (click)="selectPrompt(prompt)"
                        class="prompt-item p-3 cursor-pointer rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                        [ngClass]="{'bg-[var(--hover-blue-gray)]': selectedPromptIndex === i, 'hover:bg-[var(--hover-blue-gray)]': selectedPromptIndex !== i}">
                        <div class="flex items-start gap-2">
                          <div class="flex-shrink-0">
                            <span
                              class="inline-block px-2 py-1 bg-[var(--primary-purple)] text-white text-xs rounded-md">
                              {{ prompt.shortMessage || 'Prompt' }}
                            </span>
                          </div>
                          <div class="flex-1">
                            <p class="text-sm text-[var(--text-dark)] line-clamp-2">{{ prompt.prompt }}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Mic/Stop Buttons -->
                <div class="flex items-end h-full self-end">
                  <div class="flex gap-2 mr-2">
                    <button (click)="startRecording()" *ngIf="!isRecording"
                      class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)] bg-transparent border-none text-[var(--text-medium-gray)] cursor-pointer text-lg sm:text-xl">
                      <i class="ri-mic-line"></i>
                    </button>
                    <button (click)="stopRecording()" *ngIf="isRecording"
                      class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)] bg-transparent border-none text-[var(--text-medium-gray)] cursor-pointer text-lg sm:text-xl">
                      <i class="ri-stop-circle-line"></i>
                    </button>
                  </div>
                  <!-- Send Button -->
                  <div class="flex items-end gap-2">
                    <button (click)="sendMessage()" [disabled]="isMessageLoading"
                      class="bg-[var(--primary-purple)] text-[var(--background-white)] rounded-[6px] px-3 py-1.5 sm:px-4 sm:py-2 cursor-pointer hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] disabled:opacity-50 border-none outline-none flex items-center gap-1 min-h-[40px] sm:min-h-[48px]">
                      <span class="text-sm sm:text-[16px] md:text-[17px]">Send</span>
                      <i [class]="isMessageLoading ? 'ri-stop-circle-line' : 'ri-send-plane-fill'"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </as-split-area>

    <!-- Right Sidebar Area - Fixed initial width of 350px -->
    <as-split-area *ngIf="showSearchResultsSidebar || isAgentSidebarOpen" [size]="rightSidebarSplitSize" [minSize]="15"
      class="right-sidebar-area">
      <div class="h-full w-full relative sidebar-container">
        <!-- Width indicator (visible during drag) -->
        <div class="width-indicator" *ngIf="isDragging">{{ rightSidebarWidth | number:'1.0-0' }}px</div>

        <!-- Source References Sidebar Component -->
        <app-source-references *ngIf="showSearchResultsSidebar" [showSidebar]="showSearchResultsSidebar"
          [searchResults]="searchResults" [currentSourceName]="currentSourceName"
          (closeSidebar)="showSearchResultsSidebar = false">
        </app-source-references>

        <!-- Agent Tools Sidebar Component -->
        <app-agent-sidebar *ngIf="isAgentSidebarOpen" [isAgentSidebarOpen]="isAgentSidebarOpen"
          [agentSidebarTitle]="agentSidebarTitle"  (onClose)="toggleAgentSidebar()"
          (onSelectAgent)="selectAgent($event)">
        </app-agent-sidebar>
      </div>
    </as-split-area>
  </as-split>
</div>
