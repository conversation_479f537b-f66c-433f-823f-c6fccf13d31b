{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-no-animation.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzNoAnimationDirective {\n  constructor() {\n    this.animationType = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    this.nzNoAnimation = false;\n  }\n  static {\n    this.ɵfac = function NzNoAnimationDirective_Factory(t) {\n      return new (t || NzNoAnimationDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzNoAnimationDirective,\n      selectors: [[\"\", \"nzNoAnimation\", \"\"]],\n      hostVars: 2,\n      hostBindings: function NzNoAnimationDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"nz-animate-disabled\", ctx.nzNoAnimation || ctx.animationType === \"NoopAnimations\");\n        }\n      },\n      inputs: {\n        nzNoAnimation: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"nzNoAnimation\", \"nzNoAnimation\", booleanAttribute]\n      },\n      exportAs: [\"nzNoAnimation\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzNoAnimationDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzNoAnimation]',\n      exportAs: 'nzNoAnimation',\n      standalone: true,\n      host: {\n        '[class.nz-animate-disabled]': `nzNoAnimation || animationType === 'NoopAnimations'`\n      }\n    }]\n  }], null, {\n    nzNoAnimation: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzNoAnimationModule {\n  static {\n    this.ɵfac = function NzNoAnimationModule_Factory(t) {\n      return new (t || NzNoAnimationModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzNoAnimationModule,\n      imports: [NzNoAnimationDirective],\n      exports: [NzNoAnimationDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzNoAnimationModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzNoAnimationDirective],\n      exports: [NzNoAnimationDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzNoAnimationDirective, NzNoAnimationModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAQA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,gBAAgB,OAAO,uBAAuB;AAAA,MACjD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAAwB;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACrC,UAAU;AAAA,MACV,cAAc,SAAS,oCAAoC,IAAI,KAAK;AAClE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,uBAAuB,IAAI,iBAAiB,IAAI,kBAAkB,gBAAgB;AAAA,QACnG;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,eAAe,CAAI,WAAa,4BAA4B,iBAAiB,iBAAiB,gBAAgB;AAAA,MAChH;AAAA,MACA,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,MACZ,UAAU,CAAI,wBAAwB;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,+BAA+B;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAqB;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,sBAAsB;AAAA,MAChC,SAAS,CAAC,sBAAsB;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,sBAAsB;AAAA,MAChC,SAAS,CAAC,sBAAsB;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}