<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 65px);">
  <!-- Main Container -->
  <div class="flex-1 overflow-hidden flex flex-col">
    <!-- Header - Teams-style with compact vertical spacing -->
    <div
      class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50">
      <!-- Left side with title and count -->
      <div class="flex items-center gap-2">
        <i class="ri-file-list-line text-[var(--primary-purple)] text-xl"></i>
        <h1 class="text-lg font-medium text-[var(--text-dark)]">Projects</h1>
        <div
          class="inline-flex items-center justify-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]">
          {{ products.length }}
        </div>
      </div>

      <!-- Right side with controls -->
      <div class="flex items-center gap-2">
        <!-- View Toggle Buttons -->
        <div
          class="flex items-center bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md overflow-hidden">
          <button
            class="h-8 px-3 py-1 text-sm font-medium transition-all duration-200 flex items-center gap-1 border-none"
            [style.background-color]="viewType === 'table' ? 'var(--primary-purple)' : 'transparent'"
            [style.color]="viewType === 'table' ? 'white' : 'var(--text-medium-gray)'"
            [class.hover:bg-[var(--hover-blue-gray)]]="viewType !== 'table'" (click)="toggleView('table')"
            title="Table View">
            <i class="ri-list-view"></i>
            <span class="hidden sm:inline">Table</span>
          </button>
          <button
            class="h-8 px-3 py-1 text-sm font-medium transition-all duration-200 flex items-center gap-1 border-none"
            [style.background-color]="viewType === 'card' ? 'var(--primary-purple)' : 'transparent'"
            [style.color]="viewType === 'card' ? 'white' : 'var(--text-medium-gray)'"
            [class.hover:bg-[var(--hover-blue-gray)]]="viewType !== 'card'" (click)="toggleView('card')"
            title="Card View">
            <i class="ri-grid-fill"></i>
            <span class="hidden sm:inline">Cards</span>
          </button>
        </div>

        <!-- Add Button -->
        <button
          class="h-8 px-3 py-1 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center justify-center gap-1"
          (click)="goToProject(0)">
          <i class="ri-add-line"></i>
          <span>New Request</span>
        </button>
      </div>
    </div>

    <!-- Workspace Navigation Buttons (only visible on all-request page) -->
    <div class="px-4 pt-4" *ngIf="isAllRequestPage">
      <div class="flex flex-wrap gap-3 mb-6">
        <button
          class="flex items-center gap-2 px-4 py-3 rounded-lg font-medium text-sm border transition-all duration-300 shadow-sm"
          title="View All Projects" (click)="onAllProjects()"
          [ngClass]="{
            'text-white border-[var(--primary-purple)]': workspaceType === 'allProjects',
            'bg-[var(--background-white)] text-[var(--text-dark)] border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)] hover:text-[var(--primary-purple)] hover:bg-[var(--background-light-gray)]': workspaceType !== 'allProjects'
          }"
          [style.background-color]="workspaceType === 'allProjects' ? 'var(--primary-purple)' : ''">
          <i class="ri-folder-line text-base"></i>
          All Projects
        </button>
        <button
          class="flex items-center gap-2 px-4 py-3 rounded-lg font-medium text-sm border transition-all duration-300 shadow-sm"
          title="Assigned to Me" (click)="onAssigned()"
          [ngClass]="{
            'text-white border-[var(--primary-purple)]': workspaceType === 'assigned',
            'bg-[var(--background-white)] text-[var(--text-dark)] border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)] hover:text-[var(--primary-purple)] hover:bg-[var(--background-light-gray)]': workspaceType !== 'assigned'
          }"
          [style.background-color]="workspaceType === 'assigned' ? 'var(--primary-purple)' : ''">
          <i class="ri-user-line text-base"></i>
          Assigned to Me
        </button>
        <button
          class="flex items-center gap-2 px-4 py-3 rounded-lg font-medium text-sm border transition-all duration-300 shadow-sm"
          title="Over Due" (click)="onOverDue()"
          [ngClass]="{
            'text-white border-[var(--primary-purple)]': workspaceType === 'dueProject',
            'bg-[var(--background-white)] text-[var(--text-dark)] border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)] hover:text-[var(--primary-purple)] hover:bg-[var(--background-light-gray)]': workspaceType !== 'dueProject'
          }"
          [style.background-color]="workspaceType === 'dueProject' ? 'var(--primary-purple)' : ''">
          <i class="ri-alarm-warning-line text-base"></i>
          Over Due
        </button>
        <button
          class="flex items-center gap-2 px-4 py-3 rounded-lg font-medium text-sm border transition-all duration-300 shadow-sm"
          title="Calendar View" (click)="onCalendar()"
          [ngClass]="{
            'text-white border-[var(--primary-purple)]': workspaceType === 'calendar',
            'bg-[var(--background-white)] text-[var(--text-dark)] border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)] hover:text-[var(--primary-purple)] hover:bg-[var(--background-light-gray)]': workspaceType !== 'calendar'
          }"
          [style.background-color]="workspaceType === 'calendar' ? 'var(--primary-purple)' : ''">
          <i class="ri-calendar-line text-base"></i>
          Calendar
        </button>
      </div>
    </div>

    <!-- Stats Section -->
    <div class="px-4 pt-4" *ngIf="!isCalendarView">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div
          class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30 transition-all duration-300 cursor-pointer hover:shadow-md"
          [class.ring-2]="activeFilter === 'All'"
          [style.border-color]="activeFilter === 'All' ? 'var(--primary-purple)' : ''"
          (click)="filterProjectsByStatus('All')">
          <div class="p-4 flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg flex items-center justify-center"
              style="background-color: rgba(107, 70, 193, 0.1);">
              <i class="ri-list-check text-lg" style="color: var(--primary-purple);"></i>
            </div>
            <div class="flex flex-col">
              <span class="text-2xl font-bold text-[var(--text-dark)]">{{products.length}}</span>
              <span class="text-sm text-[var(--text-medium-gray)]">Total Projects</span>
            </div>
          </div>
        </div>

        <div
          class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30 transition-all duration-300 cursor-pointer hover:shadow-md"
          [class.ring-2]="activeFilter === 'In Progress'" [class.ring-orange-500]="activeFilter === 'In Progress'"
          (click)="filterProjectsByStatus('In Progress')">
          <div class="p-4 flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg flex items-center justify-center bg-orange-100">
              <i class="ri-loader-2-line text-orange-500 text-lg"></i>
            </div>
            <div class="flex flex-col">
              <span class="text-2xl font-bold text-[var(--text-dark)]">{{getInProgressProjects()}}</span>
              <span class="text-sm text-[var(--text-medium-gray)]">In Progress</span>
            </div>
          </div>
        </div>

        <div
          class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30 transition-all duration-300 cursor-pointer hover:shadow-md"
          [class.ring-2]="activeFilter === 'Pending'" [class.ring-blue-500]="activeFilter === 'Pending'"
          (click)="filterProjectsByStatus('Pending')">
          <div class="p-4 flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg flex items-center justify-center bg-blue-100">
              <i class="ri-folder-received-fill text-blue-500 text-lg"></i>
            </div>
            <div class="flex flex-col">
              <span class="text-2xl font-bold text-[var(--text-dark)]">{{getOpenProjects()}}</span>
              <span class="text-sm text-[var(--text-medium-gray)]">Open</span>
            </div>
          </div>
        </div>

        <div
          class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30 transition-all duration-300 cursor-pointer hover:shadow-md"
          [class.ring-2]="activeFilter === 'Completed'" [class.ring-green-500]="activeFilter === 'Completed'"
          (click)="filterProjectsByStatus('Completed')">
          <div class="p-4 flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg flex items-center justify-center bg-green-100">
              <i class="ri-check-double-fill text-green-500 text-lg"></i>
            </div>
            <div class="flex flex-col">
              <span class="text-2xl font-bold text-[var(--text-dark)]">{{getCompletedProjects()}}</span>
              <span class="text-sm text-[var(--text-medium-gray)]">Completed</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Calendar View Stats (only visible when in calendar view and on all-request page) -->
    <div class="px-4 pt-4" *ngIf="isCalendarView && isAllRequestPage">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30 transition-all duration-300 hover:shadow-md">
          <div class="p-4 flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg flex items-center justify-center bg-red-100">
              <i class="ri-time-line text-red-500 text-lg"></i>
            </div>
            <div class="flex flex-col">
              <span class="text-2xl font-bold text-[var(--text-dark)]">{{getProjectStats().myOverdue}}</span>
              <span class="text-sm text-[var(--text-medium-gray)]">My Overdue</span>
            </div>
          </div>
        </div>
        <div class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30 transition-all duration-300 hover:shadow-md">
          <div class="p-4 flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg flex items-center justify-center bg-orange-100">
              <i class="ri-alarm-warning-line text-orange-500 text-lg"></i>
            </div>
            <div class="flex flex-col">
              <span class="text-2xl font-bold text-[var(--text-dark)]">{{getProjectStats().totalOverdue}}</span>
              <span class="text-sm text-[var(--text-medium-gray)]">Total Overdue</span>
            </div>
          </div>
        </div>
        <div class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30 transition-all duration-300 hover:shadow-md">
          <div class="p-4 flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg flex items-center justify-center"
              style="background-color: rgba(107, 70, 193, 0.1);">
              <i class="ri-user-line text-lg" style="color: var(--primary-purple);"></i>
            </div>
            <div class="flex flex-col">
              <span class="text-2xl font-bold text-[var(--text-dark)]">{{getProjectStats().myAssigned}}</span>
              <span class="text-sm text-[var(--text-medium-gray)]">My Assigned</span>
            </div>
          </div>
        </div>
        <div class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30 transition-all duration-300 hover:shadow-md">
          <div class="p-4 flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg flex items-center justify-center bg-blue-100">
              <i class="ri-folder-line text-blue-500 text-lg"></i>
            </div>
            <div class="flex flex-col">
              <span class="text-2xl font-bold text-[var(--text-dark)]">{{getProjectStats().totalProjects}}</span>
              <span class="text-sm text-[var(--text-medium-gray)]">Total Projects</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Toggle button for calendar view -->
      <div class="flex justify-end mb-4">
        <button
          class="flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-300 shadow-sm hover:shadow-md"
          [ngClass]="{
            'bg-[var(--primary-purple)] text-white': showAllPastDue,
            'bg-[var(--background-white)] text-[var(--text-dark)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)]': !showAllPastDue
          }"
          (click)="togglePastDueFilter()">
          <i class="ri-user-line text-base" *ngIf="showAllPastDue"></i>
          <i class="ri-group-line text-base" *ngIf="!showAllPastDue"></i>
          {{ !showAllPastDue ? "Show All Past Due" : "Show My Past Due" }}
        </button>
      </div>
    </div>

    <!-- Calendar View Component -->
    <div class="px-4" *ngIf="isCalendarView && isAllRequestPage">
      <div class="bg-[var(--background-white)] rounded-lg shadow-sm border border-[var(--hover-blue-gray)] border-opacity-30 p-6">
        <!-- Calendar Header -->
        <div class="flex justify-between items-center mb-6">
          <div class="flex justify-center gap-4 items-center w-full">
            <button
              class="p-2 hover:bg-[var(--hover-blue-gray)] rounded-full transition-colors"
              (click)="previousMonth()">
              <i class="ri-arrow-left-line text-xl text-[var(--text-dark)]"></i>
            </button>
            <h2 class="text-xl font-semibold text-[var(--text-dark)]">
              {{ currentDate | date:'MMMM yyyy' }} ( {{getTasksForCurrentMonth()}} )
            </h2>
            <button
              class="p-2 hover:bg-[var(--hover-blue-gray)] rounded-full transition-colors"
              (click)="nextMonth()">
              <i class="ri-arrow-right-line text-xl text-[var(--text-dark)]"></i>
            </button>
          </div>
        </div>

        <!-- Calendar Grid -->
        <div class="grid grid-cols-7 gap-1">
          <!-- Week Days Header -->
          <div *ngFor="let day of weekDays"
               class="text-center font-bold p-3 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md">
            {{ day }}
          </div>

          <!-- Calendar Days -->
          <ng-container *ngIf="calendar.length">
            <ng-container *ngFor="let week of calendar">
              <div *ngFor="let date of week"
                   class="p-1 flex flex-col border border-[var(--hover-blue-gray)] border-opacity-30 min-h-[100px] relative bg-[var(--background-white)] rounded-md hover:bg-[var(--hover-blue-gray)] hover:bg-opacity-50 transition-colors">

                <!-- Date Number -->
                <span class="text-sm font-medium text-[var(--text-medium-gray)] mb-1"
                      [ngClass]="{
                        'text-[var(--primary-purple)] font-bold': isToday(date),
                        'text-[var(--text-dark)]': !isToday(date)
                      }">
                  {{ date?.getDate() }}
                </span>

                <!-- Tasks for this date -->
                <div class="flex-1 overflow-hidden" *ngIf="date && getTasksForDate(date) as dateTasks">
                  <div *ngFor="let task of dateTasks.slice(0, 2)"
                       class="text-xs mb-1 p-1 rounded cursor-pointer transition-colors"
                       [ngClass]="{
                         'bg-red-100 text-red-800 hover:bg-red-200': isPastDue(task),
                         'bg-blue-100 text-blue-800 hover:bg-blue-200': !isPastDue(task)
                       }"
                       (click)="goToProject(task.id)"
                       [title]="task.subject">
                    <div class="flex items-center gap-1">
                      <i class="ri-file-text-line text-xs"></i>
                      <span class="truncate">{{ task.subject | slice:0:15 }}{{ task.subject && task.subject.length > 15 ? '...' : '' }}</span>
                    </div>
                    <div class="text-xs opacity-75 mt-1">
                      <span class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium transition-all duration-200"
                            [ngClass]="{
                              'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400': task.status === 'Completed',
                              'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400': task.status === 'In Progress',
                              'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400': task.status === 'Pending' || task.status === 'Open',
                              'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400': task.status !== 'Completed' && task.status !== 'In Progress' && task.status !== 'Pending' && task.status !== 'Open'
                            }">
                        {{task.status}}
                      </span>
                    </div>
                  </div>

                  <!-- Show more indicator -->
                  <div *ngIf="dateTasks.length > 2"
                       class="text-xs text-gray-500 dark:text-gray-400 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300 p-1"
                       (click)="toggleTaskList(date)">
                    +{{ dateTasks.length - 2 }} more
                  </div>

                  <!-- Expanded task list -->
                  <div class="absolute top-0 left-0 bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg z-50 w-80 max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-700"
                       *ngIf="dateTasks.length > 2 && expandedDateTasks && date && expandedDateTasks[getDateKey(date)]">
                    <div class="flex justify-between items-center mb-2">
                      <h4 class="font-semibold text-gray-800 dark:text-white">
                        {{ date | date:'MMM d, yyyy' }} ({{ dateTasks.length }})
                      </h4>
                      <button (click)="toggleTaskList(date)"
                              class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="ri-close-line"></i>
                      </button>
                    </div>
                    <div *ngFor="let task of dateTasks"
                         class="mb-2 p-2 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-100 dark:border-gray-600"
                         (click)="goToProject(task.id)">
                      <div class="font-medium text-sm text-gray-800 dark:text-white mb-1">
                        {{ task.subject }}
                      </div>
                      <div class="flex items-center gap-2 text-xs">
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium transition-all duration-200"
                              [ngClass]="{
                                'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400': task.status === 'Completed',
                                'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400': task.status === 'In Progress',
                                'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400': task.status === 'Pending' || task.status === 'Open',
                                'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400': task.status !== 'Completed' && task.status !== 'In Progress' && task.status !== 'Pending' && task.status !== 'Open'
                              }">
                          {{task.status}}
                        </span>
                        <span class="text-gray-500 dark:text-gray-400">
                          {{ task.assignedEmail || 'Unassigned' }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>
      </div>
    </div>

    <!-- Requests Table - With Proper Y-Axis Scrolling -->
    <div class="flex-1 overflow-y-auto px-4 pt-2" *ngIf="viewType === 'table' && !isCalendarView">
      <div
        class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30">
        <div class="overflow-x-auto">
          <table class="w-full text-sm text-[var(--text-dark)]" *ngIf="filteredProducts.length !== 0">
            <!-- Table Header - Sticky -->
            <thead>
              <tr
                class="sticky top-0 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] font-medium text-sm border-b border-[var(--hover-blue-gray)] z-10">
                <th class=" px-2.5 py-2  text-left">
                  <div class="flex items-center">
                    <span class="uppercase">Status</span>
                  </div>
                </th>
                <th class=" px-2.5 py-2  text-left">
                  <div class="flex items-center">
                    <span class="uppercase">Priority</span>
                  </div>
                </th>
                <th class=" px-2.5 py-2  text-left">
                  <div class="flex items-center">
                    <span class="uppercase">Subject</span>
                  </div>
                </th>
                <th class=" px-2.5 py-2  text-left">
                  <div class="flex items-center">
                    <span class="uppercase">Category</span>
                  </div>
                </th>
                <th class="p px-2.5 py-2 text-left">
                  <div class="flex items-center">
                    <span class="uppercase">Created</span>
                  </div>
                </th>
                <th class=" px-2.5 py-2  text-left">
                  <div class="flex items-center">
                    <span class="uppercase">Due Date</span>
                  </div>
                </th>
                <th class=" text-left">
                  <div class="flex items-center">
                    <span class="uppercase">Assigned</span>
                  </div>
                </th>
                <th class=" text-center">
                  <div class="flex items-center justify-center">
                    <span class="uppercase">Actions</span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let request of filteredProducts; let i = index"
                class="border-b border-[var(--hover-blue-gray)] hover:bg-[var(--background-light-gray)] transition-all duration-200 cursor-pointer animate-fadeIn"
                [ngStyle]="{'animation-delay': (i * 0.05) + 's'}" (click)="goToProject(request.id)">
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <span class="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium transition-all duration-200"
                      [ngClass]="{
                        'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400': request.status === 'Completed',
                        'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400': request.status === 'In Progress',
                        'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400': request.status === 'Pending' || request.status === 'Open',
                        'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400': request.status !== 'Completed' && request.status !== 'In Progress' && request.status !== 'Pending' && request.status !== 'Open'
                      }">
                      {{request.status}}
                    </span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <span class="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium capitalize transition-all duration-200"
                      [ngClass]="{
                        'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400': request.priority?.toLowerCase() === 'critical',
                        'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400': request.priority?.toLowerCase() === 'high',
                        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400': request.priority?.toLowerCase() === 'medium',
                        'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400': request.priority?.toLowerCase() === 'low'
                      }">
                      {{request.priority}}
                    </span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <span class="font-medium" [title]="request.subject">
                      {{ request.subject && request.subject.length > 15 ? request.subject.substring(0, 15) + '...' :
                      (request.subject || '') }}
                    </span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <span
                    class="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium bg-[var(--primary-purple)] text-white">
                    {{request.projectCategory}}
                  </span>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <span>{{request.createdDate.toJSDate() | date:'MMM d, y'}}</span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <span>{{request.completionDate.toJSDate() | date:'MMM d, y'}}</span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <span [title]="request.assignedEmail || 'Unassigned'">
                      {{ ((request.assignedEmail?.length ?? 0) > 10 ? (request.assignedEmail | slice:0:10) + '...' :
                      request?.assignedEmail || 'Unassigned') }}
                    </span>
                  </div>
                </td>
                <td *ngIf="isAdmin" class="px-4 py-3">
                  <div class="flex items-center justify-center space-x-3">
                    <button
                      class="action-button w-8 h-8 rounded-md transition-all duration-200 flex items-center justify-center border-none"
                      style="background-color: var(--background-light-gray); color: var(--primary-purple);"
                      onmouseover="this.style.backgroundColor='var(--hover-blue-gray)'"
                      onmouseout="this.style.backgroundColor='var(--background-light-gray)'"
                      (click)="goToProject(request.id); $event.stopPropagation()" title="Edit Request">
                      <i class="ri-eye-fill text-base"></i>
                    </button>
                    <button
                      class="action-button w-8 h-8 rounded-md transition-all duration-200 flex items-center justify-center border-none"
                      style="background-color: var(--background-light-gray); color: var(--primary-purple);"
                      onmouseover="this.style.backgroundColor='var(--hover-blue-gray)'"
                      onmouseout="this.style.backgroundColor='var(--background-light-gray)'"
                      (click)="editProject(request.id); $event.stopPropagation()" title="Edit Request">
                      <i class="ri-edit-line text-base"></i>
                    </button>
                    <button
                      class="action-button w-8 h-8 rounded-md transition-all duration-200 flex items-center justify-center border-none"
                      style="background-color: var(--background-light-gray); color: #ef4444;"
                      onmouseover="this.style.backgroundColor='var(--hover-blue-gray)'"
                      onmouseout="this.style.backgroundColor='var(--background-light-gray)'"
                      (click)="deleteClicked($event, request.id); " title="Delete Request">
                      <i class="ri-delete-bin-6-line text-base"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div *ngIf="filteredProducts.length === 0" class="flex flex-col items-center justify-center py-16 px-4">
          <div class="w-16 h-16 rounded-full bg-[var(--hover-blue-gray)] flex items-center justify-center mb-4">
            <i class="ri-file-list-3-line text-3xl text-[var(--text-medium-gray)]"></i>
          </div>
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-2">No requests found</h3>
          <p class="text-[var(--text-medium-gray)] text-center max-w-md mb-6">
            No requests match your current filter criteria. Try adjusting your filters or create a new request.
          </p>
          <button
            class="px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-opacity-90 transition-all duration-300 flex items-center gap-2"
            (click)="goToProject(0)">
            <i class="ri-add-line"></i>
            <span>Create New Request</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Card View -->
    <div class="px-4 flex-1 overflow-y-auto" *ngIf="viewType === 'card' && !isCalendarView">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div *ngFor="let request of filteredProducts"
        class="rounded-lg p-6 border transition-all duration-300 cursor-pointer hover:shadow-lg"
        style="background-color: var(--background-white); border: 1px solid var(--hover-blue-gray); border-radius: var(--border-radius-large);"
        [style.border-left]="request.status === 'Completed' ? '4px solid var(--success-green)' : '1px solid var(--hover-blue-gray)'"
        (click)="goToProject(request.id)">

        <!-- Header: Status, Priority, and Actions -->
        <div class="flex justify-between items-start mb-4">
          <div class="flex gap-2 flex-wrap">
            <!-- Status Badge -->
            <span class="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium transition-all duration-200"
              [ngClass]="{
                'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400': request.status === 'Completed',
                'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400': request.status === 'In Progress',
                'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400': request.status === 'Pending' || request.status === 'Open',
                'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400': request.status !== 'Completed' && request.status !== 'In Progress' && request.status !== 'Pending' && request.status !== 'Open'
              }">
              {{request.status}}
            </span>
            <!-- Priority Badge -->
            <span class="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium capitalize transition-all duration-200"
              [ngClass]="{
                'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400': request.priority?.toLowerCase() === 'critical',
                'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400': request.priority?.toLowerCase() === 'high',
                'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400': request.priority?.toLowerCase() === 'medium',
                'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400': request.priority?.toLowerCase() === 'low'
              }">
              {{request.priority}}
            </span>
          </div>
          <div class="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <button
              class="w-8 h-8 flex items-center justify-center border-none cursor-pointer transition-all duration-300"
              style="background-color: var(--background-light-gray); color: var(--primary-purple); border-radius: 4px;"
              onmouseover="this.style.backgroundColor='var(--hover-blue-gray)'"
              onmouseout="this.style.backgroundColor='var(--background-light-gray)'"
              (click)="editProject(request.id); $event.stopPropagation()" title="Edit">
              <i class="ri-pencil-fill text-sm"></i>
            </button>
            <button
              class="w-8 h-8 flex items-center justify-center border-none cursor-pointer transition-all duration-300"
              style="background-color: var(--background-light-gray); color: #ef4444; border-radius: 4px;"
              onmouseover="this.style.backgroundColor='var(--hover-blue-gray)'"
              onmouseout="this.style.backgroundColor='var(--background-light-gray)'"
              (click)="deleteClicked( $event, request.id); $event.stopPropagation()" title="Delete">
              <i class="ri-delete-bin-line text-sm"></i>
            </button>
          </div>
        </div>

        <!-- Title -->
        <h3 class="text-lg font-semibold mb-2" style="color: var(--text-dark);" [title]="request.subject">
          {{request.subject}}
        </h3>



        <!-- Category Badge -->
        <div class="mb-4">
          <span class="px-3 py-1 rounded text-xs font-medium transition-all duration-200"
            [ngClass]="{
              'text-white': true
            }"
            [style.background-color]="'var(--primary-purple)'"
            [style.border-radius]="'var(--border-radius-small)'">
            {{request.projectCategory}}
          </span>
        </div>



        <!-- Dates and Assignment -->
        <div class="space-y-2">
          <div class="flex items-center gap-2 text-xs" style="color: var(--text-medium-gray);">
            <i class="ri-calendar-line text-xs"></i>
            <span>Created: {{request.createdDate.toJSDate() | date:'MMM d, y'}}</span>
          </div>
          <div class="flex items-center gap-2 text-xs" style="color: var(--text-medium-gray);">
            <i class="ri-calendar-check-line text-xs"></i>
            <span>Updated: {{request.completionDate.toJSDate() | date:'MMM d, y'}}</span>
          </div>
          <div *ngIf="request.assignedEmail" class="flex items-center gap-2 text-xs"
            style="color: var(--text-medium-gray);">
            <i class="ri-user-line text-xs"></i>
            <span class="truncate">{{request.assignedEmail}}</span>
          </div>
        </div>


      </div>

      <!-- Empty State for Card View -->
      <div *ngIf="filteredProducts.length === 0" class="col-span-full text-center py-12">
        <div class="mb-4">
          <i class="ri-file-list-3-line text-6xl" style="color: var(--text-medium-gray);"></i>
        </div>
        <h3 class="text-xl font-semibold mb-2" style="color: var(--text-dark);">No requests found</h3>
        <p class="mb-6" style="color: var(--text-medium-gray);">Try adjusting your filters or create a new request</p>
        <button class="px-6 py-3 border-none cursor-pointer transition-all duration-300 flex items-center gap-2 mx-auto"
          style="background-color: var(--primary-purple); color: white; border-radius: var(--border-radius-small);"
          onmouseover="this.style.backgroundColor='var(--secondary-purple)'"
          onmouseout="this.style.backgroundColor='var(--primary-purple)'"
          (click)="goToProject(0)">
          <i class="ri-add-line"></i>
          Create New Request
        </button>
      </div>
    </div>
  </div>
</div>

<app-delete-dialog
      *ngIf="showDialog"
      [title]="'Confirm Deletion'"
      [message]="'Are you sure you want to delete this item?'"
      (confirm)="onDeleteConfirmed($event)"
      (cancel)="onDeleteCancelled()">
    </app-delete-dialog>
