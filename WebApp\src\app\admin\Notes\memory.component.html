<div
  class="flex flex-col bg-[var(--background-light-gray)]"
  style="height: calc(100vh - 65px)"
>
  <!-- Main Container -->
  <div class="flex-1 overflow-hidden flex flex-col">
    <!-- Header - Teams-style with compact vertical spacing -->
    <div
      class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50"
    >
      <!-- Left side with title and count -->
      <div class="flex items-center gap-2">
        <i class="ri-sticky-note-line text-[var(--primary-purple)] text-xl"></i>
        <h1 class="text-lg font-medium text-[var(--text-dark)]">
          Memory Library
        </h1>
        <div
          class="inline-flex items-center justify-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]"
        >
          {{ filteredMemory.length }}
        </div>
      </div>

      <!-- Right side with controls -->
      <div class="flex items-center gap-2">
        <!-- Search Input - Teams-style -->
        <div class="relative w-full sm:w-56 flex items-center">
          <div
            class="absolute inset-y-0 left-0 flex items-center justify-center pl-2 pointer-events-none"
          >
            <i
              class="ri-search-line text-[var(--text-medium-gray)] text-sm"
            ></i>
          </div>
          <input
            type="text"
            placeholder="Search Memory..."
            [(ngModel)]="searchTerm"
            (input)="searchMemory()"
            class="w-full h-8 px-3 py-1 pl-8 text-sm border-none text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200"
          />
          <div
            class="absolute inset-y-0 right-0 flex items-center justify-center pr-2"
            *ngIf="searchTerm"
          >
            <button
              (click)="searchTerm = ''; searchMemory()"
              class="text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] border-none bg-transparent transition-colors focus:outline-none"
            >
              <i class="ri-close-line text-sm"></i>
            </button>
          </div>
        </div>

        <!-- Add Note Button - Teams-style -->
        <button
          (click)="addMemory()"
          class="h-8 px-3 py-1 bg-[var(--primary-purple)] text-white text-sm border-none font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center justify-center gap-1"
        >
          <i class="ri-add-line"></i>
          <span>Add Memory</span>
        </button>
      </div>
    </div>

    <!-- Content Area -->
    <div class="flex-1 overflow-y-auto px-4 pt-4">
      <!-- Loading State -->
      <div *ngIf="loading" class="flex justify-center items-center py-12">
        <div
          class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--primary-purple)]"
        ></div>
      </div>

      <!-- Empty State -->
      <div
        *ngIf="!loading && filteredMemory.length === 0"
        class="bg-[var(--background-white)] rounded-lg shadow-sm p-8 text-center"
      >
        <div class="flex flex-col items-center justify-center gap-4">
          <i
            class="ri-file-list-3-line text-5xl text-[var(--text-medium-gray)]"
          ></i>
          <h3 class="text-xl font-medium text-[var(--text-dark)]">
            No Memory found
          </h3>
          <p class="text-[var(--text-medium-gray)] max-w-md mx-auto">
            {{
              searchTerm
                ? "No Memory match your search criteria. Try a different search term."
                : 'You haven\'t created any Memory yet. Click the "Add Note" button to create your first note.'
            }}
          </p>
          <button
            *ngIf="searchTerm"
            (click)="searchTerm = ''; searchMemory()"
            class="mt-2 px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-opacity-80 transition-colors text-sm"
          >
            Clear Search
          </button>
        </div>
      </div>

      <!-- Memory Grid -->
      <div
        *ngIf="!loading && filteredMemory.length > 0"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
      >
        <!-- Memory Card -->
        <div
          *ngFor="let memory of paginatedMemory; let i = index"
          class="bg-[var(--background-white)] rounded-md overflow-hidden group animate-fadeIn relative h-full flex flex-col cursor-pointer"
          [ngStyle]="{ 'animation-delay': i * 0.05 + 's' }"
          (click)="selectMemory(memory)"
        >
          <!-- Action Buttons (Top Right) -->
          <div
            class="absolute top-3 right-3 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10"
          >
            <button
              [routerLink]="[memory.id]"
              class="action-button w-8 h-8 rounded-md  bg-[var(--primary-purple)] transition-all duration-200 flex items-center justify-center border-none shadow-sm"
              title="Edit Memory"
              (click)="$event.stopPropagation()"
            >
              <i class="ri-edit-line text-white text-base"></i>
            </button>
            <button
              (click)="confirmDelete(memory.id); $event.stopPropagation()"
              class="action-button w-8 h-8 rounded-md bg-[#FEE2E2] hover:bg-[#FECACA] transition-all duration-200 flex items-center justify-center border-none shadow-sm"
              title="Delete Memory"
            >
              <i class="ri-delete-bin-6-line text-red-500 text-base"></i>
            </button>
          </div>

          <!-- Card Content -->
          <div class="p-4 flex-1">
            <!-- Memory Content Preview -->
            <div class="mt-2">
              <p class="text-[var(--text-dark)] line-clamp-3 break-words">
                {{ getDisplayContent(memory) }}
              </p>
            </div>
          </div>

          <!-- Card Footer -->
          <div
            class="px-4 py-3 border-t border-[var(--hover-blue-gray)] border-opacity-30 flex justify-between items-center"
          >
            <div
              class="text-xs text-[var(--text-medium-gray)] flex items-center gap-1"
            >
              <i class="ri-time-line"></i>
              <span>{{ formatDate(memory.createdAt) }}</span>
            </div>
          </div>

          <!-- Delete Confirmation Overlay -->
          <div
            *ngIf="confirmDeleteId === memory.id"
            class="absolute inset-0 bg-[var(--background-white)] bg-opacity-95 flex flex-col items-center justify-center p-4 z-20 animate-fadeIn"
            (click)="$event.stopPropagation()"
          >
            <p class="text-center text-[var(--text-dark)] mb-4">
              Are you sure you want to delete this memory?
            </p>
            <div class="flex gap-3">
              <button
                (click)="deleteMemory(memory.id)"
                class="px-4 py-2 bg-red-500 text-white rounded-md border-none hover:bg-red-600 transition-colors"
              >
                Delete
              </button>
              <button
                (click)="cancelDelete()"
                class="px-4 py-2 bg-[var(--hover-blue-gray)] border-none text-[var(--text-dark)] rounded-md hover:bg-opacity-80 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div
        class="pagination-container flex flex-col sm:flex-row justify-between items-center mt-4 mb-4 px-4 py-3 bg-[var(--background-white)] rounded-md shadow-sm border border-[var(--hover-blue-gray)]"
        *ngIf="!loading && filteredMemory.length > 0"
      >
        <div class="text-sm text-[var(--text-medium-gray)] mb-4 sm:mb-0 flex items-center">
          <ng-container *ngIf="filteredMemory.length > 0">
            <span>Showing</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">
              {{ ((currentPage - 1) * pageSize) + 1 }}
            </span>
            <span>to</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">
              {{ ((currentPage - 1) * pageSize) + paginatedMemory.length }}
            </span>
            <span>of</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">
              {{ filteredMemory.length }}
            </span>
            <span>memories</span>
          </ng-container>
          <ng-container *ngIf="filteredMemory.length === 0">
            <span>No memories to display</span>
          </ng-container>
        </div>

        <div class="flex items-center">
          <div class="hidden sm:flex items-center mr-6 space-x-2">
            <span class="text-sm text-[var(--text-medium-gray)]">Rows per page:</span>
            <div class="relative w-auto">
              <button
                id="pageSizeDropdownBtn"
                (click)="togglePageSizeDropdown()"
                class="appearance-none h-7 rounded-md text-sm px-2 pr-7 py-0 text-center border bg-[var(--background-white)] border-gray-300 focus:outline-none focus:ring-1 cursor-pointer"
              >
                {{ pageSize || 'Select Page Size' }}
              </button>
              <div
                *ngIf="isPageSizeDropdownOpen"
                id="pageSizeDropdownMenu"
                class="absolute mt-1 w-full bg-[var(--secondary-purple)] !border-2 !border-gray-300 rounded-md shadow-lg z-10"
                [ngClass]="{
                  'bottom-full mb-1 border': isDropdownAbove,
                  'top-full border mt-1': !isDropdownAbove
                }"
              >
                <div
                  (click)="selectPageSize(5)"
                  class="px-2 py-1 text-sm cursor-pointer text-center border hover:bg-[var(--primary-purple)] rounded-md hover:text-white"
                >
                  5
                </div>
                <div
                  (click)="selectPageSize(10)"
                  class="px-2 py-1 text-sm cursor-pointer text-center rounded-md hover:bg-[var(--primary-purple)] hover:text-white"
                >
                  10
                </div>
                <div
                  (click)="selectPageSize(20)"
                  class="px-2 py-1 text-sm cursor-pointer text-center rounded-md hover:bg-[var(--primary-purple)] hover:text-white"
                >
                  20
                </div>
                <div
                  (click)="selectPageSize(50)"
                  class="px-2 py-1 text-sm cursor-pointer text-center rounded-md hover:bg-[var(--primary-purple)] hover:text-white"
                >
                  50
                </div>
              </div>
              <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-1 pointer-events-none">
                <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)] text-sm"></i>
              </div>
            </div>
          </div>

          <div class="flex items-center space-x-1" *ngIf="totalPages > 0">
            <button
              (click)="goToPage(1)"
              [disabled]="currentPage === 1 || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="First page"
            >
              <i class="ri-skip-back-mini-line text-sm"></i>
            </button>

            <button
              (click)="previousPage()"
              [disabled]="currentPage === 1 || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="Previous page"
            >
              <i class="ri-arrow-left-s-line text-sm"></i>
            </button>

            <div class="flex items-center space-x-1">
              <button
                *ngIf="currentPage > 2 && totalPages > 3"
                (click)="goToPage(1)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              >
                1
              </button>

              <span
                *ngIf="currentPage > 3 && totalPages > 4"
                class="w-7 h-7 flex items-center justify-center text-[var(--text-medium-gray)]"
                >...</span
              >

              <button
                *ngIf="currentPage > 1 && totalPages > 1"
                (click)="goToPage(currentPage - 1)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              >
                {{ currentPage - 1 }}
              </button>

              <button
                class="w-7 h-7 flex items-center justify-center rounded-md bg-[var(--primary-purple)] text-white font-medium border-none"
              >
                {{ currentPage }}
              </button>

              <button
                *ngIf="currentPage < totalPages && totalPages > 1"
                (click)="goToPage(currentPage + 1)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              >
                {{ currentPage + 1 }}
              </button>

              <span
                *ngIf="currentPage < totalPages - 2 && totalPages > 4"
                class="w-7 h-7 flex items-center justify-center text-[var(--text-medium-gray)]"
                >...</span
              >

              <button
                *ngIf="currentPage < totalPages - 1 && totalPages > 3"
                (click)="goToPage(totalPages)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              >
                {{ totalPages }}
              </button>
            </div>

            <button
              (click)="nextPage()"
              [disabled]="currentPage === totalPages || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="Next page"
            >
              <i class="ri-arrow-right-s-line text-sm"></i>
            </button>

            <button
              (click)="goToPage(totalPages)"
              [disabled]="currentPage === totalPages || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="Last page"
            >
              <i class="ri-skip-forward-mini-line text-sm"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Memory Detail Modal -->
      <div
        *ngIf="selectedMemory"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fadeIn"
        (click)="closeMemoryDetail()"
      >
        <!-- Modal Content -->
        <div
          class="bg-[var(--background-white)] rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden animate-scaleIn"
          (click)="$event.stopPropagation()"
        >
          <!-- Modal Header -->
          <div
            class="flex items-center justify-between p-4 border-b border-[var(--hover-blue-gray)]"
          >
            <div class="flex items-center gap-3">
              <div
                class="w-10 h-10 rounded-full bg-[var(--primary-purple)] flex items-center justify-center text-white"
              >
                <i class="ri-brain-line text-lg"></i>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-[var(--text-dark)]">
                  Memory Details
                </h3>
                <p class="text-sm text-[var(--text-medium-gray)]">
                  Created: {{ formatDate(selectedMemory.createdAt) }}
                </p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <button
                [routerLink]="[selectedMemory.id]"
                class="action-button w-9 h-9 flex items-center justify-center rounded-md  bg-[var(--primary-purple)] transition-all duration-200 border-none shadow-sm"
                (click)="closeMemoryDetail()"
                title="Edit Memory"
              >
                <i class="ri-edit-line text-white text-base"></i>              </button>
              <button
                (click)="closeMemoryDetail()"
                class="action-button w-9 h-9 flex items-center justify-center rounded-md bg-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200 border-none shadow-sm"
                title="Close"
              >
                <i class="ri-close-line text-[var(--text-dark)] text-base"></i>
              </button>
            </div>
          </div>

          <!-- Modal Body -->
          <div class="flex-1 overflow-y-auto p-6">
            <!-- Formatted EditorJS Content -->
            <div
              class="prose prose-sm max-w-none editorjs-content"
              [innerHTML]="
                selectedMemory.content
                  ? formatEditorContent(selectedMemory.content)
                  : ''
              "
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
