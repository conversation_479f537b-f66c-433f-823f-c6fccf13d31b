{"version": 3, "sources": ["../../../../../node_modules/ngx-markdown/fesm2022/ngx-markdown.mjs"], "sourcesContent": ["import { Async<PERSON><PERSON><PERSON>, isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, InjectionToken, Pipe, PLATFORM_ID, Injectable, Inject, Optional, EventEmitter, Input, Output, SecurityContext, NgModule } from '@angular/core';\nimport { Subject, of, timer, merge } from 'rxjs';\nimport { mapTo, switchMap, distinctUntilChanged, shareReplay, startWith, map, takeUntil, first } from 'rxjs/operators';\nimport { Renderer, marked } from 'marked';\nexport { Renderer as MarkedRenderer } from 'marked';\nimport * as i1 from '@angular/common/http';\nimport * as i2 from '@angular/platform-browser';\nconst _c0 = [\"*\"];\nconst BUTTON_TEXT_COPY = 'Copy';\nconst BUTTON_TEXT_COPIED = 'Copied';\nclass ClipboardButtonComponent {\n  constructor() {\n    this._buttonClick$ = new Subject();\n    this.copied$ = this._buttonClick$.pipe(switchMap(() => merge(of(true), timer(3000).pipe(mapTo(false)))), distinctUntilChanged(), shareReplay(1));\n    this.copiedText$ = this.copied$.pipe(startWith(false), map(copied => copied ? BUTTON_TEXT_COPIED : BUTTON_TEXT_COPY));\n  }\n  onCopyToClipboardClick() {\n    this._buttonClick$.next();\n  }\n  static {\n    this.ɵfac = function ClipboardButtonComponent_Factory(t) {\n      return new (t || ClipboardButtonComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ClipboardButtonComponent,\n      selectors: [[\"markdown-clipboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 7,\n      consts: [[1, \"markdown-clipboard-button\", 3, \"click\"]],\n      template: function ClipboardButtonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵlistener(\"click\", function ClipboardButtonComponent_Template_button_click_0_listener() {\n            return ctx.onCopyToClipboardClick();\n          });\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"copied\", i0.ɵɵpipeBind1(1, 3, ctx.copied$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 5, ctx.copiedText$));\n        }\n      },\n      dependencies: [AsyncPipe],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClipboardButtonComponent, [{\n    type: Component,\n    args: [{\n      selector: 'markdown-clipboard',\n      template: `\n    <button\n      class=\"markdown-clipboard-button\"\n      [class.copied]=\"copied$ | async\"\n      (click)=\"onCopyToClipboardClick()\"\n    >{{ copiedText$ | async }}</button>\n  `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      imports: [AsyncPipe]\n    }]\n  }], null, null);\n})();\nconst CLIPBOARD_OPTIONS = new InjectionToken('CLIPBOARD_OPTIONS');\n\n/* eslint-disable */\nclass KatexSpecificOptions {}\nclass LanguagePipe {\n  transform(value, language) {\n    if (value == null) {\n      value = '';\n    }\n    if (language == null) {\n      language = '';\n    }\n    if (typeof value !== 'string') {\n      console.error(`LanguagePipe has been invoked with an invalid value type [${typeof value}]`);\n      return value;\n    }\n    if (typeof language !== 'string') {\n      console.error(`LanguagePipe has been invoked with an invalid parameter [${typeof language}]`);\n      return value;\n    }\n    return '```' + language + '\\n' + value + '\\n```';\n  }\n  static {\n    this.ɵfac = function LanguagePipe_Factory(t) {\n      return new (t || LanguagePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"language\",\n      type: LanguagePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LanguagePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'language',\n      standalone: true\n    }]\n  }], null, null);\n})();\nvar PrismPlugin;\n(function (PrismPlugin) {\n  PrismPlugin[\"CommandLine\"] = \"command-line\";\n  PrismPlugin[\"LineHighlight\"] = \"line-highlight\";\n  PrismPlugin[\"LineNumbers\"] = \"line-numbers\";\n})(PrismPlugin || (PrismPlugin = {}));\nconst MARKED_EXTENSIONS = new InjectionToken('MARKED_EXTENSIONS');\nconst MARKED_OPTIONS = new InjectionToken('MARKED_OPTIONS');\n\n/* eslint-disable max-len */\nconst errorJoyPixelsNotLoaded = '[ngx-markdown] When using the `emoji` attribute you *have to* include Emoji-Toolkit files to `angular.json` or use imports. See README for more information';\nconst errorKatexNotLoaded = '[ngx-markdown] When using the `katex` attribute you *have to* include KaTeX files to `angular.json` or use imports. See README for more information';\nconst errorMermaidNotLoaded = '[ngx-markdown] When using the `mermaid` attribute you *have to* include Mermaid files to `angular.json` or use imports. See README for more information';\nconst errorClipboardNotLoaded = '[ngx-markdown] When using the `clipboard` attribute you *have to* include Clipboard files to `angular.json` or use imports. See README for more information';\nconst errorClipboardViewContainerRequired = '[ngx-markdown] When using the `clipboard` attribute you *have to* provide the `viewContainerRef` parameter to `MarkdownService.render()` function';\nconst errorSrcWithoutHttpClient = '[ngx-markdown] When using the `src` attribute you *have to* pass the `HttpClient` as a parameter of the `forRoot` method. See README for more information';\n/* eslint-enable max-len */\nconst SECURITY_CONTEXT = new InjectionToken('SECURITY_CONTEXT');\nclass ExtendedRenderer extends Renderer {\n  constructor() {\n    super(...arguments);\n    this.ɵNgxMarkdownRendererExtendedForExtensions = false;\n    this.ɵNgxMarkdownRendererExtendedForMermaid = false;\n  }\n}\nclass MarkdownService {\n  get options() {\n    return this._options;\n  }\n  set options(value) {\n    this._options = {\n      ...this.DEFAULT_MARKED_OPTIONS,\n      ...value\n    };\n  }\n  get renderer() {\n    return this.options.renderer;\n  }\n  set renderer(value) {\n    this.options.renderer = value;\n  }\n  constructor(clipboardOptions, extensions, options, platform, securityContext, http, sanitizer) {\n    this.clipboardOptions = clipboardOptions;\n    this.extensions = extensions;\n    this.platform = platform;\n    this.securityContext = securityContext;\n    this.http = http;\n    this.sanitizer = sanitizer;\n    this.DEFAULT_MARKED_OPTIONS = {\n      renderer: new Renderer()\n    };\n    this.DEFAULT_KATEX_OPTIONS = {\n      delimiters: [{\n        left: '$$',\n        right: '$$',\n        display: true\n      }, {\n        left: '$',\n        right: '$',\n        display: false\n      }, {\n        left: '\\\\(',\n        right: '\\\\)',\n        display: false\n      }, {\n        left: '\\\\begin{equation}',\n        right: '\\\\end{equation}',\n        display: true\n      }, {\n        left: '\\\\begin{align}',\n        right: '\\\\end{align}',\n        display: true\n      }, {\n        left: '\\\\begin{alignat}',\n        right: '\\\\end{alignat}',\n        display: true\n      }, {\n        left: '\\\\begin{gather}',\n        right: '\\\\end{gather}',\n        display: true\n      }, {\n        left: '\\\\begin{CD}',\n        right: '\\\\end{CD}',\n        display: true\n      }, {\n        left: '\\\\[',\n        right: '\\\\]',\n        display: true\n      }]\n    };\n    this.DEFAULT_MERMAID_OPTIONS = {\n      startOnLoad: false\n    };\n    this.DEFAULT_CLIPBOARD_OPTIONS = {\n      buttonComponent: undefined\n    };\n    this.DEFAULT_PARSE_OPTIONS = {\n      decodeHtml: false,\n      inline: false,\n      emoji: false,\n      mermaid: false,\n      markedOptions: undefined,\n      disableSanitizer: false\n    };\n    this.DEFAULT_RENDER_OPTIONS = {\n      clipboard: false,\n      clipboardOptions: undefined,\n      katex: false,\n      katexOptions: undefined,\n      mermaid: false,\n      mermaidOptions: undefined\n    };\n    this._reload$ = new Subject();\n    this.reload$ = this._reload$.asObservable();\n    this.options = options;\n  }\n  parse(markdown, parseOptions = this.DEFAULT_PARSE_OPTIONS) {\n    const {\n      decodeHtml,\n      inline,\n      emoji,\n      mermaid,\n      disableSanitizer\n    } = parseOptions;\n    const markedOptions = {\n      ...this.options,\n      ...parseOptions.markedOptions\n    };\n    const renderer = markedOptions.renderer || this.renderer || new Renderer();\n    if (this.extensions) {\n      this.renderer = this.extendsRendererForExtensions(renderer);\n    }\n    if (mermaid) {\n      this.renderer = this.extendsRendererForMermaid(renderer);\n    }\n    const trimmed = this.trimIndentation(markdown);\n    const decoded = decodeHtml ? this.decodeHtml(trimmed) : trimmed;\n    const emojified = emoji ? this.parseEmoji(decoded) : decoded;\n    const marked = this.parseMarked(emojified, markedOptions, inline);\n    const sanitized = disableSanitizer ? marked : this.sanitizer.sanitize(this.securityContext, marked);\n    return sanitized || '';\n  }\n  render(element, options = this.DEFAULT_RENDER_OPTIONS, viewContainerRef) {\n    const {\n      clipboard,\n      clipboardOptions,\n      katex,\n      katexOptions,\n      mermaid,\n      mermaidOptions\n    } = options;\n    if (katex) {\n      this.renderKatex(element, {\n        ...this.DEFAULT_KATEX_OPTIONS,\n        ...katexOptions\n      });\n    }\n    if (mermaid) {\n      this.renderMermaid(element, {\n        ...this.DEFAULT_MERMAID_OPTIONS,\n        ...mermaidOptions\n      });\n    }\n    if (clipboard) {\n      this.renderClipboard(element, viewContainerRef, {\n        ...this.DEFAULT_CLIPBOARD_OPTIONS,\n        ...this.clipboardOptions,\n        ...clipboardOptions\n      });\n    }\n    this.highlight(element);\n  }\n  reload() {\n    this._reload$.next();\n  }\n  getSource(src) {\n    if (!this.http) {\n      throw new Error(errorSrcWithoutHttpClient);\n    }\n    return this.http.get(src, {\n      responseType: 'text'\n    }).pipe(map(markdown => this.handleExtension(src, markdown)));\n  }\n  highlight(element) {\n    if (!isPlatformBrowser(this.platform)) {\n      return;\n    }\n    if (typeof Prism === 'undefined' || typeof Prism.highlightAllUnder === 'undefined') {\n      return;\n    }\n    if (!element) {\n      element = document;\n    }\n    const noLanguageElements = element.querySelectorAll('pre code:not([class*=\"language-\"])');\n    Array.prototype.forEach.call(noLanguageElements, x => x.classList.add('language-none'));\n    Prism.highlightAllUnder(element);\n  }\n  decodeHtml(html) {\n    if (!isPlatformBrowser(this.platform)) {\n      return html;\n    }\n    const textarea = document.createElement('textarea');\n    textarea.innerHTML = html;\n    return textarea.value;\n  }\n  extendsRendererForExtensions(renderer) {\n    const extendedRenderer = renderer;\n    if (extendedRenderer.ɵNgxMarkdownRendererExtendedForExtensions === true) {\n      return renderer;\n    }\n    if (this.extensions?.length > 0) {\n      marked.use(...this.extensions);\n    }\n    extendedRenderer.ɵNgxMarkdownRendererExtendedForExtensions = true;\n    return renderer;\n  }\n  extendsRendererForMermaid(renderer) {\n    const extendedRenderer = renderer;\n    if (extendedRenderer.ɵNgxMarkdownRendererExtendedForMermaid === true) {\n      return renderer;\n    }\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const defaultCode = renderer.code;\n    renderer.code = function (code, language, isEscaped) {\n      return language === 'mermaid' ? `<div class=\"mermaid\">${code}</div>` : defaultCode.call(this, code, language, isEscaped);\n    };\n    extendedRenderer.ɵNgxMarkdownRendererExtendedForMermaid = true;\n    return renderer;\n  }\n  handleExtension(src, markdown) {\n    const urlProtocolIndex = src.lastIndexOf('://');\n    const urlWithoutProtocol = urlProtocolIndex > -1 ? src.substring(urlProtocolIndex + 4) : src;\n    const lastSlashIndex = urlWithoutProtocol.lastIndexOf('/');\n    const lastUrlSegment = lastSlashIndex > -1 ? urlWithoutProtocol.substring(lastSlashIndex + 1).split('?')[0] : '';\n    const lastDotIndex = lastUrlSegment.lastIndexOf('.');\n    const extension = lastDotIndex > -1 ? lastUrlSegment.substring(lastDotIndex + 1) : '';\n    return !!extension && extension !== 'md' ? '```' + extension + '\\n' + markdown + '\\n```' : markdown;\n  }\n  parseMarked(html, markedOptions, inline = false) {\n    if (markedOptions.renderer) {\n      // clone renderer and remove extended flags otherwise\n      // marked throws an error thinking it is a renderer prop\n      const renderer = {\n        ...markedOptions.renderer\n      };\n      delete renderer.ɵNgxMarkdownRendererExtendedForExtensions;\n      delete renderer.ɵNgxMarkdownRendererExtendedForMermaid;\n      // remove renderer from markedOptions because if renderer is\n      // passed to marked.parse method, it will ignore all extensions\n      delete markedOptions.renderer;\n      marked.use({\n        renderer\n      });\n    }\n    return inline ? marked.parseInline(html, markedOptions) : marked.parse(html, markedOptions);\n  }\n  parseEmoji(html) {\n    if (!isPlatformBrowser(this.platform)) {\n      return html;\n    }\n    if (typeof joypixels === 'undefined' || typeof joypixels.shortnameToUnicode === 'undefined') {\n      throw new Error(errorJoyPixelsNotLoaded);\n    }\n    return joypixels.shortnameToUnicode(html);\n  }\n  renderKatex(element, options) {\n    if (!isPlatformBrowser(this.platform)) {\n      return;\n    }\n    if (typeof katex === 'undefined' || typeof renderMathInElement === 'undefined') {\n      throw new Error(errorKatexNotLoaded);\n    }\n    renderMathInElement(element, options);\n  }\n  renderClipboard(element, viewContainerRef, options) {\n    if (!isPlatformBrowser(this.platform)) {\n      return;\n    }\n    if (typeof ClipboardJS === 'undefined') {\n      throw new Error(errorClipboardNotLoaded);\n    }\n    if (!viewContainerRef) {\n      throw new Error(errorClipboardViewContainerRequired);\n    }\n    const {\n      buttonComponent,\n      buttonTemplate\n    } = options;\n    // target every <pre> elements\n    const preElements = element.querySelectorAll('pre');\n    for (let i = 0; i < preElements.length; i++) {\n      const preElement = preElements.item(i);\n      // create <pre> wrapper element\n      const preWrapperElement = document.createElement('div');\n      preWrapperElement.style.position = 'relative';\n      preElement.parentNode.insertBefore(preWrapperElement, preElement);\n      preWrapperElement.appendChild(preElement);\n      // create toolbar element\n      const toolbarWrapperElement = document.createElement('div');\n      toolbarWrapperElement.style.position = 'absolute';\n      toolbarWrapperElement.style.top = '.5em';\n      toolbarWrapperElement.style.right = '.5em';\n      toolbarWrapperElement.style.opacity = '0';\n      toolbarWrapperElement.style.transition = 'opacity 250ms ease-out';\n      toolbarWrapperElement.style.zIndex = '1';\n      preWrapperElement.insertAdjacentElement('beforeend', toolbarWrapperElement);\n      // register listener to show/hide toolbar\n      preElement.onmouseover = () => toolbarWrapperElement.style.opacity = '1';\n      preElement.onmouseout = () => toolbarWrapperElement.style.opacity = '0';\n      // declare embeddedViewRef holding variable\n      let embeddedViewRef;\n      // use provided component via input property\n      // or provided via ClipboardOptions provider\n      if (buttonComponent) {\n        const componentRef = viewContainerRef.createComponent(buttonComponent);\n        embeddedViewRef = componentRef.hostView;\n        componentRef.changeDetectorRef.markForCheck();\n      }\n      // use provided template via input property\n      else if (buttonTemplate) {\n        embeddedViewRef = viewContainerRef.createEmbeddedView(buttonTemplate);\n      }\n      // use default component\n      else {\n        const componentRef = viewContainerRef.createComponent(ClipboardButtonComponent);\n        embeddedViewRef = componentRef.hostView;\n        componentRef.changeDetectorRef.markForCheck();\n      }\n      // declare clipboard instance variable\n      let clipboardInstance;\n      // attach clipboard.js to root node\n      embeddedViewRef.rootNodes.forEach(node => {\n        node.onmouseover = () => toolbarWrapperElement.style.opacity = '1';\n        toolbarWrapperElement.appendChild(node);\n        clipboardInstance = new ClipboardJS(node, {\n          text: () => preElement.innerText\n        });\n      });\n      // destroy clipboard instance when view is destroyed\n      embeddedViewRef.onDestroy(() => clipboardInstance.destroy());\n    }\n  }\n  renderMermaid(element, options = this.DEFAULT_MERMAID_OPTIONS) {\n    if (!isPlatformBrowser(this.platform)) {\n      return;\n    }\n    if (typeof mermaid === 'undefined' || typeof mermaid.initialize === 'undefined') {\n      throw new Error(errorMermaidNotLoaded);\n    }\n    const mermaidElements = element.querySelectorAll('.mermaid');\n    if (mermaidElements.length === 0) {\n      return;\n    }\n    mermaid.initialize(options);\n    mermaid.run({\n      nodes: mermaidElements\n    });\n  }\n  trimIndentation(markdown) {\n    if (!markdown) {\n      return '';\n    }\n    let indentStart;\n    return markdown.split('\\n').map(line => {\n      let lineIdentStart = indentStart;\n      if (line.length > 0) {\n        lineIdentStart = isNaN(lineIdentStart) ? line.search(/\\S|$/) : Math.min(line.search(/\\S|$/), lineIdentStart);\n      }\n      if (isNaN(indentStart)) {\n        indentStart = lineIdentStart;\n      }\n      return lineIdentStart ? line.substring(lineIdentStart) : line;\n    }).join('\\n');\n  }\n  static {\n    this.ɵfac = function MarkdownService_Factory(t) {\n      return new (t || MarkdownService)(i0.ɵɵinject(CLIPBOARD_OPTIONS, 8), i0.ɵɵinject(MARKED_EXTENSIONS, 8), i0.ɵɵinject(MARKED_OPTIONS, 8), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(SECURITY_CONTEXT), i0.ɵɵinject(i1.HttpClient, 8), i0.ɵɵinject(i2.DomSanitizer));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MarkdownService,\n      factory: MarkdownService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkdownService, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CLIPBOARD_OPTIONS]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MARKED_EXTENSIONS]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MARKED_OPTIONS]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.SecurityContext,\n    decorators: [{\n      type: Inject,\n      args: [SECURITY_CONTEXT]\n    }]\n  }, {\n    type: i1.HttpClient,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.DomSanitizer\n  }], null);\n})();\nclass MarkdownComponent {\n  get disableSanitizer() {\n    return this._disableSanitizer;\n  }\n  set disableSanitizer(value) {\n    this._disableSanitizer = this.coerceBooleanProperty(value);\n  }\n  get inline() {\n    return this._inline;\n  }\n  set inline(value) {\n    this._inline = this.coerceBooleanProperty(value);\n  }\n  // Plugin - clipboard\n  get clipboard() {\n    return this._clipboard;\n  }\n  set clipboard(value) {\n    this._clipboard = this.coerceBooleanProperty(value);\n  }\n  // Plugin - emoji\n  get emoji() {\n    return this._emoji;\n  }\n  set emoji(value) {\n    this._emoji = this.coerceBooleanProperty(value);\n  }\n  // Plugin - katex\n  get katex() {\n    return this._katex;\n  }\n  set katex(value) {\n    this._katex = this.coerceBooleanProperty(value);\n  }\n  // Plugin - mermaid\n  get mermaid() {\n    return this._mermaid;\n  }\n  set mermaid(value) {\n    this._mermaid = this.coerceBooleanProperty(value);\n  }\n  // Plugin - lineHighlight\n  get lineHighlight() {\n    return this._lineHighlight;\n  }\n  set lineHighlight(value) {\n    this._lineHighlight = this.coerceBooleanProperty(value);\n  }\n  // Plugin - lineNumbers\n  get lineNumbers() {\n    return this._lineNumbers;\n  }\n  set lineNumbers(value) {\n    this._lineNumbers = this.coerceBooleanProperty(value);\n  }\n  // Plugin - commandLine\n  get commandLine() {\n    return this._commandLine;\n  }\n  set commandLine(value) {\n    this._commandLine = this.coerceBooleanProperty(value);\n  }\n  constructor(element, markdownService, viewContainerRef) {\n    this.element = element;\n    this.markdownService = markdownService;\n    this.viewContainerRef = viewContainerRef;\n    // Event emitters\n    this.error = new EventEmitter();\n    this.load = new EventEmitter();\n    this.ready = new EventEmitter();\n    this._clipboard = false;\n    this._commandLine = false;\n    this._disableSanitizer = false;\n    this._emoji = false;\n    this._inline = false;\n    this._katex = false;\n    this._lineHighlight = false;\n    this._lineNumbers = false;\n    this._mermaid = false;\n    this.destroyed$ = new Subject();\n  }\n  ngOnChanges() {\n    this.loadContent();\n  }\n  loadContent() {\n    if (this.data != null) {\n      this.handleData();\n      return;\n    }\n    if (this.src != null) {\n      this.handleSrc();\n      return;\n    }\n  }\n  ngAfterViewInit() {\n    if (!this.data && !this.src) {\n      this.handleTransclusion();\n    }\n    this.markdownService.reload$.pipe(takeUntil(this.destroyed$)).subscribe(() => this.loadContent());\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  async render(markdown, decodeHtml = false) {\n    const parsedOptions = {\n      decodeHtml,\n      inline: this.inline,\n      emoji: this.emoji,\n      mermaid: this.mermaid,\n      disableSanitizer: this.disableSanitizer\n    };\n    const renderOptions = {\n      clipboard: this.clipboard,\n      clipboardOptions: {\n        buttonComponent: this.clipboardButtonComponent,\n        buttonTemplate: this.clipboardButtonTemplate\n      },\n      katex: this.katex,\n      katexOptions: this.katexOptions,\n      mermaid: this.mermaid,\n      mermaidOptions: this.mermaidOptions\n    };\n    const parsed = await this.markdownService.parse(markdown, parsedOptions);\n    this.element.nativeElement.innerHTML = parsed;\n    this.handlePlugins();\n    this.markdownService.render(this.element.nativeElement, renderOptions, this.viewContainerRef);\n    this.ready.emit();\n  }\n  coerceBooleanProperty(value) {\n    return value != null && `${String(value)}` !== 'false';\n  }\n  handleData() {\n    this.render(this.data);\n  }\n  handleSrc() {\n    this.markdownService.getSource(this.src).subscribe({\n      next: markdown => {\n        this.render(markdown).then(() => {\n          this.load.emit(markdown);\n        });\n      },\n      error: error => this.error.emit(error)\n    });\n  }\n  handleTransclusion() {\n    this.render(this.element.nativeElement.innerHTML, true);\n  }\n  handlePlugins() {\n    if (this.commandLine) {\n      this.setPluginClass(this.element.nativeElement, PrismPlugin.CommandLine);\n      this.setPluginOptions(this.element.nativeElement, {\n        dataFilterOutput: this.filterOutput,\n        dataHost: this.host,\n        dataPrompt: this.prompt,\n        dataOutput: this.output,\n        dataUser: this.user\n      });\n    }\n    if (this.lineHighlight) {\n      this.setPluginOptions(this.element.nativeElement, {\n        dataLine: this.line,\n        dataLineOffset: this.lineOffset\n      });\n    }\n    if (this.lineNumbers) {\n      this.setPluginClass(this.element.nativeElement, PrismPlugin.LineNumbers);\n      this.setPluginOptions(this.element.nativeElement, {\n        dataStart: this.start\n      });\n    }\n  }\n  setPluginClass(element, plugin) {\n    const preElements = element.querySelectorAll('pre');\n    for (let i = 0; i < preElements.length; i++) {\n      const classes = plugin instanceof Array ? plugin : [plugin];\n      preElements.item(i).classList.add(...classes);\n    }\n  }\n  setPluginOptions(element, options) {\n    const preElements = element.querySelectorAll('pre');\n    for (let i = 0; i < preElements.length; i++) {\n      Object.keys(options).forEach(option => {\n        const attributeValue = options[option];\n        if (attributeValue) {\n          const attributeName = this.toLispCase(option);\n          preElements.item(i).setAttribute(attributeName, attributeValue.toString());\n        }\n      });\n    }\n  }\n  toLispCase(value) {\n    const upperChars = value.match(/([A-Z])/g);\n    if (!upperChars) {\n      return value;\n    }\n    let str = value.toString();\n    for (let i = 0, n = upperChars.length; i < n; i++) {\n      str = str.replace(new RegExp(upperChars[i]), '-' + upperChars[i].toLowerCase());\n    }\n    if (str.slice(0, 1) === '-') {\n      str = str.slice(1);\n    }\n    return str;\n  }\n  static {\n    this.ɵfac = function MarkdownComponent_Factory(t) {\n      return new (t || MarkdownComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MarkdownService), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MarkdownComponent,\n      selectors: [[\"markdown\"], [\"\", \"markdown\", \"\"]],\n      inputs: {\n        data: \"data\",\n        src: \"src\",\n        disableSanitizer: \"disableSanitizer\",\n        inline: \"inline\",\n        clipboard: \"clipboard\",\n        clipboardButtonComponent: \"clipboardButtonComponent\",\n        clipboardButtonTemplate: \"clipboardButtonTemplate\",\n        emoji: \"emoji\",\n        katex: \"katex\",\n        katexOptions: \"katexOptions\",\n        mermaid: \"mermaid\",\n        mermaidOptions: \"mermaidOptions\",\n        lineHighlight: \"lineHighlight\",\n        line: \"line\",\n        lineOffset: \"lineOffset\",\n        lineNumbers: \"lineNumbers\",\n        start: \"start\",\n        commandLine: \"commandLine\",\n        filterOutput: \"filterOutput\",\n        host: \"host\",\n        prompt: \"prompt\",\n        output: \"output\",\n        user: \"user\"\n      },\n      outputs: {\n        error: \"error\",\n        load: \"load\",\n        ready: \"ready\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MarkdownComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkdownComponent, [{\n    type: Component,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/component-selector\n      selector: 'markdown, [markdown]',\n      template: '<ng-content></ng-content>',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: MarkdownService\n  }, {\n    type: i0.ViewContainerRef\n  }], {\n    data: [{\n      type: Input\n    }],\n    src: [{\n      type: Input\n    }],\n    disableSanitizer: [{\n      type: Input\n    }],\n    inline: [{\n      type: Input\n    }],\n    clipboard: [{\n      type: Input\n    }],\n    clipboardButtonComponent: [{\n      type: Input\n    }],\n    clipboardButtonTemplate: [{\n      type: Input\n    }],\n    emoji: [{\n      type: Input\n    }],\n    katex: [{\n      type: Input\n    }],\n    katexOptions: [{\n      type: Input\n    }],\n    mermaid: [{\n      type: Input\n    }],\n    mermaidOptions: [{\n      type: Input\n    }],\n    lineHighlight: [{\n      type: Input\n    }],\n    line: [{\n      type: Input\n    }],\n    lineOffset: [{\n      type: Input\n    }],\n    lineNumbers: [{\n      type: Input\n    }],\n    start: [{\n      type: Input\n    }],\n    commandLine: [{\n      type: Input\n    }],\n    filterOutput: [{\n      type: Input\n    }],\n    host: [{\n      type: Input\n    }],\n    prompt: [{\n      type: Input\n    }],\n    output: [{\n      type: Input\n    }],\n    user: [{\n      type: Input\n    }],\n    error: [{\n      type: Output\n    }],\n    load: [{\n      type: Output\n    }],\n    ready: [{\n      type: Output\n    }]\n  });\n})();\nclass MarkdownPipe {\n  constructor(domSanitizer, elementRef, markdownService, viewContainerRef, zone) {\n    this.domSanitizer = domSanitizer;\n    this.elementRef = elementRef;\n    this.markdownService = markdownService;\n    this.viewContainerRef = viewContainerRef;\n    this.zone = zone;\n  }\n  async transform(value, options) {\n    if (value == null) {\n      return '';\n    }\n    if (typeof value !== 'string') {\n      console.error(`MarkdownPipe has been invoked with an invalid value type [${typeof value}]`);\n      return value;\n    }\n    const markdown = await this.markdownService.parse(value, options);\n    this.zone.onStable.pipe(first()).subscribe(() => this.markdownService.render(this.elementRef.nativeElement, options, this.viewContainerRef));\n    return this.domSanitizer.bypassSecurityTrustHtml(markdown);\n  }\n  static {\n    this.ɵfac = function MarkdownPipe_Factory(t) {\n      return new (t || MarkdownPipe)(i0.ɵɵdirectiveInject(i2.DomSanitizer, 16), i0.ɵɵdirectiveInject(i0.ElementRef, 16), i0.ɵɵdirectiveInject(MarkdownService, 16), i0.ɵɵdirectiveInject(i0.ViewContainerRef, 16), i0.ɵɵdirectiveInject(i0.NgZone, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"markdown\",\n      type: MarkdownPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkdownPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'markdown',\n      standalone: true\n    }]\n  }], () => [{\n    type: i2.DomSanitizer\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: MarkdownService\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\nfunction provideMarkdown(markdownModuleConfig) {\n  return [MarkdownService, markdownModuleConfig?.loader ?? [], markdownModuleConfig?.clipboardOptions ?? [], markdownModuleConfig?.markedOptions ?? [], {\n    provide: MARKED_EXTENSIONS,\n    useValue: markdownModuleConfig?.markedExtensions ?? []\n  }, {\n    provide: SECURITY_CONTEXT,\n    useValue: markdownModuleConfig?.sanitize ?? SecurityContext.HTML\n  }];\n}\nconst sharedDeclarations = [ClipboardButtonComponent, LanguagePipe, MarkdownComponent, MarkdownPipe];\nclass MarkdownModule {\n  static forRoot(markdownModuleConfig) {\n    return {\n      ngModule: MarkdownModule,\n      providers: [provideMarkdown(markdownModuleConfig)]\n    };\n  }\n  static forChild() {\n    return {\n      ngModule: MarkdownModule\n    };\n  }\n  static {\n    this.ɵfac = function MarkdownModule_Factory(t) {\n      return new (t || MarkdownModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MarkdownModule,\n      imports: [CommonModule, ClipboardButtonComponent, LanguagePipe, MarkdownComponent, MarkdownPipe],\n      exports: [ClipboardButtonComponent, LanguagePipe, MarkdownComponent, MarkdownPipe]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkdownModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, ...sharedDeclarations],\n      exports: sharedDeclarations\n    }]\n  }], null, null);\n})();\n\n/* eslint-disable */\nvar MermaidAPI;\n(function (MermaidAPI) {\n  let SecurityLevel;\n  (function (SecurityLevel) {\n    /**\n     * (default) tags in text are encoded, click functionality is disabled\n     */\n    SecurityLevel[\"Strict\"] = \"strict\";\n    /**\n     * tags in text are allowed, click functionality is enabled\n     */\n    SecurityLevel[\"Loose\"] = \"loose\";\n    /**\n     * html tags in text are allowed, (only script element is removed), click functionality is enabled\n     */\n    SecurityLevel[\"Antiscript\"] = \"antiscript\";\n    /**\n     * with this security level all rendering takes place in a sandboxed iframe.\n     * This prevent any javascript running in the context.\n     * This may hinder interactive functionality of the diagram like scripts,\n     * popups in sequence diagram or links to other tabs/targets etc.\n     */\n    SecurityLevel[\"Sandbox\"] = \"sandbox\";\n  })(SecurityLevel = MermaidAPI.SecurityLevel || (MermaidAPI.SecurityLevel = {}));\n  let Theme;\n  (function (Theme) {\n    /**\n     * Designed to modified, as the name implies it is supposed to be used as the base for making custom themes.\n     */\n    Theme[\"Base\"] = \"base\";\n    /**\n     * A theme full of light greens that is easy on the eyes.\n     */\n    Theme[\"Forest\"] = \"forest\";\n    /**\n     * A theme that would go well with other dark colored elements.\n     */\n    Theme[\"Dark\"] = \"dark\";\n    /**\n     *  The default theme for all diagrams.\n     */\n    Theme[\"Default\"] = \"default\";\n    /**\n     * The theme to be used for black and white printing\n     */\n    Theme[\"Neutral\"] = \"neutral\";\n  })(Theme = MermaidAPI.Theme || (MermaidAPI.Theme = {}));\n  let LogLevel;\n  (function (LogLevel) {\n    LogLevel[LogLevel[\"Debug\"] = 1] = \"Debug\";\n    LogLevel[LogLevel[\"Info\"] = 2] = \"Info\";\n    LogLevel[LogLevel[\"Warn\"] = 3] = \"Warn\";\n    LogLevel[LogLevel[\"Error\"] = 4] = \"Error\";\n    LogLevel[LogLevel[\"Fatal\"] = 5] = \"Fatal\";\n  })(LogLevel = MermaidAPI.LogLevel || (MermaidAPI.LogLevel = {}));\n})(MermaidAPI || (MermaidAPI = {}));\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CLIPBOARD_OPTIONS, ClipboardButtonComponent, ExtendedRenderer, KatexSpecificOptions, LanguagePipe, MARKED_EXTENSIONS, MARKED_OPTIONS, MarkdownComponent, MarkdownModule, MarkdownPipe, MarkdownService, MermaidAPI, PrismPlugin, SECURITY_CONTEXT, errorClipboardNotLoaded, errorClipboardViewContainerRequired, errorJoyPixelsNotLoaded, errorKatexNotLoaded, errorMermaidNotLoaded, errorSrcWithoutHttpClient, provideMarkdown };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,mBAAmB;AACzB,IAAM,qBAAqB;AAC3B,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AACZ,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,UAAU,KAAK,cAAc,KAAK,UAAU,MAAM,MAAM,GAAG,IAAI,GAAG,MAAM,GAAI,EAAE,KAAK,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,qBAAqB,GAAG,YAAY,CAAC,CAAC;AAC/I,SAAK,cAAc,KAAK,QAAQ,KAAK,UAAU,KAAK,GAAG,IAAI,YAAU,SAAS,qBAAqB,gBAAgB,CAAC;AAAA,EACtH;AAAA,EACA,yBAAyB;AACvB,SAAK,cAAc,KAAK;AAAA,EAC1B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,GAAG;AACvD,aAAO,KAAK,KAAK,2BAA0B;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,MAClC,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,6BAA6B,GAAG,OAAO,CAAC;AAAA,MACrD,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,UAAU,CAAC;AAChC,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,WAAW,SAAS,SAAS,4DAA4D;AAC1F,mBAAO,IAAI,uBAAuB;AAAA,UACpC,CAAC;AACD,UAAG,OAAO,CAAC;AACX,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,UAAa,YAAY,GAAG,GAAG,IAAI,OAAO,CAAC;AAC1D,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,GAAG,GAAG,IAAI,WAAW,CAAC;AAAA,QAC5D;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS;AAAA,MACxB,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,SAAS,CAAC,SAAS;AAAA,IACrB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oBAAoB,IAAI,eAAe,mBAAmB;AAGhE,IAAM,uBAAN,MAA2B;AAAC;AAC5B,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,UAAU,OAAO,UAAU;AACzB,QAAI,SAAS,MAAM;AACjB,cAAQ;AAAA,IACV;AACA,QAAI,YAAY,MAAM;AACpB,iBAAW;AAAA,IACb;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,MAAM,6DAA6D,OAAO,KAAK,GAAG;AAC1F,aAAO;AAAA,IACT;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,cAAQ,MAAM,4DAA4D,OAAO,QAAQ,GAAG;AAC5F,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,WAAW,OAAO,QAAQ;AAAA,EAC3C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAc;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAI;AAAA,CACH,SAAUA,cAAa;AACtB,EAAAA,aAAY,aAAa,IAAI;AAC7B,EAAAA,aAAY,eAAe,IAAI;AAC/B,EAAAA,aAAY,aAAa,IAAI;AAC/B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,IAAM,oBAAoB,IAAI,eAAe,mBAAmB;AAChE,IAAM,iBAAiB,IAAI,eAAe,gBAAgB;AAG1D,IAAM,0BAA0B;AAChC,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAC9B,IAAM,0BAA0B;AAChC,IAAM,sCAAsC;AAC5C,IAAM,4BAA4B;AAElC,IAAM,mBAAmB,IAAI,eAAe,kBAAkB;AAC9D,IAAM,mBAAN,cAA+B,UAAS;AAAA,EACtC,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,4CAA4C;AACjD,SAAK,yCAAyC;AAAA,EAChD;AACF;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,kCACX,KAAK,yBACL;AAAA,EAEP;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,QAAQ,WAAW;AAAA,EAC1B;AAAA,EACA,YAAY,kBAAkB,YAAY,SAAS,UAAU,iBAAiB,MAAM,WAAW;AAC7F,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,yBAAyB;AAAA,MAC5B,UAAU,IAAI,UAAS;AAAA,IACzB;AACA,SAAK,wBAAwB;AAAA,MAC3B,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,SAAK,0BAA0B;AAAA,MAC7B,aAAa;AAAA,IACf;AACA,SAAK,4BAA4B;AAAA,MAC/B,iBAAiB;AAAA,IACnB;AACA,SAAK,wBAAwB;AAAA,MAC3B,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,eAAe;AAAA,MACf,kBAAkB;AAAA,IACpB;AACA,SAAK,yBAAyB;AAAA,MAC5B,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,cAAc;AAAA,MACd,SAAS;AAAA,MACT,gBAAgB;AAAA,IAClB;AACA,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,UAAU,KAAK,SAAS,aAAa;AAC1C,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,MAAM,UAAU,eAAe,KAAK,uBAAuB;AACzD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAAC;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,gBAAgB,kCACjB,KAAK,UACL,aAAa;AAElB,UAAM,WAAW,cAAc,YAAY,KAAK,YAAY,IAAI,UAAS;AACzE,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,KAAK,6BAA6B,QAAQ;AAAA,IAC5D;AACA,QAAIA,UAAS;AACX,WAAK,WAAW,KAAK,0BAA0B,QAAQ;AAAA,IACzD;AACA,UAAM,UAAU,KAAK,gBAAgB,QAAQ;AAC7C,UAAM,UAAU,aAAa,KAAK,WAAW,OAAO,IAAI;AACxD,UAAM,YAAY,QAAQ,KAAK,WAAW,OAAO,IAAI;AACrD,UAAMC,UAAS,KAAK,YAAY,WAAW,eAAe,MAAM;AAChE,UAAM,YAAY,mBAAmBA,UAAS,KAAK,UAAU,SAAS,KAAK,iBAAiBA,OAAM;AAClG,WAAO,aAAa;AAAA,EACtB;AAAA,EACA,OAAO,SAAS,UAAU,KAAK,wBAAwB,kBAAkB;AACvE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,OAAAC;AAAA,MACA;AAAA,MACA,SAAAF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAIE,QAAO;AACT,WAAK,YAAY,SAAS,kCACrB,KAAK,wBACL,aACJ;AAAA,IACH;AACA,QAAIF,UAAS;AACX,WAAK,cAAc,SAAS,kCACvB,KAAK,0BACL,eACJ;AAAA,IACH;AACA,QAAI,WAAW;AACb,WAAK,gBAAgB,SAAS,kBAAkB,iDAC3C,KAAK,4BACL,KAAK,mBACL,iBACJ;AAAA,IACH;AACA,SAAK,UAAU,OAAO;AAAA,EACxB;AAAA,EACA,SAAS;AACP,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,UAAU,KAAK;AACb,QAAI,CAAC,KAAK,MAAM;AACd,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AACA,WAAO,KAAK,KAAK,IAAI,KAAK;AAAA,MACxB,cAAc;AAAA,IAChB,CAAC,EAAE,KAAK,IAAI,cAAY,KAAK,gBAAgB,KAAK,QAAQ,CAAC,CAAC;AAAA,EAC9D;AAAA,EACA,UAAU,SAAS;AACjB,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC;AAAA,IACF;AACA,QAAI,OAAO,UAAU,eAAe,OAAO,MAAM,sBAAsB,aAAa;AAClF;AAAA,IACF;AACA,QAAI,CAAC,SAAS;AACZ,gBAAU;AAAA,IACZ;AACA,UAAM,qBAAqB,QAAQ,iBAAiB,oCAAoC;AACxF,UAAM,UAAU,QAAQ,KAAK,oBAAoB,OAAK,EAAE,UAAU,IAAI,eAAe,CAAC;AACtF,UAAM,kBAAkB,OAAO;AAAA,EACjC;AAAA,EACA,WAAW,MAAM;AACf,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC,aAAO;AAAA,IACT;AACA,UAAM,WAAW,SAAS,cAAc,UAAU;AAClD,aAAS,YAAY;AACrB,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,6BAA6B,UAAU;AACrC,UAAM,mBAAmB;AACzB,QAAI,iBAAiB,8CAA8C,MAAM;AACvE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,aAAO,IAAI,GAAG,KAAK,UAAU;AAAA,IAC/B;AACA,qBAAiB,4CAA4C;AAC7D,WAAO;AAAA,EACT;AAAA,EACA,0BAA0B,UAAU;AAClC,UAAM,mBAAmB;AACzB,QAAI,iBAAiB,2CAA2C,MAAM;AACpE,aAAO;AAAA,IACT;AAEA,UAAM,cAAc,SAAS;AAC7B,aAAS,OAAO,SAAU,MAAM,UAAU,WAAW;AACnD,aAAO,aAAa,YAAY,wBAAwB,IAAI,WAAW,YAAY,KAAK,MAAM,MAAM,UAAU,SAAS;AAAA,IACzH;AACA,qBAAiB,yCAAyC;AAC1D,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,KAAK,UAAU;AAC7B,UAAM,mBAAmB,IAAI,YAAY,KAAK;AAC9C,UAAM,qBAAqB,mBAAmB,KAAK,IAAI,UAAU,mBAAmB,CAAC,IAAI;AACzF,UAAM,iBAAiB,mBAAmB,YAAY,GAAG;AACzD,UAAM,iBAAiB,iBAAiB,KAAK,mBAAmB,UAAU,iBAAiB,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAC9G,UAAM,eAAe,eAAe,YAAY,GAAG;AACnD,UAAM,YAAY,eAAe,KAAK,eAAe,UAAU,eAAe,CAAC,IAAI;AACnF,WAAO,CAAC,CAAC,aAAa,cAAc,OAAO,QAAQ,YAAY,OAAO,WAAW,UAAU;AAAA,EAC7F;AAAA,EACA,YAAY,MAAM,eAAe,SAAS,OAAO;AAC/C,QAAI,cAAc,UAAU;AAG1B,YAAM,WAAW,mBACZ,cAAc;AAEnB,aAAO,SAAS;AAChB,aAAO,SAAS;AAGhB,aAAO,cAAc;AACrB,aAAO,IAAI;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,SAAS,OAAO,YAAY,MAAM,aAAa,IAAI,OAAO,MAAM,MAAM,aAAa;AAAA,EAC5F;AAAA,EACA,WAAW,MAAM;AACf,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC,aAAO;AAAA,IACT;AACA,QAAI,OAAO,cAAc,eAAe,OAAO,UAAU,uBAAuB,aAAa;AAC3F,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AACA,WAAO,UAAU,mBAAmB,IAAI;AAAA,EAC1C;AAAA,EACA,YAAY,SAAS,SAAS;AAC5B,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC;AAAA,IACF;AACA,QAAI,OAAO,UAAU,eAAe,OAAO,wBAAwB,aAAa;AAC9E,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACrC;AACA,wBAAoB,SAAS,OAAO;AAAA,EACtC;AAAA,EACA,gBAAgB,SAAS,kBAAkB,SAAS;AAClD,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC;AAAA,IACF;AACA,QAAI,OAAO,gBAAgB,aAAa;AACtC,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AACA,QAAI,CAAC,kBAAkB;AACrB,YAAM,IAAI,MAAM,mCAAmC;AAAA,IACrD;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM,cAAc,QAAQ,iBAAiB,KAAK;AAClD,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAM,aAAa,YAAY,KAAK,CAAC;AAErC,YAAM,oBAAoB,SAAS,cAAc,KAAK;AACtD,wBAAkB,MAAM,WAAW;AACnC,iBAAW,WAAW,aAAa,mBAAmB,UAAU;AAChE,wBAAkB,YAAY,UAAU;AAExC,YAAM,wBAAwB,SAAS,cAAc,KAAK;AAC1D,4BAAsB,MAAM,WAAW;AACvC,4BAAsB,MAAM,MAAM;AAClC,4BAAsB,MAAM,QAAQ;AACpC,4BAAsB,MAAM,UAAU;AACtC,4BAAsB,MAAM,aAAa;AACzC,4BAAsB,MAAM,SAAS;AACrC,wBAAkB,sBAAsB,aAAa,qBAAqB;AAE1E,iBAAW,cAAc,MAAM,sBAAsB,MAAM,UAAU;AACrE,iBAAW,aAAa,MAAM,sBAAsB,MAAM,UAAU;AAEpE,UAAI;AAGJ,UAAI,iBAAiB;AACnB,cAAM,eAAe,iBAAiB,gBAAgB,eAAe;AACrE,0BAAkB,aAAa;AAC/B,qBAAa,kBAAkB,aAAa;AAAA,MAC9C,WAES,gBAAgB;AACvB,0BAAkB,iBAAiB,mBAAmB,cAAc;AAAA,MACtE,OAEK;AACH,cAAM,eAAe,iBAAiB,gBAAgB,wBAAwB;AAC9E,0BAAkB,aAAa;AAC/B,qBAAa,kBAAkB,aAAa;AAAA,MAC9C;AAEA,UAAI;AAEJ,sBAAgB,UAAU,QAAQ,UAAQ;AACxC,aAAK,cAAc,MAAM,sBAAsB,MAAM,UAAU;AAC/D,8BAAsB,YAAY,IAAI;AACtC,4BAAoB,IAAI,YAAY,MAAM;AAAA,UACxC,MAAM,MAAM,WAAW;AAAA,QACzB,CAAC;AAAA,MACH,CAAC;AAED,sBAAgB,UAAU,MAAM,kBAAkB,QAAQ,CAAC;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,cAAc,SAAS,UAAU,KAAK,yBAAyB;AAC7D,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC;AAAA,IACF;AACA,QAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,eAAe,aAAa;AAC/E,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACvC;AACA,UAAM,kBAAkB,QAAQ,iBAAiB,UAAU;AAC3D,QAAI,gBAAgB,WAAW,GAAG;AAChC;AAAA,IACF;AACA,YAAQ,WAAW,OAAO;AAC1B,YAAQ,IAAI;AAAA,MACV,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,UAAU;AACxB,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,QAAI;AACJ,WAAO,SAAS,MAAM,IAAI,EAAE,IAAI,UAAQ;AACtC,UAAI,iBAAiB;AACrB,UAAI,KAAK,SAAS,GAAG;AACnB,yBAAiB,MAAM,cAAc,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,IAAI,KAAK,OAAO,MAAM,GAAG,cAAc;AAAA,MAC7G;AACA,UAAI,MAAM,WAAW,GAAG;AACtB,sBAAc;AAAA,MAChB;AACA,aAAO,iBAAiB,KAAK,UAAU,cAAc,IAAI;AAAA,IAC3D,CAAC,EAAE,KAAK,IAAI;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAoB,SAAS,mBAAmB,CAAC,GAAM,SAAS,mBAAmB,CAAC,GAAM,SAAS,gBAAgB,CAAC,GAAM,SAAS,WAAW,GAAM,SAAS,gBAAgB,GAAM,SAAY,YAAY,CAAC,GAAM,SAAY,YAAY,CAAC;AAAA,IAC9P;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,IAAI,mBAAmB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,iBAAiB,OAAO;AAC1B,SAAK,oBAAoB,KAAK,sBAAsB,KAAK;AAAA,EAC3D;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU,KAAK,sBAAsB,KAAK;AAAA,EACjD;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,SAAK,aAAa,KAAK,sBAAsB,KAAK;AAAA,EACpD;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS,KAAK,sBAAsB,KAAK;AAAA,EAChD;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS,KAAK,sBAAsB,KAAK;AAAA,EAChD;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,sBAAsB,KAAK;AAAA,EAClD;AAAA;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,iBAAiB,KAAK,sBAAsB,KAAK;AAAA,EACxD;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe,KAAK,sBAAsB,KAAK;AAAA,EACtD;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe,KAAK,sBAAsB,KAAK;AAAA,EACtD;AAAA,EACA,YAAY,SAAS,iBAAiB,kBAAkB;AACtD,SAAK,UAAU;AACf,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AAExB,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,OAAO,IAAI,aAAa;AAC7B,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,oBAAoB;AACzB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,aAAa,IAAI,QAAQ;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,WAAW;AAChB;AAAA,IACF;AACA,QAAI,KAAK,OAAO,MAAM;AACpB,WAAK,UAAU;AACf;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,KAAK;AAC3B,WAAK,mBAAmB;AAAA,IAC1B;AACA,SAAK,gBAAgB,QAAQ,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,YAAY,CAAC;AAAA,EAClG;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA,EACM,OAAO,UAAU,aAAa,OAAO;AAAA;AACzC,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,SAAS,KAAK;AAAA,QACd,kBAAkB,KAAK;AAAA,MACzB;AACA,YAAM,gBAAgB;AAAA,QACpB,WAAW,KAAK;AAAA,QAChB,kBAAkB;AAAA,UAChB,iBAAiB,KAAK;AAAA,UACtB,gBAAgB,KAAK;AAAA,QACvB;AAAA,QACA,OAAO,KAAK;AAAA,QACZ,cAAc,KAAK;AAAA,QACnB,SAAS,KAAK;AAAA,QACd,gBAAgB,KAAK;AAAA,MACvB;AACA,YAAM,SAAS,MAAM,KAAK,gBAAgB,MAAM,UAAU,aAAa;AACvE,WAAK,QAAQ,cAAc,YAAY;AACvC,WAAK,cAAc;AACnB,WAAK,gBAAgB,OAAO,KAAK,QAAQ,eAAe,eAAe,KAAK,gBAAgB;AAC5F,WAAK,MAAM,KAAK;AAAA,IAClB;AAAA;AAAA,EACA,sBAAsB,OAAO;AAC3B,WAAO,SAAS,QAAQ,GAAG,OAAO,KAAK,CAAC,OAAO;AAAA,EACjD;AAAA,EACA,aAAa;AACX,SAAK,OAAO,KAAK,IAAI;AAAA,EACvB;AAAA,EACA,YAAY;AACV,SAAK,gBAAgB,UAAU,KAAK,GAAG,EAAE,UAAU;AAAA,MACjD,MAAM,cAAY;AAChB,aAAK,OAAO,QAAQ,EAAE,KAAK,MAAM;AAC/B,eAAK,KAAK,KAAK,QAAQ;AAAA,QACzB,CAAC;AAAA,MACH;AAAA,MACA,OAAO,WAAS,KAAK,MAAM,KAAK,KAAK;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,OAAO,KAAK,QAAQ,cAAc,WAAW,IAAI;AAAA,EACxD;AAAA,EACA,gBAAgB;AACd,QAAI,KAAK,aAAa;AACpB,WAAK,eAAe,KAAK,QAAQ,eAAe,YAAY,WAAW;AACvE,WAAK,iBAAiB,KAAK,QAAQ,eAAe;AAAA,QAChD,kBAAkB,KAAK;AAAA,QACvB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,QACjB,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,eAAe;AACtB,WAAK,iBAAiB,KAAK,QAAQ,eAAe;AAAA,QAChD,UAAU,KAAK;AAAA,QACf,gBAAgB,KAAK;AAAA,MACvB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,eAAe,KAAK,QAAQ,eAAe,YAAY,WAAW;AACvE,WAAK,iBAAiB,KAAK,QAAQ,eAAe;AAAA,QAChD,WAAW,KAAK;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe,SAAS,QAAQ;AAC9B,UAAM,cAAc,QAAQ,iBAAiB,KAAK;AAClD,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAM,UAAU,kBAAkB,QAAQ,SAAS,CAAC,MAAM;AAC1D,kBAAY,KAAK,CAAC,EAAE,UAAU,IAAI,GAAG,OAAO;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,iBAAiB,SAAS,SAAS;AACjC,UAAM,cAAc,QAAQ,iBAAiB,KAAK;AAClD,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,aAAO,KAAK,OAAO,EAAE,QAAQ,YAAU;AACrC,cAAM,iBAAiB,QAAQ,MAAM;AACrC,YAAI,gBAAgB;AAClB,gBAAM,gBAAgB,KAAK,WAAW,MAAM;AAC5C,sBAAY,KAAK,CAAC,EAAE,aAAa,eAAe,eAAe,SAAS,CAAC;AAAA,QAC3E;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,aAAa,MAAM,MAAM,UAAU;AACzC,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AACA,QAAI,MAAM,MAAM,SAAS;AACzB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACjD,YAAM,IAAI,QAAQ,IAAI,OAAO,WAAW,CAAC,CAAC,GAAG,MAAM,WAAW,CAAC,EAAE,YAAY,CAAC;AAAA,IAChF;AACA,QAAI,IAAI,MAAM,GAAG,CAAC,MAAM,KAAK;AAC3B,YAAM,IAAI,MAAM,CAAC;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAqB,UAAU,GAAM,kBAAkB,eAAe,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,IAC3J;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,GAAG,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,MAC9C,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,QACL,kBAAkB;AAAA,QAClB,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,QACzB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,cAAc;AAAA,QACd,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,OAAO;AAAA,QACP,aAAa;AAAA,QACb,cAAc;AAAA,QACd,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA,SAAS;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,cAAc,YAAY,iBAAiB,kBAAkB,MAAM;AAC7E,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,OAAO;AAAA,EACd;AAAA,EACM,UAAU,OAAO,SAAS;AAAA;AAC9B,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ,MAAM,6DAA6D,OAAO,KAAK,GAAG;AAC1F,eAAO;AAAA,MACT;AACA,YAAM,WAAW,MAAM,KAAK,gBAAgB,MAAM,OAAO,OAAO;AAChE,WAAK,KAAK,SAAS,KAAK,MAAM,CAAC,EAAE,UAAU,MAAM,KAAK,gBAAgB,OAAO,KAAK,WAAW,eAAe,SAAS,KAAK,gBAAgB,CAAC;AAC3I,aAAO,KAAK,aAAa,wBAAwB,QAAQ;AAAA,IAC3D;AAAA;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAiB,kBAAqB,cAAc,EAAE,GAAM,kBAAqB,YAAY,EAAE,GAAM,kBAAkB,iBAAiB,EAAE,GAAM,kBAAqB,kBAAkB,EAAE,GAAM,kBAAqB,QAAQ,EAAE,CAAC;AAAA,IAClP;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,gBAAgB,sBAAsB;AAC7C,SAAO,CAAC,iBAAiB,sBAAsB,UAAU,CAAC,GAAG,sBAAsB,oBAAoB,CAAC,GAAG,sBAAsB,iBAAiB,CAAC,GAAG;AAAA,IACpJ,SAAS;AAAA,IACT,UAAU,sBAAsB,oBAAoB,CAAC;AAAA,EACvD,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU,sBAAsB,YAAY,gBAAgB;AAAA,EAC9D,CAAC;AACH;AACA,IAAM,qBAAqB,CAAC,0BAA0B,cAAc,mBAAmB,YAAY;AACnG,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,QAAQ,sBAAsB;AACnC,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,gBAAgB,oBAAoB,CAAC;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO,WAAW;AAChB,WAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,cAAc,0BAA0B,cAAc,mBAAmB,YAAY;AAAA,MAC/F,SAAS,CAAC,0BAA0B,cAAc,mBAAmB,YAAY;AAAA,IACnF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,GAAG,kBAAkB;AAAA,MAC7C,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAI;AAAA,CACH,SAAUG,aAAY;AACrB,MAAI;AACJ,GAAC,SAAUC,gBAAe;AAIxB,IAAAA,eAAc,QAAQ,IAAI;AAI1B,IAAAA,eAAc,OAAO,IAAI;AAIzB,IAAAA,eAAc,YAAY,IAAI;AAO9B,IAAAA,eAAc,SAAS,IAAI;AAAA,EAC7B,GAAG,gBAAgBD,YAAW,kBAAkBA,YAAW,gBAAgB,CAAC,EAAE;AAC9E,MAAI;AACJ,GAAC,SAAUE,QAAO;AAIhB,IAAAA,OAAM,MAAM,IAAI;AAIhB,IAAAA,OAAM,QAAQ,IAAI;AAIlB,IAAAA,OAAM,MAAM,IAAI;AAIhB,IAAAA,OAAM,SAAS,IAAI;AAInB,IAAAA,OAAM,SAAS,IAAI;AAAA,EACrB,GAAG,QAAQF,YAAW,UAAUA,YAAW,QAAQ,CAAC,EAAE;AACtD,MAAI;AACJ,GAAC,SAAUG,WAAU;AACnB,IAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAClC,IAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACjC,IAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACjC,IAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAClC,IAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAAA,EACpC,GAAG,WAAWH,YAAW,aAAaA,YAAW,WAAW,CAAC,EAAE;AACjE,GAAG,eAAe,aAAa,CAAC,EAAE;", "names": ["PrismPlugin", "mermaid", "marked", "katex", "MermaidAPI", "SecurityLevel", "Theme", "LogLevel"]}