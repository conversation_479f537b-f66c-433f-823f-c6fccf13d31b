{"name": "chatapp", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^17.0.0", "@angular/cdk": "^17.3.10", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/material": "^17.3.10", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "@capacitor/core": "^7.0.1", "@ctrl/tinycolor": "^4.1.0", "@editorjs/checklist": "^1.6.0", "@editorjs/code": "^2.9.3", "@editorjs/delimiter": "^1.4.2", "@editorjs/editorjs": "^2.30.8", "@editorjs/header": "^2.8.8", "@editorjs/image": "^2.10.2", "@editorjs/inline-code": "^1.5.1", "@editorjs/link": "^2.6.2", "@editorjs/list": "^2.0.6", "@editorjs/marker": "^1.4.0", "@editorjs/quote": "^2.7.6", "@editorjs/simple-image": "^1.6.0", "@editorjs/table": "^2.4.4", "@editorjs/warning": "^1.4.1", "@microsoft/signalr": "^8.0.7", "@types/luxon": "^3.4.2", "angular-split": "^19.0.0", "luxon": "^3.5.0", "marked": "^15.0.6", "ng-zorro-antd": "^17.4.1", "ngx-markdown": "^19.1.1", "nswag": "^14.2.0", "quill": "^2.0.3", "responsivevoice": "^0.4.1", "rxjs": "~7.8.0", "tailwind-scrollbar-hide": "^2.0.0", "tslib": "^2.3.0", "zone.js": "~0.14.2"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.10", "@angular/cli": "^17.0.10", "@angular/compiler-cli": "^17.0.0", "@tailwindcss/typography": "^0.5.16", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.21", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.2.2"}}