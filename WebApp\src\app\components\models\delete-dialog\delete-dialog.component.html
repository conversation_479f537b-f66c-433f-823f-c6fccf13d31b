<mat-dialog-content class="bg-[var(--background-white)] w-[26rem] mat-elevation-z4">
  <div class="flex items-center justify-between p-2 ">
    <div class="flex items-center gap-3">
      <i class="ri-error-warning-fill text-red-600 text-xl"></i>
      <span class="text-xl text-red-600 font-bold">Warning</span>
    </div>
    <button mat-icon-button class="hover:bg-[var(--secondary-purple)] transition-all duration-500" (click)="onCancel()">
      <i class="ri-close-large-fill text-[var(--text-dark)] text-xl"></i>
    </button>
  </div>
  <div class="p-6">
    <h2 class="text-lg font-semibold text-[var(--text-dark)]">{{ data.title }}</h2>
    <p class="text-[var(--text-dark)]">{{ data.message }}</p>
    @if (data.prompt?.name) {
      <p class="text-[var(--text-medium-gray)] text-sm mt-2">Prompt: {{ data.prompt.name }}</p>
    } @else if (data.prompt?.id) {
      <p class="text-[var(--text-medium-gray)] text-sm mt-2">Prompt: {{ data.prompt.prompt }}</p>
    }
  </div>
</mat-dialog-content>
<mat-dialog-actions class="!flex !items-center !justify-between gap-3 p-6 border-t border-[var(--hover-blue-gray)] bg-[var(--hover-blue-gray)] bg-opacity-20">
  <button mat-stroked-button color="primary" class="!text-[var(--text-dark)] hover:!bg-[var(--primary-purple)] !rounded-md !p-2 !bg-[var(--secondary-purple)] !border-none " (click)="onCancel()">
    Cancel
  </button>
  <button mat-raised-button color="warn" class="!bg-red-500 !p-2 !rounded-md hover:!bg-red-600 !text-[var(--text-dark)] " (click)="onConfirm()">
    Delete
  </button>
</mat-dialog-actions>
