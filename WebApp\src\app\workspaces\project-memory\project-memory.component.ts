import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import {
  ProjectMemoryServiceProxy,
  WorkspaceServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { NzModalService } from 'ng-zorro-antd/modal';
import { AddorEditWorksapceComponent } from '../../dialogs/addor-edit-worksapce/addor-edit-worksapce.component';
import { AddOrEditMemoryComponent } from '../../dialogs/add-or-edit-memory/add-or-edit-memory.component';

@Component({
  selector: 'app-project-memory',
  standalone: true,
  imports: [FormsModule, CommonModule, NzBreadCrumbModule, ServiceProxyModule],
  templateUrl: './project-memory.component.html',
  styleUrl: './project-memory.component.css',
  providers: [NzModalService],
})
export class ProjectMemoryComponent {
  isSidebarOpen = false; // Sidebar is closed by default
  showAddProjectMemoryDialog = false; // Dialog is hidden by default
  workspaceName: any;
  projectMemory = {
    projectCategory: '',
    projectDescription: '',
    status: '',
    workspace: '',
  };
  projectMemories: any[] = []; // Array to store project memories
  filteredMemories: any[] = []; // Array to store filtered project memories
  paginatedMemories: any[] = []; // Array to store paginated project memories
  workspaceId: any; // Variable to store the workspace ID
  isUpdating: boolean = false; // Flag to track if the form is in update mode

  // Pagination properties
  currentPage: number = 1;
  pageSize: number = 10;
  totalPages: number = 1;

  // Make Math available to the template
  Math = Math;
  constructor(
    private projectMemoryService: ProjectMemoryServiceProxy,
    private route: ActivatedRoute,
    private router: Router,
    private workspaceService: WorkspaceServiceProxy,
    private modalService: NzModalService
  ) { } //
  ngOnInit(): void {

    // Use ActivatedRoute to get workspace name from route params or URL
    this.route.paramMap.subscribe(params => {
      const workspaceParam = params.get('name');
      if (workspaceParam) {
        this.workspaceName = decodeURIComponent(workspaceParam);
        console.log('Workspace Name (from param):', this.workspaceName);
      } else {
        // Fallback: try to extract from URL if param is missing
        const url = this.router.url;
        const urlSegments = url.split('/');
        if (url.includes('/workspaces/') && urlSegments.length > 3) {
          this.workspaceName = decodeURIComponent(urlSegments[3]);
          console.log('Workspace Name (from URL):', this.workspaceName);
        }
      }
    });

    this.loadAllProjectMemories();
  }

  loadAllProjectMemories() {
    console.log('Loading project memories for workspace:', this.workspaceName);
    this.projectMemoryService
      .getAllProjectMemory(this.workspaceName)
      .subscribe((response: any) => {
        this.projectMemories = response;
        this.filteredMemories = [...this.projectMemories];
        this.updatePagination();
        console.log('Project Memories loaded:', this.projectMemories);
      });
  }

  /**
   * Update pagination based on current page and page size
   */
  updatePagination() {
    // Calculate total pages (minimum 1 page)
    this.totalPages = Math.max(1, Math.ceil(this.filteredMemories.length / this.pageSize));

    // Ensure current page is within bounds
    if (this.currentPage < 1) this.currentPage = 1;
    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages;

    // Get current page of memories
    if (this.filteredMemories.length === 0) {
      this.paginatedMemories = [];
    } else {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = Math.min(startIndex + this.pageSize, this.filteredMemories.length);
      this.paginatedMemories = this.filteredMemories.slice(startIndex, endIndex);
    }
  }

  /**
   * Go to a specific page
   */
  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePagination();
    }
  }

  /**
   * Go to the previous page
   */
  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePagination();
    }
  }

  /**
   * Go to the next page
   */
  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePagination();
    }
  }

  /**
   * Get page numbers to display in pagination
   * This method is no longer used with the updated pagination UI
   * Kept for reference
   */
  getPageNumbers(): (number | string)[] {
    const pages: (number | string)[] = [];

    if (this.totalPages <= 5) {
      // If 5 or fewer pages, show all page numbers
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      if (this.currentPage > 3) {
        // Show ellipsis if current page is far from the beginning
        pages.push('...');
      }

      // Show current page and surrounding pages
      const startPage = Math.max(2, this.currentPage - 1);
      const endPage = Math.min(this.totalPages - 1, this.currentPage + 1);

      for (let i = startPage; i <= endPage; i++) {
        if (i !== 1 && i !== this.totalPages) {
          pages.push(i);
        }
      }

      if (this.currentPage < this.totalPages - 2) {
        // Show ellipsis if current page is far from the end
        pages.push('...');
      }

      // Always show last page
      pages.push(this.totalPages);
    }

    return pages;
  }
  addProjectMemory() {
    let modalRef = this.modalService.create({
      nzContent: AddOrEditMemoryComponent,
      nzData: {
        isUpdating: this.isUpdating,
        project_memo: null,
      },
      nzFooter: null, // We handle the footer in the dialog component
      nzWidth: '500px',
    });
    modalRef.afterClose.subscribe((result) => {
      if (!result) return; // Check if result is null or undefined
      this.projectMemories.push(result);
      this.filteredMemories = [...this.projectMemories];
      this.updatePagination();
    });
    this.isUpdating = false; // Reset the flag to false when adding a new memory
    if (
      this.projectMemory.projectCategory &&
      this.projectMemory.projectDescription
    ) {
      const newMemory: any = {
        ...this.projectMemory,
        workspace: this.workspaceName,
      };
      this.projectMemoryService
        .saveProjectMemory(newMemory)
        .subscribe((response: any) => {
          this.projectMemories.push(response); // Add the new memory to the list
          this.showAddProjectMemoryDialog = false;
          this.clearInput(); // Reset form
        });
    } else {
      console.log('Please fill all fields');
    }
  }
  onUpdate(memory: any) {
    this.isUpdating = true; // Set the flag to true for update mode

    this.projectMemory = { ...memory }; // Populate the form with the selected memory data

    let modalRef = this.modalService.create({
      nzContent: AddOrEditMemoryComponent,
      nzData: {
        isUpdating: this.isUpdating,
        project_memo: this.projectMemory,
      },
      nzFooter: null, // We handle the footer in the dialog component
      nzWidth: '500px',
    });
    modalRef.afterClose.subscribe((result) => {
      if (!result) return; // Check if result is null or undefined

      // Update the memory in the list
      const index = this.projectMemories.findIndex(
        (m) => m.id === result.id
      );
      if (index !== -1) {
        this.projectMemories[index] = result;
        this.filteredMemories = [...this.projectMemories];
        this.updatePagination();
      }
    });
  }

  editMemory() {
    // Logic to edit the memory
    let obj: any = { ...this.projectMemory };
    this.projectMemoryService
      .saveProjectMemory(obj)
      .subscribe((response: any) => {
        console.log('Project Memory updated:', response);

        // Update the memory in the list
        const index = this.projectMemories.findIndex(m => m.id === response.id);
        if (index !== -1) {
          this.projectMemories[index] = response;
          this.filteredMemories = [...this.projectMemories];
          this.updatePagination();
        } else {
          this.loadAllProjectMemories(); // Reload if not found
        }

        this.clearInput();
        this.showAddProjectMemoryDialog = false; // Hide the dialog after saving
      });
  }
  deleteMemory(id: number) {
    this.projectMemoryService
      .deleteProjectMemory(id)
      .subscribe((response: any) => {
        console.log('Project Memory deleted:', response);
        this.projectMemories = this.projectMemories.filter(
          (memory) => memory.id !== id
        );
        this.filteredMemories = [...this.projectMemories];
        this.updatePagination();
      });
  }
  clearInput() {
    this.projectMemory = {
      projectCategory: '',
      projectDescription: '',
      status: '',
      workspace: this.workspaceName,
    }; // Reset form
    this.isUpdating = false; // Reset the flag to false when clearing input
  }
}
