/* Status Badges */
.status-completed {
  background-color: #dcfce7;
  color: #166534;
}

.status-in-progress {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-default {
  background-color: var(--hover-blue-gray);
  color: var(--text-medium-gray);
}

/* Priority Badges */
.priority-high {
  background-color: #fee2e2;
  color: #dc2626;
}

.priority-medium {
  background-color: #fef3c7;
  color: #92400e;
}

.priority-low {
  background-color: #dcfce7;
  color: #166534;
}

.priority-default {
  background-color: var(--hover-blue-gray);
  color: var(--text-medium-gray);
}

/* Empty State */
.empty-state {
  padding: 2rem;
  text-align: center;
}

/* Table Hover Effects */
tbody tr:hover {
  background-color: var(--hover-blue-gray) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .overflow-x-auto {
    overflow-x: auto;
  }

  table {
    min-width: 600px;
  }
}

/* Dark Mode Support */
:host-context(.dark-mode) .status-completed {
  background-color: rgba(34, 197, 94, 0.2);
  color: #4ade80;
}

:host-context(.dark-mode) .status-in-progress {
  background-color: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
}

:host-context(.dark-mode) .status-pending {
  background-color: rgba(245, 158, 11, 0.2);
  color: #fbbf24;
}

:host-context(.dark-mode) .priority-high {
  background-color: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

:host-context(.dark-mode) .priority-medium {
  background-color: rgba(245, 158, 11, 0.2);
  color: #fbbf24;
}

:host-context(.dark-mode) .priority-low {
  background-color: rgba(34, 197, 94, 0.2);
  color: #4ade80;
}
