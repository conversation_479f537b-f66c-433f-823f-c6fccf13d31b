import { Component, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSelectModule } from 'ng-zorro-antd/select';
import {
  AgentDefinitionServiceProxy,
  AiServiceProxy,
  ModelDetailsServiceProxy,
  PluginServiceProxy,
  ResponseMessage,
  DatabaseConnectionServiceProxy,
  DatabaseConnectionDto,
  DocsServiceProxy
} from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { finalize } from 'rxjs/operators';
import { RemoveProviderPrefixPipe } from "../../../../shared/pipes/remove-provider-prefix.pipe";
import { NzModalService } from 'ng-zorro-antd/modal';

// Define interface for chat messages
interface ChatMessage {
  sender: 'user' | 'agent' | 'loading';
  content: string;
  timestamp: Date;
}

@Component({
  selector: 'app-add-or-edit-agent',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    NzInputModule,
    NzAutocompleteModule,
    NzSelectModule,
    NzRadioModule,
    NzBreadCrumbModule,
    ServiceProxyModule,
    RouterLink,
    RemoveProviderPrefixPipe
  ],
  providers: [NzModalService],
  templateUrl: './add-or-edit-agents.component.html', // Updated to match selector,
  styles: [
    `
      ::ng-deep .ant-select-multiple .ant-select-selection-item{
        background-color: var(--secondary-purple) !important;
      }

      .toggle-switch {
        position: relative;
        width: 44px;
        height: 24px;
        background-color: #e5e7eb;
        border-radius: 12px;
        transition: background-color 0.3s ease;
        cursor: pointer;
      }

      .toggle-switch.active {
        background-color: #f59e0b;
      }

      .toggle-slider {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background-color: white;
        border-radius: 50%;
        transition: transform 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .toggle-switch.active .toggle-slider {
        transform: translateX(20px);
      }

      .toggle-switch:hover {
        background-color: #d1d5db;
      }

      .toggle-switch.active:hover {
        background-color: #d97706;
      }
    `,
  ],
})
export class AddOrEditAgentsComponent implements AfterViewChecked {
  @ViewChild('sidebarContainer') private sidebarContainer!: ElementRef;

  // Add a flag to track if scrolling is needed
  private shouldScrollToBottom: boolean = false;

  // Renamed to singular for consistency
  agents: any[] = [];
  isEditing: boolean = false;
  currentAgent: any = {
    agentName: '',
    instructions: '',
    userInstructions: '',
    modelName: '',
    tools: [],
    workspace: '',
    sourceType: '',
    sourceName: '',
    sourceConfig: '',
    isSystemAgent: false,
  };
  modelSearchQuery: string = '';
  filteredModels: any[] = [];
  models: any[] = [];
  agentName: string = '';
  workspaceName: string = '';
  plugins: any[] = [];
  selectedPlugins: string[] = [];
  routerParts: string[] = [];

  // Agent description for AI generation
  isGeneratingInstructions: boolean = false;
  isInstructionsDisabled: boolean = true; // Initially disable instructions field

  // Test agent properties
  testQuestion: string = '';
  testResponse: string = '';
  testError: string = '';
  isTestingAgent: boolean = false;
  testSuccessful: boolean = true; // Always enabled as testing is optional

  // Chat sidebar properties
  showTestResults: boolean = false;

  // Chat properties
  chatMessages: ChatMessage[] = [];
  chatInput: string = '';

  // Database source properties
  databaseConnections: DatabaseConnectionDto[] = [];

  // Document source properties
  availableDocuments: any[] = [];
  workspaceDocuments: any[] = [];

  sourceTypes = [
    { label: 'None', value: '' },
    { label: 'Database', value: 'Database' },
    { label: 'Documents', value: 'Documents' }
  ];

  constructor(
    private agentService: AgentDefinitionServiceProxy,
    private router: Router,
    private aiService: AiServiceProxy,
    private modelDetailsService: ModelDetailsServiceProxy,
    private _pluginService: PluginServiceProxy,
    private databaseConnectionService: DatabaseConnectionServiceProxy,
    private docsService: DocsServiceProxy,
    private modalService: NzModalService
  ) { }

  /**
   * Scroll to the bottom of the chat container when new messages are added
   */
  ngAfterViewChecked() {
    // Only scroll to bottom if needed
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false; // Reset the flag after scrolling
    }
  }

  /**
   * Scroll to the bottom of the sidebar container
   */
  scrollToBottom(): void {
    try {
      if (this.sidebarContainer && this.showTestResults) {
        setTimeout(() => {
          if (this.sidebarContainer && this.sidebarContainer.nativeElement) {
            this.sidebarContainer.nativeElement.scrollTop = this.sidebarContainer.nativeElement.scrollHeight;
            console.log('Scrolled to bottom, height:', this.sidebarContainer.nativeElement.scrollHeight);
          }
        }, 200);
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

  ngOnInit(): void {
    // Extract workspace and agent information from URL
    this.routerParts = this.router.url.split('/');
    console.log(this.routerParts);

    // Load database connections for source selection
    this.loadDatabaseConnections();

    if (this.routerParts.includes('settings') && this.routerParts.includes('agents')) {
      // Handle settings/agents route
      if (this.routerParts.includes('new')) {
        this.agentName = 'new';
        this.isEditing = false;
        this.workspaceName = 'Default';
      } else {
        this.agentName = decodeURIComponent(this.routerParts[this.routerParts.indexOf('agents') + 1]);
        this.isEditing = true;
        this.loadAgent(this.agentName); // Load existing agent data
      }
    } else if (this.routerParts.includes('chat') && this.routerParts.includes('workspace')) {
      // Handle chat/workspace route
      const workspaceIndex = this.routerParts.indexOf('workspace');
      if (workspaceIndex !== -1 && this.routerParts.length > workspaceIndex + 1) {
        this.workspaceName = decodeURIComponent(this.routerParts[workspaceIndex + 1]);
        console.log('Workspace Name:', this.workspaceName);

        if (this.routerParts.includes('new')) {
          this.agentName = 'new';
          this.isEditing = false;
        } else {
          this.agentName = decodeURIComponent(this.routerParts[this.routerParts.indexOf('agents') + 1]);
          this.isEditing = true;
          this.loadAgent(this.agentName); // Load existing agent data
        }
      }
    }

    // Load models and plugins (common for both contexts)
    this.loadModels();
    this.loadPlugins();
    this.loadDocuments();
  }

  loadAgent(agentName: string) {
    // Fetch agent details for editing
    this.agentService.getByAgentName(agentName).subscribe((agent: any) => {
      this.currentAgent = { ...agent };
      this.modelSearchQuery = this.currentAgent.modelName || '';
      this.selectedPlugins = [...(this.currentAgent.tools || [])];
      this.workspaceName = this.currentAgent.workspace || 'Default';

      // For editing existing agents, we still require testing before saving
      // to ensure any changes don't break the agent
      this.testSuccessful = false;
    });
  }

  loadModels() {
    this.modelDetailsService.getAllActiveModel().subscribe((models: any) => {
      this.filteredModels = models;
      this.models = models;
    });
  }

  loadPlugins() {
    this._pluginService.getAllPluginNames().subscribe((res: any) => {
      this.plugins = [...res.message];
    });
  }

  onChange(event: any) {
    const query = event.target.value.toLowerCase();
    this.filteredModels = this.models.filter((m: any) =>
      m.modelName.toLowerCase().includes(query)
    );
  }

  updateModel(modelName: string) {
    this.currentAgent.modelName = modelName;
    // Also update the search box to show only the model name (without provider) for user display
    this.modelSearchQuery = this.removeProviderPrefix(modelName);
  }

  /**
   * Remove provider prefix for display (e.g., azure_gpt-4o-mini -> gpt-4o-mini)
   */
  removeProviderPrefix(value: string): string {
    if (!value) return '';
    const parts = value.split('_');
    return parts.slice(1).join('_');
  }

  /**
   * Check if the agent can generate instructions
   * @returns boolean indicating if the agent has the required fields to generate instructions
   */
  canGenerateInstructions(): boolean {
    return (
      this.currentAgent.agentName?.trim() !== '' &&
      this.currentAgent.modelName?.trim() !== ''
    );
  }

  /**
   * Generate agent instructions using AI
   */
  generateInstructions() {
    this.isGeneratingInstructions = true;

    // Prepare the prompt for the instruction generator agent
    const prompt = `Generate detailed instructions for an AI agent with the following description:

Agent Name: ${this.currentAgent.agentName || 'Unnamed Agent'}
Description: ${this.currentAgent.userInstructions || 'No description provided'}
Selected Plugins: ${this.selectedPlugins.join(', ')}

The instructions should be comprehensive and clearly define the agent's purpose, capabilities, and limitations.
Format the instructions in a clear, professional manner that will guide the AI in responding appropriately to user queries.
`;

    // Call the instruction generator agent
    this.aiService.callAgent('InstructionGeneratorAgent', prompt)
      .pipe(
        finalize(() => {
          this.isGeneratingInstructions = false;
        })
      )
      .subscribe({
        next: (response: ResponseMessage) => {
          if (response && response.message) {
            // Update the instructions field with the generated content
            this.currentAgent.instructions = response.message;
            // Enable the instructions field for editing
            this.isInstructionsDisabled = false;
            console.log('Instructions generated successfully');
          } else {
            console.error('Failed to generate instructions: Empty response');
          }
        },
        error: (error) => {
          console.error('Error generating instructions:', error);
          // If there's an error with the instruction generator agent, try a fallback approach
          this.generateFallbackInstructions();
        }
      });
  }

  /**
   * Generate fallback instructions if the instruction generator agent fails
   */
  private generateFallbackInstructions() {
    // Create a basic template based on the agent description
    this.currentAgent.instructions = `You are ${this.currentAgent.agentName || 'an AI assistant'}.

Purpose: ${this.currentAgent.userInstructions || 'No description provided'}

${this.selectedPlugins.length > 0 ? 'You have access to the following plugins: ' + this.selectedPlugins.join(', ') : ''}

When responding to user queries:
1. Be helpful, accurate, and concise
2. Stay within your defined purpose and capabilities
3. If you're unsure about something, acknowledge your limitations
4. Provide clear, well-structured responses
`;
    // Enable the instructions field for editing
    this.isInstructionsDisabled = false;
  }

  saveAgent() {
    this.currentAgent.tools = this.selectedPlugins;
    this.currentAgent.workspace = this.workspaceName;

    // Check if this is a protected agent being edited
    if (this.isEditing && this.currentAgent.isSystemAgent) {
      this.modalService.confirm({
        nzTitle: 'Confirm Protected Agent Update',
        nzContent: `You are about to update "${this.currentAgent.agentName}" which is a protected system agent. Are you sure you want to proceed with these changes?`,
        nzOkText: 'Update Agent',
        nzOkType: 'primary',
        nzCancelText: 'Cancel',
        nzWidth: 450,
        nzCentered: true,
        nzMaskClosable: false,
        nzClosable: true,
        nzOnOk: () => {
          this.performSave();
        },
        nzOnCancel: () => {
          console.log('Protected agent update cancelled');
        }
      });
    } else {
      this.performSave();
    }
  }

  private performSave() {
    this.agentService.createOrUpdate(this.currentAgent).subscribe((res: any) => {
      // Check the URL context and redirect accordingly
      if (this.routerParts.includes('settings') && this.routerParts.includes('agents')) {
        // Redirect to settings/agents
        this.router.navigate(['settings', 'agents']);

      } else if (this.routerParts.includes('chat') && this.routerParts.includes('workspace')) {
        // Redirect to workspaces/<workspaceName>/agents

        this.router.navigate(['chat', 'workspace', this.workspaceName, 'agents']);
      } else {
        console.error('Unknown URL context. Unable to determine redirection.');
      }
    });
  }

  /**
   * Check if the agent has required fields filled
   * @returns boolean indicating if the agent has required fields
   */
  hasRequiredFields(): boolean {
    // Check if all required fields are filled
    return (
      this.currentAgent.agentName?.trim() !== '' &&
      this.currentAgent.instructions?.trim() !== '' &&
      this.currentAgent.modelName?.trim() !== '' &&
      this.workspaceName?.trim() !== '' &&
      this.currentAgent.userInstructions?.trim() !== ''
    );
  }

  /**
   * Open the chat interface for testing the agent
   */
  testAgent(): void {
    console.log("Opening chat interface");

    // Make sure we have the required fields
    if (!this.hasRequiredFields()) {
      console.error("Missing required fields");
      return;
    }

    // Clear previous chat messages when starting a new test
    this.chatMessages = [];

    // Add a welcome message
    const welcomeMessage: ChatMessage = {
      sender: 'agent',
      content: `Hello! I'm your ${this.currentAgent.agentName || 'AI'} agent. How can I help you today?`,
      timestamp: new Date()
    };
    this.chatMessages.push(welcomeMessage);

    // Show the chat sidebar with a slight delay to ensure smooth animation
    setTimeout(() => {
      this.showTestResults = true;

      // Force scroll to bottom after sidebar is shown
      setTimeout(() => {
        this.scrollToBottom();
        // Try one more time after a longer delay to ensure content is fully rendered
        setTimeout(() => {
          this.scrollToBottom();
        }, 500);
      }, 300);
    }, 50);
  }

  /**
   * Send a message in the chat
   */
  sendChatMessage(): void {
    if (!this.chatInput.trim()) {
      return;
    }

    console.log("Sending message:", this.chatInput);

    // Add user message to chat
    const userMessage: ChatMessage = {
      sender: 'user',
      content: this.chatInput.trim(),
      timestamp: new Date()
    };
    this.chatMessages.push(userMessage);

    // Store the question
    const question = this.chatInput.trim();

    // Clear input field
    this.chatInput = '';

    // Add loading message
    const loadingMessage: ChatMessage = {
      sender: 'loading',
      content: '',
      timestamp: new Date()
    };
    this.chatMessages.push(loadingMessage);

    // Force scroll to bottom with multiple attempts to ensure it works
    setTimeout(() => {
      this.scrollToBottom();
      // Try again after a short delay
      setTimeout(() => {
        this.scrollToBottom();
      }, 300);
    }, 100);

    // Set testing state
    this.isTestingAgent = true;

    // Make sure we have an agent name
    if (!this.currentAgent.agentName) {
      this.chatMessages.pop(); // Remove loading message
      const errorMessage: ChatMessage = {
        sender: 'agent',
        content: 'Error: Agent name is required',
        timestamp: new Date()
      };
      this.chatMessages.push(errorMessage);
      this.isTestingAgent = false;
      return;
    }

    // Call the agent
    console.log("Calling agent:", this.currentAgent.agentName);
    this.agentService.testAgentCall(question, this.currentAgent)
      .subscribe({
        next: (response: ResponseMessage) => {
          // Remove loading message
          this.chatMessages.pop();

          // Add agent response
          const agentMessage: ChatMessage = {
            sender: 'agent',
            content: response.message || 'No response from agent',
            timestamp: new Date()
          };
          this.chatMessages.push(agentMessage);

          // Update test state
          this.isTestingAgent = false;
          this.testSuccessful = true;

          // Scroll to bottom with multiple attempts
          this.scrollToBottom();
          setTimeout(() => {
            this.scrollToBottom();
          }, 300);
        },
        error: (error: any) => {
          // Remove loading message
          this.chatMessages.pop();

          // Add error message
          const errorMessage: ChatMessage = {
            sender: 'agent',
            content: 'Error: ' + (error.message || 'Unknown error occurred'),
            timestamp: new Date()
          };
          this.chatMessages.push(errorMessage);

          // Update test state
          this.isTestingAgent = false;

          // Scroll to bottom with multiple attempts
          this.scrollToBottom();
          setTimeout(() => {
            this.scrollToBottom();
          }, 300);
        }
      });
  }

  /**
   * Load available database connections for source selection
   */
  loadDatabaseConnections(): void {
    this.databaseConnectionService.getActive().subscribe({
      next: (connections) => {
        this.databaseConnections = connections;
      },
      error: (error) => {
        console.error('Error loading database connections:', error);
      }
    });
  }

  /**
   * Handle source type change
   */
  onSourceTypeChange(): void {
    // Reset source name and config when type changes
    this.currentAgent.sourceName = '';
    this.currentAgent.sourceConfig = '';

    // Load documents if Documents source type is selected
    if (this.currentAgent.sourceType === 'Documents') {
      this.loadDocuments();
    }
  }

  /**
   * Handle database connection selection
   */
  onDatabaseConnectionChange(): void {
    const selectedConnection = this.databaseConnections.find(
      conn => conn.name === this.currentAgent.sourceName
    );

    if (selectedConnection) {
      // Store connection configuration
      this.currentAgent.sourceConfig = JSON.stringify({
        connectionId: selectedConnection.id,
        databaseType: selectedConnection.databaseType
      });
    }
  }

  /**
   * Get available database connections for dropdown
   */
  getAvailableDatabaseConnections(): DatabaseConnectionDto[] {
    return this.databaseConnections.filter(conn =>
      conn.isActive && conn.isSchemaExtracted
    );
  }

  /**
   * Load available documents for source selection
   */
  loadDocuments(): void {
    // Load all documents across all workspaces for agent creation
    this.docsService.getAll().subscribe({
      next: (documents) => {
        this.availableDocuments = documents;
        // Group documents by workspace for better organization
        this.workspaceDocuments = this.groupDocumentsByWorkspace(documents);
      },
      error: (error) => {
        console.error('Error loading documents:', error);
        // Fallback: try to load by current workspace if getAll fails
        if (this.workspaceName) {
          this.docsService.getByWorkspaceName(this.workspaceName).subscribe({
            next: (docs) => {
              this.availableDocuments = docs;
              this.workspaceDocuments = [{ workspace: this.workspaceName, documents: docs }];
            },
            error: (err) => console.error('Fallback document loading failed:', err)
          });
        }
      }
    });
  }

  /**
   * Handle document source selection
   */
  onDocumentSourceChange(): void {
    if (this.currentAgent.sourceName) {
      // Set source configuration for document source
      this.currentAgent.sourceConfig = JSON.stringify({
        maxDocuments: 10,
        includeMetadata: true
      });
    }
  }

  /**
   * Get available documents for dropdown
   */
  getAvailableDocuments(): any[] {
    return this.availableDocuments || [];
  }

  /**
   * Group documents by workspace name
   */
  groupDocumentsByWorkspace(documents: any[]): any[] {
    const grouped = documents.reduce((acc, doc) => {
      const workspace = doc.workspaceName || 'Default';
      if (!acc[workspace]) {
        acc[workspace] = [];
      }
      acc[workspace].push(doc);
      return acc;
    }, {});

    return Object.keys(grouped).map(workspace => ({
      workspace,
      documents: grouped[workspace]
    }));
  }

  /**
   * Get available workspaces for dropdown
   */
  getAvailableWorkspaces(): string[] {
    const workspaces = new Set<string>();
    this.availableDocuments?.forEach(doc => {
      if (doc.workspaceName) {
        workspaces.add(doc.workspaceName);
      }
    });
    return Array.from(workspaces).sort();
  }
}
