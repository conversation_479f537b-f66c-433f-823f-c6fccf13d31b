import { Component, Inject } from '@angular/core';
import { NzModalRef, NzModalService, NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import {
  UserAccountServiceProxy,
  AssignWorkspaceServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { CommonModule } from '@angular/common';
import { NzTableModule } from 'ng-zorro-antd/table';
import { AddUserComponent } from '../add-user/add-user.component';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { AuthService } from '../../../shared/services/auth.service';

@Component({
  selector: 'app-workspace-users-dialog',
  standalone: true,
  imports: [ServiceProxyModule, CommonModule, NzTableModule, NzButtonModule],
  templateUrl: './workspace-users-dialog.component.html',
  styleUrl: './workspace-users-dialog.component.css',
})
export class WorkspaceUsersDialogComponent {
  constructor(
    private modalRef: NzModalRef,
    private userAccountService: UserAccountServiceProxy,
    private modalService: NzModalService,
    private assignWorkspaceService: AssignWorkspaceServiceProxy,
    public authService: AuthService,
    @Inject(NZ_MODAL_DATA) public data: { id: number }
  ) { }
  users: any = []; // Array to hold user data
  workspaceId: number = this.data.id; // Get the workspace ID from modal data

  // Pagination properties
  currentPage: number = 1;
  pageSize: number = 10;
  totalPages: number = 1;
  previousPageSize: number = 10;
  paginatedUsers: any[] = [];
  filteredUsers: any[] = [];

  // Pagination dropdown state
  isPageSizeDropdownOpen = false;
  isDropdownAbove = false;

  ngOnInit(): void {
    console.log(this.data.id);
    if (this.data.id) {
      this.loadUsers();
    }
  }
  get isAdmin() {
    return this.authService.isAdmin();
  }
  loadUsers() {
    console.log(this.data.id);

    this.assignWorkspaceService
      .getUsersByWorkspaceId(this.data.id)
      .subscribe((users: any) => {
        this.users = users;
        console.log(users);
        this.updateFilteredUsers();
      });
    // this.userAccountService.getAll().subscribe((users: any) => {
    //   this.users = users;
    //   console.log(this.users);
    // });
  }
  addUser() {
    const modelRef = this.modalService.create({
      nzTitle: 'User List',
      nzContent: AddUserComponent,
      nzData: {
        id: this.data.id, // Pass any necessary parameters to the dialog component
        users: this.users, // Pass the current user list to the dialog component
      },
      nzWidth: '800px',
      nzFooter: null, // We handle the footer in the dialog component

      nzMaskClosable: true,  // Allows closing on click outside the modal
      nzClosable: true,
    });
    modelRef.afterClose.subscribe((result) => {
      if (result && result.addedUsers) {
        // Update the user list with added users
        this.users = [...this.users, ...result.addedUsers];
        this.updateFilteredUsers();
      }
    });
  }
  removeUser(user: any) {
    this.assignWorkspaceService
      .removeUser(this.data.id, user.email)
      .subscribe((response: any) => {
        console.log('User removed:', response);
        // Remove the user from the list
        this.users = this.users.filter((u: any) => u.email !== user.email);
        this.updateFilteredUsers();
      });
  }
  closeDialog(): void {
    this.modalRef.close({
      // Return any necessary data to the parent component
    });
  }

  updateFilteredUsers() {
    // Apply any filtering logic here if needed
    this.filteredUsers = this.users || [];
    this.updatePagination();
  }

  updatePagination() {
    this.pageSize = Number(this.pageSize);
    const pageSizeChanged = this.previousPageSize !== this.pageSize;
    this.totalPages = Math.max(1, Math.ceil(this.filteredUsers.length / this.pageSize));
    if (pageSizeChanged) {
      this.currentPage = 1;
    }
    if (this.currentPage < 1) this.currentPage = 1;
    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages;
    if (this.filteredUsers.length === 0) {
      this.paginatedUsers = [];
    } else {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = Math.min(startIndex + this.pageSize, this.filteredUsers.length);
      this.paginatedUsers = this.filteredUsers.slice(startIndex, endIndex);
    }
    this.previousPageSize = this.pageSize;
  }

  togglePageSizeDropdown() {
    this.isPageSizeDropdownOpen = !this.isPageSizeDropdownOpen;
    if (this.isPageSizeDropdownOpen) {
      setTimeout(() => {
        const btn = document.getElementById('pageSizeDropdownBtn');
        const dropdown = document.getElementById('pageSizeDropdownMenu');
        if (btn && dropdown) {
          const rect = btn.getBoundingClientRect();
          const dropdownHeight = dropdown.offsetHeight || 160;
          const spaceBelow = window.innerHeight - rect.bottom;
          this.isDropdownAbove = spaceBelow < dropdownHeight + 16;
        }
      }, 0);
    }
  }

  selectPageSize(size: number) {
    this.pageSize = size;
    this.isPageSizeDropdownOpen = false;
    this.currentPage = 1;
    this.updatePagination();
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePagination();
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePagination();
    }
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePagination();
    }
  }
}
