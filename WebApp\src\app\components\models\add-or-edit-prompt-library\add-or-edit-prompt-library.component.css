:host {
  display: block;
}

/* Remove dialog borders */
:host ::ng-deep .mat-dialog-content {
  padding: 0 !important;
  border: none !important;
  outline: none !important;
}

:host ::ng-deep .mat-dialog-container {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Remove form field borders completely */
:host ::ng-deep .mat-form-field-wrapper {
  padding-bottom: 0 !important;
  border: none !important;
  outline: none !important;
}

:host ::ng-deep .mat-form-field-flex {
  background-color: transparent !important;
  border: none !important;
  border-radius: var(--border-radius-small) !important;
  padding: 0.75rem !important;
  outline: none !important;
}

:host ::ng-deep .mat-form-field-outline {
  display: none !important; /* Remove default border */
  border: none !important;
  outline: none !important;
}

:host ::ng-deep .mat-form-field-outline-start,
:host ::ng-deep .mat-form-field-outline-end,
:host ::ng-deep .mat-form-field-outline-gap {
  border: none !important;
  outline: none !important;
}

:host ::ng-deep .mat-form-field {
  border: none !important;
  outline: none !important;
}

/* Remove fill appearance borders */
:host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
  border: none !important;
  outline: none !important;
  background-color: transparent !important;
}

:host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-underline {
  display: none !important;
}

:host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-subscript-wrapper {
  display: none !important;
}

/* Remove all Material Design focus styles */
:host ::ng-deep .mat-form-field.mat-focused .mat-form-field-flex {
  border: none !important;
  outline: none !important;
}

:host ::ng-deep .mat-form-field-label {
  display: none !important; /* Hide default label as we use custom <label> */
}

:host ::ng-deep .mat-form-field.mat-focused .mat-form-field-label {
  color: var(--primary-purple) !important;
}

:host ::ng-deep .mat-input-element {
  background-color: transparent !important;
  border: none !important;
  outline: none !important;
}

/* Remove button borders but keep custom styling */
:host ::ng-deep .mat-button,
:host ::ng-deep .mat-raised-button,
:host ::ng-deep .mat-stroked-button,
:host ::ng-deep .mat-flat-button,
:host ::ng-deep .mat-icon-button {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Keep custom focus styles for buttons */
:host ::ng-deep .mat-button:focus,
:host ::ng-deep .mat-raised-button:focus,
:host ::ng-deep .mat-stroked-button:focus,
:host ::ng-deep .mat-flat-button:focus,
:host ::ng-deep .mat-icon-button:focus {
  outline: none !important;
  border: none !important;
}

/* Remove dialog backdrop and container borders */
:host ::ng-deep .cdk-overlay-pane {
  border: none !important;
  outline: none !important;
}

:host ::ng-deep .cdk-overlay-backdrop {
  border: none !important;
  outline: none !important;
}

/* Remove any remaining Material Design borders */
:host ::ng-deep .mat-mdc-dialog-container {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

:host ::ng-deep .mat-mdc-dialog-surface {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Remove form field underline */
:host ::ng-deep .mat-form-field-underline {
  display: none !important;
}

:host ::ng-deep .mat-form-field-subscript-wrapper {
  display: none !important;
}

/* Remove all Material Design focus indicators */
:host ::ng-deep *:focus {
  /* Allow custom focus styles to work */
}

:host ::ng-deep .mat-focus-indicator {
  display: none !important;
}

/* Remove any ripple effects that might show borders */
:host ::ng-deep .mat-ripple {
  display: none !important;
}

/* Ensure textarea has no borders but keep custom styling */
:host ::ng-deep textarea.mat-input-element {
  border: none !important;
  outline: none !important;
  resize: none !important;
}

/* Remove any Material Design elevation/borders */
:host ::ng-deep .mat-elevation-z0,
:host ::ng-deep .mat-elevation-z1,
:host ::ng-deep .mat-elevation-z2,
:host ::ng-deep .mat-elevation-z3,
:host ::ng-deep .mat-elevation-z4,
:host ::ng-deep .mat-elevation-z5,
:host ::ng-deep .mat-elevation-z6,
:host ::ng-deep .mat-elevation-z7,
:host ::ng-deep .mat-elevation-z8,
:host ::ng-deep .mat-elevation-z9,
:host ::ng-deep .mat-elevation-z10,
:host ::ng-deep .mat-elevation-z11,
:host ::ng-deep .mat-elevation-z12,
:host ::ng-deep .mat-elevation-z13,
:host ::ng-deep .mat-elevation-z14,
:host ::ng-deep .mat-elevation-z15,
:host ::ng-deep .mat-elevation-z16,
:host ::ng-deep .mat-elevation-z17,
:host ::ng-deep .mat-elevation-z18,
:host ::ng-deep .mat-elevation-z19,
:host ::ng-deep .mat-elevation-z20,
:host ::ng-deep .mat-elevation-z21,
:host ::ng-deep .mat-elevation-z22,
:host ::ng-deep .mat-elevation-z23,
:host ::ng-deep .mat-elevation-z24 {
  box-shadow: none !important;
  border: none !important;
}

/* Additional rules to remove any remaining borders */
:host ::ng-deep .mat-form-field-infix {
  border: none !important;
  outline: none !important;
}

:host ::ng-deep .mat-form-field-suffix,
:host ::ng-deep .mat-form-field-prefix {
  border: none !important;
  outline: none !important;
}

:host ::ng-deep .mat-form-field-hint-wrapper {
  border: none !important;
  outline: none !important;
}

/* Remove any MDC (Material Design Components) borders */
:host ::ng-deep .mdc-text-field {
  border: none !important;
  outline: none !important;
}

:host ::ng-deep .mdc-text-field--outlined {
  border: none !important;
  outline: none !important;
}

:host ::ng-deep .mdc-text-field--filled {
  border: none !important;
  outline: none !important;
}

:host ::ng-deep .mdc-notched-outline {
  display: none !important;
}

:host ::ng-deep .mdc-line-ripple {
  display: none !important;
}

/* Remove any remaining outline borders */
:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  display: none !important;
}

:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-thick {
  display: none !important;
}

/* Ensure no borders on any Material components */
:host ::ng-deep .mat-mdc-form-field {
  border: none !important;
  outline: none !important;
}

:host ::ng-deep .mat-mdc-text-field-wrapper {
  border: none !important;
  outline: none !important;
}

:host ::ng-deep .mat-mdc-form-field-focus-overlay {
  display: none !important;
}

/* Remove any conflicting Material styles that might override custom borders */
:host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex,
:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-flex {
  background-color: transparent !important;
  border: none !important;
  outline: none !important;
}

/* Ensure input elements can show custom borders */
:host ::ng-deep input.mat-input-element,
:host ::ng-deep textarea.mat-input-element {
  background-color: transparent !important;
  border: none !important;
  outline: none !important;
}

/* Remove any Material Design padding that might interfere */
:host ::ng-deep .mat-form-field-infix {
  padding: 0 !important;
  border: none !important;
}

/* Ensure the form field wrapper doesn't interfere with custom styling */
:host ::ng-deep .mat-form-field-wrapper {
  padding-bottom: 0 !important;
  border: none !important;
  outline: none !important;
  background-color: transparent !important;
}

/* Custom form field styling - allow custom borders on all sides */
:host ::ng-deep .custom-form-field .mat-form-field-flex {
  border: none !important;
  border-radius: var(--border-radius-small) !important;
  background-color: var(--background-light-gray) !important;
  transition: var(--transition-default) !important;
}

/* Remove all focus styles from Material */
:host ::ng-deep .custom-form-field.mat-focused .mat-form-field-flex {
  border: none !important;
  outline: none !important;
}

/* Ensure proper spacing for custom form fields */
:host ::ng-deep .custom-form-field .mat-form-field-infix {
  padding: 0.75rem !important;
  border: none !important;
}

:host ::ng-deep .custom-form-field .mat-input-element {
  background-color: transparent !important;
  border: none !important;
  outline: none !important;
}

/* Allow Tailwind border classes to work on all sides */
:host ::ng-deep .border {
  border-style: solid !important;
  border-width: 1px !important;
}

:host ::ng-deep .border-\[var\(--hover-blue-gray\)\] {
  border-color: var(--hover-blue-gray) !important;
}

/* Allow Tailwind focus styles to work */
:host ::ng-deep .focus\:outline-none:focus {
  outline: none !important;
}

:host ::ng-deep .focus\:border-none:focus {
  border: none !important;
}

:host ::ng-deep .focus\:outline-\[var\(--primary-purple\)\]:focus {
  outline: 2px solid var(--primary-purple) !important;
  outline-offset: 0 !important;
}

/* Remove any Material Design focus rings */
:host ::ng-deep .mat-form-field.mat-focused .mat-form-field-flex::before,
:host ::ng-deep .mat-form-field.mat-focused .mat-form-field-flex::after {
  display: none !important;
}

/* Ensure custom borders work properly */
:host ::ng-deep .custom-form-field input.mat-input-element,
:host ::ng-deep .custom-form-field textarea.mat-input-element {
  border: 1px solid var(--hover-blue-gray) !important;
  outline: none !important;
  transition: var(--transition-default) !important;
}

:host ::ng-deep .custom-form-field input.mat-input-element:focus,
:host ::ng-deep .custom-form-field textarea.mat-input-element:focus {
  border: none !important;
  outline: 2px solid var(--primary-purple) !important;
  outline-offset: 0 !important;
}
