<div class="p-4" style="background-color: var(--background-light-gray); min-height: 100vh;">
  <div class="flex justify-between mb-4">
    <h2 class="text-2xl font-bold" style="color: var(--text-dark);">Agent Evaluation</h2>
  </div>

  <nz-spin *ngIf="loading" class="flex justify-center mt-8"></nz-spin>

  <div *ngIf="!loading" class="modern-tabset">
    <!-- Modern Tabs for the two main sections -->
    <div class="tab-container-modern">
      <div class="tab-header-modern"
        style="border-bottom: 1px solid var(--hover-blue-gray); border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;">
        <div class="flex">
          <button class="mr-2" [class]="'tab-button' + (activeTabIndex === 0 ? ' active' : '')" (click)="onTabChange(0)"
            [style]="activeTabIndex === 0 ?
                            'color: white; border-bottom-color: transparent; background-color: var(--primary-purple); border-radius: var(--border-radius-small);' :
                            'color: var(--text-medium-gray); border-bottom-color: transparent; border-radius: var(--border-radius-small);'">
            <i class="ri-list-check-2"></i>
            <span>Prompts</span>
            <span class="tab-badge"
              [style]="activeTabIndex === 0 ?
                                'background-color: rgba(255, 255, 255, 0.2); color: white; border-radius: var(--border-radius-small);' :
                                'background-color: var(--hover-blue-gray); color: var(--text-dark); border-radius: var(--border-radius-small);'">
              {{ getPromptsCount() }}
            </span>
          </button>
          <button [class]="'tab-button' + (activeTabIndex === 1 ? ' active' : '')" (click)="onTabChange(1)"
            [style]="activeTabIndex === 1 ?
                            'color: white; border-bottom-color: transparent; background-color: var(--primary-purple); border-radius: var(--border-radius-small);' :
                            'color: var(--text-medium-gray); border-bottom-color: transparent; border-radius: var(--border-radius-small);'">
            <i class="ri-play-circle-line"></i>
            <span>Execution</span>
            <span *ngIf="hasExecutedPrompts" class="tab-badge"
              [style]="activeTabIndex === 1 ?
                                'background-color: rgba(255, 255, 255, 0.2); color: white; border-radius: var(--border-radius-small);' :
                                'background-color: var(--hover-blue-gray); color: var(--text-dark); border-radius: var(--border-radius-small);'">
              {{ getExecutedPromptsCount() }}
            </span>
          </button>
        </div>
      </div>

      <!-- Tab Content -->
      <div class="tab-content-modern">
        <!-- PROMPTS TAB CONTENT -->
        <div *ngIf="activeTabIndex === 0" style="padding-top: 24px;">
          <div class="flex justify-between items-center mb-6">
            <div class="flex gap-3 items-center">
              <input nz-input placeholder="Search prompts" [(ngModel)]="searchQuery" (ngModelChange)="onSearch()"
                class="mr-4"
                style="width: 250px; background-color: var(--background-white); border-color: var(--hover-blue-gray); color: var(--text-dark);" />

              <nz-select nzPlaceHolder="Filter by agent" [(ngModel)]="selectedAgent"
                (ngModelChange)="onAgentSelect($event)" style="width: 250px;" class="mr-4" nzShowSearch nzAllowClear
                [nzSuffixIcon]="selectedAgent ? 'close-circle' : 'search'" [nzFilterOption]="agentFilterOption">
                <nz-option [nzValue]="null" [nzLabel]="'All agents (' + agents.length + ')'"></nz-option>
                <nz-option *ngFor="let agent of sortedAgents" [nzValue]="agent.agentName"
                  [nzLabel]="getAgentDisplayLabel(agent)">
                </nz-option>
              </nz-select>

              <button nz-button (click)="resetFilters()" class="mr-2"
                style="background-color: var(--hover-blue-gray); border-color: var(--hover-blue-gray); color: var(--text-dark); transition: var(--transition-default);"
                onmouseover="this.style.backgroundColor='var(--secondary-purple)'; this.style.borderColor='var(--secondary-purple)'"
                onmouseout="this.style.backgroundColor='var(--hover-blue-gray)'; this.style.borderColor='var(--hover-blue-gray)'">
                <i class="ri-refresh-line mr-1"></i>
                Reset Filters
              </button>

              <button nz-button nzType="default" (click)="loadData()" [nzLoading]="loading" class="mr-2"
                style="background-color: var(--hover-blue-gray); border-color: var(--hover-blue-gray); color: var(--text-dark); transition: var(--transition-default);"
                onmouseover="this.style.backgroundColor='var(--secondary-purple)'; this.style.borderColor='var(--secondary-purple)'"
                onmouseout="this.style.backgroundColor='var(--hover-blue-gray)'; this.style.borderColor='var(--hover-blue-gray)'">
                <i class="ri-loader-line animate-spin mr-1" *ngIf="loading"></i>
                <i class="ri-refresh-line mr-1" *ngIf="!loading"></i>
                Refresh
              </button>
            </div>

            <button nz-button nzType="primary" (click)="openAddPromptDialog()"
              style="background-color: var(--primary-purple); border-color: var(--primary-purple); color: white; transition: var(--transition-default); border-radius: var(--border-radius-small);"
              onmouseover="this.style.backgroundColor='var(--secondary-purple)'; this.style.borderColor='var(--secondary-purple)'; this.style.color='white'"
              onmouseout="this.style.backgroundColor='var(--primary-purple)'; this.style.borderColor='var(--primary-purple)'; this.style.color='white'">
              <i class="ri-add-line mr-1"></i>
              Add Prompt
            </button>
          </div>

          <!-- Prompts Table -->
          <div class="overflow-x-auto">
            <table class="w-full"
              style="background-color: var(--background-white); border-radius: var(--border-radius-large); overflow: hidden; box-shadow: var(--box-shadow); border: 1px solid var(--hover-blue-gray);">
              <thead style="background-color: var(--header-bg);">
                <tr>
                  <th class="px-4 py-3 text-left text-sm font-medium"
                    style="width: 15%; color: var(--header-text); background-color: var(--header-bg); border-bottom: 1px solid var(--hover-blue-gray);">
                    Agent</th>
                  <th class="px-4 py-3 text-left text-sm font-medium"
                    style="width: 35%; color: var(--header-text); background-color: var(--header-bg); border-bottom: 1px solid var(--hover-blue-gray);">
                    Prompt</th>
                  <th class="px-4 py-3 text-left text-sm font-medium"
                    style="width: 12%; color: var(--header-text); background-color: var(--header-bg); border-bottom: 1px solid var(--hover-blue-gray);">
                    Created At</th>
                  <th class="px-4 py-3 text-left text-sm font-medium"
                    style="width: 8%; color: var(--header-text); background-color: var(--header-bg); border-bottom: 1px solid var(--hover-blue-gray);">
                    Score</th>
                  <th class="px-4 py-3 text-left text-sm font-medium"
                    style="width: 15%; color: var(--header-text); background-color: var(--header-bg); border-bottom: 1px solid var(--hover-blue-gray);">
                    Status</th>
                  <th class="px-4 py-3 text-left text-sm font-medium"
                    style="width: 15%; color: var(--header-text); background-color: var(--header-bg); border-bottom: 1px solid var(--hover-blue-gray);">
                    Actions</th>
                </tr>
              </thead>
              <tbody style="background-color: var(--background-white);">
                <tr *ngFor="let evaluation of paginatedEvaluations"
                  style="border-bottom: 1px solid var(--hover-blue-gray); transition: var(--transition-default); background-color: var(--background-white);"
                  onmouseover="this.style.backgroundColor='var(--hover-blue-gray)'"
                  onmouseout="this.style.backgroundColor='var(--background-white)'">
                  <td class="px-4 py-3">
                    <span
                      style="background-color: var(--primary-purple); color: white; padding: 4px 8px; border-radius: var(--border-radius-small); font-size: 12px; font-weight: 500;">
                      {{ evaluation.agentName }}
                    </span>
                  </td>
                  <td class="px-4 py-3">
                    <div class="text-sm" style="color: var(--text-dark);" [title]="evaluation.prompt">
                      {{ truncateText(evaluation.prompt, 80) }}
                    </div>
                  </td>
                  <td class="px-4 py-3 text-sm" style="color: var(--text-medium-gray);">
                    {{ formatDate(evaluation.createdAt) }}
                  </td>
                  <td class="px-4 py-3">
                    <span *ngIf="evaluation.score !== undefined"
                      [class]="'px-2 py-1 text-xs font-medium ' +
                                              (evaluation.score >= 8 ? 'bg-green-100 text-green-800' :
                                               evaluation.score >= 5 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800')"
                      style="border-radius: var(--border-radius-small);">
                      {{ evaluation.score }}/10
                    </span>
                    <span *ngIf="evaluation.score === undefined" class="text-sm"
                      style="color: var(--text-medium-gray);">-</span>
                  </td>
                  <td class="px-4 py-3">
                    <div class="flex flex-wrap gap-1 mb-1">
                      <span *ngIf="evaluation.output" class="px-2 py-1 text-xs"
                        style="background-color: #dcfce7; color: #166534; border-radius: var(--border-radius-small);">
                        Executed
                      </span>
                      <span *ngIf="!evaluation.output" class="px-2 py-1 text-xs"
                        style="background-color: var(--hover-blue-gray); color: var(--text-medium-gray); border-radius: var(--border-radius-small);">
                        Pending
                      </span>
                      <span *ngIf="evaluation.expectedOutput" class="px-2 py-1 text-xs"
                        style="background-color: #dbeafe; color: #1e40af; border-radius: var(--border-radius-small);">
                        Has Expected
                      </span>
                    </div>
                    <button *ngIf="evaluation.output" (click)="showPromptDetails(evaluation)"
                      class="text-xs bg-transparent border-none outline-none cursor-pointer"
                      style="color: var(--primary-purple);" onmouseover="this.style.color='var(--secondary-purple)'"
                      onmouseout="this.style.color='var(--primary-purple)'">
                      View Details
                    </button>
                  </td>
                  <td class="px-4 py-3">
                    <div class="flex items-center gap-2 ">
                      <button (click)="openEditDialog(evaluation)"
                        class="action-button w-8 h-8 rounded-md bg-[#F0F0F0] hover:bg-[#E0E0E0] transition-all duration-200 flex items-center justify-center border-none"
                       title="Edit prompt">
                        <i class="ri-edit-line text-sm"></i>
                      </button>
                      <button (click)="openDeleteConfirmDialog(evaluation)"
                        class="action-button w-8 h-8 rounded-md bg-[#F0F0F0] hover:bg-[#E0E0E0] transition-all duration-200 flex items-center justify-center border-none"

                        title="Delete evaluation">
                        <i class="ri-delete-bin-line text-sm"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div *ngIf="filteredEvaluationsGetter.length === 0" class="empty-state bg-[var(--background-white)] dark:bg-gray-700">
            <i class="ri-file-list-line"></i>
            <h3 class="dark:text-white">No prompts found</h3>
            <p>No prompts match your current filters</p>
            <button (click)="openAddPromptDialog()" class="px-3 p-2 bg-[var(--background-white)] border-none ">
              <i class="ri-add-line text-sm"></i>
               <span>Add Your First Prompt</span>
            </button>
          </div>

          <!-- Pagination -->
          <div
            class="pagination-container flex flex-col sm:flex-row justify-between items-center mt-4 mb-4 px-4 py-3 bg-[var(--background-white)] rounded-md shadow-sm border border-[var(--hover-blue-gray)]"
            *ngIf="!loading && filteredEvaluationsGetter.length > 0">
            <div class="text-sm text-[var(--text-medium-gray)] mb-4 sm:mb-0 flex items-center">
              <ng-container *ngIf="filteredEvaluationsGetter.length > 0">
                <span>Showing</span>
                <span class="font-medium text-[var(--text-dark)] mx-1">{{ ((currentPage - 1) * pageSize) + 1 }}</span>
                <span>to</span>
                <span class="font-medium text-[var(--text-dark)] mx-1">{{ ((currentPage - 1) * pageSize) +
                  paginatedEvaluations.length }}</span>
                <span>of</span>
                <span class="font-medium text-[var(--text-dark)] mx-1">{{ filteredEvaluationsGetter.length }}</span>
                <span>evaluations</span>
              </ng-container>
              <ng-container *ngIf="filteredEvaluationsGetter.length === 0">
                <span>No evaluations to display</span>
              </ng-container>
            </div>

            <div class="flex items-center">
              <div class="hidden sm:flex items-center mr-6 space-x-2">
                <span class="text-sm text-[var(--text-medium-gray)]">Rows per page:</span>
                <div class="relative w-auto">
                  <button id="pageSizeDropdownBtn" (click)="togglePageSizeDropdown()"
                    class="appearance-none h-7 rounded-md text-sm px-2 pr-7 py-0 text-center border bg-[var(--background-white)] border-gray-300 focus:outline-none focus:ring-1 cursor-pointer">
                    {{ pageSize || 'Select Page Size' }}
                  </button>
                  <div *ngIf="isPageSizeDropdownOpen" id="pageSizeDropdownMenu"
                    class="absolute mt-1 w-full bg-[var(--secondary-purple)] !border-2 !border-gray-300 rounded-md shadow-lg z-10"
                    [ngClass]="{'bottom-full mb-1 border': isDropdownAbove, 'top-full border mt-1': !isDropdownAbove}">
                    <div (click)="selectPageSize(5)"
                      class="px-2 py-1 text-sm cursor-pointer text-center border hover:bg-[var(--primary-purple)] rounded-md hover:text-white">
                      5</div>
                    <div (click)="selectPageSize(10)"
                      class="px-2 py-1 text-sm cursor-pointer text-center rounded-md hover:bg-[var(--primary-purple)] hover:text-white">
                      10</div>
                    <div (click)="selectPageSize(20)"
                      class="px-2 py-1 text-sm cursor-pointer text-center rounded-md hover:bg-[var(--primary-purple)] hover:text-white">
                      20</div>
                    <div (click)="selectPageSize(50)"
                      class="px-2 py-1 text-sm cursor-pointer text-center rounded-md hover:bg-[var(--primary-purple)] hover:text-white">
                      50</div>
                  </div>
                  <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-1 pointer-events-none">
                    <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)] text-sm"></i>
                  </div>
                </div>
              </div>

              <div class="flex items-center space-x-1" *ngIf="totalPages > 0">
                <button (click)="goToPage(1)" [disabled]="currentPage === 1 || totalPages <= 1"
                  class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
                  aria-label="First page">
                  <i class="ri-skip-back-mini-line text-sm"></i>
                </button>

                <button (click)="previousPage()" [disabled]="currentPage === 1 || totalPages <= 1"
                  class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
                  aria-label="Previous page">
                  <i class="ri-arrow-left-s-line text-sm"></i>
                </button>

                <div class="flex items-center space-x-1">
                  <button *ngIf="currentPage > 2 && totalPages > 3" (click)="goToPage(1)"
                    class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                    1
                  </button>

                  <span *ngIf="currentPage > 3 && totalPages > 4"
                    class="w-7 h-7 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>

                  <button *ngIf="currentPage > 1 && totalPages > 1" (click)="goToPage(currentPage - 1)"
                    class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                    {{ currentPage - 1 }}
                  </button>

                  <button
                    class="w-7 h-7 flex items-center justify-center rounded-md bg-[var(--primary-purple)] text-white font-medium border-none">
                    {{ currentPage }}
                  </button>

                  <button *ngIf="currentPage < totalPages && totalPages > 1" (click)="goToPage(currentPage + 1)"
                    class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                    {{ currentPage + 1 }}
                  </button>

                  <span *ngIf="currentPage < totalPages - 2 && totalPages > 4"
                    class="w-7 h-7 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>

                  <button *ngIf="currentPage < totalPages - 1 && totalPages > 3" (click)="goToPage(totalPages)"
                    class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                    {{ totalPages }}
                  </button>
                </div>

                <button (click)="nextPage()" [disabled]="currentPage === totalPages || totalPages <= 1"
                  class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
                  aria-label="Next page">
                  <i class="ri-arrow-right-s-line text-sm"></i>
                </button>

                <button (click)="goToPage(totalPages)" [disabled]="currentPage === totalPages || totalPages <= 1"
                  class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
                  aria-label="Last page">
                  <i class="ri-skip-forward-mini-line text-sm"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- EXECUTION TAB CONTENT -->
        <div *ngIf="activeTabIndex === 1" style="padding-top: 24px;">
          <div
            style="background-color: var(--background-white); border-radius: var(--border-radius-large); box-shadow: var(--box-shadow); border: 1px solid var(--hover-blue-gray); overflow: hidden;">
            <div class="p-6">
              <div class="mb-6">
                <h3 class="text-lg font-semibold mb-4" style="color: var(--text-dark);">
                  {{ selectedAgentForExecution ? 'Execute Evaluations for ' + selectedAgentForExecution :
                  'Execute Agent Evaluations' }}
                </h3>

                <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-4">
                  <select [(ngModel)]="selectedAgentForExecution" (ngModelChange)="onAgentForExecutionSelect($event)"
                    class="flex-1"
                    style="background-color: var(--background-white); border: 1px solid var(--hover-blue-gray); border-radius: var(--border-radius-small); padding: 8px 12px; color: var(--text-dark); font-size: 14px; transition: var(--transition-default);"
                    onfocus="this.style.borderColor='var(--primary-purple)'; this.style.boxShadow='0 0 0 2px rgba(var(--primary-purple-rgb), 0.2)'"
                    onblur="this.style.borderColor='var(--hover-blue-gray)'; this.style.boxShadow='none'">
                    <option [value]="null" style="color: var(--text-medium-gray);">Select agent to execute...</option>
                    <option *ngFor="let agent of sortedAgents" [value]="agent.agentName"
                      style="color: var(--text-dark);">
                      {{ getAgentExecutionDisplayLabel(agent) }}
                    </option>
                  </select>

                  <button (click)="executeEvaluations()" [disabled]="executing || !selectedAgentForExecution"
                    class="px-4 py-2 border-none cursor-pointer transition-all duration-300 flex items-center gap-2"
                    [style]="(executing || !selectedAgentForExecution) ?
                                            'background-color: var(--hover-blue-gray); color: var(--text-medium-gray); border-radius: var(--border-radius-small); opacity: 0.5; cursor: not-allowed;' :
                                            'background-color: var(--primary-purple); color: var(--background-white); border-radius: var(--border-radius-small);'"
                    onmouseover="if(!this.disabled) { this.style.backgroundColor='var(--secondary-purple)'; }"
                    onmouseout="if(!this.disabled) { this.style.backgroundColor='var(--primary-purple)'; }">
                    <i [class]="executing ? 'ri-loader-line animate-spin' : 'ri-play-line'"></i>
                    Execute All Prompts
                  </button>
                </div>

                <!-- Show prompt count for selected agent -->
                <div *ngIf="selectedAgentForExecution" class="mb-4 flex gap-2">
                  <span class="px-2 py-1 text-xs"
                    style="background-color: #dbeafe; color: #1e40af; border-radius: var(--border-radius-small);">
                    {{ getAgentPromptsCount(selectedAgentForExecution) }} prompts available
                  </span>
                  <span *ngIf="hasAgentExecutedPrompts(selectedAgentForExecution)" class="px-2 py-1 text-xs"
                    style="background-color: #dcfce7; color: #166534; border-radius: var(--border-radius-small);">
                    {{ getAgentExecutedPromptsCount(selectedAgentForExecution) }} already executed
                  </span>
                </div>

                <div class="mb-6" style="border-bottom: 1px solid var(--hover-blue-gray);"></div>

                <div *ngIf="!currentExecutionProgress && !executionLogs.length" class="text-center py-8">
                  <div style="color: var(--text-medium-gray);">Select an agent and click Execute to run all prompts
                  </div>
                </div>

                <!-- Execution Progress -->
                <div *ngIf="currentExecutionProgress" class="mb-4">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium" style="color: var(--text-dark);">Execution Progress</span>
                    <span class="text-sm" style="color: var(--text-medium-gray);">
                      {{ currentExecutionProgress.completed }} / {{ currentExecutionProgress.total }}
                    </span>
                  </div>
                  <div class="w-full h-2 rounded-full" style="background-color: var(--hover-blue-gray);">
                    <div class="h-2 rounded-full transition-all duration-300"
                      style="background-color: var(--primary-purple);" [style.width.%]="getExecutionProgressPercent()">
                    </div>
                  </div>
                </div>

                <!-- Execution Logs -->
                <div *ngIf="executionLogs.length > 0" class="h-64 overflow-auto p-4"
                  style="border: 1px solid var(--hover-blue-gray); border-radius: var(--border-radius-small); background-color: var(--background-light-gray);">
                  <div *ngFor="let log of executionLogs" class="mb-2">
                    <div class="font-semibold" style="color: var(--text-dark);">{{ log.title }}</div>
                    <div class="text-sm whitespace-pre-wrap" style="color: var(--text-medium-gray);"
                      [innerHTML]="log.content"></div>
                  </div>
                </div>

                <!-- Re-evaluate button shown after execution -->
                <div *ngIf="hasExecutedPrompts && executionLogs.length > 0 && !executing"
                  class="mt-6 flex justify-between items-center">
                  <button (click)="resetExecution()" *ngIf="executionLogs.length > 0"
                    class="px-4 py-2 border-none cursor-pointer transition-all duration-300"
                    style="background-color: var(--hover-blue-gray); color: var(--text-dark); border-radius: var(--border-radius-small);"
                    onmouseover="this.style.backgroundColor='var(--secondary-purple)'"
                    onmouseout="this.style.backgroundColor='var(--hover-blue-gray)'">
                    Clear Logs
                  </button>

                  <button (click)="reevaluateAgent()" [disabled]="reevaluating || !selectedAgentForExecution"
                    class="px-4 py-2 border-none cursor-pointer transition-all duration-300 flex items-center gap-2"
                    [style]="(reevaluating || !selectedAgentForExecution) ?
                                            'background-color: var(--hover-blue-gray); color: var(--text-medium-gray); border-radius: var(--border-radius-small); opacity: 0.5; cursor: not-allowed;' :
                                            'background-color: var(--primary-purple); color: var(--background-white); border-radius: var(--border-radius-small);'"
                    onmouseover="if(!this.disabled) { this.style.backgroundColor='var(--secondary-purple)'; }"
                    onmouseout="if(!this.disabled) { this.style.backgroundColor='var(--primary-purple)'; }">
                    <i [class]="reevaluating ? 'ri-loader-line animate-spin' : 'ri-line-chart-line'"></i>
                    Re-evaluate Agent
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Re-evaluation Results -->
          <div *ngIf="reEvaluationResult" class="mt-6"
            style="background-color: var(--background-white); border-radius: var(--border-radius-large); box-shadow: var(--box-shadow); border: 1px solid var(--hover-blue-gray); overflow: hidden;">
            <div class="p-6">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold" style="color: var(--text-dark);">Agent Evaluation Results</h3>
                <button (click)="resetReEvaluation()" class="p-1 border-none outline-none cursor-pointer"
                  style="background-color: var(--hover-blue-gray); color: var(--text-dark); border-radius: var(--border-radius-small); transition: var(--transition-default);"
                  onmouseover="this.style.backgroundColor='var(--secondary-purple)'; this.style.color='var(--primary-purple)'"
                  onmouseout="this.style.backgroundColor='var(--hover-blue-gray)'; this.style.color='var(--text-dark)'">
                  <i class="ri-close-line"></i>
                </button>
              </div>

              <div class="mb-4 flex items-center">
                <div class="font-semibold mr-2" style="color: var(--text-dark);">Agent:</div>
                <p class="m-0 text-lg" style="color: var(--text-dark);">{{ selectedAgentForExecution }}</p>
              </div>

              <div class="mb-4">
                <div class="font-semibold mb-2" style="color: var(--text-dark);">Overall Score</div>
                <div class="flex items-center">
                  <span
                    [class]="'text-xl py-2 px-4 font-medium ' +
                                          (reEvaluationResult.score >= 8 ? 'bg-green-100 text-green-800' :
                                           reEvaluationResult.score >= 5 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800')"
                    style="border-radius: var(--border-radius-small);">
                    {{ reEvaluationResult.score }}/10
                  </span>
                  <span class="ml-4" style="color: var(--text-medium-gray);">
                    {{ reEvaluationResult.score >= 8 ? '(Excellent)' :
                    reEvaluationResult.score >= 5 ? '(Good)' : '(Needs Improvement)' }}
                  </span>
                </div>
              </div>

              <div class="mb-4">
                <div class="font-semibold mb-2" style="color: var(--text-dark);">Evaluation</div>
                <div class="p-4 whitespace-pre-wrap"
                  style="border: 1px solid var(--hover-blue-gray); background-color: var(--background-light-gray); border-radius: var(--border-radius-small); color: var(--text-dark);">
                  {{ reEvaluationResult.evaluation }}
                </div>
              </div>

              <div class="mb-4">
                <div class="font-semibold mb-2" style="color: var(--text-dark);">Improvement Suggestions</div>
                <div class="p-4"
                  style="border: 1px solid var(--hover-blue-gray); background-color: var(--background-light-gray); border-radius: var(--border-radius-small);">
                  <div
                    *ngFor="let suggestion of processImprovementSuggestions(reEvaluationResult.improvementSuggestions)"
                    class="mb-2 flex items-start gap-2" style="color: var(--text-dark);">
                    <i class="ri-arrow-right-line mt-0.5 flex-shrink-0" style="color: var(--primary-purple);"></i>
                    <span>{{ suggestion }}</span>
                  </div>
                </div>
              </div>

              <div class="mb-4" *ngIf="reEvaluationResult.updatedPrompt">
                <div class="font-semibold mb-2" style="color: var(--text-dark);">Updated Prompt</div>
                <div class="p-4 whitespace-pre-wrap"
                  style="border: 1px solid var(--hover-blue-gray); background-color: var(--background-light-gray); border-radius: var(--border-radius-small); color: var(--text-dark);">
                  {{ reEvaluationResult.updatedPrompt }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Prompt Dialog -->
  <nz-modal [(nzVisible)]="showDetailsModal" [nzTitle]="isBulkMode ? 'Add Multiple Prompts' : 'Add New Prompt'"
    nzOkText="Add" nzCancelText="Cancel" (nzOnOk)="isBulkMode ? submitBulkEvaluations() : submitEvaluation()"
    (nzOnCancel)="showDetailsModal = false" [nzOkLoading]="submitting" [nzWidth]="700"
    [nzBodyStyle]="{ 'padding': '0', 'height': '80vh', 'overflow': 'auto' }" [nzFooter]="null">
    <ng-container *nzModalContent>
      <div
        class="rounded-[var(--border-radius-large)] bg-[var(--background-white)] shadow-[var(--box-shadow)] h-full flex flex-col">
        <form [formGroup]="evaluationForm" (ngSubmit)="isBulkMode ? submitBulkEvaluations() : submitEvaluation()"
          class="flex-1 flex flex-col p-6" *ngIf="!isBulkMode">
          <div class="flex-1 space-y-6 overflow-y-auto ">
            <!-- Agent Selection -->
            <div class="mb-4">
              <label class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
                <i class="ri-robot-line mr-2 text-[var(--primary-purple)]"></i>Agent
              </label>
              <div class="relative">
                <button id="agentDropdownBtn" type="button" (click)="toggleAgentDropdown()"
                  class="appearance-none w-full p-3 pr-10 rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)] bg-[var(--background-white)] text-[var(--text-dark)] text-left focus:ring-2 focus:ring-[var(--primary-purple)] transition-all duration-200 cursor-pointer">
                  {{ selectedAgentLabel || 'Select an agent' }}
                </button>
                <div *ngIf="isAgentDropdownOpen" id="agentDropdownMenu"
                  class="absolute mt-1 w-full bg-[var(--background-white)] border border-gray-300 rounded-md shadow-lg z-10"
                  [ngClass]="{'bottom-full mb-1 border': isAgentDropdownAbove, 'top-full border mt-1': !isAgentDropdownAbove}">
                  <div (click)="selectAgent('')"
                    class="px-3 py-1 text-sm cursor-pointer hover:bg-[var(--primary-purple)] hover:text-white border !border-gray-400 ">Select an
                    agent</div>
                  <div *ngFor="let agent of sortedAgents" (click)="selectAgent(agent.agentName)"
                    class="px-3 py-1 text-sm cursor-pointer hover:bg-[var(--primary-purple)] hover:text-white border !border-gray-400">{{
                    agent.agentName }}</div>
                </div>
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)]"></i>
                </div>
              </div>
              <div
                *ngIf="evaluationForm.get('agentName')?.errors?.['required'] && evaluationForm.get('agentName')?.touched"
                class="text-red-500 text-xs mt-1 flex items-center">
                <i class="ri-error-warning-line mr-1"></i>Please select an agent
              </div>
            </div>

            <!-- Prompt Input -->
            <div class="mb-4">
              <label class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
                <i class="ri-file-text-line mr-2 text-[var(--primary-purple)]"></i>Prompt
              </label>
              <textarea formControlName="prompt" placeholder="Enter the prompt for evaluation"
                class="w-full p-3 rounded-md border border-[var(--hover-blue-gray)] focus:border-none focus:outline-[var(--primary-purple)] bg-[var(--background-light-gray)] outline-none transition-[var(--transition-default)]">
                            </textarea>
              <div *ngIf="evaluationForm.get('prompt')?.errors?.['required'] && evaluationForm.get('prompt')?.touched"
                class="text-red-500 text-xs mt-1 flex items-center">
                <i class="ri-error-warning-line mr-1"></i>Please enter a prompt
              </div>
            </div>

            <!-- Expected Output -->
            <div class="mb-4">
              <label class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
                <i class="ri-target-line mr-2 text-[var(--primary-purple)]"></i>Expected Output
              </label>
              <textarea formControlName="expectedOutput"
                placeholder="Enter the expected output for evaluation (optional)"
                class="w-full p-3 rounded-md border border-[var(--hover-blue-gray)] focus:border-none focus:outline-[var(--primary-purple)] bg-[var(--background-light-gray)] outline-none transition-[var(--transition-default)]">
                            </textarea>
            </div>

            <div class="mb-4 p-3 bg-blue-50 dark:bg-gray-800 border border-blue-200 rounded-md">
              <p class="text-sm text-blue-700 dark:text-gray-300">
                <i class="ri-information-line mr-1"></i>
                Your prompt will be sent to the selected agent for execution. If you provide an expected output, the
                system will automatically score the response.
              </p>
            </div>
          </div>

          <!-- Buttons -->
          <div class="flex justify-end space-x-4 pt-4 border-t border-[var(--hover-blue-gray)] mt-auto">
            <button type="button" (click)="showDetailsModal = false"
              class="bg-[var(--hover-blue-gray)] text-[var(--text-dark)] px-4 py-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-all duration-300 outline-none border-none cursor-pointer hover:shadow-md flex items-center gap-2">
              <i class="ri-close-line"></i>
              Cancel
            </button>
            <button type="submit" [disabled]="!evaluationForm.valid || submitting"
              class="bg-[var(--primary-purple)] text-white px-4 py-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-all duration-300 outline-none border-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 hover:shadow-md">
              <i *ngIf="submitting" class="ri-loader-line animate-spin"></i>
              <i *ngIf="!submitting" class="ri-save-line"></i>
              {{ submitting ? 'Adding...' : 'Add' }}
            </button>
          </div>
        </form>
      </div>
    </ng-container>
  </nz-modal>

  <!-- Edit Evaluation Modal -->
  <nz-modal [(nzVisible)]="showEditModal" nzTitle="Edit Evaluation" nzOkText="Update" nzCancelText="Cancel"
    (nzOnOk)="submitEditEvaluation()" (nzOnCancel)="closeEditModal()" [nzOkLoading]="submitting" [nzWidth]="700"
    [nzBodyStyle]="{ 'max-height': '80vh', 'overflow-y': 'auto', 'padding': '0' }" [nzFooter]="null">
    <ng-container *nzModalContent>
      <div class="rounded-[var(--border-radius-large)] bg-[var(--background-white)] shadow-[var(--box-shadow)]">
        <form [formGroup]="editForm" (ngSubmit)="submitEditEvaluation()" class="space-y-6 p-6">
          <!-- Prompt Input -->
          <div class="mb-4">
            <label class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
              <i class="ri-file-text-line mr-2 text-[var(--primary-purple)]"></i>Prompt
            </label>
            <textarea formControlName="prompt" placeholder="Enter the prompt for evaluation"
              class="w-full p-3 rounded-md border border-[var(--hover-blue-gray)] focus:border-none focus:outline-[var(--primary-purple)] bg-[var(--background-light-gray)] outline-none transition-[var(--transition-default)]">
                        </textarea>
            <div *ngIf="editForm.get('prompt')?.errors?.['required'] && editForm.get('prompt')?.touched"
              class="text-red-500 text-xs mt-1 flex items-center">
              <i class="ri-error-warning-line mr-1"></i>Please enter a prompt
            </div>
          </div>

          <!-- Expected Output -->
          <div class="mb-4">
            <label class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
              <i class="ri-target-line mr-2 text-[var(--primary-purple)]"></i>Expected Output
            </label>
            <textarea formControlName="expectedOutput" placeholder="Enter the expected output for evaluation (optional)"
              class="w-full p-3 rounded-md border border-[var(--hover-blue-gray)] focus:border-none focus:outline-[var(--primary-purple)] bg-[var(--background-light-gray)] outline-none transition-[var(--transition-default)]">
                        </textarea>
          </div>

          <div class="mb-4 p-3 bg-blue-50 border dark:bg-gray-800 border-blue-200 rounded-md">
            <p class="text-sm text-blue-700 dark:text-gray-300">
              <i class="ri-information-line mr-1"></i>
              Note: Editing will clear any previous execution results and scores.
            </p>
          </div>

          <!-- Buttons -->
          <div class="flex justify-end space-x-4 pt-4 border-t border-[var(--hover-blue-gray)]">
            <button type="button" (click)="closeEditModal()"
              class="bg-[var(--hover-blue-gray)] text-[var(--text-dark)] px-4 py-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-all duration-300 outline-none border-none cursor-pointer hover:shadow-md flex items-center gap-2">
              <i class="ri-close-line"></i>
              Cancel
            </button>
            <button type="submit" [disabled]="!editForm.valid || submitting"
              class="bg-[var(--primary-purple)] text-white px-4 py-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-all duration-300 outline-none border-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 hover:shadow-md">
              <i *ngIf="submitting" class="ri-loader-line animate-spin"></i>
              <i *ngIf="!submitting" class="ri-save-line"></i>
              {{ submitting ? 'Updating...' : 'Update' }}
            </button>
          </div>
        </form>
      </div>
    </ng-container>
  </nz-modal>

  <!-- Delete Confirmation Modal -->
  <nz-modal [(nzVisible)]="showDeleteConfirmModal" nzTitle="Confirm Delete" nzOkText="Delete" nzCancelText="Cancel"
    (nzOnOk)="confirmDeleteEvaluation()" (nzOnCancel)="closeDeleteConfirmModal()" [nzOkLoading]="submitting" nzOkDanger
    [nzWidth]="480">
    <ng-container *nzModalContent>
      <div *ngIf="deletingEvaluation" class="delete-confirmation-content" style="padding: 8px 0; border-radius: 15px;">
        <!-- Header Icon and Message -->
        <div class="text-center mb-6">
          <div class="mb-4">
            <div class="inline-flex items-center justify-center w-10 h-10 rounded-full"
              style="background-color: rgba(254, 242, 242, 0.8); border: 3px solid rgba(254, 202, 202, 0.6);">
              <i class="ri-delete-bin-line text-2xl" style="color: #dc2626;"></i>
            </div>
            <h3 class="text-lg font-semibold mb-2" style="color: var(--text-dark);">Delete Evaluation</h3>
          </div>
          <p class="text-sm" style="color: var(--text-medium-gray); margin: 0;">
            This action will permanently remove this evaluation from the system.
          </p>
        </div>

        <!-- Evaluation Details Card -->
        <div class="mb-6 p-4 rounded-lg"
          style="background-color: var(--background-light-gray); border: 1px solid var(--hover-blue-gray); border-radius: var(--border-radius-small);">
          <div class="flex items-start gap-3">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 rounded-full flex items-center justify-center"
                style="background-color: var(--primary-purple); color: white;">
                <i class="ri-robot-line"></i>
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 mb-2">
                <span class="text-sm font-medium" style="color: var(--text-dark);">Agent:</span>
                <span class="px-2 py-1 text-xs font-medium rounded"
                  style="background-color: var(--primary-purple); color: white;">
                  {{ deletingEvaluation.agentName }}
                </span>
              </div>
              <div class="mb-1">
                <span class="text-sm font-medium" style="color: var(--text-dark);">Prompt:</span>
              </div>
              <div class="text-sm p-2 rounded"
                style="background-color: var(--background-white); border: 1px solid var(--hover-blue-gray); color: var(--text-medium-gray); line-height: 1.4;">
                {{ truncateText(deletingEvaluation.prompt, 120) }}
              </div>
            </div>
          </div>
        </div>

        <!-- Warning Section -->
        <div class="p-4 rounded-lg flex items-start gap-3 dark:bg-gray-800 bg-gray-200">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 rounded-full flex items-center justify-center" style="background-color: #fff">
              <i class="ri-error-warning-line text-sm " style="color: #dc2626;"></i>
            </div>
          </div>
          <div class="flex-1">
            <h4 class="text-sm font-semibold mb-1 dark:text-gray-200 text-gray-800 ">Warning</h4>
            <p class="text-sm  dark:text-gray-200 text-gray-800 " style=" margin: 0; line-height: 1.4;">
              This action cannot be undone. All related execution results and scores will also be permanently deleted.
            </p>
          </div>
        </div>
      </div>
    </ng-container>
  </nz-modal>
</div>
