{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-i18n.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, makeEnvironmentProviders, Injectable, Optional, Inject, Pipe, NgModule, inject } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { formatDate } from '@angular/common';\nimport { getISOWeek, format, parse } from 'date-fns';\nimport { ɵNgTimeParser as _NgTimeParser } from 'ng-zorro-antd/core/time';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar en_US = {\n  locale: 'en',\n  Pagination: {\n    items_per_page: '/ page',\n    jump_to: 'Go to',\n    jump_to_confirm: 'confirm',\n    page: 'Page',\n    prev_page: 'Previous Page',\n    next_page: 'Next Page',\n    prev_5: 'Previous 5 Pages',\n    next_5: 'Next 5 Pages',\n    prev_3: 'Previous 3 Pages',\n    next_3: 'Next 3 Pages',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Select date',\n      yearPlaceholder: 'Select year',\n      quarterPlaceholder: 'Select quarter',\n      monthPlaceholder: 'Select month',\n      weekPlaceholder: 'Select week',\n      rangePlaceholder: ['Start date', 'End date'],\n      rangeYearPlaceholder: ['Start year', 'End year'],\n      rangeMonthPlaceholder: ['Start month', 'End month'],\n      rangeWeekPlaceholder: ['Start week', 'End week'],\n      locale: 'en_US',\n      today: 'Today',\n      now: 'Now',\n      backToToday: 'Back to today',\n      ok: 'Ok',\n      clear: 'Clear',\n      month: 'Month',\n      year: 'Year',\n      timeSelect: 'select time',\n      dateSelect: 'select date',\n      weekSelect: 'Choose a week',\n      monthSelect: 'Choose a month',\n      yearSelect: 'Choose a year',\n      decadeSelect: 'Choose a decade',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Previous month (PageUp)',\n      nextMonth: 'Next month (PageDown)',\n      previousYear: 'Last year (Control + left)',\n      nextYear: 'Next year (Control + right)',\n      previousDecade: 'Last decade',\n      nextDecade: 'Next decade',\n      previousCentury: 'Last century',\n      nextCentury: 'Next century'\n    },\n    timePickerLocale: {\n      placeholder: 'Select time',\n      rangePlaceholder: ['Start time', 'End time']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Select time',\n    rangePlaceholder: ['Start time', 'End time']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Select date',\n      yearPlaceholder: 'Select year',\n      quarterPlaceholder: 'Select quarter',\n      monthPlaceholder: 'Select month',\n      weekPlaceholder: 'Select week',\n      rangePlaceholder: ['Start date', 'End date'],\n      rangeYearPlaceholder: ['Start year', 'End year'],\n      rangeMonthPlaceholder: ['Start month', 'End month'],\n      rangeWeekPlaceholder: ['Start week', 'End week'],\n      locale: 'en_US',\n      today: 'Today',\n      now: 'Now',\n      backToToday: 'Back to today',\n      ok: 'Ok',\n      clear: 'Clear',\n      month: 'Month',\n      year: 'Year',\n      timeSelect: 'select time',\n      dateSelect: 'select date',\n      weekSelect: 'Choose a week',\n      monthSelect: 'Choose a month',\n      yearSelect: 'Choose a year',\n      decadeSelect: 'Choose a decade',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Previous month (PageUp)',\n      nextMonth: 'Next month (PageDown)',\n      previousYear: 'Last year (Control + left)',\n      nextYear: 'Next year (Control + right)',\n      previousDecade: 'Last decade',\n      nextDecade: 'Next decade',\n      previousCentury: 'Last century',\n      nextCentury: 'Next century'\n    },\n    timePickerLocale: {\n      placeholder: 'Select time',\n      rangePlaceholder: ['Start time', 'End time']\n    }\n  },\n  global: {\n    placeholder: 'Please select'\n  },\n  Table: {\n    filterTitle: 'Filter menu',\n    filterConfirm: 'OK',\n    filterReset: 'Reset',\n    filterEmptyText: 'No filters',\n    emptyText: 'No data',\n    selectAll: 'Select current page',\n    selectInvert: 'Invert current page',\n    selectionAll: 'Select all data',\n    sortTitle: 'Sort',\n    expand: 'Expand row',\n    collapse: 'Collapse row',\n    triggerDesc: 'Click to sort descending',\n    triggerAsc: 'Click to sort ascending',\n    cancelSort: 'Click to cancel sorting',\n    filterCheckall: 'Select all items',\n    filterSearchPlaceholder: 'Search in filters',\n    selectNone: 'Clear all data'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Cancel',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Cancel'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Search here',\n    itemUnit: 'item',\n    itemsUnit: 'items',\n    remove: 'Remove',\n    selectCurrent: 'Select current page',\n    removeCurrent: 'Remove current page',\n    selectAll: 'Select all data',\n    removeAll: 'Remove all data',\n    selectInvert: 'Invert current page'\n  },\n  Upload: {\n    uploading: 'Uploading...',\n    removeFile: 'Remove file',\n    uploadError: 'Upload error',\n    previewFile: 'Preview file',\n    downloadFile: 'Download file'\n  },\n  Empty: {\n    description: 'No Data'\n  },\n  Icon: {\n    icon: 'icon'\n  },\n  Text: {\n    edit: 'Edit',\n    copy: 'Copy',\n    copied: 'Copied',\n    expand: 'Expand'\n  },\n  PageHeader: {\n    back: 'Back'\n  },\n  Image: {\n    preview: 'Preview'\n  },\n  CronExpression: {\n    cronError: 'Invalid cron expression',\n    second: 'second',\n    minute: 'minute',\n    hour: 'hour',\n    day: 'day',\n    month: 'month',\n    week: 'week'\n  },\n  QRCode: {\n    expired: 'QR code expired',\n    refresh: 'Refresh'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar zh_CN = {\n  locale: 'zh-cn',\n  Pagination: {\n    items_per_page: '条/页',\n    jump_to: '跳至',\n    jump_to_confirm: '确定',\n    page: '页',\n    prev_page: '上一页',\n    next_page: '下一页',\n    prev_5: '向前 5 页',\n    next_5: '向后 5 页',\n    prev_3: '向前 3 页',\n    next_3: '向后 3 页',\n    page_size: '页码'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: '请选择日期',\n      yearPlaceholder: '请选择年份',\n      quarterPlaceholder: '请选择季度',\n      monthPlaceholder: '请选择月份',\n      weekPlaceholder: '请选择周',\n      rangePlaceholder: ['开始日期', '结束日期'],\n      rangeYearPlaceholder: ['开始年份', '结束年份'],\n      rangeMonthPlaceholder: ['开始月份', '结束月份'],\n      rangeWeekPlaceholder: ['开始周', '结束周'],\n      locale: 'zh_CN',\n      today: '今天',\n      now: '此刻',\n      backToToday: '返回今天',\n      ok: '确定',\n      timeSelect: '选择时间',\n      dateSelect: '选择日期',\n      weekSelect: '选择周',\n      clear: '清除',\n      month: '月',\n      year: '年',\n      previousMonth: '上个月 (翻页上键)',\n      nextMonth: '下个月 (翻页下键)',\n      monthSelect: '选择月份',\n      yearSelect: '选择年份',\n      decadeSelect: '选择年代',\n      yearFormat: 'YYYY年',\n      dayFormat: 'D日',\n      dateFormat: 'YYYY年M月D日',\n      dateTimeFormat: 'YYYY年M月D日 HH时mm分ss秒',\n      previousYear: '上一年 (Control键加左方向键)',\n      nextYear: '下一年 (Control键加右方向键)',\n      previousDecade: '上一年代',\n      nextDecade: '下一年代',\n      previousCentury: '上一世纪',\n      nextCentury: '下一世纪'\n    },\n    timePickerLocale: {\n      placeholder: '请选择时间',\n      rangePlaceholder: ['开始时间', '结束时间']\n    }\n  },\n  TimePicker: {\n    placeholder: '请选择时间',\n    rangePlaceholder: ['开始时间', '结束时间']\n  },\n  Calendar: {\n    lang: {\n      placeholder: '请选择日期',\n      yearPlaceholder: '请选择年份',\n      quarterPlaceholder: '请选择季度',\n      monthPlaceholder: '请选择月份',\n      weekPlaceholder: '请选择周',\n      rangePlaceholder: ['开始日期', '结束日期'],\n      rangeYearPlaceholder: ['开始年份', '结束年份'],\n      rangeMonthPlaceholder: ['开始月份', '结束月份'],\n      rangeWeekPlaceholder: ['开始周', '结束周'],\n      locale: 'zh_CN',\n      today: '今天',\n      now: '此刻',\n      backToToday: '返回今天',\n      ok: '确定',\n      timeSelect: '选择时间',\n      dateSelect: '选择日期',\n      weekSelect: '选择周',\n      clear: '清除',\n      month: '月',\n      year: '年',\n      previousMonth: '上个月 (翻页上键)',\n      nextMonth: '下个月 (翻页下键)',\n      monthSelect: '选择月份',\n      yearSelect: '选择年份',\n      decadeSelect: '选择年代',\n      yearFormat: 'YYYY年',\n      dayFormat: 'D日',\n      dateFormat: 'YYYY年M月D日',\n      dateTimeFormat: 'YYYY年M月D日 HH时mm分ss秒',\n      previousYear: '上一年 (Control键加左方向键)',\n      nextYear: '下一年 (Control键加右方向键)',\n      previousDecade: '上一年代',\n      nextDecade: '下一年代',\n      previousCentury: '上一世纪',\n      nextCentury: '下一世纪'\n    },\n    timePickerLocale: {\n      placeholder: '请选择时间',\n      rangePlaceholder: ['开始时间', '结束时间']\n    }\n  },\n  global: {\n    placeholder: '请选择'\n  },\n  Table: {\n    filterTitle: '筛选',\n    filterConfirm: '确定',\n    filterReset: '重置',\n    filterEmptyText: '无筛选项',\n    selectAll: '全选当页',\n    selectInvert: '反选当页',\n    selectionAll: '全选所有',\n    sortTitle: '排序',\n    expand: '展开行',\n    collapse: '关闭行',\n    triggerDesc: '点击降序',\n    triggerAsc: '点击升序',\n    cancelSort: '取消排序',\n    filterCheckall: '全选',\n    filterSearchPlaceholder: '在筛选项中搜索',\n    selectNone: '清空所有'\n  },\n  Modal: {\n    okText: '确定',\n    cancelText: '取消',\n    justOkText: '知道了'\n  },\n  Popconfirm: {\n    cancelText: '取消',\n    okText: '确定'\n  },\n  Transfer: {\n    searchPlaceholder: '请输入搜索内容',\n    itemUnit: '项',\n    itemsUnit: '项',\n    remove: '删除',\n    selectCurrent: '全选当页',\n    removeCurrent: '删除当页',\n    selectAll: '全选所有',\n    removeAll: '删除全部',\n    selectInvert: '反选当页'\n  },\n  Upload: {\n    uploading: '文件上传中',\n    removeFile: '删除文件',\n    uploadError: '上传错误',\n    previewFile: '预览文件',\n    downloadFile: '下载文件'\n  },\n  Empty: {\n    description: '暂无数据'\n  },\n  Icon: {\n    icon: '图标'\n  },\n  Text: {\n    edit: '编辑',\n    copy: '复制',\n    copied: '复制成功',\n    expand: '展开'\n  },\n  PageHeader: {\n    back: '返回'\n  },\n  Image: {\n    preview: '预览'\n  },\n  CronExpression: {\n    cronError: 'cron 表达式不合法',\n    second: '秒',\n    minute: '分钟',\n    hour: '小时',\n    day: '日',\n    month: '月',\n    week: '周'\n  },\n  QRCode: {\n    expired: '二维码过期',\n    refresh: '点击刷新'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst NZ_I18N = new InjectionToken('nz-i18n');\nfunction provideNzI18n(config) {\n  return makeEnvironmentProviders([{\n    provide: NZ_I18N,\n    useValue: config\n  }]);\n}\n/** Locale for date operations, should import from date-fns, see example: https://github.com/date-fns/date-fns/blob/v1.30.1/src/locale/zh_cn/index.js */\nconst NZ_DATE_LOCALE = new InjectionToken('nz-date-locale');\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzI18nService {\n  get localeChange() {\n    return this._change.asObservable();\n  }\n  constructor(locale, dateLocale) {\n    this._change = new BehaviorSubject(this._locale);\n    this.setLocale(locale || zh_CN);\n    this.setDateLocale(dateLocale || null);\n  }\n  // [NOTE] Performance issue: this method may called by every change detections\n  // TODO: cache more deeply paths for performance\n  translate(path, data) {\n    // this._logger.debug(`[NzI18nService] Translating(${this._locale.locale}): ${path}`);\n    let content = this._getObjectPath(this._locale, path);\n    if (typeof content === 'string') {\n      if (data) {\n        Object.keys(data).forEach(key => content = content.replace(new RegExp(`%${key}%`, 'g'), data[key]));\n      }\n      return content;\n    }\n    return path;\n  }\n  /**\n   * Set/Change current locale globally throughout the WHOLE application\n   * NOTE: If called at runtime, rendered interface may not change along with the locale change,\n   * because this do not trigger another render schedule.\n   *\n   * @param locale The translating letters\n   */\n  setLocale(locale) {\n    if (this._locale && this._locale.locale === locale.locale) {\n      return;\n    }\n    this._locale = locale;\n    this._change.next(locale);\n  }\n  getLocale() {\n    return this._locale;\n  }\n  getLocaleId() {\n    return this._locale ? this._locale.locale : '';\n  }\n  setDateLocale(dateLocale) {\n    this.dateLocale = dateLocale;\n  }\n  getDateLocale() {\n    return this.dateLocale;\n  }\n  /**\n   * Get locale data\n   *\n   * @param path dot paths for finding exist value from locale data, eg. \"a.b.c\"\n   * @param defaultValue default value if the result is not \"truthy\"\n   */\n  getLocaleData(path, defaultValue) {\n    const result = path ? this._getObjectPath(this._locale, path) : this._locale;\n    if (!result && !defaultValue) {\n      warn(`Missing translations for \"${path}\" in language \"${this._locale.locale}\".\nYou can use \"NzI18nService.setLocale\" as a temporary fix.\nWelcome to submit a pull request to help us optimize the translations!\nhttps://github.com/NG-ZORRO/ng-zorro-antd/blob/master/CONTRIBUTING.md`);\n    }\n    return result || defaultValue || this._getObjectPath(en_US, path) || {};\n  }\n  _getObjectPath(obj, path) {\n    let res = obj;\n    const paths = path.split('.');\n    const depth = paths.length;\n    let index = 0;\n    while (res && index < depth) {\n      res = res[paths[index++]];\n    }\n    return index === depth ? res : null;\n  }\n  static {\n    this.ɵfac = function NzI18nService_Factory(t) {\n      return new (t || NzI18nService)(i0.ɵɵinject(NZ_I18N, 8), i0.ɵɵinject(NZ_DATE_LOCALE, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzI18nService,\n      factory: NzI18nService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzI18nService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [NZ_I18N]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [NZ_DATE_LOCALE]\n    }]\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzI18nPipe {\n  constructor(_locale) {\n    this._locale = _locale;\n  }\n  transform(path, keyValue) {\n    return this._locale.translate(path, keyValue);\n  }\n  static {\n    this.ɵfac = function NzI18nPipe_Factory(t) {\n      return new (t || NzI18nPipe)(i0.ɵɵdirectiveInject(NzI18nService, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzI18n\",\n      type: NzI18nPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzI18nPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzI18n',\n      standalone: true\n    }]\n  }], () => [{\n    type: NzI18nService\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzI18nModule {\n  static {\n    this.ɵfac = function NzI18nModule_Factory(t) {\n      return new (t || NzI18nModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzI18nModule,\n      imports: [NzI18nPipe],\n      exports: [NzI18nPipe]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzI18nModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzI18nPipe],\n      exports: [NzI18nPipe]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst NZ_DATE_CONFIG = new InjectionToken('date-config');\nconst NZ_DATE_CONFIG_DEFAULT = {\n  firstDayOfWeek: undefined\n};\nfunction mergeDateConfig(config) {\n  return {\n    ...NZ_DATE_CONFIG_DEFAULT,\n    ...config\n  };\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction DATE_HELPER_SERVICE_FACTORY() {\n  const i18n = inject(NzI18nService);\n  const config = inject(NZ_DATE_CONFIG, {\n    optional: true\n  });\n  return i18n.getDateLocale() ? new DateHelperByDateFns(i18n, config) : new DateHelperByDatePipe(i18n, config);\n}\n/**\n * Abstract DateHelperService(Token via Class)\n * Compatibility: compact for original usage by default which using DatePipe\n */\nclass DateHelperService {\n  constructor(i18n, config) {\n    this.i18n = i18n;\n    this.config = mergeDateConfig(config);\n  }\n  static {\n    this.ɵfac = function DateHelperService_Factory(t) {\n      return new (t || DateHelperService)(i0.ɵɵinject(NzI18nService), i0.ɵɵinject(NZ_DATE_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DateHelperService,\n      factory: () => DATE_HELPER_SERVICE_FACTORY(),\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DateHelperService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: DATE_HELPER_SERVICE_FACTORY\n    }]\n  }], () => [{\n    type: NzI18nService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [NZ_DATE_CONFIG]\n    }]\n  }], null);\n})();\n/**\n * DateHelper that handles date formats with date-fns\n */\nclass DateHelperByDateFns extends DateHelperService {\n  getISOWeek(date) {\n    return getISOWeek(date);\n  }\n  // Use date-fns's \"weekStartsOn\" to support different locale when \"config.firstDayOfWeek\" is null\n  // https://github.com/date-fns/date-fns/blob/v2.0.0-alpha.27/src/locale/en-US/index.js#L23\n  getFirstDayOfWeek() {\n    let defaultWeekStartsOn;\n    try {\n      defaultWeekStartsOn = this.i18n.getDateLocale().options.weekStartsOn;\n    } catch (e) {\n      defaultWeekStartsOn = 1;\n    }\n    return this.config.firstDayOfWeek == null ? defaultWeekStartsOn : this.config.firstDayOfWeek;\n  }\n  /**\n   * Format a date\n   *\n   * @see https://date-fns.org/docs/format#description\n   * @param date Date\n   * @param formatStr format string\n   */\n  format(date, formatStr) {\n    return date ? format(date, formatStr, {\n      locale: this.i18n.getDateLocale()\n    }) : '';\n  }\n  parseDate(text, formatStr) {\n    return parse(text, formatStr, new Date(), {\n      locale: this.i18n.getDateLocale(),\n      weekStartsOn: this.getFirstDayOfWeek()\n    });\n  }\n  parseTime(text, formatStr) {\n    return this.parseDate(text, formatStr);\n  }\n}\n/**\n * DateHelper that handles date formats with angular's date-pipe\n *\n * @see https://github.com/NG-ZORRO/ng-zorro-antd/issues/2406 - DatePipe may cause non-standard week bug, see:\n *\n */\nclass DateHelperByDatePipe extends DateHelperService {\n  getISOWeek(date) {\n    return +this.format(date, 'w');\n  }\n  getFirstDayOfWeek() {\n    if (this.config.firstDayOfWeek === undefined) {\n      const locale = this.i18n.getLocaleId();\n      return locale && ['zh-cn', 'zh-tw'].indexOf(locale.toLowerCase()) > -1 ? 1 : 0;\n    }\n    return this.config.firstDayOfWeek;\n  }\n  format(date, formatStr) {\n    return date ? formatDate(date, formatStr, this.i18n.getLocaleId()) : '';\n  }\n  parseDate(text) {\n    return new Date(text);\n  }\n  parseTime(text, formatStr) {\n    const parser = new _NgTimeParser(formatStr, this.i18n.getLocaleId());\n    return parser.toDate(text);\n  }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ar_EG = {\n  locale: 'ar',\n  Pagination: {\n    items_per_page: '/ الصفحة',\n    jump_to: 'الذهاب إلى',\n    jump_to_confirm: 'تأكيد',\n    page: 'الصفحة',\n    prev_page: 'الصفحة السابقة',\n    next_page: 'الصفحة التالية',\n    prev_5: 'خمس صفحات سابقة',\n    next_5: 'خمس صفحات تالية',\n    prev_3: 'ثلاث صفحات سابقة',\n    next_3: 'ثلاث صفحات تالية',\n    page_size: 'مقاس الصفحه'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'اختيار التاريخ',\n      yearPlaceholder: 'اختيار السنة',\n      quarterPlaceholder: 'اختيار الربع',\n      monthPlaceholder: 'اختيار الشهر',\n      weekPlaceholder: 'اختيار الأسبوع',\n      rangePlaceholder: ['البداية', 'النهاية'],\n      rangeYearPlaceholder: ['سنة البداية', 'سنة النهاية'],\n      rangeMonthPlaceholder: ['شهر البداية', 'شهر النهاية'],\n      rangeWeekPlaceholder: ['أسبوع البداية', 'أسبوع النهاية'],\n      locale: 'ar_EG',\n      today: 'اليوم',\n      now: 'الأن',\n      backToToday: 'العودة إلى اليوم',\n      ok: 'تأكيد',\n      clear: 'مسح',\n      month: 'الشهر',\n      year: 'السنة',\n      timeSelect: 'اختيار الوقت',\n      dateSelect: 'اختيار التاريخ',\n      weekSelect: 'اختيار الأسبوع',\n      monthSelect: 'اختيار الشهر',\n      yearSelect: 'اختيار السنة',\n      decadeSelect: 'اختيار العقد',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'الشهر السابق (PageUp)',\n      nextMonth: 'الشهر التالى(PageDown)',\n      previousYear: 'العام السابق (Control + left)',\n      nextYear: 'العام التالى (Control + right)',\n      previousDecade: 'العقد السابق',\n      nextDecade: 'العقد التالى',\n      previousCentury: 'القرن السابق',\n      nextCentury: 'القرن التالى'\n    },\n    timePickerLocale: {\n      placeholder: 'اختيار الوقت'\n    },\n    dateFormat: 'DD-MM-YYYY',\n    monthFormat: 'MM-YYYY',\n    dateTimeFormat: 'DD-MM-YYYY HH:mm:ss',\n    weekFormat: 'wo-YYYY'\n  },\n  TimePicker: {\n    placeholder: 'اختيار الوقت'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'اختيار التاريخ',\n      yearPlaceholder: 'اختيار السنة',\n      quarterPlaceholder: 'اختيار الربع',\n      monthPlaceholder: 'اختيار الشهر',\n      weekPlaceholder: 'اختيار الأسبوع',\n      rangePlaceholder: ['البداية', 'النهاية'],\n      rangeYearPlaceholder: ['سنة البداية', 'سنة النهاية'],\n      rangeMonthPlaceholder: ['شهر البداية', 'شهر النهاية'],\n      rangeWeekPlaceholder: ['أسبوع البداية', 'أسبوع النهاية'],\n      locale: 'ar_EG',\n      today: 'اليوم',\n      now: 'الأن',\n      backToToday: 'العودة إلى اليوم',\n      ok: 'تأكيد',\n      clear: 'مسح',\n      month: 'الشهر',\n      year: 'السنة',\n      timeSelect: 'اختيار الوقت',\n      dateSelect: 'اختيار التاريخ',\n      weekSelect: 'اختيار الأسبوع',\n      monthSelect: 'اختيار الشهر',\n      yearSelect: 'اختيار السنة',\n      decadeSelect: 'اختيار العقد',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'الشهر السابق (PageUp)',\n      nextMonth: 'الشهر التالى(PageDown)',\n      previousYear: 'العام السابق (Control + left)',\n      nextYear: 'العام التالى (Control + right)',\n      previousDecade: 'العقد السابق',\n      nextDecade: 'العقد التالى',\n      previousCentury: 'القرن السابق',\n      nextCentury: 'القرن التالى'\n    },\n    timePickerLocale: {\n      placeholder: 'اختيار الوقت'\n    },\n    dateFormat: 'DD-MM-YYYY',\n    monthFormat: 'MM-YYYY',\n    dateTimeFormat: 'DD-MM-YYYY HH:mm:ss',\n    weekFormat: 'wo-YYYY'\n  },\n  global: {\n    placeholder: 'يرجى التحديد'\n  },\n  Table: {\n    filterTitle: 'الفلاتر',\n    filterConfirm: 'تأكيد',\n    filterReset: 'إعادة ضبط',\n    selectAll: 'اختيار الكل',\n    selectInvert: 'إلغاء الاختيار',\n    selectionAll: 'حدد جميع البيانات',\n    sortTitle: 'رتب',\n    expand: 'توسيع الصف',\n    collapse: 'طي الصف',\n    triggerDesc: 'ترتيب تنازلي',\n    triggerAsc: 'ترتيب تصاعدي',\n    cancelSort: 'إلغاء الترتيب'\n  },\n  Modal: {\n    okText: 'تأكيد',\n    cancelText: 'إلغاء',\n    justOkText: 'تأكيد'\n  },\n  Popconfirm: {\n    okText: 'تأكيد',\n    cancelText: 'إلغاء'\n  },\n  Transfer: {\n    searchPlaceholder: 'ابحث هنا',\n    itemUnit: 'عنصر',\n    itemsUnit: 'عناصر'\n  },\n  Upload: {\n    uploading: 'جاري الرفع...',\n    removeFile: 'احذف الملف',\n    uploadError: 'مشكلة فى الرفع',\n    previewFile: 'استعرض الملف',\n    downloadFile: 'تحميل الملف'\n  },\n  Empty: {\n    description: 'لا توجد بيانات'\n  },\n  Icon: {\n    icon: 'أيقونة'\n  },\n  Text: {\n    edit: 'تعديل',\n    copy: 'نسخ',\n    copied: 'نقل',\n    expand: 'وسع'\n  },\n  PageHeader: {\n    back: 'عودة'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar az_AZ = {\n  locale: 'az',\n  Pagination: {\n    items_per_page: '/ səhifə',\n    jump_to: 'Get',\n    jump_to_confirm: 'təsdiqlə',\n    page: '',\n    prev_page: 'Əvvəlki Səhifə',\n    next_page: 'Növbəti Səhifə',\n    prev_5: 'Əvvəlki 5 Səhifə',\n    next_5: 'Növbəti 5 Səhifə',\n    prev_3: 'Əvvəlki 3 Səhifə',\n    next_3: 'Növbəti 3 Səhifə',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Tarix seçin',\n      rangePlaceholder: ['Başlama tarixi', 'Bitmə tarixi'],\n      locale: 'az_AZ',\n      today: 'Bugün',\n      now: 'İndi',\n      backToToday: 'Bugünə qayıt',\n      ok: 'Təsdiq',\n      clear: 'Təmizlə',\n      month: 'Ay',\n      year: 'İl',\n      timeSelect: 'vaxtı seç',\n      dateSelect: 'tarixi seç',\n      weekSelect: 'Həftə seç',\n      monthSelect: 'Ay seç',\n      yearSelect: 'il seç',\n      decadeSelect: 'Onillik seçin',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Əvvəlki ay (PageUp)',\n      nextMonth: 'Növbəti ay (PageDown)',\n      previousYear: 'Sonuncu il (Control + left)',\n      nextYear: 'Növbəti il (Control + right)',\n      previousDecade: 'Sonuncu onillik',\n      nextDecade: 'Növbəti onillik',\n      previousCentury: 'Sonuncu əsr',\n      nextCentury: 'Növbəti əsr'\n    },\n    timePickerLocale: {\n      placeholder: 'Vaxtı seç'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Vaxtı seç'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Tarix seçin',\n      rangePlaceholder: ['Başlama tarixi', 'Bitmə tarixi'],\n      locale: 'az_AZ',\n      today: 'Bugün',\n      now: 'İndi',\n      backToToday: 'Bugünə qayıt',\n      ok: 'Təsdiq',\n      clear: 'Təmizlə',\n      month: 'Ay',\n      year: 'İl',\n      timeSelect: 'vaxtı seç',\n      dateSelect: 'tarixi seç',\n      weekSelect: 'Həftə seç',\n      monthSelect: 'Ay seç',\n      yearSelect: 'il seç',\n      decadeSelect: 'Onillik seçin',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Əvvəlki ay (PageUp)',\n      nextMonth: 'Növbəti ay (PageDown)',\n      previousYear: 'Sonuncu il (Control + left)',\n      nextYear: 'Növbəti il (Control + right)',\n      previousDecade: 'Sonuncu onillik',\n      nextDecade: 'Növbəti onillik',\n      previousCentury: 'Sonuncu əsr',\n      nextCentury: 'Növbəti əsr'\n    },\n    timePickerLocale: {\n      placeholder: 'Vaxtı seç'\n    }\n  },\n  Table: {\n    filterTitle: 'Filter menyu',\n    filterConfirm: 'Axtar',\n    filterReset: 'Sıfırla',\n    emptyText: 'Məlumat yoxdur',\n    selectAll: 'Cari səhifəni seç',\n    selectInvert: 'Invert current page'\n  },\n  Modal: {\n    okText: 'Bəli',\n    cancelText: 'Ləğv et',\n    justOkText: 'Bəli'\n  },\n  Popconfirm: {\n    okText: 'Bəli',\n    cancelText: 'Ləğv et'\n  },\n  Transfer: {\n    titles: ['', ''],\n    notFoundContent: 'Tapılmadı',\n    searchPlaceholder: 'Burada axtar',\n    itemUnit: 'item',\n    itemsUnit: 'items'\n  },\n  Select: {\n    notFoundContent: 'Tapılmadı'\n  },\n  Upload: {\n    uploading: 'Yüklənir...',\n    removeFile: 'Faylı sil',\n    uploadError: 'Yükləmə xətası',\n    previewFile: 'Fayla önbaxış'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar bg_BG = {\n  locale: 'bg',\n  Pagination: {\n    items_per_page: '/ страница',\n    jump_to: 'Към',\n    jump_to_confirm: 'потвърждавам',\n    page: '',\n    prev_page: 'Предишна страница',\n    next_page: 'Следваща страница',\n    prev_5: 'Предишни 5 страници',\n    next_5: 'Следващи 5 страници',\n    prev_3: 'Предишни 3 страници',\n    next_3: 'Следващи 3 страници',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Избор на дата',\n      rangePlaceholder: ['Начална', 'Крайна'],\n      locale: 'bg_BG',\n      today: 'Днес',\n      now: 'Сега',\n      backToToday: 'Към днес',\n      ok: 'Добре',\n      clear: 'Изчистване',\n      month: 'Месец',\n      year: 'Година',\n      timeSelect: 'Избор на час',\n      dateSelect: 'Избор на дата',\n      monthSelect: 'Избор на месец',\n      yearSelect: 'Избор на година',\n      decadeSelect: 'Десетилетие',\n      yearFormat: 'YYYY',\n      dateFormat: 'D M YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D M YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Предишен месец (PageUp)',\n      nextMonth: 'Следващ месец (PageDown)',\n      previousYear: 'Последна година (Control + left)',\n      nextYear: 'Следваща година (Control + right)',\n      previousDecade: 'Предишно десетилетие',\n      nextDecade: 'Следващо десетилетие',\n      previousCentury: 'Последен век',\n      nextCentury: 'Следващ век'\n    },\n    timePickerLocale: {\n      placeholder: 'Избор на час'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Избор на час'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Избор на дата',\n      rangePlaceholder: ['Начална', 'Крайна'],\n      locale: 'bg_BG',\n      today: 'Днес',\n      now: 'Сега',\n      backToToday: 'Към днес',\n      ok: 'Добре',\n      clear: 'Изчистване',\n      month: 'Месец',\n      year: 'Година',\n      timeSelect: 'Избор на час',\n      dateSelect: 'Избор на дата',\n      monthSelect: 'Избор на месец',\n      yearSelect: 'Избор на година',\n      decadeSelect: 'Десетилетие',\n      yearFormat: 'YYYY',\n      dateFormat: 'D M YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D M YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Предишен месец (PageUp)',\n      nextMonth: 'Следващ месец (PageDown)',\n      previousYear: 'Последна година (Control + left)',\n      nextYear: 'Следваща година (Control + right)',\n      previousDecade: 'Предишно десетилетие',\n      nextDecade: 'Следващо десетилетие',\n      previousCentury: 'Последен век',\n      nextCentury: 'Следващ век'\n    },\n    timePickerLocale: {\n      placeholder: 'Избор на час'\n    }\n  },\n  Table: {\n    filterTitle: 'Филтриране',\n    filterConfirm: 'Добре',\n    filterReset: 'Нулриане',\n    selectAll: 'Избор на текуща страница',\n    selectInvert: 'Обръщане'\n  },\n  Modal: {\n    okText: 'Добре',\n    cancelText: 'Отказ',\n    justOkText: 'Добре'\n  },\n  Popconfirm: {\n    okText: 'Добре',\n    cancelText: 'Отказ'\n  },\n  Transfer: {\n    searchPlaceholder: 'Търсене',\n    itemUnit: 'избор',\n    itemsUnit: 'избори'\n  },\n  Upload: {\n    uploading: 'Качване...',\n    removeFile: 'Премахване',\n    uploadError: 'Грешка при качването',\n    previewFile: 'Преглед',\n    downloadFile: 'Свали файл'\n  },\n  Empty: {\n    description: 'Няма данни'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar bn_BD = {\n  locale: 'bn-bd',\n  Pagination: {\n    items_per_page: '/ পৃষ্ঠা',\n    jump_to: 'যাও',\n    jump_to_confirm: 'নিশ্চিত',\n    page: 'পৃষ্ঠা',\n    prev_page: 'আগের পৃষ্ঠা',\n    next_page: 'পরের পৃষ্ঠা',\n    prev_5: 'পূর্ববর্তী ৫ পৃষ্ঠা',\n    next_5: 'পরবর্তী ৫ পৃষ্ঠা',\n    prev_3: 'পূর্ববর্তী ৩ পৃষ্ঠা',\n    next_3: 'পরবর্তী ৩ পৃষ্ঠা',\n    page_size: 'পাতার আকার'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'তারিখ নির্বাচন',\n      yearPlaceholder: 'বছর নির্বাচন',\n      quarterPlaceholder: 'কোয়ার্টার নির্বাচন',\n      monthPlaceholder: 'মাস নির্বাচন',\n      weekPlaceholder: 'সপ্তাহ নির্বাচন',\n      rangePlaceholder: ['শুরুর তারিখ', 'শেষ তারিখ'],\n      rangeYearPlaceholder: ['শুরুর বছর', 'শেষ বছর'],\n      rangeMonthPlaceholder: ['শুরুর মাস', 'শেষ মাস'],\n      rangeWeekPlaceholder: ['শুরুর সপ্তাহ', 'শেষ সপ্তাহ'],\n      locale: 'bn_BD',\n      today: 'আজ',\n      now: 'এখন',\n      backToToday: 'আজকে ফিরে চলুন',\n      ok: 'ওকে',\n      clear: 'পরিস্কার',\n      month: 'মাস',\n      year: 'বছর',\n      timeSelect: 'সময় নির্বাচন',\n      dateSelect: 'তারিখ নির্বাচন',\n      weekSelect: 'সপ্তাহ পছন্দ করুন',\n      monthSelect: 'মাস পছন্দ করুন',\n      yearSelect: 'বছর পছন্দ করুন',\n      decadeSelect: 'একটি দশক পছন্দ করুন',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'গত মাস (PageUp)',\n      nextMonth: 'আগামী মাস (PageDown)',\n      previousYear: 'গত বছর (Control + left)',\n      nextYear: 'আগামী বছর (Control + right)',\n      previousDecade: 'গত দশক',\n      nextDecade: 'পরের দশক',\n      previousCentury: 'গত শতাব্দী',\n      nextCentury: 'পরের শতাব্দী'\n    },\n    timePickerLocale: {\n      placeholder: 'সময় নির্বাচন',\n      rangePlaceholder: ['সময় শুরু', 'শেষ সময়']\n    }\n  },\n  TimePicker: {\n    placeholder: 'সময় নির্বাচন',\n    rangePlaceholder: ['সময় শুরু', 'শেষ সময়']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'তারিখ নির্বাচন',\n      yearPlaceholder: 'বছর নির্বাচন',\n      quarterPlaceholder: 'কোয়ার্টার নির্বাচন',\n      monthPlaceholder: 'মাস নির্বাচন',\n      weekPlaceholder: 'সপ্তাহ নির্বাচন',\n      rangePlaceholder: ['শুরুর তারিখ', 'শেষ তারিখ'],\n      rangeYearPlaceholder: ['শুরুর বছর', 'শেষ বছর'],\n      rangeMonthPlaceholder: ['শুরুর মাস', 'শেষ মাস'],\n      rangeWeekPlaceholder: ['শুরুর সপ্তাহ', 'শেষ সপ্তাহ'],\n      locale: 'bn_BD',\n      today: 'আজ',\n      now: 'এখন',\n      backToToday: 'আজকে ফিরে চলুন',\n      ok: 'ওকে',\n      clear: 'পরিস্কার',\n      month: 'মাস',\n      year: 'বছর',\n      timeSelect: 'সময় নির্বাচন',\n      dateSelect: 'তারিখ নির্বাচন',\n      weekSelect: 'সপ্তাহ পছন্দ করুন',\n      monthSelect: 'মাস পছন্দ করুন',\n      yearSelect: 'বছর পছন্দ করুন',\n      decadeSelect: 'একটি দশক পছন্দ করুন',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'গত মাস (PageUp)',\n      nextMonth: 'আগামী মাস (PageDown)',\n      previousYear: 'গত বছর (Control + left)',\n      nextYear: 'আগামী বছর (Control + right)',\n      previousDecade: 'গত দশক',\n      nextDecade: 'পরের দশক',\n      previousCentury: 'গত শতাব্দী',\n      nextCentury: 'পরের শতাব্দী'\n    },\n    timePickerLocale: {\n      placeholder: 'সময় নির্বাচন',\n      rangePlaceholder: ['সময় শুরু', 'শেষ সময়']\n    }\n  },\n  global: {\n    placeholder: 'অনুগ্রহ করে নির্বাচন করুন'\n  },\n  Table: {\n    filterTitle: 'ফিল্টার মেনু',\n    filterConfirm: 'ঠিক',\n    filterReset: 'রিসেট',\n    filterEmptyText: 'ফিল্টার নেই',\n    emptyText: 'কোনও ডেটা নেই',\n    selectAll: 'বর্তমান পৃষ্ঠা নির্বাচন করুন',\n    selectInvert: 'বর্তমান পৃষ্ঠাটি উল্টে দিন',\n    selectNone: 'সমস্ত ডেটা সাফ করুন',\n    selectionAll: 'সমস্ত ডেটা নির্বাচন করুন',\n    sortTitle: 'সাজান',\n    expand: 'সারি প্রসারিত করুন',\n    collapse: 'সারি সঙ্কুচিত করুন',\n    triggerDesc: 'অবতরণকে সাজানোর জন্য ক্লিক করুন',\n    triggerAsc: 'আরোহী বাছাই করতে ক্লিক করুন',\n    cancelSort: 'বাছাই বাতিল করতে ক্লিক করুন'\n  },\n  Modal: {\n    okText: 'ঠিক',\n    cancelText: 'বাতিল',\n    justOkText: 'ঠিক'\n  },\n  Popconfirm: {\n    okText: 'ঠিক',\n    cancelText: 'বাতিল'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'এখানে অনুসন্ধান',\n    itemUnit: 'আইটেম',\n    itemsUnit: 'আইটেমসমূহ',\n    remove: 'অপসারণ',\n    selectCurrent: 'বর্তমান পৃষ্ঠা নির্বাচন করুন',\n    removeCurrent: 'বর্তমান পৃষ্ঠাটি সরান',\n    selectAll: 'সমস্ত ডেটা নির্বাচন করুন',\n    removeAll: 'সমস্ত ডেটা সরান',\n    selectInvert: 'বর্তমান পৃষ্ঠাটি উল্টে দিন'\n  },\n  Upload: {\n    uploading: 'আপলোড হচ্ছে ...',\n    removeFile: 'ফাইল সরান',\n    uploadError: 'আপলোডে সমস্যা',\n    previewFile: 'ফাইলের পূর্বরূপ',\n    downloadFile: 'ফাইল ডাউনলোড'\n  },\n  Empty: {\n    description: 'কোনও ডেটা নেই'\n  },\n  Icon: {\n    icon: 'আইকন'\n  },\n  Text: {\n    edit: 'সম্পাদনা',\n    copy: 'অনুলিপি',\n    copied: 'অনুলিপি হয়েছে',\n    expand: 'বিস্তৃত করা'\n  },\n  PageHeader: {\n    back: 'পেছনে'\n  },\n  Image: {\n    preview: 'পূর্বরূপ'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar by_BY = {\n  locale: 'by',\n  Pagination: {\n    items_per_page: '/старонка',\n    jump_to: 'Перайсці',\n    jump_to_confirm: 'Пацвердзіць',\n    page: '',\n    prev_page: 'Назад',\n    next_page: 'Наперад',\n    prev_5: 'Папярэднія 5',\n    next_5: 'Наступныя 5',\n    prev_3: 'Папярэднія 3',\n    next_3: 'Наступныя 3',\n    page_size: 'памер старонкі'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Выберыце дату',\n      yearPlaceholder: 'Выберыце год',\n      quarterPlaceholder: 'Выберыце квартал',\n      monthPlaceholder: 'Выберыце месяц',\n      weekPlaceholder: 'Выберыце тыдзень',\n      rangePlaceholder: ['Пачатковая дата', 'Канчатковая дата'],\n      rangeYearPlaceholder: ['Пачатковы год', 'Год заканчэння'],\n      rangeMonthPlaceholder: ['Пачатковы месяц', 'Канчатковы месяц'],\n      rangeWeekPlaceholder: ['Пачатковы тыдзень', 'Канчатковы тыдзень'],\n      locale: 'by_BY',\n      today: 'Сёння',\n      now: 'Зараз',\n      backToToday: 'Дадзеная дата',\n      ok: 'Ok',\n      clear: 'Ачысціць',\n      month: 'Месяц',\n      year: 'Год',\n      timeSelect: 'Выбраць час',\n      dateSelect: 'Выбраць дату',\n      weekSelect: 'Выбраць тыдзень',\n      monthSelect: 'Выбраць месяц',\n      yearSelect: 'Выбраць год',\n      decadeSelect: 'Выбраць дзесяцігоддзе',\n      yearFormat: 'YYYY',\n      dateFormat: 'D-M-YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D-M-YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Папярэдні месяц (PageUp)',\n      nextMonth: 'Наступны месяц (PageDown)',\n      previousYear: 'Папярэдні год (Control + left)',\n      nextYear: 'Наступны год (Control + right)',\n      previousDecade: 'Папярэдняе дзесяцігоддзе',\n      nextDecade: 'Наступнае дзесяцігоддзе',\n      previousCentury: 'Папярэдні век',\n      nextCentury: 'Наступны век'\n    },\n    timePickerLocale: {\n      placeholder: 'Выберыце час',\n      rangePlaceholder: ['Час пачатку', 'Час заканчэння']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Выберыце час',\n    rangePlaceholder: ['Час пачатку', 'Час заканчэння']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Выберыце дату',\n      yearPlaceholder: 'Выберыце год',\n      quarterPlaceholder: 'Выберыце квартал',\n      monthPlaceholder: 'Выберыце месяц',\n      weekPlaceholder: 'Выберыце тыдзень',\n      rangePlaceholder: ['Пачатковая дата', 'Канчатковая дата'],\n      rangeYearPlaceholder: ['Пачатковы год', 'Год заканчэння'],\n      rangeMonthPlaceholder: ['Пачатковы месяц', 'Канчатковы месяц'],\n      rangeWeekPlaceholder: ['Пачатковы тыдзень', 'Канчатковы тыдзень'],\n      locale: 'by_BY',\n      today: 'Сёння',\n      now: 'Зараз',\n      backToToday: 'Дадзеная дата',\n      ok: 'Ok',\n      clear: 'Ачысціць',\n      month: 'Месяц',\n      year: 'Год',\n      timeSelect: 'Выбраць час',\n      dateSelect: 'Выбраць дату',\n      weekSelect: 'Выбраць тыдзень',\n      monthSelect: 'Выбраць месяц',\n      yearSelect: 'Выбраць год',\n      decadeSelect: 'Выбраць дзесяцігоддзе',\n      yearFormat: 'YYYY',\n      dateFormat: 'D-M-YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D-M-YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Папярэдні месяц (PageUp)',\n      nextMonth: 'Наступны месяц (PageDown)',\n      previousYear: 'Папярэдні год (Control + left)',\n      nextYear: 'Наступны год (Control + right)',\n      previousDecade: 'Папярэдняе дзесяцігоддзе',\n      nextDecade: 'Наступнае дзесяцігоддзе',\n      previousCentury: 'Папярэдні век',\n      nextCentury: 'Наступны век'\n    },\n    timePickerLocale: {\n      placeholder: 'Выберыце час',\n      rangePlaceholder: ['Час пачатку', 'Час заканчэння']\n    }\n  },\n  global: {\n    placeholder: 'Калі ласка выберыце'\n  },\n  Table: {\n    filterTitle: 'Фільтр',\n    filterConfirm: 'OK',\n    filterReset: 'Скінуць',\n    filterEmptyText: 'Без фільтраў',\n    emptyText: 'Няма дадзеных',\n    selectAll: 'Выбраць усе',\n    selectInvert: 'Інвертаваць выбар',\n    selectionAll: 'Выбраць усе дадзеныя',\n    sortTitle: 'Сартаванне',\n    expand: 'Разгарнуць радок',\n    collapse: 'Згарнуць радок',\n    triggerDesc: 'Націсніце для сартавання па змяншэнні',\n    triggerAsc: 'Націсніце для сартавання па ўзросту',\n    cancelSort: 'Націсніце, каб адмяніць сартаванне'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Адмена',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Адмена'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Пошук',\n    itemUnit: 'элем.',\n    itemsUnit: 'элем.',\n    remove: 'Выдаліць',\n    selectAll: 'Выбраць усе дадзеныя',\n    selectCurrent: 'Вылучыць дадзеную старонку',\n    selectInvert: 'Паказаць у зваротным парадку',\n    removeAll: 'Выдаліць усе дадзеныя',\n    removeCurrent: 'Выдаліць дадзеную старонку'\n  },\n  Upload: {\n    uploading: 'Загрузка...',\n    removeFile: 'Выдаліць файл',\n    uploadError: 'Адбылася памылка пры загрузцы',\n    previewFile: 'Прадпрагляд файла',\n    downloadFile: 'Загрузіць файл'\n  },\n  Empty: {\n    description: 'Няма дадзеных'\n  },\n  Icon: {\n    icon: 'Іконка'\n  },\n  Text: {\n    edit: 'Рэдагаваць',\n    copy: 'Капіяваць',\n    copied: 'Капіяванне завершана',\n    expand: 'Разгарнуць'\n  },\n  PageHeader: {\n    back: 'Назад'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ca_ES = {\n  locale: 'ca',\n  Pagination: {\n    items_per_page: '/ pàgina',\n    jump_to: 'Anar a',\n    jump_to_confirm: 'Confirma',\n    page: '',\n    prev_page: 'Pàgina prèvia',\n    next_page: 'Pàgina següent',\n    prev_5: '5 pàgines prèvies',\n    next_5: '5 pàgines següents',\n    prev_3: '3 pàgines prèvies',\n    next_3: '3 pàgines següents',\n    page_size: 'mida de la pàgina'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Seleccionar data',\n      rangePlaceholder: ['Data inicial', 'Data final'],\n      locale: 'ca_ES',\n      today: 'Avui',\n      now: 'Ara',\n      backToToday: 'Tornar a avui',\n      ok: 'Acceptar',\n      clear: 'Netejar',\n      month: 'Mes',\n      year: 'Any',\n      timeSelect: 'Seleccionar hora',\n      dateSelect: 'Seleccionar data',\n      monthSelect: 'Escollir un mes',\n      yearSelect: 'Escollir un any',\n      decadeSelect: 'Escollir una dècada',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Mes anterior (PageUp)',\n      nextMonth: 'Mes següent (PageDown)',\n      previousYear: 'Any anterior (Control + left)',\n      nextYear: 'Mes següent (Control + right)',\n      previousDecade: 'Dècada anterior',\n      nextDecade: 'Dècada següent',\n      previousCentury: 'Segle anterior',\n      nextCentury: 'Segle següent'\n    },\n    timePickerLocale: {\n      placeholder: 'Seleccionar hora'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Seleccionar hora'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Seleccionar data',\n      rangePlaceholder: ['Data inicial', 'Data final'],\n      locale: 'ca_ES',\n      today: 'Avui',\n      now: 'Ara',\n      backToToday: 'Tornar a avui',\n      ok: 'Acceptar',\n      clear: 'Netejar',\n      month: 'Mes',\n      year: 'Any',\n      timeSelect: 'Seleccionar hora',\n      dateSelect: 'Seleccionar data',\n      monthSelect: 'Escollir un mes',\n      yearSelect: 'Escollir un any',\n      decadeSelect: 'Escollir una dècada',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Mes anterior (PageUp)',\n      nextMonth: 'Mes següent (PageDown)',\n      previousYear: 'Any anterior (Control + left)',\n      nextYear: 'Mes següent (Control + right)',\n      previousDecade: 'Dècada anterior',\n      nextDecade: 'Dècada següent',\n      previousCentury: 'Segle anterior',\n      nextCentury: 'Segle següent'\n    },\n    timePickerLocale: {\n      placeholder: 'Seleccionar hora'\n    }\n  },\n  global: {\n    placeholder: 'Seleccionar'\n  },\n  Table: {\n    filterTitle: 'Filtrar el menú',\n    filterConfirm: 'D’acord',\n    filterReset: 'Reiniciar',\n    filterEmptyText: 'Sense filtres',\n    selectAll: 'Seleccionar la pàgina actual',\n    selectInvert: 'Invertir la selecció',\n    selectionAll: 'Seleccionar-ho tot',\n    sortTitle: 'Ordenar',\n    expand: 'Ampliar la fila',\n    collapse: 'Plegar la fila',\n    triggerDesc: 'Ordre descendent',\n    triggerAsc: 'Ordre ascendent',\n    cancelSort: 'Desactivar l’ordre'\n  },\n  Modal: {\n    okText: 'D’acord',\n    cancelText: 'Cancel·lar',\n    justOkText: 'D’acord'\n  },\n  Popconfirm: {\n    okText: 'D’acord',\n    cancelText: 'Cancel·lar'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Cercar',\n    itemUnit: 'ítem',\n    itemsUnit: 'ítems',\n    remove: 'Eliminar',\n    selectCurrent: 'Seleccionar la pàgina actual',\n    removeCurrent: 'Eliminar la selecció',\n    selectAll: 'Seleccionar-ho tot',\n    removeAll: 'Eliminar-ho tot',\n    selectInvert: 'Invertir la selecció'\n  },\n  Upload: {\n    uploading: 'Carregant…',\n    removeFile: 'Eliminar el fitxer',\n    uploadError: 'Error de càrrega',\n    previewFile: 'Vista prèvia del fitxer',\n    downloadFile: 'Baixar el fitxer'\n  },\n  Empty: {\n    description: 'Sense dades'\n  },\n  Icon: {\n    icon: 'icona'\n  },\n  Text: {\n    edit: 'Editar',\n    copy: 'Copiar',\n    copied: 'Copiat',\n    expand: 'Ampliar'\n  },\n  PageHeader: {\n    back: 'Enrere'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar cs_CZ = {\n  locale: 'cs',\n  Pagination: {\n    items_per_page: '/ strana',\n    jump_to: 'Přejít',\n    jump_to_confirm: 'potvrdit',\n    page: '',\n    prev_page: 'Předchozí strana',\n    next_page: 'Následující strana',\n    prev_5: 'Předchozích 5 stran',\n    next_5: 'Následujících 5 stran',\n    prev_3: 'Předchozí 3 strany',\n    next_3: 'Následující 3 strany',\n    page_size: 'velikost stránky'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Vybrat datum',\n      rangePlaceholder: ['Od', 'Do'],\n      locale: 'cs_CZ',\n      today: 'Dnes',\n      now: 'Nyní',\n      backToToday: 'Zpět na dnešek',\n      ok: 'Ok',\n      clear: 'Vymazat',\n      month: 'Měsíc',\n      year: 'Rok',\n      timeSelect: 'Vybrat čas',\n      dateSelect: 'Vybrat datum',\n      monthSelect: 'Vyberte měsíc',\n      yearSelect: 'Vyberte rok',\n      decadeSelect: 'Vyberte dekádu',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Předchozí měsíc (PageUp)',\n      nextMonth: 'Následující (PageDown)',\n      previousYear: 'Předchozí rok (Control + left)',\n      nextYear: 'Následující rok (Control + right)',\n      previousDecade: 'Předchozí dekáda',\n      nextDecade: 'Následující dekáda',\n      previousCentury: 'Předchozí století',\n      nextCentury: 'Následující století'\n    },\n    timePickerLocale: {\n      placeholder: 'Vybrat čas'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Vybrat čas'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Vybrat datum',\n      rangePlaceholder: ['Od', 'Do'],\n      locale: 'cs_CZ',\n      today: 'Dnes',\n      now: 'Nyní',\n      backToToday: 'Zpět na dnešek',\n      ok: 'Ok',\n      clear: 'Vymazat',\n      month: 'Měsíc',\n      year: 'Rok',\n      timeSelect: 'Vybrat čas',\n      dateSelect: 'Vybrat datum',\n      monthSelect: 'Vyberte měsíc',\n      yearSelect: 'Vyberte rok',\n      decadeSelect: 'Vyberte dekádu',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Předchozí měsíc (PageUp)',\n      nextMonth: 'Následující (PageDown)',\n      previousYear: 'Předchozí rok (Control + left)',\n      nextYear: 'Následující rok (Control + right)',\n      previousDecade: 'Předchozí dekáda',\n      nextDecade: 'Následující dekáda',\n      previousCentury: 'Předchozí století',\n      nextCentury: 'Následující století'\n    },\n    timePickerLocale: {\n      placeholder: 'Vybrat čas'\n    }\n  },\n  global: {\n    placeholder: 'Prosím vyber'\n  },\n  Table: {\n    filterTitle: 'Filtr',\n    filterConfirm: 'Potvrdit',\n    filterReset: 'Obnovit',\n    filterEmptyText: 'Žádné filtry',\n    selectAll: 'Vybrat všechny řádky na současné stránce',\n    selectInvert: 'Invertovat výběr na současné stránce',\n    selectionAll: 'Vybrat všechny řádky',\n    sortTitle: 'Řadit',\n    expand: 'Rozbalit řádek',\n    collapse: 'Zabalit řádek',\n    triggerDesc: 'Klikni pro sestupné řazení',\n    triggerAsc: 'Klikni pro vzestupné řazení',\n    cancelSort: 'Klikni pro zrušení řazení'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Storno',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Storno'\n  },\n  Transfer: {\n    searchPlaceholder: 'Vyhledávání',\n    itemUnit: 'položka',\n    itemsUnit: 'položek'\n  },\n  Upload: {\n    uploading: 'Nahrávání...',\n    removeFile: 'Odstranit soubor',\n    uploadError: 'Chyba při nahrávání',\n    previewFile: 'Zobrazit soubor',\n    downloadFile: 'Stáhnout soubor'\n  },\n  Empty: {\n    description: 'Žádná data'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar da_DK = {\n  locale: 'da',\n  DatePicker: {\n    lang: {\n      placeholder: 'Vælg dato',\n      rangePlaceholder: ['Startdato', 'Slutdato'],\n      locale: 'da_DK',\n      today: 'I dag',\n      now: 'Nu',\n      backToToday: 'Gå til i dag',\n      ok: 'Ok',\n      clear: 'Ryd',\n      month: 'Måned',\n      year: 'År',\n      timeSelect: 'Vælg tidspunkt',\n      dateSelect: 'Vælg dato',\n      monthSelect: 'Vælg måned',\n      yearSelect: 'Vælg år',\n      decadeSelect: 'Vælg årti',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Forrige måned (Page Up)',\n      nextMonth: 'Næste måned (Page Down)',\n      previousYear: 'Forrige år (Ctrl-venstre pil)',\n      nextYear: 'Næste år (Ctrl-højre pil)',\n      previousDecade: 'Forrige årti',\n      nextDecade: 'Næste årti',\n      previousCentury: 'Forrige århundrede',\n      nextCentury: 'Næste århundrede'\n    },\n    timePickerLocale: {\n      placeholder: 'Vælg tid',\n      rangePlaceholder: ['Starttidspunkt', 'Sluttidspunkt']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Vælg tid',\n    rangePlaceholder: ['Starttidspunkt', 'Sluttidspunkt']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Vælg dato',\n      rangePlaceholder: ['Startdato', 'Slutdato'],\n      locale: 'da_DK',\n      today: 'I dag',\n      now: 'Nu',\n      backToToday: 'Gå til i dag',\n      ok: 'Ok',\n      clear: 'Ryd',\n      month: 'Måned',\n      year: 'År',\n      timeSelect: 'Vælg tidspunkt',\n      dateSelect: 'Vælg dato',\n      monthSelect: 'Vælg måned',\n      yearSelect: 'Vælg år',\n      decadeSelect: 'Vælg årti',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Forrige måned (Page Up)',\n      nextMonth: 'Næste måned (Page Down)',\n      previousYear: 'Forrige år (Ctrl-venstre pil)',\n      nextYear: 'Næste år (Ctrl-højre pil)',\n      previousDecade: 'Forrige årti',\n      nextDecade: 'Næste årti',\n      previousCentury: 'Forrige århundrede',\n      nextCentury: 'Næste århundrede'\n    },\n    timePickerLocale: {\n      placeholder: 'Vælg tid',\n      rangePlaceholder: ['Starttidspunkt', 'Sluttidspunkt']\n    }\n  },\n  Pagination: {\n    items_per_page: '/ side',\n    jump_to: 'Gå til',\n    jump_to_confirm: 'bekræft',\n    page: 'Side',\n    prev_page: 'Forrige Side',\n    next_page: 'Næste Side',\n    prev_5: 'Forrige 5 Sider',\n    next_5: 'Næste 5 Sider',\n    prev_3: 'Forrige 3 Sider',\n    next_3: 'Næste 3 Sider',\n    page_size: 'sidestørrelse'\n  },\n  Table: {\n    filterTitle: 'Filtermenu',\n    filterConfirm: 'OK',\n    filterReset: 'Nulstil',\n    selectAll: 'Vælg alle',\n    selectInvert: 'Invertér valg',\n    filterEmptyText: 'Ingen filtre',\n    emptyText: 'Ingen data',\n    selectNone: 'Ryd alt data',\n    selectionAll: 'Vælg alt data',\n    sortTitle: 'Sortér',\n    expand: 'Udvid række',\n    collapse: 'Flet række',\n    triggerDesc: 'Klik for at sortere faldende',\n    triggerAsc: 'Klik for at sortere stigende',\n    cancelSort: 'Klik for at annullere sortering'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Afbryd',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Afbryd'\n  },\n  Transfer: {\n    searchPlaceholder: 'Søg her',\n    itemUnit: 'element',\n    itemsUnit: 'elementer'\n  },\n  Upload: {\n    uploading: 'Uploader...',\n    removeFile: 'Fjern fil',\n    uploadError: 'Fejl ved upload',\n    previewFile: 'Forhåndsvisning',\n    downloadFile: 'Download fil'\n  },\n  Empty: {\n    description: 'Ingen data'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar de_DE = {\n  locale: 'de',\n  Pagination: {\n    items_per_page: '/ Seite',\n    jump_to: 'Gehe zu',\n    jump_to_confirm: 'bestätigen',\n    page: 'Seite',\n    prev_page: 'Vorherige Seite',\n    next_page: 'Nächste Seite',\n    prev_5: '5 Seiten zurück',\n    next_5: '5 Seiten vor',\n    prev_3: '3 Seiten zurück',\n    next_3: '3 Seiten vor',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Datum auswählen',\n      yearPlaceholder: 'Jahr auswählen',\n      quarterPlaceholder: 'Quartal auswählen',\n      monthPlaceholder: 'Monat auswählen',\n      weekPlaceholder: 'Woche auswählen',\n      rangePlaceholder: ['Startdatum', 'Enddatum'],\n      rangeYearPlaceholder: ['Startjahr', 'Endjahr'],\n      rangeMonthPlaceholder: ['Startmonat', 'Endmonat'],\n      rangeWeekPlaceholder: ['Startwoche', 'Endwoche'],\n      locale: 'de_DE',\n      today: 'Heute',\n      now: 'Jetzt',\n      backToToday: 'Zurück zu Heute',\n      ok: 'OK',\n      clear: 'Zurücksetzen',\n      month: 'Monat',\n      year: 'Jahr',\n      timeSelect: 'Zeit wählen',\n      dateSelect: 'Datum wählen',\n      weekSelect: 'Woche wählen',\n      monthSelect: 'Wähle einen Monat',\n      yearSelect: 'Wähle ein Jahr',\n      decadeSelect: 'Wähle ein Jahrzehnt',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Vorheriger Monat (PageUp)',\n      nextMonth: 'Nächster Monat (PageDown)',\n      previousYear: 'Vorheriges Jahr (Ctrl + left)',\n      nextYear: 'Nächstes Jahr (Ctrl + right)',\n      previousDecade: 'Vorheriges Jahrzehnt',\n      nextDecade: 'Nächstes Jahrzehnt',\n      previousCentury: 'Vorheriges Jahrhundert',\n      nextCentury: 'Nächstes Jahrhundert'\n    },\n    timePickerLocale: {\n      placeholder: 'Zeit auswählen',\n      rangePlaceholder: ['Startzeit', 'Endzeit']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Zeit auswählen'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Datum auswählen',\n      yearPlaceholder: 'Jahr auswählen',\n      quarterPlaceholder: 'Quartal auswählen',\n      monthPlaceholder: 'Monat auswählen',\n      weekPlaceholder: 'Woche auswählen',\n      rangePlaceholder: ['Startdatum', 'Enddatum'],\n      rangeYearPlaceholder: ['Startjahr', 'Endjahr'],\n      rangeMonthPlaceholder: ['Startmonat', 'Endmonat'],\n      rangeWeekPlaceholder: ['Startwoche', 'Endwoche'],\n      locale: 'de_DE',\n      today: 'Heute',\n      now: 'Jetzt',\n      backToToday: 'Zurück zu Heute',\n      ok: 'OK',\n      clear: 'Zurücksetzen',\n      month: 'Monat',\n      year: 'Jahr',\n      timeSelect: 'Zeit wählen',\n      dateSelect: 'Datum wählen',\n      weekSelect: 'Woche wählen',\n      monthSelect: 'Wähle einen Monat',\n      yearSelect: 'Wähle ein Jahr',\n      decadeSelect: 'Wähle ein Jahrzehnt',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Vorheriger Monat (PageUp)',\n      nextMonth: 'Nächster Monat (PageDown)',\n      previousYear: 'Vorheriges Jahr (Ctrl + left)',\n      nextYear: 'Nächstes Jahr (Ctrl + right)',\n      previousDecade: 'Vorheriges Jahrzehnt',\n      nextDecade: 'Nächstes Jahrzehnt',\n      previousCentury: 'Vorheriges Jahrhundert',\n      nextCentury: 'Nächstes Jahrhundert'\n    },\n    timePickerLocale: {\n      placeholder: 'Zeit auswählen',\n      rangePlaceholder: ['Startzeit', 'Endzeit']\n    }\n  },\n  global: {\n    placeholder: 'Bitte auswählen'\n  },\n  Table: {\n    filterTitle: 'Filter-Menü',\n    filterConfirm: 'OK',\n    filterReset: 'Zurücksetzen',\n    filterEmptyText: 'Keine Filter',\n    emptyText: 'Keine Daten',\n    selectAll: 'Selektiere Alle',\n    selectInvert: 'Selektion Invertieren',\n    selectionAll: 'Wählen Sie alle Daten aus',\n    sortTitle: 'Sortieren',\n    expand: 'Zeile erweitern',\n    collapse: 'Zeile reduzieren',\n    triggerDesc: 'Klicken zur absteigenden  Sortierung',\n    triggerAsc: 'Klicken zur aufsteigenden Sortierung',\n    cancelSort: 'Klicken zum Abbrechen der Sortierung'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Abbrechen',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Abbrechen'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Suchen',\n    itemUnit: 'Eintrag',\n    itemsUnit: 'Einträge',\n    remove: 'Entfernen',\n    selectCurrent: 'Alle auf aktueller Seite auswählen',\n    removeCurrent: 'Auswahl auf aktueller Seite aufheben',\n    selectAll: 'Alle auswählen',\n    removeAll: 'Auswahl aufheben',\n    selectInvert: 'Auswahl umkehren'\n  },\n  Upload: {\n    uploading: 'Hochladen...',\n    removeFile: 'Datei entfernen',\n    uploadError: 'Fehler beim Hochladen',\n    previewFile: 'Dateivorschau',\n    downloadFile: 'Download-Datei'\n  },\n  Empty: {\n    description: 'Keine Daten'\n  },\n  Text: {\n    edit: 'Bearbeiten',\n    copy: 'Kopieren',\n    copied: 'Kopiert',\n    expand: 'Erweitern'\n  },\n  PageHeader: {\n    back: 'Zurück'\n  },\n  Image: {\n    preview: 'Vorschau'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar el_GR = {\n  locale: 'el',\n  Pagination: {\n    items_per_page: '/ σελίδα',\n    jump_to: 'Μετάβαση',\n    jump_to_confirm: 'επιβεβαιώνω',\n    page: '',\n    prev_page: 'Προηγούμενη Σελίδα',\n    next_page: 'Επόμενη Σελίδα',\n    prev_5: 'Προηγούμενες 5 Σελίδες',\n    next_5: 'Επόμενες 5 σελίδες',\n    prev_3: 'Προηγούμενες 3 Σελίδες',\n    next_3: 'Επόμενες 3 Σελίδες',\n    page_size: 'Μέγεθος σελίδας'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Επιλέξτε ημερομηνία',\n      yearPlaceholder: 'Επιλέξτε χρονιά',\n      quarterPlaceholder: 'Επιλέξτε τρίμηνο',\n      monthPlaceholder: 'Επιλέξτε μήνα',\n      weekPlaceholder: 'Επιλέξτε εβδομάδα',\n      rangePlaceholder: ['Αρχική ημερομηνία', 'Τελική ημερομηνία'],\n      rangeYearPlaceholder: ['Αρχική χρονιά', 'Τελική χρονιά'],\n      rangeMonthPlaceholder: ['Αρχικός μήνας', 'Τελικός μήνας'],\n      rangeWeekPlaceholder: ['Αρχική εβδομάδα', 'Τελική εβδομάδα'],\n      locale: 'el_GR',\n      today: 'Σήμερα',\n      now: 'Τώρα',\n      backToToday: 'Πίσω στη σημερινή μέρα',\n      ok: 'Ok',\n      clear: 'Καθαρισμός',\n      month: 'Μήνας',\n      year: 'Έτος',\n      timeSelect: 'Επιλογή ώρας',\n      dateSelect: 'Επιλογή ημερομηνίας',\n      weekSelect: 'Επιλογή εβδομάδας',\n      monthSelect: 'Επιλογή μήνα',\n      yearSelect: 'Επιλογή έτους',\n      decadeSelect: 'Επιλογή δεκαετίας',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Προηγούμενος μήνας (PageUp)',\n      nextMonth: 'Επόμενος μήνας (PageDown)',\n      previousYear: 'Προηγούμενο έτος (Control + αριστερά)',\n      nextYear: 'Επόμενο έτος (Control + δεξιά)',\n      previousDecade: 'Προηγούμενη δεκαετία',\n      nextDecade: 'Επόμενη δεκαετία',\n      previousCentury: 'Προηγούμενος αιώνας',\n      nextCentury: 'Επόμενος αιώνας'\n    },\n    timePickerLocale: {\n      placeholder: 'Επιλέξτε ώρα',\n      rangePlaceholder: ['Ώρα έναρξης', 'Ώρα λήξης']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Επιλέξτε ώρα',\n    rangePlaceholder: ['Ώρα έναρξης', 'Ώρα λήξης']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Επιλέξτε ημερομηνία',\n      yearPlaceholder: 'Επιλέξτε χρονιά',\n      quarterPlaceholder: 'Επιλέξτε τρίμηνο',\n      monthPlaceholder: 'Επιλέξτε μήνα',\n      weekPlaceholder: 'Επιλέξτε εβδομάδα',\n      rangePlaceholder: ['Αρχική ημερομηνία', 'Τελική ημερομηνία'],\n      rangeYearPlaceholder: ['Αρχική χρονιά', 'Τελική χρονιά'],\n      rangeMonthPlaceholder: ['Αρχικός μήνας', 'Τελικός μήνας'],\n      rangeWeekPlaceholder: ['Αρχική εβδομάδα', 'Τελική εβδομάδα'],\n      locale: 'el_GR',\n      today: 'Σήμερα',\n      now: 'Τώρα',\n      backToToday: 'Πίσω στη σημερινή μέρα',\n      ok: 'Ok',\n      clear: 'Καθαρισμός',\n      month: 'Μήνας',\n      year: 'Έτος',\n      timeSelect: 'Επιλογή ώρας',\n      dateSelect: 'Επιλογή ημερομηνίας',\n      weekSelect: 'Επιλογή εβδομάδας',\n      monthSelect: 'Επιλογή μήνα',\n      yearSelect: 'Επιλογή έτους',\n      decadeSelect: 'Επιλογή δεκαετίας',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Προηγούμενος μήνας (PageUp)',\n      nextMonth: 'Επόμενος μήνας (PageDown)',\n      previousYear: 'Προηγούμενο έτος (Control + αριστερά)',\n      nextYear: 'Επόμενο έτος (Control + δεξιά)',\n      previousDecade: 'Προηγούμενη δεκαετία',\n      nextDecade: 'Επόμενη δεκαετία',\n      previousCentury: 'Προηγούμενος αιώνας',\n      nextCentury: 'Επόμενος αιώνας'\n    },\n    timePickerLocale: {\n      placeholder: 'Επιλέξτε ώρα',\n      rangePlaceholder: ['Ώρα έναρξης', 'Ώρα λήξης']\n    }\n  },\n  Table: {\n    filterTitle: 'Μενού φίλτρων',\n    filterConfirm: 'ΟΚ',\n    filterReset: 'Επαναφορά',\n    selectAll: 'Επιλογή τρέχουσας σελίδας',\n    selectInvert: 'Αντιστροφή τρέχουσας σελίδας'\n  },\n  Modal: {\n    okText: 'ΟΚ',\n    cancelText: 'Άκυρο',\n    justOkText: 'ΟΚ'\n  },\n  Popconfirm: {\n    okText: 'ΟΚ',\n    cancelText: 'Άκυρο'\n  },\n  Transfer: {\n    searchPlaceholder: 'Αναζήτηση',\n    itemUnit: 'αντικείμενο',\n    itemsUnit: 'αντικείμενα'\n  },\n  Upload: {\n    uploading: 'Μεταφόρτωση...',\n    removeFile: 'Αφαίρεση αρχείου',\n    uploadError: 'Σφάλμα μεταφόρτωσης',\n    previewFile: 'Προεπισκόπηση αρχείου',\n    downloadFile: 'Λήψη αρχείου'\n  },\n  Empty: {\n    description: 'Δεν υπάρχουν δεδομένα'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar en_GB = {\n  locale: 'en-gb',\n  Pagination: {\n    items_per_page: '/ page',\n    jump_to: 'Go to',\n    jump_to_confirm: 'confirm',\n    page: 'Page',\n    prev_page: 'Previous Page',\n    next_page: 'Next Page',\n    prev_5: 'Previous 5 Pages',\n    next_5: 'Next 5 Pages',\n    prev_3: 'Previous 3 Pages',\n    next_3: 'Next 3 Pages',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Select date',\n      yearPlaceholder: 'Select year',\n      quarterPlaceholder: 'Select quarter',\n      monthPlaceholder: 'Select month',\n      weekPlaceholder: 'Select week',\n      rangePlaceholder: ['Start date', 'End date'],\n      rangeYearPlaceholder: ['Start year', 'End year'],\n      rangeMonthPlaceholder: ['Start month', 'End month'],\n      rangeWeekPlaceholder: ['Start week', 'End week'],\n      locale: 'en_GB',\n      today: 'Today',\n      now: 'Now',\n      backToToday: 'Back to today',\n      ok: 'Ok',\n      clear: 'Clear',\n      month: 'Month',\n      year: 'Year',\n      timeSelect: 'Select time',\n      dateSelect: 'Select date',\n      weekSelect: 'Choose a week',\n      monthSelect: 'Choose a month',\n      yearSelect: 'Choose a year',\n      decadeSelect: 'Choose a decade',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Previous month (PageUp)',\n      nextMonth: 'Next month (PageDown)',\n      previousYear: 'Last year (Control + left)',\n      nextYear: 'Next year (Control + right)',\n      previousDecade: 'Last decade',\n      nextDecade: 'Next decade',\n      previousCentury: 'Last century',\n      nextCentury: 'Next century'\n    },\n    timePickerLocale: {\n      placeholder: 'Select time',\n      rangePlaceholder: ['Start time', 'End time']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Select time',\n    rangePlaceholder: ['Start time', 'End time']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Select date',\n      yearPlaceholder: 'Select year',\n      quarterPlaceholder: 'Select quarter',\n      monthPlaceholder: 'Select month',\n      weekPlaceholder: 'Select week',\n      rangePlaceholder: ['Start date', 'End date'],\n      rangeYearPlaceholder: ['Start year', 'End year'],\n      rangeMonthPlaceholder: ['Start month', 'End month'],\n      rangeWeekPlaceholder: ['Start week', 'End week'],\n      locale: 'en_GB',\n      today: 'Today',\n      now: 'Now',\n      backToToday: 'Back to today',\n      ok: 'Ok',\n      clear: 'Clear',\n      month: 'Month',\n      year: 'Year',\n      timeSelect: 'Select time',\n      dateSelect: 'Select date',\n      weekSelect: 'Choose a week',\n      monthSelect: 'Choose a month',\n      yearSelect: 'Choose a year',\n      decadeSelect: 'Choose a decade',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Previous month (PageUp)',\n      nextMonth: 'Next month (PageDown)',\n      previousYear: 'Last year (Control + left)',\n      nextYear: 'Next year (Control + right)',\n      previousDecade: 'Last decade',\n      nextDecade: 'Next decade',\n      previousCentury: 'Last century',\n      nextCentury: 'Next century'\n    },\n    timePickerLocale: {\n      placeholder: 'Select time',\n      rangePlaceholder: ['Start time', 'End time']\n    }\n  },\n  global: {\n    placeholder: 'Please select'\n  },\n  Table: {\n    filterTitle: 'Filter menu',\n    filterConfirm: 'OK',\n    filterReset: 'Reset',\n    filterEmptyText: 'No filters',\n    emptyText: 'No data',\n    selectAll: 'Select current page',\n    selectInvert: 'Invert current page',\n    selectionAll: 'Select all data',\n    sortTitle: 'Sort',\n    expand: 'Expand row',\n    collapse: 'Collapse row',\n    triggerDesc: 'Click to sort descending',\n    triggerAsc: 'Click to sort ascending',\n    cancelSort: 'Click to cancel sorting',\n    selectNone: 'Clear all data'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Cancel',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Cancel'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Search here',\n    itemUnit: 'item',\n    itemsUnit: 'items',\n    remove: 'Remove',\n    selectCurrent: 'Select current page',\n    removeCurrent: 'Remove current page',\n    selectAll: 'Select all data',\n    removeAll: 'Remove all data',\n    selectInvert: 'Invert current page'\n  },\n  Upload: {\n    uploading: 'Uploading...',\n    removeFile: 'Remove file',\n    uploadError: 'Upload error',\n    previewFile: 'Preview file',\n    downloadFile: 'Download file'\n  },\n  Empty: {\n    description: 'No data'\n  },\n  Icon: {\n    icon: 'icon'\n  },\n  Text: {\n    edit: 'Edit',\n    copy: 'Copy',\n    copied: 'Copied',\n    expand: 'Expand'\n  },\n  PageHeader: {\n    back: 'Back'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar es_ES = {\n  locale: 'es',\n  Pagination: {\n    items_per_page: '/ página',\n    jump_to: 'Ir a',\n    jump_to_confirm: 'confirmar',\n    page: 'Página',\n    prev_page: 'Página anterior',\n    next_page: 'Página siguiente',\n    prev_5: '5 páginas previas',\n    next_5: '5 páginas siguientes',\n    prev_3: '3 páginas previas',\n    next_3: '3 páginas siguientes',\n    page_size: 'tamaño de página'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Seleccionar fecha',\n      yearPlaceholder: 'Seleccionar año',\n      quarterPlaceholder: 'Seleccionar trimestre',\n      monthPlaceholder: 'Seleccionar mes',\n      weekPlaceholder: 'Seleccionar semana',\n      rangePlaceholder: ['Fecha inicial', 'Fecha final'],\n      rangeYearPlaceholder: ['Año inicial', 'Año final'],\n      rangeMonthPlaceholder: ['Mes inicial', 'Mes final'],\n      rangeWeekPlaceholder: ['Semana inicial', 'Semana final'],\n      locale: 'es_ES',\n      today: 'Hoy',\n      now: 'Ahora',\n      backToToday: 'Volver a hoy',\n      ok: 'Aceptar',\n      clear: 'Limpiar',\n      month: 'Mes',\n      year: 'Año',\n      timeSelect: 'Seleccionar hora',\n      dateSelect: 'Seleccionar fecha',\n      weekSelect: 'Elegir una semana',\n      monthSelect: 'Elegir un mes',\n      yearSelect: 'Elegir un año',\n      decadeSelect: 'Elegir una década',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Mes anterior (PageUp)',\n      nextMonth: 'Mes siguiente (PageDown)',\n      previousYear: 'Año anterior (Control + left)',\n      nextYear: 'Año siguiente (Control + right)',\n      previousDecade: 'Década anterior',\n      nextDecade: 'Década siguiente',\n      previousCentury: 'Siglo anterior',\n      nextCentury: 'Siglo siguiente'\n    },\n    timePickerLocale: {\n      placeholder: 'Seleccionar hora',\n      rangePlaceholder: ['Hora inicial', 'Hora final']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Seleccionar hora',\n    rangePlaceholder: ['Hora inicial', 'Hora final']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Seleccionar fecha',\n      yearPlaceholder: 'Seleccionar año',\n      quarterPlaceholder: 'Seleccionar trimestre',\n      monthPlaceholder: 'Seleccionar mes',\n      weekPlaceholder: 'Seleccionar semana',\n      rangePlaceholder: ['Fecha inicial', 'Fecha final'],\n      rangeYearPlaceholder: ['Año inicial', 'Año final'],\n      rangeMonthPlaceholder: ['Mes inicial', 'Mes final'],\n      rangeWeekPlaceholder: ['Semana inicial', 'Semana final'],\n      locale: 'es_ES',\n      today: 'Hoy',\n      now: 'Ahora',\n      backToToday: 'Volver a hoy',\n      ok: 'Aceptar',\n      clear: 'Limpiar',\n      month: 'Mes',\n      year: 'Año',\n      timeSelect: 'Seleccionar hora',\n      dateSelect: 'Seleccionar fecha',\n      weekSelect: 'Elegir una semana',\n      monthSelect: 'Elegir un mes',\n      yearSelect: 'Elegir un año',\n      decadeSelect: 'Elegir una década',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Mes anterior (AvPág)',\n      nextMonth: 'Mes siguiente (RePág)',\n      previousYear: 'Año anterior (Control + izquierda)',\n      nextYear: 'Año siguiente (Control + derecha)',\n      previousDecade: 'Década anterior',\n      nextDecade: 'Década siguiente',\n      previousCentury: 'Siglo anterior',\n      nextCentury: 'Siglo siguiente'\n    },\n    timePickerLocale: {\n      placeholder: 'Seleccionar hora',\n      rangePlaceholder: ['Hora inicial', 'Hora final']\n    }\n  },\n  global: {\n    placeholder: 'Seleccione'\n  },\n  Table: {\n    filterTitle: 'Filtrar menú',\n    filterConfirm: 'Aceptar',\n    filterReset: 'Reiniciar',\n    filterEmptyText: 'Sin filtros',\n    emptyText: 'Sin datos',\n    selectAll: 'Seleccionar todo',\n    selectInvert: 'Invertir selección',\n    selectionAll: 'Seleccionar todos los datos',\n    sortTitle: 'Ordenar',\n    expand: 'Expandir fila',\n    collapse: 'Colapsar fila',\n    triggerDesc: 'Click para ordenar descendentemente',\n    triggerAsc: 'Click para ordenar ascendentemenre',\n    cancelSort: 'Click para cancelar ordenación',\n    filterCheckall: 'Seleccionar todos los filtros',\n    filterSearchPlaceholder: 'Buscar en filtros',\n    selectNone: 'Vaciar todo'\n  },\n  Modal: {\n    okText: 'Aceptar',\n    cancelText: 'Cancelar',\n    justOkText: 'Aceptar'\n  },\n  Popconfirm: {\n    okText: 'Aceptar',\n    cancelText: 'Cancelar'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Buscar aquí',\n    itemUnit: 'elemento',\n    itemsUnit: 'elementos',\n    remove: 'Eliminar',\n    selectCurrent: 'Seleccionar página actual',\n    removeCurrent: 'Eliminar página actual',\n    selectAll: 'Seleccionar todos los datos',\n    removeAll: 'Eliminar todos los datos',\n    selectInvert: 'Invertir página actual'\n  },\n  Upload: {\n    uploading: 'Subiendo...',\n    removeFile: 'Eliminar archivo',\n    uploadError: 'Error al subir el archivo',\n    previewFile: 'Vista previa',\n    downloadFile: 'Descargar archivo'\n  },\n  Empty: {\n    description: 'No hay datos'\n  },\n  Icon: {\n    icon: 'icono'\n  },\n  Text: {\n    edit: 'Editar',\n    copy: 'Copiar',\n    copied: 'Copiado',\n    expand: 'Expandir'\n  },\n  PageHeader: {\n    back: 'Volver'\n  },\n  Image: {\n    preview: 'Previsualización'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar et_EE = {\n  locale: 'et',\n  Pagination: {\n    items_per_page: '/ leheküljel',\n    jump_to: 'Hüppa',\n    jump_to_confirm: 'Kinnitage',\n    page: '',\n    prev_page: 'Eelmine leht',\n    next_page: 'Järgmine leht',\n    prev_5: 'Eelmised 5 lehekülge',\n    next_5: 'Järgmised 5 lehekülge',\n    prev_3: 'Eelmised 3 lehekülge',\n    next_3: 'Järgmised 3 lehekülge',\n    page_size: 'lehe suurus'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Vali kuupäev',\n      rangePlaceholder: ['Algus kuupäev', 'Lõpu kuupäev'],\n      locale: 'et_EE',\n      today: 'Täna',\n      now: 'Praegu',\n      backToToday: 'Tagasi tänase juurde',\n      ok: 'Ok',\n      clear: 'Tühista',\n      month: 'Kuu',\n      year: 'Aasta',\n      timeSelect: 'Vali aeg',\n      dateSelect: 'Vali kuupäev',\n      monthSelect: 'Vali kuu',\n      yearSelect: 'Vali aasta',\n      decadeSelect: 'Vali dekaad',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Eelmine kuu (PageUp)',\n      nextMonth: 'Järgmine kuu (PageDown)',\n      previousYear: 'Eelmine aasta (Control + left)',\n      nextYear: 'Järgmine aasta (Control + right)',\n      previousDecade: 'Eelmine dekaad',\n      nextDecade: 'Järgmine dekaad',\n      previousCentury: 'Eelmine sajand',\n      nextCentury: 'Järgmine sajand'\n    },\n    timePickerLocale: {\n      placeholder: 'Vali aeg'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Vali aeg'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Vali kuupäev',\n      rangePlaceholder: ['Algus kuupäev', 'Lõpu kuupäev'],\n      locale: 'et_EE',\n      today: 'Täna',\n      now: 'Praegu',\n      backToToday: 'Tagasi tänase juurde',\n      ok: 'Ok',\n      clear: 'Tühista',\n      month: 'Kuu',\n      year: 'Aasta',\n      timeSelect: 'Vali aeg',\n      dateSelect: 'Vali kuupäev',\n      monthSelect: 'Vali kuu',\n      yearSelect: 'Vali aasta',\n      decadeSelect: 'Vali dekaad',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Eelmine kuu (PageUp)',\n      nextMonth: 'Järgmine kuu (PageDown)',\n      previousYear: 'Eelmine aasta (Control + left)',\n      nextYear: 'Järgmine aasta (Control + right)',\n      previousDecade: 'Eelmine dekaad',\n      nextDecade: 'Järgmine dekaad',\n      previousCentury: 'Eelmine sajand',\n      nextCentury: 'Järgmine sajand'\n    },\n    timePickerLocale: {\n      placeholder: 'Vali aeg'\n    }\n  },\n  Table: {\n    filterTitle: 'Filtri menüü',\n    filterConfirm: 'OK',\n    filterReset: 'Nulli',\n    selectAll: 'Vali kõik',\n    selectInvert: 'Inverteeri valik',\n    filterEmptyText: 'Filtreid pole',\n    filterCheckall: 'Vali kõik',\n    filterSearchPlaceholder: 'Otsi filtritest',\n    emptyText: 'Andmed puuduvad',\n    selectNone: 'Kustuta kõik andmed',\n    selectionAll: 'Vali kõik andmed',\n    sortTitle: 'Sorteeri',\n    expand: 'Laienda rida',\n    collapse: 'Ahenda rida',\n    triggerDesc: 'Klõpsa kahanevalt sortimiseks',\n    triggerAsc: 'Klõpsa kasvavalt sortimiseks',\n    cancelSort: 'Klõpsa sortimise tühistamiseks'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Tühista',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Tühista'\n  },\n  Transfer: {\n    searchPlaceholder: 'Otsi siit',\n    itemUnit: 'kogus',\n    itemsUnit: 'kogused',\n    titles: ['', ''],\n    remove: 'Eemalda',\n    selectCurrent: 'Vali praegune leht',\n    removeCurrent: 'Eemalda praegune leht',\n    selectAll: 'Vali kõik',\n    removeAll: 'Eemalda kõik andmed',\n    selectInvert: 'Inverteeri valik'\n  },\n  Upload: {\n    uploading: 'Üleslaadimine...',\n    removeFile: 'Eemalda fail',\n    uploadError: 'Üleslaadimise tõrge',\n    previewFile: 'Faili eelvaade',\n    downloadFile: 'Lae fail alla'\n  },\n  Empty: {\n    description: 'Andmed puuduvad'\n  },\n  global: {\n    placeholder: 'Palun vali'\n  },\n  Icon: {\n    icon: 'ikoon'\n  },\n  Text: {\n    edit: 'Muuda',\n    copy: 'Kopeeri',\n    copied: 'Kopeeritud',\n    expand: 'Laienda'\n  },\n  PageHeader: {\n    back: 'Tagasi'\n  },\n  Image: {\n    preview: 'Eelvaade'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar fa_IR = {\n  locale: 'fa',\n  Pagination: {\n    items_per_page: '/ صفحه',\n    jump_to: 'برو به',\n    jump_to_confirm: 'تایید',\n    page: '',\n    prev_page: 'صفحه قبلی',\n    next_page: 'صفحه بعدی',\n    prev_5: '۵ صفحه قبلی',\n    next_5: '۵ صفحه بعدی',\n    prev_3: '۳ صفحه قبلی',\n    next_3: '۳ صفحه بعدی',\n    page_size: 'اندازه صفحه'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'انتخاب تاریخ',\n      yearPlaceholder: 'انتخاب سال',\n      quarterPlaceholder: 'انتخاب فصل',\n      monthPlaceholder: 'انتخاب ماه',\n      weekPlaceholder: 'انتخاب هفته',\n      rangePlaceholder: ['تاریخ شروع', 'تاریخ پایان'],\n      rangeYearPlaceholder: ['سال شروع', 'سال پایان'],\n      rangeMonthPlaceholder: ['ماه شروع', 'ماه پایان'],\n      rangeWeekPlaceholder: ['هفته شروع', 'هفته پایان'],\n      locale: 'fa_IR',\n      today: 'امروز',\n      now: 'اکنون',\n      backToToday: 'بازگشت به روز',\n      ok: 'باشه',\n      clear: 'پاک کردن',\n      month: 'ماه',\n      year: 'سال',\n      timeSelect: 'انتخاب زمان',\n      dateSelect: 'انتخاب تاریخ',\n      weekSelect: 'یک هفته رو انتخاب کنید',\n      monthSelect: 'یک ماه را انتخاب کنید',\n      yearSelect: 'یک سال را انتخاب کنید',\n      decadeSelect: 'یک دهه را انتخاب کنید',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'ماه قبل (PageUp)',\n      nextMonth: 'ماه بعد (PageDown)',\n      previousYear: 'سال قبل (Control + left)',\n      nextYear: 'سال بعد (Control + right)',\n      previousDecade: 'دهه قبل',\n      nextDecade: 'دهه بعد',\n      previousCentury: 'قرن قبل',\n      nextCentury: 'قرن بعد'\n    },\n    timePickerLocale: {\n      placeholder: 'انتخاب زمان',\n      rangePlaceholder: ['زمان شروع', 'زمان پایان']\n    }\n  },\n  TimePicker: {\n    placeholder: 'انتخاب زمان',\n    rangePlaceholder: ['زمان شروع', 'زمان پایان']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'انتخاب تاریخ',\n      yearPlaceholder: 'انتخاب سال',\n      quarterPlaceholder: 'انتخاب فصل',\n      monthPlaceholder: 'انتخاب ماه',\n      weekPlaceholder: 'انتخاب هفته',\n      rangePlaceholder: ['تاریخ شروع', 'تاریخ پایان'],\n      rangeYearPlaceholder: ['سال شروع', 'سال پایان'],\n      rangeMonthPlaceholder: ['ماه شروع', 'ماه پایان'],\n      rangeWeekPlaceholder: ['هفته شروع', 'هفته پایان'],\n      locale: 'fa_IR',\n      today: 'امروز',\n      now: 'اکنون',\n      backToToday: 'بازگشت به روز',\n      ok: 'باشه',\n      clear: 'پاک کردن',\n      month: 'ماه',\n      year: 'سال',\n      timeSelect: 'انتخاب زمان',\n      dateSelect: 'انتخاب تاریخ',\n      weekSelect: 'انتخاب هفته',\n      monthSelect: 'یک ماه را انتخاب کنید',\n      yearSelect: 'یک سال را انتخاب کنید',\n      decadeSelect: 'یک دهه را انتخاب کنید',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'ماه قبل (PageUp)',\n      nextMonth: 'ماه بعد (PageDown)',\n      previousYear: 'سال قبل (Control + left)',\n      nextYear: 'سال بعد (Control + right)',\n      previousDecade: 'دهه قبل',\n      nextDecade: 'دهه بعد',\n      previousCentury: 'قرن قبل',\n      nextCentury: 'قرن بعد'\n    },\n    timePickerLocale: {\n      placeholder: 'انتخاب زمان',\n      rangePlaceholder: ['زمان شروع', 'زمان پایان']\n    }\n  },\n  global: {\n    placeholder: 'لطفا انتخاب کنید'\n  },\n  Table: {\n    filterTitle: 'منوی فیلتر',\n    filterConfirm: 'تایید',\n    filterReset: 'پاک کردن',\n    filterEmptyText: 'بدون فیلتر',\n    emptyText: 'بدون داده',\n    selectAll: 'انتخاب صفحه‌ی کنونی',\n    selectInvert: 'معکوس کردن انتخاب‌ها در صفحه ی کنونی',\n    selectionAll: 'انتخاب همه داده‌ها',\n    sortTitle: 'مرتب سازی',\n    expand: 'باز شدن ردیف',\n    collapse: 'بستن ردیف',\n    triggerDesc: 'ترتیب نزولی',\n    triggerAsc: 'ترتیب صعودی',\n    cancelSort: 'لغوِ ترتیبِ داده شده',\n    filterCheckall: 'انتخاب همه موارد',\n    filterSearchPlaceholder: 'جست‌و‌جو در فیلتر‌ها',\n    selectNone: 'انتخاب هیچکدام'\n  },\n  Modal: {\n    okText: 'تایید',\n    cancelText: 'لغو',\n    justOkText: 'تایید'\n  },\n  Popconfirm: {\n    okText: 'تایید',\n    cancelText: 'لغو'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'جستجو',\n    itemUnit: 'عدد',\n    itemsUnit: 'عدد',\n    remove: 'حذف',\n    selectCurrent: 'انتخاب صفحه فعلی',\n    removeCurrent: 'پاک کردن انتخاب‌های صفحه فعلی',\n    selectAll: 'انتخاب همه',\n    removeAll: 'پاک کردن همه انتخاب‌ها',\n    selectInvert: 'معکوس کردن انتخاب‌ها در صفحه ی کنونی'\n  },\n  Upload: {\n    uploading: 'در حال آپلود...',\n    removeFile: 'حذف فایل',\n    uploadError: 'خطا در آپلود',\n    previewFile: 'مشاهده‌ی فایل',\n    downloadFile: 'دریافت فایل'\n  },\n  Empty: {\n    description: 'داده‌ای موجود نیست'\n  },\n  Icon: {\n    icon: 'آیکن'\n  },\n  Text: {\n    edit: 'ویرایش',\n    copy: 'کپی',\n    copied: 'کپی شد',\n    expand: 'توسعه'\n  },\n  PageHeader: {\n    back: 'برگشت'\n  },\n  Image: {\n    preview: 'نمایش'\n  },\n  CronExpression: {\n    cronError: 'Invalid cron expression',\n    second: 'ثانیه',\n    minute: 'دقیقه',\n    hour: 'ساعت',\n    day: 'روز',\n    month: 'ماه',\n    week: 'هفته'\n  },\n  QRCode: {\n    expired: 'کد QR منقضی شده است',\n    refresh: 'تازه کردن'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar fi_FI = {\n  locale: 'fi',\n  Pagination: {\n    items_per_page: '/ sivu',\n    jump_to: 'Mene',\n    jump_to_confirm: 'Potvrdite',\n    page: 'Sivu',\n    prev_page: 'Edellinen sivu',\n    next_page: 'Seuraava sivu',\n    prev_5: 'Edelliset 5 sivua',\n    next_5: 'Seuraavat 5 sivua',\n    prev_3: 'Edelliset 3 sivua',\n    next_3: 'Seuraavat 3 sivua',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Valitse päivä',\n      rangePlaceholder: ['Alkamispäivä', 'Päättymispäivä'],\n      locale: 'fi_FI',\n      today: 'Tänään',\n      now: 'Nyt',\n      backToToday: 'Tämä päivä',\n      ok: 'Ok',\n      clear: 'Tyhjennä',\n      month: 'Kuukausi',\n      year: 'Vuosi',\n      timeSelect: 'Valise aika',\n      dateSelect: 'Valitse päivä',\n      monthSelect: 'Valitse kuukausi',\n      yearSelect: 'Valitse vuosi',\n      decadeSelect: 'Valitse vuosikymmen',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Edellinen kuukausi (PageUp)',\n      nextMonth: 'Seuraava kuukausi (PageDown)',\n      previousYear: 'Edellinen vuosi (Control + left)',\n      nextYear: 'Seuraava vuosi (Control + right)',\n      previousDecade: 'Edellinen vuosikymmen',\n      nextDecade: 'Seuraava vuosikymmen',\n      previousCentury: 'Edellinen vuosisata',\n      nextCentury: 'Seuraava vuosisata'\n    },\n    timePickerLocale: {\n      placeholder: 'Valitse aika'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Valitse aika'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Valitse päivä',\n      rangePlaceholder: ['Alkamispäivä', 'Päättymispäivä'],\n      locale: 'fi_FI',\n      today: 'Tänään',\n      now: 'Nyt',\n      backToToday: 'Tämä päivä',\n      ok: 'Ok',\n      clear: 'Tyhjennä',\n      month: 'Kuukausi',\n      year: 'Vuosi',\n      timeSelect: 'Valise aika',\n      dateSelect: 'Valitse päivä',\n      monthSelect: 'Valitse kuukausi',\n      yearSelect: 'Valitse vuosi',\n      decadeSelect: 'Valitse vuosikymmen',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Edellinen kuukausi (PageUp)',\n      nextMonth: 'Seuraava kuukausi (PageDown)',\n      previousYear: 'Edellinen vuosi (Control + left)',\n      nextYear: 'Seuraava vuosi (Control + right)',\n      previousDecade: 'Edellinen vuosikymmen',\n      nextDecade: 'Seuraava vuosikymmen',\n      previousCentury: 'Edellinen vuosisata',\n      nextCentury: 'Seuraava vuosisata'\n    },\n    timePickerLocale: {\n      placeholder: 'Valitse aika'\n    }\n  },\n  Table: {\n    filterTitle: 'Suodatus valikko',\n    filterConfirm: 'OK',\n    filterReset: 'Tyhjennä',\n    selectAll: 'Valitse kaikki',\n    selectInvert: 'Valitse päinvastoin',\n    sortTitle: 'Lajittele',\n    triggerDesc: 'Lajittele laskevasti',\n    triggerAsc: 'Lajittele nousevasti',\n    cancelSort: 'Peruuta lajittelu'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Peruuta',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Peruuta'\n  },\n  Transfer: {\n    searchPlaceholder: 'Etsi täältä',\n    itemUnit: 'kohde',\n    itemsUnit: 'kohdetta'\n  },\n  Upload: {\n    uploading: 'Lähetetään...',\n    removeFile: 'Poista tiedosto',\n    uploadError: 'Virhe lähetyksessä',\n    previewFile: 'Esikatsele tiedostoa',\n    downloadFile: 'Lataa tiedosto'\n  },\n  Empty: {\n    description: 'Ei kohteita'\n  },\n  Text: {\n    edit: 'Muokkaa',\n    copy: 'Kopioi',\n    copied: 'Kopioitu',\n    expand: 'Näytä lisää'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar fr_BE = {\n  locale: 'fr',\n  Pagination: {\n    items_per_page: '/ page',\n    jump_to: 'Aller à',\n    jump_to_confirm: 'confirmer',\n    page: 'Page',\n    prev_page: 'Page précédente',\n    next_page: 'Page suivante',\n    prev_5: '5 Pages précédentes',\n    next_5: '5 Pages suivantes',\n    prev_3: '3 Pages précédentes',\n    next_3: '3 Pages suivantes',\n    page_size: 'taille de la page'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Sélectionner une date',\n      yearPlaceholder: 'Sélectionner une année',\n      quarterPlaceholder: 'Sélectionner un trimestre',\n      monthPlaceholder: 'Sélectionner un mois',\n      weekPlaceholder: 'Sélectionner une semaine',\n      rangePlaceholder: ['Date de début', 'Date de fin'],\n      rangeYearPlaceholder: ['Année de début', 'Année de fin'],\n      rangeMonthPlaceholder: ['Mois de début', 'Mois de fin'],\n      rangeWeekPlaceholder: ['Semaine de début', 'Semaine de fin'],\n      locale: 'fr_BE',\n      today: \"Aujourd'hui\",\n      now: 'Maintenant',\n      backToToday: \"Aujourd'hui\",\n      ok: 'Ok',\n      clear: 'Rétablir',\n      month: 'Mois',\n      year: 'Année',\n      timeSelect: \"Sélectionner l'heure\",\n      dateSelect: 'Sélectionner la date',\n      weekSelect: 'Choisissez une semaine',\n      monthSelect: 'Choisissez un mois',\n      yearSelect: 'Choisissez une année',\n      decadeSelect: 'Choisissez une décennie',\n      yearFormat: 'YYYY',\n      dateFormat: 'DD/MM/YYYY',\n      dayFormat: 'DD',\n      dateTimeFormat: 'DD/MM/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Mois précédent (PageUp)',\n      nextMonth: 'Mois suivant (PageDown)',\n      previousYear: 'Année précédente (Ctrl + gauche)',\n      nextYear: 'Année prochaine (Ctrl + droite)',\n      previousDecade: 'Décennie précédente',\n      nextDecade: 'Décennie suivante',\n      previousCentury: 'Siècle précédent',\n      nextCentury: 'Siècle suivant'\n    },\n    timePickerLocale: {\n      placeholder: \"Sélectionner l'heure\",\n      rangePlaceholder: ['Heure de début', 'Heure de fin']\n    }\n  },\n  TimePicker: {\n    placeholder: \"Sélectionner l'heure\",\n    rangePlaceholder: ['Heure de début', 'Heure de fin']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Sélectionner une date',\n      yearPlaceholder: 'Sélectionner une année',\n      quarterPlaceholder: 'Sélectionner un trimestre',\n      monthPlaceholder: 'Sélectionner un mois',\n      weekPlaceholder: 'Sélectionner une semaine',\n      rangePlaceholder: ['Date de début', 'Date de fin'],\n      rangeYearPlaceholder: ['Année de début', 'Année de fin'],\n      rangeMonthPlaceholder: ['Mois de début', 'Mois de fin'],\n      rangeWeekPlaceholder: ['Semaine de début', 'Semaine de fin'],\n      locale: 'fr_BE',\n      today: \"Aujourd'hui\",\n      now: 'Maintenant',\n      backToToday: \"Aujourd'hui\",\n      ok: 'Ok',\n      clear: 'Rétablir',\n      month: 'Mois',\n      year: 'Année',\n      timeSelect: \"Sélectionner l'heure\",\n      dateSelect: 'Sélectionner la date',\n      monthSelect: 'Choisissez un mois',\n      yearSelect: 'Choisissez une année',\n      decadeSelect: 'Choisissez une décennie',\n      yearFormat: 'YYYY',\n      dateFormat: 'DD/MM/YYYY',\n      dayFormat: 'DD',\n      dateTimeFormat: 'DD/MM/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Mois précédent (PageUp)',\n      nextMonth: 'Mois suivant (PageDown)',\n      previousYear: 'Année précédente (Ctrl + gauche)',\n      nextYear: 'Année prochaine (Ctrl + droite)',\n      previousDecade: 'Décennie précédente',\n      nextDecade: 'Décennie suivante',\n      previousCentury: 'Siècle précédent',\n      nextCentury: 'Siècle suivant'\n    },\n    timePickerLocale: {\n      placeholder: \"Sélectionner l'heure\",\n      rangePlaceholder: ['Heure de début', 'Heure de fin']\n    }\n  },\n  global: {\n    placeholder: 'Sélectionner'\n  },\n  Table: {\n    filterTitle: 'Filtrer',\n    filterConfirm: 'OK',\n    filterReset: 'Réinitialiser',\n    selectAll: 'Sélectionner la page actuelle',\n    selectInvert: 'Inverser la sélection de la page actuelle',\n    selectionAll: 'Sélectionner toutes les données',\n    sortTitle: 'Trier',\n    expand: 'Développer la ligne',\n    collapse: 'Réduire la ligne',\n    triggerDesc: 'Trier par ordre décroissant',\n    triggerAsc: 'Trier par ordre croissant',\n    cancelSort: 'Annuler le tri',\n    filterEmptyText: 'Aucun filtre',\n    emptyText: 'Aucune donnée',\n    selectNone: 'Désélectionner toutes les données'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Annuler',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Annuler'\n  },\n  Transfer: {\n    searchPlaceholder: 'Rechercher',\n    itemUnit: 'élément',\n    itemsUnit: 'éléments',\n    titles: ['', ''],\n    remove: 'Désélectionner',\n    selectCurrent: 'Sélectionner la page actuelle',\n    removeCurrent: 'Désélectionner la page actuelle',\n    selectAll: 'Sélectionner toutes les données',\n    removeAll: 'Désélectionner toutes les données',\n    selectInvert: 'Inverser la sélection de la page actuelle'\n  },\n  Empty: {\n    description: 'Aucune donnée'\n  },\n  Upload: {\n    uploading: 'Téléchargement...',\n    removeFile: 'Effacer le fichier',\n    uploadError: 'Erreur de téléchargement',\n    previewFile: 'Fichier de prévisualisation',\n    downloadFile: 'Télécharger un fichier'\n  },\n  Text: {\n    edit: 'Éditer',\n    copy: 'Copier',\n    copied: 'Copie effectuée',\n    expand: 'Développer'\n  },\n  PageHeader: {\n    back: 'Retour'\n  },\n  Icon: {\n    icon: 'icône'\n  },\n  Image: {\n    preview: 'Aperçu'\n  },\n  CronExpression: {\n    cronError: 'Expression CRON invalide',\n    second: 'seconde',\n    minute: 'minute',\n    hour: 'heure',\n    day: 'jour',\n    month: 'mois',\n    week: 'semaine'\n  },\n  QRCode: {\n    expired: 'QR code expiré',\n    refresh: 'Rafraîchir'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar fr_CA = {\n  locale: 'fr',\n  Pagination: {\n    items_per_page: '/ page',\n    jump_to: 'Aller à',\n    jump_to_confirm: 'confirmer',\n    page: 'Page',\n    prev_page: 'Page précédente',\n    next_page: 'Page suivante',\n    prev_5: '5 Pages précédentes',\n    next_5: '5 Pages suivantes',\n    prev_3: '3 Pages précédentes',\n    next_3: '3 Pages suivantes',\n    page_size: 'taille de la page'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Sélectionner une date',\n      yearPlaceholder: 'Sélectionner une année',\n      quarterPlaceholder: 'Sélectionner un trimestre',\n      monthPlaceholder: 'Sélectionner un mois',\n      weekPlaceholder: 'Sélectionner une semaine',\n      rangePlaceholder: ['Date de début', 'Date de fin'],\n      rangeYearPlaceholder: ['Année de début', 'Année de fin'],\n      rangeMonthPlaceholder: ['Mois de début', 'Mois de fin'],\n      rangeWeekPlaceholder: ['Semaine de début', 'Semaine de fin'],\n      locale: 'fr_CA',\n      today: \"Aujourd'hui\",\n      now: 'Maintenant',\n      backToToday: \"Aujourd'hui\",\n      ok: 'Ok',\n      clear: 'Rétablir',\n      month: 'Mois',\n      year: 'Année',\n      timeSelect: \"Sélectionner l'heure\",\n      dateSelect: 'Sélectionner la date',\n      weekSelect: 'Choisissez une semaine',\n      monthSelect: 'Choisissez un mois',\n      yearSelect: 'Choisissez une année',\n      decadeSelect: 'Choisissez une décennie',\n      yearFormat: 'YYYY',\n      dateFormat: 'DD/MM/YYYY',\n      dayFormat: 'DD',\n      dateTimeFormat: 'DD/MM/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Mois précédent (PageUp)',\n      nextMonth: 'Mois suivant (PageDown)',\n      previousYear: 'Année précédente (Ctrl + gauche)',\n      nextYear: 'Année prochaine (Ctrl + droite)',\n      previousDecade: 'Décennie précédente',\n      nextDecade: 'Décennie suivante',\n      previousCentury: 'Siècle précédent',\n      nextCentury: 'Siècle suivant'\n    },\n    timePickerLocale: {\n      placeholder: \"Sélectionner l'heure\",\n      rangePlaceholder: ['Heure de début', 'Heure de fin']\n    }\n  },\n  TimePicker: {\n    placeholder: \"Sélectionner l'heure\",\n    rangePlaceholder: ['Heure de début', 'Heure de fin']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Sélectionner une date',\n      yearPlaceholder: 'Sélectionner une année',\n      quarterPlaceholder: 'Sélectionner un trimestre',\n      monthPlaceholder: 'Sélectionner un mois',\n      weekPlaceholder: 'Sélectionner une semaine',\n      rangePlaceholder: ['Date de début', 'Date de fin'],\n      rangeYearPlaceholder: ['Année de début', 'Année de fin'],\n      rangeMonthPlaceholder: ['Mois de début', 'Mois de fin'],\n      rangeWeekPlaceholder: ['Semaine de début', 'Semaine de fin'],\n      locale: 'fr_CA',\n      today: \"Aujourd'hui\",\n      now: 'Maintenant',\n      backToToday: \"Aujourd'hui\",\n      ok: 'Ok',\n      clear: 'Rétablir',\n      month: 'Mois',\n      year: 'Année',\n      timeSelect: \"Sélectionner l'heure\",\n      dateSelect: 'Sélectionner la date',\n      monthSelect: 'Choisissez un mois',\n      yearSelect: 'Choisissez une année',\n      decadeSelect: 'Choisissez une décennie',\n      yearFormat: 'YYYY',\n      dateFormat: 'DD/MM/YYYY',\n      dayFormat: 'DD',\n      dateTimeFormat: 'DD/MM/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Mois précédent (PageUp)',\n      nextMonth: 'Mois suivant (PageDown)',\n      previousYear: 'Année précédente (Ctrl + gauche)',\n      nextYear: 'Année prochaine (Ctrl + droite)',\n      previousDecade: 'Décennie précédente',\n      nextDecade: 'Décennie suivante',\n      previousCentury: 'Siècle précédent',\n      nextCentury: 'Siècle suivant'\n    },\n    timePickerLocale: {\n      placeholder: \"Sélectionner l'heure\",\n      rangePlaceholder: ['Heure de début', 'Heure de fin']\n    }\n  },\n  global: {\n    placeholder: 'Sélectionner'\n  },\n  Table: {\n    filterTitle: 'Filtrer',\n    filterConfirm: 'OK',\n    filterReset: 'Réinitialiser',\n    selectAll: 'Sélectionner la page actuelle',\n    selectInvert: 'Inverser la sélection de la page actuelle',\n    selectionAll: 'Sélectionner toutes les données',\n    sortTitle: 'Trier',\n    expand: 'Développer la ligne',\n    collapse: 'Réduire la ligne',\n    triggerDesc: 'Trier par ordre décroissant',\n    triggerAsc: 'Trier par ordre croissant',\n    cancelSort: 'Annuler le tri',\n    filterEmptyText: 'Aucun filtre',\n    emptyText: 'Aucune donnée',\n    selectNone: 'Désélectionner toutes les données'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Annuler',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Annuler'\n  },\n  Transfer: {\n    searchPlaceholder: 'Rechercher',\n    itemUnit: 'élément',\n    itemsUnit: 'éléments',\n    titles: ['', ''],\n    remove: 'Désélectionner',\n    selectCurrent: 'Sélectionner la page actuelle',\n    removeCurrent: 'Désélectionner la page actuelle',\n    selectAll: 'Sélectionner toutes les données',\n    removeAll: 'Désélectionner toutes les données',\n    selectInvert: 'Inverser la sélection de la page actuelle'\n  },\n  Empty: {\n    description: 'Aucune donnée'\n  },\n  Upload: {\n    uploading: 'Téléchargement...',\n    removeFile: 'Effacer le fichier',\n    uploadError: 'Erreur de téléchargement',\n    previewFile: 'Fichier de prévisualisation',\n    downloadFile: 'Télécharger un fichier'\n  },\n  Text: {\n    edit: 'Éditer',\n    copy: 'Copier',\n    copied: 'Copie effectuée',\n    expand: 'Développer'\n  },\n  PageHeader: {\n    back: 'Retour'\n  },\n  Icon: {\n    icon: 'icône'\n  },\n  Image: {\n    preview: 'Aperçu'\n  },\n  CronExpression: {\n    cronError: 'Expression CRON invalide',\n    second: 'seconde',\n    minute: 'minute',\n    hour: 'heure',\n    day: 'jour',\n    month: 'mois',\n    week: 'semaine'\n  },\n  QRCode: {\n    expired: 'QR code expiré',\n    refresh: 'Rafraîchir'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar fr_FR = {\n  locale: 'fr',\n  Pagination: {\n    items_per_page: '/ page',\n    jump_to: 'Aller à',\n    jump_to_confirm: 'confirmer',\n    page: 'Page',\n    prev_page: 'Page précédente',\n    next_page: 'Page suivante',\n    prev_5: '5 Pages précédentes',\n    next_5: '5 Pages suivantes',\n    prev_3: '3 Pages précédentes',\n    next_3: '3 Pages suivantes',\n    page_size: 'taille de la page'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Sélectionner une date',\n      yearPlaceholder: 'Sélectionner une année',\n      quarterPlaceholder: 'Sélectionner un trimestre',\n      monthPlaceholder: 'Sélectionner un mois',\n      weekPlaceholder: 'Sélectionner une semaine',\n      rangePlaceholder: ['Date de début', 'Date de fin'],\n      rangeYearPlaceholder: ['Année de début', 'Année de fin'],\n      rangeMonthPlaceholder: ['Mois de début', 'Mois de fin'],\n      rangeWeekPlaceholder: ['Semaine de début', 'Semaine de fin'],\n      locale: 'fr_FR',\n      today: \"Aujourd'hui\",\n      now: 'Maintenant',\n      backToToday: \"Aujourd'hui\",\n      ok: 'Ok',\n      clear: 'Rétablir',\n      month: 'Mois',\n      year: 'Année',\n      timeSelect: \"Sélectionner l'heure\",\n      dateSelect: 'Sélectionner la date',\n      weekSelect: 'Choisissez une semaine',\n      monthSelect: 'Choisissez un mois',\n      yearSelect: 'Choisissez une année',\n      decadeSelect: 'Choisissez une décennie',\n      yearFormat: 'YYYY',\n      dateFormat: 'DD/MM/YYYY',\n      dayFormat: 'DD',\n      dateTimeFormat: 'DD/MM/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Mois précédent (PageUp)',\n      nextMonth: 'Mois suivant (PageDown)',\n      previousYear: 'Année précédente (Ctrl + gauche)',\n      nextYear: 'Année prochaine (Ctrl + droite)',\n      previousDecade: 'Décennie précédente',\n      nextDecade: 'Décennie suivante',\n      previousCentury: 'Siècle précédent',\n      nextCentury: 'Siècle suivant'\n    },\n    timePickerLocale: {\n      placeholder: \"Sélectionner l'heure\",\n      rangePlaceholder: ['Heure de début', 'Heure de fin']\n    }\n  },\n  TimePicker: {\n    placeholder: \"Sélectionner l'heure\",\n    rangePlaceholder: ['Heure de début', 'Heure de fin']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Sélectionner une date',\n      yearPlaceholder: 'Sélectionner une année',\n      quarterPlaceholder: 'Sélectionner un trimestre',\n      monthPlaceholder: 'Sélectionner un mois',\n      weekPlaceholder: 'Sélectionner une semaine',\n      rangePlaceholder: ['Date de début', 'Date de fin'],\n      rangeYearPlaceholder: ['Année de début', 'Année de fin'],\n      rangeMonthPlaceholder: ['Mois de début', 'Mois de fin'],\n      rangeWeekPlaceholder: ['Semaine de début', 'Semaine de fin'],\n      locale: 'fr_FR',\n      today: \"Aujourd'hui\",\n      now: 'Maintenant',\n      backToToday: \"Aujourd'hui\",\n      ok: 'Ok',\n      clear: 'Rétablir',\n      month: 'Mois',\n      year: 'Année',\n      timeSelect: \"Sélectionner l'heure\",\n      dateSelect: 'Sélectionner la date',\n      monthSelect: 'Choisissez un mois',\n      yearSelect: 'Choisissez une année',\n      decadeSelect: 'Choisissez une décennie',\n      yearFormat: 'YYYY',\n      dateFormat: 'DD/MM/YYYY',\n      dayFormat: 'DD',\n      dateTimeFormat: 'DD/MM/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Mois précédent (PageUp)',\n      nextMonth: 'Mois suivant (PageDown)',\n      previousYear: 'Année précédente (Ctrl + gauche)',\n      nextYear: 'Année prochaine (Ctrl + droite)',\n      previousDecade: 'Décennie précédente',\n      nextDecade: 'Décennie suivante',\n      previousCentury: 'Siècle précédent',\n      nextCentury: 'Siècle suivant'\n    },\n    timePickerLocale: {\n      placeholder: \"Sélectionner l'heure\",\n      rangePlaceholder: ['Heure de début', 'Heure de fin']\n    }\n  },\n  global: {\n    placeholder: 'Sélectionner'\n  },\n  Table: {\n    filterTitle: 'Filtrer',\n    filterConfirm: 'OK',\n    filterReset: 'Réinitialiser',\n    selectAll: 'Sélectionner la page actuelle',\n    selectInvert: 'Inverser la sélection de la page actuelle',\n    selectionAll: 'Sélectionner toutes les données',\n    sortTitle: 'Trier',\n    expand: 'Développer la ligne',\n    collapse: 'Réduire la ligne',\n    triggerDesc: 'Trier par ordre décroissant',\n    triggerAsc: 'Trier par ordre croissant',\n    cancelSort: 'Annuler le tri',\n    filterEmptyText: 'Aucun filtre',\n    emptyText: 'Aucune donnée',\n    selectNone: 'Désélectionner toutes les données'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Annuler',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Annuler'\n  },\n  Transfer: {\n    searchPlaceholder: 'Rechercher',\n    itemUnit: 'élément',\n    itemsUnit: 'éléments',\n    titles: ['', ''],\n    remove: 'Désélectionner',\n    selectCurrent: 'Sélectionner la page actuelle',\n    removeCurrent: 'Désélectionner la page actuelle',\n    selectAll: 'Sélectionner toutes les données',\n    removeAll: 'Désélectionner toutes les données',\n    selectInvert: 'Inverser la sélection de la page actuelle'\n  },\n  Empty: {\n    description: 'Aucune donnée'\n  },\n  Upload: {\n    uploading: 'Téléchargement...',\n    removeFile: 'Effacer le fichier',\n    uploadError: 'Erreur de téléchargement',\n    previewFile: 'Fichier de prévisualisation',\n    downloadFile: 'Télécharger un fichier'\n  },\n  Text: {\n    edit: 'Éditer',\n    copy: 'Copier',\n    copied: 'Copie effectuée',\n    expand: 'Développer'\n  },\n  PageHeader: {\n    back: 'Retour'\n  },\n  Icon: {\n    icon: 'icône'\n  },\n  Image: {\n    preview: 'Aperçu'\n  },\n  CronExpression: {\n    cronError: 'Expression CRON invalide',\n    second: 'seconde',\n    minute: 'minute',\n    hour: 'heure',\n    day: 'jour',\n    month: 'mois',\n    week: 'semaine'\n  },\n  QRCode: {\n    expired: 'QR code expiré',\n    refresh: 'Rafraîchir'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ga_IE = {\n  locale: 'ga',\n  Pagination: {\n    items_per_page: '/ leathanach',\n    jump_to: 'Téigh',\n    jump_to_confirm: 'dheimhnigh',\n    page: '',\n    prev_page: 'Leathanach Roimhe Seo',\n    next_page: 'An chéad leathanach eile',\n    prev_5: '5 leathanach roimhe seo',\n    next_5: 'Ar Aghaidh 5 Leathanaigh',\n    prev_3: '3 leathanach roimhe seo',\n    next_3: 'Ar Aghaidh 3 Leathanaigh',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Roghnaigh dáta',\n      yearPlaceholder: 'Roghnaigh bliain',\n      quarterPlaceholder: 'Roghnaigh ráithe',\n      monthPlaceholder: 'Roghnaigh mí',\n      weekPlaceholder: 'Roghnaigh seachtain',\n      rangePlaceholder: ['Dáta tosaigh', 'Dáta deiridh'],\n      rangeYearPlaceholder: ['Tús na bliana', 'Deireadh na bliana'],\n      rangeMonthPlaceholder: ['Tosaigh mhí', 'Deireadh mhí'],\n      rangeWeekPlaceholder: ['Tosaigh an tseachtain', 'Deireadh na seachtaine'],\n      locale: 'ga_IE',\n      today: 'inniu',\n      now: 'anois',\n      backToToday: 'Ar ais inniu',\n      ok: 'ceart go leor',\n      clear: 'soiléir',\n      month: 'mhí',\n      year: 'bhliain',\n      timeSelect: 'roghnaigh am',\n      dateSelect: 'roghnaigh dáta',\n      weekSelect: 'Roghnaigh seachtain',\n      monthSelect: 'Roghnaigh mí',\n      yearSelect: 'Roghnaigh bliain',\n      decadeSelect: 'Roghnaigh deich mbliana',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'An mhí roimhe seo (PageUp)',\n      nextMonth: 'An mhí seo chugainn (PageDown)',\n      previousYear: 'Anuraidh (Control + left)',\n      nextYear: 'An bhliain seo chugainn (Control + right)',\n      previousDecade: 'Le deich mbliana anuas',\n      nextDecade: 'Deich mbliana amach romhainn',\n      previousCentury: 'An chéid seo caite',\n      nextCentury: 'An chéad aois eile'\n    },\n    timePickerLocale: {\n      placeholder: 'Roghnaigh am',\n      rangePlaceholder: ['Am tosaigh', 'Am deiridh']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Roghnaigh am',\n    rangePlaceholder: ['Am tosaigh', 'Am deiridh']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Roghnaigh dáta',\n      yearPlaceholder: 'Roghnaigh bliain',\n      quarterPlaceholder: 'Roghnaigh ráithe',\n      monthPlaceholder: 'Roghnaigh mí',\n      weekPlaceholder: 'Roghnaigh seachtain',\n      rangePlaceholder: ['Dáta tosaigh', 'Dáta deiridh'],\n      rangeYearPlaceholder: ['Tús na bliana', 'Deireadh na bliana'],\n      rangeMonthPlaceholder: ['Tosaigh mhí', 'Deireadh mhí'],\n      rangeWeekPlaceholder: ['Tosaigh an tseachtain', 'Deireadh na seachtaine'],\n      locale: 'ga_IE',\n      today: 'inniu',\n      now: 'anois',\n      backToToday: 'Ar ais inniu',\n      ok: 'ceart go leor',\n      clear: 'soiléir',\n      month: 'mhí',\n      year: 'bhliain',\n      timeSelect: 'roghnaigh am',\n      dateSelect: 'roghnaigh dáta',\n      weekSelect: 'Roghnaigh seachtain',\n      monthSelect: 'Roghnaigh mí',\n      yearSelect: 'Roghnaigh bliain',\n      decadeSelect: 'Roghnaigh deich mbliana',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'An mhí roimhe seo (PageUp)',\n      nextMonth: 'An mhí seo chugainn (PageDown)',\n      previousYear: 'Anuraidh (Control + left)',\n      nextYear: 'An bhliain seo chugainn (Control + right)',\n      previousDecade: 'Le deich mbliana anuas',\n      nextDecade: 'Deich mbliana amach romhainn',\n      previousCentury: 'An chéid seo caite',\n      nextCentury: 'An chéad aois eile'\n    },\n    timePickerLocale: {\n      placeholder: 'Roghnaigh am',\n      rangePlaceholder: ['Am tosaigh', 'Am deiridh']\n    }\n  },\n  global: {\n    placeholder: 'Please select'\n  },\n  Table: {\n    filterTitle: 'Filter menu',\n    filterConfirm: 'OK',\n    filterReset: 'Reset',\n    selectAll: 'Select current page',\n    selectInvert: 'Invert current page',\n    selectionAll: 'Select all data',\n    sortTitle: 'Sort',\n    expand: 'Expand row',\n    collapse: 'Collapse row',\n    triggerDesc: 'Click to sort descending',\n    triggerAsc: 'Click to sort ascending',\n    cancelSort: 'Click to cancel sorting'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Cancel',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Cancel'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Search here',\n    itemUnit: 'item',\n    itemsUnit: 'items',\n    remove: 'Remove',\n    selectCurrent: 'Select current page',\n    removeCurrent: 'Remove current page',\n    selectAll: 'Select all data',\n    removeAll: 'Remove all data',\n    selectInvert: 'Invert current page'\n  },\n  Upload: {\n    uploading: 'Uploading...',\n    removeFile: 'Remove file',\n    uploadError: 'Upload error',\n    previewFile: 'Preview file',\n    downloadFile: 'Download file'\n  },\n  Empty: {\n    description: 'No Data'\n  },\n  Icon: {\n    icon: 'icon'\n  },\n  Text: {\n    edit: 'Edit',\n    copy: 'Copy',\n    copied: 'Copied',\n    expand: 'Expand'\n  },\n  PageHeader: {\n    back: 'Back'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar gl_ES = {\n  locale: 'gl',\n  Pagination: {\n    items_per_page: '/ páxina',\n    jump_to: 'Ir a',\n    jump_to_confirm: 'confirmar',\n    page: '',\n    prev_page: 'Páxina anterior',\n    next_page: 'Páxina seguinte',\n    prev_5: '5 páxinas previas',\n    next_5: '5 páxinas seguintes',\n    prev_3: '3 páxinas previas',\n    next_3: '3 páxinas seguintes',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Escolla data',\n      rangePlaceholder: ['Data inicial', 'Data final'],\n      locale: 'gl_ES',\n      today: 'Hoxe',\n      now: 'Agora',\n      backToToday: 'Voltar a hoxe',\n      ok: 'Aceptar',\n      clear: 'Limpar',\n      month: 'Mes',\n      year: 'Ano',\n      timeSelect: 'Seleccionar hora',\n      dateSelect: 'Seleccionar data',\n      monthSelect: 'Elexir un mes',\n      yearSelect: 'Elexir un año',\n      decadeSelect: 'Elexir unha década',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Mes anterior (PageUp)',\n      nextMonth: 'Mes seguinte (PageDown)',\n      previousYear: 'Ano anterior (Control + left)',\n      nextYear: 'Ano seguinte (Control + right)',\n      previousDecade: 'Década anterior',\n      nextDecade: 'Década seguinte',\n      previousCentury: 'Século anterior',\n      nextCentury: 'Século seguinte'\n    },\n    timePickerLocale: {\n      placeholder: 'Escolla hora'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Escolla hora'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Escolla data',\n      rangePlaceholder: ['Data inicial', 'Data final'],\n      locale: 'gl_ES',\n      today: 'Hoxe',\n      now: 'Agora',\n      backToToday: 'Voltar a hoxe',\n      ok: 'Aceptar',\n      clear: 'Limpar',\n      month: 'Mes',\n      year: 'Ano',\n      timeSelect: 'Seleccionar hora',\n      dateSelect: 'Seleccionar data',\n      monthSelect: 'Elexir un mes',\n      yearSelect: 'Elexir un año',\n      decadeSelect: 'Elexir unha década',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Mes anterior (PageUp)',\n      nextMonth: 'Mes seguinte (PageDown)',\n      previousYear: 'Ano anterior (Control + left)',\n      nextYear: 'Ano seguinte (Control + right)',\n      previousDecade: 'Década anterior',\n      nextDecade: 'Década seguinte',\n      previousCentury: 'Século anterior',\n      nextCentury: 'Século seguinte'\n    },\n    timePickerLocale: {\n      placeholder: 'Escolla hora'\n    }\n  },\n  global: {\n    placeholder: 'Escolla'\n  },\n  Table: {\n    filterTitle: 'Filtrar menú',\n    filterConfirm: 'Aceptar',\n    filterReset: 'Reiniciar',\n    selectAll: 'Seleccionar todo',\n    selectInvert: 'Invertir selección',\n    sortTitle: 'Ordenar'\n  },\n  Modal: {\n    okText: 'Aceptar',\n    cancelText: 'Cancelar',\n    justOkText: 'Aceptar'\n  },\n  Popconfirm: {\n    okText: 'Aceptar',\n    cancelText: 'Cancelar'\n  },\n  Transfer: {\n    searchPlaceholder: 'Buscar aquí',\n    itemUnit: 'elemento',\n    itemsUnit: 'elementos'\n  },\n  Upload: {\n    uploading: 'Subindo...',\n    removeFile: 'Eliminar arquivo',\n    uploadError: 'Error ao subir o arquivo',\n    previewFile: 'Vista previa',\n    downloadFile: 'Descargar arquivo'\n  },\n  Empty: {\n    description: 'Non hai datos'\n  },\n  Icon: {\n    icon: 'icona'\n  },\n  Text: {\n    edit: 'editar',\n    copy: 'copiar',\n    copied: 'copiado',\n    expand: 'expandir'\n  },\n  PageHeader: {\n    back: 'voltar'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar he_IL = {\n  locale: 'he',\n  Pagination: {\n    items_per_page: '/ עמוד',\n    jump_to: 'עבור אל',\n    jump_to_confirm: 'אישור',\n    page: '',\n    prev_page: 'העמוד הקודם',\n    next_page: 'העמוד הבא',\n    prev_5: '5 עמודים קודמים',\n    next_5: '5 עמודים הבאים',\n    prev_3: '3 עמודים קודמים',\n    next_3: '3 עמודים הבאים',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'בחר תאריך',\n      rangePlaceholder: ['תאריך התחלה', 'תאריך סיום'],\n      locale: 'he_IL',\n      today: 'היום',\n      now: 'עכשיו',\n      backToToday: 'חזור להיום',\n      ok: 'אישור',\n      clear: 'איפוס',\n      month: 'חודש',\n      year: 'שנה',\n      timeSelect: 'בחר שעה',\n      dateSelect: 'בחר תאריך',\n      weekSelect: 'בחר שבוע',\n      monthSelect: 'בחר חודש',\n      yearSelect: 'בחר שנה',\n      decadeSelect: 'בחר עשור',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'חודש קודם (PageUp)',\n      nextMonth: 'חודש הבא (PageDown)',\n      previousYear: 'שנה שעברה (Control + left)',\n      nextYear: 'שנה הבאה (Control + right)',\n      previousDecade: 'העשור הקודם',\n      nextDecade: 'העשור הבא',\n      previousCentury: 'המאה הקודמת',\n      nextCentury: 'המאה הבאה'\n    },\n    timePickerLocale: {\n      placeholder: 'בחר שעה'\n    }\n  },\n  TimePicker: {\n    placeholder: 'בחר שעה'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'בחר תאריך',\n      rangePlaceholder: ['תאריך התחלה', 'תאריך סיום'],\n      locale: 'he_IL',\n      today: 'היום',\n      now: 'עכשיו',\n      backToToday: 'חזור להיום',\n      ok: 'אישור',\n      clear: 'איפוס',\n      month: 'חודש',\n      year: 'שנה',\n      timeSelect: 'בחר שעה',\n      dateSelect: 'בחר תאריך',\n      weekSelect: 'בחר שבוע',\n      monthSelect: 'בחר חודש',\n      yearSelect: 'בחר שנה',\n      decadeSelect: 'בחר עשור',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'חודש קודם (PageUp)',\n      nextMonth: 'חודש הבא (PageDown)',\n      previousYear: 'שנה שעברה (Control + left)',\n      nextYear: 'שנה הבאה (Control + right)',\n      previousDecade: 'העשור הקודם',\n      nextDecade: 'העשור הבא',\n      previousCentury: 'המאה הקודמת',\n      nextCentury: 'המאה הבאה'\n    },\n    timePickerLocale: {\n      placeholder: 'בחר שעה'\n    }\n  },\n  global: {\n    placeholder: 'אנא בחר'\n  },\n  Table: {\n    filterTitle: 'תפריט סינון',\n    filterConfirm: 'אישור',\n    filterReset: 'איפוס',\n    selectAll: 'בחר הכל',\n    selectInvert: 'הפוך בחירה',\n    selectionAll: 'בחר את כל הנתונים',\n    sortTitle: 'מיון',\n    expand: 'הרחב שורה',\n    collapse: 'צמצם שורהw',\n    triggerDesc: 'לחץ על מיון לפי סדר יורד',\n    triggerAsc: 'לחץ על מיון לפי סדר עולה',\n    cancelSort: 'לחץ כדי לבטל את המיון'\n  },\n  Modal: {\n    okText: 'אישור',\n    cancelText: 'ביטול',\n    justOkText: 'אישור'\n  },\n  Popconfirm: {\n    okText: 'אישור',\n    cancelText: 'ביטול'\n  },\n  Transfer: {\n    searchPlaceholder: 'חפש כאן',\n    itemUnit: 'פריט',\n    itemsUnit: 'פריטים'\n  },\n  Upload: {\n    uploading: 'מעלה...',\n    removeFile: 'הסר קובץ',\n    uploadError: 'שגיאת העלאה',\n    previewFile: 'הצג קובץ',\n    downloadFile: 'הורד קובץ'\n  },\n  Empty: {\n    description: 'אין מידע'\n  },\n  Icon: {\n    icon: 'סמל'\n  },\n  Text: {\n    edit: 'ערוך',\n    copy: 'העתק',\n    copied: 'הועתק',\n    expand: 'הרחב'\n  },\n  PageHeader: {\n    back: 'חזרה'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar hi_IN = {\n  locale: 'hi',\n  Pagination: {\n    items_per_page: '/ पृष्ठ',\n    jump_to: 'इस पर चलें',\n    jump_to_confirm: 'पुष्टि करें',\n    page: '',\n    prev_page: 'पिछला पृष्ठ',\n    next_page: 'अगला पृष्ठ',\n    prev_5: 'पिछले 5 पृष्ठ',\n    next_5: 'अगले 5 पृष्ठ',\n    prev_3: 'पिछले 3 पृष्ठ',\n    next_3: 'अगले 3 पेज',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'तारीख़ चुनें',\n      rangePlaceholder: ['प्रारंभ तिथि', 'समाप्ति तिथि'],\n      locale: 'hi_IN',\n      today: 'आज',\n      now: 'अभी',\n      backToToday: 'आज तक',\n      ok: 'ठीक',\n      clear: 'स्पष्ट',\n      month: 'महीना',\n      year: 'साल',\n      timeSelect: 'समय का चयन करें',\n      dateSelect: 'तारीख़ चुनें',\n      weekSelect: 'एक सप्ताह चुनें',\n      monthSelect: 'एक महीना चुनें',\n      yearSelect: 'एक वर्ष चुनें',\n      decadeSelect: 'एक दशक चुनें',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'पिछला महीना (पेजअप)',\n      nextMonth: 'अगले महीने (पेजडाउन)',\n      previousYear: 'पिछले साल (Ctrl + बाएं)',\n      nextYear: 'अगले साल (Ctrl + दाहिना)',\n      previousDecade: 'पिछला दशक',\n      nextDecade: 'अगले दशक',\n      previousCentury: 'पीछ्ली शताब्दी',\n      nextCentury: 'अगली सदी',\n      yearPlaceholder: 'वर्ष चुनें',\n      quarterPlaceholder: 'तिमाही चुनें',\n      monthPlaceholder: 'महीना चुनिए',\n      weekPlaceholder: 'सप्ताह चुनें',\n      rangeYearPlaceholder: ['आरंभिक वर्ष', 'अंत वर्ष'],\n      rangeMonthPlaceholder: ['आरंभिक महीना', 'अंत महीना'],\n      rangeWeekPlaceholder: ['आरंभिक सप्ताह', 'अंत सप्ताह']\n    },\n    timePickerLocale: {\n      placeholder: 'समय का चयन करें',\n      rangePlaceholder: ['आरंभिक समय', 'अंत समय']\n    }\n  },\n  TimePicker: {\n    placeholder: 'समय का चयन करें',\n    rangePlaceholder: ['आरंभिक समय', 'अंत समय']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'तारीख़ चुनें',\n      rangePlaceholder: ['प्रारंभ तिथि', 'समाप्ति तिथि'],\n      locale: 'hi_IN',\n      today: 'आज',\n      now: 'अभी',\n      backToToday: 'आज तक',\n      ok: 'ठीक',\n      clear: 'स्पष्ट',\n      month: 'महीना',\n      year: 'साल',\n      timeSelect: 'समय का चयन करें',\n      dateSelect: 'तारीख़ चुनें',\n      weekSelect: 'एक सप्ताह चुनें',\n      monthSelect: 'एक महीना चुनें',\n      yearSelect: 'एक वर्ष चुनें',\n      decadeSelect: 'एक दशक चुनें',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'पिछला महीना (पेजअप)',\n      nextMonth: 'अगले महीने (पेजडाउन)',\n      previousYear: 'पिछले साल (Ctrl + बाएं)',\n      nextYear: 'अगले साल (Ctrl + दाहिना)',\n      previousDecade: 'पिछला दशक',\n      nextDecade: 'अगले दशक',\n      previousCentury: 'पीछ्ली शताब्दी',\n      nextCentury: 'अगली सदी',\n      yearPlaceholder: 'वर्ष चुनें',\n      quarterPlaceholder: 'तिमाही चुनें',\n      monthPlaceholder: 'महीना चुनिए',\n      weekPlaceholder: 'सप्ताह चुनें',\n      rangeYearPlaceholder: ['आरंभिक वर्ष', 'अंत वर्ष'],\n      rangeMonthPlaceholder: ['आरंभिक महीना', 'अंत महीना'],\n      rangeWeekPlaceholder: ['आरंभिक सप्ताह', 'अंत सप्ताह']\n    },\n    timePickerLocale: {\n      placeholder: 'समय का चयन करें',\n      rangePlaceholder: ['आरंभिक समय', 'अंत समय']\n    }\n  },\n  global: {\n    placeholder: 'कृपया चुनें'\n  },\n  Table: {\n    filterTitle: 'सूची बंद करें',\n    filterConfirm: 'अच्छी तरह से',\n    filterReset: 'रीसेट',\n    emptyText: 'कोई जानकारी नहीं',\n    selectAll: 'वर्तमान पृष्ठ का चयन करें',\n    selectInvert: 'वर्तमान पृष्ठ घुमाएं',\n    sortTitle: 'द्वारा क्रमबद्ध करें',\n    filterEmptyText: 'कोई फ़िल्टर नहीं',\n    selectNone: 'सभी डेटा साफ़ करें',\n    selectionAll: 'सभी डेटा का चयन करें',\n    expand: 'पंक्ति का विस्तार करें',\n    collapse: 'पंक्ति संक्षिप्त करें',\n    triggerDesc: 'अवरोही क्रमित करने के लिए क्लिक करें',\n    triggerAsc: 'आरोही क्रमित करने के लिए क्लिक करें',\n    cancelSort: 'छँटाई रद्द करने के लिए क्लिक करें'\n  },\n  Modal: {\n    okText: 'अच्छी तरह से',\n    cancelText: 'रद्द करना',\n    justOkText: 'अच्छी तरह से'\n  },\n  Popconfirm: {\n    okText: 'अच्छी तरह से',\n    cancelText: 'रद्द करना'\n  },\n  Transfer: {\n    titles: ['', ''],\n    notFoundContent: 'नहीं मिला',\n    searchPlaceholder: 'यहां खोजें',\n    itemUnit: 'तत्त्व',\n    itemsUnit: 'विषय-वस्तु',\n    remove: 'हटाए',\n    selectCurrent: 'वर्तमान पृष्ठ का चयन करें',\n    removeCurrent: 'वर्तमान पृष्ठ हटाएं',\n    selectAll: 'सभी डेटा का चयन करें',\n    removeAll: 'सभी डेटा हटाएं',\n    selectInvert: 'वर्तमान पृष्ठ को उल्टा करें'\n  },\n  Select: {\n    notFoundContent: 'नहीं मिला'\n  },\n  Upload: {\n    uploading: 'अपलोड हो रहा...',\n    removeFile: 'फ़ाइल निकालें',\n    uploadError: 'अपलोड में त्रुटि',\n    previewFile: 'फ़ाइल पूर्वावलोकन',\n    downloadFile: 'फ़ाइल डाउनलोड करें'\n  },\n  Empty: {\n    description: 'कोई आकड़ा उपलब्ध नहीं है'\n  },\n  Icon: {\n    icon: 'आइकन'\n  },\n  Text: {\n    edit: 'संपादित करें',\n    copy: 'प्रतिलिपि',\n    copied: 'कॉपी किया गया',\n    expand: 'विस्तार'\n  },\n  PageHeader: {\n    back: 'वापस'\n  },\n  Image: {\n    preview: 'पूर्वावलोकन'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar hr_HR = {\n  locale: 'hr',\n  Pagination: {\n    items_per_page: '/ str',\n    jump_to: 'Idi na',\n    jump_to_confirm: 'potvrdi',\n    page: '',\n    prev_page: 'Prijašnja stranica',\n    next_page: 'Sljedeća stranica',\n    prev_5: 'Prijašnjih 5 stranica',\n    next_5: 'Sljedećih 5 stranica',\n    prev_3: 'Prijašnje 3 stranice',\n    next_3: 'Sljedeće 3 stranice',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Odaberite datum',\n      rangePlaceholder: ['Početni datum', 'Završni datum'],\n      locale: 'hr_HR',\n      today: 'Danas',\n      now: 'Sad',\n      backToToday: 'Natrag na danas',\n      ok: 'Ok',\n      clear: 'Očisti',\n      month: 'Mjesec',\n      year: 'Godina',\n      timeSelect: 'odaberite vrijeme',\n      dateSelect: 'odaberite datum',\n      weekSelect: 'Odaberite tjedan',\n      monthSelect: 'Odaberite mjesec',\n      yearSelect: 'Odaberite godinu',\n      decadeSelect: 'Odaberite desetljeće',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Prošli mjesec (PageUp)',\n      nextMonth: 'Sljedeći mjesec (PageDown)',\n      previousYear: 'Prošla godina (Control + left)',\n      nextYear: 'Sljedeća godina (Control + right)',\n      previousDecade: 'Prošlo desetljeće',\n      nextDecade: 'Sljedeće desetljeće',\n      previousCentury: 'Prošlo stoljeće',\n      nextCentury: 'Sljedeće stoljeće',\n      yearPlaceholder: 'Odaberite godinu',\n      quarterPlaceholder: 'Odaberite četvrtinu',\n      monthPlaceholder: 'Odaberite mjesec',\n      weekPlaceholder: 'Odaberite tjedan',\n      rangeYearPlaceholder: ['Početna godina', 'Završna godina'],\n      rangeMonthPlaceholder: ['Početni mjesec', 'Završni mjesec'],\n      rangeWeekPlaceholder: ['Početni tjedan', 'Završni tjedan']\n    },\n    timePickerLocale: {\n      placeholder: 'Odaberite vrijeme',\n      rangePlaceholder: ['Vrijeme početka', 'Vrijeme završetka']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Odaberite vrijeme',\n    rangePlaceholder: ['Vrijeme početka', 'Vrijeme završetka']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Odaberite datum',\n      rangePlaceholder: ['Početni datum', 'Završni datum'],\n      locale: 'hr_HR',\n      today: 'Danas',\n      now: 'Sad',\n      backToToday: 'Natrag na danas',\n      ok: 'Ok',\n      clear: 'Očisti',\n      month: 'Mjesec',\n      year: 'Godina',\n      timeSelect: 'odaberite vrijeme',\n      dateSelect: 'odaberite datum',\n      weekSelect: 'Odaberite tjedan',\n      monthSelect: 'Odaberite mjesec',\n      yearSelect: 'Odaberite godinu',\n      decadeSelect: 'Odaberite desetljeće',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Prošli mjesec (PageUp)',\n      nextMonth: 'Sljedeći mjesec (PageDown)',\n      previousYear: 'Prošla godina (Control + left)',\n      nextYear: 'Sljedeća godina (Control + right)',\n      previousDecade: 'Prošlo desetljeće',\n      nextDecade: 'Sljedeće desetljeće',\n      previousCentury: 'Prošlo stoljeće',\n      nextCentury: 'Sljedeće stoljeće',\n      yearPlaceholder: 'Odaberite godinu',\n      quarterPlaceholder: 'Odaberite četvrtinu',\n      monthPlaceholder: 'Odaberite mjesec',\n      weekPlaceholder: 'Odaberite tjedan',\n      rangeYearPlaceholder: ['Početna godina', 'Završna godina'],\n      rangeMonthPlaceholder: ['Početni mjesec', 'Završni mjesec'],\n      rangeWeekPlaceholder: ['Početni tjedan', 'Završni tjedan']\n    },\n    timePickerLocale: {\n      placeholder: 'Odaberite vrijeme',\n      rangePlaceholder: ['Vrijeme početka', 'Vrijeme završetka']\n    }\n  },\n  global: {\n    placeholder: 'Molimo označite'\n  },\n  Table: {\n    filterTitle: 'Filter meni',\n    filterConfirm: 'OK',\n    filterReset: 'Reset',\n    selectAll: 'Označi trenutnu stranicu',\n    selectInvert: 'Invertiraj trenutnu stranicu',\n    sortTitle: 'Sortiraj',\n    filterEmptyText: 'Nema filtera',\n    emptyText: 'Nema podataka',\n    selectionAll: 'Odaberite sve podatke',\n    expand: 'Proširi redak',\n    collapse: 'Sažmi redak',\n    triggerDesc: 'Kliknite za sortiranje silazno',\n    triggerAsc: 'Kliknite za sortiranje uzlazno',\n    cancelSort: 'Kliknite da biste otkazali sortiranje'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Odustani',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Odustani'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Pretraži ovdje',\n    itemUnit: 'stavka',\n    itemsUnit: 'stavke',\n    remove: 'Ukloniti',\n    selectCurrent: 'Odaberite trenutnu stranicu',\n    removeCurrent: 'Ukloni trenutnu stranicu',\n    selectAll: 'Odaberite sve podatke',\n    removeAll: 'Uklonite sve podatke',\n    selectInvert: 'Obrni trenutnu stranicu'\n  },\n  Upload: {\n    uploading: 'Upload u tijeku...',\n    removeFile: 'Makni datoteku',\n    uploadError: 'Greška kod uploada',\n    previewFile: 'Pogledaj datoteku',\n    downloadFile: 'Preuzmi datoteku'\n  },\n  Empty: {\n    description: 'Nema podataka'\n  },\n  Icon: {\n    icon: 'ikona'\n  },\n  Text: {\n    edit: 'Uredi',\n    copy: 'Kopiraj',\n    copied: 'Kopiranje uspješno',\n    expand: 'Proširi'\n  },\n  PageHeader: {\n    back: 'Natrag'\n  },\n  Image: {\n    preview: 'Pregled'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar hu_HU = {\n  locale: 'hu',\n  Pagination: {\n    items_per_page: '/ oldal',\n    jump_to: 'Ugrás',\n    jump_to_confirm: 'megerősít',\n    page: '',\n    prev_page: 'Előző oldal',\n    next_page: 'Következő oldal',\n    prev_5: 'Előző 5 oldal',\n    next_5: 'Következő 5 oldal',\n    prev_3: 'Előző 3 oldal',\n    next_3: 'Következő 3 oldal',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Válasszon dátumot',\n      yearPlaceholder: 'Válasszon évet',\n      quarterPlaceholder: 'Válasszon negyedévet',\n      monthPlaceholder: 'Válasszon hónapot',\n      weekPlaceholder: 'Válasszon hetet',\n      rangePlaceholder: ['Kezdő dátum', 'Befejezés dátuma'],\n      rangeYearPlaceholder: ['Kezdő év', 'Befejezés éve'],\n      rangeMonthPlaceholder: ['Kezdő hónap', 'Befejezés hónapja'],\n      rangeWeekPlaceholder: ['Kezdő hét', 'Befejezés hete'],\n      locale: 'hu_HU',\n      today: 'Ma',\n      now: 'Most',\n      backToToday: 'Vissza a mai napra',\n      ok: 'Ok',\n      clear: 'Törlés',\n      month: 'Hónap',\n      year: 'Év',\n      timeSelect: 'Időpont kiválasztása',\n      dateSelect: 'Dátum kiválasztása',\n      weekSelect: 'Hét kiválasztása',\n      monthSelect: 'Hónap kiválasztása',\n      yearSelect: 'Év kiválasztása',\n      decadeSelect: 'Évtized kiválasztása',\n      yearFormat: 'YYYY',\n      dateFormat: 'YYYY/MM/DD',\n      dayFormat: 'DD',\n      dateTimeFormat: 'YYYY/MM/DD HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Előző hónap (PageUp)',\n      nextMonth: 'Következő hónap (PageDown)',\n      previousYear: 'Múlt év (Control + left)',\n      nextYear: 'Jövő év (Control + right)',\n      previousDecade: 'Előző évtized',\n      nextDecade: 'Következő évtized',\n      previousCentury: 'Múlt évszázad',\n      nextCentury: 'Jövő évszázad'\n    },\n    timePickerLocale: {\n      placeholder: 'Válasszon időt',\n      rangePlaceholder: ['Kezdő idő', 'Befejezés ideje']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Válasszon időt',\n    rangePlaceholder: ['Kezdő idő', 'Befejezés ideje']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Válasszon dátumot',\n      yearPlaceholder: 'Válasszon évet',\n      quarterPlaceholder: 'Válasszon negyedévet',\n      monthPlaceholder: 'Válasszon hónapot',\n      weekPlaceholder: 'Válasszon hetet',\n      rangePlaceholder: ['Kezdő dátum', 'Befejezés dátuma'],\n      rangeYearPlaceholder: ['Kezdő év', 'Befejezés éve'],\n      rangeMonthPlaceholder: ['Kezdő hónap', 'Befejezés hónapja'],\n      rangeWeekPlaceholder: ['Kezdő hét', 'Befejezés hete'],\n      locale: 'hu_HU',\n      today: 'Ma',\n      now: 'Most',\n      backToToday: 'Vissza a mai napra',\n      ok: 'Ok',\n      clear: 'Törlés',\n      month: 'Hónap',\n      year: 'Év',\n      timeSelect: 'Időpont kiválasztása',\n      dateSelect: 'Dátum kiválasztása',\n      weekSelect: 'Hét kiválasztása',\n      monthSelect: 'Hónap kiválasztása',\n      yearSelect: 'Év kiválasztása',\n      decadeSelect: 'Évtized kiválasztása',\n      yearFormat: 'YYYY',\n      dateFormat: 'YYYY/MM/DD',\n      dayFormat: 'DD',\n      dateTimeFormat: 'YYYY/MM/DD HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Előző hónap (PageUp)',\n      nextMonth: 'Következő hónap (PageDown)',\n      previousYear: 'Múlt év (Control + left)',\n      nextYear: 'Jövő év (Control + right)',\n      previousDecade: 'Előző évtized',\n      nextDecade: 'Következő évtized',\n      previousCentury: 'Múlt évszázad',\n      nextCentury: 'Jövő évszázad'\n    },\n    timePickerLocale: {\n      placeholder: 'Válasszon időt',\n      rangePlaceholder: ['Kezdő idő', 'Befejezés ideje']\n    }\n  },\n  global: {\n    placeholder: 'Kérlek, válassz'\n  },\n  Table: {\n    filterTitle: 'Szűrők',\n    filterConfirm: 'Alkalmazás',\n    filterReset: 'Visszaállítás',\n    filterEmptyText: 'No filters',\n    emptyText: 'Nincs adat',\n    selectAll: 'Jelenlegi oldal kiválasztása',\n    selectInvert: 'Jelenlegi oldal inverze',\n    selectionAll: 'Összes adat kiválasztása',\n    sortTitle: 'Rendezés',\n    expand: 'Sor kinyitása',\n    collapse: 'Sor becsukása',\n    triggerDesc: 'Kattintson a csökkenő sorrendbe rendezéshez',\n    triggerAsc: 'Kattintson a növekvő sorrendbe rendezéshez',\n    cancelSort: 'Kattintson a rendezés visszavonásához',\n    selectNone: 'Összes visszavonása'\n  },\n  Modal: {\n    okText: 'Alkalmazás',\n    cancelText: 'Visszavonás',\n    justOkText: 'Alkalmazás'\n  },\n  Popconfirm: {\n    okText: 'Alkalmazás',\n    cancelText: 'Visszavonás'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Keresés',\n    itemUnit: 'elem',\n    itemsUnit: 'elemek',\n    remove: 'Eltávolít',\n    selectCurrent: 'Jelenlegi oldal kiválasztása',\n    removeCurrent: 'Jelenlegi oldal eltávolítása',\n    selectAll: 'Összes adat kiválasztása',\n    removeAll: 'Összes adat eltávolítása',\n    selectInvert: 'Jelenlegi oldal inverze'\n  },\n  Upload: {\n    uploading: 'Feltöltés...',\n    removeFile: 'Fájl eltávolítása',\n    uploadError: 'Feltöltési hiba',\n    previewFile: 'Fájl előnézet',\n    downloadFile: 'Fájl letöltése'\n  },\n  Empty: {\n    description: 'Nincs adat'\n  },\n  Icon: {\n    icon: 'ikon'\n  },\n  Text: {\n    edit: 'Szerkesztés',\n    copy: 'Másolás',\n    copied: 'Másolva',\n    expand: 'Kiterjesztés'\n  },\n  PageHeader: {\n    back: 'Vissza'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar hy_AM = {\n  locale: 'hy-am',\n  Pagination: {\n    items_per_page: '/ էջ',\n    jump_to: 'Գնալ',\n    jump_to_confirm: 'հաստատել',\n    page: '',\n    prev_page: 'Նախորդ Էջ',\n    next_page: 'Հաջորդ Էջ',\n    prev_5: 'Նախորդ 5 Էջերը',\n    next_5: 'Հաջորդ 5 Էջերը',\n    prev_3: 'Նախորդ 3 Էջերը',\n    next_3: 'Հաջորդ 3 Էջերը'\n  },\n  DatePicker: {\n    lang: {\n      locale: 'hy-am',\n      placeholder: 'Ընտրեք ամսաթիվը',\n      rangePlaceholder: ['Մեկնարկի ամսաթիվ', 'Ավարտի ամսաթիվը'],\n      today: 'Այսօր',\n      now: 'Հիմա',\n      backToToday: 'Վերադառնալ այսօր',\n      ok: 'Օկ',\n      clear: 'Մաքրել',\n      month: 'Ամիս',\n      year: 'Տարի',\n      timeSelect: 'ընտրեք ժամը',\n      dateSelect: 'ընտրեք ամսաթիվը',\n      weekSelect: 'Ընտրեք շաբաթը',\n      monthSelect: 'Ընտրեք ամիսը',\n      yearSelect: 'Ընտրեք տարին',\n      decadeSelect: 'Ընտրեք տասնամյակը',\n      yearFormat: 'YYYY',\n      dateFormat: 'DD/MM//YYYY',\n      dayFormat: 'DD',\n      dateTimeFormat: 'DD/MM//YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Անցած ամիս (PageUp)',\n      nextMonth: 'Մյուս ամիս (PageDown)',\n      previousYear: 'Անցած տարի (Control + left)',\n      nextYear: 'Մյուս տարի (Control + right)',\n      previousDecade: 'Անցած տասնամյակ',\n      nextDecade: 'Մյուս տասնամյակ',\n      previousCentury: 'Անցած դար',\n      nextCentury: 'Մյուս դար'\n    },\n    timePickerLocale: {\n      placeholder: 'Ընտրեք ժամը'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Ընտրեք ժամը'\n  },\n  Calendar: {\n    lang: {\n      locale: 'hy-am',\n      placeholder: 'Ընտրեք ամսաթիվը',\n      rangePlaceholder: ['Մեկնարկի ամսաթիվ', 'Ավարտի ամսաթիվը'],\n      today: 'Այսօր',\n      now: 'Հիմա',\n      backToToday: 'Վերադառնալ այսօր',\n      ok: 'Օկ',\n      clear: 'Մաքրել',\n      month: 'Ամիս',\n      year: 'Տարի',\n      timeSelect: 'ընտրեք ժամը',\n      dateSelect: 'ընտրեք ամսաթիվը',\n      weekSelect: 'Ընտրեք շաբաթը',\n      monthSelect: 'Ընտրեք ամիսը',\n      yearSelect: 'Ընտրեք տարին',\n      decadeSelect: 'Ընտրեք տասնամյակը',\n      yearFormat: 'YYYY',\n      dateFormat: 'DD/MM//YYYY',\n      dayFormat: 'DD',\n      dateTimeFormat: 'DD/MM//YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Անցած ամիս (PageUp)',\n      nextMonth: 'Մյուս ամիս (PageDown)',\n      previousYear: 'Անցած տարի (Control + left)',\n      nextYear: 'Մյուս տարի (Control + right)',\n      previousDecade: 'Անցած տասնամյակ',\n      nextDecade: 'Մյուս տասնամյակ',\n      previousCentury: 'Անցած դար',\n      nextCentury: 'Մյուս դար'\n    },\n    timePickerLocale: {\n      placeholder: 'Ընտրեք ժամը'\n    }\n  },\n  global: {\n    placeholder: 'Ընտրեք'\n  },\n  Table: {\n    filterTitle: 'ֆիլտրի ընտրացանկ',\n    filterConfirm: 'ֆիլտրել',\n    filterReset: 'Զրոյացնել',\n    selectAll: 'Ընտրեք ընթացիկ էջը',\n    selectInvert: 'Փոխարկել ընթացիկ էջը',\n    sortTitle: 'Տեսակավորել',\n    expand: 'Ընդլայնեք տողը',\n    collapse: 'Կրճատել տողը'\n  },\n  Modal: {\n    okText: 'Օկ',\n    cancelText: 'Չեղարկել',\n    justOkText: 'Օկ'\n  },\n  Popconfirm: {\n    okText: 'Հաստատել',\n    cancelText: 'Մերժել'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Որոնեք այստեղ',\n    itemUnit: 'պարագան',\n    itemsUnit: 'պարագաները'\n  },\n  Upload: {\n    uploading: 'Ներբեռնում...',\n    removeFile: 'Հեռացնել ֆայլը',\n    uploadError: 'Ներբեռնման սխալ',\n    previewFile: 'Դիտել ֆայլը',\n    downloadFile: 'Ներբեռնել ֆայլը'\n  },\n  Empty: {\n    description: 'Տվյալներ չկան'\n  },\n  Icon: {\n    icon: 'պատկեր'\n  },\n  Text: {\n    edit: 'Խմբագրել',\n    copy: 'Պատճենել',\n    copied: 'Պատճենվել է',\n    expand: 'Տեսնել ավելին'\n  },\n  PageHeader: {\n    back: 'Հետ'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar id_ID = {\n  locale: 'id',\n  Pagination: {\n    items_per_page: '/ halaman',\n    jump_to: 'Menuju',\n    jump_to_confirm: 'konfirmasi',\n    page: 'Halaman',\n    prev_page: 'Halaman Sebelumnya',\n    next_page: 'Halaman Berikutnya',\n    prev_5: '5 Halaman Sebelumnya',\n    next_5: '5 Halaman Berikutnya',\n    prev_3: '3 Halaman Sebelumnya',\n    next_3: '3 Halaman Berikutnya',\n    page_size: 'ukuran halaman'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Pilih tanggal',\n      rangePlaceholder: ['Mulai tanggal', 'Tanggal akhir'],\n      locale: 'id_ID',\n      today: 'Hari ini',\n      now: 'Sekarang',\n      backToToday: 'Kembali ke hari ini',\n      ok: 'Baik',\n      clear: 'Bersih',\n      month: 'Bulan',\n      year: 'Tahun',\n      timeSelect: 'pilih waktu',\n      dateSelect: 'pilih tanggal',\n      weekSelect: 'Pilih satu minggu',\n      monthSelect: 'Pilih satu bulan',\n      yearSelect: 'Pilih satu tahun',\n      decadeSelect: 'Pilih satu dekade',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Bulan sebelumnya (PageUp)',\n      nextMonth: 'Bulan selanjutnya (PageDown)',\n      previousYear: 'Tahun lalu (Control + kiri)',\n      nextYear: 'Tahun selanjutnya (Kontrol + kanan)',\n      previousDecade: 'Dekade terakhir',\n      nextDecade: 'Dekade berikutnya',\n      previousCentury: 'Abad terakhir',\n      nextCentury: 'Abad berikutnya'\n    },\n    timePickerLocale: {\n      placeholder: 'Pilih waktu'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Pilih waktu'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Pilih tanggal',\n      rangePlaceholder: ['Mulai tanggal', 'Tanggal akhir'],\n      locale: 'id_ID',\n      today: 'Hari ini',\n      now: 'Sekarang',\n      backToToday: 'Kembali ke hari ini',\n      ok: 'Baik',\n      clear: 'Bersih',\n      month: 'Bulan',\n      year: 'Tahun',\n      timeSelect: 'pilih waktu',\n      dateSelect: 'pilih tanggal',\n      weekSelect: 'Pilih satu minggu',\n      monthSelect: 'Pilih satu bulan',\n      yearSelect: 'Pilih satu tahun',\n      decadeSelect: 'Pilih satu dekade',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Bulan sebelumnya (PageUp)',\n      nextMonth: 'Bulan selanjutnya (PageDown)',\n      previousYear: 'Tahun lalu (Control + kiri)',\n      nextYear: 'Tahun selanjutnya (Kontrol + kanan)',\n      previousDecade: 'Dekade terakhir',\n      nextDecade: 'Dekade berikutnya',\n      previousCentury: 'Abad terakhir',\n      nextCentury: 'Abad berikutnya'\n    },\n    timePickerLocale: {\n      placeholder: 'Pilih waktu'\n    }\n  },\n  Table: {\n    filterTitle: 'Saring',\n    filterConfirm: 'OK',\n    filterReset: 'Hapus',\n    selectAll: 'Pilih semua di halaman ini',\n    selectInvert: 'Balikkan pilihan di halaman ini',\n    sortTitle: 'Urutkan'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Batal',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Batal'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Cari',\n    itemUnit: 'item',\n    itemsUnit: 'item'\n  },\n  Upload: {\n    uploading: 'Mengunggah...',\n    removeFile: 'Hapus file',\n    uploadError: 'Kesalahan pengunggahan',\n    previewFile: 'File pratinjau',\n    downloadFile: 'Unduh berkas'\n  },\n  Empty: {\n    description: 'Tidak ada data'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar is_IS = {\n  locale: 'is',\n  Pagination: {\n    items_per_page: '/ síðu',\n    jump_to: 'Síða',\n    jump_to_confirm: 'staðfest',\n    page: '',\n    prev_page: 'Fyrri síða',\n    next_page: 'Næsta síða',\n    prev_5: 'Til baka 5 síður',\n    next_5: 'Áfram 5 síður',\n    prev_3: 'Til baka 3 síður',\n    next_3: 'Áfram 3 síður',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Veldu dag',\n      rangePlaceholder: ['Upphafsdagur', 'Lokadagur'],\n      locale: 'is_IS',\n      today: 'Í dag',\n      now: 'Núna',\n      backToToday: 'Til baka til dagsins í dag',\n      ok: 'Í lagi',\n      clear: 'Hreinsa',\n      month: 'Mánuður',\n      year: 'Ár',\n      timeSelect: 'Velja tíma',\n      dateSelect: 'Velja dag',\n      monthSelect: 'Velja mánuð',\n      yearSelect: 'Velja ár',\n      decadeSelect: 'Velja áratug',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Fyrri mánuður (PageUp)',\n      nextMonth: 'Næsti mánuður (PageDown)',\n      previousYear: 'Fyrra ár (Control + left)',\n      nextYear: 'Næsta ár (Control + right)',\n      previousDecade: 'Fyrri áratugur',\n      nextDecade: 'Næsti áratugur',\n      previousCentury: 'Fyrri öld',\n      nextCentury: 'Næsta öld'\n    },\n    timePickerLocale: {\n      placeholder: 'Velja tíma'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Velja tíma'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Veldu dag',\n      rangePlaceholder: ['Upphafsdagur', 'Lokadagur'],\n      locale: 'is_IS',\n      today: 'Í dag',\n      now: 'Núna',\n      backToToday: 'Til baka til dagsins í dag',\n      ok: 'Í lagi',\n      clear: 'Hreinsa',\n      month: 'Mánuður',\n      year: 'Ár',\n      timeSelect: 'Velja tíma',\n      dateSelect: 'Velja dag',\n      monthSelect: 'Velja mánuð',\n      yearSelect: 'Velja ár',\n      decadeSelect: 'Velja áratug',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Fyrri mánuður (PageUp)',\n      nextMonth: 'Næsti mánuður (PageDown)',\n      previousYear: 'Fyrra ár (Control + left)',\n      nextYear: 'Næsta ár (Control + right)',\n      previousDecade: 'Fyrri áratugur',\n      nextDecade: 'Næsti áratugur',\n      previousCentury: 'Fyrri öld',\n      nextCentury: 'Næsta öld'\n    },\n    timePickerLocale: {\n      placeholder: 'Velja tíma'\n    }\n  },\n  Table: {\n    filterTitle: 'Afmarkanir',\n    filterConfirm: 'Staðfesta',\n    filterReset: 'Núllstilla',\n    selectAll: 'Velja allt',\n    selectInvert: 'Viðsnúa vali'\n  },\n  Modal: {\n    okText: 'Áfram',\n    cancelText: 'Hætta við',\n    justOkText: 'Í lagi'\n  },\n  Popconfirm: {\n    okText: 'Áfram',\n    cancelText: 'Hætta við'\n  },\n  Transfer: {\n    searchPlaceholder: 'Leita hér',\n    itemUnit: 'færsla',\n    itemsUnit: 'færslur'\n  },\n  Upload: {\n    uploading: 'Hleð upp...',\n    removeFile: 'Fjarlægja skrá',\n    uploadError: 'Villa við að hlaða upp',\n    previewFile: 'Forskoða skrá',\n    downloadFile: 'Hlaða niður skrá'\n  },\n  Empty: {\n    description: 'Engin gögn'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar it_IT = {\n  locale: 'it',\n  Pagination: {\n    items_per_page: '/ pagina',\n    jump_to: 'vai a',\n    jump_to_confirm: 'Conferma',\n    page: 'Pagina',\n    prev_page: 'Pagina precedente',\n    next_page: 'Pagina successiva',\n    prev_5: 'Precedente 5 pagine',\n    next_5: 'Prossime 5 pagine',\n    prev_3: 'Precedente 3 pagine',\n    next_3: 'Prossime 3 pagine',\n    page_size: 'dimensioni della pagina'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Selezionare la data',\n      yearPlaceholder: \"Selezionare l'anno\",\n      quarterPlaceholder: 'Selezionare il trimestre',\n      monthPlaceholder: 'Selezionare il mese',\n      weekPlaceholder: 'Selezionare la settimana',\n      rangePlaceholder: [\"Data d'inizio\", 'Data di fine'],\n      rangeYearPlaceholder: [\"Anno d'inizio\", 'Anno di fine'],\n      rangeMonthPlaceholder: [\"Mese d'inizio \", 'Mese di fine'],\n      rangeWeekPlaceholder: [\"Settimana d'inizio\", 'Settimana di fine'],\n      locale: 'it_IT',\n      today: 'Oggi',\n      now: 'Adesso',\n      backToToday: 'Torna ad oggi',\n      ok: 'Ok',\n      clear: 'Cancella',\n      month: 'Mese',\n      year: 'Anno',\n      timeSelect: \"Seleziona l'ora\",\n      dateSelect: 'Seleziona la data',\n      weekSelect: 'Seleziona la settimana',\n      monthSelect: 'Seleziona il mese',\n      yearSelect: \"Seleziona l'anno\",\n      decadeSelect: 'Seleziona il decennio',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Il mese scorso (PageUp)',\n      nextMonth: 'Il prossimo mese (PageDown)',\n      previousYear: \"L'anno scorso (Control + sinistra)\",\n      nextYear: \"L'anno prossimo (Control + destra)\",\n      previousDecade: 'Ultimo decennio',\n      nextDecade: 'Prossimo decennio',\n      previousCentury: 'Secolo precedente',\n      nextCentury: 'Prossimo secolo'\n    },\n    timePickerLocale: {\n      placeholder: \"Selezionare l'orario\",\n      rangePlaceholder: [\"Ora d'inizio\", 'Ora di fine']\n    }\n  },\n  TimePicker: {\n    placeholder: \"Selezionare l'orario\",\n    rangePlaceholder: [\"Ora d'inizio\", 'Ora di fine']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Selezionare la data',\n      yearPlaceholder: \"Selezionare l'anno\",\n      quarterPlaceholder: 'Selezionare il trimestre',\n      monthPlaceholder: 'Selezionare il mese',\n      weekPlaceholder: 'Selezionare la settimana',\n      rangePlaceholder: [\"Data d'inizio\", 'Data di fine'],\n      rangeYearPlaceholder: [\"Anno d'inizio\", 'Anno di fine'],\n      rangeMonthPlaceholder: [\"Mese d'inizio \", 'Mese di fine'],\n      rangeWeekPlaceholder: [\"Settimana d'inizio\", 'Settimana di fine'],\n      locale: 'it_IT',\n      today: 'Oggi',\n      now: 'Adesso',\n      backToToday: 'Torna ad oggi',\n      ok: 'Ok',\n      clear: 'Cancella',\n      month: 'Mese',\n      year: 'Anno',\n      timeSelect: \"Seleziona l'ora\",\n      weekSelect: 'Seleziona la settimana',\n      dateSelect: 'Seleziona la data',\n      monthSelect: 'Seleziona il mese',\n      yearSelect: \"Seleziona l'anno\",\n      decadeSelect: 'Seleziona il decennio',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Il mese scorso (PageUp)',\n      nextMonth: 'Il prossimo mese (PageDown)',\n      previousYear: \"L'anno scorso (Control + sinistra)\",\n      nextYear: \"L'anno prossimo (Control + destra)\",\n      previousDecade: 'Ultimo decennio',\n      nextDecade: 'Prossimo decennio',\n      previousCentury: 'Secolo precedente',\n      nextCentury: 'Prossimo secolo'\n    },\n    timePickerLocale: {\n      placeholder: \"Selezionare l'orario\",\n      rangePlaceholder: [\"Ora d'inizio\", 'Ora di fine']\n    }\n  },\n  global: {\n    placeholder: 'Selezionare'\n  },\n  Table: {\n    filterTitle: 'Menù Filtro',\n    filterConfirm: 'OK',\n    filterReset: 'Reset',\n    filterEmptyText: 'Nessun filtro',\n    emptyText: 'Nessun dato',\n    selectAll: 'Seleziona pagina corrente',\n    selectInvert: 'Inverti selezione nella pagina corrente',\n    selectionAll: 'Seleziona tutti i dati',\n    sortTitle: 'Ordina',\n    expand: 'Esapandi riga',\n    collapse: 'Chiudi riga',\n    triggerDesc: 'Clicca per ordinare in modo discendente',\n    triggerAsc: 'Clicca per ordinare in modo ascendente',\n    cancelSort: 'Clicca per eliminare i filtri',\n    filterCheckall: 'Seleziona tutto',\n    filterSearchPlaceholder: 'Cerca nei filtri',\n    selectNone: 'Pulisci tutti i dati'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Annulla',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Annulla'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Cerca qui',\n    itemUnit: 'elemento',\n    itemsUnit: 'elementi',\n    remove: 'Rimuovi',\n    selectCurrent: 'Seleziona pagina corrente',\n    removeCurrent: 'Rimuovi pagina corrente',\n    selectAll: 'Selezione tutti i dati',\n    removeAll: 'Rimuovi tutti i dati',\n    selectInvert: 'Inverti selezione nella pagina corrente'\n  },\n  Upload: {\n    uploading: 'Caricamento...',\n    removeFile: 'Rimuovi il file',\n    uploadError: 'Errore di caricamento',\n    previewFile: 'Anteprima file',\n    downloadFile: 'Download file'\n  },\n  Empty: {\n    description: 'Nessun dato'\n  },\n  Icon: {\n    icon: 'icona'\n  },\n  Text: {\n    edit: 'modifica',\n    copy: 'copia',\n    copied: 'copia effettuata',\n    expand: 'espandi'\n  },\n  PageHeader: {\n    back: 'Indietro'\n  },\n  Image: {\n    preview: 'Anteprima'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ja_JP = {\n  locale: 'ja',\n  Pagination: {\n    items_per_page: '件 / ページ',\n    jump_to: '移動',\n    jump_to_confirm: '確認する',\n    page: 'ページ',\n    prev_page: '前のページ',\n    next_page: '次のページ',\n    prev_5: '前 5ページ',\n    next_5: '次 5ページ',\n    prev_3: '前 3ページ',\n    next_3: '次 3ページ',\n    page_size: 'ページサイズ'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: '日付を選択',\n      yearPlaceholder: '年を選択',\n      monthPlaceholder: '月を選択',\n      weekPlaceholder: '週を選択',\n      rangePlaceholder: ['開始日付', '終了日付'],\n      rangeYearPlaceholder: ['開始年', '終了年'],\n      rangeMonthPlaceholder: ['開始月', '終了月'],\n      rangeWeekPlaceholder: ['開始週', '終了週'],\n      locale: 'ja_JP',\n      today: '今日',\n      now: '現在時刻',\n      backToToday: '今日に戻る',\n      ok: '決定',\n      timeSelect: '時間を選択',\n      dateSelect: '日時を選択',\n      weekSelect: '週を選択',\n      clear: 'クリア',\n      month: '月',\n      year: '年',\n      previousMonth: '前月 (ページアップキー)',\n      nextMonth: '翌月 (ページダウンキー)',\n      monthSelect: '月を選択',\n      yearSelect: '年を選択',\n      decadeSelect: '年代を選択',\n      yearFormat: 'YYYY年',\n      dayFormat: 'D日',\n      dateFormat: 'YYYY年M月D日',\n      dateTimeFormat: 'YYYY年M月D日 HH時mm分ss秒',\n      previousYear: '前年 (Controlを押しながら左キー)',\n      nextYear: '翌年 (Controlを押しながら右キー)',\n      previousDecade: '前の年代',\n      nextDecade: '次の年代',\n      previousCentury: '前の世紀',\n      nextCentury: '次の世紀'\n    },\n    timePickerLocale: {\n      placeholder: '時間を選択',\n      rangePlaceholder: ['開始時間', '終了時間']\n    }\n  },\n  TimePicker: {\n    placeholder: '時間を選択',\n    rangePlaceholder: ['開始時間', '終了時間']\n  },\n  Calendar: {\n    lang: {\n      placeholder: '日付を選択',\n      rangePlaceholder: ['開始日付', '終了日付'],\n      locale: 'ja_JP',\n      today: '今日',\n      now: '現在時刻',\n      backToToday: '今日に戻る',\n      ok: '決定',\n      timeSelect: '時間を選択',\n      dateSelect: '日時を選択',\n      weekSelect: '週を選択',\n      clear: 'クリア',\n      month: '月',\n      year: '年',\n      previousMonth: '前月 (ページアップキー)',\n      nextMonth: '翌月 (ページダウンキー)',\n      monthSelect: '月を選択',\n      yearSelect: '年を選択',\n      decadeSelect: '年代を選択',\n      yearFormat: 'YYYY年',\n      dayFormat: 'D日',\n      dateFormat: 'YYYY年M月D日',\n      dateTimeFormat: 'YYYY年M月D日 HH時mm分ss秒',\n      previousYear: '前年 (Controlを押しながら左キー)',\n      nextYear: '翌年 (Controlを押しながら右キー)',\n      previousDecade: '前の年代',\n      nextDecade: '次の年代',\n      previousCentury: '前の世紀',\n      nextCentury: '次の世紀'\n    },\n    timePickerLocale: {\n      placeholder: '時間を選択',\n      rangePlaceholder: ['開始時間', '終了時間']\n    }\n  },\n  Table: {\n    filterTitle: 'フィルター',\n    filterConfirm: 'OK',\n    filterReset: 'リセット',\n    filterEmptyText: 'フィルターなし',\n    selectAll: 'ページ単位で選択',\n    selectInvert: 'ページ単位で反転',\n    selectionAll: 'すべてを選択',\n    sortTitle: 'ソート',\n    expand: '展開する',\n    collapse: '折り畳む',\n    triggerDesc: 'クリックで降順にソート',\n    triggerAsc: 'クリックで昇順にソート',\n    cancelSort: 'ソートをキャンセル'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'キャンセル',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'キャンセル'\n  },\n  Transfer: {\n    searchPlaceholder: 'ここを検索',\n    itemUnit: 'アイテム',\n    itemsUnit: 'アイテム'\n  },\n  Upload: {\n    uploading: 'アップロード中...',\n    removeFile: 'ファイルを削除',\n    uploadError: 'アップロードエラー',\n    previewFile: 'ファイルをプレビュー',\n    downloadFile: 'ダウンロードファイル'\n  },\n  Empty: {\n    description: 'データがありません'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ka_GE = {\n  locale: 'ka',\n  Pagination: {\n    items_per_page: '/ გვერდი.',\n    jump_to: 'გადასვლა',\n    jump_to_confirm: 'დადასტურება',\n    page: '',\n    prev_page: 'წინა გვერდი',\n    next_page: 'შემდეგი გვერდი',\n    prev_5: 'წინა 5 გვერდი',\n    next_5: 'შემდეგი 5 გვერდი',\n    prev_3: 'წინა 3 გვერდი',\n    next_3: 'შემდეგი 3 გვერდი',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'აირჩიეთ თარიღი',\n      yearPlaceholder: 'აირჩიეთ წელი',\n      quarterPlaceholder: 'აირჩიეთ მეოთხედი',\n      monthPlaceholder: 'აირჩიეთ თვე',\n      weekPlaceholder: 'აირჩიეთ კვირა',\n      rangePlaceholder: ['საწყისი თარიღი', 'საბოლოო თარიღი'],\n      rangeYearPlaceholder: ['საწყისი წელი', 'საბოლოო წელი'],\n      rangeMonthPlaceholder: ['საწყისი თვე', 'საბოლოო თვე'],\n      rangeWeekPlaceholder: ['საწყისი კვირა', 'საბოლოო კვირა'],\n      locale: 'ka_GE',\n      today: 'დღეს',\n      now: 'ახლა',\n      backToToday: 'მიმდინარე თარიღი',\n      ok: 'Ok',\n      clear: 'გასუფთავება',\n      month: 'თვე',\n      year: 'წელი',\n      timeSelect: 'დროის არჩევა',\n      dateSelect: 'თარიღის არჩევა',\n      weekSelect: 'კვირის არჩევა',\n      monthSelect: 'თვის არჩევა',\n      yearSelect: 'წლის არჩევა',\n      decadeSelect: 'ათწლეულის არჩევა',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'წინა თვე (PageUp)',\n      nextMonth: 'მომდევნო თვე (PageDown)',\n      previousYear: 'წინა წელი (Control + left)',\n      nextYear: 'მომდევნო წელი (Control + right)',\n      previousDecade: 'წინა ათწლეული',\n      nextDecade: 'მომდევნო ათწლეული',\n      previousCentury: 'გასული საუკუნე',\n      nextCentury: 'მომდევნო საუკუნე'\n    },\n    timePickerLocale: {\n      placeholder: 'აირჩიეთ დრო',\n      rangePlaceholder: ['საწყისი თარიღი', 'საბოლოო თარიღი']\n    }\n  },\n  TimePicker: {\n    placeholder: 'აირჩიეთ დრო',\n    rangePlaceholder: ['საწყისი თარიღი', 'საბოლოო თარიღი']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'აირჩიეთ თარიღი',\n      yearPlaceholder: 'აირჩიეთ წელი',\n      quarterPlaceholder: 'აირჩიეთ მეოთხედი',\n      monthPlaceholder: 'აირჩიეთ თვე',\n      weekPlaceholder: 'აირჩიეთ კვირა',\n      rangePlaceholder: ['საწყისი თარიღი', 'საბოლოო თარიღი'],\n      rangeYearPlaceholder: ['საწყისი წელი', 'საბოლოო წელი'],\n      rangeMonthPlaceholder: ['საწყისი თვე', 'საბოლოო თვე'],\n      rangeWeekPlaceholder: ['საწყისი კვირა', 'საბოლოო კვირა'],\n      locale: 'ka_GE',\n      today: 'დღეს',\n      now: 'ახლა',\n      backToToday: 'მიმდინარე თარიღი',\n      ok: 'Ok',\n      clear: 'გასუფთავება',\n      month: 'თვე',\n      year: 'წელი',\n      timeSelect: 'დროის არჩევა',\n      dateSelect: 'თარიღის არჩევა',\n      weekSelect: 'კვირის არჩევა',\n      monthSelect: 'თვის არჩევა',\n      yearSelect: 'წლის არჩევა',\n      decadeSelect: 'ათწლეულის არჩევა',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'წინა თვე (PageUp)',\n      nextMonth: 'მომდევნო თვე (PageDown)',\n      previousYear: 'წინა წელი (Control + left)',\n      nextYear: 'მომდევნო წელი (Control + right)',\n      previousDecade: 'წინა ათწლეული',\n      nextDecade: 'მომდევნო ათწლეული',\n      previousCentury: 'გასული საუკუნე',\n      nextCentury: 'მომდევნო საუკუნე'\n    },\n    timePickerLocale: {\n      placeholder: 'აირჩიეთ დრო',\n      rangePlaceholder: ['საწყისი თარიღი', 'საბოლოო თარიღი']\n    }\n  },\n  global: {\n    placeholder: 'გთხოვთ აირჩიოთ'\n  },\n  Table: {\n    filterTitle: 'ფილტრის მენიუ',\n    filterConfirm: 'კარგი',\n    filterReset: 'გასუფთავება',\n    filterEmptyText: 'ფილტრები არაა',\n    emptyText: 'ინფორმაცია არაა',\n    selectAll: 'აირჩიეთ მიმდინარე გვერდი',\n    selectInvert: 'შეაბრუნეთ მიმდინარე გვერდი',\n    selectionAll: 'ყველას მონიშვნა',\n    sortTitle: 'დალაგება',\n    expand: 'სტრიქონის გაშლა',\n    collapse: 'სტრიქონის შეკუმშვა',\n    triggerDesc: 'დაღმავალი დალაგება',\n    triggerAsc: 'აღმავალი დალაგება',\n    cancelSort: 'დალაგების გაუქმება',\n    selectNone: 'მონაცემების გასუფთავება'\n  },\n  Modal: {\n    okText: 'კარგი',\n    cancelText: 'გაუქმება',\n    justOkText: 'ოკ'\n  },\n  Popconfirm: {\n    okText: 'კარგი',\n    cancelText: 'გაუქმება'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'მოძებნე აქ',\n    itemUnit: 'ერთეული',\n    itemsUnit: 'ერთეულები',\n    remove: 'ამოშლა',\n    selectCurrent: 'მიმდინარე გვერდის არჩევა',\n    removeCurrent: 'მიმდინარე გვერდის ამოშლა',\n    selectAll: 'ყველას მონიშვნა',\n    removeAll: 'ყველას წაშლა',\n    selectInvert: 'მიმდინარე გვერდის შებრუნება'\n  },\n  Upload: {\n    uploading: 'იტვირთება...',\n    removeFile: 'ფაილის ამოშლა',\n    uploadError: 'ატვირთვის შეცდომა',\n    previewFile: 'ფაილის გადახედვა',\n    downloadFile: 'ფაილის ჩამოტვირთვა'\n  },\n  Empty: {\n    description: 'ინფორმაცია არაა'\n  },\n  Icon: {\n    icon: 'ხატულა'\n  },\n  Text: {\n    edit: 'რედაქტირება',\n    copy: 'ასლი',\n    copied: 'ასლი აღებულია',\n    expand: 'გაშლა'\n  },\n  PageHeader: {\n    back: 'უკან'\n  },\n  Image: {\n    preview: 'გადახედვა'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar km_KH = {\n  locale: 'km',\n  Pagination: {\n    items_per_page: '/ ទំព័រ',\n    jump_to: 'លោត​ទៅ',\n    jump_to_confirm: 'បញ្ជាក់',\n    page: 'ទំព័រ',\n    prev_page: 'ទំព័រ​មុន',\n    next_page: 'ទំព័រ​​បន្ទាប់',\n    prev_5: '៥ ទំព័រថយក្រោយ',\n    next_5: '៥ ទំព័រទៅមុខ',\n    prev_3: '៣ ទំព័រថយក្រោយ',\n    next_3: '៣ ទំព័រទៅមុខ',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'រើសថ្ងៃ',\n      yearPlaceholder: 'រើសឆ្នាំ',\n      quarterPlaceholder: 'រើសត្រីមាស',\n      monthPlaceholder: 'រើសខែ',\n      weekPlaceholder: 'រើសសប្តាហ៍',\n      rangePlaceholder: ['ថ្ងៃចាប់ផ្ដើម', 'ថ្ងៃបញ្ចប់'],\n      rangeYearPlaceholder: ['ឆ្នាំចាប់ផ្ដើម', 'ឆ្នាំបញ្ចប់'],\n      rangeMonthPlaceholder: ['ខែចាប់ផ្ដើម', 'ខែបញ្ចប់'],\n      rangeWeekPlaceholder: ['សប្ដាហ៍ចាប់ផ្ដើម', 'សប្ដាហ៍បញ្ចប់'],\n      locale: 'km',\n      today: 'ថ្ងៃនេះ',\n      now: 'ឥឡូវ​នេះ',\n      backToToday: 'ត្រលប់ទៅថ្ងៃនេះ',\n      ok: 'កំណត់',\n      timeSelect: 'រយៈពេលជ្រើសរើស',\n      dateSelect: 'ជ្រើសរើសកាលបរិច្ឆេទ',\n      weekSelect: 'ជ្រើសរើសសប្តាហ៍',\n      clear: 'ច្បាស់',\n      month: 'ខែ',\n      year: 'ឆ្នាំ',\n      previousMonth: 'ខែមុន (ឡើងទំព័រ)',\n      nextMonth: 'ខែបន្ទាប់ (ប៊ូតុងចុះទំព័រ)',\n      monthSelect: 'ជ្រើសរើសខែ',\n      yearSelect: 'ជ្រើសរើសឆ្នាំ',\n      decadeSelect: 'ជ្រើសរើសអាយុ',\n      yearFormat: 'YYYY',\n      dayFormat: 'D',\n      dateFormat: 'YYYY-M-D',\n      dateTimeFormat: 'YYYY-M-D HH:mm:ss',\n      previousYear: 'ឆ្នាំមុន (Controlគ្រាប់ចុចបូកព្រួញខាងឆ្វេង)',\n      nextYear: 'ឆ្នាំក្រោយ (Control គ្រាប់ចុចបូកព្រួញស្ដាំ)',\n      previousDecade: 'ជំនាន់ចុងក្រោយ',\n      nextDecade: 'ជំនាន់​ក្រោយ',\n      previousCentury: 'សតវត្សចុងក្រោយ',\n      nextCentury: 'សតវត្សរ៍បន្ទាប់',\n      monthBeforeYear: true\n    },\n    timePickerLocale: {\n      placeholder: 'រើសម៉ោង',\n      rangePlaceholder: ['ម៉ោងចប់ផ្ដើម', 'ម៉ោងបញ្ចប់']\n    }\n  },\n  TimePicker: {\n    placeholder: 'រើសម៉ោង',\n    rangePlaceholder: ['ម៉ោងចប់ផ្ដើម', 'ម៉ោងបញ្ចប់']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'រើសថ្ងៃ',\n      yearPlaceholder: 'រើសឆ្នាំ',\n      quarterPlaceholder: 'រើសត្រីមាស',\n      monthPlaceholder: 'រើសខែ',\n      weekPlaceholder: 'រើសសប្តាហ៍',\n      rangePlaceholder: ['ថ្ងៃចាប់ផ្ដើម', 'ថ្ងៃបញ្ចប់'],\n      rangeYearPlaceholder: ['ឆ្នាំចាប់ផ្ដើម', 'ឆ្នាំបញ្ចប់'],\n      rangeMonthPlaceholder: ['ខែចាប់ផ្ដើម', 'ខែបញ្ចប់'],\n      rangeWeekPlaceholder: ['សប្ដាហ៍ចាប់ផ្ដើម', 'សប្ដាហ៍បញ្ចប់'],\n      locale: 'km',\n      today: 'ថ្ងៃនេះ',\n      now: 'ឥឡូវ​នេះ',\n      backToToday: 'ត្រលប់ទៅថ្ងៃនេះ',\n      ok: 'កំណត់',\n      timeSelect: 'រយៈពេលជ្រើសរើស',\n      dateSelect: 'ជ្រើសរើសកាលបរិច្ឆេទ',\n      weekSelect: 'ជ្រើសរើសសប្តាហ៍',\n      clear: 'ច្បាស់',\n      month: 'ខែ',\n      year: 'ឆ្នាំ',\n      previousMonth: 'ខែមុន (ឡើងទំព័រ)',\n      nextMonth: 'ខែបន្ទាប់ (ប៊ូតុងចុះទំព័រ)',\n      monthSelect: 'ជ្រើសរើសខែ',\n      yearSelect: 'ជ្រើសរើសឆ្នាំ',\n      decadeSelect: 'ជ្រើសរើសអាយុ',\n      yearFormat: 'YYYY',\n      dayFormat: 'D',\n      dateFormat: 'YYYY-M-D',\n      dateTimeFormat: 'YYYY-M-D HH:mm:ss',\n      previousYear: 'ឆ្នាំមុន (Controlគ្រាប់ចុចបូកព្រួញខាងឆ្វេង)',\n      nextYear: 'ឆ្នាំក្រោយ (Control គ្រាប់ចុចបូកព្រួញស្ដាំ)',\n      previousDecade: 'ជំនាន់ចុងក្រោយ',\n      nextDecade: 'ជំនាន់​ក្រោយ',\n      previousCentury: 'សតវត្សចុងក្រោយ',\n      nextCentury: 'សតវត្សរ៍បន្ទាប់',\n      monthBeforeYear: true\n    },\n    timePickerLocale: {\n      placeholder: 'រើសម៉ោង',\n      rangePlaceholder: ['ម៉ោងចប់ផ្ដើម', 'ម៉ោងបញ្ចប់']\n    }\n  },\n  global: {\n    placeholder: 'សូមជ្រើសរើស'\n  },\n  Table: {\n    filterTitle: 'បញ្ចីតម្រៀប',\n    filterConfirm: 'យល់ព្រម',\n    filterReset: 'ត្រឡប់ដើម',\n    filterEmptyText: 'គ្មានបញ្ចីតម្រៀប',\n    emptyText: 'គ្មានទិន្នន័យ',\n    selectAll: 'រើសក្នុងទំព័រនេះ',\n    selectInvert: 'បញ្ច្រាសក្នុងទំព័រនេះ',\n    selectNone: 'លុបចេញទាំងអស់',\n    selectionAll: 'រើសយកទាំងអស់',\n    sortTitle: 'តម្រៀប',\n    expand: 'ពន្លាត',\n    collapse: 'បិតបាំង',\n    triggerDesc: 'ចុចដើម្បីរៀបតាមលំដាប់ធំ',\n    triggerAsc: 'ចុចដើម្បីរៀបតាមលំដាប់តូច​',\n    cancelSort: 'ចុចដើម្បីបោះបង់'\n  },\n  Modal: {\n    okText: 'យល់ព្រម',\n    cancelText: 'បោះបង់',\n    justOkText: 'យល់ព្រម'\n  },\n  Popconfirm: {\n    okText: 'យល់ព្រម',\n    cancelText: 'បោះបង់'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'ស្វែងរកនៅទីនេះ',\n    itemUnit: 'item',\n    itemsUnit: 'items',\n    remove: 'លុប',\n    selectCurrent: 'រើសទំព័របច្ចុប្បន្ន',\n    removeCurrent: 'លុបទំព័របច្ចុប្បន្ន',\n    selectAll: 'រើសទិន្នន័យទាំងអស់',\n    removeAll: 'លុបទិន្នន័យទាំងអស់',\n    selectInvert: 'បញ្ច្រាសទំព័របច្ចុប្បន្ន'\n  },\n  Upload: {\n    uploading: 'កំពុងបញ្ចូលឡើង...',\n    removeFile: 'លុបឯកសារ',\n    uploadError: 'បញ្ចូលមិនជោកជ័យ',\n    previewFile: 'មើលឯកសារ',\n    downloadFile: 'ទាញយកឯកសារ'\n  },\n  Empty: {\n    description: 'គ្មានទិន្នន័យ'\n  },\n  Icon: {\n    icon: 'icon'\n  },\n  Text: {\n    edit: 'កែ',\n    copy: 'Copy',\n    copied: 'Copied',\n    expand: 'ពង្រីក'\n  },\n  PageHeader: {\n    back: 'Back'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar kk_KZ = {\n  locale: 'kk',\n  Pagination: {\n    items_per_page: '/ бет',\n    jump_to: 'Секіру',\n    jump_to_confirm: 'Растау',\n    page: '',\n    prev_page: 'Артқа',\n    next_page: 'Алға',\n    prev_5: 'Алдыңғы 5',\n    next_5: 'Келесі 5',\n    prev_3: 'Алдыңғы 3',\n    next_3: 'Келесі 3',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Күнді таңдаңыз',\n      yearPlaceholder: 'Жылды таңдаңыз',\n      quarterPlaceholder: 'Тоқсанды таңдаңыз',\n      monthPlaceholder: 'Айды таңдаңыз',\n      weekPlaceholder: 'Аптаны таңдаңыз',\n      rangePlaceholder: ['Бастау күні', 'Аяқталу күні'],\n      rangeYearPlaceholder: ['Бастау жылы', 'Аяқталу жылы'],\n      rangeMonthPlaceholder: ['Бастау айы', 'Аяқталу айы'],\n      rangeWeekPlaceholder: ['Бастау апта', 'Аяқталу апта'],\n      locale: 'kk_KZ',\n      today: 'Бүгін',\n      now: 'Қазір',\n      backToToday: 'Ағымдағы күн',\n      ok: 'Таңдау',\n      clear: 'Таза',\n      month: 'Ай',\n      year: 'Жыл',\n      timeSelect: 'Уақытты таңдау',\n      dateSelect: 'Күнді таңдау',\n      monthSelect: 'Айды таңдаңыз',\n      yearSelect: 'Жылды таңдаңыз',\n      decadeSelect: 'Онжылды таңдаңыз',\n      yearFormat: 'YYYY',\n      dateFormat: 'D-M-YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D-M-YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Алдыңғы ай (PageUp)',\n      nextMonth: 'Келесі ай (PageDown)',\n      previousYear: 'Алдыңғы жыл (Control + left)',\n      nextYear: 'Келесі жыл (Control + right)',\n      previousDecade: 'Алдыңғы онжылдық',\n      nextDecade: 'Келесі онжылдық',\n      previousCentury: 'Алдыңғы ғасыр',\n      nextCentury: 'Келесі ғасыр'\n    },\n    timePickerLocale: {\n      placeholder: 'Уақытты таңдаңыз',\n      rangePlaceholder: ['Бастау уақыты', 'Аяқталу уақыты']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Уақытты таңдаңыз',\n    rangePlaceholder: ['Бастау уақыты', 'Аяқталу уақыты']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Күнді таңдаңыз',\n      yearPlaceholder: 'Жылды таңдаңыз',\n      quarterPlaceholder: 'Тоқсанды таңдаңыз',\n      monthPlaceholder: 'Айды таңдаңыз',\n      weekPlaceholder: 'Аптаны таңдаңыз',\n      rangePlaceholder: ['Бастау күні', 'Аяқталу күні'],\n      rangeYearPlaceholder: ['Бастау жылы', 'Аяқталу жылы'],\n      rangeMonthPlaceholder: ['Бастау айы', 'Аяқталу айы'],\n      rangeWeekPlaceholder: ['Бастау апта', 'Аяқталу апта'],\n      locale: 'kk_KZ',\n      today: 'Бүгін',\n      now: 'Қазір',\n      backToToday: 'Ағымдағы күн',\n      ok: 'Таңдау',\n      clear: 'Таза',\n      month: 'Ай',\n      year: 'Жыл',\n      timeSelect: 'Уақытты таңдау',\n      dateSelect: 'Күнді таңдау',\n      monthSelect: 'Айды таңдаңыз',\n      yearSelect: 'Жылды таңдаңыз',\n      decadeSelect: 'Онжылды таңдаңыз',\n      yearFormat: 'YYYY',\n      dateFormat: 'D-M-YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D-M-YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Алдыңғы ай (PageUp)',\n      nextMonth: 'Келесі ай (PageDown)',\n      previousYear: 'Алдыңғы жыл (Control + left)',\n      nextYear: 'Келесі жыл (Control + right)',\n      previousDecade: 'Алдыңғы онжылдық',\n      nextDecade: 'Келесі онжылдық',\n      previousCentury: 'Алдыңғы ғасыр',\n      nextCentury: 'Келесі ғасыр'\n    },\n    timePickerLocale: {\n      placeholder: 'Уақытты таңдаңыз',\n      rangePlaceholder: ['Бастау уақыты', 'Аяқталу уақыты']\n    }\n  },\n  global: {\n    placeholder: 'Таңдаңыз'\n  },\n  Table: {\n    filterTitle: 'Фильтр',\n    filterConfirm: 'OK',\n    filterReset: 'Тазарту',\n    filterEmptyText: 'Фильтр жоқ',\n    emptyText: 'Деректер жоқ',\n    selectAll: 'Барлығын таңдау',\n    selectInvert: 'Таңдауды төңкеру',\n    selectionAll: 'Барлық деректерді таңдаңыз',\n    sortTitle: 'Сұрыптау',\n    expand: 'Жолды жазу',\n    collapse: 'Жолды бүктеу',\n    triggerDesc: 'Төмендеуді сұрыптау үшін басыңыз',\n    triggerAsc: 'Өсу ретімен сұрыптау үшін басыңыз',\n    cancelSort: 'Сұрыптаудан бас тарту үшін басыңыз'\n  },\n  Modal: {\n    okText: 'Жарайды',\n    cancelText: 'Болдырмау',\n    justOkText: 'Жарайды'\n  },\n  Popconfirm: {\n    okText: 'Жарайды',\n    cancelText: 'Болдырмау'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Іздеу',\n    itemUnit: 'элемент.',\n    itemsUnit: 'элемент.',\n    remove: 'Жою',\n    selectAll: 'Барлық деректерді таңдау',\n    selectCurrent: 'Ағымдағы бетті таңдау',\n    selectInvert: 'Кері тәртіпте көрсету',\n    removeAll: 'Барлық деректерді жою',\n    removeCurrent: 'Ағымдағы парақты өшіру'\n  },\n  Upload: {\n    uploading: 'Жүктеу...',\n    removeFile: 'Файлды жою',\n    uploadError: 'Жүктеу кезінде қате пайда болды',\n    previewFile: 'Файлды алдын ала қарау',\n    downloadFile: 'Файлды жүктеу'\n  },\n  Empty: {\n    description: 'Деректер жоқ'\n  },\n  Icon: {\n    icon: 'белгішесі'\n  },\n  Text: {\n    edit: 'Өңдеу',\n    copy: 'Көшіру',\n    copied: 'Көшірілді',\n    expand: 'Жазу'\n  },\n  PageHeader: {\n    back: 'Артқа'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar kmr_IQ = {\n  locale: 'ku',\n  Pagination: {\n    items_per_page: '/ rûpel',\n    jump_to: 'Biçe',\n    jump_to_confirm: 'piştrast bike',\n    page: '',\n    prev_page: 'Rûpelê Pêş',\n    next_page: 'Rûpelê Paş',\n    prev_5: '5 Rûpelên Pêş',\n    next_5: '5 Rûpelên Paş',\n    prev_3: '3 Rûpelên Pêş',\n    next_3: '3 Rûpelên Paş',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Dîrok hilbijêre',\n      rangePlaceholder: ['Dîroka destpêkê', 'Dîroka dawîn'],\n      locale: 'ku',\n      today: 'Îro',\n      now: 'Niha',\n      backToToday: 'Vegere îro',\n      ok: 'Temam',\n      clear: 'Paqij bike',\n      month: 'Meh',\n      year: 'Sal',\n      timeSelect: 'Demê hilbijêre',\n      dateSelect: 'Dîrok hilbijêre',\n      monthSelect: 'Meh hilbijêre',\n      yearSelect: 'Sal hilbijêre',\n      decadeSelect: 'Dehsal hilbijêre',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Meha peş (PageUp))',\n      nextMonth: 'Meha paş (PageDown)',\n      previousYear: 'Sala peş (Control + şep)',\n      nextYear: 'Sala paş (Control + rast)',\n      previousDecade: 'Dehsalen peş',\n      nextDecade: 'Dehsalen paş',\n      previousCentury: 'Sedsalen peş',\n      nextCentury: 'Sedsalen paş'\n    },\n    timePickerLocale: {\n      placeholder: 'Demê hilbijêre'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Demê hilbijêre'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Dîrok hilbijêre',\n      rangePlaceholder: ['Dîroka destpêkê', 'Dîroka dawîn'],\n      locale: 'ku',\n      today: 'Îro',\n      now: 'Niha',\n      backToToday: 'Vegere îro',\n      ok: 'Temam',\n      clear: 'Paqij bike',\n      month: 'Meh',\n      year: 'Sal',\n      timeSelect: 'Demê hilbijêre',\n      dateSelect: 'Dîrok hilbijêre',\n      monthSelect: 'Meh hilbijêre',\n      yearSelect: 'Sal hilbijêre',\n      decadeSelect: 'Dehsal hilbijêre',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Meha peş (PageUp))',\n      nextMonth: 'Meha paş (PageDown)',\n      previousYear: 'Sala peş (Control + şep)',\n      nextYear: 'Sala paş (Control + rast)',\n      previousDecade: 'Dehsalen peş',\n      nextDecade: 'Dehsalen paş',\n      previousCentury: 'Sedsalen peş',\n      nextCentury: 'Sedsalen paş'\n    },\n    timePickerLocale: {\n      placeholder: 'Demê hilbijêre'\n    }\n  },\n  Table: {\n    filterTitle: 'Menuê peldanka',\n    filterConfirm: 'Temam',\n    filterReset: 'Jê bibe',\n    selectAll: 'Hemî hilbijêre',\n    selectInvert: 'Hilbijartinan veguhere'\n  },\n  Modal: {\n    okText: 'Temam',\n    cancelText: 'Betal ke',\n    justOkText: 'Temam'\n  },\n  Popconfirm: {\n    okText: 'Temam',\n    cancelText: 'Betal ke'\n  },\n  Transfer: {\n    searchPlaceholder: 'Lêgerîn',\n    itemUnit: 'tişt',\n    itemsUnit: 'tişt'\n  },\n  Upload: {\n    uploading: 'Bardike...',\n    removeFile: 'Pelê rabike',\n    uploadError: 'Xeta barkirine',\n    previewFile: 'Pelê pêşbibîne',\n    downloadFile: 'Pelê dakêşin'\n  },\n  Empty: {\n    description: 'Agahî tune'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar kn_IN = {\n  locale: 'kn',\n  Pagination: {\n    items_per_page: '/ ಪುಟ',\n    jump_to: 'ಜಿಗಿತವನ್ನು',\n    jump_to_confirm: 'ಖಚಿತಪಡಿಸಲು ಜಿಗಿತವನ್ನು',\n    page: '',\n    prev_page: 'ಹಿಂದಿನ ಪುಟ',\n    next_page: 'ಮುಂದಿನ ಪುಟ',\n    prev_5: 'ಹಿಂದಿನ 5 ಪುಟಗಳು',\n    next_5: 'ಮುಂದಿನ 5 ಪುಟಗಳು',\n    prev_3: 'ಹಿಂದಿನ 3 ಪುಟಗಳು',\n    next_3: 'ಮುಂದಿನ 3 ಪುಟಗಳು',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'ದಿನಾಂಕ ಆಯ್ಕೆಮಾಡಿ',\n      rangePlaceholder: ['ಪ್ರಾರಂಭ ದಿನಾಂಕ', 'ಅಂತಿಮ ದಿನಾಂಕ'],\n      locale: 'kn_IN',\n      today: 'ಇಂದು',\n      now: 'ಈಗ',\n      backToToday: 'ಇಂದು ಹಿಂದಿರುಗಿ',\n      ok: 'ಸರಿ',\n      clear: 'ಸ್ಪಷ್ಟ',\n      month: 'ತಿಂಗಳು',\n      year: 'ವರ್ಷ',\n      timeSelect: 'ಸಮಯ ಆಯ್ಕೆಮಾಡಿ',\n      dateSelect: 'ದಿನಾಂಕವನ್ನು ಆಯ್ಕೆ ಮಾಡಿ',\n      weekSelect: 'ಒಂದು ವಾರದ ಆರಿಸಿ',\n      monthSelect: 'ಒಂದು ತಿಂಗಳು ಆಯ್ಕೆಮಾಡಿ',\n      yearSelect: 'ಒಂದು ವರ್ಷ ಆರಿಸಿ',\n      decadeSelect: 'ಒಂದು ದಶಕದ ಆಯ್ಕೆಮಾಡಿ',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'ಹಿಂದಿನ ತಿಂಗಳು (ಪೇಜ್ಅಪ್)',\n      nextMonth: 'ಮುಂದಿನ ತಿಂಗಳು (ಪೇಜ್ಡೌನ್)',\n      previousYear: 'ಕಳೆದ ವರ್ಷ (Ctrl + ಎಡ)',\n      nextYear: 'ಮುಂದಿನ ವರ್ಷ (Ctrl + ಬಲ)',\n      previousDecade: 'ಕಳೆದ ದಶಕ',\n      nextDecade: 'ಮುಂದಿನ ದಶಕ',\n      previousCentury: 'ಕಳೆದ ಶತಮಾನ',\n      nextCentury: 'ಮುಂದಿನ ಶತಮಾನ'\n    },\n    timePickerLocale: {\n      placeholder: 'ಸಮಯ ಆಯ್ಕೆಮಾಡಿ'\n    }\n  },\n  TimePicker: {\n    placeholder: 'ಸಮಯ ಆಯ್ಕೆಮಾಡಿ'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'ದಿನಾಂಕ ಆಯ್ಕೆಮಾಡಿ',\n      rangePlaceholder: ['ಪ್ರಾರಂಭ ದಿನಾಂಕ', 'ಅಂತಿಮ ದಿನಾಂಕ'],\n      locale: 'kn_IN',\n      today: 'ಇಂದು',\n      now: 'ಈಗ',\n      backToToday: 'ಇಂದು ಹಿಂದಿರುಗಿ',\n      ok: 'ಸರಿ',\n      clear: 'ಸ್ಪಷ್ಟ',\n      month: 'ತಿಂಗಳು',\n      year: 'ವರ್ಷ',\n      timeSelect: 'ಸಮಯ ಆಯ್ಕೆಮಾಡಿ',\n      dateSelect: 'ದಿನಾಂಕವನ್ನು ಆಯ್ಕೆ ಮಾಡಿ',\n      weekSelect: 'ಒಂದು ವಾರದ ಆರಿಸಿ',\n      monthSelect: 'ಒಂದು ತಿಂಗಳು ಆಯ್ಕೆಮಾಡಿ',\n      yearSelect: 'ಒಂದು ವರ್ಷ ಆರಿಸಿ',\n      decadeSelect: 'ಒಂದು ದಶಕದ ಆಯ್ಕೆಮಾಡಿ',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'ಹಿಂದಿನ ತಿಂಗಳು (ಪೇಜ್ಅಪ್)',\n      nextMonth: 'ಮುಂದಿನ ತಿಂಗಳು (ಪೇಜ್ಡೌನ್)',\n      previousYear: 'ಕಳೆದ ವರ್ಷ (Ctrl + ಎಡ)',\n      nextYear: 'ಮುಂದಿನ ವರ್ಷ (Ctrl + ಬಲ)',\n      previousDecade: 'ಕಳೆದ ದಶಕ',\n      nextDecade: 'ಮುಂದಿನ ದಶಕ',\n      previousCentury: 'ಕಳೆದ ಶತಮಾನ',\n      nextCentury: 'ಮುಂದಿನ ಶತಮಾನ'\n    },\n    timePickerLocale: {\n      placeholder: 'ಸಮಯ ಆಯ್ಕೆಮಾಡಿ'\n    }\n  },\n  global: {\n    placeholder: 'ದಯವಿಟ್ಟು ಆರಿಸಿ'\n  },\n  Table: {\n    filterTitle: 'ಪಟ್ಟಿ ಸೋಸಿ',\n    filterConfirm: 'ಸರಿ',\n    filterReset: 'ಮರುಹೊಂದಿಸಿ',\n    emptyText: 'ಮಾಹಿತಿ ಇಲ್ಲ',\n    selectAll: 'ಪ್ರಸ್ತುತ ಪುಟವನ್ನು ಆಯ್ಕೆಮಾಡಿ',\n    selectInvert: 'ಪ್ರಸ್ತುತ ಪುಟವನ್ನು ತಿರುಗಿಸಿ',\n    sortTitle: 'ವಿಂಗಡಿಸಿ'\n  },\n  Modal: {\n    okText: 'ಸರಿ',\n    cancelText: 'ರದ್ದು',\n    justOkText: 'ಸರಿ'\n  },\n  Popconfirm: {\n    okText: 'ಸರಿ',\n    cancelText: 'ರದ್ದು'\n  },\n  Transfer: {\n    titles: ['', ''],\n    notFoundContent: 'ದೊರೆತಿಲ್ಲ',\n    searchPlaceholder: 'ಇಲ್ಲಿ ಹುಡುಕಿ',\n    itemUnit: 'ವಿಷಯ',\n    itemsUnit: 'ವಿಷಯಗಳು'\n  },\n  Select: {\n    notFoundContent: 'ದೊರೆತಿಲ್ಲ'\n  },\n  Upload: {\n    uploading: 'ಏರಿಸಿ...',\n    removeFile: 'ಫೈಲ್ ತೆಗೆದುಹಾಕಿ',\n    uploadError: 'ಏರಿಸುವ ದೋಷ',\n    previewFile: 'ಫೈಲ್ ಮುನ್ನೋಟ',\n    downloadFile: 'ಫೈಲ್ ಡೌನ್‌ಲೋಡ್ ಮಾಡಿ'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ko_KR = {\n  locale: 'ko',\n  Pagination: {\n    items_per_page: '/ 쪽',\n    jump_to: '이동하기',\n    jump_to_confirm: '확인하다',\n    page: '페이지',\n    prev_page: '이전 페이지',\n    next_page: '다음 페이지',\n    prev_5: '이전 5 페이지',\n    next_5: '다음 5 페이지',\n    prev_3: '이전 3 페이지',\n    next_3: '다음 3 페이지',\n    page_size: '페이지 크기'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: '날짜 선택',\n      rangePlaceholder: ['시작일', '종료일'],\n      locale: 'ko_KR',\n      today: '오늘',\n      now: '현재 시각',\n      backToToday: '오늘로 돌아가기',\n      ok: '확인',\n      clear: '지우기',\n      month: '월',\n      year: '년',\n      timeSelect: '시간 선택',\n      dateSelect: '날짜 선택',\n      monthSelect: '달 선택',\n      yearSelect: '연 선택',\n      decadeSelect: '연대 선택',\n      yearFormat: 'YYYY년',\n      dateFormat: 'YYYY-MM-DD',\n      dayFormat: 'Do',\n      dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',\n      monthBeforeYear: false,\n      previousMonth: '이전 달 (PageUp)',\n      nextMonth: '다음 달 (PageDown)',\n      previousYear: '이전 해 (Control + left)',\n      nextYear: '다음 해 (Control + right)',\n      previousDecade: '이전 연대',\n      nextDecade: '다음 연대',\n      previousCentury: '이전 세기',\n      nextCentury: '다음 세기'\n    },\n    timePickerLocale: {\n      placeholder: '시간 선택',\n      rangePlaceholder: ['시작 시간', '종료 시간']\n    }\n  },\n  TimePicker: {\n    placeholder: '시간 선택',\n    rangePlaceholder: ['시작 시간', '종료 시간']\n  },\n  Calendar: {\n    lang: {\n      placeholder: '날짜 선택',\n      rangePlaceholder: ['시작일', '종료일'],\n      locale: 'ko_KR',\n      today: '오늘',\n      now: '현재 시각',\n      backToToday: '오늘로 돌아가기',\n      ok: '확인',\n      clear: '지우기',\n      month: '월',\n      year: '년',\n      timeSelect: '시간 선택',\n      dateSelect: '날짜 선택',\n      monthSelect: '달 선택',\n      yearSelect: '연 선택',\n      decadeSelect: '연대 선택',\n      yearFormat: 'YYYY년',\n      dateFormat: 'YYYY-MM-DD',\n      dayFormat: 'Do',\n      dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',\n      monthBeforeYear: false,\n      previousMonth: '이전 달 (PageUp)',\n      nextMonth: '다음 달 (PageDown)',\n      previousYear: '이전 해 (Control + left)',\n      nextYear: '다음 해 (Control + right)',\n      previousDecade: '이전 연대',\n      nextDecade: '다음 연대',\n      previousCentury: '이전 세기',\n      nextCentury: '다음 세기'\n    },\n    timePickerLocale: {\n      placeholder: '시간 선택',\n      rangePlaceholder: ['시작 시간', '종료 시간']\n    }\n  },\n  Table: {\n    filterTitle: '필터 메뉴',\n    filterConfirm: '확인',\n    filterReset: '초기화',\n    selectAll: '모두 선택',\n    selectInvert: '선택 반전',\n    filterEmptyText: '필터 없음',\n    emptyText: '데이터 없음'\n  },\n  Modal: {\n    okText: '확인',\n    cancelText: '취소',\n    justOkText: '확인'\n  },\n  Popconfirm: {\n    okText: '확인',\n    cancelText: '취소'\n  },\n  Transfer: {\n    searchPlaceholder: '여기에 검색하세요',\n    itemUnit: '개',\n    itemsUnit: '개'\n  },\n  Upload: {\n    uploading: '업로드 중...',\n    removeFile: '파일 삭제',\n    uploadError: '업로드 실패',\n    previewFile: '파일 미리보기',\n    downloadFile: '파일 다운로드'\n  },\n  Empty: {\n    description: '데이터 없음'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ku_IQ = {\n  locale: 'ku-iq',\n  Pagination: {\n    items_per_page: '/ rûpel',\n    jump_to: 'Biçe',\n    jump_to_confirm: 'piştrast bike',\n    page: '',\n    prev_page: 'Rûpelê Pêş',\n    next_page: 'Rûpelê Paş',\n    prev_5: '5 Rûpelên Pêş',\n    next_5: '5 Rûpelên Paş',\n    prev_3: '3 Rûpelên Pêş',\n    next_3: '3 Rûpelên Paş',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Dîrok hilbijêre',\n      rangePlaceholder: ['Dîroka destpêkê', 'Dîroka dawîn'],\n      locale: 'ku',\n      today: 'Îro',\n      now: 'Niha',\n      backToToday: 'Vegere îro',\n      ok: 'Temam',\n      clear: 'Paqij bike',\n      month: 'Meh',\n      year: 'Sal',\n      timeSelect: 'Demê hilbijêre',\n      dateSelect: 'Dîrok hilbijêre',\n      monthSelect: 'Meh hilbijêre',\n      yearSelect: 'Sal hilbijêre',\n      decadeSelect: 'Dehsal hilbijêre',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Meha peş (PageUp))',\n      nextMonth: 'Meha paş (PageDown)',\n      previousYear: 'Sala peş (Control + şep)',\n      nextYear: 'Sala paş (Control + rast)',\n      previousDecade: 'Dehsalen peş',\n      nextDecade: 'Dehsalen paş',\n      previousCentury: 'Sedsalen peş',\n      nextCentury: 'Sedsalen paş'\n    },\n    timePickerLocale: {\n      placeholder: 'Demê hilbijêre'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Demê hilbijêre'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Dîrok hilbijêre',\n      rangePlaceholder: ['Dîroka destpêkê', 'Dîroka dawîn'],\n      locale: 'ku',\n      today: 'Îro',\n      now: 'Niha',\n      backToToday: 'Vegere îro',\n      ok: 'Temam',\n      clear: 'Paqij bike',\n      month: 'Meh',\n      year: 'Sal',\n      timeSelect: 'Demê hilbijêre',\n      dateSelect: 'Dîrok hilbijêre',\n      monthSelect: 'Meh hilbijêre',\n      yearSelect: 'Sal hilbijêre',\n      decadeSelect: 'Dehsal hilbijêre',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Meha peş (PageUp))',\n      nextMonth: 'Meha paş (PageDown)',\n      previousYear: 'Sala peş (Control + şep)',\n      nextYear: 'Sala paş (Control + rast)',\n      previousDecade: 'Dehsalen peş',\n      nextDecade: 'Dehsalen paş',\n      previousCentury: 'Sedsalen peş',\n      nextCentury: 'Sedsalen paş'\n    },\n    timePickerLocale: {\n      placeholder: 'Demê hilbijêre'\n    }\n  },\n  Table: {\n    filterTitle: 'Menuê peldanka',\n    filterConfirm: 'Temam',\n    filterReset: 'Jê bibe',\n    selectAll: 'Hemî hilbijêre',\n    selectInvert: 'Hilbijartinan veguhere'\n  },\n  Modal: {\n    okText: 'Temam',\n    cancelText: 'Betal ke',\n    justOkText: 'Temam'\n  },\n  Popconfirm: {\n    okText: 'Temam',\n    cancelText: 'Betal ke'\n  },\n  Transfer: {\n    searchPlaceholder: 'Lêgerîn',\n    itemUnit: 'tişt',\n    itemsUnit: 'tişt'\n  },\n  Upload: {\n    uploading: 'Bardike...',\n    removeFile: 'Pelê rabike',\n    uploadError: 'Xeta barkirine',\n    previewFile: 'Pelê pêşbibîne',\n    downloadFile: 'Pelê dakêşin'\n  },\n  Empty: {\n    description: 'Agahî tune'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar lt_LT = {\n  locale: 'lt',\n  Pagination: {\n    items_per_page: '/ psl.',\n    jump_to: 'Pereiti',\n    jump_to_confirm: 'patvirtinti',\n    page: '',\n    prev_page: 'Atgal',\n    next_page: 'Pirmyn',\n    prev_5: 'Grįžti 5 pls.',\n    next_5: 'Peršokti 5 pls.',\n    prev_3: 'Grįžti 3 pls.',\n    next_3: 'Peršokti 3 pls.',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Pasirinkite datą',\n      yearPlaceholder: 'Pasirinkite metus',\n      quarterPlaceholder: 'Pasirinkite ketvirtį',\n      monthPlaceholder: 'Pasirinkite mėnesį',\n      weekPlaceholder: 'Pasirinkite savaitę',\n      rangePlaceholder: ['Pradžios data', 'Pabaigos data'],\n      rangeYearPlaceholder: ['Pradžios metai', 'Pabaigos metai'],\n      rangeMonthPlaceholder: ['Pradžios mėnesis', 'Pabaigos mėnesis'],\n      rangeWeekPlaceholder: ['Pradžios savaitė', 'Pabaigos savaitė'],\n      locale: 'lt_LT',\n      today: 'Šiandien',\n      now: 'Dabar',\n      backToToday: 'Rodyti šiandien',\n      ok: 'Gerai',\n      clear: 'Išvalyti',\n      month: 'Mėnesis',\n      year: 'Metai',\n      timeSelect: 'Pasirinkti laiką',\n      dateSelect: 'Pasirinkti datą',\n      monthSelect: 'Pasirinkti mėnesį',\n      yearSelect: 'Pasirinkti metus',\n      decadeSelect: 'Pasirinkti dešimtmetį',\n      yearFormat: 'YYYY',\n      dateFormat: 'YYYY-MM-DD',\n      dayFormat: 'DD',\n      dateTimeFormat: 'YYYY-MM-DD HH:MM:SS',\n      monthBeforeYear: true,\n      previousMonth: 'Buvęs mėnesis (PageUp)',\n      nextMonth: 'Sekantis mėnesis (PageDown)',\n      previousYear: 'Buvę metai (Control + left)',\n      nextYear: 'Sekantis metai (Control + right)',\n      previousDecade: 'Buvęs dešimtmetis',\n      nextDecade: 'Sekantis dešimtmetis',\n      previousCentury: 'Buvęs amžius',\n      nextCentury: 'Sekantis amžius'\n    },\n    timePickerLocale: {\n      placeholder: 'Pasirinkite laiką',\n      rangePlaceholder: ['Pradžios laikas', 'Pabaigos laikas']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Pasirinkite laiką',\n    rangePlaceholder: ['Pradžios laikas', 'Pabaigos laikas']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Pasirinkite datą',\n      yearPlaceholder: 'Pasirinkite metus',\n      quarterPlaceholder: 'Pasirinkite ketvirtį',\n      monthPlaceholder: 'Pasirinkite mėnesį',\n      weekPlaceholder: 'Pasirinkite savaitę',\n      rangePlaceholder: ['Pradžios data', 'Pabaigos data'],\n      rangeYearPlaceholder: ['Pradžios metai', 'Pabaigos metai'],\n      rangeMonthPlaceholder: ['Pradžios mėnesis', 'Pabaigos mėnesis'],\n      rangeWeekPlaceholder: ['Pradžios savaitė', 'Pabaigos savaitė'],\n      locale: 'lt_LT',\n      today: 'Šiandien',\n      now: 'Dabar',\n      backToToday: 'Rodyti šiandien',\n      ok: 'Gerai',\n      clear: 'Išvalyti',\n      month: 'Mėnesis',\n      year: 'Metai',\n      timeSelect: 'Pasirinkti laiką',\n      dateSelect: 'Pasirinkti datą',\n      monthSelect: 'Pasirinkti mėnesį',\n      yearSelect: 'Pasirinkti metus',\n      decadeSelect: 'Pasirinkti dešimtmetį',\n      yearFormat: 'YYYY',\n      dateFormat: 'YYYY-MM-DD',\n      dayFormat: 'DD',\n      dateTimeFormat: 'YYYY-MM-DD HH:MM:SS',\n      monthBeforeYear: true,\n      previousMonth: 'Buvęs mėnesis (PageUp)',\n      nextMonth: 'Sekantis mėnesis (PageDown)',\n      previousYear: 'Buvę metai (Control + left)',\n      nextYear: 'Sekantis metai (Control + right)',\n      previousDecade: 'Buvęs dešimtmetis',\n      nextDecade: 'Sekantis dešimtmetis',\n      previousCentury: 'Buvęs amžius',\n      nextCentury: 'Sekantis amžius'\n    },\n    timePickerLocale: {\n      placeholder: 'Pasirinkite laiką',\n      rangePlaceholder: ['Pradžios laikas', 'Pabaigos laikas']\n    }\n  },\n  Table: {\n    filterTitle: 'Filtras',\n    filterConfirm: 'Gerai',\n    filterReset: 'Atstatyti',\n    filterEmptyText: 'Be filtrų',\n    emptyText: 'Nėra duomenų',\n    selectAll: 'Pasirinkti viską',\n    selectInvert: 'Apversti pasirinkimą',\n    selectionAll: 'Rinktis visus',\n    sortTitle: 'Rikiavimas',\n    expand: 'Išskleisti',\n    collapse: 'Suskleisti',\n    triggerDesc: 'Spustelėkite norėdami rūšiuoti mažėjančia tvarka',\n    triggerAsc: 'Spustelėkite norėdami rūšiuoti didėjančia tvarka',\n    cancelSort: 'Spustelėkite, kad atšauktumėte rūšiavimą'\n  },\n  Modal: {\n    okText: 'Taip',\n    cancelText: 'Atšaukti',\n    justOkText: 'Gerai'\n  },\n  Popconfirm: {\n    okText: 'Taip',\n    cancelText: 'Atšaukti'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Paieška',\n    itemUnit: 'vnt.',\n    itemsUnit: 'vnt.',\n    remove: 'Pašalinti',\n    selectAll: 'Pasirinkti visus',\n    selectCurrent: 'Pasirinkite dabartinį puslapį',\n    selectInvert: 'Atkeist pasirinkimą',\n    removeAll: 'Ištrinti visus duomenis',\n    removeCurrent: 'Ištrinti dabartinį puslapį'\n  },\n  Upload: {\n    uploading: 'Gaunami duomenys...',\n    removeFile: 'Ištrinti failą',\n    uploadError: 'Įkeliant įvyko klaida',\n    previewFile: 'Failo peržiūra',\n    downloadFile: 'Įkelti failą'\n  },\n  Empty: {\n    description: 'Nėra duomenų'\n  },\n  Icon: {\n    icon: 'piktograma'\n  },\n  Text: {\n    edit: 'Redaguoti',\n    copy: 'Kopijuoti',\n    copied: 'Nukopijuota',\n    expand: 'Plačiau'\n  },\n  PageHeader: {\n    back: 'Atgal'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar lv_LV = {\n  locale: 'lv',\n  Pagination: {\n    items_per_page: '/ lappuse',\n    jump_to: 'iet uz',\n    jump_to_confirm: 'apstiprināt',\n    page: '',\n    prev_page: 'Iepriekšējā lapa',\n    next_page: 'Nākamā lapaspuse',\n    prev_5: 'Iepriekšējās 5 lapas',\n    next_5: 'Nākamās 5 lapas',\n    prev_3: 'Iepriekšējās 3 lapas',\n    next_3: 'Nākamās 3 lapas',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Izvēlieties datumu',\n      rangePlaceholder: ['Sākuma datums', 'Beigu datums'],\n      locale: 'lv_LV',\n      today: 'Šodien',\n      now: 'Tagad',\n      backToToday: 'Atpakaļ pie šodienas',\n      ok: 'Ok',\n      clear: 'Skaidrs',\n      month: 'Mēnesis',\n      year: 'Gads',\n      timeSelect: 'Izvēlieties laiku',\n      dateSelect: 'Izvēlieties datumu',\n      monthSelect: 'Izvēlieties mēnesi',\n      yearSelect: 'Izvēlieties gadu',\n      decadeSelect: 'Izvēlieties desmit gadus',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Iepriekšējais mēnesis (PageUp)',\n      nextMonth: 'Nākammēnes (PageDown)',\n      previousYear: 'Pagājušais gads (Control + left)',\n      nextYear: 'Nākamgad (Control + right)',\n      previousDecade: 'Pēdējā desmitgadē',\n      nextDecade: 'Nākamā desmitgade',\n      previousCentury: 'Pagājušajā gadsimtā',\n      nextCentury: 'Nākamajā gadsimtā'\n    },\n    timePickerLocale: {\n      placeholder: 'Izvēlieties laiku'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Izvēlieties laiku'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Izvēlieties datumu',\n      rangePlaceholder: ['Sākuma datums', 'Beigu datums'],\n      locale: 'lv_LV',\n      today: 'Šodien',\n      now: 'Tagad',\n      backToToday: 'Atpakaļ pie šodienas',\n      ok: 'Ok',\n      clear: 'Skaidrs',\n      month: 'Mēnesis',\n      year: 'Gads',\n      timeSelect: 'Izvēlieties laiku',\n      dateSelect: 'Izvēlieties datumu',\n      monthSelect: 'Izvēlieties mēnesi',\n      yearSelect: 'Izvēlieties gadu',\n      decadeSelect: 'Izvēlieties desmit gadus',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Iepriekšējais mēnesis (PageUp)',\n      nextMonth: 'Nākammēnes (PageDown)',\n      previousYear: 'Pagājušais gads (Control + left)',\n      nextYear: 'Nākamgad (Control + right)',\n      previousDecade: 'Pēdējā desmitgadē',\n      nextDecade: 'Nākamā desmitgade',\n      previousCentury: 'Pagājušajā gadsimtā',\n      nextCentury: 'Nākamajā gadsimtā'\n    },\n    timePickerLocale: {\n      placeholder: 'Izvēlieties laiku'\n    }\n  },\n  Table: {\n    filterTitle: 'Filtrēšanas izvēlne',\n    filterConfirm: 'OK',\n    filterReset: 'Atiestatīt',\n    selectAll: 'Atlasiet pašreizējo lapu',\n    selectInvert: 'Pārvērst pašreizējo lapu'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Atcelt',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Atcelt'\n  },\n  Transfer: {\n    searchPlaceholder: 'Meklēt šeit',\n    itemUnit: 'vienumu',\n    itemsUnit: 'vienumus'\n  },\n  Upload: {\n    uploading: 'Augšupielāde...',\n    removeFile: 'Noņemt failu',\n    uploadError: 'Augšupielādes kļūda',\n    previewFile: 'Priekšskatiet failu',\n    downloadFile: 'Lejupielādēt failu'\n  },\n  Empty: {\n    description: 'Nav datu'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar mk_MK = {\n  locale: 'mk',\n  Pagination: {\n    items_per_page: '/ стр',\n    jump_to: 'Оди на',\n    jump_to_confirm: 'потврди',\n    page: '',\n    prev_page: 'Претходна страница',\n    next_page: 'Наредна страница',\n    prev_5: 'Претходни 5 страници',\n    next_5: 'Наредни 5 страници',\n    prev_3: 'Претходни 3 страници',\n    next_3: 'Наредни 3 страници',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Избери датум',\n      rangePlaceholder: ['Од датум', 'До датум'],\n      locale: 'mk_MK',\n      today: 'Денес',\n      now: 'Сега',\n      backToToday: 'Назад до денес',\n      ok: 'ОК',\n      clear: 'Избриши',\n      month: 'Месец',\n      year: 'Година',\n      timeSelect: 'Избери време',\n      dateSelect: 'Избери датум',\n      monthSelect: 'Избери месец',\n      yearSelect: 'Избери година',\n      decadeSelect: 'Избери деценија',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Претходен месец (PageUp)',\n      nextMonth: 'Нареден месец (PageDown)',\n      previousYear: 'Претходна година (Control + left)',\n      nextYear: 'Наредна година (Control + right)',\n      previousDecade: 'Претходна деценија',\n      nextDecade: 'Наредна деценија',\n      previousCentury: 'Претходен век',\n      nextCentury: 'Нареден век'\n    },\n    timePickerLocale: {\n      placeholder: 'Избери време'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Избери време'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Избери датум',\n      rangePlaceholder: ['Од датум', 'До датум'],\n      locale: 'mk_MK',\n      today: 'Денес',\n      now: 'Сега',\n      backToToday: 'Назад до денес',\n      ok: 'ОК',\n      clear: 'Избриши',\n      month: 'Месец',\n      year: 'Година',\n      timeSelect: 'Избери време',\n      dateSelect: 'Избери датум',\n      monthSelect: 'Избери месец',\n      yearSelect: 'Избери година',\n      decadeSelect: 'Избери деценија',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Претходен месец (PageUp)',\n      nextMonth: 'Нареден месец (PageDown)',\n      previousYear: 'Претходна година (Control + left)',\n      nextYear: 'Наредна година (Control + right)',\n      previousDecade: 'Претходна деценија',\n      nextDecade: 'Наредна деценија',\n      previousCentury: 'Претходен век',\n      nextCentury: 'Нареден век'\n    },\n    timePickerLocale: {\n      placeholder: 'Избери време'\n    }\n  },\n  global: {\n    placeholder: 'Ве молиме означете'\n  },\n  Table: {\n    filterTitle: 'Мени за филтрирање',\n    filterConfirm: 'ОК',\n    filterReset: 'Избриши',\n    selectAll: 'Одбери страница',\n    selectInvert: 'Инвертирај страница'\n  },\n  Modal: {\n    okText: 'ОК',\n    cancelText: 'Откажи',\n    justOkText: 'ОК'\n  },\n  Popconfirm: {\n    okText: 'ОК',\n    cancelText: 'Откажи'\n  },\n  Transfer: {\n    searchPlaceholder: 'Пребарај тука',\n    itemUnit: 'предмет',\n    itemsUnit: 'предмети'\n  },\n  Upload: {\n    uploading: 'Се прикачува...',\n    removeFile: 'Избриши фајл',\n    uploadError: 'Грешка при прикачување',\n    previewFile: 'Прикажи фајл',\n    downloadFile: 'Преземи фајл'\n  },\n  Empty: {\n    description: 'Нема податоци'\n  },\n  Icon: {\n    icon: 'Икона'\n  },\n  Text: {\n    edit: 'Уреди',\n    copy: 'Копирај',\n    copied: 'Копирано',\n    expand: 'Зголеми'\n  },\n  PageHeader: {\n    back: 'Назад'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ml_IN = {\n  locale: 'ml',\n  Pagination: {\n    items_per_page: '/ പേജ്',\n    jump_to: 'അടുത്തത്',\n    jump_to_confirm: 'ഉറപ്പാക്കുക',\n    page: '',\n    prev_page: 'മുൻപുള്ള പേജ്',\n    next_page: 'അടുത്ത പേജ്',\n    prev_5: 'മുൻപുള്ള 5 പേജുകൾ',\n    next_5: 'അടുത്ത 5 പേജുകൾ',\n    prev_3: 'മുൻപുള്ള 3 പേജുകൾ',\n    next_3: 'അടുത്ത 3 പേജുകൾ',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'തിയതി തിരഞ്ഞെടുക്കുക',\n      yearPlaceholder: 'വർഷം തിരഞ്ഞെടുക്കുക',\n      quarterPlaceholder: 'ത്രൈമാസം തിരഞ്ഞെടുക്കുക',\n      monthPlaceholder: 'മാസം തിരഞ്ഞെടുക്കുക',\n      weekPlaceholder: 'വാരം തിരഞ്ഞെടുക്കുക',\n      rangePlaceholder: ['ആരംഭ ദിനം', 'അവസാന ദിനം'],\n      rangeYearPlaceholder: ['ആരംഭ വർഷം', 'അവസാന വർഷം'],\n      rangeMonthPlaceholder: ['ആരംഭ മാസം', 'അവസാന മാസം'],\n      rangeWeekPlaceholder: ['ആരംഭ വാരം', 'അവസാന വാരം'],\n      locale: 'ml_IN',\n      today: 'ഇന്ന്',\n      now: 'ഇപ്പോൾ',\n      backToToday: 'ഇന്നത്തെ ദിവസത്തിലേക്ക് തിരിച്ചു പോകുക',\n      ok: 'ശരിയാണ്',\n      clear: 'നീക്കം ചെയ്യുക',\n      month: 'മാസം',\n      year: 'വർഷം',\n      timeSelect: 'സമയം തിരഞ്ഞെടുക്കുക',\n      dateSelect: 'ദിവസം തിരഞ്ഞെടുക്കുക',\n      weekSelect: 'വാരം തിരഞ്ഞെടുക്കുക',\n      monthSelect: 'മാസം തിരഞ്ഞെടുക്കുക',\n      yearSelect: 'വർഷം തിരഞ്ഞെടുക്കുക',\n      decadeSelect: 'ദശാബ്ദം തിരഞ്ഞെടുക്കുക',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'കഴിഞ്ഞ മാസം (PageUp)',\n      nextMonth: 'അടുത്ത മാസം (PageDown)',\n      previousYear: 'കഴിഞ്ഞ വർഷം (Control + left)',\n      nextYear: 'അടുത്ത വർഷം (Control + right)',\n      previousDecade: 'കഴിഞ്ഞ ദശാബ്ദം',\n      nextDecade: 'അടുത്ത ദശാബ്ദം',\n      previousCentury: 'കഴിഞ്ഞ നൂറ്റാണ്ട്',\n      nextCentury: 'അടുത്ത നൂറ്റാണ്ട്'\n    },\n    timePickerLocale: {\n      placeholder: 'സമയം തിരഞ്ഞെടുക്കുക',\n      rangePlaceholder: ['ആരംഭ സമയം', 'അവസാന സമയം']\n    }\n  },\n  TimePicker: {\n    placeholder: 'സമയം തിരഞ്ഞെടുക്കുക',\n    rangePlaceholder: ['ആരംഭ സമയം', 'അവസാന സമയം']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'തിയതി തിരഞ്ഞെടുക്കുക',\n      yearPlaceholder: 'വർഷം തിരഞ്ഞെടുക്കുക',\n      quarterPlaceholder: 'ത്രൈമാസം തിരഞ്ഞെടുക്കുക',\n      monthPlaceholder: 'മാസം തിരഞ്ഞെടുക്കുക',\n      weekPlaceholder: 'വാരം തിരഞ്ഞെടുക്കുക',\n      rangePlaceholder: ['ആരംഭ ദിനം', 'അവസാന ദിനം'],\n      rangeYearPlaceholder: ['ആരംഭ വർഷം', 'അവസാന വർഷം'],\n      rangeMonthPlaceholder: ['ആരംഭ മാസം', 'അവസാന മാസം'],\n      rangeWeekPlaceholder: ['ആരംഭ വാരം', 'അവസാന വാരം'],\n      locale: 'ml_IN',\n      today: 'ഇന്ന്',\n      now: 'ഇപ്പോൾ',\n      backToToday: 'ഇന്നത്തെ ദിവസത്തിലേക്ക് തിരിച്ചു പോകുക',\n      ok: 'ശരിയാണ്',\n      clear: 'നീക്കം ചെയ്യുക',\n      month: 'മാസം',\n      year: 'വർഷം',\n      timeSelect: 'സമയം തിരഞ്ഞെടുക്കുക',\n      dateSelect: 'ദിവസം തിരഞ്ഞെടുക്കുക',\n      weekSelect: 'വാരം തിരഞ്ഞെടുക്കുക',\n      monthSelect: 'മാസം തിരഞ്ഞെടുക്കുക',\n      yearSelect: 'വർഷം തിരഞ്ഞെടുക്കുക',\n      decadeSelect: 'ദശാബ്ദം തിരഞ്ഞെടുക്കുക',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'കഴിഞ്ഞ മാസം (PageUp)',\n      nextMonth: 'അടുത്ത മാസം (PageDown)',\n      previousYear: 'കഴിഞ്ഞ വർഷം (Control + left)',\n      nextYear: 'അടുത്ത വർഷം (Control + right)',\n      previousDecade: 'കഴിഞ്ഞ ദശാബ്ദം',\n      nextDecade: 'അടുത്ത ദശാബ്ദം',\n      previousCentury: 'കഴിഞ്ഞ നൂറ്റാണ്ട്',\n      nextCentury: 'അടുത്ത നൂറ്റാണ്ട്'\n    },\n    timePickerLocale: {\n      placeholder: 'സമയം തിരഞ്ഞെടുക്കുക',\n      rangePlaceholder: ['ആരംഭ സമയം', 'അവസാന സമയം']\n    }\n  },\n  global: {\n    placeholder: 'ദയവായി തിരഞ്ഞെടുക്കുക'\n  },\n  Table: {\n    filterTitle: 'ഫിൽറ്റർ',\n    filterConfirm: 'ശരിയാണ്',\n    filterReset: 'പുനഃക്രമീകരിക്കുക',\n    filterEmptyText: 'ഫിൽറ്ററുകളൊന്നുമില്ല',\n    emptyText: 'ഡാറ്റയൊന്നുമില്ല',\n    selectAll: 'നിലവിലെ പേജ് തിരഞ്ഞെടുക്കുക',\n    selectInvert: 'നിലവിലെ പേജിൽ ഇല്ലാത്തത് തിരഞ്ഞെടുക്കുക',\n    selectNone: 'എല്ലാ ഡാറ്റയും നീക്കം ചെയ്യുക',\n    selectionAll: 'എല്ലാ ഡാറ്റയും തിരഞ്ഞെടുക്കുക',\n    sortTitle: 'ക്രമമാക്കുക',\n    expand: 'വരി വികസിപ്പിക്കുക',\n    collapse: 'വരി ചുരുക്കുക',\n    triggerDesc: 'അവരോഹണ ക്രമത്തിനായി ക്ലിക്ക് ചെയ്യുക',\n    triggerAsc: 'ആരോഹണ ക്രമത്തിനായി ക്ലിക്ക് ചെയ്യുക',\n    cancelSort: 'ക്രമീകരണം ഒഴിവാക്കുന്നതിനായി ക്ലിക്ക് ചെയ്യുക'\n  },\n  Modal: {\n    okText: 'ശരിയാണ്',\n    cancelText: 'റദ്ദാക്കുക',\n    justOkText: 'ശരിയാണ്'\n  },\n  Popconfirm: {\n    okText: 'ശരിയാണ്',\n    cancelText: 'റദ്ദാക്കുക'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'ഇവിടെ തിരയുക',\n    itemUnit: 'ഇനം',\n    itemsUnit: 'ഇനങ്ങൾ',\n    remove: 'നീക്കം ചെയ്യുക',\n    selectCurrent: 'നിലവിലെ പേജ് തിരഞ്ഞെടുക്കുക',\n    removeCurrent: 'നിലവിലെ പേജ് നീക്കം ചെയ്യുക',\n    selectAll: 'എല്ലാ ഡാറ്റയും തിരഞ്ഞെടുക്കുക',\n    removeAll: 'എല്ലാ ഡാറ്റയും നീക്കം ചെയ്യുക',\n    selectInvert: 'നിലവിലെ പേജിൽ ഇല്ലാത്തത് തിരഞ്ഞെടുക്കുക'\n  },\n  Upload: {\n    uploading: 'അപ്‌ലോഡ് ചെയ്തു കൊണ്ടിരിക്കുന്നു...',\n    removeFile: 'ഫയൽ നീക്കം ചെയ്യുക',\n    uploadError: 'അപ്‌ലോഡിൽ പിശക് സംഭവിച്ചിരിക്കുന്നു',\n    previewFile: 'ഫയൽ പ്രിവ്യൂ ചെയ്യുക',\n    downloadFile: 'ഫയൽ ഡൗൺലോഡ് ചെയ്യുക'\n  },\n  Empty: {\n    description: 'ഡാറ്റയൊന്നുമില്ല'\n  },\n  Icon: {\n    icon: 'ഐക്കൺ'\n  },\n  Text: {\n    edit: 'തിരുത്തുക',\n    copy: 'കോപ്പി ചെയ്യുക',\n    copied: 'കോപ്പി ചെയ്തു',\n    expand: 'വികസിപ്പിക്കുക'\n  },\n  PageHeader: {\n    back: 'തിരികെ'\n  },\n  Image: {\n    preview: 'പ്രിവ്യൂ'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar mn_MN = {\n  locale: 'mn-mn',\n  Pagination: {\n    items_per_page: '/ хуудас',\n    jump_to: 'Шилжих',\n    jump_to_confirm: 'сонгох',\n    page: '',\n    prev_page: 'Өмнөх хуудас',\n    next_page: 'Дараагийн хуудас',\n    prev_5: 'Дараагийн 5 хуудас',\n    next_5: 'Дараагийн 5 хуудас',\n    prev_3: 'Дараагийн 3 хуудас',\n    next_3: 'Дараагийн 3 хуудас',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Огноо сонгох',\n      rangePlaceholder: ['Эхлэх огноо', 'Дуусах огноо'],\n      locale: 'mn_MN',\n      today: 'Өнөөдөр',\n      now: 'Одоо',\n      backToToday: 'Өнөөдөрлүү буцах',\n      ok: 'Ok',\n      clear: 'Цэвэрлэх',\n      month: 'Сар',\n      year: 'Жил',\n      timeSelect: 'Цаг сонгох',\n      dateSelect: 'Огноо сонгох',\n      weekSelect: '7 хоног сонгох',\n      monthSelect: 'Сар сонгох',\n      yearSelect: 'Жил сонгох',\n      decadeSelect: 'Арван сонгох',\n      yearFormat: 'YYYY',\n      dateFormat: 'YYYY/MM/DD',\n      dayFormat: 'DD',\n      dateTimeFormat: 'YYYY/MM/DD HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Өмнөх сар (PageUp)',\n      nextMonth: 'Дараа сар (PageDown)',\n      previousYear: 'Өмнөх жил (Control + left)',\n      nextYear: 'Дараа жил (Control + right)',\n      previousDecade: 'Өмнөх арван',\n      nextDecade: 'Дараа арван',\n      previousCentury: 'Өмнөх зуун',\n      nextCentury: 'Дараа зуун'\n    },\n    timePickerLocale: {\n      placeholder: 'Цаг сонгох'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Цаг сонгох'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Огноо сонгох',\n      rangePlaceholder: ['Эхлэх огноо', 'Дуусах огноо'],\n      locale: 'mn_MN',\n      today: 'Өнөөдөр',\n      now: 'Одоо',\n      backToToday: 'Өнөөдөрлүү буцах',\n      ok: 'Ok',\n      clear: 'Цэвэрлэх',\n      month: 'Сар',\n      year: 'Жил',\n      timeSelect: 'Цаг сонгох',\n      dateSelect: 'Огноо сонгох',\n      weekSelect: '7 хоног сонгох',\n      monthSelect: 'Сар сонгох',\n      yearSelect: 'Жил сонгох',\n      decadeSelect: 'Арван сонгох',\n      yearFormat: 'YYYY',\n      dateFormat: 'YYYY/MM/DD',\n      dayFormat: 'DD',\n      dateTimeFormat: 'YYYY/MM/DD HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Өмнөх сар (PageUp)',\n      nextMonth: 'Дараа сар (PageDown)',\n      previousYear: 'Өмнөх жил (Control + left)',\n      nextYear: 'Дараа жил (Control + right)',\n      previousDecade: 'Өмнөх арван',\n      nextDecade: 'Дараа арван',\n      previousCentury: 'Өмнөх зуун',\n      nextCentury: 'Дараа зуун'\n    },\n    timePickerLocale: {\n      placeholder: 'Цаг сонгох'\n    }\n  },\n  Table: {\n    filterTitle: 'Хайх цэс',\n    filterConfirm: 'OK',\n    filterReset: 'Цэвэрлэх',\n    selectAll: 'Бүгдийг сонгох',\n    selectInvert: 'Бусдыг сонгох'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Цуцлах',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Цуцлах'\n  },\n  Transfer: {\n    searchPlaceholder: 'Хайх',\n    itemUnit: 'Зүйл',\n    itemsUnit: 'Зүйлүүд'\n  },\n  Upload: {\n    uploading: 'Хуулж байна...',\n    removeFile: 'Файл устгах',\n    uploadError: 'Хуулахад алдаа гарлаа',\n    previewFile: 'Файлыг түргэн үзэх',\n    downloadFile: 'Файлыг татах'\n  },\n  Empty: {\n    description: 'Мэдээлэл байхгүй байна'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ms_MY = {\n  locale: 'ms-my',\n  Pagination: {\n    items_per_page: '/ halaman',\n    jump_to: 'Lompat ke',\n    jump_to_confirm: 'Sahkan',\n    page: '',\n    prev_page: 'Halaman sebelumnya',\n    next_page: 'Halam seterusnya',\n    prev_5: '5 halaman sebelum',\n    next_5: '5 halaman seterusnya',\n    prev_3: '3 halaman sebelumnya',\n    next_3: '3 halaman seterusnya',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Pilih tarikh',\n      rangePlaceholder: ['Tarikh mula', 'Tarikh akhir'],\n      locale: 'ms_MY',\n      today: 'Hari ini',\n      now: 'Sekarang',\n      backToToday: 'Kembali ke hari ini',\n      ok: 'Ok',\n      timeSelect: 'Pilih masa',\n      dateSelect: 'Pilih tarikh',\n      weekSelect: 'Pilih minggu',\n      clear: 'Padam',\n      month: 'Bulan',\n      year: 'Tahun',\n      previousMonth: 'Bulan lepas',\n      nextMonth: 'Bulan depan',\n      monthSelect: 'Pilih bulan',\n      yearSelect: 'Pilih tahun',\n      decadeSelect: 'Pilih dekad',\n      yearFormat: 'YYYY',\n      dayFormat: 'D',\n      dateFormat: 'M/D/YYYY',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      previousYear: 'Tahun lepas (Ctrl+left)',\n      nextYear: 'Tahun depan (Ctrl+right)',\n      previousDecade: 'Dekad lepas',\n      nextDecade: 'Dekad depan',\n      previousCentury: 'Abad lepas',\n      nextCentury: 'Abad depan'\n    },\n    timePickerLocale: {\n      placeholder: 'Sila pilih masa'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Sila pilih masa'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Pilih tarikh',\n      rangePlaceholder: ['Tarikh mula', 'Tarikh akhir'],\n      locale: 'ms_MY',\n      today: 'Hari ini',\n      now: 'Sekarang',\n      backToToday: 'Kembali ke hari ini',\n      ok: 'Ok',\n      timeSelect: 'Pilih masa',\n      dateSelect: 'Pilih tarikh',\n      weekSelect: 'Pilih minggu',\n      clear: 'Padam',\n      month: 'Bulan',\n      year: 'Tahun',\n      previousMonth: 'Bulan lepas',\n      nextMonth: 'Bulan depan',\n      monthSelect: 'Pilih bulan',\n      yearSelect: 'Pilih tahun',\n      decadeSelect: 'Pilih dekad',\n      yearFormat: 'YYYY',\n      dayFormat: 'D',\n      dateFormat: 'M/D/YYYY',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      previousYear: 'Tahun lepas (Ctrl+left)',\n      nextYear: 'Tahun depan (Ctrl+right)',\n      previousDecade: 'Dekad lepas',\n      nextDecade: 'Dekad depan',\n      previousCentury: 'Abad lepas',\n      nextCentury: 'Abad depan'\n    },\n    timePickerLocale: {\n      placeholder: 'Sila pilih masa'\n    }\n  },\n  global: {\n    placeholder: 'Sila pilih'\n  },\n  PageHeader: {\n    back: 'Kembali'\n  },\n  Text: {\n    edit: 'Sunting',\n    copy: 'Salin',\n    copied: 'Berjaya menyalin',\n    expand: 'Kembang'\n  },\n  Empty: {\n    description: 'Tiada data'\n  },\n  Table: {\n    filterTitle: 'Cari dengan tajuk',\n    filterConfirm: 'OK',\n    filterReset: 'Menetapkan semula',\n    emptyText: 'Tiada data',\n    selectAll: 'Pilih semua',\n    selectInvert: 'Terbalikkan'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Batal',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Batal'\n  },\n  Transfer: {\n    notFoundContent: 'Tidak dijumpai',\n    searchPlaceholder: 'Carian di sini',\n    itemUnit: 'item',\n    itemsUnit: 'item'\n  },\n  Icon: {\n    icon: 'ikon'\n  },\n  Select: {\n    notFoundContent: 'Tidak Dijumpai'\n  },\n  Upload: {\n    uploading: 'Sedang memuat naik...',\n    removeFile: 'Buang fail',\n    uploadError: 'Masalah muat naik',\n    previewFile: 'Tengok fail',\n    downloadFile: 'Muat turun fail'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar nb_NO = {\n  locale: 'nb',\n  Pagination: {\n    items_per_page: '/ side',\n    jump_to: 'Gå til side',\n    page: 'Side',\n    prev_page: 'Forrige side',\n    next_page: 'Neste side',\n    prev_5: '5 forrige',\n    next_5: '5 neste',\n    prev_3: '3 forrige',\n    next_3: '3 neste',\n    page_size: 'sidestørrelse'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Velg dato',\n      yearPlaceholder: 'Velg år',\n      quarterPlaceholder: 'Velg kvartal',\n      monthPlaceholder: 'Velg måned',\n      weekPlaceholder: 'Velg uke',\n      rangePlaceholder: ['Startdato', 'Sluttdato'],\n      rangeYearPlaceholder: ['Startår', 'Sluttår'],\n      rangeMonthPlaceholder: ['Startmåned', 'Sluttmåned'],\n      rangeWeekPlaceholder: ['Start uke', 'Sluttuke'],\n      locale: 'nb_NO',\n      today: 'I dag',\n      now: 'Nå',\n      backToToday: 'Gå til i dag',\n      ok: 'Ok',\n      clear: 'Annuller',\n      month: 'Måned',\n      year: 'År',\n      timeSelect: 'Velg tidspunkt',\n      dateSelect: 'Velg dato',\n      weekSelect: 'Velg uke',\n      monthSelect: 'Velg måned',\n      yearSelect: 'Velg år',\n      decadeSelect: 'Velg tiår',\n      yearFormat: 'YYYY',\n      dateFormat: 'DD.MM.YYYY',\n      dayFormat: 'DD',\n      dateTimeFormat: 'DD.MM.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Forrige måned (PageUp)',\n      nextMonth: 'Neste måned (PageDown)',\n      previousYear: 'Forrige år (Control + venstre)',\n      nextYear: 'Neste år (Control + høyre)',\n      previousDecade: 'Forrige tiår',\n      nextDecade: 'Neste tiår',\n      previousCentury: 'Forrige århundre',\n      nextCentury: 'Neste århundre'\n    },\n    timePickerLocale: {\n      placeholder: 'Velg tid',\n      rangePlaceholder: ['Starttid', 'Sluttid']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Velg tid',\n    rangePlaceholder: ['Starttid', 'Sluttid']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Velg dato',\n      yearPlaceholder: 'Velg år',\n      quarterPlaceholder: 'Velg kvartal',\n      monthPlaceholder: 'Velg måned',\n      weekPlaceholder: 'Velg uke',\n      rangePlaceholder: ['Startdato', 'Sluttdato'],\n      rangeYearPlaceholder: ['Startår', 'Sluttår'],\n      rangeMonthPlaceholder: ['Startmåned', 'Sluttmåned'],\n      rangeWeekPlaceholder: ['Start uke', 'Sluttuke'],\n      locale: 'nb_NO',\n      today: 'I dag',\n      now: 'Nå',\n      backToToday: 'Gå til i dag',\n      ok: 'Ok',\n      clear: 'Annuller',\n      month: 'Måned',\n      year: 'År',\n      timeSelect: 'Velg tidspunkt',\n      dateSelect: 'Velg dato',\n      weekSelect: 'Velg uke',\n      monthSelect: 'Velg måned',\n      yearSelect: 'Velg år',\n      decadeSelect: 'Velg tiår',\n      yearFormat: 'YYYY',\n      dateFormat: 'DD.MM.YYYY',\n      dayFormat: 'DD',\n      dateTimeFormat: 'DD.MM.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Forrige måned (PageUp)',\n      nextMonth: 'Neste måned (PageDown)',\n      previousYear: 'Forrige år (Control + venstre)',\n      nextYear: 'Neste år (Control + høyre)',\n      previousDecade: 'Forrige tiår',\n      nextDecade: 'Neste tiår',\n      previousCentury: 'Forrige århundre',\n      nextCentury: 'Neste århundre'\n    },\n    timePickerLocale: {\n      placeholder: 'Velg tid',\n      rangePlaceholder: ['Starttid', 'Sluttid']\n    }\n  },\n  global: {\n    placeholder: 'Vennligst velg'\n  },\n  Table: {\n    filterTitle: 'Filtermeny',\n    filterConfirm: 'OK',\n    filterReset: 'Nullstill',\n    filterEmptyText: 'Ingen filtre',\n    selectAll: 'Velg alle',\n    selectInvert: 'Inverter gjeldende side',\n    selectionAll: 'Velg all data',\n    sortTitle: 'Sorter',\n    expand: 'Utvid rad',\n    collapse: 'Skjul rad',\n    triggerDesc: 'Sorter data i synkende rekkefølge',\n    triggerAsc: 'Sorterer data i stigende rekkefølge',\n    cancelSort: 'Klikk for å avbryte sorteringen'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Avbryt',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Avbryt'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Søk her',\n    itemUnit: 'element',\n    itemsUnit: 'elementer',\n    remove: 'Fjern',\n    selectCurrent: 'Velg gjeldende side',\n    removeCurrent: 'Fjern gjeldende side',\n    selectAll: 'Velg all data',\n    removeAll: 'Fjern all data',\n    selectInvert: 'Inverter gjeldende side'\n  },\n  Upload: {\n    uploading: 'Laster opp...',\n    removeFile: 'Fjern fil',\n    uploadError: 'Feil ved opplastning',\n    previewFile: 'Forhåndsvisning',\n    downloadFile: 'Last ned fil'\n  },\n  Empty: {\n    description: 'Ingen data'\n  },\n  Icon: {\n    icon: 'ikon'\n  },\n  Text: {\n    edit: 'Rediger',\n    copy: 'Kopier',\n    copied: 'Kopiert',\n    expand: 'Utvid'\n  },\n  PageHeader: {\n    back: 'Tilbake'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ne_NP = {\n  locale: 'ne-np',\n  Pagination: {\n    items_per_page: '/ page',\n    jump_to: 'Go to',\n    jump_to_confirm: 'confirm',\n    page: 'Page',\n    prev_page: 'Previous Page',\n    next_page: 'Next Page',\n    prev_5: 'Previous 5 Pages',\n    next_5: 'Next 5 Pages',\n    prev_3: 'Previous 3 Pages',\n    next_3: 'Next 3 Pages',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Select date',\n      yearPlaceholder: 'Select year',\n      quarterPlaceholder: 'Select quarter',\n      monthPlaceholder: 'Select month',\n      weekPlaceholder: 'Select week',\n      rangePlaceholder: ['Start date', 'End date'],\n      rangeYearPlaceholder: ['Start year', 'End year'],\n      rangeMonthPlaceholder: ['Start month', 'End month'],\n      rangeWeekPlaceholder: ['Start week', 'End week'],\n      locale: 'en_US',\n      today: 'Today',\n      now: 'Now',\n      backToToday: 'Back to today',\n      ok: 'Ok',\n      clear: 'Clear',\n      month: 'Month',\n      year: 'Year',\n      timeSelect: 'select time',\n      dateSelect: 'select date',\n      weekSelect: 'Choose a week',\n      monthSelect: 'Choose a month',\n      yearSelect: 'Choose a year',\n      decadeSelect: 'Choose a decade',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Previous month (PageUp)',\n      nextMonth: 'Next month (PageDown)',\n      previousYear: 'Last year (Control + left)',\n      nextYear: 'Next year (Control + right)',\n      previousDecade: 'Last decade',\n      nextDecade: 'Next decade',\n      previousCentury: 'Last century',\n      nextCentury: 'Next century'\n    },\n    timePickerLocale: {\n      placeholder: 'Select time',\n      rangePlaceholder: ['Start time', 'End time']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Select time',\n    rangePlaceholder: ['Start time', 'End time']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Select date',\n      yearPlaceholder: 'Select year',\n      quarterPlaceholder: 'Select quarter',\n      monthPlaceholder: 'Select month',\n      weekPlaceholder: 'Select week',\n      rangePlaceholder: ['Start date', 'End date'],\n      rangeYearPlaceholder: ['Start year', 'End year'],\n      rangeMonthPlaceholder: ['Start month', 'End month'],\n      rangeWeekPlaceholder: ['Start week', 'End week'],\n      locale: 'en_US',\n      today: 'Today',\n      now: 'Now',\n      backToToday: 'Back to today',\n      ok: 'Ok',\n      clear: 'Clear',\n      month: 'Month',\n      year: 'Year',\n      timeSelect: 'select time',\n      dateSelect: 'select date',\n      weekSelect: 'Choose a week',\n      monthSelect: 'Choose a month',\n      yearSelect: 'Choose a year',\n      decadeSelect: 'Choose a decade',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Previous month (PageUp)',\n      nextMonth: 'Next month (PageDown)',\n      previousYear: 'Last year (Control + left)',\n      nextYear: 'Next year (Control + right)',\n      previousDecade: 'Last decade',\n      nextDecade: 'Next decade',\n      previousCentury: 'Last century',\n      nextCentury: 'Next century'\n    },\n    timePickerLocale: {\n      placeholder: 'Select time',\n      rangePlaceholder: ['Start time', 'End time']\n    }\n  },\n  Table: {\n    filterTitle: 'फिल्टर मेनु',\n    filterConfirm: 'हो',\n    filterReset: 'रीसेट',\n    selectAll: 'सबै छान्नुुहोस्',\n    selectInvert: 'छनौट उल्टाउनुहोस'\n  },\n  Modal: {\n    okText: 'हो',\n    cancelText: 'होईन',\n    justOkText: 'हो'\n  },\n  Popconfirm: {\n    okText: 'हो',\n    cancelText: 'होईन'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'यहाँ खोज्नुहोस्',\n    itemUnit: 'वस्तु',\n    itemsUnit: 'वस्तुहरू'\n  },\n  Upload: {\n    uploading: 'अपलोड गर्दै...',\n    removeFile: 'फाइल हटाउनुहोस्',\n    uploadError: 'अप्लोडमा समस्या भयो',\n    previewFile: 'फाइल पूर्वावलोकन गर्नुहोस्',\n    downloadFile: 'डाउनलोड फाइल'\n  },\n  Empty: {\n    description: 'डाटा छैन'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar nl_BE = {\n  locale: 'nl-be',\n  Pagination: {\n    items_per_page: '/ pagina',\n    jump_to: 'Ga naar',\n    jump_to_confirm: 'bevestigen',\n    page: '',\n    prev_page: 'Vorige pagina',\n    next_page: 'Volgende pagina',\n    prev_5: \"Vorige 5 pagina's\",\n    next_5: \"Volgende 5 pagina's\",\n    prev_3: \"Vorige 3 pagina's\",\n    next_3: \"Volgende 3 pagina's\",\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Selecteer datum',\n      rangePlaceholder: ['Begin datum', 'Eind datum'],\n      locale: 'nl_BE',\n      today: 'Vandaag',\n      now: 'Nu',\n      backToToday: 'Terug naar vandaag',\n      ok: 'Ok',\n      clear: 'Reset',\n      month: 'Maand',\n      year: 'Jaar',\n      timeSelect: 'Selecteer tijd',\n      dateSelect: 'Selecteer datum',\n      monthSelect: 'Kies een maand',\n      yearSelect: 'Kies een jaar',\n      decadeSelect: 'Kies een decennium',\n      yearFormat: 'YYYY',\n      dateFormat: 'D-M-YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D-M-YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Vorige maand (PageUp)',\n      nextMonth: 'Volgende maand (PageDown)',\n      previousYear: 'Vorig jaar (Control + left)',\n      nextYear: 'Volgend jaar (Control + right)',\n      previousDecade: 'Vorig decennium',\n      nextDecade: 'Volgend decennium',\n      previousCentury: 'Vorige eeuw',\n      nextCentury: 'Volgende eeuw',\n      monthPlaceholder: 'Selecteer maand',\n      quarterPlaceholder: 'Selecteer kwartaal',\n      rangeMonthPlaceholder: ['Begin maand', 'Eind maand'],\n      rangeWeekPlaceholder: ['Begin week', 'Eind week'],\n      rangeYearPlaceholder: ['Begin jaar', 'Eind jaar'],\n      weekPlaceholder: 'Selecteer week',\n      yearPlaceholder: 'Selecteer jaar'\n    },\n    timePickerLocale: {\n      placeholder: 'Selecteer tijd',\n      rangePlaceholder: ['Start tijd', 'Eind tijd']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Selecteer tijd',\n    rangePlaceholder: ['Start tijd', 'Eind tijd']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Selecteer datum',\n      rangePlaceholder: ['Begin datum', 'Eind datum'],\n      locale: 'nl_BE',\n      today: 'Vandaag',\n      now: 'Nu',\n      backToToday: 'Terug naar vandaag',\n      ok: 'Ok',\n      clear: 'Reset',\n      month: 'Maand',\n      year: 'Jaar',\n      timeSelect: 'Selecteer tijd',\n      dateSelect: 'Selecteer datum',\n      monthSelect: 'Kies een maand',\n      yearSelect: 'Kies een jaar',\n      decadeSelect: 'Kies een decennium',\n      yearFormat: 'YYYY',\n      dateFormat: 'D-M-YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D-M-YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Vorige maand (PageUp)',\n      nextMonth: 'Volgende maand (PageDown)',\n      previousYear: 'Vorig jaar (Control + left)',\n      nextYear: 'Volgend jaar (Control + right)',\n      previousDecade: 'Vorig decennium',\n      nextDecade: 'Volgend decennium',\n      previousCentury: 'Vorige eeuw',\n      nextCentury: 'Volgende eeuw',\n      monthPlaceholder: 'Selecteer maand',\n      quarterPlaceholder: 'Selecteer kwartaal',\n      rangeMonthPlaceholder: ['Begin maand', 'Eind maand'],\n      rangeWeekPlaceholder: ['Begin week', 'Eind week'],\n      rangeYearPlaceholder: ['Begin jaar', 'Eind jaar'],\n      weekPlaceholder: 'Selecteer week',\n      yearPlaceholder: 'Selecteer jaar'\n    },\n    timePickerLocale: {\n      placeholder: 'Selecteer tijd',\n      rangePlaceholder: ['Start tijd', 'Eind tijd']\n    }\n  },\n  Table: {\n    filterTitle: 'Filteren',\n    filterConfirm: 'OK',\n    filterReset: 'Reset',\n    selectAll: 'Selecteer huidige pagina',\n    selectInvert: 'Keer volgorde om',\n    cancelSort: 'Klik om sortering te annuleren',\n    collapse: 'Rij inklappen',\n    emptyText: 'Geen data',\n    expand: 'Rij uitklappen',\n    filterEmptyText: 'Geen filters',\n    selectNone: 'Maak selectie leeg',\n    selectionAll: 'Selecteer alle data',\n    sortTitle: 'Sorteren',\n    triggerAsc: 'Klik om oplopend te sorteren',\n    triggerDesc: 'Klik om aflopend te sorteren'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Annuleer',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Annuleer'\n  },\n  Transfer: {\n    searchPlaceholder: 'Zoek hier',\n    itemUnit: 'item',\n    itemsUnit: 'items',\n    remove: 'Verwijder',\n    removeAll: 'Verwijder alles',\n    removeCurrent: 'Verwijder huidige pagina',\n    selectAll: 'Selecteer alles',\n    selectCurrent: 'Selecteer huidige pagina',\n    selectInvert: 'Huidige pagina omkeren',\n    titles: ['', '']\n  },\n  Upload: {\n    uploading: 'Uploaden...',\n    removeFile: 'Verwijder bestand',\n    uploadError: 'Fout tijdens uploaden',\n    previewFile: 'Preview file',\n    downloadFile: 'Bestand downloaden'\n  },\n  Empty: {\n    description: 'Geen gegevens'\n  },\n  global: {\n    placeholder: 'Maak een selectie'\n  },\n  Icon: {\n    icon: 'icoon'\n  },\n  Text: {\n    edit: 'Bewerken',\n    copy: 'kopiëren',\n    copied: 'Gekopieerd',\n    expand: 'Uitklappen'\n  },\n  PageHeader: {\n    back: 'Terug'\n  },\n  Image: {\n    preview: 'Voorbeeld'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar nl_NL = {\n  locale: 'nl',\n  Pagination: {\n    items_per_page: '/ pagina',\n    jump_to: 'Ga naar',\n    jump_to_confirm: 'bevestigen',\n    page: 'Pagina',\n    prev_page: 'Vorige pagina',\n    next_page: 'Volgende pagina',\n    prev_5: \"Vorige 5 pagina's\",\n    next_5: \"Volgende 5 pagina's\",\n    prev_3: \"Vorige 3 pagina's\",\n    next_3: \"Volgende 3 pagina's\",\n    page_size: 'pagina grootte'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Selecteer datum',\n      rangePlaceholder: ['Begin datum', 'Eind datum'],\n      locale: 'nl_NL',\n      today: 'Vandaag',\n      now: 'Nu',\n      backToToday: 'Terug naar vandaag',\n      ok: 'Ok',\n      clear: 'Reset',\n      month: 'Maand',\n      year: 'Jaar',\n      timeSelect: 'Selecteer tijd',\n      dateSelect: 'Selecteer datum',\n      monthSelect: 'Kies een maand',\n      yearSelect: 'Kies een jaar',\n      decadeSelect: 'Kies een decennium',\n      yearFormat: 'YYYY',\n      dateFormat: 'D-M-YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D-M-YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Vorige maand (PageUp)',\n      nextMonth: 'Volgende maand (PageDown)',\n      previousYear: 'Vorig jaar (Control + left)',\n      nextYear: 'Volgend jaar (Control + right)',\n      previousDecade: 'Vorig decennium',\n      nextDecade: 'Volgend decennium',\n      previousCentury: 'Vorige eeuw',\n      nextCentury: 'Volgende eeuw',\n      monthPlaceholder: 'Selecteer maand',\n      quarterPlaceholder: 'Selecteer kwartaal',\n      rangeMonthPlaceholder: ['Begin maand', 'Eind maand'],\n      rangeWeekPlaceholder: ['Begin week', 'Eind week'],\n      rangeYearPlaceholder: ['Begin jaar', 'Eind jaar'],\n      weekPlaceholder: 'Selecteer week',\n      yearPlaceholder: 'Selecteer jaar'\n    },\n    timePickerLocale: {\n      placeholder: 'Selecteer tijd',\n      rangePlaceholder: ['Start tijd', 'Eind tijd']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Selecteer tijd',\n    rangePlaceholder: ['Start tijd', 'Eind tijd']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Selecteer datum',\n      rangePlaceholder: ['Begin datum', 'Eind datum'],\n      locale: 'nl_NL',\n      today: 'Vandaag',\n      now: 'Nu',\n      backToToday: 'Terug naar vandaag',\n      ok: 'Ok',\n      clear: 'Reset',\n      month: 'Maand',\n      year: 'Jaar',\n      timeSelect: 'Selecteer tijd',\n      dateSelect: 'Selecteer datum',\n      monthSelect: 'Kies een maand',\n      yearSelect: 'Kies een jaar',\n      decadeSelect: 'Kies een decennium',\n      yearFormat: 'YYYY',\n      dateFormat: 'D-M-YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D-M-YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Vorige maand (PageUp)',\n      nextMonth: 'Volgende maand (PageDown)',\n      previousYear: 'Vorig jaar (Control + left)',\n      nextYear: 'Volgend jaar (Control + right)',\n      previousDecade: 'Vorig decennium',\n      nextDecade: 'Volgend decennium',\n      previousCentury: 'Vorige eeuw',\n      nextCentury: 'Volgende eeuw',\n      monthPlaceholder: 'Selecteer maand',\n      quarterPlaceholder: 'Selecteer kwartaal',\n      rangeMonthPlaceholder: ['Begin maand', 'Eind maand'],\n      rangeWeekPlaceholder: ['Begin week', 'Eind week'],\n      rangeYearPlaceholder: ['Begin jaar', 'Eind jaar'],\n      weekPlaceholder: 'Selecteer week',\n      yearPlaceholder: 'Selecteer jaar'\n    },\n    timePickerLocale: {\n      placeholder: 'Selecteer tijd',\n      rangePlaceholder: ['Start tijd', 'Eind tijd']\n    }\n  },\n  global: {\n    placeholder: 'Maak een selectie'\n  },\n  Table: {\n    filterTitle: 'Filteren',\n    filterConfirm: 'OK',\n    filterReset: 'Reset',\n    selectAll: 'Selecteer huidige pagina',\n    selectInvert: 'Keer volgorde om',\n    sortTitle: 'Sorteren',\n    expand: 'Rij uitklappen',\n    collapse: 'Rij inklappen',\n    cancelSort: 'Klik om sortering te annuleren',\n    emptyText: 'Geen data',\n    filterEmptyText: 'Geen filters',\n    selectNone: 'Maak selectie leeg',\n    selectionAll: 'Selecteer alle data',\n    triggerAsc: 'Klik om oplopend te sorteren',\n    triggerDesc: 'Klik om aflopend te sorteren'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Annuleer',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Annuleer'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Zoek hier',\n    itemUnit: 'item',\n    itemsUnit: 'items',\n    remove: 'Verwijder',\n    removeAll: 'Verwijder alles',\n    removeCurrent: 'Verwijder huidige pagina',\n    selectAll: 'Selecteer alles',\n    selectCurrent: 'Selecteer huidige pagina',\n    selectInvert: 'Huidige pagina omkeren'\n  },\n  Upload: {\n    uploading: 'Uploaden...',\n    removeFile: 'Verwijder bestand',\n    uploadError: 'Fout tijdens uploaden',\n    previewFile: 'Preview file',\n    downloadFile: 'Bestand downloaden'\n  },\n  Empty: {\n    description: 'Geen gegevens'\n  },\n  Icon: {\n    icon: 'icoon'\n  },\n  Text: {\n    edit: 'Bewerken',\n    copy: 'kopiëren',\n    copied: 'Gekopieerd',\n    expand: 'Uitklappen'\n  },\n  PageHeader: {\n    back: 'Terug'\n  },\n  Image: {\n    preview: 'Voorbeeld'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar pl_PL = {\n  locale: 'pl',\n  Pagination: {\n    items_per_page: 'na stronę',\n    jump_to: 'Idź do',\n    jump_to_confirm: 'potwierdź',\n    page: '',\n    prev_page: 'Poprzednia strona',\n    next_page: 'Następna strona',\n    prev_5: 'Poprzednie 5 stron',\n    next_5: 'Następne 5 stron',\n    prev_3: 'Poprzednie 3 strony',\n    next_3: 'Następne 3 strony',\n    page_size: 'rozmiar strony'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Wybierz datę',\n      yearPlaceholder: 'Wybierz rok',\n      monthPlaceholder: 'Wybierz miesiąc',\n      weekPlaceholder: 'Wybierz tydzień',\n      rangePlaceholder: ['Data początkowa', 'Data końcowa'],\n      rangeYearPlaceholder: ['Początkowy rok', 'Końcowy rok'],\n      rangeMonthPlaceholder: ['Początkowy miesiąc', 'Końcowy miesiąc'],\n      rangeWeekPlaceholder: ['Początkowy tydzień', 'Końcowy tydzień'],\n      locale: 'pl_PL',\n      today: 'Dzisiaj',\n      now: 'Teraz',\n      backToToday: 'Ustaw dzisiaj',\n      ok: 'Ok',\n      clear: 'Wyczyść',\n      month: 'Miesiąc',\n      year: 'Rok',\n      timeSelect: 'Ustaw czas',\n      dateSelect: 'Ustaw datę',\n      monthSelect: 'Wybierz miesiąc',\n      yearSelect: 'Wybierz rok',\n      decadeSelect: 'Wybierz dekadę',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Poprzedni miesiąc (PageUp)',\n      nextMonth: 'Następny miesiąc (PageDown)',\n      previousYear: 'Ostatni rok (Ctrl + left)',\n      nextYear: 'Następny rok (Ctrl + right)',\n      previousDecade: 'Ostatnia dekada',\n      nextDecade: 'Następna dekada',\n      previousCentury: 'Ostatni wiek',\n      nextCentury: 'Następny wiek'\n    },\n    timePickerLocale: {\n      placeholder: 'Wybierz godzinę'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Wybierz godzinę'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Wybierz datę',\n      rangePlaceholder: ['Data początkowa', 'Data końcowa'],\n      locale: 'pl_PL',\n      today: 'Dzisiaj',\n      now: 'Teraz',\n      backToToday: 'Ustaw dzisiaj',\n      ok: 'Ok',\n      clear: 'Wyczyść',\n      month: 'Miesiąc',\n      year: 'Rok',\n      timeSelect: 'Ustaw czas',\n      dateSelect: 'Ustaw datę',\n      monthSelect: 'Wybierz miesiąc',\n      yearSelect: 'Wybierz rok',\n      decadeSelect: 'Wybierz dekadę',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Poprzedni miesiąc (PageUp)',\n      nextMonth: 'Następny miesiąc (PageDown)',\n      previousYear: 'Ostatni rok (Ctrl + left)',\n      nextYear: 'Następny rok (Ctrl + right)',\n      previousDecade: 'Ostatnia dekada',\n      nextDecade: 'Następna dekada',\n      previousCentury: 'Ostatni wiek',\n      nextCentury: 'Następny wiek'\n    },\n    timePickerLocale: {\n      placeholder: 'Wybierz godzinę'\n    }\n  },\n  Table: {\n    filterTitle: 'Menu filtra',\n    filterConfirm: 'OK',\n    filterReset: 'Usuń filtry',\n    selectAll: 'Zaznacz bieżącą stronę',\n    selectInvert: 'Odwróć zaznaczenie',\n    triggerDesc: 'Sortuj malejąco',\n    triggerAsc: 'Sortuj rosnąco',\n    cancelSort: 'Usuń sortowanie',\n    filterEmptyText: 'Brak filtrów',\n    filterCheckall: 'Wybierz wszystkie elementy',\n    filterSearchPlaceholder: 'Szukaj w filtrach',\n    emptyText: 'Brak danych',\n    selectNone: 'Wyczyść',\n    selectionAll: 'Wybierz wszystkie',\n    sortTitle: 'Sortowanie',\n    expand: 'Rozwiń wiersz',\n    collapse: 'Zwiń wiersz'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Anuluj',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Anuluj'\n  },\n  Transfer: {\n    searchPlaceholder: 'Szukaj',\n    itemUnit: 'obiekt',\n    itemsUnit: 'obiekty',\n    titles: ['', ''],\n    remove: 'Usuń',\n    selectCurrent: 'Wybierz aktualną stronę',\n    removeCurrent: 'Usuń aktualną stronę',\n    selectAll: 'Wybierz wszystkie',\n    removeAll: 'Usuń wszystkie',\n    selectInvert: 'Odwróć wybór'\n  },\n  Upload: {\n    uploading: 'Wysyłanie...',\n    removeFile: 'Usuń plik',\n    uploadError: 'Błąd wysyłania',\n    previewFile: 'Podejrzyj plik',\n    downloadFile: 'Pobieranie pliku'\n  },\n  Empty: {\n    description: 'Brak danych'\n  },\n  global: {\n    placeholder: 'Wybierz'\n  },\n  Icon: {\n    icon: 'Ikona'\n  },\n  Text: {\n    edit: 'Edytuj',\n    copy: 'Kopiuj',\n    copied: 'Skopiowany',\n    expand: 'Rozwiń'\n  },\n  PageHeader: {\n    back: 'Wstecz'\n  },\n  Image: {\n    preview: 'Podgląd'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar pt_BR = {\n  locale: 'pt-br',\n  Pagination: {\n    items_per_page: '/ página',\n    jump_to: 'Vá até',\n    jump_to_confirm: 'confirme',\n    page: 'Página',\n    prev_page: 'Página anterior',\n    next_page: 'Próxima página',\n    prev_5: '5 páginas anteriores',\n    next_5: '5 próximas páginas',\n    prev_3: '3 páginas anteriores',\n    next_3: '3 próximas páginas',\n    page_size: 'tamanho da página'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Selecionar data',\n      yearPlaceholder: 'Selecionar ano',\n      quarterPlaceholder: 'Selecionar trimestre',\n      monthPlaceholder: 'Selecionar mês',\n      weekPlaceholder: 'Selecionar semana',\n      rangePlaceholder: ['Data inicial', 'Data final'],\n      rangeYearPlaceholder: ['Ano inicial', 'Ano Final'],\n      rangeMonthPlaceholder: ['Mês inicial', 'Mês final'],\n      rangeWeekPlaceholder: ['Semana inicial', 'Semana final'],\n      locale: 'pt_BR',\n      today: 'Hoje',\n      now: 'Agora',\n      backToToday: 'Voltar para hoje',\n      ok: 'Ok',\n      clear: 'Limpar',\n      month: 'Mês',\n      year: 'Ano',\n      timeSelect: 'Selecionar hora',\n      dateSelect: 'Selecionar data',\n      weekSelect: 'Escolher semana',\n      monthSelect: 'Escolher mês',\n      yearSelect: 'Escolher ano',\n      decadeSelect: 'Escolher década',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: false,\n      previousMonth: 'Mês anterior (PageUp)',\n      nextMonth: 'Próximo mês (PageDown)',\n      previousYear: 'Ano anterior (Control + esquerda)',\n      nextYear: 'Próximo ano (Control + direita)',\n      previousDecade: 'Década anterior',\n      nextDecade: 'Próxima década',\n      previousCentury: 'Século anterior',\n      nextCentury: 'Próximo século',\n      shortWeekDays: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'],\n      shortMonths: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']\n    },\n    timePickerLocale: {\n      placeholder: 'Hora',\n      rangePlaceholder: ['Hora inicial', 'Hora final']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Hora',\n    rangePlaceholder: ['Hora inicial', 'Hora final']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Selecionar data',\n      yearPlaceholder: 'Selecionar ano',\n      quarterPlaceholder: 'Selecionar trimestre',\n      monthPlaceholder: 'Selecionar mês',\n      weekPlaceholder: 'Selecionar semana',\n      rangePlaceholder: ['Data inicial', 'Data final'],\n      rangeYearPlaceholder: ['Ano inicial', 'Ano Final'],\n      rangeMonthPlaceholder: ['Mês inicial', 'Mês final'],\n      rangeWeekPlaceholder: ['Semana inicial', 'Semana final'],\n      locale: 'pt_BR',\n      today: 'Hoje',\n      now: 'Agora',\n      backToToday: 'Voltar para hoje',\n      ok: 'Ok',\n      clear: 'Limpar',\n      month: 'Mês',\n      year: 'Ano',\n      timeSelect: 'Selecionar hora',\n      dateSelect: 'Selecionar data',\n      weekSelect: 'Escolher semana',\n      monthSelect: 'Escolher mês',\n      yearSelect: 'Escolher ano',\n      decadeSelect: 'Escolher década',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: false,\n      previousMonth: 'Mês anterior (PageUp)',\n      nextMonth: 'Próximo mês (PageDown)',\n      previousYear: 'Ano anterior (Control + esquerda)',\n      nextYear: 'Próximo ano (Control + direita)',\n      previousDecade: 'Década anterior',\n      nextDecade: 'Próxima década',\n      previousCentury: 'Século anterior',\n      nextCentury: 'Próximo século',\n      shortWeekDays: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'],\n      shortMonths: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']\n    },\n    timePickerLocale: {\n      placeholder: 'Hora',\n      rangePlaceholder: ['Hora inicial', 'Hora final']\n    }\n  },\n  global: {\n    placeholder: 'Por favor escolha'\n  },\n  Table: {\n    filterTitle: 'Menu de Filtro',\n    filterConfirm: 'OK',\n    filterReset: 'Resetar',\n    filterEmptyText: 'Sem filtros',\n    emptyText: 'Sem conteúdo',\n    selectAll: 'Selecionar página atual',\n    selectInvert: 'Inverter seleção',\n    selectionAll: 'Selecionar todo o conteúdo',\n    sortTitle: 'Ordenar título',\n    expand: 'Expandir linha',\n    collapse: 'Colapsar linha',\n    triggerDesc: 'Clique organiza por descendente',\n    triggerAsc: 'Clique organiza por ascendente',\n    cancelSort: 'Clique para cancelar organização',\n    selectNone: 'Apagar todo o conteúdo'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Cancelar',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Cancelar'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Procurar',\n    itemUnit: 'item',\n    itemsUnit: 'items',\n    remove: 'Remover',\n    selectCurrent: 'Selecionar página atual',\n    removeCurrent: 'Remover página atual',\n    selectAll: 'Selecionar todos',\n    removeAll: 'Remover todos',\n    selectInvert: 'Inverter seleção atual'\n  },\n  Upload: {\n    uploading: 'Enviando...',\n    removeFile: 'Remover arquivo',\n    uploadError: 'Erro no envio',\n    previewFile: 'Visualizar arquivo',\n    downloadFile: 'Baixar arquivo'\n  },\n  Empty: {\n    description: 'Não há dados'\n  },\n  Icon: {\n    icon: 'ícone'\n  },\n  Text: {\n    edit: 'editar',\n    copy: 'copiar',\n    copied: 'copiado',\n    expand: 'expandir'\n  },\n  PageHeader: {\n    back: 'Retornar'\n  },\n  Image: {\n    preview: 'Pré-visualização'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar pt_PT = {\n  locale: 'pt',\n  Pagination: {\n    items_per_page: '/ página',\n    jump_to: 'Saltar',\n    jump_to_confirm: 'confirmar',\n    page: 'Página',\n    prev_page: 'Página Anterior',\n    next_page: 'Página Seguinte',\n    prev_5: 'Recuar 5 Páginas',\n    next_5: 'Avançar 5 Páginas',\n    prev_3: 'Recuar 3 Páginas',\n    next_3: 'Avançar 3 Páginas',\n    page_size: 'mărimea paginii'\n  },\n  DatePicker: {\n    lang: {\n      yearPlaceholder: 'Selecionar ano',\n      quarterPlaceholder: 'Selecionar trimestre',\n      monthPlaceholder: 'Selecionar mês',\n      weekPlaceholder: 'Selecionar semana',\n      rangePlaceholder: ['Data inicial', 'Data final'],\n      rangeYearPlaceholder: ['Ano inicial', 'Ano final'],\n      rangeMonthPlaceholder: ['Mês inicial', 'Mês final'],\n      rangeWeekPlaceholder: ['Semana inicial', 'Semana final'],\n      locale: 'pt_PT',\n      today: 'Hoje',\n      now: 'Agora',\n      backToToday: 'Hoje',\n      ok: 'OK',\n      clear: 'Limpar',\n      month: 'Mês',\n      year: 'Ano',\n      timeSelect: 'Hora',\n      dateSelect: 'Selecionar data',\n      monthSelect: 'Selecionar mês',\n      yearSelect: 'Selecionar ano',\n      decadeSelect: 'Selecionar década',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: false,\n      previousMonth: 'Mês anterior (PageUp)',\n      nextMonth: 'Mês seguinte (PageDown)',\n      previousYear: 'Ano anterior (Control + left)',\n      nextYear: 'Ano seguinte (Control + right)',\n      previousDecade: 'Última década',\n      nextDecade: 'Próxima década',\n      previousCentury: 'Último século',\n      nextCentury: 'Próximo século',\n      placeholder: 'Data',\n      monthFormat: 'MMMM'\n    },\n    timePickerLocale: {\n      placeholder: 'Hora'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Hora'\n  },\n  Calendar: {\n    lang: {\n      locale: 'pt_PT',\n      today: 'Hoje',\n      now: 'Agora',\n      backToToday: 'Hoje',\n      ok: 'OK',\n      clear: 'Limpar',\n      month: 'Mês',\n      year: 'Ano',\n      timeSelect: 'Hora',\n      dateSelect: 'Selecionar data',\n      monthSelect: 'Selecionar mês',\n      yearSelect: 'Selecionar ano',\n      decadeSelect: 'Selecionar década',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: false,\n      previousMonth: 'Mês anterior (PageUp)',\n      nextMonth: 'Mês seguinte (PageDown)',\n      previousYear: 'Ano anterior (Control + left)',\n      nextYear: 'Ano seguinte (Control + right)',\n      previousDecade: 'Última década',\n      nextDecade: 'Próxima década',\n      previousCentury: 'Último século',\n      nextCentury: 'Próximo século',\n      placeholder: 'Data',\n      rangePlaceholder: ['Data inicial', 'Data final'],\n      monthFormat: 'MMMM'\n    },\n    timePickerLocale: {\n      placeholder: 'Hora'\n    }\n  },\n  Table: {\n    filterTitle: 'Filtro',\n    filterConfirm: 'Aplicar',\n    filterReset: 'Reiniciar',\n    selectAll: 'Selecionar página atual',\n    selectInvert: 'Inverter seleção',\n    sortTitle: 'Ordenação'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Cancelar',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Cancelar'\n  },\n  Transfer: {\n    searchPlaceholder: 'Procurar...',\n    itemUnit: 'item',\n    itemsUnit: 'itens'\n  },\n  Upload: {\n    uploading: 'A carregar...',\n    removeFile: 'Remover',\n    uploadError: 'Erro ao carregar',\n    previewFile: 'Pré-visualizar',\n    downloadFile: 'Baixar'\n  },\n  Empty: {\n    description: 'Sem resultados'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ro_RO = {\n  locale: 'ro',\n  Pagination: {\n    items_per_page: '/ pagină',\n    jump_to: 'Mergi la',\n    jump_to_confirm: 'confirm',\n    page: '',\n    prev_page: 'Pagina Anterioară',\n    next_page: 'Pagina Următoare',\n    prev_5: '5 Pagini Anterioare',\n    next_5: '5 Pagini Următoare',\n    prev_3: '3 Pagini Anterioare',\n    next_3: '3 Pagini Următoare',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Selectează data',\n      rangePlaceholder: ['Data start', 'Data sfârșit'],\n      locale: 'ro_RO',\n      today: 'Azi',\n      now: 'Acum',\n      backToToday: 'Înapoi la azi',\n      ok: 'Ok',\n      clear: 'Șterge',\n      month: 'Lună',\n      year: 'An',\n      timeSelect: 'selectează timpul',\n      dateSelect: 'selectează data',\n      weekSelect: 'Alege o săptămână',\n      monthSelect: 'Alege o lună',\n      yearSelect: 'Alege un an',\n      decadeSelect: 'Alege un deceniu',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Luna anterioară (PageUp)',\n      nextMonth: 'Luna următoare (PageDown)',\n      previousYear: 'Anul anterior (Control + stânga)',\n      nextYear: 'Anul următor (Control + dreapta)',\n      previousDecade: 'Deceniul anterior',\n      nextDecade: 'Deceniul următor',\n      previousCentury: 'Secolul anterior',\n      nextCentury: 'Secolul următor'\n    },\n    timePickerLocale: {\n      placeholder: 'Selectează ora'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Selectează ora'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Selectează data',\n      rangePlaceholder: ['Data start', 'Data sfârșit'],\n      locale: 'ro_RO',\n      today: 'Azi',\n      now: 'Acum',\n      backToToday: 'Înapoi la azi',\n      ok: 'Ok',\n      clear: 'Șterge',\n      month: 'Lună',\n      year: 'An',\n      timeSelect: 'selectează timpul',\n      dateSelect: 'selectează data',\n      weekSelect: 'Alege o săptămână',\n      monthSelect: 'Alege o lună',\n      yearSelect: 'Alege un an',\n      decadeSelect: 'Alege un deceniu',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Luna anterioară (PageUp)',\n      nextMonth: 'Luna următoare (PageDown)',\n      previousYear: 'Anul anterior (Control + stânga)',\n      nextYear: 'Anul următor (Control + dreapta)',\n      previousDecade: 'Deceniul anterior',\n      nextDecade: 'Deceniul următor',\n      previousCentury: 'Secolul anterior',\n      nextCentury: 'Secolul următor'\n    },\n    timePickerLocale: {\n      placeholder: 'Selectează ora'\n    }\n  },\n  global: {\n    placeholder: 'Selectează'\n  },\n  Table: {\n    filterTitle: 'Filtrează',\n    filterConfirm: 'OK',\n    filterReset: 'Resetează',\n    selectAll: 'Selectează pagina curentă',\n    selectInvert: 'Inversează pagina curentă',\n    sortTitle: 'Ordonează',\n    expand: 'Extinde rândul',\n    collapse: 'Micșorează rândul',\n    filterEmptyText: 'Fără filtre',\n    emptyText: 'Nu există date',\n    selectNone: 'Șterge selecția',\n    selectionAll: 'Selectează toate datele',\n    triggerDesc: 'Apasă pentru ordonare descrescătoare',\n    triggerAsc: 'Apasă pentru ordonare crescătoare',\n    cancelSort: 'Apasă pentru a anula ordonarea'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Anulare',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Anulare'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Căutare',\n    itemUnit: 'element',\n    itemsUnit: 'elemente',\n    remove: 'Șterge',\n    selectCurrent: 'Selectează pagina curentă',\n    removeCurrent: 'Șterge pagina curentă',\n    selectAll: 'Selectează toate datele',\n    removeAll: 'Șterge toate datele',\n    selectInvert: 'Inversează pagina curentă'\n  },\n  Upload: {\n    uploading: 'Se transferă...',\n    removeFile: 'Înlătură fișierul',\n    uploadError: 'Eroare la upload',\n    previewFile: 'Previzualizare fișier',\n    downloadFile: 'Descărcare fișier'\n  },\n  Empty: {\n    description: 'Fără date'\n  },\n  Icon: {\n    icon: 'icon'\n  },\n  Text: {\n    edit: 'editează',\n    copy: 'copiază',\n    copied: 'copiat',\n    expand: 'extinde'\n  },\n  PageHeader: {\n    back: 'înapoi'\n  },\n  Image: {\n    preview: 'Preview'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ru_RU = {\n  locale: 'ru',\n  Pagination: {\n    items_per_page: '/ стр.',\n    jump_to: 'Перейти',\n    jump_to_confirm: 'подтвердить',\n    page: 'Страница',\n    prev_page: 'Назад',\n    next_page: 'Вперед',\n    prev_5: 'Предыдущие 5',\n    next_5: 'Следующие 5',\n    prev_3: 'Предыдущие 3',\n    next_3: 'Следующие 3',\n    page_size: 'размер страницы'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Выберите дату',\n      yearPlaceholder: 'Выберите год',\n      quarterPlaceholder: 'Выберите квартал',\n      monthPlaceholder: 'Выберите месяц',\n      weekPlaceholder: 'Выберите неделю',\n      rangePlaceholder: ['Начальная дата', 'Конечная дата'],\n      rangeYearPlaceholder: ['Начальный год', 'Год окончания'],\n      rangeMonthPlaceholder: ['Начальный месяц', 'Конечный месяц'],\n      rangeWeekPlaceholder: ['Начальная неделя', 'Конечная неделя'],\n      locale: 'ru_RU',\n      today: 'Сегодня',\n      now: 'Сейчас',\n      backToToday: 'Текущая дата',\n      ok: 'ОК',\n      clear: 'Очистить',\n      month: 'Месяц',\n      year: 'Год',\n      timeSelect: 'Выбрать время',\n      dateSelect: 'Выбрать дату',\n      monthSelect: 'Выбрать месяц',\n      yearSelect: 'Выбрать год',\n      decadeSelect: 'Выбрать десятилетие',\n      yearFormat: 'YYYY',\n      dateFormat: 'D-M-YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D-M-YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Предыдущий месяц (PageUp)',\n      nextMonth: 'Следующий месяц (PageDown)',\n      previousYear: 'Предыдущий год (Control + left)',\n      nextYear: 'Следующий год (Control + right)',\n      previousDecade: 'Предыдущее десятилетие',\n      nextDecade: 'Следущее десятилетие',\n      previousCentury: 'Предыдущий век',\n      nextCentury: 'Следующий век'\n    },\n    timePickerLocale: {\n      placeholder: 'Выберите время',\n      rangePlaceholder: ['Время начала', 'Время окончания']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Выберите время',\n    rangePlaceholder: ['Время начала', 'Время окончания']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Выберите дату',\n      yearPlaceholder: 'Выберите год',\n      quarterPlaceholder: 'Выберите квартал',\n      monthPlaceholder: 'Выберите месяц',\n      weekPlaceholder: 'Выберите неделю',\n      rangePlaceholder: ['Начальная дата', 'Конечная дата'],\n      rangeYearPlaceholder: ['Начальный год', 'Год окончания'],\n      rangeMonthPlaceholder: ['Начальный месяц', 'Конечный месяц'],\n      rangeWeekPlaceholder: ['Начальная неделя', 'Конечная неделя'],\n      locale: 'ru_RU',\n      today: 'Сегодня',\n      now: 'Сейчас',\n      backToToday: 'Текущая дата',\n      ok: 'ОК',\n      clear: 'Очистить',\n      month: 'Месяц',\n      year: 'Год',\n      timeSelect: 'Выбрать время',\n      dateSelect: 'Выбрать дату',\n      monthSelect: 'Выбрать месяц',\n      yearSelect: 'Выбрать год',\n      decadeSelect: 'Выбрать десятилетие',\n      yearFormat: 'YYYY',\n      dateFormat: 'D-M-YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D-M-YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Предыдущий месяц (PageUp)',\n      nextMonth: 'Следующий месяц (PageDown)',\n      previousYear: 'Предыдущий год (Control + left)',\n      nextYear: 'Следующий год (Control + right)',\n      previousDecade: 'Предыдущее десятилетие',\n      nextDecade: 'Следущее десятилетие',\n      previousCentury: 'Предыдущий век',\n      nextCentury: 'Следующий век'\n    },\n    timePickerLocale: {\n      placeholder: 'Выберите время',\n      rangePlaceholder: ['Время начала', 'Время окончания']\n    }\n  },\n  global: {\n    placeholder: 'Пожалуйста выберите'\n  },\n  Table: {\n    filterTitle: 'Фильтр',\n    filterConfirm: 'OK',\n    filterReset: 'Сбросить',\n    filterEmptyText: 'Без фильтров',\n    emptyText: 'Нет данных',\n    selectAll: 'Выбрать всё',\n    selectInvert: 'Инвертировать выбор',\n    selectionAll: 'Выбрать все данные',\n    sortTitle: 'Сортировка',\n    expand: 'Развернуть строку',\n    collapse: 'Свернуть строку',\n    triggerDesc: 'Нажмите для сортировки по убыванию',\n    triggerAsc: 'Нажмите для сортировки по возрастанию',\n    cancelSort: 'Нажмите, чтобы отменить сортировку',\n    selectNone: 'Очистить все данные'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Отмена',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Отмена'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Поиск',\n    itemUnit: 'элем.',\n    itemsUnit: 'элем.',\n    remove: 'Удалить',\n    selectAll: 'Выбрать все данные',\n    selectCurrent: 'Выбрать текущую страницу',\n    selectInvert: 'Показать в обратном порядке',\n    removeAll: 'Удалить все данные',\n    removeCurrent: 'Удалить текущую страницу'\n  },\n  Upload: {\n    uploading: 'Загрузка...',\n    removeFile: 'Удалить файл',\n    uploadError: 'При загрузке произошла ошибка',\n    previewFile: 'Предпросмотр файла',\n    downloadFile: 'Загрузить файл'\n  },\n  Empty: {\n    description: 'Нет данных'\n  },\n  Icon: {\n    icon: 'иконка'\n  },\n  Text: {\n    edit: 'Редактировать',\n    copy: 'Копировать',\n    copied: 'Скопировано',\n    expand: 'Раскрыть'\n  },\n  PageHeader: {\n    back: 'Назад'\n  },\n  Image: {\n    preview: 'Предпросмотр'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar sk_SK = {\n  locale: 'sk',\n  Pagination: {\n    items_per_page: '/ strana',\n    jump_to: 'Choď na',\n    jump_to_confirm: 'potvrdit',\n    page: '',\n    prev_page: 'Predchádzajúca strana',\n    next_page: 'Nasledujúca strana',\n    prev_5: 'Predchádzajúcich 5 strán',\n    next_5: 'Nasledujúcich 5 strán',\n    prev_3: 'Predchádzajúce 3 strany',\n    next_3: 'Nasledujúce 3 strany',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Vybrať dátum',\n      rangePlaceholder: ['Od', 'Do'],\n      locale: 'sk_SK',\n      today: 'Dnes',\n      now: 'Teraz',\n      backToToday: 'Späť na dnes',\n      ok: 'Ok',\n      clear: 'Vymazať',\n      month: 'Mesiac',\n      year: 'Rok',\n      timeSelect: 'Vybrať čas',\n      dateSelect: 'Vybrať dátum',\n      monthSelect: 'Vybrať mesiac',\n      yearSelect: 'Vybrať rok',\n      decadeSelect: 'Vybrať dekádu',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Predchádzajúci mesiac (PageUp)',\n      nextMonth: 'Nasledujúci mesiac (PageDown)',\n      previousYear: 'Predchádzajúci rok (Control + left)',\n      nextYear: 'Nasledujúci rok (Control + right)',\n      previousDecade: 'Predchádzajúca dekáda',\n      nextDecade: 'Nasledujúca dekáda',\n      previousCentury: 'Predchádzajúce storočie',\n      nextCentury: 'Nasledujúce storočie'\n    },\n    timePickerLocale: {\n      placeholder: 'Vybrať čas'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Vybrať čas'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Vybrať dátum',\n      rangePlaceholder: ['Od', 'Do'],\n      locale: 'sk_SK',\n      today: 'Dnes',\n      now: 'Teraz',\n      backToToday: 'Späť na dnes',\n      ok: 'Ok',\n      clear: 'Vymazať',\n      month: 'Mesiac',\n      year: 'Rok',\n      timeSelect: 'Vybrať čas',\n      dateSelect: 'Vybrať dátum',\n      monthSelect: 'Vybrať mesiac',\n      yearSelect: 'Vybrať rok',\n      decadeSelect: 'Vybrať dekádu',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Predchádzajúci mesiac (PageUp)',\n      nextMonth: 'Nasledujúci mesiac (PageDown)',\n      previousYear: 'Predchádzajúci rok (Control + left)',\n      nextYear: 'Nasledujúci rok (Control + right)',\n      previousDecade: 'Predchádzajúca dekáda',\n      nextDecade: 'Nasledujúca dekáda',\n      previousCentury: 'Predchádzajúce storočie',\n      nextCentury: 'Nasledujúce storočie'\n    },\n    timePickerLocale: {\n      placeholder: 'Vybrať čas'\n    }\n  },\n  global: {\n    placeholder: 'Prosím vyberte'\n  },\n  Table: {\n    filterTitle: 'Filter',\n    filterConfirm: 'OK',\n    filterReset: 'Obnoviť',\n    selectAll: 'Vybrať všetko',\n    selectInvert: 'Vybrať opačné',\n    sortTitle: 'Zoradiť',\n    expand: 'Rozbaliť riadok',\n    collapse: 'Zbaliť riadok'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Zrušiť',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Zrušiť'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Vyhľadávanie',\n    itemUnit: 'položka',\n    itemsUnit: 'položiek'\n  },\n  Upload: {\n    uploading: 'Nahrávanie...',\n    removeFile: 'Odstrániť súbor',\n    uploadError: 'Chyba pri nahrávaní',\n    previewFile: 'Zobraziť súbor',\n    downloadFile: 'Stiahnuť súbor'\n  },\n  Empty: {\n    description: 'Žiadne dáta'\n  },\n  Icon: {\n    icon: 'ikona'\n  },\n  Text: {\n    edit: 'Upraviť',\n    copy: 'Kopírovať',\n    copied: 'Skopírované',\n    expand: 'Zväčšiť'\n  },\n  PageHeader: {\n    back: 'Späť'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar sl_SI = {\n  locale: 'sl',\n  Pagination: {\n    items_per_page: '/ strani',\n    jump_to: 'Pojdi na',\n    jump_to_confirm: 'potrdi',\n    page: '',\n    prev_page: 'Prejšnja stran',\n    next_page: 'Naslednja stran',\n    prev_5: 'Prejšnjih 5 strani',\n    next_5: 'Naslednjih 5 strani',\n    prev_3: 'Prejšnje 3 strani',\n    next_3: 'Naslednje 3 strani',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      locale: 'sl',\n      placeholder: 'Izberite datum',\n      rangePlaceholder: ['Začetni datum', 'Končni datum'],\n      today: 'Danes',\n      now: 'Trenutno',\n      backToToday: 'Nazaj na trenutni datum',\n      ok: 'OK',\n      clear: 'Počisti',\n      month: 'Mesec',\n      year: 'Leto',\n      timeSelect: 'Izberi čas',\n      dateSelect: 'Izberi datum',\n      monthSelect: 'Izberite mesec',\n      yearSelect: 'Izberite leto',\n      decadeSelect: 'Izberite desetletje',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthFormat: 'MMMM',\n      monthBeforeYear: true,\n      previousMonth: 'Prejšnji mesec (PageUp)',\n      nextMonth: 'Naslednji mesec (PageDown)',\n      previousYear: 'Lansko leto (Control + left)',\n      nextYear: 'Naslednje leto (Control + right)',\n      previousDecade: 'Prejšnje desetletje',\n      nextDecade: 'Naslednje desetletje',\n      previousCentury: 'Zadnje stoletje',\n      nextCentury: 'Naslednje stoletje'\n    },\n    timePickerLocale: {\n      placeholder: 'Izberite čas'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Izberite čas'\n  },\n  Calendar: {\n    lang: {\n      locale: 'sl',\n      placeholder: 'Izberite datum',\n      rangePlaceholder: ['Začetni datum', 'Končni datum'],\n      today: 'Danes',\n      now: 'Trenutno',\n      backToToday: 'Nazaj na trenutni datum',\n      ok: 'OK',\n      clear: 'Počisti',\n      month: 'Mesec',\n      year: 'Leto',\n      timeSelect: 'Izberi čas',\n      dateSelect: 'Izberi datum',\n      monthSelect: 'Izberite mesec',\n      yearSelect: 'Izberite leto',\n      decadeSelect: 'Izberite desetletje',\n      yearFormat: 'YYYY',\n      dateFormat: 'D.M.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D.M.YYYY HH:mm:ss',\n      monthFormat: 'MMMM',\n      monthBeforeYear: true,\n      previousMonth: 'Prejšnji mesec (PageUp)',\n      nextMonth: 'Naslednji mesec (PageDown)',\n      previousYear: 'Lansko leto (Control + left)',\n      nextYear: 'Naslednje leto (Control + right)',\n      previousDecade: 'Prejšnje desetletje',\n      nextDecade: 'Naslednje desetletje',\n      previousCentury: 'Zadnje stoletje',\n      nextCentury: 'Naslednje stoletje'\n    },\n    timePickerLocale: {\n      placeholder: 'Izberite čas'\n    }\n  },\n  Table: {\n    filterTitle: 'Filter',\n    filterConfirm: 'Filtriraj',\n    filterReset: 'Pobriši filter',\n    selectAll: 'Izberi vse na trenutni strani',\n    selectInvert: 'Obrni izbor na trenutni strani'\n  },\n  Modal: {\n    okText: 'V redu',\n    cancelText: 'Prekliči',\n    justOkText: 'V redu'\n  },\n  Popconfirm: {\n    okText: 'v redu',\n    cancelText: 'Prekliči'\n  },\n  Transfer: {\n    searchPlaceholder: 'Išči tukaj',\n    itemUnit: 'Objekt',\n    itemsUnit: 'Objektov'\n  },\n  Upload: {\n    uploading: 'Nalaganje...',\n    removeFile: 'Odstrani datoteko',\n    uploadError: 'Napaka pri nalaganju',\n    previewFile: 'Predogled datoteke',\n    downloadFile: 'Prenos datoteke'\n  },\n  Empty: {\n    description: 'Ni podatkov'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar sr_RS = {\n  locale: 'sr',\n  Pagination: {\n    items_per_page: '/ strani',\n    jump_to: 'Idi na',\n    page: '',\n    prev_page: 'Prethodna strana',\n    next_page: 'Sledeća strana',\n    prev_5: 'Prethodnih 5 Strana',\n    next_5: 'Sledećih 5 Strana',\n    prev_3: 'Prethodnih 3 Strane',\n    next_3: 'Sledećih 3 Strane',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Izaberi datum',\n      rangePlaceholder: ['Datum početka', 'Datum završetka'],\n      locale: 'sr_RS',\n      today: 'Danas',\n      now: 'Sada',\n      backToToday: 'Vrati se na danas',\n      ok: 'U redu',\n      clear: 'Obriši',\n      month: 'Mesec',\n      year: 'Godina',\n      timeSelect: 'Izaberi vreme',\n      dateSelect: 'Izaberi datum',\n      monthSelect: 'Izaberi mesec',\n      yearSelect: 'Izaberi godinu',\n      decadeSelect: 'Izaberi deceniju',\n      yearFormat: 'YYYY',\n      dateFormat: 'DD.MM.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'DD.MM.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Prethodni mesec (PageUp)',\n      nextMonth: 'Sledeći mesec (PageDown)',\n      previousYear: 'Prethodna godina (Control + left)',\n      nextYear: 'Sledeća godina (Control + right)',\n      previousDecade: 'Prethodna decenija',\n      nextDecade: 'Sledeća decenija',\n      previousCentury: 'Prethodni vek',\n      nextCentury: 'Sledeći vek',\n      yearPlaceholder: 'Izaberi godinu',\n      quarterPlaceholder: 'Izaberi tromesečje',\n      monthPlaceholder: 'Izaberi mesec',\n      weekPlaceholder: 'Izaberi sedmicu',\n      rangeYearPlaceholder: ['Godina početka', 'Godina završetka'],\n      rangeMonthPlaceholder: ['Mesec početka', 'Mesec završetka'],\n      rangeWeekPlaceholder: ['Sedmica početka', 'Sedmica završetka']\n    },\n    timePickerLocale: {\n      placeholder: 'Izaberi vreme',\n      rangePlaceholder: ['Vreme početka', 'Vreme završetka']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Izaberi vreme',\n    rangePlaceholder: ['Vreme početka', 'Vreme završetka']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Izaberi datum',\n      rangePlaceholder: ['Datum početka', 'Datum završetka'],\n      locale: 'sr_RS',\n      today: 'Danas',\n      now: 'Sada',\n      backToToday: 'Vrati se na danas',\n      ok: 'U redu',\n      clear: 'Obriši',\n      month: 'Mesec',\n      year: 'Godina',\n      timeSelect: 'Izaberi vreme',\n      dateSelect: 'Izaberi datum',\n      monthSelect: 'Izaberi mesec',\n      yearSelect: 'Izaberi godinu',\n      decadeSelect: 'Izaberi deceniju',\n      yearFormat: 'YYYY',\n      dateFormat: 'DD.MM.YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'DD.MM.YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Prethodni mesec (PageUp)',\n      nextMonth: 'Sledeći mesec (PageDown)',\n      previousYear: 'Prethodna godina (Control + left)',\n      nextYear: 'Sledeća godina (Control + right)',\n      previousDecade: 'Prethodna decenija',\n      nextDecade: 'Sledeća decenija',\n      previousCentury: 'Prethodni vek',\n      nextCentury: 'Sledeći vek',\n      yearPlaceholder: 'Izaberi godinu',\n      quarterPlaceholder: 'Izaberi tromesečje',\n      monthPlaceholder: 'Izaberi mesec',\n      weekPlaceholder: 'Izaberi sedmicu',\n      rangeYearPlaceholder: ['Godina početka', 'Godina završetka'],\n      rangeMonthPlaceholder: ['Mesec početka', 'Mesec završetka'],\n      rangeWeekPlaceholder: ['Sedmica početka', 'Sedmica završetka']\n    },\n    timePickerLocale: {\n      placeholder: 'Izaberi vreme',\n      rangePlaceholder: ['Vreme početka', 'Vreme završetka']\n    }\n  },\n  Table: {\n    filterTitle: 'Meni filtera',\n    filterConfirm: 'U redu',\n    filterReset: 'Poništi',\n    selectAll: 'Izaberi trenutnu stranicu',\n    selectInvert: 'Obrni izbor trenutne stranice',\n    filterEmptyText: 'Nema filtera',\n    emptyText: 'Nema podataka',\n    selectNone: 'Obriši sve podatke',\n    selectionAll: 'Izaberi sve podatke',\n    sortTitle: 'Sortiraj',\n    expand: 'Proširi red',\n    collapse: 'Skupi red',\n    triggerDesc: 'Klikni da sortiraš po padajućem redosledu',\n    triggerAsc: 'Klikni da sortiraš po rastućem redosledu',\n    cancelSort: 'Klikni da otkažeš sortiranje'\n  },\n  Modal: {\n    okText: 'U redu',\n    cancelText: 'Otkaži',\n    justOkText: 'U redu'\n  },\n  Popconfirm: {\n    okText: 'U redu',\n    cancelText: 'Otkaži'\n  },\n  Transfer: {\n    searchPlaceholder: 'Pretraži ovde',\n    itemUnit: 'stavka',\n    itemsUnit: 'stavki',\n    titles: ['', ''],\n    remove: 'Ukloni',\n    selectCurrent: 'Izaberi trenutnu stranicu',\n    removeCurrent: 'Ukloni trenutnu stranicu',\n    selectAll: 'Izaberi sve podatke',\n    removeAll: 'Ukloni sve podatke',\n    selectInvert: 'Obrni izbor trenutne stranice'\n  },\n  Upload: {\n    uploading: 'Otpremanje...',\n    removeFile: 'Ukloni datoteku',\n    uploadError: 'Greška pri otpremanju',\n    previewFile: 'Pregledaj datoteku',\n    downloadFile: 'Preuzmi datoteku'\n  },\n  Empty: {\n    description: 'Nema podataka'\n  },\n  global: {\n    placeholder: 'Izaberi'\n  },\n  Icon: {\n    icon: 'ikona'\n  },\n  Text: {\n    edit: 'Uredi',\n    copy: 'Kopiraj',\n    copied: 'Kopirano',\n    expand: 'Proširi'\n  },\n  PageHeader: {\n    back: 'Nazad'\n  },\n  Image: {\n    preview: 'Pregled'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar sv_SE = {\n  locale: 'sv',\n  Pagination: {\n    items_per_page: '/ sida',\n    jump_to: 'Gå till',\n    jump_to_confirm: 'bekräfta',\n    page: 'Sida',\n    prev_page: 'Föreg sida',\n    next_page: 'Nästa sida',\n    prev_5: 'Föreg 5 sidor',\n    next_5: 'Nästa 5 sidor',\n    prev_3: 'Föreg 3 sidor',\n    next_3: 'Nästa 3 sidor',\n    page_size: 'sidstorlek'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Välj datum',\n      rangePlaceholder: ['Startdatum', 'Slutdatum'],\n      locale: 'sv_SE',\n      today: 'I dag',\n      now: 'Nu',\n      backToToday: 'Till idag',\n      ok: 'Ok',\n      clear: 'Avbryt',\n      month: 'Månad',\n      year: 'År',\n      timeSelect: 'Välj tidpunkt',\n      dateSelect: 'Välj datum',\n      monthSelect: 'Välj månad',\n      yearSelect: 'Välj år',\n      decadeSelect: 'Välj årtionde',\n      yearFormat: 'YYYY',\n      dateFormat: 'YYYY-MM-DD',\n      dayFormat: 'D',\n      dateTimeFormat: 'YYYY-MM-DD H:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Förra månaden (PageUp)',\n      nextMonth: 'Nästa månad (PageDown)',\n      previousYear: 'Föreg år (Control + left)',\n      nextYear: 'Nästa år (Control + right)',\n      previousDecade: 'Föreg årtionde',\n      nextDecade: 'Nästa årtionde',\n      previousCentury: 'Föreg århundrade',\n      nextCentury: 'Nästa århundrade',\n      yearPlaceholder: 'Välj år',\n      quarterPlaceholder: 'Välj kvartal',\n      monthPlaceholder: 'Välj månad',\n      weekPlaceholder: 'Välj vecka',\n      rangeYearPlaceholder: ['Startår', 'Slutår'],\n      rangeMonthPlaceholder: ['Startmånad', 'Slutmånad'],\n      rangeWeekPlaceholder: ['Startvecka', 'Slutvecka']\n    },\n    timePickerLocale: {\n      placeholder: 'Välj tid'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Välj tid'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Välj datum',\n      rangePlaceholder: ['Startdatum', 'Slutdatum'],\n      locale: 'sv_SE',\n      today: 'I dag',\n      now: 'Nu',\n      backToToday: 'Till idag',\n      ok: 'Ok',\n      clear: 'Avbryt',\n      month: 'Månad',\n      year: 'År',\n      timeSelect: 'Välj tidpunkt',\n      dateSelect: 'Välj datum',\n      monthSelect: 'Välj månad',\n      yearSelect: 'Välj år',\n      decadeSelect: 'Välj årtionde',\n      yearFormat: 'YYYY',\n      dateFormat: 'YYYY-MM-DD',\n      dayFormat: 'D',\n      dateTimeFormat: 'YYYY-MM-DD H:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Förra månaden (PageUp)',\n      nextMonth: 'Nästa månad (PageDown)',\n      previousYear: 'Föreg år (Control + left)',\n      nextYear: 'Nästa år (Control + right)',\n      previousDecade: 'Föreg årtionde',\n      nextDecade: 'Nästa årtionde',\n      previousCentury: 'Föreg århundrade',\n      nextCentury: 'Nästa århundrade',\n      yearPlaceholder: 'Välj år',\n      quarterPlaceholder: 'Välj kvartal',\n      monthPlaceholder: 'Välj månad',\n      weekPlaceholder: 'Välj vecka',\n      rangeYearPlaceholder: ['Startår', 'Slutår'],\n      rangeMonthPlaceholder: ['Startmånad', 'Slutmånad'],\n      rangeWeekPlaceholder: ['Startvecka', 'Slutvecka']\n    },\n    timePickerLocale: {\n      placeholder: 'Välj tid'\n    }\n  },\n  Table: {\n    filterTitle: 'Filtermeny',\n    filterConfirm: 'OK',\n    filterReset: 'Återställ',\n    filterEmptyText: 'Inga filter',\n    emptyText: 'Ingen data',\n    selectAll: 'Markera nuvarande sida',\n    selectInvert: 'Invertera nuvarande sida',\n    selectNone: 'Avmarkera all data',\n    selectionAll: 'Markera all data',\n    sortTitle: 'Sortera',\n    expand: 'Expandera rad',\n    collapse: 'Komprimera rad',\n    triggerDesc: 'Klicka för att sortera i fallande ordning',\n    triggerAsc: 'Klicka för att sortera i stigande ordning',\n    cancelSort: 'Klicka för att avbryta sortering'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Avbryt',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Avbryt'\n  },\n  Transfer: {\n    searchPlaceholder: 'Sök här',\n    itemUnit: 'objekt',\n    itemsUnit: 'objekt',\n    titles: ['', ''],\n    remove: 'Ta bort',\n    selectCurrent: 'Markera nuvarande sida',\n    removeCurrent: 'Ta bort nuvarande sida',\n    selectAll: 'Markera all data',\n    removeAll: 'Ta bort all data',\n    selectInvert: 'Invertera nuvarande sida'\n  },\n  Empty: {\n    description: 'Ingen data'\n  },\n  Text: {\n    edit: 'Redigera',\n    copy: 'Kopiera',\n    copied: 'Kopierad',\n    expand: 'Expandera'\n  },\n  Upload: {\n    uploading: 'Laddar upp...',\n    removeFile: 'Ta bort fil',\n    uploadError: 'Uppladdningsfel',\n    previewFile: 'Förhandsgranska fil',\n    downloadFile: 'Ladda ned fil'\n  },\n  global: {\n    placeholder: 'Vänligen välj'\n  },\n  Icon: {\n    icon: 'ikon'\n  },\n  PageHeader: {\n    back: 'Tillbaka'\n  },\n  Image: {\n    preview: 'Förhandsgranska'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ta_IN = {\n  locale: 'ta',\n  Pagination: {\n    items_per_page: '/ பக்கம்',\n    jump_to: 'அடுத்த',\n    jump_to_confirm: 'உறுதிப்படுத்தவும்',\n    page: '',\n    prev_page: 'முந்தைய பக்கம்',\n    next_page: 'அடுத்த பக்கம்',\n    prev_5: 'முந்தைய 5 பக்கங்கள்',\n    next_5: 'அடுத்த 5 பக்கங்கள்',\n    prev_3: 'முந்தைய 3 பக்கங்கள்',\n    next_3: 'அடுத்த 3 பக்கங்கள்',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'தேதியைத் தேர்ந்தெடுக்கவும்',\n      rangePlaceholder: ['தொடக்க தேதி', 'கடைசி தேதி'],\n      locale: 'ta_IN',\n      today: 'இன்று',\n      now: 'இப்போது',\n      backToToday: 'இன்றுக்கு திரும்பு',\n      ok: 'சரி',\n      clear: 'அழி',\n      month: 'மாதம்',\n      year: 'வருடம்',\n      timeSelect: 'நேரத்தைத் தேர்ந்தெடு',\n      dateSelect: 'தேதியைத் தேர்ந்தெடு',\n      weekSelect: 'வாரத்தைத் தேர்வுசெய்க',\n      monthSelect: 'மாதத்தைத் தேர்வுசெய்க',\n      yearSelect: 'வருடத்தைத் தேர்வுசெய்க',\n      decadeSelect: 'தசாப்தத்தைத் தேர்வுசெய்க',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'முந்தைய மாதம் (PageUp)',\n      nextMonth: 'அடுத்த மாதம் (PageDown)',\n      previousYear: 'முந்தைய வருடம் (Control + left)',\n      nextYear: 'அடுத்த வருடம் (Control + right)',\n      previousDecade: 'முந்தைய தசாப்தம்',\n      nextDecade: 'அடுத்த தசாப்தம்',\n      previousCentury: 'முந்தைய நூற்றாண்டு',\n      nextCentury: 'அடுத்த நூற்றாண்டு'\n    },\n    timePickerLocale: {\n      placeholder: 'நேரத்தைத் தேர்ந்தெடுக்கவும்'\n    }\n  },\n  TimePicker: {\n    placeholder: 'நேரத்தைத் தேர்ந்தெடுக்கவும்'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'தேதியைத் தேர்ந்தெடுக்கவும்',\n      rangePlaceholder: ['தொடக்க தேதி', 'கடைசி தேதி'],\n      locale: 'ta_IN',\n      today: 'இன்று',\n      now: 'இப்போது',\n      backToToday: 'இன்றுக்கு திரும்பு',\n      ok: 'சரி',\n      clear: 'அழி',\n      month: 'மாதம்',\n      year: 'வருடம்',\n      timeSelect: 'நேரத்தைத் தேர்ந்தெடு',\n      dateSelect: 'தேதியைத் தேர்ந்தெடு',\n      weekSelect: 'வாரத்தைத் தேர்வுசெய்க',\n      monthSelect: 'மாதத்தைத் தேர்வுசெய்க',\n      yearSelect: 'வருடத்தைத் தேர்வுசெய்க',\n      decadeSelect: 'தசாப்தத்தைத் தேர்வுசெய்க',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'முந்தைய மாதம் (PageUp)',\n      nextMonth: 'அடுத்த மாதம் (PageDown)',\n      previousYear: 'முந்தைய வருடம் (Control + left)',\n      nextYear: 'அடுத்த வருடம் (Control + right)',\n      previousDecade: 'முந்தைய தசாப்தம்',\n      nextDecade: 'அடுத்த தசாப்தம்',\n      previousCentury: 'முந்தைய நூற்றாண்டு',\n      nextCentury: 'அடுத்த நூற்றாண்டு'\n    },\n    timePickerLocale: {\n      placeholder: 'நேரத்தைத் தேர்ந்தெடுக்கவும்'\n    }\n  },\n  global: {\n    placeholder: 'தேதியைத் தேர்ந்தெடுக்கவும்'\n  },\n  Table: {\n    filterTitle: 'பட்டியலை மூடு',\n    filterConfirm: 'சரி',\n    filterReset: 'மீட்டமை',\n    emptyText: 'தகவல் இல்லை',\n    selectAll: 'அனைத்தையும் தேர்வுசெய்',\n    selectInvert: 'தலைகீழாக மாற்று',\n    sortTitle: 'தலைப்பை வரிசைப்படுத்தவும்'\n  },\n  Modal: {\n    okText: 'சரி',\n    cancelText: 'ரத்து செய்யவும்',\n    justOkText: 'பரவாயில்லை, சரி'\n  },\n  Popconfirm: {\n    okText: 'சரி',\n    cancelText: 'ரத்து செய்யவும்'\n  },\n  Transfer: {\n    titles: ['', ''],\n    notFoundContent: 'உள்ளடக்கம் கிடைக்கவில்லை',\n    searchPlaceholder: 'இங்கு தேடவும்',\n    itemUnit: 'தகவல்',\n    itemsUnit: 'தகவல்கள்'\n  },\n  Upload: {\n    uploading: 'பதிவேற்றுகிறது...',\n    removeFile: 'கோப்பை அகற்று',\n    uploadError: 'பதிவேற்றுவதில் பிழை',\n    previewFile: 'கோப்பை முன்னோட்டமிடுங்கள்',\n    downloadFile: 'பதிவிறக்க கோப்பு'\n  },\n  Empty: {\n    description: 'தகவல் இல்லை'\n  },\n  Icon: {\n    icon: 'உருவம்'\n  },\n  Text: {\n    edit: 'திருத்து',\n    copy: 'நகல் எடு',\n    copied: 'நகல் எடுக்கப்பட்டது',\n    expand: 'விரிவாக்கவும்'\n  },\n  PageHeader: {\n    back: 'பின் செல்லவும்'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar th_TH = {\n  locale: 'th',\n  Pagination: {\n    items_per_page: '/ หน้า',\n    jump_to: 'ไปยัง',\n    jump_to_confirm: 'ยืนยัน',\n    page: 'หน้า',\n    prev_page: 'หน้าก่อนหน้า',\n    next_page: 'หน้าถัดไป',\n    prev_5: 'ย้อนกลับ 5 หน้า',\n    next_5: 'ถัดไป 5 หน้า',\n    prev_3: 'ย้อนกลับ 3 หน้า',\n    next_3: 'ถัดไป 3 หน้า',\n    page_size: 'ขนาดหน้า'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'เลือกวันที่',\n      yearPlaceholder: 'เลือกปี',\n      quarterPlaceholder: 'เลือกไตรมาส',\n      monthPlaceholder: 'เลือกเดือน',\n      weekPlaceholder: 'เลือกสัปดาห์',\n      rangePlaceholder: ['วันเริ่มต้น', 'วันสิ้นสุด'],\n      rangeYearPlaceholder: ['ปีเริ่มต้น', 'ปีสิ้นสุด'],\n      rangeMonthPlaceholder: ['เดือนเริ่มต้น', 'เดือนสิ้นสุด'],\n      rangeWeekPlaceholder: ['สัปดาห์เริ่มต้น', 'สัปดาห์สิ้นสุด'],\n      locale: 'th_TH',\n      today: 'วันนี้',\n      now: 'ตอนนี้',\n      backToToday: 'กลับไปยังวันนี้',\n      ok: 'ตกลง',\n      clear: 'ลบล้าง',\n      month: 'เดือน',\n      year: 'ปี',\n      timeSelect: 'เลือกเวลา',\n      dateSelect: 'เลือกวัน',\n      monthSelect: 'เลือกเดือน',\n      yearSelect: 'เลือกปี',\n      decadeSelect: 'เลือกทศวรรษ',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'เดือนก่อนหน้า (PageUp)',\n      nextMonth: 'เดือนถัดไป (PageDown)',\n      previousYear: 'ปีก่อนหน้า (Control + left)',\n      nextYear: 'ปีถัดไป (Control + right)',\n      previousDecade: 'ทศวรรษก่อนหน้า',\n      nextDecade: 'ทศวรรษถัดไป',\n      previousCentury: 'ศตวรรษก่อนหน้า',\n      nextCentury: 'ศตวรรษถัดไป'\n    },\n    timePickerLocale: {\n      placeholder: 'เลือกเวลา'\n    }\n  },\n  TimePicker: {\n    placeholder: 'เลือกเวลา'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'เลือกวันที่',\n      yearPlaceholder: 'เลือกปี',\n      quarterPlaceholder: 'เลือกไตรมาส',\n      monthPlaceholder: 'เลือกเดือน',\n      weekPlaceholder: 'เลือกสัปดาห์',\n      rangePlaceholder: ['วันเริ่มต้น', 'วันสิ้นสุด'],\n      rangeYearPlaceholder: ['ปีเริ่มต้น', 'ปีสิ้นสุด'],\n      rangeMonthPlaceholder: ['เดือนเริ่มต้น', 'เดือนสิ้นสุด'],\n      rangeWeekPlaceholder: ['สัปดาห์เริ่มต้น', 'สัปดาห์สิ้นสุด'],\n      locale: 'th_TH',\n      today: 'วันนี้',\n      now: 'ตอนนี้',\n      backToToday: 'กลับไปยังวันนี้',\n      ok: 'ตกลง',\n      clear: 'ลบล้าง',\n      month: 'เดือน',\n      year: 'ปี',\n      timeSelect: 'เลือกเวลา',\n      dateSelect: 'เลือกวัน',\n      monthSelect: 'เลือกเดือน',\n      yearSelect: 'เลือกปี',\n      decadeSelect: 'เลือกทศวรรษ',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'เดือนก่อนหน้า (PageUp)',\n      nextMonth: 'เดือนถัดไป (PageDown)',\n      previousYear: 'ปีก่อนหน้า (Control + left)',\n      nextYear: 'ปีถัดไป (Control + right)',\n      previousDecade: 'ทศวรรษก่อนหน้า',\n      nextDecade: 'ทศวรรษถัดไป',\n      previousCentury: 'ศตวรรษก่อนหน้า',\n      nextCentury: 'ศตวรรษถัดไป'\n    },\n    timePickerLocale: {\n      placeholder: 'เลือกเวลา'\n    }\n  },\n  global: {\n    placeholder: 'กรุณาเลือก'\n  },\n  Table: {\n    filterTitle: 'ตัวกรอง',\n    filterConfirm: 'ยืนยัน',\n    filterReset: 'รีเซ็ต',\n    filterEmptyText: 'ไม่มีตัวกรอง',\n    emptyText: 'ไม่มีข้อมูล',\n    selectAll: 'เลือกทั้งหมดในหน้านี้',\n    selectInvert: 'กลับสถานะการเลือกในหน้านี้',\n    selectionAll: 'เลือกข้อมูลทั้งหมด',\n    sortTitle: 'เรียง',\n    expand: 'แสดงแถวข้อมูล',\n    collapse: 'ย่อแถวข้อมูล',\n    triggerDesc: 'คลิกเรียงจากมากไปน้อย',\n    triggerAsc: 'คลิกเรียงจากน้อยไปมาก',\n    cancelSort: 'คลิกเพื่อยกเลิกการเรียง'\n  },\n  Modal: {\n    okText: 'ตกลง',\n    cancelText: 'ยกเลิก',\n    justOkText: 'ตกลง'\n  },\n  Popconfirm: {\n    okText: 'ตกลง',\n    cancelText: 'ยกเลิก'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'ค้นหา',\n    itemUnit: 'ชิ้น',\n    itemsUnit: 'ชิ้น',\n    remove: 'นำออก',\n    selectCurrent: 'เลือกทั้งหมดในหน้านี้',\n    removeCurrent: 'นำออกทั้งหมดในหน้านี้',\n    selectAll: 'เลือกข้อมูลทั้งหมด',\n    removeAll: 'นำข้อมูลออกทั้งหมด',\n    selectInvert: 'กลับสถานะการเลือกในหน้านี้'\n  },\n  Upload: {\n    uploading: 'กำลังอัปโหลด...',\n    removeFile: 'ลบไฟล์',\n    uploadError: 'เกิดข้อผิดพลาดในการอัปโหลด',\n    previewFile: 'ดูตัวอย่างไฟล์',\n    downloadFile: 'ดาวน์โหลดไฟล์'\n  },\n  Empty: {\n    description: 'ไม่มีข้อมูล'\n  },\n  Icon: {\n    icon: 'ไอคอน'\n  },\n  Text: {\n    edit: 'แก้ไข',\n    copy: 'คัดลอก',\n    copied: 'คัดลอกแล้ว',\n    expand: 'ขยาย'\n  },\n  PageHeader: {\n    back: 'ย้อนกลับ'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar tr_TR = {\n  locale: 'tr',\n  Pagination: {\n    items_per_page: '/ sayfa',\n    jump_to: 'Git',\n    jump_to_confirm: 'onayla',\n    page: 'Sayfa',\n    prev_page: 'Önceki Sayfa',\n    next_page: 'Sonraki Sayfa',\n    prev_5: 'Önceki 5 Sayfa',\n    next_5: 'Sonraki 5 Sayfa',\n    prev_3: 'Önceki 3 Sayfa',\n    next_3: 'Sonraki 3 Sayfa',\n    page_size: 'sayfa boyutu'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Tarih seç',\n      yearPlaceholder: 'Yıl seç',\n      quarterPlaceholder: 'Çeyrek seç',\n      monthPlaceholder: 'Ay seç',\n      weekPlaceholder: 'Hafta seç',\n      rangePlaceholder: ['Başlangıç tarihi', 'Bitiş tarihi'],\n      rangeYearPlaceholder: ['Başlangıç yılı', 'Bitiş yılı'],\n      rangeMonthPlaceholder: ['Başlangıç ayı', 'Bitiş ayı'],\n      rangeWeekPlaceholder: ['Başlangıç haftası', 'Bitiş haftası'],\n      locale: 'tr_TR',\n      today: 'Bugün',\n      now: 'Şimdi',\n      backToToday: 'Bugüne Geri Dön',\n      ok: 'tamam',\n      clear: 'Temizle',\n      month: 'Ay',\n      year: 'Yıl',\n      timeSelect: 'Zaman Seç',\n      dateSelect: 'Tarih Seç',\n      monthSelect: 'Ay Seç',\n      yearSelect: 'Yıl Seç',\n      decadeSelect: 'On Yıl Seç',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Önceki Ay (PageUp)',\n      nextMonth: 'Sonraki Ay (PageDown)',\n      previousYear: 'Önceki Yıl (Control + Sol)',\n      nextYear: 'Sonraki Yıl (Control + Sağ)',\n      previousDecade: 'Önceki On Yıl',\n      nextDecade: 'Sonraki On Yıl',\n      previousCentury: 'Önceki Yüzyıl',\n      nextCentury: 'Sonraki Yüzyıl'\n    },\n    timePickerLocale: {\n      placeholder: 'Zaman seç',\n      rangePlaceholder: ['Başlangıç zamanı', 'Bitiş zamanı']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Zaman seç',\n    rangePlaceholder: ['Başlangıç zamanı', 'Bitiş zamanı']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Tarih seç',\n      yearPlaceholder: 'Yıl seç',\n      quarterPlaceholder: 'Çeyrek seç',\n      monthPlaceholder: 'Ay seç',\n      weekPlaceholder: 'Hafta seç',\n      rangePlaceholder: ['Başlangıç tarihi', 'Bitiş tarihi'],\n      rangeYearPlaceholder: ['Başlangıç yılı', 'Bitiş yılı'],\n      rangeMonthPlaceholder: ['Başlangıç ayı', 'Bitiş ayı'],\n      rangeWeekPlaceholder: ['Başlangıç haftası', 'Bitiş haftası'],\n      locale: 'tr_TR',\n      today: 'Bugün',\n      now: 'Şimdi',\n      backToToday: 'Bugüne Geri Dön',\n      ok: 'tamam',\n      clear: 'Temizle',\n      month: 'Ay',\n      year: 'Yıl',\n      timeSelect: 'Zaman Seç',\n      dateSelect: 'Tarih Seç',\n      monthSelect: 'Ay Seç',\n      yearSelect: 'Yıl Seç',\n      decadeSelect: 'On Yıl Seç',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Önceki Ay (PageUp)',\n      nextMonth: 'Sonraki Ay (PageDown)',\n      previousYear: 'Önceki Yıl (Control + Sol)',\n      nextYear: 'Sonraki Yıl (Control + Sağ)',\n      previousDecade: 'Önceki On Yıl',\n      nextDecade: 'Sonraki On Yıl',\n      previousCentury: 'Önceki Yüzyıl',\n      nextCentury: 'Sonraki Yüzyıl'\n    },\n    timePickerLocale: {\n      placeholder: 'Zaman seç',\n      rangePlaceholder: ['Başlangıç zamanı', 'Bitiş zamanı']\n    }\n  },\n  global: {\n    placeholder: 'Lütfen seçiniz'\n  },\n  Table: {\n    filterTitle: 'Filtre menüsü',\n    filterConfirm: 'Tamam',\n    filterReset: 'Sıfırla',\n    filterEmptyText: 'Filtre yok',\n    selectAll: 'Tüm sayfayı seç',\n    selectInvert: 'Tersini seç',\n    selectionAll: 'Tümünü seç',\n    sortTitle: 'Sırala',\n    expand: 'Satırı genişlet',\n    collapse: 'Satırı daralt',\n    triggerDesc: 'Azalan düzende sırala',\n    triggerAsc: 'Artan düzende sırala',\n    cancelSort: 'Sıralamayı kaldır'\n  },\n  Modal: {\n    okText: 'Tamam',\n    cancelText: 'İptal',\n    justOkText: 'Tamam'\n  },\n  Popconfirm: {\n    okText: 'Tamam',\n    cancelText: 'İptal'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Arama',\n    itemUnit: 'Öğe',\n    itemsUnit: 'Öğeler',\n    remove: 'Kaldır',\n    selectCurrent: 'Tüm sayfayı seç',\n    removeCurrent: 'Sayfayı kaldır',\n    selectAll: 'Tümünü seç',\n    removeAll: 'Tümünü kaldır',\n    selectInvert: 'Tersini seç'\n  },\n  Upload: {\n    uploading: 'Yükleniyor...',\n    removeFile: 'Dosyayı kaldır',\n    uploadError: 'Yükleme hatası',\n    previewFile: 'Dosyayı önizle',\n    downloadFile: 'Dosyayı indir'\n  },\n  Empty: {\n    description: 'Veri Yok'\n  },\n  Icon: {\n    icon: 'ikon'\n  },\n  Text: {\n    edit: 'Düzenle',\n    copy: 'Kopyala',\n    copied: 'Kopyalandı',\n    expand: 'Genişlet'\n  },\n  PageHeader: {\n    back: 'Geri'\n  },\n  Image: {\n    preview: 'Önizleme'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar uk_UA = {\n  locale: 'uk',\n  Pagination: {\n    items_per_page: '/ сторінці',\n    jump_to: 'Перейти',\n    jump_to_confirm: 'підтвердити',\n    page: '',\n    prev_page: 'Попередня сторінка',\n    next_page: 'Наступна сторінка',\n    prev_5: 'Попередні 5 сторінок',\n    next_5: 'Наступні 5 сторінок',\n    prev_3: 'Попередні 3 сторінки',\n    next_3: 'Наступні 3 сторінки',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Оберіть дату',\n      yearPlaceholder: 'Оберіть рік',\n      quarterPlaceholder: 'Оберіть квартал',\n      monthPlaceholder: 'Оберіть місяць',\n      weekPlaceholder: 'Оберіть тиждень',\n      rangePlaceholder: ['Початкова дата', 'Кінцева дата'],\n      rangeYearPlaceholder: ['Початковий рік', 'Рік закінчення'],\n      rangeMonthPlaceholder: ['Початковий місяць', 'Кінцевий місяць'],\n      rangeWeekPlaceholder: ['Початковий тиждень', 'Кінцевий тиждень'],\n      locale: 'uk_UA',\n      today: 'Сьогодні',\n      now: 'Зараз',\n      backToToday: 'Поточна дата',\n      ok: 'Ok',\n      clear: 'Очистити',\n      month: 'Місяць',\n      year: 'Рік',\n      timeSelect: 'Обрати час',\n      dateSelect: 'Обрати дату',\n      monthSelect: 'Обрати місяць',\n      yearSelect: 'Обрати рік',\n      decadeSelect: 'Обрати десятиріччя',\n      yearFormat: 'YYYY',\n      dateFormat: 'D-M-YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D-M-YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Попередній місяць (PageUp)',\n      nextMonth: 'Наступний місяць (PageDown)',\n      previousYear: 'Попередній рік (Control + left)',\n      nextYear: 'Наступний рік (Control + right)',\n      previousDecade: 'Попереднє десятиріччя',\n      nextDecade: 'Наступне десятиріччя',\n      previousCentury: 'Попереднє століття',\n      nextCentury: 'Наступне століття'\n    },\n    timePickerLocale: {\n      placeholder: 'Оберіть час',\n      rangePlaceholder: ['Час початку', 'Час закінчення']\n    }\n  },\n  TimePicker: {\n    placeholder: 'Оберіть час',\n    rangePlaceholder: ['Час початку', 'Час закінчення']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Оберіть дату',\n      rangePlaceholder: ['Початкова дата', 'Кінцева дата'],\n      locale: 'uk_UA',\n      today: 'Сьогодні',\n      now: 'Зараз',\n      backToToday: 'Поточна дата',\n      ok: 'Ok',\n      clear: 'Очистити',\n      month: 'Місяць',\n      year: 'Рік',\n      timeSelect: 'Обрати час',\n      dateSelect: 'Обрати дату',\n      monthSelect: 'Обрати місяць',\n      yearSelect: 'Обрати рік',\n      decadeSelect: 'Обрати десятиріччя',\n      yearFormat: 'YYYY',\n      dateFormat: 'D-M-YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D-M-YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Попередній місяць (PageUp)',\n      nextMonth: 'Наступний місяць (PageDown)',\n      previousYear: 'Попередній рік (Control + left)',\n      nextYear: 'Наступний рік (Control + right)',\n      previousDecade: 'Попереднє десятиріччя',\n      nextDecade: 'Наступне десятиріччя',\n      previousCentury: 'Попереднє століття',\n      nextCentury: 'Наступне століття'\n    },\n    timePickerLocale: {\n      placeholder: 'Оберіть час'\n    }\n  },\n  Table: {\n    filterTitle: 'Фільтрувати',\n    filterConfirm: 'OK',\n    filterReset: 'Скинути',\n    selectAll: 'Обрати всі',\n    selectInvert: 'Інвертувати вибір'\n  },\n  Modal: {\n    okText: 'Гаразд',\n    cancelText: 'Скасувати',\n    justOkText: 'Гаразд'\n  },\n  Popconfirm: {\n    okText: 'Гаразд',\n    cancelText: 'Скасувати'\n  },\n  Transfer: {\n    searchPlaceholder: 'Введіть текст для пошуку',\n    itemUnit: 'елем.',\n    itemsUnit: 'елем.'\n  },\n  Upload: {\n    uploading: 'Завантаження ...',\n    removeFile: 'Видалити файл',\n    uploadError: 'Помилка завантаження',\n    previewFile: 'Попередній перегляд файлу',\n    downloadFile: 'Завантажити файл'\n  },\n  Empty: {\n    description: 'Даних немає'\n  },\n  Icon: {\n    icon: 'іконка'\n  },\n  Text: {\n    edit: 'Редагувати',\n    copy: 'Копіювати',\n    copied: 'Скопійовано',\n    expand: 'Розгорнути'\n  },\n  PageHeader: {\n    back: 'Назад'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar ur_PK = {\n  locale: 'ur',\n  Pagination: {\n    items_per_page: '/ صفحہ',\n    jump_to: 'پاس جاؤ',\n    jump_to_confirm: 'تصدیق کریں',\n    page: '',\n    prev_page: 'پچھلا صفحہ',\n    next_page: 'اگلا صفحہ',\n    prev_5: 'پچھلے 5 صفحات',\n    next_5: 'اگلے 5 صفحات',\n    prev_3: 'پچھلے 3 صفحات',\n    next_3: 'اگلے 3 صفحات',\n    page_size: 'Page Size'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'تاریخ منتخب کریں',\n      yearPlaceholder: 'سال کو منتخب کریں',\n      quarterPlaceholder: 'کوارٹر منتخب کریں',\n      monthPlaceholder: 'ماہ منتخب کریں',\n      weekPlaceholder: 'ہفتہ منتخب کریں',\n      rangePlaceholder: ['شروع کرنے کی تاریخ', 'آخری تاریخ'],\n      rangeYearPlaceholder: ['آغاز سال', 'آخر سال'],\n      rangeMonthPlaceholder: ['مہینہ شروع', 'اختتامی مہینہ'],\n      rangeWeekPlaceholder: ['ہفتے شروع کریں', 'اختتام ہفتہ'],\n      locale: 'ur_PK',\n      today: 'آج',\n      now: 'ابھی',\n      backToToday: 'آج واپس',\n      ok: 'ٹھیک ہے',\n      clear: 'صاف',\n      month: 'مہینہ',\n      year: 'سال',\n      timeSelect: 'وقت منتخب کریں',\n      dateSelect: 'تاریخ منتخب کریں',\n      weekSelect: 'ایک ہفتہ کا انتخاب کریں',\n      monthSelect: 'ایک مہینہ کا انتخاب کریں',\n      yearSelect: 'ایک سال کا انتخاب کریں',\n      decadeSelect: 'ایک دہائی کا انتخاب کریں',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'پچھلے مہینے (PageUp)',\n      nextMonth: 'اگلے مہینے (PageDown)',\n      previousYear: 'گزشتہ سال (Control + left)',\n      nextYear: 'اگلے سال (Control + right)',\n      previousDecade: 'پچھلی دہائی',\n      nextDecade: 'اگلی دہائی',\n      previousCentury: 'پچھلی صدی',\n      nextCentury: 'اگلی صدی'\n    },\n    timePickerLocale: {\n      placeholder: 'وقت منتخب کریں',\n      rangePlaceholder: ['وقت منتخب کریں', 'آخر وقت']\n    }\n  },\n  TimePicker: {\n    placeholder: 'وقت منتخب کریں',\n    rangePlaceholder: ['وقت منتخب کریں', 'آخر وقت']\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'تاریخ منتخب کریں',\n      yearPlaceholder: 'سال کو منتخب کریں',\n      quarterPlaceholder: 'کوارٹر منتخب کریں',\n      monthPlaceholder: 'ماہ منتخب کریں',\n      weekPlaceholder: 'ہفتہ منتخب کریں',\n      rangePlaceholder: ['شروع کرنے کی تاریخ', 'آخری تاریخ'],\n      rangeYearPlaceholder: ['آغاز سال', 'آخر سال'],\n      rangeMonthPlaceholder: ['مہینہ شروع', 'اختتامی مہینہ'],\n      rangeWeekPlaceholder: ['ہفتے شروع کریں', 'اختتام ہفتہ'],\n      locale: 'ur_PK',\n      today: 'آج',\n      now: 'ابھی',\n      backToToday: 'آج واپس',\n      ok: 'ٹھیک ہے',\n      clear: 'صاف',\n      month: 'مہینہ',\n      year: 'سال',\n      timeSelect: 'وقت منتخب کریں',\n      dateSelect: 'تاریخ منتخب کریں',\n      weekSelect: 'ایک ہفتہ کا انتخاب کریں',\n      monthSelect: 'ایک مہینہ کا انتخاب کریں',\n      yearSelect: 'ایک سال کا انتخاب کریں',\n      decadeSelect: 'ایک دہائی کا انتخاب کریں',\n      yearFormat: 'YYYY',\n      dateFormat: 'M/D/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'پچھلے مہینے (PageUp)',\n      nextMonth: 'اگلے مہینے (PageDown)',\n      previousYear: 'گزشتہ سال (Control + left)',\n      nextYear: 'اگلے سال (Control + right)',\n      previousDecade: 'پچھلی دہائی',\n      nextDecade: 'اگلی دہائی',\n      previousCentury: 'پچھلی صدی',\n      nextCentury: 'اگلی صدی'\n    },\n    timePickerLocale: {\n      placeholder: 'وقت منتخب کریں',\n      rangePlaceholder: ['وقت منتخب کریں', 'آخر وقت']\n    }\n  },\n  global: {\n    placeholder: 'منتخب کریں'\n  },\n  Table: {\n    filterTitle: 'فلٹر مینو',\n    filterConfirm: 'ٹھیک ہے',\n    filterReset: 'ری سیٹ کریں',\n    filterEmptyText: 'فلٹرز نہیں',\n    emptyText: 'کوئی ڈیٹا نہیں',\n    selectAll: 'موجودہ صفحہ منتخب کریں',\n    selectInvert: 'موجودہ صفحے کو الٹ دیں',\n    selectNone: 'تمام ڈیٹا صاف کریں',\n    selectionAll: 'تمام ڈیٹا کو منتخب کریں',\n    sortTitle: 'ترتیب دیں',\n    expand: 'پھیلائیں',\n    collapse: 'سمیٹیں',\n    triggerDesc: 'نزولی کو ترتیب دینے کیلئے کلک کریں',\n    triggerAsc: 'چڑھنے کو ترتیب دینے کیلئے کلک کریں',\n    cancelSort: 'ترتیب کو منسوخ کرنے کیلئے دبائیں'\n  },\n  Modal: {\n    okText: 'ٹھیک ہے',\n    cancelText: 'منسوخ کریں',\n    justOkText: 'ٹھیک ہے'\n  },\n  Popconfirm: {\n    okText: 'ٹھیک ہے',\n    cancelText: 'منسوخ کریں'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'یہاں تلاش کریں',\n    itemUnit: 'شے',\n    itemsUnit: 'اشیاء',\n    remove: 'ہٹائیں',\n    selectCurrent: 'موجودہ صفحہ منتخب کریں',\n    removeCurrent: 'موجودہ صفحہ ہٹائیں',\n    selectAll: 'تمام ڈیٹا کو منتخب کریں',\n    removeAll: 'تمام ڈیٹا کو ہٹا دیں',\n    selectInvert: 'موجودہ صفحے کو الٹ دیں'\n  },\n  Upload: {\n    uploading: 'اپ لوڈ ہو رہا ہے…',\n    removeFile: 'فائل کو ہٹا دیں',\n    uploadError: 'اپ لوڈ کی خرابی',\n    previewFile: 'پیش نظار فائل',\n    downloadFile: 'فائل ڈاؤن لوڈ کریں'\n  },\n  Empty: {\n    description: 'کوئی ڈیٹا نہیں'\n  },\n  Icon: {\n    icon: 'آئیکن'\n  },\n  Text: {\n    edit: 'ترمیم',\n    copy: 'کاپی',\n    copied: 'کاپی ہوگیا',\n    expand: 'پھیلائیں'\n  },\n  PageHeader: {\n    back: 'پیچھے'\n  },\n  Image: {\n    preview: 'پیش نظارہ'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar vi_VN = {\n  locale: 'vi',\n  Pagination: {\n    items_per_page: '/ trang',\n    jump_to: 'Đến',\n    jump_to_confirm: 'xác nhận',\n    page: 'Trang',\n    prev_page: 'Trang Trước',\n    next_page: 'Trang Kế',\n    prev_5: 'Về 5 Trang Trước',\n    next_5: 'Đến 5 Trang Kế',\n    prev_3: 'Về 3 Trang Trước',\n    next_3: 'Đến 3 Trang Kế',\n    page_size: 'kích thước trang'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: 'Chọn thời điểm',\n      yearPlaceholder: 'Chọn năm',\n      quarterPlaceholder: 'Chọn quý',\n      monthPlaceholder: 'Chọn tháng',\n      weekPlaceholder: 'Chọn tuần',\n      rangePlaceholder: ['Ngày bắt đầu', 'Ngày kết thúc'],\n      rangeYearPlaceholder: ['Năm bắt đầu', 'Năm kết thúc'],\n      rangeMonthPlaceholder: ['Tháng bắt đầu', 'Tháng kết thúc'],\n      rangeWeekPlaceholder: ['Tuần bắt đầu', 'Tuần kết thúc'],\n      locale: 'vi_VN',\n      today: 'Hôm nay',\n      now: 'Bây giờ',\n      backToToday: 'Trở về hôm nay',\n      ok: 'Ok',\n      clear: 'Xóa',\n      month: 'Tháng',\n      year: 'Năm',\n      timeSelect: 'Chọn thời gian',\n      dateSelect: 'Chọn ngày',\n      weekSelect: 'Chọn tuần',\n      monthSelect: 'Chọn tháng',\n      yearSelect: 'Chọn năm',\n      decadeSelect: 'Chọn thập kỷ',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Tháng trước (PageUp)',\n      nextMonth: 'Tháng sau (PageDown)',\n      previousYear: 'Năm trước (Control + left)',\n      nextYear: 'Năm sau (Control + right)',\n      previousDecade: 'Thập kỷ trước',\n      nextDecade: 'Thập kỷ sau',\n      previousCentury: 'Thế kỷ trước',\n      nextCentury: 'Thế kỷ sau'\n    },\n    timePickerLocale: {\n      placeholder: 'Chọn thời gian'\n    }\n  },\n  TimePicker: {\n    placeholder: 'Chọn thời gian'\n  },\n  Calendar: {\n    lang: {\n      placeholder: 'Chọn thời điểm',\n      yearPlaceholder: 'Chọn năm',\n      quarterPlaceholder: 'Chọn quý',\n      monthPlaceholder: 'Chọn tháng',\n      weekPlaceholder: 'Chọn tuần',\n      rangePlaceholder: ['Ngày bắt đầu', 'Ngày kết thúc'],\n      rangeYearPlaceholder: ['Năm bắt đầu', 'Năm kết thúc'],\n      rangeMonthPlaceholder: ['Tháng bắt đầu', 'Tháng kết thúc'],\n      rangeWeekPlaceholder: ['Tuần bắt đầu', 'Tuần kết thúc'],\n      locale: 'vi_VN',\n      today: 'Hôm nay',\n      now: 'Bây giờ',\n      backToToday: 'Trở về hôm nay',\n      ok: 'Ok',\n      clear: 'Xóa',\n      month: 'Tháng',\n      year: 'Năm',\n      timeSelect: 'Chọn thời gian',\n      dateSelect: 'Chọn ngày',\n      weekSelect: 'Chọn tuần',\n      monthSelect: 'Chọn tháng',\n      yearSelect: 'Chọn năm',\n      decadeSelect: 'Chọn thập kỷ',\n      yearFormat: 'YYYY',\n      dateFormat: 'D/M/YYYY',\n      dayFormat: 'D',\n      dateTimeFormat: 'D/M/YYYY HH:mm:ss',\n      monthBeforeYear: true,\n      previousMonth: 'Tháng trước (PageUp)',\n      nextMonth: 'Tháng sau (PageDown)',\n      previousYear: 'Năm trước (Control + left)',\n      nextYear: 'Năm sau (Control + right)',\n      previousDecade: 'Thập kỷ trước',\n      nextDecade: 'Thập kỷ sau',\n      previousCentury: 'Thế kỷ trước',\n      nextCentury: 'Thế kỷ sau'\n    },\n    timePickerLocale: {\n      placeholder: 'Chọn thời gian'\n    }\n  },\n  Table: {\n    filterTitle: 'Bộ ',\n    filterConfirm: 'OK',\n    filterReset: 'Tạo Lại',\n    selectAll: 'Chọn Tất Cả',\n    selectInvert: 'Chọn Ngược Lại'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Huỷ',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Huỷ'\n  },\n  Transfer: {\n    searchPlaceholder: 'Tìm ở đây',\n    itemUnit: 'mục',\n    itemsUnit: 'mục'\n  },\n  Upload: {\n    uploading: 'Đang tải lên...',\n    removeFile: 'Gỡ bỏ tập tin',\n    uploadError: 'Lỗi tải lên',\n    previewFile: 'Xem thử tập tin',\n    downloadFile: 'Tải tập tin'\n  },\n  Empty: {\n    description: 'Trống'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar zh_HK = {\n  locale: 'zh-hk',\n  Pagination: {\n    items_per_page: '條/頁',\n    jump_to: '跳至',\n    jump_to_confirm: '確定',\n    page: '頁',\n    prev_page: '上一頁',\n    next_page: '下一頁',\n    prev_5: '向前 5 頁',\n    next_5: '向後 5 頁',\n    prev_3: '向前 3 頁',\n    next_3: '向後 3 頁',\n    page_size: '頁碼'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: '請選擇日期',\n      rangePlaceholder: ['開始日期', '結束日期'],\n      locale: 'zh_TW',\n      today: '今天',\n      now: '此刻',\n      backToToday: '返回今天',\n      ok: '確定',\n      timeSelect: '選擇時間',\n      dateSelect: '選擇日期',\n      weekSelect: '選擇周',\n      clear: '清除',\n      month: '月',\n      year: '年',\n      previousMonth: '上個月 (翻頁上鍵)',\n      nextMonth: '下個月 (翻頁下鍵)',\n      monthSelect: '選擇月份',\n      yearSelect: '選擇年份',\n      decadeSelect: '選擇年代',\n      yearFormat: 'YYYY年',\n      dayFormat: 'D日',\n      dateFormat: 'YYYY年M月D日',\n      dateTimeFormat: 'YYYY年M月D日 HH時mm分ss秒',\n      previousYear: '上一年 (Control鍵加左方向鍵)',\n      nextYear: '下一年 (Control鍵加右方向鍵)',\n      previousDecade: '上一年代',\n      nextDecade: '下一年代',\n      previousCentury: '上一世紀',\n      nextCentury: '下一世紀',\n      yearPlaceholder: '請選擇年份',\n      quarterPlaceholder: '請選擇季度',\n      monthPlaceholder: '請選擇月份',\n      weekPlaceholder: '請選擇周',\n      rangeYearPlaceholder: ['開始年份', '結束年份'],\n      rangeMonthPlaceholder: ['開始月份', '結束月份'],\n      rangeWeekPlaceholder: ['開始周', '結束周']\n    },\n    timePickerLocale: {\n      placeholder: '請選擇時間'\n    }\n  },\n  TimePicker: {\n    placeholder: '請選擇時間'\n  },\n  Calendar: {\n    lang: {\n      placeholder: '請選擇日期',\n      rangePlaceholder: ['開始日期', '結束日期'],\n      locale: 'zh_TW',\n      today: '今天',\n      now: '此刻',\n      backToToday: '返回今天',\n      ok: '確定',\n      timeSelect: '選擇時間',\n      dateSelect: '選擇日期',\n      weekSelect: '選擇周',\n      clear: '清除',\n      month: '月',\n      year: '年',\n      previousMonth: '上個月 (翻頁上鍵)',\n      nextMonth: '下個月 (翻頁下鍵)',\n      monthSelect: '選擇月份',\n      yearSelect: '選擇年份',\n      decadeSelect: '選擇年代',\n      yearFormat: 'YYYY年',\n      dayFormat: 'D日',\n      dateFormat: 'YYYY年M月D日',\n      dateTimeFormat: 'YYYY年M月D日 HH時mm分ss秒',\n      previousYear: '上一年 (Control鍵加左方向鍵)',\n      nextYear: '下一年 (Control鍵加右方向鍵)',\n      previousDecade: '上一年代',\n      nextDecade: '下一年代',\n      previousCentury: '上一世紀',\n      nextCentury: '下一世紀',\n      yearPlaceholder: '請選擇年份',\n      quarterPlaceholder: '請選擇季度',\n      monthPlaceholder: '請選擇月份',\n      weekPlaceholder: '請選擇周',\n      rangeYearPlaceholder: ['開始年份', '結束年份'],\n      rangeMonthPlaceholder: ['開始月份', '結束月份'],\n      rangeWeekPlaceholder: ['開始周', '結束周']\n    },\n    timePickerLocale: {\n      placeholder: '請選擇時間'\n    }\n  },\n  global: {\n    placeholder: '請選擇'\n  },\n  Table: {\n    filterTitle: '篩選器',\n    filterConfirm: '確定',\n    filterReset: '重置',\n    filterEmptyText: '無篩選項',\n    selectAll: '全部選取',\n    selectInvert: '反向選取',\n    selectionAll: '全選所有',\n    sortTitle: '排序',\n    expand: '展開行',\n    collapse: '關閉行',\n    triggerDesc: '點擊降序',\n    triggerAsc: '點擊升序',\n    cancelSort: '取消排序',\n    selectNone: '清空所有'\n  },\n  Modal: {\n    okText: '確定',\n    cancelText: '取消',\n    justOkText: '知道了'\n  },\n  Popconfirm: {\n    okText: '確定',\n    cancelText: '取消'\n  },\n  Transfer: {\n    searchPlaceholder: '搜尋資料',\n    itemUnit: '項目',\n    itemsUnit: '項目',\n    remove: '刪除',\n    selectCurrent: '全選當頁',\n    removeCurrent: '刪除當頁',\n    selectAll: '全選所有',\n    removeAll: '刪除全部',\n    selectInvert: '反選當頁'\n  },\n  Upload: {\n    uploading: '正在上傳...',\n    removeFile: '刪除檔案',\n    uploadError: '上傳失敗',\n    previewFile: '檔案預覽',\n    downloadFile: '下载文件'\n  },\n  Empty: {\n    description: '無此資料'\n  },\n  Icon: {\n    icon: '圖標'\n  },\n  Text: {\n    edit: '編輯',\n    copy: '複製',\n    copied: '複製成功',\n    expand: '展開'\n  },\n  PageHeader: {\n    back: '返回'\n  },\n  Image: {\n    preview: '預覽'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nvar zh_TW = {\n  locale: 'zh-tw',\n  Pagination: {\n    items_per_page: '條/頁',\n    jump_to: '跳至',\n    jump_to_confirm: '確定',\n    page: '頁',\n    prev_page: '上一頁',\n    next_page: '下一頁',\n    prev_5: '向前 5 頁',\n    next_5: '向後 5 頁',\n    prev_3: '向前 3 頁',\n    next_3: '向後 3 頁',\n    page_size: '頁碼'\n  },\n  DatePicker: {\n    lang: {\n      placeholder: '請選擇日期',\n      rangePlaceholder: ['開始日期', '結束日期'],\n      locale: 'zh_TW',\n      today: '今天',\n      now: '此刻',\n      backToToday: '返回今天',\n      ok: '確定',\n      timeSelect: '選擇時間',\n      dateSelect: '選擇日期',\n      weekSelect: '選擇周',\n      clear: '清除',\n      month: '月',\n      year: '年',\n      previousMonth: '上個月 (翻頁上鍵)',\n      nextMonth: '下個月 (翻頁下鍵)',\n      monthSelect: '選擇月份',\n      yearSelect: '選擇年份',\n      decadeSelect: '選擇年代',\n      yearFormat: 'YYYY年',\n      dayFormat: 'D日',\n      dateFormat: 'YYYY年M月D日',\n      dateTimeFormat: 'YYYY年M月D日 HH時mm分ss秒',\n      previousYear: '上一年 (Control鍵加左方向鍵)',\n      nextYear: '下一年 (Control鍵加右方向鍵)',\n      previousDecade: '上一年代',\n      nextDecade: '下一年代',\n      previousCentury: '上一世紀',\n      nextCentury: '下一世紀',\n      yearPlaceholder: '請選擇年份',\n      quarterPlaceholder: '請選擇季度',\n      monthPlaceholder: '請選擇月份',\n      weekPlaceholder: '請選擇周',\n      rangeYearPlaceholder: ['開始年份', '結束年份'],\n      rangeMonthPlaceholder: ['開始月份', '結束月份'],\n      rangeWeekPlaceholder: ['開始周', '結束周']\n    },\n    timePickerLocale: {\n      placeholder: '請選擇時間'\n    }\n  },\n  TimePicker: {\n    placeholder: '請選擇時間'\n  },\n  Calendar: {\n    lang: {\n      placeholder: '請選擇日期',\n      rangePlaceholder: ['開始日期', '結束日期'],\n      locale: 'zh_TW',\n      today: '今天',\n      now: '此刻',\n      backToToday: '返回今天',\n      ok: '確定',\n      timeSelect: '選擇時間',\n      dateSelect: '選擇日期',\n      weekSelect: '選擇周',\n      clear: '清除',\n      month: '月',\n      year: '年',\n      previousMonth: '上個月 (翻頁上鍵)',\n      nextMonth: '下個月 (翻頁下鍵)',\n      monthSelect: '選擇月份',\n      yearSelect: '選擇年份',\n      decadeSelect: '選擇年代',\n      yearFormat: 'YYYY年',\n      dayFormat: 'D日',\n      dateFormat: 'YYYY年M月D日',\n      dateTimeFormat: 'YYYY年M月D日 HH時mm分ss秒',\n      previousYear: '上一年 (Control鍵加左方向鍵)',\n      nextYear: '下一年 (Control鍵加右方向鍵)',\n      previousDecade: '上一年代',\n      nextDecade: '下一年代',\n      previousCentury: '上一世紀',\n      nextCentury: '下一世紀',\n      yearPlaceholder: '請選擇年份',\n      quarterPlaceholder: '請選擇季度',\n      monthPlaceholder: '請選擇月份',\n      weekPlaceholder: '請選擇周',\n      rangeYearPlaceholder: ['開始年份', '結束年份'],\n      rangeMonthPlaceholder: ['開始月份', '結束月份'],\n      rangeWeekPlaceholder: ['開始周', '結束周']\n    },\n    timePickerLocale: {\n      placeholder: '請選擇時間'\n    }\n  },\n  global: {\n    placeholder: '請選擇'\n  },\n  Table: {\n    filterTitle: '篩選器',\n    filterConfirm: '確定',\n    filterReset: '重置',\n    filterEmptyText: '無篩選項',\n    selectAll: '全部選取',\n    selectInvert: '反向選取',\n    selectionAll: '全選所有',\n    sortTitle: '排序',\n    expand: '展開行',\n    collapse: '關閉行',\n    triggerDesc: '點擊降序',\n    triggerAsc: '點擊升序',\n    cancelSort: '取消排序',\n    selectNone: '清空所有'\n  },\n  Modal: {\n    okText: '確定',\n    cancelText: '取消',\n    justOkText: '知道了'\n  },\n  Popconfirm: {\n    okText: '確定',\n    cancelText: '取消'\n  },\n  Transfer: {\n    searchPlaceholder: '搜尋資料',\n    itemUnit: '項目',\n    itemsUnit: '項目',\n    remove: '删除',\n    selectCurrent: '全選當頁',\n    removeCurrent: '删除當頁',\n    selectAll: '全選所有',\n    removeAll: '删除全部',\n    selectInvert: '反選當頁'\n  },\n  Upload: {\n    uploading: '正在上傳...',\n    removeFile: '刪除檔案',\n    uploadError: '上傳失敗',\n    previewFile: '檔案預覽',\n    downloadFile: '下載文件'\n  },\n  Empty: {\n    description: '無此資料'\n  },\n  Icon: {\n    icon: '圖標'\n  },\n  Text: {\n    edit: '編輯',\n    copy: '複製',\n    copied: '複製成功',\n    expand: '展開'\n  },\n  PageHeader: {\n    back: '返回'\n  },\n  Image: {\n    preview: '預覽'\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DATE_HELPER_SERVICE_FACTORY, DateHelperByDateFns, DateHelperByDatePipe, DateHelperService, NZ_DATE_CONFIG, NZ_DATE_CONFIG_DEFAULT, NZ_DATE_LOCALE, NZ_I18N, NzI18nModule, NzI18nPipe, NzI18nService, ar_EG, az_AZ, bg_BG, bn_BD, by_BY, ca_ES, cs_CZ, da_DK, de_DE, el_GR, en_GB, en_US, es_ES, et_EE, fa_IR, fi_FI, fr_BE, fr_CA, fr_FR, ga_IE, gl_ES, he_IL, hi_IN, hr_HR, hu_HU, hy_AM, id_ID, is_IS, it_IT, ja_JP, ka_GE, kk_KZ, km_KH, kmr_IQ, kn_IN, ko_KR, ku_IQ, lt_LT, lv_LV, mergeDateConfig, mk_MK, ml_IN, mn_MN, ms_MY, nb_NO, ne_NP, nl_BE, nl_NL, pl_PL, provideNzI18n, pt_BR, pt_PT, ro_RO, ru_RU, sk_SK, sl_SI, sr_RS, sv_SE, ta_IN, th_TH, tr_TR, uk_UA, ur_PK, vi_VN, zh_CN, zh_HK, zh_TW };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,cAAc,UAAU;AAAA,MAC3C,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,uBAAuB,CAAC,eAAe,WAAW;AAAA,MAClD,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,UAAU;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,cAAc,UAAU;AAAA,EAC7C;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,cAAc,UAAU;AAAA,MAC3C,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,uBAAuB,CAAC,eAAe,WAAW;AAAA,MAClD,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,UAAU;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,gBAAgB;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,QAAQ,MAAM;AAAA,MACjC,sBAAsB,CAAC,QAAQ,MAAM;AAAA,MACrC,uBAAuB,CAAC,QAAQ,MAAM;AAAA,MACtC,sBAAsB,CAAC,OAAO,KAAK;AAAA,MACnC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,QAAQ,MAAM;AAAA,IACnC;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,QAAQ,MAAM;AAAA,EACnC;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,QAAQ,MAAM;AAAA,MACjC,sBAAsB,CAAC,QAAQ,MAAM;AAAA,MACrC,uBAAuB,CAAC,QAAQ,MAAM;AAAA,MACtC,sBAAsB,CAAC,OAAO,KAAK;AAAA,MACnC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,QAAQ,MAAM;AAAA,IACnC;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,gBAAgB;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AACF;AAMA,IAAM,UAAU,IAAI,eAAe,SAAS;AAC5C,SAAS,cAAc,QAAQ;AAC7B,SAAO,yBAAyB,CAAC;AAAA,IAC/B,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ;AAEA,IAAM,iBAAiB,IAAI,eAAe,gBAAgB;AAM1D,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,IAAI,eAAe;AACjB,WAAO,KAAK,QAAQ,aAAa;AAAA,EACnC;AAAA,EACA,YAAY,QAAQ,YAAY;AAC9B,SAAK,UAAU,IAAI,gBAAgB,KAAK,OAAO;AAC/C,SAAK,UAAU,UAAU,KAAK;AAC9B,SAAK,cAAc,cAAc,IAAI;AAAA,EACvC;AAAA;AAAA;AAAA,EAGA,UAAU,MAAM,MAAM;AAEpB,QAAI,UAAU,KAAK,eAAe,KAAK,SAAS,IAAI;AACpD,QAAI,OAAO,YAAY,UAAU;AAC/B,UAAI,MAAM;AACR,eAAO,KAAK,IAAI,EAAE,QAAQ,SAAO,UAAU,QAAQ,QAAQ,IAAI,OAAO,IAAI,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC;AAAA,MACpG;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,QAAI,KAAK,WAAW,KAAK,QAAQ,WAAW,OAAO,QAAQ;AACzD;AAAA,IACF;AACA,SAAK,UAAU;AACf,SAAK,QAAQ,KAAK,MAAM;AAAA,EAC1B;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,EAC9C;AAAA,EACA,cAAc,YAAY;AACxB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,MAAM,cAAc;AAChC,UAAM,SAAS,OAAO,KAAK,eAAe,KAAK,SAAS,IAAI,IAAI,KAAK;AACrE,QAAI,CAAC,UAAU,CAAC,cAAc;AAC5B,WAAK,6BAA6B,IAAI,kBAAkB,KAAK,QAAQ,MAAM;AAAA;AAAA;AAAA,sEAGX;AAAA,IAClE;AACA,WAAO,UAAU,gBAAgB,KAAK,eAAe,OAAO,IAAI,KAAK,CAAC;AAAA,EACxE;AAAA,EACA,eAAe,KAAK,MAAM;AACxB,QAAI,MAAM;AACV,UAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,UAAM,QAAQ,MAAM;AACpB,QAAI,QAAQ;AACZ,WAAO,OAAO,QAAQ,OAAO;AAC3B,YAAM,IAAI,MAAM,OAAO,CAAC;AAAA,IAC1B;AACA,WAAO,UAAU,QAAQ,MAAM;AAAA,EACjC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAkB,SAAS,SAAS,CAAC,GAAM,SAAS,gBAAgB,CAAC,CAAC;AAAA,IACzF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,UAAU,MAAM,UAAU;AACxB,WAAO,KAAK,QAAQ,UAAU,MAAM,QAAQ;AAAA,EAC9C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,GAAG;AACzC,aAAO,KAAK,KAAK,aAAe,kBAAkB,eAAe,EAAE,CAAC;AAAA,IACtE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAc;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,UAAU;AAAA,MACpB,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU;AAAA,MACpB,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,iBAAiB,IAAI,eAAe,aAAa;AACvD,IAAM,yBAAyB;AAAA,EAC7B,gBAAgB;AAClB;AACA,SAAS,gBAAgB,QAAQ;AAC/B,SAAO,kCACF,yBACA;AAEP;AAWA,SAAS,8BAA8B;AACrC,QAAM,OAAO,OAAO,aAAa;AACjC,QAAM,SAAS,OAAO,gBAAgB;AAAA,IACpC,UAAU;AAAA,EACZ,CAAC;AACD,SAAO,KAAK,cAAc,IAAI,IAAI,oBAAoB,MAAM,MAAM,IAAI,IAAI,qBAAqB,MAAM,MAAM;AAC7G;AAKA,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,MAAM,QAAQ;AACxB,SAAK,OAAO;AACZ,SAAK,SAAS,gBAAgB,MAAM;AAAA,EACtC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,SAAS,aAAa,GAAM,SAAS,gBAAgB,CAAC,CAAC;AAAA,IAChG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,MAAM,4BAA4B;AAAA,MAC3C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAIH,IAAM,sBAAN,cAAkC,kBAAkB;AAAA,EAClD,WAAW,MAAM;AACf,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA;AAAA;AAAA,EAGA,oBAAoB;AAClB,QAAI;AACJ,QAAI;AACF,4BAAsB,KAAK,KAAK,cAAc,EAAE,QAAQ;AAAA,IAC1D,SAAS,GAAG;AACV,4BAAsB;AAAA,IACxB;AACA,WAAO,KAAK,OAAO,kBAAkB,OAAO,sBAAsB,KAAK,OAAO;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,MAAM,WAAW;AACtB,WAAO,OAAO,OAAO,MAAM,WAAW;AAAA,MACpC,QAAQ,KAAK,KAAK,cAAc;AAAA,IAClC,CAAC,IAAI;AAAA,EACP;AAAA,EACA,UAAU,MAAM,WAAW;AACzB,WAAO,MAAM,MAAM,WAAW,oBAAI,KAAK,GAAG;AAAA,MACxC,QAAQ,KAAK,KAAK,cAAc;AAAA,MAChC,cAAc,KAAK,kBAAkB;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,UAAU,MAAM,WAAW;AACzB,WAAO,KAAK,UAAU,MAAM,SAAS;AAAA,EACvC;AACF;AAOA,IAAM,uBAAN,cAAmC,kBAAkB;AAAA,EACnD,WAAW,MAAM;AACf,WAAO,CAAC,KAAK,OAAO,MAAM,GAAG;AAAA,EAC/B;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,OAAO,mBAAmB,QAAW;AAC5C,YAAM,SAAS,KAAK,KAAK,YAAY;AACrC,aAAO,UAAU,CAAC,SAAS,OAAO,EAAE,QAAQ,OAAO,YAAY,CAAC,IAAI,KAAK,IAAI;AAAA,IAC/E;AACA,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,OAAO,MAAM,WAAW;AACtB,WAAO,OAAO,WAAW,MAAM,WAAW,KAAK,KAAK,YAAY,CAAC,IAAI;AAAA,EACvE;AAAA,EACA,UAAU,MAAM;AACd,WAAO,IAAI,KAAK,IAAI;AAAA,EACtB;AAAA,EACA,UAAU,MAAM,WAAW;AACzB,UAAM,SAAS,IAAI,aAAc,WAAW,KAAK,KAAK,YAAY,CAAC;AACnE,WAAO,OAAO,OAAO,IAAI;AAAA,EAC3B;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,WAAW,SAAS;AAAA,MACvC,sBAAsB,CAAC,eAAe,aAAa;AAAA,MACnD,uBAAuB,CAAC,eAAe,aAAa;AAAA,MACpD,sBAAsB,CAAC,iBAAiB,eAAe;AAAA,MACvD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,WAAW,SAAS;AAAA,MACvC,sBAAsB,CAAC,eAAe,aAAa;AAAA,MACnD,uBAAuB,CAAC,eAAe,aAAa;AAAA,MACpD,sBAAsB,CAAC,iBAAiB,eAAe;AAAA,MACvD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,YAAY;AAAA,EACd;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,MACnD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,MACnD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,WAAW,QAAQ;AAAA,MACtC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,WAAW,QAAQ;AAAA,MACtC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,eAAe,WAAW;AAAA,MAC7C,sBAAsB,CAAC,aAAa,SAAS;AAAA,MAC7C,uBAAuB,CAAC,aAAa,SAAS;AAAA,MAC9C,sBAAsB,CAAC,gBAAgB,YAAY;AAAA,MACnD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,aAAa,UAAU;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,aAAa,UAAU;AAAA,EAC5C;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,eAAe,WAAW;AAAA,MAC7C,sBAAsB,CAAC,aAAa,SAAS;AAAA,MAC7C,uBAAuB,CAAC,aAAa,SAAS;AAAA,MAC9C,sBAAsB,CAAC,gBAAgB,YAAY;AAAA,MACnD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,aAAa,UAAU;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,mBAAmB,kBAAkB;AAAA,MACxD,sBAAsB,CAAC,iBAAiB,gBAAgB;AAAA,MACxD,uBAAuB,CAAC,mBAAmB,kBAAkB;AAAA,MAC7D,sBAAsB,CAAC,qBAAqB,oBAAoB;AAAA,MAChE,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,gBAAgB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,eAAe,gBAAgB;AAAA,EACpD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,mBAAmB,kBAAkB;AAAA,MACxD,sBAAsB,CAAC,iBAAiB,gBAAgB;AAAA,MACxD,uBAAuB,CAAC,mBAAmB,kBAAkB;AAAA,MAC7D,sBAAsB,CAAC,qBAAqB,oBAAoB;AAAA,MAChE,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,gBAAgB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,eAAe;AAAA,IACf,cAAc;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,EACjB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,MAAM,IAAI;AAAA,MAC7B,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,MAAM,IAAI;AAAA,MAC7B,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,aAAa,UAAU;AAAA,MAC1C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,eAAe;AAAA,IACtD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,kBAAkB,eAAe;AAAA,EACtD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,aAAa,UAAU;AAAA,MAC1C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,eAAe;AAAA,IACtD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,cAAc,UAAU;AAAA,MAC3C,sBAAsB,CAAC,aAAa,SAAS;AAAA,MAC7C,uBAAuB,CAAC,cAAc,UAAU;AAAA,MAChD,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,aAAa,SAAS;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,cAAc,UAAU;AAAA,MAC3C,sBAAsB,CAAC,aAAa,SAAS;AAAA,MAC7C,uBAAuB,CAAC,cAAc,UAAU;AAAA,MAChD,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,aAAa,SAAS;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,qBAAqB,mBAAmB;AAAA,MAC3D,sBAAsB,CAAC,iBAAiB,eAAe;AAAA,MACvD,uBAAuB,CAAC,iBAAiB,eAAe;AAAA,MACxD,sBAAsB,CAAC,mBAAmB,iBAAiB;AAAA,MAC3D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,WAAW;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,eAAe,WAAW;AAAA,EAC/C;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,qBAAqB,mBAAmB;AAAA,MAC3D,sBAAsB,CAAC,iBAAiB,eAAe;AAAA,MACvD,uBAAuB,CAAC,iBAAiB,eAAe;AAAA,MACxD,sBAAsB,CAAC,mBAAmB,iBAAiB;AAAA,MAC3D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,WAAW;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,cAAc,UAAU;AAAA,MAC3C,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,uBAAuB,CAAC,eAAe,WAAW;AAAA,MAClD,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,UAAU;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,cAAc,UAAU;AAAA,EAC7C;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,cAAc,UAAU;AAAA,MAC3C,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,uBAAuB,CAAC,eAAe,WAAW;AAAA,MAClD,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,UAAU;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,aAAa;AAAA,MACjD,sBAAsB,CAAC,eAAe,WAAW;AAAA,MACjD,uBAAuB,CAAC,eAAe,WAAW;AAAA,MAClD,sBAAsB,CAAC,kBAAkB,cAAc;AAAA,MACvD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,IACjD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,EACjD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,aAAa;AAAA,MACjD,sBAAsB,CAAC,eAAe,WAAW;AAAA,MACjD,uBAAuB,CAAC,eAAe,WAAW;AAAA,MAClD,sBAAsB,CAAC,kBAAkB,cAAc;AAAA,MACvD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,IACjD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,cAAc;AAAA,MAClD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,cAAc;AAAA,MAClD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,cAAc,aAAa;AAAA,MAC9C,sBAAsB,CAAC,YAAY,WAAW;AAAA,MAC9C,uBAAuB,CAAC,YAAY,WAAW;AAAA,MAC/C,sBAAsB,CAAC,aAAa,YAAY;AAAA,MAChD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,aAAa,YAAY;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,aAAa,YAAY;AAAA,EAC9C;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,cAAc,aAAa;AAAA,MAC9C,sBAAsB,CAAC,YAAY,WAAW;AAAA,MAC9C,uBAAuB,CAAC,YAAY,WAAW;AAAA,MAC/C,sBAAsB,CAAC,aAAa,YAAY;AAAA,MAChD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,aAAa,YAAY;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,gBAAgB;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,gBAAgB;AAAA,MACnD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,gBAAgB;AAAA,MACnD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,aAAa;AAAA,MACjD,sBAAsB,CAAC,kBAAkB,cAAc;AAAA,MACvD,uBAAuB,CAAC,iBAAiB,aAAa;AAAA,MACtD,sBAAsB,CAAC,oBAAoB,gBAAgB;AAAA,MAC3D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,IACrD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,EACrD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,aAAa;AAAA,MACjD,sBAAsB,CAAC,kBAAkB,cAAc;AAAA,MACvD,uBAAuB,CAAC,iBAAiB,aAAa;AAAA,MACtD,sBAAsB,CAAC,oBAAoB,gBAAgB;AAAA,MAC3D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,IACrD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,gBAAgB;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,aAAa;AAAA,MACjD,sBAAsB,CAAC,kBAAkB,cAAc;AAAA,MACvD,uBAAuB,CAAC,iBAAiB,aAAa;AAAA,MACtD,sBAAsB,CAAC,oBAAoB,gBAAgB;AAAA,MAC3D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,IACrD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,EACrD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,aAAa;AAAA,MACjD,sBAAsB,CAAC,kBAAkB,cAAc;AAAA,MACvD,uBAAuB,CAAC,iBAAiB,aAAa;AAAA,MACtD,sBAAsB,CAAC,oBAAoB,gBAAgB;AAAA,MAC3D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,IACrD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,gBAAgB;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,aAAa;AAAA,MACjD,sBAAsB,CAAC,kBAAkB,cAAc;AAAA,MACvD,uBAAuB,CAAC,iBAAiB,aAAa;AAAA,MACtD,sBAAsB,CAAC,oBAAoB,gBAAgB;AAAA,MAC3D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,IACrD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,EACrD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,aAAa;AAAA,MACjD,sBAAsB,CAAC,kBAAkB,cAAc;AAAA,MACvD,uBAAuB,CAAC,iBAAiB,aAAa;AAAA,MACtD,sBAAsB,CAAC,oBAAoB,gBAAgB;AAAA,MAC3D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,IACrD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,gBAAgB;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,gBAAgB,cAAc;AAAA,MACjD,sBAAsB,CAAC,iBAAiB,oBAAoB;AAAA,MAC5D,uBAAuB,CAAC,eAAe,cAAc;AAAA,MACrD,sBAAsB,CAAC,yBAAyB,wBAAwB;AAAA,MACxE,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,YAAY;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,cAAc,YAAY;AAAA,EAC/C;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,gBAAgB,cAAc;AAAA,MACjD,sBAAsB,CAAC,iBAAiB,oBAAoB;AAAA,MAC5D,uBAAuB,CAAC,eAAe,cAAc;AAAA,MACrD,sBAAsB,CAAC,yBAAyB,wBAAwB;AAAA,MACxE,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,YAAY;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,YAAY;AAAA,MAC9C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,YAAY;AAAA,MAC9C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,cAAc;AAAA,MACjD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,sBAAsB,CAAC,eAAe,UAAU;AAAA,MAChD,uBAAuB,CAAC,gBAAgB,WAAW;AAAA,MACnD,sBAAsB,CAAC,iBAAiB,YAAY;AAAA,IACtD;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,SAAS;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,cAAc,SAAS;AAAA,EAC5C;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,cAAc;AAAA,MACjD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,sBAAsB,CAAC,eAAe,UAAU;AAAA,MAChD,uBAAuB,CAAC,gBAAgB,WAAW;AAAA,MACnD,sBAAsB,CAAC,iBAAiB,YAAY;AAAA,IACtD;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,SAAS;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,eAAe;AAAA,MACnD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,sBAAsB,CAAC,kBAAkB,gBAAgB;AAAA,MACzD,uBAAuB,CAAC,kBAAkB,gBAAgB;AAAA,MAC1D,sBAAsB,CAAC,kBAAkB,gBAAgB;AAAA,IAC3D;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,mBAAmB,mBAAmB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,mBAAmB,mBAAmB;AAAA,EAC3D;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,eAAe;AAAA,MACnD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,sBAAsB,CAAC,kBAAkB,gBAAgB;AAAA,MACzD,uBAAuB,CAAC,kBAAkB,gBAAgB;AAAA,MAC1D,sBAAsB,CAAC,kBAAkB,gBAAgB;AAAA,IAC3D;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,mBAAmB,mBAAmB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,eAAe,kBAAkB;AAAA,MACpD,sBAAsB,CAAC,YAAY,eAAe;AAAA,MAClD,uBAAuB,CAAC,eAAe,mBAAmB;AAAA,MAC1D,sBAAsB,CAAC,aAAa,gBAAgB;AAAA,MACpD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,aAAa,iBAAiB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,aAAa,iBAAiB;AAAA,EACnD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,eAAe,kBAAkB;AAAA,MACpD,sBAAsB,CAAC,YAAY,eAAe;AAAA,MAClD,uBAAuB,CAAC,eAAe,mBAAmB;AAAA,MAC1D,sBAAsB,CAAC,aAAa,gBAAgB;AAAA,MACpD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,aAAa,iBAAiB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,kBAAkB,CAAC,oBAAoB,iBAAiB;AAAA,MACxD,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,kBAAkB,CAAC,oBAAoB,iBAAiB;AAAA,MACxD,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,eAAe;AAAA,MACnD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,eAAe;AAAA,MACnD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,WAAW;AAAA,MAC9C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,WAAW;AAAA,MAC9C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,cAAc;AAAA,MAClD,sBAAsB,CAAC,iBAAiB,cAAc;AAAA,MACtD,uBAAuB,CAAC,kBAAkB,cAAc;AAAA,MACxD,sBAAsB,CAAC,sBAAsB,mBAAmB;AAAA,MAChE,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,aAAa;AAAA,IAClD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,gBAAgB,aAAa;AAAA,EAClD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,cAAc;AAAA,MAClD,sBAAsB,CAAC,iBAAiB,cAAc;AAAA,MACtD,uBAAuB,CAAC,kBAAkB,cAAc;AAAA,MACxD,sBAAsB,CAAC,sBAAsB,mBAAmB;AAAA,MAChE,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,aAAa;AAAA,IAClD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,QAAQ,MAAM;AAAA,MACjC,sBAAsB,CAAC,OAAO,KAAK;AAAA,MACnC,uBAAuB,CAAC,OAAO,KAAK;AAAA,MACpC,sBAAsB,CAAC,OAAO,KAAK;AAAA,MACnC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,QAAQ,MAAM;AAAA,IACnC;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,QAAQ,MAAM;AAAA,EACnC;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,QAAQ,MAAM;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,QAAQ,MAAM;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,kBAAkB,gBAAgB;AAAA,MACrD,sBAAsB,CAAC,gBAAgB,cAAc;AAAA,MACrD,uBAAuB,CAAC,eAAe,aAAa;AAAA,MACpD,sBAAsB,CAAC,iBAAiB,eAAe;AAAA,MACvD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,gBAAgB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,kBAAkB,gBAAgB;AAAA,EACvD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,kBAAkB,gBAAgB;AAAA,MACrD,sBAAsB,CAAC,gBAAgB,cAAc;AAAA,MACrD,uBAAuB,CAAC,eAAe,aAAa;AAAA,MACpD,sBAAsB,CAAC,iBAAiB,eAAe;AAAA,MACvD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,gBAAgB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,YAAY;AAAA,MAChD,sBAAsB,CAAC,kBAAkB,aAAa;AAAA,MACtD,uBAAuB,CAAC,eAAe,UAAU;AAAA,MACjD,sBAAsB,CAAC,oBAAoB,eAAe;AAAA,MAC1D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,IACnB;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,IACjD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,EACjD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,YAAY;AAAA,MAChD,sBAAsB,CAAC,kBAAkB,aAAa;AAAA,MACtD,uBAAuB,CAAC,eAAe,UAAU;AAAA,MACjD,sBAAsB,CAAC,oBAAoB,eAAe;AAAA,MAC1D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,IACnB;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,IACjD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,eAAe,cAAc;AAAA,MAChD,sBAAsB,CAAC,eAAe,cAAc;AAAA,MACpD,uBAAuB,CAAC,cAAc,aAAa;AAAA,MACnD,sBAAsB,CAAC,eAAe,cAAc;AAAA,MACpD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,gBAAgB;AAAA,IACtD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,iBAAiB,gBAAgB;AAAA,EACtD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,eAAe,cAAc;AAAA,MAChD,sBAAsB,CAAC,eAAe,cAAc;AAAA,MACpD,uBAAuB,CAAC,cAAc,aAAa;AAAA,MACnD,sBAAsB,CAAC,eAAe,cAAc;AAAA,MACpD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,gBAAgB;AAAA,IACtD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,eAAe;AAAA,IACf,cAAc;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,EACjB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,SAAS;AAAA,EACX,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,mBAAmB,cAAc;AAAA,MACpD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,mBAAmB,cAAc;AAAA,MACpD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,MACnD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,MACnD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,OAAO,KAAK;AAAA,MAC/B,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,SAAS,OAAO;AAAA,IACrC;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,SAAS,OAAO;AAAA,EACrC;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,OAAO,KAAK;AAAA,MAC/B,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,SAAS,OAAO;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,mBAAmB,cAAc;AAAA,MACpD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,mBAAmB,cAAc;AAAA,MACpD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,eAAe;AAAA,MACnD,sBAAsB,CAAC,kBAAkB,gBAAgB;AAAA,MACzD,uBAAuB,CAAC,oBAAoB,kBAAkB;AAAA,MAC9D,sBAAsB,CAAC,oBAAoB,kBAAkB;AAAA,MAC7D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,mBAAmB,iBAAiB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,mBAAmB,iBAAiB;AAAA,EACzD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,iBAAiB,eAAe;AAAA,MACnD,sBAAsB,CAAC,kBAAkB,gBAAgB;AAAA,MACzD,uBAAuB,CAAC,oBAAoB,kBAAkB;AAAA,MAC9D,sBAAsB,CAAC,oBAAoB,kBAAkB;AAAA,MAC7D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,mBAAmB,iBAAiB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,eAAe;AAAA,IACf,cAAc;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,EACjB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,cAAc;AAAA,MAClD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,cAAc;AAAA,MAClD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,YAAY,UAAU;AAAA,MACzC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,YAAY,UAAU;AAAA,MACzC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,aAAa,YAAY;AAAA,MAC5C,sBAAsB,CAAC,aAAa,YAAY;AAAA,MAChD,uBAAuB,CAAC,aAAa,YAAY;AAAA,MACjD,sBAAsB,CAAC,aAAa,YAAY;AAAA,MAChD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,aAAa,YAAY;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,aAAa,YAAY;AAAA,EAC9C;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,aAAa,YAAY;AAAA,MAC5C,sBAAsB,CAAC,aAAa,YAAY;AAAA,MAChD,uBAAuB,CAAC,aAAa,YAAY;AAAA,MACjD,sBAAsB,CAAC,aAAa,YAAY;AAAA,MAChD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,aAAa,YAAY;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,cAAc;AAAA,MAChD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,cAAc;AAAA,MAChD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,cAAc;AAAA,MAChD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,cAAc;AAAA,MAChD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,aAAa,WAAW;AAAA,MAC3C,sBAAsB,CAAC,WAAW,SAAS;AAAA,MAC3C,uBAAuB,CAAC,cAAc,YAAY;AAAA,MAClD,sBAAsB,CAAC,aAAa,UAAU;AAAA,MAC9C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,YAAY,SAAS;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,YAAY,SAAS;AAAA,EAC1C;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,aAAa,WAAW;AAAA,MAC3C,sBAAsB,CAAC,WAAW,SAAS;AAAA,MAC3C,uBAAuB,CAAC,cAAc,YAAY;AAAA,MAClD,sBAAsB,CAAC,aAAa,UAAU;AAAA,MAC9C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,YAAY,SAAS;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,cAAc,UAAU;AAAA,MAC3C,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,uBAAuB,CAAC,eAAe,WAAW;AAAA,MAClD,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,UAAU;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,cAAc,UAAU;AAAA,EAC7C;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,cAAc,UAAU;AAAA,MAC3C,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,uBAAuB,CAAC,eAAe,WAAW;AAAA,MAClD,sBAAsB,CAAC,cAAc,UAAU;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,UAAU;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,YAAY;AAAA,MAC9C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,uBAAuB,CAAC,eAAe,YAAY;AAAA,MACnD,sBAAsB,CAAC,cAAc,WAAW;AAAA,MAChD,sBAAsB,CAAC,cAAc,WAAW;AAAA,MAChD,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,IACnB;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,WAAW;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,cAAc,WAAW;AAAA,EAC9C;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,YAAY;AAAA,MAC9C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,uBAAuB,CAAC,eAAe,YAAY;AAAA,MACnD,sBAAsB,CAAC,cAAc,WAAW;AAAA,MAChD,sBAAsB,CAAC,cAAc,WAAW;AAAA,MAChD,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,IACnB;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,WAAW;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,eAAe;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,cAAc;AAAA,IACd,QAAQ,CAAC,IAAI,EAAE;AAAA,EACjB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,YAAY;AAAA,MAC9C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,uBAAuB,CAAC,eAAe,YAAY;AAAA,MACnD,sBAAsB,CAAC,cAAc,WAAW;AAAA,MAChD,sBAAsB,CAAC,cAAc,WAAW;AAAA,MAChD,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,IACnB;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,WAAW;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,cAAc,WAAW;AAAA,EAC9C;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,YAAY;AAAA,MAC9C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,uBAAuB,CAAC,eAAe,YAAY;AAAA,MACnD,sBAAsB,CAAC,cAAc,WAAW;AAAA,MAChD,sBAAsB,CAAC,cAAc,WAAW;AAAA,MAChD,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,IACnB;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,WAAW;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,eAAe;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,mBAAmB,cAAc;AAAA,MACpD,sBAAsB,CAAC,kBAAkB,aAAa;AAAA,MACtD,uBAAuB,CAAC,sBAAsB,iBAAiB;AAAA,MAC/D,sBAAsB,CAAC,sBAAsB,iBAAiB;AAAA,MAC9D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,mBAAmB,cAAc;AAAA,MACpD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,MAC/C,sBAAsB,CAAC,eAAe,WAAW;AAAA,MACjD,uBAAuB,CAAC,eAAe,WAAW;AAAA,MAClD,sBAAsB,CAAC,kBAAkB,cAAc;AAAA,MACvD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,eAAe,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,MAC/D,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,IAClG;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,IACjD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,EACjD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,MAC/C,sBAAsB,CAAC,eAAe,WAAW;AAAA,MACjD,uBAAuB,CAAC,eAAe,WAAW;AAAA,MAClD,sBAAsB,CAAC,kBAAkB,cAAc;AAAA,MACvD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,eAAe,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,MAC/D,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,IAClG;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,IACjD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,MAC/C,sBAAsB,CAAC,eAAe,WAAW;AAAA,MACjD,uBAAuB,CAAC,eAAe,WAAW;AAAA,MAClD,sBAAsB,CAAC,kBAAkB,cAAc;AAAA,MACvD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,YAAY;AAAA,MAC/C,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,cAAc;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,cAAc;AAAA,MAC/C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,kBAAkB,eAAe;AAAA,MACpD,sBAAsB,CAAC,iBAAiB,eAAe;AAAA,MACvD,uBAAuB,CAAC,mBAAmB,gBAAgB;AAAA,MAC3D,sBAAsB,CAAC,oBAAoB,iBAAiB;AAAA,MAC5D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,iBAAiB;AAAA,IACtD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,gBAAgB,iBAAiB;AAAA,EACtD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,kBAAkB,eAAe;AAAA,MACpD,sBAAsB,CAAC,iBAAiB,eAAe;AAAA,MACvD,uBAAuB,CAAC,mBAAmB,gBAAgB;AAAA,MAC3D,sBAAsB,CAAC,oBAAoB,iBAAiB;AAAA,MAC5D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,gBAAgB,iBAAiB;AAAA,IACtD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,eAAe;AAAA,IACf,cAAc;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,EACjB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,MAAM,IAAI;AAAA,MAC7B,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,MAAM,IAAI;AAAA,MAC7B,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,cAAc;AAAA,MAClD,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,cAAc;AAAA,MAClD,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,iBAAiB;AAAA,MACrD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,sBAAsB,CAAC,kBAAkB,kBAAkB;AAAA,MAC3D,uBAAuB,CAAC,iBAAiB,iBAAiB;AAAA,MAC1D,sBAAsB,CAAC,mBAAmB,mBAAmB;AAAA,IAC/D;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,iBAAiB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,iBAAiB,iBAAiB;AAAA,EACvD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,iBAAiB;AAAA,MACrD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,sBAAsB,CAAC,kBAAkB,kBAAkB;AAAA,MAC3D,uBAAuB,CAAC,iBAAiB,iBAAiB;AAAA,MAC1D,sBAAsB,CAAC,mBAAmB,mBAAmB;AAAA,IAC/D;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,iBAAiB,iBAAiB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,WAAW;AAAA,MAC5C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,sBAAsB,CAAC,WAAW,QAAQ;AAAA,MAC1C,uBAAuB,CAAC,cAAc,WAAW;AAAA,MACjD,sBAAsB,CAAC,cAAc,WAAW;AAAA,IAClD;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,cAAc,WAAW;AAAA,MAC5C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,sBAAsB,CAAC,WAAW,QAAQ;AAAA,MAC1C,uBAAuB,CAAC,cAAc,WAAW;AAAA,MACjD,sBAAsB,CAAC,cAAc,WAAW;AAAA,IAClD;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,YAAY;AAAA,MAC9C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,YAAY;AAAA,MAC9C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,eAAe,YAAY;AAAA,MAC9C,sBAAsB,CAAC,cAAc,WAAW;AAAA,MAChD,uBAAuB,CAAC,iBAAiB,cAAc;AAAA,MACvD,sBAAsB,CAAC,mBAAmB,gBAAgB;AAAA,MAC1D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,eAAe,YAAY;AAAA,MAC9C,sBAAsB,CAAC,cAAc,WAAW;AAAA,MAChD,uBAAuB,CAAC,iBAAiB,cAAc;AAAA,MACvD,sBAAsB,CAAC,mBAAmB,gBAAgB;AAAA,MAC1D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,oBAAoB,cAAc;AAAA,MACrD,sBAAsB,CAAC,kBAAkB,YAAY;AAAA,MACrD,uBAAuB,CAAC,iBAAiB,WAAW;AAAA,MACpD,sBAAsB,CAAC,qBAAqB,eAAe;AAAA,MAC3D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,oBAAoB,cAAc;AAAA,IACvD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,oBAAoB,cAAc;AAAA,EACvD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,oBAAoB,cAAc;AAAA,MACrD,sBAAsB,CAAC,kBAAkB,YAAY;AAAA,MACrD,uBAAuB,CAAC,iBAAiB,WAAW;AAAA,MACpD,sBAAsB,CAAC,qBAAqB,eAAe;AAAA,MAC3D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,oBAAoB,cAAc;AAAA,IACvD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,MACnD,sBAAsB,CAAC,kBAAkB,gBAAgB;AAAA,MACzD,uBAAuB,CAAC,qBAAqB,iBAAiB;AAAA,MAC9D,sBAAsB,CAAC,sBAAsB,kBAAkB;AAAA,MAC/D,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,eAAe,gBAAgB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,eAAe,gBAAgB;AAAA,EACpD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,cAAc;AAAA,MACnD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,sBAAsB,YAAY;AAAA,MACrD,sBAAsB,CAAC,YAAY,SAAS;AAAA,MAC5C,uBAAuB,CAAC,cAAc,eAAe;AAAA,MACrD,sBAAsB,CAAC,kBAAkB,aAAa;AAAA,MACtD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,SAAS;AAAA,IAChD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,IACb,kBAAkB,CAAC,kBAAkB,SAAS;AAAA,EAChD;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,sBAAsB,YAAY;AAAA,MACrD,sBAAsB,CAAC,YAAY,SAAS;AAAA,MAC5C,uBAAuB,CAAC,cAAc,eAAe;AAAA,MACrD,sBAAsB,CAAC,kBAAkB,aAAa;AAAA,MACtD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,MACb,kBAAkB,CAAC,kBAAkB,SAAS;AAAA,IAChD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,gBAAgB,eAAe;AAAA,MAClD,sBAAsB,CAAC,eAAe,cAAc;AAAA,MACpD,uBAAuB,CAAC,iBAAiB,gBAAgB;AAAA,MACzD,sBAAsB,CAAC,gBAAgB,eAAe;AAAA,MACtD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB,CAAC,gBAAgB,eAAe;AAAA,MAClD,sBAAsB,CAAC,eAAe,cAAc;AAAA,MACpD,uBAAuB,CAAC,iBAAiB,gBAAgB;AAAA,MACzD,sBAAsB,CAAC,gBAAgB,eAAe;AAAA,MACtD,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,QAAQ,MAAM;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,sBAAsB,CAAC,QAAQ,MAAM;AAAA,MACrC,uBAAuB,CAAC,QAAQ,MAAM;AAAA,MACtC,sBAAsB,CAAC,OAAO,KAAK;AAAA,IACrC;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,QAAQ,MAAM;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,sBAAsB,CAAC,QAAQ,MAAM;AAAA,MACrC,uBAAuB,CAAC,QAAQ,MAAM;AAAA,MACtC,sBAAsB,CAAC,OAAO,KAAK;AAAA,IACrC;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AAMA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,QAAQ,MAAM;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,sBAAsB,CAAC,QAAQ,MAAM;AAAA,MACrC,uBAAuB,CAAC,QAAQ,MAAM;AAAA,MACtC,sBAAsB,CAAC,OAAO,KAAK;AAAA,IACrC;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,MACJ,aAAa;AAAA,MACb,kBAAkB,CAAC,QAAQ,MAAM;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,sBAAsB,CAAC,QAAQ,MAAM;AAAA,MACrC,uBAAuB,CAAC,QAAQ,MAAM;AAAA,MACtC,sBAAsB,CAAC,OAAO,KAAK;AAAA,IACrC;AAAA,IACA,kBAAkB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;", "names": []}