{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-form.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { ReplaySubject, BehaviorSubject } from 'rxjs';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i2 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction NzFormItemFeedbackIconComponent_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzType\", ctx_r0.iconType);\n  }\n}\nclass NzFormStatusService {\n  constructor() {\n    this.formStatusChanges = new ReplaySubject(1);\n  }\n  static {\n    this.ɵfac = function NzFormStatusService_Factory(t) {\n      return new (t || NzFormStatusService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzFormStatusService,\n      factory: NzFormStatusService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormStatusService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n// Used in input-group/input-number-group to make sure components in addon work well\nclass NzFormNoStatusService {\n  constructor() {\n    this.noFormStatus = new BehaviorSubject(false);\n  }\n  static {\n    this.ɵfac = function NzFormNoStatusService_Factory(t) {\n      return new (t || NzFormNoStatusService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzFormNoStatusService,\n      factory: NzFormNoStatusService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormNoStatusService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst iconTypeMap = {\n  error: 'close-circle-fill',\n  validating: 'loading',\n  success: 'check-circle-fill',\n  warning: 'exclamation-circle-fill'\n};\nclass NzFormItemFeedbackIconComponent {\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.status = '';\n    this.iconType = null;\n  }\n  ngOnChanges(_changes) {\n    this.updateIcon();\n  }\n  updateIcon() {\n    this.iconType = this.status ? iconTypeMap[this.status] : null;\n    this.cdr.markForCheck();\n  }\n  static {\n    this.ɵfac = function NzFormItemFeedbackIconComponent_Factory(t) {\n      return new (t || NzFormItemFeedbackIconComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFormItemFeedbackIconComponent,\n      selectors: [[\"nz-form-item-feedback-icon\"]],\n      hostAttrs: [1, \"ant-form-item-feedback-icon\"],\n      hostVars: 8,\n      hostBindings: function NzFormItemFeedbackIconComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-form-item-feedback-icon-error\", ctx.status === \"error\")(\"ant-form-item-feedback-icon-warning\", ctx.status === \"warning\")(\"ant-form-item-feedback-icon-success\", ctx.status === \"success\")(\"ant-form-item-feedback-icon-validating\", ctx.status === \"validating\");\n        }\n      },\n      inputs: {\n        status: \"status\"\n      },\n      exportAs: [\"nzFormFeedbackIcon\"],\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[\"nz-icon\", \"\", 3, \"nzType\", 4, \"ngIf\"], [\"nz-icon\", \"\", 3, \"nzType\"]],\n      template: function NzFormItemFeedbackIconComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzFormItemFeedbackIconComponent_span_0_Template, 1, 1, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.iconType);\n        }\n      },\n      dependencies: [i1.NgIf, i2.NzIconDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormItemFeedbackIconComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-item-feedback-icon',\n      exportAs: 'nzFormFeedbackIcon',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: ` <span *ngIf=\"iconType\" nz-icon [nzType]=\"iconType\"></span> `,\n      host: {\n        class: 'ant-form-item-feedback-icon',\n        '[class.ant-form-item-feedback-icon-error]': 'status===\"error\"',\n        '[class.ant-form-item-feedback-icon-warning]': 'status===\"warning\"',\n        '[class.ant-form-item-feedback-icon-success]': 'status===\"success\"',\n        '[class.ant-form-item-feedback-icon-validating]': 'status===\"validating\"'\n      }\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    status: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormPatchModule {\n  static {\n    this.ɵfac = function NzFormPatchModule_Factory(t) {\n      return new (t || NzFormPatchModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzFormPatchModule,\n      declarations: [NzFormItemFeedbackIconComponent],\n      imports: [CommonModule, NzIconModule],\n      exports: [NzFormItemFeedbackIconComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, NzIconModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormPatchModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, NzIconModule],\n      exports: [NzFormItemFeedbackIconComponent],\n      declarations: [NzFormItemFeedbackIconComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzFormItemFeedbackIconComponent, NzFormNoStatusService, NzFormPatchModule, NzFormStatusService };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,OAAO,QAAQ;AAAA,EACzC;AACF;AACA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,oBAAoB,IAAI,cAAc,CAAC;AAAA,EAC9C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAqB;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,cAAc;AACZ,SAAK,eAAe,IAAI,gBAAgB,KAAK;AAAA,EAC/C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAAuB;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,uBAAsB;AAAA,IACjC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,cAAc;AAAA,EAClB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AACX;AACA,IAAM,kCAAN,MAAM,iCAAgC;AAAA,EACpC,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,aAAa;AACX,SAAK,WAAW,KAAK,SAAS,YAAY,KAAK,MAAM,IAAI;AACzD,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wCAAwC,GAAG;AAC9D,aAAO,KAAK,KAAK,kCAAoC,kBAAqB,iBAAiB,CAAC;AAAA,IAC9F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,4BAA4B,CAAC;AAAA,MAC1C,WAAW,CAAC,GAAG,6BAA6B;AAAA,MAC5C,UAAU;AAAA,MACV,cAAc,SAAS,6CAA6C,IAAI,KAAK;AAC3E,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,qCAAqC,IAAI,WAAW,OAAO,EAAE,uCAAuC,IAAI,WAAW,SAAS,EAAE,uCAAuC,IAAI,WAAW,SAAS,EAAE,0CAA0C,IAAI,WAAW,YAAY;AAAA,QACrR;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,oBAAoB;AAAA,MAC/B,UAAU,CAAI,oBAAoB;AAAA,MAClC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,WAAW,IAAI,GAAG,UAAU,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,QAAQ,CAAC;AAAA,MAC9E,UAAU,SAAS,yCAAyC,IAAI,KAAK;AACnE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,QAAQ,CAAC;AAAA,QACnF;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAQ,IAAI,QAAQ;AAAA,QACpC;AAAA,MACF;AAAA,MACA,cAAc,CAAI,MAAS,eAAe;AAAA,MAC1C,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iCAAiC,CAAC;AAAA,IACxG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,6CAA6C;AAAA,QAC7C,+CAA+C;AAAA,QAC/C,+CAA+C;AAAA,QAC/C,kDAAkD;AAAA,MACpD;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAmB;AAAA,IACtC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,+BAA+B;AAAA,MAC9C,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,SAAS,CAAC,+BAA+B;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,cAAc,YAAY;AAAA,IACtC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,SAAS,CAAC,+BAA+B;AAAA,MACzC,cAAc,CAAC,+BAA+B;AAAA,IAChD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}