<div
  class="flex flex-col min-h-[80vh] sm:flex-row  bg-[var(--background-light-gray)] text-[var(--text-dark)] font-[var(--font-family)]">
  <!-- Left Sidebar -->
  <div
    class="w-full sm:w-52 border-r border-[var(--hover-blue-gray)] p-4 bg-[var(--background-white)] shadow-[var(--box-shadow)]">
    <div class="space-y-1 *:no-underline transition-[var(--transition-default)] *:cursor-pointer flex sm:block gap-2">
      <div class="flex items-center gap-2 py-1 hover:text-[var(--primary-purple)]"
        [ngClass]="{'text-[var(--primary-purple)]': activeTab === 'user-management', 'text-[var(--text-medium-gray)]': activeTab !== 'user-management'}"
        (click)="activeTab = 'user-management'">
        <i class="ri-robot-line"></i>
        <span class=" ">User Management</span>
      </div>
      <div class="flex items-center gap-2 py-1 hover:text-[var(--primary-purple)]"
        [ngClass]="{'text-[var(--primary-purple)]': activeTab === 'connections', 'text-[var(--text-medium-gray)]': activeTab !== 'connections'}"
        (click)="activeTab = 'connections'">
        <i class="ri-link"></i>
        <span class=" ">Connections</span>
      </div>
      <div class="flex items-center gap-2 py-1 hover:text-[var(--primary-purple)]"
        [ngClass]="{'text-[var(--primary-purple)]': activeTab === 'database-connections', 'text-[var(--text-medium-gray)]': activeTab !== 'database-connections'}"
        (click)="activeTab = 'database-connections'">
        <i class="ri-database-2-line"></i>
        <span class=" ">Database Connections</span>
      </div>
      <div class="flex items-center gap-2 py-1 hover:text-[var(--primary-purple)]"
        [ngClass]="{'text-[var(--primary-purple)]': activeTab === 'models', 'text-[var(--text-medium-gray)]': activeTab !== 'models'}"
        (click)="activeTab = 'models'">
        <i class="ri-brain-line"></i>
        <span class=" ">Models</span>
      </div>
      <div class="flex items-center gap-2 py-1 hover:text-[var(--primary-purple)]"
        [ngClass]="{'text-[var(--primary-purple)]': activeTab === 'agents', 'text-[var(--text-medium-gray)]': activeTab !== 'agents'}"
        (click)="activeTab = 'agents'">
        <i class="ri-user-line"></i>
        <span class=" ">Agents</span>
      </div>
      <div class="flex items-center gap-2 py-1 hover:text-[var(--primary-purple)]"
        [ngClass]="{'text-[var(--primary-purple)]': activeTab === 'embedding', 'text-[var(--text-medium-gray)]': activeTab !== 'embedding'}"
        (click)="activeTab = 'embedding'">
        <i class="ri-code-line"></i>
        <span class=" ">Embedding</span>
      </div>
      <div class="flex items-center gap-2 py-1 hover:text-[var(--primary-purple)]"
        [ngClass]="{'text-[var(--primary-purple)]': activeTab === 'embedding', 'text-[var(--text-medium-gray)]': activeTab !== 'prompts-library'}"
        (click)="activeTab = 'prompts-library'">
        <i class="ri-code-line"></i>
        <span class=" ">Prompts Library</span>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="w-full" *ngIf="activeTab === 'user-management'">
    <app-user-management></app-user-management>
  </div>
  <div class="w-full" *ngIf="activeTab === 'agents'">
    <app-agents></app-agents>
  </div>
  <!-- <div class="w-full" *ngIf="activeTab === 'embedding'">
     <app-embedding></app-embedding>
  </div> -->
  <div class="w-full" *ngIf="activeTab === 'prompts-library'">
    <app-prompts-library></app-prompts-library>
  </div>
  <div class="w-full" *ngIf="activeTab === 'database-connections'">
    <app-database-connections></app-database-connections>
  </div>

  <!-- API Settings -->
  <div
    class="px-4 sm:px-6 py-5 bg-[var(--background-light-gray)] rounded-[var(--border-radius-large)] mb-6 w-full shadow-[var(--box-shadow)]"
    *ngIf="activeTab === 'connections'" style="height: calc(100vh - 84px);">
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex flex-row items-start sm:items-center justify-between gap-4">
        <h1 class="font-[var(--font-weight-bold)] text-[var(--text-dark)]">API Settings</h1>
        <button (click)="onAddApi()"
          class="bg-[var(--primary-purple)] text-[var(--background-white)] px-3 py-1 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] transition-[var(--transition-default)] cursor-pointer border-none outline-none sm:w-auto">
          Add API
        </button>
      </div>

      <!-- API List -->
      <div class="space-y-6">
        @for (api of apiLists; track $index) {
        <div
          class="flex flex-col sm:flex-row items-start sm:items-center justify-between p-3 bg-[var(--background-white)] rounded-[var(--border-radius-large)] border border-[var(--hover-blue-gray)] gap-4">
          <div class="w-full sm:w-auto">
            <h3 class="text-base sm:text-lg  text-[var(--text-dark)] capitalize ">{{api.tokenUrl?.split('.')[1]}}
              Url</h3>
            <span class="  break-all text-[var(--text-medium-gray)]">{{api.tokenUrl}}</span>
          </div>
          <div class="relative flex items-center gap-4 w-full sm:w-auto justify-end">
            <button (click)="deleteApi(api)"
              class="p-2 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] cursor-pointer outline-none border-none">
              <i class="ri-delete-bin-6-line text-red-500 hover:text-red-600 transition-colors text-lg"></i>
            </button>
          </div>
        </div>
        }

        <!-- Empty State -->
        <div *ngIf="apiLists.length === 0 && !showForm"
          class="flex flex-col items-center justify-center p-6 sm:p-10 bg-[var(--background-white)] rounded-[var(--border-radius-large)] border border-dashed border-[var(--hover-blue-gray)]">
          <i class="ri-brain-line text-4xl sm:text-6xl text-[var(--text-medium-gray)] mb-4"></i>
          <p class="text-[var(--text-medium-gray)] text-center mb-4  ">No API Provider have been added yet.</p>
          <button (click)="onAddApi()"
            class="px-4 py-2 bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] rounded-[var(--border-radius-small)] flex items-center gap-2 transition-[var(--transition-default)] outline-none border-none cursor-pointer text-[var(--background-white)]  ">
            Add Your First Model
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Model Settings -->
  <div
    class="px-4 sm:px-6 py-5 bg-[var(--background-light-gray)]rounded-[var(--border-radius-large)] mb-6 w-full shadow-[var(--box-shadow)]"
    *ngIf="activeTab === 'models'" style="height: calc(100vh - 84px);">
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div class="flex items-center gap-4">
          <div class="flex items-end">
            <h1 class="font-[var(--font-weight-bold)] m-0 text-[var(--text-dark)] leading-normal"
              style="line-height: normal;">Models</h1>
            <span class="bg-[var(--hover-blue-gray)] px-2 py-0.5 rounded text-xs text-[var(--text-dark)] mx-2">{{
              filterdModels.length }}</span>
          </div>
        </div>
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full sm:w-auto">
          <div
            class="flex items-center gap-2 px-2 bg-[var(--background-white)] rounded-[var(--border-radius-small)] p-1">

            <i class="ri-search-line text-[var(--text-medium-gray)]"></i>
            <input type="text" placeholder="Search Models" [(ngModel)]="searchModelsQuery" (input)="filterModels()"
              class="w-full px-2 py-1 text-[var(--text-dark)] rounded-[var(--border-radius-small)] outline-none border-none bg-[var(--background-white)] ">
          </div>
          <select [(ngModel)]="selectedProvider" (change)="filterModels()"
            class="bg-[var(--background-white)] text-[var(--text-dark)] outline-none border-none  p-2 rounded-[var(--border-radius-small)]   w-full sm:w-auto">
            <option value="">All Providers</option>
            <option *ngFor="let provider of uniqueProviders" [value]="provider">{{ provider }}</option>
          </select>
        </div>
      </div>

      <!-- Models List -->
      <div class="space-y-3  overflow-y-auto max-h-[80vh]">
        <ng-container *ngIf="filterdModels.length > 0">
          <div
            class="flex flex-col sm:flex-row items-start sm:items-center justify-between p-3 bg-[var(--background-white)] rounded-[var(--border-radius-large)] border border-[var(--hover-blue-gray)]"
            *ngFor="let model of filterdModels">
            <div class="flex items-center gap-3 mb-2 sm:mb-0 w-full sm:w-auto">
              <div
                class="w-8 h-8 bg-[var(--hover-blue-gray)] rounded-full flex items-center justify-center flex-shrink-0">
                <i class="ri-ai-generate text-[var(--primary-purple)]"></i>
              </div>
              <div class="flex-1">
                <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2">
                  <h3
                    class=" w-auto font-[var(--font-weight-regular)] flex justify-between sm:justify-start   text-[var(--text-dark)]">
                    {{model.modelName}}
                    <div class="relative inline-block sm:hidden w-10 align-middle select-none sm:invisible">
                      <input type="checkbox" [(ngModel)]="model.isActive" (input)="updateModelIsActive(model)"
                        class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                        [ngClass]="{'mr-[14px]':!model.isActive, 'mr-0': model.isActive}" />
                      <label
                        class="toggle-label block overflow-hidden h-6 rounded-full bg-[var(--hover-blue-gray)] cursor-pointer"></label>
                    </div>
                  </h3>
                  <h4 class="  font-[var(--font-weight-regular)] text-[var(--text-medium-gray)]">{{model.modelProvider}}
                  </h4>
                </div>
                <div class="flex flex-row items-start sm:items-center gap-2">
                  <p class="text-xs text-[var(--text-medium-gray)] m-0">AI Agents:</p>
                  <div class="flex flex-wrap items-center gap-1">
                    <span
                      class="text-xs bg-[var(--hover-blue-gray)] px-2 py-0.5 rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)] mr-1 text-[var(--text-dark)]"
                      *ngFor="let agent of model.agentNames">{{agent}}</span>
                    <span class="text-xs text-[var(--text-medium-gray)]" *ngIf="model.agentNames.length==0">No agents
                      associated</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex items-center gap-4 w-full sm:w-auto justify-start sm:justify-end">
              <div class="relative hidden sm:inline-block w-10 align-middle select-none">
                <input type="checkbox" [(ngModel)]="model.isActive" (input)="updateModelIsActive(model)"
                  class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                  [ngClass]="{'mr-[14px]':!model.isActive, 'mr-0': model.isActive}" />
                <label
                  class="toggle-label block overflow-hidden h-6 rounded-full bg-[var(--hover-blue-gray)] cursor-pointer"></label>
              </div>
            </div>
          </div>
        </ng-container>

        <!-- Empty State -->
        <div *ngIf="filterdModels.length === 0"
          class="flex flex-col items-center justify-center p-6 sm:p-8 bg-[var(--background-white)] rounded-[var(--border-radius-large)] border border-dashed border-[var(--hover-blue-gray)]">
          <i class="ri-search-line text-4xl sm:text-6xl mb-4 text-[var(--text-medium-gray)]"></i>
          <h3 class="text-lg sm:text-xl font-[var(--font-weight-regular)] text-[var(--text-dark)] mb-2">No models found
          </h3>
          <p class="text-[var(--text-medium-gray)] text-center  ">No models match your current search criteria.</p>
          <button (click)="searchModelsQuery = ''; selectedProvider = ''; filterModels();"
            class="mt-4 px-4 py-2 bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] rounded-[var(--border-radius-small)]   transition-[var(--transition-default)] cursor-pointer border-none outline-none text-[var(--background-white)]">
            Clear search
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Form Overlay -->
<div *ngIf="showForm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div
    class="bg-[var(--background-white)] rounded-[var(--border-radius-large)] p-6 w-full max-w-md border border-[var(--hover-blue-gray)] shadow-[var(--box-shadow)]">
    <div class="space-y-4">
      <div>
        <label class="block   font-[var(--font-weight-regular)] text-[var(--text-medium-gray)]">Token URL</label>
        <input type="text" [(ngModel)]="apiData.tokenUrl" (input)="isCredentialsValid = false"
          class="w-full bg-[var(--background-light-gray)] px-3 py-2 border border-[var(--hover-blue-gray)] outline-none text-[var(--text-dark)] rounded-[var(--border-radius-small)]  ">
      </div>
      <div>
        <label class="block   font-[var(--font-weight-regular)] text-[var(--text-medium-gray)]">API Key</label>
        <input type="text" [(ngModel)]="apiData.apiKey" (input)="isCredentialsValid = false"
          class="w-full bg-[var(--background-light-gray)] px-3 py-2 border border-[var(--hover-blue-gray)] outline-none text-[var(--text-dark)] rounded-[var(--border-radius-small)]  ">
      </div>
      <div class="flex justify-end gap-3 mt-6">
        <button (click)="validateCredentials()"
          class="px-4 py-2 bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)] cursor-pointer outline-none border-none text-[var(--background-white)]">
          Validate
        </button>
      </div>
    </div>
    <div class="flex justify-end gap-3 mt-6">
      <button (click)="resetForm()"
        class="px-4 py-2 bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)] cursor-pointer outline-none border-none text-[var(--text-dark)]">
        Cancel
      </button>
      <button (click)="saveApi()"
        [ngClass]="{'bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)]': isCredentialsValid, 'bg-gray-500 !cursor-not-allowed': !isCredentialsValid}"
        class="px-4 py-2 rounded-[var(--border-radius-small)] transition-[var(--transition-default)] cursor-pointer outline-none border-none text-[var(--background-white)]"
        [disabled]="!isCredentialsValid">
        Save
      </button>
    </div>
  </div>
</div>