import { Component, inject } from '@angular/core';
import { KeyValue } from '@angular/common';
import { TogglingService } from '../../toggling.service';
import { ChatListService } from '../../services/chat-list.service';
import { ThemeService } from '../../../shared/services/theam.service';
import {
  NavigationEnd,
  Router,
  RouterLink,
  RouterLinkActive,
  ActivatedRoute,
} from '@angular/router';
import {
  ChatServiceProxy,
  ChatRequestDto,
  AgentDefinitionServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AuthService } from '../../../shared/services/auth.service';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subscription, BehaviorSubject } from 'rxjs';
import { filter } from 'rxjs/operators';
import {
  Note,
  NotesService,
} from '../../MyNotesProjects/services/notes.service';

import { DailyInsightsSidebarComponent } from '../@rightSideComponents/daily-insights-sidebar/daily-insights-sidebar.component';
// import { RightsidebarchatComponent } from '../@leftSideComponents/rightsidebarchat/rightsidebarchat.component';
import { SettingsidebarcomponentComponent } from '../@leftSideComponents/settingsidebarcomponent/settingsidebarcomponent.component';
import { NotesSidebarComponent } from '../@leftSideComponents/notes-sidebar/notes-sidebar.component';
import { AgentAndWorkspaceSidebarComponent } from '../@leftSideComponents/agent-and-workspace-sidebar/agent-and-workspace-sidebar.component';
import { RequestSidebarComponent } from '../@leftSideComponents/request-sidebar/request-sidebar.component';
// import { RequestListComponent } from '../../my-request/request-list/request-list.component';
@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [
    ServiceProxyModule,
    CommonModule,
    FormsModule,
    RouterLink,
    NzButtonModule,
    NzPopoverModule,
    DailyInsightsSidebarComponent,
    // RightsidebarchatComponent,
    SettingsidebarcomponentComponent,
    NotesSidebarComponent,
    AgentAndWorkspaceSidebarComponent,
    RequestSidebarComponent,
    // RequestListComponent,
  ],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
})
export class SidebarComponent {
  togglingService = inject(TogglingService);
  chatListService = inject(ChatListService);
  router = inject(Router);
  notesService = inject(NotesService);
  activatedRoute = inject(ActivatedRoute);
  themeService = inject(ThemeService);
  groupedChats: { [key: string]: any[] } = { History: [] };
  chatId: any;
  filteredGroupedChats: { [key: string]: any[] } = { ...this.groupedChats };
  isAllChatsOpen = true;
  activeTab:
    | 'all'
    | 'pinned-history'
    | 'favorite'
    | 'archive'
    | 'notes'
    | 'plugins'
    | 'history' = 'all';
  panelVisible = true;

  // Add this property to track if in workspace mode
  isWorkspaceMode = false;

  // Notes-related properties
  favoriteNotes: Note[] = [];
  recentNotes: Note[] = [];
  notesByCategory: { key: string; value: any[] }[] = [];
  isNotesSubmenuOpen = false;
  private favoriteNotesSubscription!: Subscription;
  private recentNotesSource = new BehaviorSubject<number>(5); // Default limit
  private recentNotesSubscription!: Subscription;

  // Add this property to track if in admin mode
  isSettingsMode = false;
  // Property to track current admin tab
  activeAdminTab = 'prompt-library';

  // Add this property to track if in DailyInsight mode
  isDailyInsightMode = false;

  // Add this property to track if in Notes mode
  isNotesMode = false;

  // Add this property to track if in Chat mode
  isChatMode = false;

  // Add this property to track if in My Request mode
  isMyRequestMode = false;
  allMyRequestMode = false;

  // Properties for chat-sidebar component
  userInput: ChatRequestDto = new ChatRequestDto();
  selectedAgent: string = '';

  // Properties for agent sidebar
  workspaceAgents: any[] = [];
  agentSidebarTitle: string = 'Available Agents';

  originalOrder = (
    a: KeyValue<string, any[]>,
    b: KeyValue<string, any[]>
  ): number => {
    const order = ['History'];
    return order.indexOf(a.key) - order.indexOf(b.key);
  };
  hasMoreMessages = true;
  originalChatList: any[] = [];
  pinnedChats: any[] = [];
  favoriteChats: any[] = [];
  archivedChats: any[] = [];
  notes: any[] = [];
  counter = 1;
  hasWorkspaces = false;
  workspaceName: string = '';

  tabConfig: {
    [key: string]: {
      title: string;
      chats: any;
      isGrouped: boolean;
      hasMore?: boolean;
    };
  } = {
    all: {
      title: 'All Chats',
      chats: () => this.groupedChats,
      isGrouped: true,
      hasMore: true,
    },
    'pinned-history': {
      title: 'Pinned Chats',
      chats: () => this.groupChatsByDate(this.pinnedChats),
      isGrouped: true,
    },
    favorite: {
      title: 'Favorite Chats',
      chats: () => this.groupChatsByDate(this.favoriteChats),
      isGrouped: true,
    },
    archive: {
      title: 'Archive Chats',
      chats: () => this.groupChatsByDate(this.archivedChats),
      isGrouped: true,
    },
    notes: { title: 'My Notes', chats: () => [], isGrouped: false },
    history: { title: 'Chat History', chats: () => [], isGrouped: false },
  };

  isDarkMode: boolean = false;
  isLoading: boolean = false;
  isLoadingMore: boolean = false;

  constructor(
    private _chatService: ChatServiceProxy,
    public authService: AuthService,
    private nzMessageService: NzMessageService,
    private agentDefinitionService: AgentDefinitionServiceProxy
  ) {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event: any) => {
        // Get the current URL
        const url = event.url;

        // Update mode flags
        this.isDailyInsightMode = url.startsWith('/daily-insight');
        this.isWorkspaceMode =
          url.includes('/workspaces') && !url.includes('/chat');
        this.isSettingsMode = url.includes('/settings');
        this.isNotesMode = url.includes('/notes');
        this.isChatMode = url.includes('/chat');
        this.isMyRequestMode =
          url.includes('/my-request') || url.includes('/all-request');
        this.allMyRequestMode =
          url.includes('/my-request') || url.includes('/all-request');

        // Keep sidebar open for daily-insight routes
        if (this.isDailyInsightMode && !this.togglingService.isNavbarOpen) {
          this.togglingService.toggleNavbar();
        }

        // Update active admin tab when in settings
        if (this.isSettingsMode) {
          const urlParts = url.split('/');
          // Get the last segment of the URL
          this.activeAdminTab = urlParts[urlParts.length - 1];
        } else {
          // Reset activeAdminTab when not in settings
          this.activeAdminTab = '';
        }

        // Update workspace name and reset counter when navigating between workspaces
        const oldWorkspaceName = this.workspaceName;
        if (url.includes('workspaces')) {
          this.workspaceName = url.split('/')[2];
          // Only reset counter if workspace changed
          if (oldWorkspaceName !== this.workspaceName) {
            this.counter = 1;
          }
        } else {
          if (this.workspaceName !== '') {
            // Only reset if we're changing from a workspace to non-workspace
            this.workspaceName = '';
            this.counter = 1;
          }
        }

        if (this.workspaceName?.length > 0) {
          this.hasWorkspaces = true;
        }

        // Store the current tab before data reloads
        const previousTab = this.activeTab;

        // Reload data when navigating between different sections or workspaces
        const needsReload =
          oldWorkspaceName !== this.workspaceName || // Workspace changed
          (url.includes('/chat') &&
            !event.urlAfterRedirects.includes('/chat')) || // Navigated to/from chat
          this.isDailyInsightMode !== (url === '/' || url === ''); // Daily insight mode changed

        if (needsReload) {
          console.log('Navigation requires data reload');

          // Only load chat lists if we're not in daily insight mode
          if (!this.isDailyInsightMode) {
            console.log('Not in daily insight mode, loading chat lists');
            this.loadChatList();

            // Only load data for the active tab
            if (this.activeTab === 'pinned-history') {
              this.loadPinnedChats();
            } else if (this.activeTab === 'favorite') {
              this.loadFavoriteChats();
            } else if (this.activeTab === 'archive') {
              this.loadArchivedChats();
            }
          } else {
            console.log('In daily insight mode, skipping chat list loading');
          }
        }

        // Restore the previous tab
        if (previousTab !== 'all') {
          this.activeTab = previousTab;

          // Set the appropriate filtered list based on active tab
          switch (this.activeTab) {
            case 'pinned-history':
              this.filteredGroupedChats = this.groupChatsByDate(
                this.pinnedChats
              );
              break;
            case 'favorite':
              this.filteredGroupedChats = this.groupChatsByDate(
                this.favoriteChats
              );
              break;
            case 'archive':
              this.filteredGroupedChats = this.groupChatsByDate(
                this.archivedChats
              );
              break;
            default:
              // For 'all' tab or other tabs, keep default behavior
              break;
          }
        }
      });
    this.isDarkMode = document.body.classList.contains('dark-mode');
  }

  ngOnInit(): void {
    // Initialize based on current URL
    const currentUrl = this.router.url;
    this.isSettingsMode = currentUrl.includes('/settings');
    this.isWorkspaceMode =
      currentUrl.includes('/workspaces') && !currentUrl.includes('/chat');
    this.isDailyInsightMode = currentUrl.startsWith('/daily-insight');
    this.isNotesMode = currentUrl.includes('/notes');
    this.isChatMode = currentUrl.includes('/chat');
    this.isMyRequestMode =
      currentUrl.includes('/my-request') || currentUrl.includes('/all-request');

    // Extract workspace name from URL if available
    if (currentUrl.includes('workspaces')) {
      this.workspaceName = currentUrl.split('/')[2];
      if (this.workspaceName?.length > 0) {
        this.hasWorkspaces = true;
      }
    }

    // Extract active admin tab from URL if in settings mode
    if (this.isSettingsMode) {
      const urlParts = currentUrl.split('/');
      if (urlParts.length > 2) {
        this.activeAdminTab = urlParts[2];
      }
    }

    // Load chat list data immediately on initialization
    console.log('Initializing sidebar component, loading chat list data');
    this.loadChatList();

    // Load other data based on active tab
    if (this.activeTab === 'pinned-history') {
      this.loadPinnedChats();
    } else if (this.activeTab === 'favorite') {
      this.loadFavoriteChats();
    } else if (this.activeTab === 'archive') {
      this.loadArchivedChats();
    }
  }

  async loadChatList() {
    // Show loading indicator if we're on the all chats tab
    if (this.activeTab === 'all') {
      this.isLoading = true;
    }

    try {
      console.log('Loading chat list for workspace:', this.workspaceName);

      // Make sure counter is initialized
      if (!this.counter || this.counter < 1) {
        this.counter = 1;
      }

      let res: any = await this._chatService
        .list(this.workspaceName, this.counter, 15)
        .toPromise();

      if (res && res.messages) {
        console.log(
          'Chat list loaded successfully:',
          res.messages.length,
          'messages'
        );
        this.chatListService.chatList = res.messages;
        this.originalChatList = res.messages;
        this.hasMoreMessages = res.hasMoreMessages;
        this.chatListService.groupChatsByDate();
        this.groupedChats = this.chatListService.groupedChats;

        // Only update filteredGroupedChats if we're on the 'all' tab
        // This prevents losing our filtered view when clicking on a chat
        if (this.activeTab === 'all') {
          this.filteredGroupedChats = { ...this.groupedChats };
        } else {
          // For other tabs, maintain the existing filtered list by refreshing it
          switch (this.activeTab) {
            case 'pinned-history':
              this.filteredGroupedChats = this.groupChatsByDate(
                this.pinnedChats
              );
              break;
            case 'favorite':
              this.filteredGroupedChats = this.groupChatsByDate(
                this.favoriteChats
              );
              break;
            case 'archive':
              this.filteredGroupedChats = this.groupChatsByDate(
                this.archivedChats
              );
              break;
          }
        }

        this.counter++;
      } else {
        console.warn('Chat list response was empty or invalid:', res);
        // Initialize with empty data
        this.initializeEmptyChats();
      }
    } catch (err) {
      console.error('Error loading chat list:', err);
      // Initialize with empty data
      this.initializeEmptyChats();
    } finally {
      // Always make sure to turn off loading indicator
      this.isLoading = false;
    }
  }

  // Helper method to initialize empty chat lists
  private initializeEmptyChats() {
    this.chatListService.chatList = [];
    this.originalChatList = [];
    this.hasMoreMessages = false;
    this.chatListService.groupedChats = {
      Today: [],
      Yesterday: [],
      'Last 7 Days': [],
      'Last 30 Days': [],
      Older: [],
    };
    this.groupedChats = this.chatListService.groupedChats;
    this.filteredGroupedChats = { ...this.groupedChats };
  }

  loadMoreChatList() {
    // Show loading indicator for "load more"
    this.isLoadingMore = true;
    this._chatService.list(this.workspaceName, this.counter, 15).subscribe({
      next: (res: any) => {
        this.counter++;
        if (res) {
          this.chatListService.chatList.push(...res.messages);
          this.originalChatList.push(...res.messages);
          this.hasMoreMessages = res.hasMoreMessages;
          this.chatListService.groupChatsByDate();
          this.groupedChats = this.chatListService.groupedChats;

          // Only update filteredGroupedChats if we're on the 'all' tab
          if (this.activeTab === 'all') {
            this.filteredGroupedChats = { ...this.groupedChats };
          }
        }
        this.isLoadingMore = false;
      },
      error: (err) => {
        console.error('Error loading more chats:', err);
        this.isLoadingMore = false;
      },
    });
  }

  loadPinnedChats() {
    // Show loading indicator if we're on the pinned tab
    if (this.activeTab === 'pinned-history') {
      this.isLoading = true;
    }

    this._chatService.pinned(this.workspaceName).subscribe({
      next: (res: any) => {
        if (res && Array.isArray(res)) {
          this.pinnedChats = res;
          if (this.activeTab === 'pinned-history') {
            this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);
            this.isLoading = false;
          }
        } else {
          this.pinnedChats = [];
          if (this.activeTab === 'pinned-history') {
            this.filteredGroupedChats = {};
            this.isLoading = false;
          }
        }
      },
      error: (err) => {
        console.error('Error loading pinned chats:', err);
        this.pinnedChats = [];
        if (this.activeTab === 'pinned-history') {
          this.filteredGroupedChats = {};
          this.isLoading = false;
        }
      },
    });
  }

  loadFavoriteChats() {
    // Show loading indicator if we're on the favorites tab
    if (this.activeTab === 'favorite') {
      this.isLoading = true;
    }

    this._chatService.favorites(this.workspaceName).subscribe({
      next: (res: any) => {
        if (res && Array.isArray(res)) {
          this.favoriteChats = res;
          if (this.activeTab === 'favorite') {
            this.filteredGroupedChats = this.groupChatsByDate(
              this.favoriteChats
            );
            this.isLoading = false;
          }
        } else {
          this.favoriteChats = [];
          if (this.activeTab === 'favorite') {
            this.filteredGroupedChats = {};
            this.isLoading = false;
          }
        }
      },
      error: (err) => {
        console.error('Error loading favorite chats:', err);
        this.favoriteChats = [];
        if (this.activeTab === 'favorite') {
          this.filteredGroupedChats = {};
          this.isLoading = false;
        }
      },
    });
  }

  loadArchivedChats() {
    // Show loading indicator if we're on the archive tab
    if (this.activeTab === 'archive') {
      this.isLoading = true;
    }

    this._chatService.archived(this.workspaceName).subscribe({
      next: (res: any) => {
        if (res && Array.isArray(res)) {
          this.archivedChats = res;
          if (this.activeTab === 'archive') {
            this.filteredGroupedChats = this.groupChatsByDate(
              this.archivedChats
            );
            this.isLoading = false;
          }
        } else {
          this.archivedChats = [];
          if (this.activeTab === 'archive') {
            this.filteredGroupedChats = {};
            this.isLoading = false;
          }
        }
      },
      error: (err) => {
        console.error('Error loading archived chats:', err);
        this.archivedChats = [];
        if (this.activeTab === 'archive') {
          this.filteredGroupedChats = {};
          this.isLoading = false;
        }
      },
    });
  }

  toggleTab(tab: any) {
    // If we're already on this tab, don't do anything
    if (this.activeTab === tab) {
      return;
    }

    // Update the activeTab
    this.activeTab = tab;
    this.isAllChatsOpen = true;

    // Ensure we're in chat mode (not settings or workspace mode)
    this.isSettingsMode = false;
    this.isWorkspaceMode = false;
    this.isDailyInsightMode = false;

    // Apply changes based on the selected tab
    switch (tab) {
      case 'pinned-history':
        // Check if we already have pinned chats data
        if (!this.pinnedChats || this.pinnedChats.length === 0) {
          // Only load if we don't have data
          this.loadPinnedChats();
        } else {
          // Use existing data
          this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);
        }
        break;
      case 'favorite':
        // Check if we already have favorite chats data
        if (!this.favoriteChats || this.favoriteChats.length === 0) {
          // Only load if we don't have data
          this.loadFavoriteChats();
        } else {
          // Use existing data
          this.filteredGroupedChats = this.groupChatsByDate(this.favoriteChats);
        }
        break;
      case 'archive':
        // Check if we already have archived chats data
        if (!this.archivedChats || this.archivedChats.length === 0) {
          // Only load if we don't have data
          this.loadArchivedChats();
        } else {
          // Use existing data
          this.filteredGroupedChats = this.groupChatsByDate(this.archivedChats);
        }
        break;
      case 'all':
      default:
        this.isNotesSubmenuOpen = false;
        this.filteredGroupedChats = this.groupedChats;
        break;
    }
  }

  toggleNotesSubmenu() {
    this.isNotesSubmenuOpen = !this.isNotesSubmenuOpen;
  }

  trackByNoteCategory(
    index: number,
    category: { key: string; value: any[] }
  ): string {
    return category.key;
  }

  trackByChatId(index: number, chat: any): string {
    return chat.id;
  }

  /**
   * Handles the agent selected event from the agent sidebar component
   * @param agent The selected agent
   */
  onAgentSelected(agent: any) {
    if (!agent) return;

    // Set the selected agent
    this.selectedAgent = agent.agentName;

    // Navigate to the agent chat page with the selected agent
    this.router.navigate(['/agent-chat', agent.agentName]);
  }

  filterChats(event: Event) {
    event.stopPropagation();
    const searchTerm = (event.target as HTMLInputElement).value.toLowerCase();

    if (!searchTerm) {
      this.filteredGroupedChats = { ...this.groupedChats };
      return;
    }

    this.filteredGroupedChats = {};
    Object.keys(this.groupedChats).forEach((group) => {
      this.filteredGroupedChats[group] = this.groupedChats[group].filter(
        (chat: any) => chat.title.toLowerCase().includes(searchTerm)
      );
    });
  }

  toggleAllChats(event: Event) {
    event.stopPropagation();
    this.isAllChatsOpen = !this.isAllChatsOpen;
  }

  addNewChats(event: Event) {
    event.stopPropagation();
    this.isAllChatsOpen = true;
    this.chatListService.chatId = 0; // Reset chatId to 0 for new chat

    if (this.workspaceName) {
      // If workspaceName is available, navigate to the chat within that workspace
      this.router.navigate(['workspaces', this.workspaceName, 'chat']);
    } else {
      // If no workspace, navigate to chat
      this.router.navigate(['/chat']);
    }
  }

  toggleChat(
    event: Event,
    chat: {
      id: number;
      title: string;
      lastMessage: string;
      isToggled: boolean;
      chats: any;
    }
  ) {
    // Set the chat ID and navigate to the chat
    this.chatListService.chatId = chat.id;

    // Navigate based on workspace context
    if (this.workspaceName) {
      this.router.navigate(['workspaces', this.workspaceName, 'chat', chat.id]);
    } else {
      this.router.navigate(['/chat', chat.id]);
    }

    event.stopPropagation();
    chat.isToggled = !chat.isToggled;
  }

  openSidebar() {
    this.togglingService.isNavbarOpen = true;
  }

  addToPinnedChat(chat: any) {
    this.chatId = chat.id;
    this._chatService.pin(this.chatId, !chat.isPinned).subscribe((res) => {
      if (res.isPinned) {
        this.nzMessageService.success('Chat pinned successfully!');
        this.pinnedChats = [...this.pinnedChats, res];
      } else {
        this.nzMessageService.success('Chat unpinned successfully!');
        this.pinnedChats = this.pinnedChats.filter((c) => c.id !== res.id);
      }
      const index = this.chatListService.chatList.findIndex(
        (c: any) => c.id === res.id
      );
      if (index !== -1) {
        this.chatListService.chatList[index] = res;
        this.chatListService.groupChatsByDate();
        this.groupedChats = this.chatListService.groupedChats;
        this.filteredGroupedChats = { ...this.groupedChats };
      }
    });
  }

  addToFavChat(chat: any) {
    this.chatId = chat.id;
    this._chatService
      .favorite(this.chatId, !chat.isFavorite)
      .subscribe((res) => {
        if (res.isFavorite) {
          this.nzMessageService.success('Chat favorited successfully!');
          this.favoriteChats = [...this.favoriteChats, res];
        } else {
          this.nzMessageService.success('Chat unfavorited successfully!');
          this.favoriteChats = this.favoriteChats.filter(
            (c) => c.id !== res.id
          );
        }
        const index = this.chatListService.chatList.findIndex(
          (c: any) => c.id === res.id
        );
        if (index !== -1) {
          this.chatListService.chatList[index] = res;
          this.chatListService.groupChatsByDate();
          this.groupedChats = this.chatListService.groupedChats;
          this.filteredGroupedChats = { ...this.groupedChats };
        }
      });
  }

  addToArchiveChat(chat: any) {
    this.chatId = chat.id;
    this._chatService
      .archive(this.chatId, !chat.isArchived)
      .subscribe((res) => {
        if (res.isArchived) {
          this.nzMessageService.success('Chat archived successfully!');
          this.archivedChats = [...this.archivedChats, res];
          this.chatListService.chatList = this.chatListService.chatList.filter(
            (c: any) => c.id !== res.id
          );
        } else {
          this.nzMessageService.success('Chat unarchived successfully!');
          this.archivedChats = this.archivedChats.filter(
            (c) => c.id !== res.id
          );
          this.chatListService.chatList.push(res);
        }
        this.chatListService.groupChatsByDate();
        this.groupedChats = this.chatListService.groupedChats;
        this.filteredGroupedChats = { ...this.groupedChats };
      });
  }

  getCurrentTabChats() {
    if (this.activeTab === 'all') {
      return this.groupedChats;
    }

    const chats = this.filteredGroupedChats;
    if (this.tabConfig[this.activeTab].isGrouped) {
      return Object.keys(chats).length > 0 ? chats : { 'No Chats': [] };
    }
    return chats;
  }

  getCurrentTabTitle() {
    return this.tabConfig[this.activeTab].title;
  }

  isCurrentTabGrouped() {
    return this.tabConfig[this.activeTab].isGrouped;
  }

  hasMoreForCurrentTab() {
    return this.tabConfig[this.activeTab].hasMore && this.hasMoreMessages;
  }

  toggleTheme() {
    this.isDarkMode = !this.isDarkMode;
    if (this.isDarkMode) {
      document.body.classList.add('dark-mode');
    } else {
      document.body.classList.remove('dark-mode');
    }
  }

  private groupChatsByDate(chats: any[]): { [key: string]: any[] } {
    // Return early if the chats array is null, undefined, or empty
    if (!chats || chats.length === 0) {
      return {};
    }

    const grouped: { [key: string]: any[] } = {};
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 7);
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    chats.forEach((chat) => {
      if (!chat || chat.createdDate === undefined) {
        return;
      }

      try {
        let date: Date;
        if (typeof chat.createdDate === 'string') {
          date = new Date(chat.createdDate);
        } else if (
          chat.createdDate &&
          typeof chat.createdDate === 'object' &&
          chat.createdDate.toString
        ) {
          date = new Date(chat.createdDate.toString());
        } else {
          return;
        }

        if (isNaN(date.getTime())) {
          return;
        }

        let groupKey:
          | 'Today'
          | 'Yesterday'
          | 'Previous 7 Days'
          | 'Previous 30 Days'
          | string;

        if (this.isSameDay(date, today)) {
          groupKey = 'Today';
        } else if (this.isSameDay(date, yesterday)) {
          groupKey = 'Yesterday';
        } else if (date >= sevenDaysAgo) {
          groupKey = 'Previous 7 Days';
        } else if (date >= thirtyDaysAgo) {
          groupKey = 'Previous 30 Days';
        } else {
          groupKey = date.toLocaleDateString('en-US', {
            month: 'long',
            year: 'numeric',
          });
        }

        if (!grouped[groupKey]) {
          grouped[groupKey] = [];
        }
        grouped[groupKey].push(chat);
      } catch (error) {
        console.error('Error processing chat date:', error);
      }
    });

    // Sort chats within each group
    for (const key in grouped) {
      grouped[key].sort((a, b) => {
        try {
          const dateA = new Date(a.createdDate.toString()).getTime();
          const dateB = new Date(b.createdDate.toString()).getTime();
          return dateB - dateA;
        } catch (error) {
          return 0;
        }
      });
    }

    return grouped;
  }

  private isSameDay(date1: Date, date2: Date): boolean {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  }

  // Method to navigate to Daily Insight
  navigateToDailyInsights(event: Event) {
    event.stopPropagation();

    // Reset chatId to 0
    this.chatListService.chatId = 0;

    // Set mode flags
    this.isSettingsMode = false;
    this.isWorkspaceMode = false;
    this.isDailyInsightMode = true;
    this.isNotesMode = false;
    this.isChatMode = false;
    this.isMyRequestMode = false;

    // Always keep the sidebar open for daily insights
    if (!this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }

    // Navigate to daily insights
    this.router.navigate(['/daily-insight']);
  }

  // Update navigateToSettings to handle event and toggle sidebar
  navigateToSettings(event: Event, section: string) {
    event.stopPropagation();

    // Set mode flags
    this.isSettingsMode = true;
    this.isWorkspaceMode = false;
    this.isDailyInsightMode = false;

    // Update the active tab
    this.activeAdminTab = section;

    // Make sure the sidebar is open
    if (!this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }

    // Navigate to the settings section
    this.router.navigate(['/settings', section]);
  }

  // Method to handle navigation from the chat icon in narrow sidebar
  navigateToChatPage(event: Event) {
    event.stopPropagation();

    // Switch from admin mode to chat mode if in admin mode
    this.isSettingsMode = false;
    this.isDailyInsightMode = false;
    this.isNotesMode = false;
    this.isChatMode = false;

    if (!this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }

    // Open the sidebar and select 'all' chat tab
    this.openSidebar();
    this.activeTab = 'all';

    // Reset chatId to 0 for new chat
    this.chatListService.chatId = 0;

    // Navigate based on workspace context (same logic as addNewChats)
    if (this.workspaceName) {
      // If workspaceName is available, navigate to the chat within that workspace
      this.router.navigate(['workspaces', this.workspaceName, 'chat']);
    } else {
      // If no workspace, navigate to chat
      this.router.navigate(['/chat']);
    }
  }

  // Method to handle workspace navigation and toggle sidebar
  navigateToWorkspace(event: Event) {
    event.stopPropagation();

    // Reset chatId to 0
    this.chatListService.chatId = 0;

    // Toggle/hide the sidebar
    if (this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }

    // Navigate to workspaces
    this.router.navigate(['/workspaces']);
  }

  // Daily insight mode methods moved to DailyInsightsSidebarComponent

  // Method to navigate to Notes
  navigateToNotes(event: Event) {
    event.stopPropagation();

    // Reset chatId to 0
    this.chatListService.chatId = 0;

    // Set mode flags
    this.isSettingsMode = false;
    this.isWorkspaceMode = false;
    this.isDailyInsightMode = false;
    this.isNotesMode = true;
    this.isChatMode = false;

    // Open the sidebar if it's closed, keep it open if already open
    if (!this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }

    // Navigate to notes
    this.router.navigate(['/notes']);
  }

  // Method to navigate to Agent Chat
  navigateToChat(event: Event) {
    event.stopPropagation();

    // Reset chatId to 0
    this.chatListService.chatId = 0;

    // Set mode flags
    this.isSettingsMode = false;
    this.isWorkspaceMode = false;
    this.isDailyInsightMode = false;
    this.isNotesMode = false;
    this.isChatMode = true;

    // Open the sidebar if it's closed, keep it open if already open
    if (!this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }

    // Navigate to agent chat page (without specific agent initially)
    this.router.navigate(['/chat']);
  }

  // Method to navigate to Daily Insights
  navigateToDailyInsight(event: Event) {
    event.stopPropagation();

    // Reset chatId to 0
    this.chatListService.chatId = 0;

    // Set mode flags
    this.isSettingsMode = false;
    this.isWorkspaceMode = false;
    this.isDailyInsightMode = true;
    this.isNotesMode = false;
    this.isChatMode = false;
    this.isMyRequestMode = false;

    // Always keep the sidebar open for daily insights
    if (!this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }

    // Navigate to daily insights
    this.router.navigate(['/daily-insight']);
  }

  // Method to navigate to My Request
  navigateToMyRequest(event: Event) {
    event.stopPropagation();

    // Reset chatId to 0
    this.chatListService.chatId = 0;

    // Set mode flags
    this.isSettingsMode = false;
    this.isWorkspaceMode = false;
    this.isDailyInsightMode = false;
    this.isNotesMode = false;
    this.isChatMode = false;
    this.isMyRequestMode = true;

    // Open the sidebar if it's closed, keep it open if already open
    if (!this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }

    // Navigate to my request
    this.router.navigate(['/my-request']);
  }

  // Method to load agents for the current workspace
  loadAgentsForWorkspace() {
    if (!this.workspaceName) {
      // If no workspace, load all agents
      this.agentDefinitionService.getAll().subscribe({
        next: (agents) => {
          this.workspaceAgents = agents || [];
        },
        error: (error) => {
          console.error('Error loading agents:', error);
          this.workspaceAgents = [];
        },
      });
    } else {
      // Load agents for specific workspace
      this.agentDefinitionService
        .getAllByWorkspace(this.workspaceName)
        .subscribe({
          next: (agents) => {
            this.workspaceAgents = agents || [];
          },
          error: (error) => {
            console.error('Error loading agents for workspace:', error);
            this.workspaceAgents = [];
          },
        });
    }
  }

  // refreshDailyInsights method moved to DailyInsightsSidebarComponent
  // isSettingsTabActive method moved to SettingsidebarcomponentComponent
}
