<div class="sql-connection-dialog">
  <div class="dialog-header">
    <div class="flex items-center gap-3">
      <h3 class="text-lg font-medium text-[var(--text-dark)]">SQL Connection</h3>
    </div>
  </div>

  <div class="dialog-body mt-4">
    <div class="mb-4">
      <label class="font-medium text-[var(--text-dark)] block mb-2">Connection Type</label>
      <div class="flex gap-3">
        <label class="font-medium text-[var(--text-dark)] bg-[var(--background-white)] px-3 py-2 rounded-md hover:bg-[var(--hover-blue-gray)] transition-all cursor-pointer flex items-center">
          <input
            type="radio"
            name="connectionType"
            value="existing"
            [(ngModel)]="connectionType"
            (change)="onConnectionTypeChange()"
            class="mr-2 accent-[var(--primary-purple)]"
          />
          Use Existing Connection
        </label>
        <label class="font-medium text-[var(--text-dark)] bg-[var(--background-white)] px-3 py-2 rounded-md hover:bg-[var(--hover-blue-gray)] transition-all cursor-pointer flex items-center">
          <input
            type="radio"
            name="connectionType"
            value="custom"
            [(ngModel)]="connectionType"
            (change)="onConnectionTypeChange()"
            class="mr-2 accent-[var(--primary-purple)]"
          />
          Custom Connection
        </label>
      </div>
    </div>
    <div *ngIf="connectionType === 'existing'" class="mb-4">
      <label class="font-medium text-[var(--text-dark)] block mb-2">Select Connection</label>
      <div class="flex flex-col gap-2">
        <select [(ngModel)]="selectedConnection" (change)="onConnectionSelect()"
          class="w-full p-2 bg-[var(--background-white)] text-[var(--text-dark)] border border-[var(--hover-blue-gray)] rounded-md"
          [disabled]="isLoading">
          <option *ngIf="isLoading" value="">Loading connections...</option>
          <option *ngIf="!isLoading && savedConnections.length === 0" value="">No saved connections found</option>
          <option *ngFor="let conn of savedConnections" [value]="conn">{{ conn }}</option>
        </select>

        <button (click)="testConnection()"
          class="self-end px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-[var(--secondary-purple)] transition-all"
          [disabled]="isTesting || !connectionString">
          <i *ngIf="isTesting" class="ri-loader-4-line animate-spin mr-1"></i>
          Test Connection
        </button>
      </div>
    </div>
    <div *ngIf="connectionType === 'custom'" class="mb-4">
      <label for="connectionString" class="font-medium text-[var(--text-dark)] block mb-2">Connection String</label>
      <div class="flex flex-col gap-2">
        <input type="text" id="connectionString" [(ngModel)]="connectionString"
          placeholder="Enter your connection string"
          class="w-full p-2 bg-[var(--background-white)] text-[var(--text-dark)] border border-[var(--hover-blue-gray)] rounded-md">
        <p class="text-xs text-[var(--text-medium-gray)] mt-1">Format:
          Server=myServerAddress;Database=myDataBase;User Id=myUsername;Password=myPassword;</p>

        <button (click)="testConnection()"
          class="self-end px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-[var(--secondary-purple)] transition-all"
          [disabled]="isTesting || !connectionString">
          <i *ngIf="isTesting" class="ri-loader-4-line animate-spin mr-1"></i>
          Test Connection
        </button>
      </div>
    </div>

    <div class="mb-4">
      <label for="sqlQuery" class="font-medium text-[var(--text-dark)] block mb-2">SQL Query</label>
      <textarea id="sqlQuery" [(ngModel)]="sql" rows="5"
        class="w-full p-2 bg-[var(--background-white)] text-[var(--text-dark)] border border-[var(--hover-blue-gray)] rounded-md font-mono"
        placeholder="Your SQL query will appear here"></textarea>
    </div>
  </div>
  <div class="dialog-footer mt-6 pt-4 border-t border-[var(--hover-blue-gray)] flex justify-end gap-3">
    <button (click)="onCancel()"
      class="px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-opacity-80 transition-all"
      [disabled]="isTesting">
      Cancel
    </button>
    <button (click)="onSubmit()"
      class="px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-opacity-90 transition-all"
      [disabled]="isTesting || !connectionString || !sql">
      <i *ngIf="isTesting" class="ri-loader-4-line animate-spin mr-1"></i>
      <i *ngIf="!isTesting" class="ri-send-plane-line mr-1"></i>
      Execute
    </button>
  </div>
</div>
