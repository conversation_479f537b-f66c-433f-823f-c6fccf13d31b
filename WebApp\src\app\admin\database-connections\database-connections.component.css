/* Database Connections Component Styles */

.connection-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-4;
}

.connection-header {
  @apply flex justify-between items-start mb-3;
}

.connection-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.connection-description {
  @apply text-sm text-gray-600 dark:text-gray-400 mt-1;
}

.connection-meta {
  @apply flex flex-wrap gap-2 text-sm text-gray-500 dark:text-gray-400;
}

.connection-actions {
  @apply flex gap-2;
}

.status-indicator {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.status-active {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.status-inactive {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

.schema-status {
  @apply mt-2 text-xs;
}

.schema-success {
  @apply text-green-600 dark:text-green-400;
}

.schema-error {
  @apply text-red-600 dark:text-red-400;
}

.schema-pending {
  @apply text-blue-600 dark:text-blue-400;
}

.connection-string-preview {
  @apply font-mono text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded border;
  word-break: break-all;
}

/* Modal form styles */
.ant-form-item {
  @apply mb-4;
}

.ant-form-item-label > label {
  @apply text-gray-700 dark:text-gray-300 font-medium;
}

.ant-input,
.ant-select-selector {
  @apply border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector {
  @apply border-blue-500 dark:border-blue-400;
}

/* Table customizations */
.ant-table-thead > tr > th {
  @apply bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700;
}

.ant-table-tbody > tr > td {
  @apply border-gray-200 dark:border-gray-700;
}

.ant-table-tbody > tr:hover > td {
  @apply bg-gray-50 dark:bg-gray-800;
}

/* Button customizations */
.ant-btn-primary {
  @apply bg-blue-600 border-blue-600 hover:bg-blue-700 hover:border-blue-700;
}

.ant-btn-default {
  @apply border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white;
}

.ant-btn-default:hover {
  @apply border-blue-500 dark:border-blue-400 text-blue-600 dark:text-blue-400;
}

/* Tag customizations */
.ant-tag {
  @apply border-0 rounded-full px-3 py-1;
}

/* Loading states */
.loading-overlay {
  @apply absolute inset-0 bg-white dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 flex items-center justify-center z-10;
}

/* Responsive design */
@media (max-width: 768px) {
  .connection-header {
    @apply flex-col gap-2;
  }
  
  .connection-actions {
    @apply w-full justify-start;
  }
  
  .ant-table-wrapper {
    @apply overflow-x-auto;
  }
}
