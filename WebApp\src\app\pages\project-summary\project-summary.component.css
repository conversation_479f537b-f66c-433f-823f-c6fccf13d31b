/* Project Summary Component Styles */

/* Theme-aware component styling */
:host {
  display: block;
  width: 100%;
  height: 100%;
}

/* Custom scrollbar for project list */
:host ::-webkit-scrollbar {
  width: 6px;
}

:host ::-webkit-scrollbar-track {
  background: var(--background-light-gray);
  border-radius: var(--border-radius-small);
}

:host ::-webkit-scrollbar-thumb {
  background: var(--hover-blue-gray);
  border-radius: var(--border-radius-small);
}

:host ::-webkit-scrollbar-thumb:hover {
  background: var(--primary-purple);
}

/* Line clamp utility for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Enhanced card hover effects */
.group:hover {
  transform: translateY(-2px);
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Theme-aware prose styling */
.prose-theme-light {
  color: var(--text-dark);
}

.prose-theme-light h1,
.prose-theme-light h2,
.prose-theme-light h3,
.prose-theme-light h4,
.prose-theme-light h5,
.prose-theme-light h6 {
  color: var(--text-dark);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.prose-theme-light p {
  color: var(--text-dark);
  line-height: 1.7;
  margin-bottom: 1em;
}

.prose-theme-light ul,
.prose-theme-light ol {
  color: var(--text-dark);
  padding-left: 1.5em;
}

.prose-theme-light li {
  margin-bottom: 0.5em;
}

.prose-theme-light strong {
  color: var(--text-dark);
  font-weight: 600;
}

.prose-theme-light em {
  color: var(--text-medium-gray);
  font-style: italic;
}

.prose-theme-light code {
  background-color: var(--background-light-gray);
  color: var(--primary-purple);
  padding: 0.2em 0.4em;
  border-radius: var(--border-radius-small);
  font-size: 0.875em;
  font-family: 'Courier New', monospace;
}

.prose-theme-light pre {
  background-color: var(--background-light-gray);
  border: 1px solid var(--hover-blue-gray);
  border-radius: var(--border-radius-small);
  padding: 1em;
  overflow-x: auto;
}

.prose-theme-light blockquote {
  border-left: 4px solid var(--primary-purple);
  padding-left: 1em;
  margin-left: 0;
  font-style: italic;
  color: var(--text-medium-gray);
}

.prose-theme-light a {
  color: var(--primary-purple);
  text-decoration: underline;
  transition: color var(--transition-default);
}

.prose-theme-light a:hover {
  color: var(--secondary-purple);
}

/* Dark theme prose styling */
.prose-theme-dark {
  color: var(--text-dark);
}

.prose-theme-dark h1,
.prose-theme-dark h2,
.prose-theme-dark h3,
.prose-theme-dark h4,
.prose-theme-dark h5,
.prose-theme-dark h6 {
  color: var(--text-dark);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.prose-theme-dark p {
  color: var(--text-dark);
  line-height: 1.7;
  margin-bottom: 1em;
}

.prose-theme-dark ul,
.prose-theme-dark ol {
  color: var(--text-dark);
  padding-left: 1.5em;
}

.prose-theme-dark li {
  margin-bottom: 0.5em;
}

.prose-theme-dark strong {
  color: var(--text-dark);
  font-weight: 600;
}

.prose-theme-dark em {
  color: var(--text-medium-gray);
  font-style: italic;
}

.prose-theme-dark code {
  background-color: var(--hover-blue-gray);
  color: var(--primary-purple);
  padding: 0.2em 0.4em;
  border-radius: var(--border-radius-small);
  font-size: 0.875em;
  font-family: 'Courier New', monospace;
}

.prose-theme-dark pre {
  background-color: var(--hover-blue-gray);
  border: 1px solid var(--text-medium-gray);
  border-radius: var(--border-radius-small);
  padding: 1em;
  overflow-x: auto;
}

.prose-theme-dark blockquote {
  border-left: 4px solid var(--primary-purple);
  padding-left: 1em;
  margin-left: 0;
  font-style: italic;
  color: var(--text-medium-gray);
}

.prose-theme-dark a {
  color: var(--primary-purple);
  text-decoration: underline;
  transition: color var(--transition-default);
}

.prose-theme-dark a:hover {
  color: var(--secondary-purple);
}

/* Status indicator animations */
.status-indicator {
  position: relative;
  overflow: hidden;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.group:hover .status-indicator::before {
  left: 100%;
}

/* Theme transition support */
* {
  transition: background-color var(--transition-default),
              color var(--transition-default),
              border-color var(--transition-default);
}

/* Dark theme specific adjustments */
:host-context(.dark) {
  color-scheme: dark;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border {
    border-width: 2px;
  }

  .prose-theme-light code,
  .prose-theme-dark code {
    border: 1px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .group:hover {
    transform: none;
  }

  .status-indicator::before {
    transition: none;
  }
}
