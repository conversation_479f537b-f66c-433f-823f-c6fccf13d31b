import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { RequestListComponent } from "./request-list/request-list.component";

// Interfaces for type safety
interface Request {
  id: number;
  title: string;
  description: string;
  status: 'Pending' | 'In Progress' | 'Completed' | 'Cancelled';
  createdDate: Date;
  updatedDate?: Date;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  assignedTo?: string;
  dueDate?: Date;
  tags?: string[];
  estimatedHours?: number;
  completedHours?: number;
  requestType?: string;
}

@Component({
  selector: 'app-my-request',
  standalone: true,
  imports: [CommonModule, RequestListComponent],
  templateUrl: './my-request.component.html',
  styleUrl: './my-request.component.css'
})
export class MyRequestComponent implements OnInit {
  requests: Request[] = [
    {
      id: 1,
      title: 'Data Analysis Request',
      description: 'Analysis of quarterly sales data with detailed insights and recommendations',
      status: 'In Progress',
      createdDate: new Date('2024-01-15'),
      updatedDate: new Date('2024-01-16'),
      priority: 'High',
      assignedTo: '<PERSON>',
      dueDate: new Date('2024-02-15'),
      tags: ['analytics', 'sales', 'quarterly'],
      estimatedHours: 40,
      completedHours: 15,
      requestType: 'Analysis'
    },
    {
      id: 2,
      title: 'Report Generation',
      description: 'Monthly performance report with KPIs and metrics visualization',
      status: 'Completed',
      createdDate: new Date('2024-01-10'),
      updatedDate: new Date('2024-01-25'),
      priority: 'Medium',
      assignedTo: 'Jane Smith',
      dueDate: new Date('2024-01-30'),
      tags: ['reporting', 'kpi', 'monthly'],
      estimatedHours: 20,
      completedHours: 20,
      requestType: 'Report'
    },
    {
      id: 3,
      title: 'System Integration',
      description: 'Integration with CRM system to streamline customer data flow',
      status: 'Pending',
      createdDate: new Date('2024-01-20'),
      priority: 'Low',
      dueDate: new Date('2024-03-01'),
      tags: ['integration', 'crm', 'automation'],
      estimatedHours: 60,
      completedHours: 0,
      requestType: 'Integration'
    },
    {
      id: 4,
      title: 'Bug Fix - Login Issues',
      description: 'Fix authentication problems affecting user login process',
      status: 'In Progress',
      createdDate: new Date('2024-01-22'),
      updatedDate: new Date('2024-01-23'),
      priority: 'Critical',
      assignedTo: 'Mike Johnson',
      dueDate: new Date('2024-01-25'),
      tags: ['bug', 'authentication', 'urgent'],
      estimatedHours: 8,
      completedHours: 5,
      requestType: 'Bug Fix'
    },
    {
      id: 5,
      title: 'Feature Enhancement',
      description: 'Add dark mode support and improved user interface elements',
      status: 'Pending',
      createdDate: new Date('2024-01-18'),
      priority: 'Medium',
      dueDate: new Date('2024-02-28'),
      tags: ['enhancement', 'ui', 'dark-mode'],
      estimatedHours: 35,
      completedHours: 0,
      requestType: 'Enhancement'
    }
  ];

  // Filtered and sorted requests
  filteredRequests: Request[] = [];
  currentFilter: string = 'All';
  currentSort: string = 'newest';

  constructor(private router: Router) {}

  ngOnInit(): void {
    this.applyFilters();
  }

  // Filter methods
  applyFilters(): void {
    this.filteredRequests = [...this.requests];

    // Apply status filter
    if (this.currentFilter !== 'All') {
      this.filteredRequests = this.filteredRequests.filter(
        request => request.status === this.currentFilter
      );
    }

    // Apply sorting
    this.applySorting();
  }

  filterByStatus(status: string): void {
    this.currentFilter = status;
    this.applyFilters();
  }

  // Sorting methods
  applySorting(): void {
    switch (this.currentSort) {
      case 'newest':
        this.filteredRequests.sort((a, b) =>
          new Date(b.createdDate).getTime() - new Date(a.createdDate).getTime()
        );
        break;
      case 'oldest':
        this.filteredRequests.sort((a, b) =>
          new Date(a.createdDate).getTime() - new Date(b.createdDate).getTime()
        );
        break;
      case 'priority':
        this.filteredRequests.sort((a, b) => {
          const priorityOrder = { 'Critical': 4, 'High': 3, 'Medium': 2, 'Low': 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        });
        break;
      case 'title':
        this.filteredRequests.sort((a, b) => a.title.localeCompare(b.title));
        break;
    }
  }

  sortBy(sortType: string): void {
    this.currentSort = sortType;
    this.applySorting();
  }

  // Status and priority utility methods
  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'status-completed';
      case 'in progress':
        return 'status-in-progress';
      case 'pending':
        return 'status-pending';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return 'status-default';
    }
  }

  getPriorityClass(priority: string): string {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 'priority-critical';
      case 'high':
        return 'priority-high';
      case 'medium':
        return 'priority-medium';
      case 'low':
        return 'priority-low';
      default:
        return 'priority-default';
    }
  }

  // Count methods for dashboard
  getRequestCountByStatus(status: string): number {
    if (status === 'All') return this.requests.length;
    return this.requests.filter(request => request.status === status).length;
  }

  getOverdueRequests(): Request[] {
    const today = new Date();
    return this.requests.filter(request =>
      request.dueDate &&
      new Date(request.dueDate) < today &&
      request.status !== 'Completed'
    );
  }

  getHighPriorityRequests(): Request[] {
    return this.requests.filter(request =>
      request.priority === 'High' || request.priority === 'Critical'
    );
  }

  // Progress calculation
  getProgressPercentage(request: Request): number {
    if (!request.estimatedHours || request.estimatedHours === 0) return 0;
    return Math.min(100, Math.round((request.completedHours || 0) / request.estimatedHours * 100));
  }

  // Navigation methods
  navigateToDetails(requestId: number): void {
    this.router.navigate(['/my-request/details', requestId]);
  }

  navigateToEdit(requestId: number): void {
    this.router.navigate(['/my-request/edit-request', requestId]);
  }

  navigateToCreate(): void {
    this.router.navigate(['/my-request/new-request']);
  }

  // CRUD operations
  updateRequestStatus(requestId: number, newStatus: Request['status']): void {
    const request = this.requests.find(r => r.id === requestId);
    if (request) {
      request.status = newStatus;
      request.updatedDate = new Date();

      // If marking as completed, set completed hours to estimated hours
      if (newStatus === 'Completed' && request.estimatedHours) {
        request.completedHours = request.estimatedHours;
      }

      this.applyFilters();
      console.log(`Request ${requestId} status updated to ${newStatus}`);
    }
  }

  deleteRequest(requestId: number): void {
    if (confirm('Are you sure you want to delete this request?')) {
      this.requests = this.requests.filter(r => r.id !== requestId);
      this.applyFilters();
      console.log(`Request ${requestId} deleted`);
    }
  }

  duplicateRequest(requestId: number): void {
    const originalRequest = this.requests.find(r => r.id === requestId);
    if (originalRequest) {
      const newRequest: Request = {
        ...originalRequest,
        id: Math.max(...this.requests.map(r => r.id)) + 1,
        title: `${originalRequest.title} (Copy)`,
        status: 'Pending',
        createdDate: new Date(),
        updatedDate: undefined,
        completedHours: 0
      };

      this.requests.push(newRequest);
      this.applyFilters();
      console.log(`Request duplicated with ID ${newRequest.id}`);
    }
  }

  // Search functionality
  searchRequests(searchTerm: string): void {
    if (!searchTerm.trim()) {
      this.applyFilters();
      return;
    }

    const term = searchTerm.toLowerCase();
    this.filteredRequests = this.requests.filter(request =>
      request.title.toLowerCase().includes(term) ||
      request.description.toLowerCase().includes(term) ||
      request.tags?.some(tag => tag.toLowerCase().includes(term)) ||
      request.assignedTo?.toLowerCase().includes(term)
    );
  }

  // Utility methods
  getDaysUntilDue(dueDate: Date): number {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  isOverdue(request: Request): boolean {
    if (!request.dueDate || request.status === 'Completed') return false;
    return new Date(request.dueDate) < new Date();
  }

  formatDuration(hours: number): string {
    if (hours < 8) return `${hours}h`;
    const days = Math.floor(hours / 8);
    const remainingHours = hours % 8;
    return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`;
  }

  // Export functionality
  exportToCSV(): void {
    const headers = ['ID', 'Title', 'Description', 'Status', 'Priority', 'Created Date', 'Due Date', 'Assigned To'];
    const csvContent = [
      headers.join(','),
      ...this.filteredRequests.map(request => [
        request.id,
        `"${request.title}"`,
        `"${request.description}"`,
        request.status,
        request.priority,
        request.createdDate.toISOString().split('T')[0],
        request.dueDate?.toISOString().split('T')[0] || '',
        request.assignedTo || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `requests_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);
  }
}
