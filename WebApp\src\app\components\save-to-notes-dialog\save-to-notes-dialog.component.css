/* Save to Notes Dialog - Fixed Theme Integration */

/* EditorJS container styling */
#notes-editor {
  min-height: 300px;
  padding: 1rem;
  font-size: 14px;
  line-height: 1.6;
  background-color: white;
  color: #1f2937;

}

/* Dark mode for editor */
:host-context(.dark) #notes-editor,
.dark #notes-editor {
  background-color: #374151;
  color: #f9fafb;
}

/* EditorJS blocks */
#notes-editor .ce-block__content {
  max-width: none;
  margin: 0;
  padding: 0.25rem 0;
}

#notes-editor .ce-toolbar__content {
  max-width: none;
}

#notes-editor .ce-paragraph {
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  color: #1f2937;
}

.dark #notes-editor .ce-paragraph {
  color: #f9fafb;
}

/* Headers */
#notes-editor .ce-header {
  font-weight: 600;
  margin: 0.75rem 0 0.25rem 0;
  color: #1f2937;
}

.dark #notes-editor .ce-header {
  color: #f9fafb;
}

#notes-editor .ce-header[data-level="1"] {
  font-size: 1.5rem;
}

#notes-editor .ce-header[data-level="2"] {
  font-size: 1.25rem;
}

#notes-editor .ce-header[data-level="3"] {
  font-size: 1.125rem;
}

/* Lists */
#notes-editor .ce-list {
  margin: 0.5rem 0;
}

#notes-editor .ce-list__item {
  padding: 0.125rem 0;
  line-height: 1.5;
  color: #1f2937;
}

.dark #notes-editor .ce-list__item {
  color: #f9fafb;
}

/* Code blocks */
#notes-editor .ce-code {
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 0.75rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #1f2937;
}

.dark #notes-editor .ce-code {
  background: #1f2937;
  border-color: #4b5563;
  color: #f9fafb;
}

/* Quotes */
#notes-editor .ce-quote {
  border-left: 3px solid #3b82f6;
  padding-left: 0.75rem;
  margin: 0.75rem 0;
  font-style: italic;
  background: #f3f4f6;
  padding: 0.75rem;
  border-radius: 0 6px 6px 0;
  color: #1f2937;
}

.dark #notes-editor .ce-quote {
  background: #1f2937;
  border-left-color: #60a5fa;
  color: #f9fafb;
}

/* Toolbar */
#notes-editor .ce-toolbar {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.dark #notes-editor .ce-toolbar {
  background: #374151;
  border-color: #4b5563;
}

#notes-editor .ce-toolbar__plus,
#notes-editor .ce-toolbar__settings-btn {
  color: #1f2937;
}

.dark #notes-editor .ce-toolbar__plus,
.dark #notes-editor .ce-toolbar__settings-btn {
  color: #f9fafb;
}

#notes-editor .ce-toolbar__plus:hover,
#notes-editor .ce-toolbar__settings-btn:hover {
  color: #3b82f6;
}

/* Delimiter */
#notes-editor .ce-delimiter {
  text-align: center;
  margin: 1.5rem 0;
  color: #6b7280;
}

#notes-editor .ce-delimiter::before {
  content: "***";
  font-size: 1.25rem;
  letter-spacing: 0.25rem;
}

/* Table */
#notes-editor .ce-table {
  margin: 0.75rem 0;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.dark #notes-editor .ce-table {
  border-color: #4b5563;
}

#notes-editor .ce-table td {
  border: 1px solid #e5e7eb;
  background: white;
  color: #1f2937;
}

.dark #notes-editor .ce-table td {
  border-color: #4b5563;
  background: #374151;
  color: #f9fafb;
}

/* Inline code */
#notes-editor .ce-code-inline {
  background: #f3f4f6;
  color: #3b82f6;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875em;
  border: 1px solid #e5e7eb;
}

.dark #notes-editor .ce-code-inline {
  background: #1f2937;
  border-color: #4b5563;
  color: #60a5fa;
}

/* Animation for bounce */
@keyframes bounce {

  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.3;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-bounce {
  animation: bounce 1.4s infinite ease-in-out;
}

/* Dialog fade-in animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-enter {
  animation: fadeInUp 0.3s ease-out;
}

/* EditorJS enhanced styling with better spacing */
#notes-editor .ce-block__content {
  max-width: none;
  margin: 0;
  padding: 0.25rem 0;
}

#notes-editor .ce-toolbar__content {
  max-width: none;
}

#notes-editor .ce-toolbar {
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
}

#notes-editor .ce-toolbar__plus,
#notes-editor .ce-toolbar__settings-btn {
  color: var(--text-color);
  transition: all 0.2s ease;
}

#notes-editor .ce-toolbar__plus:hover,
#notes-editor .ce-toolbar__settings-btn:hover {
  color: var(--primary-color);
  background: var(--hover-blue-gray);
}

#notes-editor .ce-block {
  padding: 0.25rem 0;
}

#notes-editor .ce-paragraph {
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  color: var(--text-color);
}

#notes-editor .ce-header {
  font-weight: 600;
  margin: 0.5rem 0 0.25rem 0;
  color: var(--text-color);
}

#notes-editor .ce-header[data-level="1"] {
  font-size: 1.5rem;
  color: var(--primary-color);
}

#notes-editor .ce-header[data-level="2"] {
  font-size: 1.25rem;
  color: var(--primary-color);
}

#notes-editor .ce-header[data-level="3"] {
  font-size: 1.125rem;
  color: var(--text-color);
}

#notes-editor .ce-code {
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.75rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: var(--text-color);
  margin: 0.5rem 0;
}

#notes-editor .ce-quote {
  border-left: 3px solid var(--primary-color);
  padding-left: 0.75rem;
  margin: 0.5rem 0;
  font-style: italic;
  background: var(--background-secondary);
  padding: 0.75rem;
  border-radius: 0 6px 6px 0;
  color: var(--text-color);
}

#notes-editor .ce-list {
  margin: 0.25rem 0;
}

#notes-editor .ce-list__item {
  padding: 0.125rem 0;
  line-height: 1.5;
  color: var(--text-color);
}

#notes-editor .ce-delimiter {
  text-align: center;
  margin: 1rem 0;
  color: var(--text-secondary);
}

#notes-editor .ce-delimiter::before {
  content: "***";
  font-size: 1.25rem;
  letter-spacing: 0.25rem;
}

#notes-editor .ce-table {
  margin: 0.5rem 0;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  overflow: hidden;
}

#notes-editor .ce-table td {
  border: 1px solid var(--border-color);
  background: var(--background-primary);
  color: var(--text-color);
  padding: 0.5rem;
}

/* Inline code styling */
#notes-editor .ce-code-inline {
  background: var(--background-secondary);
  color: var(--primary-color);
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875em;
  border: 1px solid var(--border-color);
}

/* Popover styling */
#notes-editor .ce-popover {
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

#notes-editor .ce-popover__item {
  color: var(--text-color);
  padding: 0.5rem 0.75rem;
}

#notes-editor .ce-popover__item:hover {
  background: var(--hover-blue-gray);
  color: var(--primary-color);
}

/* Focus states */
#notes-editor .ce-block--selected {
  background: var(--background-secondary);
  border-radius: 4px;
  padding: 0.125rem;
}

/* Enhanced animations */
@keyframes bounce {

  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.3;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-bounce {
  animation: bounce 1.4s infinite ease-in-out;
}

/* Focus states for inputs */
input:focus,
#notes-editor:focus-within {
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  border-color: var(--primary-color);
}

/* Button hover effects */
button:hover:not(:disabled) {
  transform: translateY(-1px);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #notes-editor {
    min-height: 250px;
    padding: 0.75rem;
    font-size: 14px;
  }
}

/* EditorJS enhanced styling with theme integration */
#notes-editor .ce-block__content {
  max-width: none;
  margin: 0;
  padding: 0.5rem 0;
}

#notes-editor .ce-toolbar__content {
  max-width: none;
}

#notes-editor .ce-toolbar {
  background: var(--background-secondary, #f9fafb) !important;
  border: 1px solid var(--border-color, #e5e7eb) !important;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#notes-editor .ce-toolbar__plus,
#notes-editor .ce-toolbar__settings-btn {
  color: var(--text-color, #111827) !important;
  transition: all 0.2s ease;
}

#notes-editor .ce-toolbar__plus:hover,
#notes-editor .ce-toolbar__settings-btn:hover {
  color: var(--primary-color, #6366f1) !important;
  background: var(--hover-blue-gray, #f3f4f6) !important;
}

#notes-editor .ce-block {
  padding: 0.5rem 0;
}

#notes-editor .ce-paragraph {
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  color: var(--text-color, #111827) !important;
}

#notes-editor .ce-header {
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  color: var(--text-color, #111827) !important;
}

#notes-editor .ce-header[data-level="1"] {
  font-size: 1.75rem;
  color: var(--primary-color, #6366f1) !important;
}

#notes-editor .ce-header[data-level="2"] {
  font-size: 1.5rem;
  color: var(--primary-color, #6366f1) !important;
}

#notes-editor .ce-header[data-level="3"] {
  font-size: 1.25rem;
  color: var(--text-color, #111827) !important;
}

#notes-editor .ce-header[data-level="4"] {
  font-size: 1.125rem;
  color: var(--text-color, #111827) !important;
}

#notes-editor .ce-code {
  background: var(--background-secondary, #f9fafb) !important;
  border: 1px solid var(--border-color, #e5e7eb) !important;
  border-radius: 8px;
  padding: 1rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: var(--text-color, #111827) !important;
  margin: 0.75rem 0;
}

#notes-editor .ce-quote {
  border-left: 4px solid var(--primary-color, #6366f1) !important;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  background: var(--background-secondary, #f9fafb) !important;
  padding: 1rem;
  border-radius: 0 8px 8px 0;
  color: var(--text-color, #111827) !important;
}

#notes-editor .ce-list {
  margin: 0.75rem 0;
}

#notes-editor .ce-list__item {
  padding: 0.25rem 0;
  line-height: 1.6;
  color: var(--text-color, #111827) !important;
}

#notes-editor .ce-delimiter {
  text-align: center;
  margin: 2rem 0;
  color: var(--text-secondary, #6b7280) !important;
}

#notes-editor .ce-delimiter::before {
  content: "***";
  font-size: 1.5rem;
  letter-spacing: 0.5rem;
}

#notes-editor .ce-table {
  margin: 0.75rem 0;
  border: 1px solid var(--border-color, #e5e7eb) !important;
  border-radius: 8px;
  overflow: hidden;
}

#notes-editor .ce-table td {
  border: 1px solid var(--border-color, #e5e7eb) !important;
  background: var(--background-primary, #ffffff) !important;
  color: var(--text-color, #111827) !important;
  padding: 0.5rem;
}

/* Inline code styling */
#notes-editor .ce-code-inline {
  background: var(--background-secondary, #f9fafb) !important;
  color: var(--primary-color, #6366f1) !important;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875em;
  border: 1px solid var(--border-color, #e5e7eb) !important;
}

/* Dark theme specific overrides */
:host-context([data-theme="dark"]) #notes-editor {
  background-color: var(--background-primary, #1f2937) !important;
  color: var(--text-color, #f9fafb) !important;
}

:host-context([data-theme="dark"]) #notes-editor .ce-toolbar {
  background: var(--background-secondary, #374151) !important;
  border-color: var(--border-color, #4b5563) !important;
}

:host-context([data-theme="dark"]) #notes-editor .ce-paragraph,
:host-context([data-theme="dark"]) #notes-editor .ce-header,
:host-context([data-theme="dark"]) #notes-editor .ce-list__item {
  color: var(--text-color, #f9fafb) !important;
}

:host-context([data-theme="dark"]) #notes-editor .ce-code {
  background: var(--background-secondary, #374151) !important;
  border-color: var(--border-color, #4b5563) !important;
  color: var(--text-color, #f9fafb) !important;
}

:host-context([data-theme="dark"]) #notes-editor .ce-quote {
  background: var(--background-secondary, #374151) !important;
  color: var(--text-color, #f9fafb) !important;
}

/* Enhanced popup styling and focus states */
#notes-editor .ce-popover {
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

#notes-editor .ce-popover__item {
  color: var(--text-color);
  padding: 0.5rem 0.75rem;
}

#notes-editor .ce-popover__item:hover {
  background: var(--hover-blue-gray);
  color: var(--primary-color);
}

#notes-editor .ce-popover__item--active {
  background: var(--primary-color);
  color: white;
}

/* Focus and selection styling */
#notes-editor .ce-block--selected {
  background: var(--background-secondary);
  border-radius: 6px;
  padding: 0.25rem;
}

#notes-editor .ce-block--focused {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  border-radius: 6px;
}

/* Placeholder styling */
#notes-editor .ce-paragraph[data-placeholder]:empty::before {
  color: var(--text-secondary);
  font-style: italic;
}

/* Animation improvements */

/* Enhanced animations */
@keyframes bounce {

  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.3;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-bounce {
  animation: bounce 1.4s infinite ease-in-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-enter {
  animation: fadeInUp 0.3s ease-out;
}

/* Enhanced focus states */
input:focus,
#notes-editor:focus-within {
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  border-color: #6366f1;
}

.dark input:focus,
.dark #notes-editor:focus-within {
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2);
  border-color: #8b5cf6;
}

/* Enhanced button effects */
button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.3);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

/* Gradient button enhancements */
.gradient-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.gradient-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.gradient-button:hover::before {
  left: 100%;
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .max-w-5xl {
    max-width: 95vw;
    margin: 0.5rem;
  }

  .p-6 {
    padding: 1rem;
  }

  #notes-editor {
    min-height: 300px;
    padding: 1rem;
    font-size: 14px;
  }

  .grid-cols-3 {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

/* Loading state improvements */
.loading-overlay {
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
}

.dark .loading-overlay {
  background: rgba(31, 41, 55, 0.8);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {

  .animate-bounce,
  button:hover:not(:disabled) {
    animation: none;
    transform: none;
  }
}

/* Custom scrollbar */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.dark .overflow-y-auto::-webkit-scrollbar-track {
  background: #374151;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}