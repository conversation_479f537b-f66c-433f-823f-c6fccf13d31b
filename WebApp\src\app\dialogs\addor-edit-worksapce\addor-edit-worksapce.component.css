/* Custom styles for nz-switch to align with styles.css variables */
.custom-switch.ant-switch {
  background-color: var(--hover-blue-gray); /* Default (unchecked) background */
  transition: var(--transition-default);
}

.custom-switch.ant-switch-checked {
  background-color: var(--primary-purple); /* Checked background */
}

.custom-switch.ant-switch:hover:not(.ant-switch-checked) {
  background-color: var(--secondary-purple); /* Hover state for unchecked */
}

.custom-switch.ant-switch-checked:hover {
  background-color: color-mix(in srgb, var(--primary-purple) 90%, var(--secondary-purple)); /* Slightly lighter on hover when checked */
}

.custom-switch.ant-switch .ant-switch-handle::before {
  background-color: var(--background-white); /* Handle color */
  box-shadow: var(--box-shadow);
}

.custom-switch.ant-switch-checked .ant-switch-handle::before {
  background-color: var(--background-white); /* Handle color when checked */
}

/* Dark mode support */
:host-context(.dark) .custom-switch.ant-switch {
  background-color: var(--hover-blue-gray);
}

:host-context(.dark) .custom-switch.ant-switch-checked {
  background-color: var(--primary-purple);
}

:host-context(.dark) .custom-switch.ant-switch:hover:not(.ant-switch-checked) {
  background-color: var(--secondary-purple);
}

:host-context(.dark) .custom-switch.ant-switch-checked:hover {
  background-color: color-mix(in srgb, var(--primary-purple) 90%, var(--secondary-purple));
}

:host-context(.dark) .custom-switch.ant-switch .ant-switch-handle::before {
  background-color: var(--background-white);
}
