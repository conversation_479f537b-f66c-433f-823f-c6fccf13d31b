<!-- Daily Insight Content -->
<div class="p-4 overflow-y-auto max-h-[calc(100vh-74px)]">
  <!-- Header Section -->
  <div class="flex items-center justify-between mb-4">
    <div class="flex items-center gap-1.5">
      <span
        class="w-1.5 h-5 bg-gradient-to-b from-[var(--primary-purple)] to-[var(--secondary-purple)] rounded-full"></span>
      <div class="flex flex-col">
        <span class="font-bold text-[var(--text-dark)] text-lg">Daily Insights</span>
        <span class="text-xs text-[var(--text-medium-gray)]">Welcome to your AI Hub</span>
      </div>
    </div>

    <!-- Refresh Button -->
    <!-- <div class="tooltip-container">
      <button
        class="w-7 h-7 rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-all duration-300 flex justify-center items-center outline-none border-none bg-transparent text-lg cursor-pointer"
        (click)="refreshDailyInsights()">
        <i
          class="ri-refresh-line text-[var(--text-medium-gray)] hover:text-[var(--primary-purple)] transition-colors duration-300"></i>
      </button>
      <span class="custom-tooltip refresh-tooltip">Refresh</span>
    </div> -->
  </div>
  <div class="space-y-6">
    <!-- Quick Actions Section -->
    <div class="space-y-2 space-x-2">
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center gap-2">
          <i class="ri-flashlight-line text-[var(--primary-purple)] text-lg"></i>
          <h3 class="text-base font-semibold text-[var(--text-dark)]">Quick Actions</h3>
        </div>
        <div class="h-px flex-grow bg-[var(--hover-blue-gray)] ml-2 opacity-50"></div>
      </div>

      <!-- Action Cards with enhanced styling -->
      <div class="flex flex-col gap-2">
        <!-- New Chat Card -->
        <div
          class="flex items-center gap-3 py-2 px-3 cursor-pointer rounded-md transition-all relative group"
          [ngClass]="{
            'bg-[var(--primary-purple)] text-white': isQuickActionActive('new-chat'),
            'hover:bg-[var(--primary-purple)] hover:text-white': !isQuickActionActive('new-chat')
          }"
          (click)="goToChat()">
          <i class="ri-chat-new-line text-xl"></i>
          <span class="font-medium block">New Chat</span>
        </div>
        <div
          class="flex items-center gap-3 py-2 px-3 cursor-pointer rounded-md transition-all relative group"
          [ngClass]="{
            'bg-[var(--primary-purple)] text-white': isQuickActionActive('workspaces'),
            'hover:bg-[var(--primary-purple)] hover:text-white': !isQuickActionActive('workspaces')
          }"
          (click)="goToWorkspace()">
          <i class="ri-folder-add-line text-xl"></i>
          <span class="font-medium block">Workspaces</span>
        </div>
        <div
          class="flex items-center gap-3 py-2 px-3 cursor-pointer rounded-md transition-all relative group"
          [ngClass]="{
            'bg-[var(--primary-purple)] text-white': isQuickActionActive('project-summary'),
            'hover:bg-[var(--primary-purple)] hover:text-white': !isQuickActionActive('project-summary')
          }"
          (click)="goToProjectSummary()">
          <i class="ri-file-chart-line text-xl"></i>
          <span class="font-medium block">Project Summery</span>
        </div>
        <div
          class="flex items-center gap-3 py-2 px-3 cursor-pointer rounded-md transition-all relative group"
          [ngClass]="{
            'bg-[var(--primary-purple)] text-white': isQuickActionActive('task-management'),
            'hover:bg-[var(--primary-purple)] hover:text-white': !isQuickActionActive('task-management')
          }"
          (click)="goToTaskManagement()">
          <i class="ri-task-line text-xl"></i>
          <span class="font-medium block">Task Management</span>
        </div>
      </div>
    </div>



    <!-- Last Clicked Item Display -->
    <!-- <div *ngIf="lastClickedItem"
      class="mt-6 p-4 bg-[var(--background-light-gray)] rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)]">
      <div class="flex items-center gap-2 mb-2">
        <i class="ri-time-line text-[var(--primary-purple)]"></i>
        <span class="text-sm font-medium text-[var(--text-dark)]">Last Action</span>
      </div>
      <div class="text-xs text-[var(--text-medium-gray)]">
        <div class="font-medium text-[var(--text-dark)]">{{ lastClickedItem }}</div>
        <div class="mt-1">{{ lastClickedTime }}</div>
      </div>
    </div> -->
  </div>
</div>
