import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ProjectMemoryServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { Router } from '@angular/router';

@Component({
  selector: 'app-add-or-edit-memory',
  standalone: true,
  imports: [FormsModule, CommonModule],
  templateUrl: './add-or-edit-memory.component.html',
  styleUrl: './add-or-edit-memory.component.css',
})
export class AddOrEditMemoryComponent {
  projectMemory: any = {
    projectCategory: '',
    projectDescription: '',
    status: '',
    workspace: '',
  };
  workspaceName: any;
  constructor(
    // private modelDetailsService: ModelDetailsServiceProxy,

    @Inject(NZ_MODAL_DATA)
    public data: { isUpdating: boolean; project_memo: any },
    private modelRef: NzModalRef,
    private projectMemoryService: ProjectMemoryServiceProxy,
    private router:Router
  ) {
    if (this.data.isUpdating) {
      this.projectMemory = this.data.project_memo;
    } else {
      this.projectMemory = {
        projectCategory: '',
        projectDescription: '',
        status: '',
        workspace: '',
      };
    }
    console.log(this.data);
  }
  ngOnInit(): void {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //Add 'implements OnInit' to the class.
    let router = this.router.url.split('/');
    this.workspaceName = router[3];
    this.projectMemory.workspace = this.workspaceName;
  }
  addProjectMemory() {
    this.projectMemoryService
      .saveProjectMemory(this.projectMemory)
      .subscribe((result) => {
        console.log(result);
        this.modelRef.close({ ...result });
      });
  }
  closeDialog() {
    this.modelRef.close();
  }
}
