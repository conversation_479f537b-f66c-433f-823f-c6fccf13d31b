import { Component, OnInit } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  FileServiceProxy,
  ProjectDto,
  ProjectsServiceProxy,
  FileParameter,
  ProjectViewDto,
  CommentServiceProxy,
  CommentDto,
} from '../../../shared/service-proxies/service-proxies';
import { getRemoteServiceBaseUrl } from '../../app.config';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { AuthService } from '../../../shared/services/auth.service';

@Component({
  selector: 'app-add-request',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './add-request.component.html',
  providers: [FileServiceProxy, ProjectsServiceProxy, CommentServiceProxy],
  styleUrls: ['./add-request.component.css'],
})
export class AddRequestComponent implements OnInit {
  fileInputs: { name?: string; url?: string }[] = [{}];
  allowedFileTypes: string = '.jpg, .jpeg, .png, .pdf, .txt, .docx';
  invalidFileType: boolean = false;
  formData = new ProjectDto();
  projectViewData = new ProjectViewDto();
  requestId: number | undefined;
  baseUrl: string = getRemoteServiceBaseUrl();
  isNotPending: boolean = true;
  showReply: boolean = false;
  newComment: string = '';
  comments: any[] = [];
  isEditMode: boolean = false;
  showFileInputs: boolean = false;
  attachedFiles: File[] = [];
  deletedFiles: string[] = [];
  filteredFiles: string[] = [];

  constructor(
    private _fileService: FileServiceProxy,
    private _projectService: ProjectsServiceProxy,
    private router: Router,
    private route: ActivatedRoute,
    private _commentService: CommentServiceProxy,
    private authService: AuthService,
    private sanitizer: DomSanitizer,
    private location: Location
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.requestId = +params['id'];
      if (this.requestId) {
        this.loadProjectData();
      } else {
        // New project
        this.isEditMode = true;
      }
    });
  }

  loadProjectData(): void {
    if (this.requestId) {
      this._projectService.getById(this.requestId).subscribe((res) => {
        if (res !== undefined) {
          this.formData = res;
          this.isEditMode = res.status?.toLowerCase() === 'open';
          this.getFiles();
          if (res.status !== 'Open') {
            this.projectViewData = res;
            this.isNotPending = false;
            this.loadCommentData();
          }
        }
      });
    }
  }

  addFileInput() {
    this.showFileInputs = true;
    if (!this.fileInputs.some((input) => !input.name)) {
      this.fileInputs.push({});
    }
  }

  removeFileInput(index: number) {
    this.fileInputs.splice(index, 1);
    if (this.fileInputs.length === 0) {
      this.showFileInputs = false;
    }
  }

  triggerFileInput() {
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    fileInput?.click();
  }

  onFileChange(event: any) {
    const files = event.target.files;
    if (files && files.length > 0) {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        this.attachedFiles.push(file);
        this.fileInputs.push({ name: file.name });
      }
    }
    event.target.value = '';
  }

  removeFile(index: number) {
    const fileName = this.attachedFiles[index]?.name;
    if (fileName) {
      if (confirm('Are you sure you want to delete this item?')) {
        this.deletedFiles.push(fileName);
        this.attachedFiles.splice(index, 1);
        this.fileInputs = this.fileInputs.filter(f => f.name !== fileName);
        this.getFiles();
      }
    }
  }

  getFiles() {
    this.filteredFiles =
      this.formData.filesName?.filter(
        (file: string) => !this.deletedFiles.includes(file)
      ) || [];
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  submitRequest() {
    const fileParams: FileParameter[] = this.attachedFiles.map((file) => ({
      data: file,
      fileName: file.name,
    }));
    if (fileParams.length > 0) {
      this._fileService.upload("",fileParams).subscribe((uploadResponse) => {
        let fileNames: string[] = [];
        if (uploadResponse.message) {
          fileNames = uploadResponse.message.split(', ');
        }
        this.handleProjectSave(fileNames);
      });
    } else {
      this.handleProjectSave([]);
    }
  }

  handleProjectSave(fileNames: string[]) {
    this.formData.filesName = this.formData.filesName || [];
    const uniqueFileNames = [
      ...new Set(this.formData.filesName.concat(fileNames)),
    ];
    this.formData.filesName = uniqueFileNames.filter(
      (file) => !this.deletedFiles.includes(file)
    );
    if (this.deletedFiles.length) {
      this._fileService.deleteFile(this.deletedFiles).subscribe(() => {
        this.saveProject();
      });
    } else {
      this.saveProject();
    }
  }

  saveProject() {
    this._projectService.createOrUpdate(this.formData).subscribe((projectResponse) => {
      if (projectResponse) {
        alert('Your request has been saved successfully.');
        this.goBack();
      }
    });
  }

  // Comments
  addComment() {
    if (!this.newComment.trim()) return;
    const comment = new CommentDto();
    comment.projectId = this.formData.id;
    comment.message = this.newComment.trim();
    this._commentService.addComment(comment).subscribe(() => {
      this.newComment = '';
      this.loadCommentData();
    });
  }

  loadCommentData() {
    this._commentService.getAllByProjectId(this.requestId).subscribe((res) => {
      this.comments = res || [];
      this.sortMessagesByDate();
    });
  }

  sortMessagesByDate() {
    this.comments.sort((a, b) => {
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    });
  }

  goBack() {
    this.router.navigate(['/my-request']);
  }
}
