import { Component, Input, Output, EventEmitter, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RemoveProviderPrefixPipe } from '../../../../shared/pipes/remove-provider-prefix.pipe';
import { ThemeService } from '../../../../shared/services/theam.service';
import { Router, ActivatedRoute } from '@angular/router';
import { AgentDefinition, AgentDefinitionServiceProxy } from '../../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-agent-sidebar',
  standalone: true,
  imports: [
    CommonModule,
    RemoveProviderPrefixPipe
  ],
  templateUrl: './agent-sidebar.component.html',
  styleUrls: ['./agent-sidebar.component.css']
})
export class AgentSidebarComponent {
  // Inject ThemeService
  themeService = inject(ThemeService);

  // Input properties
  @Input() isAgentSidebarOpen: boolean = false;
  @Input() agentSidebarTitle: string = 'Available Agents';
  workspaceAgents: any[] = [];
  @Output() onClose = new EventEmitter<void>();
  @Output() onSelectAgent = new EventEmitter<any>();

  constructor(private router: Router, private route: ActivatedRoute, private _agentDefinition: AgentDefinitionServiceProxy) { }

  ngOnInit(): void {
    this.route.url.subscribe(() => {
      const segments = this.router.url.split('/');
      const workspaceIndex = segments.indexOf('workspace');
      if (workspaceIndex !== -1 && segments.length > workspaceIndex + 1) {
        const workspaceName = segments[workspaceIndex + 1];
        const decodedWorkspaceName = decodeURIComponent(workspaceName);
        this._agentDefinition.getAllByWorkspace(decodedWorkspaceName).subscribe((res) => {
          this.workspaceAgents = res
        })
      }
    });
  }
  get showSidebar(): boolean {
    return this.isAgentSidebarOpen;
  }

  /**
   * Handles the sidebar close event
   */
  closeSidebar(): void {
    this.onClose.emit();
  }

  /**
   * Alternative method name to match source-references component
   */
  onCloseSidebar(): void {
    this.closeSidebar();
  }

  /**
   * Handles agent selection
   * @param agent The selected agent
   */
  selectAgent(agent: any): void {
    if (!agent) return;
    this.onSelectAgent.emit(agent);
  }
}
