<!-- Request Sidebar Content -->
<div class="flex-1 flex flex-col h-full border-r" [ngClass]="{
       'bg-[#343541] border-[#3a3a45]': themeService.isDarkMode(),
       '  border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
     }">
  <!-- Header -->
  <div
    class="px-[var(--padding-small)] py-[var(--padding-small)] border-b flex justify-between flex-col gap-2 w-[16rem]"
    [ngClass]="{'border-[#3a3a45]': themeService.isDarkMode(), 'border-[var(--hover-blue-gray)]': !themeService.isDarkMode()}">
    <div class="flex items-center justify-between w-full">
      <div class="flex items-center gap-2">
        <i class="ri-file-list-3-line text-xl"
          [ngClass]="{'text-[#10A37F]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}"></i>
        <span class="font-bold text-lg"
          [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">My
          Requests</span>
      </div>
    </div>
  </div>

  <!-- Request Navigation -->
  <div class="flex-1 flex flex-col overflow-y-auto">
    <div class="space-y-1 *:no-underline transition-[var(--transition-default)] p-4">
      <div class="mb-4 flex items-center gap-2">
        <span class="text-xs uppercase font-medium"
          [ngClass]="{'text-[#ACACBE]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}">Requests</span>
        <div class="flex-1 h-[1px] opacity-70"
          [ngClass]="{'bg-[#3a3a45]': themeService.isDarkMode(), 'bg-[var(--hover-blue-gray)]': !themeService.isDarkMode()}">
        </div>
      </div>

      <!-- Request List Button -->
      <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isRequestTabActive('request-list') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isRequestTabActive('request-list') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToRequestTab($event, 'request-list')">
        <!-- Active indicator bar -->
        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isRequestTabActive('request-list'),
            'opacity-0': !isRequestTabActive('request-list'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-list-check-2 text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isRequestTabActive('request-list') && !themeService.isDarkMode(),
            'text-white': isRequestTabActive('request-list') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isRequestTabActive('request-list') && themeService.isDarkMode(),
            '': !isRequestTabActive('request-list') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isRequestTabActive('request-list') && !themeService.isDarkMode(),
            'text-white': isRequestTabActive('request-list') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isRequestTabActive('request-list') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isRequestTabActive('request-list') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">Request List</span>
      </div>

      <!-- All Request Button -->
      <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isRequestTabActive('all-request') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isRequestTabActive('all-request') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToRequestTab($event, 'all-request')">

        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isRequestTabActive('all-request'),
            'opacity-0': !isRequestTabActive('all-request'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-add-circle-line text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isRequestTabActive('all-request') && !themeService.isDarkMode(),
            'text-white': isRequestTabActive('all-request') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isRequestTabActive('all-request') && themeService.isDarkMode(),
            '': !isRequestTabActive('all-request') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isRequestTabActive('all-request') && !themeService.isDarkMode(),
            'text-white': isRequestTabActive('all-request') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isRequestTabActive('all-request') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isRequestTabActive('all-request') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">All Request</span>
      </div>

      <!-- My Submissions Button -->
      <!-- <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isRequestTabActive('my-submissions') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isRequestTabActive('my-submissions') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToRequestTab($event, 'my-submissions')"> -->
      <!-- Active indicator bar -->
      <!-- <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isRequestTabActive('my-submissions'),
            'opacity-0': !isRequestTabActive('my-submissions'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-send-plane-line text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isRequestTabActive('my-submissions') && !themeService.isDarkMode(),
            'text-white': isRequestTabActive('my-submissions') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isRequestTabActive('my-submissions') && themeService.isDarkMode(),
            'text-[var(--primary-purple)]': !isRequestTabActive('my-submissions') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isRequestTabActive('my-submissions') && !themeService.isDarkMode(),
            'text-white': isRequestTabActive('my-submissions') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isRequestTabActive('my-submissions') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isRequestTabActive('my-submissions') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">My Submissions</span>
      </div> -->

    </div>
  </div>
</div>
