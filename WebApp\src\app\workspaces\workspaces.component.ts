import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { FormsModule } from '@angular/forms';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import {
  ModelDetailsServiceProxy,
  UserAccountServiceProxy,
  WorkspaceServiceProxy,
} from '../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../shared/service-proxies/service-proxy.module';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { WorkspaceUsersDialogComponent } from '../dialogs/workspace-users-dialog/workspace-users-dialog.component';
import { ThemeService } from '../../shared/services/theam.service';
import { AddorEditWorksapceComponent } from '../dialogs/addor-edit-worksapce/addor-edit-worksapce.component';
import { AuthService } from '../../shared/services/auth.service';

@Component({
  selector: 'app-workspaces',
  standalone: true,
  imports: [
    CommonModule,
    NzInputModule,
    NzIconModule,
    NzAutocompleteModule,
    FormsModule,
    NzBreadCrumbModule,
    RouterLink,
    ServiceProxyModule,
    NzSwitchModule,
  ],
  templateUrl: './workspaces.component.html',
  styleUrl: './workspaces.component.css',
  providers: [NzModalService],
})
export class WorkspacesComponent {
  showDialog = false;
  router = inject(Router);
  workspace: any = {
    title: '',
    description: '',
    systemInformation: '',
    modelName: '',
    isDefault: false,
    isProjectManagement: false,
  };
  isUpdating = false;
  workspaceList: any[] = [];
  users: any[] = [];
  themeService = inject(ThemeService);

  constructor(
    private modelDetailsService: ModelDetailsServiceProxy,
    private worksapceService: WorkspaceServiceProxy,
    private userAccountService: UserAccountServiceProxy,
    private modalService: NzModalService,
    public authService: AuthService
  ) { }

  ngOnInit(): void {
    this.loadAllWorkspaces();
    this.loadModels();
    // this.loadAllUsers();
  }
  get isAdmin() {
    return this.authService.isAdmin();
  }

  loadAllWorkspaces() {
    var isAdmin = this.authService.isAdmin();
    if (!isAdmin) {
      // For non-admin users, fetch only their workspaces
      this.worksapceService.getWorkspacesByUserEmail().subscribe((response: any) => {
        this.workspaceList = response;
        console.log('User-specific workspaces loaded:', this.workspaceList);
      });
      return;
    }
    // For admin users, fetch all workspaces
    this.worksapceService.getAll().subscribe((response: any) => {
      this.workspaceList = response;
      console.log('Workspaces loaded:', this.workspaceList);
    });
  }

  // loadAllUsers() {
  //   this.userAccountService.getAll().subscribe((users: any) => {
  //     this.users = users;
  //     console.log(this.users);
  //   });
  // }

  toggleDialog() {
    this.clearWorkspace();
    this.showDialog = !this.showDialog;
  }

  viewWorkspace(workspace: any, event: Event) {
    event.preventDefault();
    this.router.navigate(['/chat/workspace', workspace.title]);
  }

  addWorkspace() {
    let modalRef = this.modalService.create({
      nzTitle: 'Add Workspace',
      nzContent: AddorEditWorksapceComponent,
      nzData: {
        title: 'Add Workspace',
        isUpdating: false,
        workspace: null,
      },

      nzWidth: '600px',
      nzFooter: null,
    });
    modalRef.afterClose.subscribe((result: any | undefined) => {
      console.log(result);

      if (result) {
        this.workspaceList.push(result);
      }
    });
  }

  modelSearchQuery: string = '';
  models: any[] = [];
  filteredModels = [...this.models];

  onChange(event: Event) {
    const query = (event.target as HTMLInputElement).value.toLowerCase();
    this.filteredModels = this.models.filter((option: any) =>
      option.modelName.toLowerCase().includes(query)
    );
  }

  loadModels() {
    this.modelDetailsService.getAllActiveModel().subscribe((response: any) => {
      this.models = response;
      this.filteredModels = this.models;
    });
  }

  updateModel(selectedModel: string) {
    this.modelSearchQuery = selectedModel;
  }

  saveWorkspace() {
    this.workspace.modelName = this.modelSearchQuery;
    console.log(this.workspace.systemInformation);

    this.worksapceService
      .createOrUpdate(this.workspace)
      .subscribe((response: any) => {
        if (response) {
          this.workspaceList.push(response);
          this.clearWorkspace();
          this.showDialog = false;
          console.log('Workspace saved');
        }
      });
  }

  editWorkspace(workspace: any, event: Event) {
    event.preventDefault();
    this.workspace = { ...workspace };

    this.modelSearchQuery = workspace.modelName;
    this.modalService.create({
      nzTitle: 'Update Workspace',
      nzContent: AddorEditWorksapceComponent,
      nzData: {
        title: 'Update Workspace',
        isUpdating: true,
        workspace: workspace,
      },

      nzWidth: '600px',
      nzFooter: null,
    });
  }

  updateWorkspace() {
    this.workspace.modelName = this.modelSearchQuery;

    this.worksapceService
      .createOrUpdate(this.workspace)
      .subscribe((response: any) => {
        if (response) {
          this.isUpdating = false;
          this.loadAllWorkspaces();
          this.clearWorkspace();
          this.showDialog = false;
          console.log('Workspace updated');
        }
      });
  }

  deleteWorkspace(workspace: any, event: Event) {
    event.preventDefault();
    this.worksapceService.delete(workspace.id).subscribe((response: any) => {
      if (response) {
        this.workspaceList = this.workspaceList.filter(
          (w: any) => w.id !== workspace.id
        );
        console.log('Workspace deleted');
      }
    });
  }

  openDialog(id: number): void {
    this.modalService.create({
      nzTitle: 'User List',
      nzContent: WorkspaceUsersDialogComponent,
      nzData: {
        id: id,
      },
      nzWidth: '800px',
      nzFooter: null,
    });
  }

  clearWorkspace() {
    this.workspace = {
      title: '',
      description: '',
      systemInformation: '',
      modelName: '',
      isDefault: false,
      isProjectManagement: false,
    };
    this.modelSearchQuery = '';
    this.isUpdating = false;
  }
}
