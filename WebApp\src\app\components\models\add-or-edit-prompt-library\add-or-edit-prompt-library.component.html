<div class=" p-4 pb-6 bg-[var(--background-white)] z-10 shadow-[var(--box-shadow)] max-h-[70vh] overflow-auto">
  <div class="flex items-center justify-between ">
    <div>
       <i class="ri-file-list-3-line text-[var(--primary-purple)] text-xl"></i>
       <span class="text-[var(--primary-purple)] text-xl font-bold"> {{promptData.id? 'Update Prompt' : ' Add Prompt'}} </span>
    </div>
    <button (click)="onCancel()"
            class="px-3 p-1 bg-[var(--primary-purple)] rounded-md border-none  hover:bg-[var(--secondary-purple)] text-white transition-[var(--transition-default)]">
      <span>X</span>
    </button>
  </div>
  <form #promptForm="ngForm" (ngSubmit)="onSubmit()" class="space-y-6">
    <!-- Short Message Field -->
    <div class="mb-4">
      <label class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
        <i class="ri-quote-text mr-2 text-[var(--primary-purple)]"></i> Short Message
      </label>
      <input type="text" name="shortMessage" [(ngModel)]="promptData.shortMessage" required #shortMessage="ngModel"
             class="w-full p-3 rounded-md border border-[var(--hover-blue-gray)] focus:border-none focus:outline-[var(--primary-purple)] bg-[var(--background-light-gray)] outline-none transition-[var(--transition-default)] text-[var(--text-dark)]"
             placeholder="Enter a short name or description">
      <div *ngIf="shortMessage.invalid && shortMessage.touched" class="text-red-500 text-xs mt-1 flex items-center">
        <i class="ri-error-warning-line mr-1"></i>Please enter a short message
      </div>
    </div>

    <!-- Prompt Field -->
    <div class="mb-4">
      <label class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
        <i class="ri-file-text-line mr-2 text-[var(--primary-purple)]"></i>Prompt
      </label>
      <textarea name="prompt" [(ngModel)]="promptData.prompt" required #prompt="ngModel"
                class="w-full p-3 rounded-md border border-[var(--hover-blue-gray)] focus:border-none focus:outline-[var(--primary-purple)] text-[var(--text-dark)] bg-[var(--background-light-gray)] outline-none transition-[var(--transition-default)]"
                placeholder="Enter your prompt"></textarea>
      <div *ngIf="prompt.invalid && prompt.touched" class="text-red-500 text-xs mt-1 flex items-center">
        <i class="ri-error-warning-line mr-1"></i>Please enter a prompt
      </div>
    </div>

    <!-- Workspace Dropdown -->
    <div class="mb-4">
      <label class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
        <i class="ri-folder-line mr-2 text-[var(--primary-purple)]"></i>Workspace
      </label>
      <div class="relative">
        <button #workspaceDropdownButton (click)="toggleWorkspaceDropdown()"
                class="w-full p-3 rounded-md border border-[var(--hover-blue-gray)] bg-[var(--background-light-gray)] text-[var(--text-dark)] text-left
                       focus:outline-none focus:ring-1 focus:ring-[var(--primary-purple)] hover:bg-[var(--primary-purple)] hover:text-[var(--background-white)] transition-[var(--transition-default)] flex items-center justify-between">
          <span>{{ promptData.workspaceName || 'Select a workspace' }}</span>
          <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)]"></i>
        </button>
        <div #workspaceDropdownMenu *ngIf="isWorkspaceDropdownOpen"
             class="absolute w-full h-60 overflow-auto bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] shadow-[var(--box-shadow)] z-50 hover:border-[var(--primary-purple)] mb-10"
             [ngClass]="{'bottom-full mb-[var(--margin-small)]': isWorkspaceDropdownAbove, 'top-full mt-[var(--margin-small)]': !isWorkspaceDropdownAbove}">
          <div (click)="selectWorkspace('')"
               class="px-3 py-2 text-sm cursor-pointer text-left bg-[var(--background-light-gray)] text-[var(--text-dark)] hover:bg-[var(--primary-purple)] hover:text-[var(--background-white)] transition-[var(--transition-default)] ">
            Select a workspace
          </div>
          <div *ngFor="let workspace of workspaces" (click)="selectWorkspace(workspace.title)"
               class="px-3 py-2 text-sm cursor-pointer text-left bg-[var(--background-light-gray)] text-[var(--text-dark)] hover:bg-[var(--primary-purple)] hover:text-[var(--background-white)] transition-[var(--transition-default)]">
            {{ workspace.title }}
          </div>
        </div>
      </div>
    </div>

    <!-- Agent Dropdown -->
    <div class="mb-4">
      <label class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
        <i class="ri-robot-line mr-2 text-[var(--primary-purple)]"></i>Agent
      </label>
      <div class="relative">
        <button #agentDropdownButton (click)="toggleAgentDropdown()"
                class="w-full p-3 rounded-md border border-[var(--hover-blue-gray)] bg-[var(--background-light-gray)] text-[var(--text-dark)] text-left
                       focus:outline-none focus:ring-1 focus:ring-[var(--primary-purple)] hover:bg-[var(--primary-purple)] hover:text-[var(--background-white)] transition-[var(--transition-default)] flex items-center justify-between">
          <span>{{ promptData.agentName || 'Select an agent' }}</span>
          <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)]"></i>
        </button>
        <div #agentDropdownMenu *ngIf="isAgentDropdownOpen"
             class="absolute w-full bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] shadow-[var(--box-shadow)] z-50 hover:border-[var(--primary-purple)]  h-40 overflow-auto"
             [ngClass]="{'bottom-full mb-[var(--margin-small)]': isAgentDropdownAbove, 'top-full mt-[var(--margin-small)]': !isAgentDropdownAbove}">
          <div (click)="selectAgent('')"
               class="px-3 py-2 text-sm cursor-pointer text-left bg-[var(--background-light-gray)] text-[var(--text-dark)] hover:bg-[var(--primary-purple)] hover:text-[var(--background-white)] transition-[var(--transition-default)]">
            Select an agent
          </div>
          <div *ngFor="let agent of agents" (click)="selectAgent(agent.agentName)"
               class="px-3 py-2 text-sm cursor-pointer text-left bg-[var(--background-light-gray)] text-[var(--text-dark)] hover:bg-[var(--primary-purple)] hover:text-[var(--background-white)] transition-[var(--transition-default)]">
            {{ agent.agentName }}
          </div>
        </div>
      </div>
    </div>

    <div class="mb-4 p-3 bg-[var(--background-light-gray)] border dark:bg-[var(--background-light-gray)] border-[var(--hover-blue-gray)] rounded-md">
      <p class="text-sm text-[var(--text-medium-gray)] dark:text-[var(--text-medium-gray)]">
        <i class="ri-information-line mr-1"></i>
        Select either a workspace or an agent for this prompt. You cannot select both.
      </p>
    </div>

    <!-- Buttons -->
    <div class="flex justify-end space-x-4 mb-4 pb-5 pt-4 border-t border-[var(--hover-blue-gray)]">
      <button type="button" (click)="onCancel()"
              class="bg-[var(--hover-blue-gray)] text-[var(--text-dark)] px-4 py-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] transition-[var(--transition-default)] outline-none border-none cursor-pointer hover:shadow-md flex items-center gap-2">
        <i class="ri-close-line"></i>
        Cancel
      </button>
      <button type="submit" [disabled]="!promptForm.form.valid"
              class="bg-[var(--primary-purple)] text-[var(--background-white)] px-4 py-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] transition-[var(--transition-default)] outline-none border-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 hover:shadow-md">
        <i class="ri-save-line"></i>
        {{ promptData.id ? 'Update' : 'Add' }}
      </button>
    </div>
  </form>
</div>
