{"version": 3, "sources": ["../../../../../node_modules/@angular/cdk/fesm2022/cdk.mjs", "../../../../../node_modules/@angular/material/fesm2022/core.mjs"], "sourcesContent": ["import { Version } from '@angular/core';\n\n/** Current version of the Angular Component Development Kit. */\nconst VERSION = new Version('17.3.10');\n\nexport { VERSION };\n\n", "import * as i0 from '@angular/core';\nimport { Version, InjectionToken, inject, NgModule, Optional, Inject, LOCALE_ID, Injectable, Directive, ANIMATION_MODULE_TYPE, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, booleanAttribute, EventEmitter, Output, ViewChild, NgZone, ElementRef } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { VERSION as VERSION$1 } from '@angular/cdk';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { Platform, _isTestEnvironment, normalizePassiveListenerOptions, _getEventTarget } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceNumberProperty, coerceElement } from '@angular/cdk/coercion';\nimport { Observable, Subject } from 'rxjs';\nimport { startWith } from 'rxjs/operators';\nimport { ENTER, SPACE, hasModifierKey } from '@angular/cdk/keycodes';\n\n/** Current version of Angular Material. */\nconst _c0 = [\"*\", [[\"mat-option\"], [\"ng-container\"]]];\nconst _c1 = [\"*\", \"mat-option, ng-container\"];\nconst _c2 = [\"text\"];\nconst _c3 = [[[\"mat-icon\"]], \"*\"];\nconst _c4 = [\"mat-icon\", \"*\"];\nfunction MatOption_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled)(\"state\", ctx_r0.selected ? \"checked\" : \"unchecked\");\n  }\n}\nfunction MatOption_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled);\n  }\n}\nfunction MatOption_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r0.group.label, \")\");\n  }\n}\nconst _c5 = [\"mat-internal-form-field\", \"\"];\nconst _c6 = [\"*\"];\nconst VERSION = new Version('17.3.10');\n\n/** @docs-private */\nclass AnimationCurves {\n  static {\n    this.STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\n  }\n  static {\n    this.DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\n  }\n  static {\n    this.ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\n  }\n  static {\n    this.SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n  }\n}\n/** @docs-private */\nclass AnimationDurations {\n  static {\n    this.COMPLEX = '375ms';\n  }\n  static {\n    this.ENTERING = '225ms';\n  }\n  static {\n    this.EXITING = '195ms';\n  }\n}\n\n/** @docs-private */\nfunction MATERIAL_SANITY_CHECKS_FACTORY() {\n  return true;\n}\n/** Injection token that configures whether the Material sanity checks are enabled. */\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n  providedIn: 'root',\n  factory: MATERIAL_SANITY_CHECKS_FACTORY\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n */\nclass MatCommonModule {\n  constructor(highContrastModeDetector, _sanityChecks, _document) {\n    this._sanityChecks = _sanityChecks;\n    this._document = _document;\n    /** Whether we've done the global sanity checks (e.g. a theme is loaded, there is a doctype). */\n    this._hasDoneGlobalChecks = false;\n    // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n    // in MatCommonModule.\n    highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n    if (!this._hasDoneGlobalChecks) {\n      this._hasDoneGlobalChecks = true;\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Inject in here so the reference to `Platform` can be removed in production mode.\n        const platform = inject(Platform, {\n          optional: true\n        });\n        if (this._checkIsEnabled('doctype')) {\n          _checkDoctypeIsDefined(this._document);\n        }\n        if (this._checkIsEnabled('theme')) {\n          _checkThemeIsPresent(this._document, !!platform?.isBrowser);\n        }\n        if (this._checkIsEnabled('version')) {\n          _checkCdkVersionMatch();\n        }\n      }\n    }\n  }\n  /** Gets whether a specific sanity check is enabled. */\n  _checkIsEnabled(name) {\n    if (_isTestEnvironment()) {\n      return false;\n    }\n    if (typeof this._sanityChecks === 'boolean') {\n      return this._sanityChecks;\n    }\n    return !!this._sanityChecks[name];\n  }\n  static {\n    this.ɵfac = function MatCommonModule_Factory(t) {\n      return new (t || MatCommonModule)(i0.ɵɵinject(i1.HighContrastModeDetector), i0.ɵɵinject(MATERIAL_SANITY_CHECKS, 8), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatCommonModule,\n      imports: [BidiModule],\n      exports: [BidiModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [BidiModule, BidiModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCommonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule],\n      exports: [BidiModule]\n    }]\n  }], () => [{\n    type: i1.HighContrastModeDetector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MATERIAL_SANITY_CHECKS]\n    }]\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/** Checks that the page has a doctype. */\nfunction _checkDoctypeIsDefined(doc) {\n  if (!doc.doctype) {\n    console.warn('Current document does not have a doctype. This may cause ' + 'some Angular Material components not to behave as expected.');\n  }\n}\n/** Checks that a theme has been included. */\nfunction _checkThemeIsPresent(doc, isBrowser) {\n  // We need to assert that the `body` is defined, because these checks run very early\n  // and the `body` won't be defined if the consumer put their scripts in the `head`.\n  if (!doc.body || !isBrowser) {\n    return;\n  }\n  const testElement = doc.createElement('div');\n  testElement.classList.add('mat-theme-loaded-marker');\n  doc.body.appendChild(testElement);\n  const computedStyle = getComputedStyle(testElement);\n  // In some situations the computed style of the test element can be null. For example in\n  // Firefox, the computed style is null if an application is running inside of a hidden iframe.\n  // See: https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n  if (computedStyle && computedStyle.display !== 'none') {\n    console.warn('Could not find Angular Material core theme. Most Material ' + 'components may not work as expected. For more info refer ' + 'to the theming guide: https://material.angular.io/guide/theming');\n  }\n  testElement.remove();\n}\n/** Checks whether the Material version matches the CDK version. */\nfunction _checkCdkVersionMatch() {\n  if (VERSION.full !== VERSION$1.full) {\n    console.warn('The Angular Material version (' + VERSION.full + ') does not match ' + 'the Angular CDK version (' + VERSION$1.full + ').\\n' + 'Please ensure the versions of these two packages exactly match.');\n  }\n}\nfunction mixinDisabled(base) {\n  return class extends base {\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n    constructor(...args) {\n      super(...args);\n      this._disabled = false;\n    }\n  };\n}\nfunction mixinColor(base, defaultColor) {\n  return class extends base {\n    get color() {\n      return this._color;\n    }\n    set color(value) {\n      const colorPalette = value || this.defaultColor;\n      if (colorPalette !== this._color) {\n        if (this._color) {\n          this._elementRef.nativeElement.classList.remove(`mat-${this._color}`);\n        }\n        if (colorPalette) {\n          this._elementRef.nativeElement.classList.add(`mat-${colorPalette}`);\n        }\n        this._color = colorPalette;\n      }\n    }\n    constructor(...args) {\n      super(...args);\n      this.defaultColor = defaultColor;\n      // Set the default color that can be specified from the mixin.\n      this.color = defaultColor;\n    }\n  };\n}\nfunction mixinDisableRipple(base) {\n  return class extends base {\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n      return this._disableRipple;\n    }\n    set disableRipple(value) {\n      this._disableRipple = coerceBooleanProperty(value);\n    }\n    constructor(...args) {\n      super(...args);\n      this._disableRipple = false;\n    }\n  };\n}\nfunction mixinTabIndex(base, defaultTabIndex = 0) {\n  return class extends base {\n    get tabIndex() {\n      return this.disabled ? -1 : this._tabIndex;\n    }\n    set tabIndex(value) {\n      // If the specified tabIndex value is null or undefined, fall back to the default value.\n      this._tabIndex = value != null ? coerceNumberProperty(value) : this.defaultTabIndex;\n    }\n    constructor(...args) {\n      super(...args);\n      this._tabIndex = defaultTabIndex;\n      this.defaultTabIndex = defaultTabIndex;\n    }\n  };\n}\n\n/**\n * Class that tracks the error state of a component.\n * @docs-private\n */\nclass _ErrorStateTracker {\n  constructor(_defaultMatcher, ngControl, _parentFormGroup, _parentForm, _stateChanges) {\n    this._defaultMatcher = _defaultMatcher;\n    this.ngControl = ngControl;\n    this._parentFormGroup = _parentFormGroup;\n    this._parentForm = _parentForm;\n    this._stateChanges = _stateChanges;\n    /** Whether the tracker is currently in an error state. */\n    this.errorState = false;\n  }\n  /** Updates the error state based on the provided error state matcher. */\n  updateErrorState() {\n    const oldState = this.errorState;\n    const parent = this._parentFormGroup || this._parentForm;\n    const matcher = this.matcher || this._defaultMatcher;\n    const control = this.ngControl ? this.ngControl.control : null;\n    const newState = matcher?.isErrorState(control, parent) ?? false;\n    if (newState !== oldState) {\n      this.errorState = newState;\n      this._stateChanges.next();\n    }\n  }\n}\nfunction mixinErrorState(base) {\n  return class extends base {\n    /** Whether the component is in an error state. */\n    get errorState() {\n      return this._getTracker().errorState;\n    }\n    set errorState(value) {\n      this._getTracker().errorState = value;\n    }\n    /** An object used to control the error state of the component. */\n    get errorStateMatcher() {\n      return this._getTracker().matcher;\n    }\n    set errorStateMatcher(value) {\n      this._getTracker().matcher = value;\n    }\n    /** Updates the error state based on the provided error state matcher. */\n    updateErrorState() {\n      this._getTracker().updateErrorState();\n    }\n    _getTracker() {\n      if (!this._tracker) {\n        this._tracker = new _ErrorStateTracker(this._defaultErrorStateMatcher, this.ngControl, this._parentFormGroup, this._parentForm, this.stateChanges);\n      }\n      return this._tracker;\n    }\n    constructor(...args) {\n      super(...args);\n    }\n  };\n}\n\n/**\n * Mixin to augment a directive with an initialized property that will emits when ngOnInit ends.\n * @deprecated Track the initialized state manually.\n * @breaking-change 19.0.0\n */\nfunction mixinInitialized(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      /** Whether this directive has been marked as initialized. */\n      this._isInitialized = false;\n      /**\n       * List of subscribers that subscribed before the directive was initialized. Should be notified\n       * during _markInitialized. Set to null after pending subscribers are notified, and should\n       * not expect to be populated after.\n       */\n      this._pendingSubscribers = [];\n      /**\n       * Observable stream that emits when the directive initializes. If already initialized, the\n       * subscriber is stored to be notified once _markInitialized is called.\n       */\n      this.initialized = new Observable(subscriber => {\n        // If initialized, immediately notify the subscriber. Otherwise store the subscriber to notify\n        // when _markInitialized is called.\n        if (this._isInitialized) {\n          this._notifySubscriber(subscriber);\n        } else {\n          this._pendingSubscribers.push(subscriber);\n        }\n      });\n    }\n    /**\n     * Marks the state as initialized and notifies pending subscribers. Should be called at the end\n     * of ngOnInit.\n     * @docs-private\n     */\n    _markInitialized() {\n      if (this._isInitialized && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('This directive has already been marked as initialized and ' + 'should not be called twice.');\n      }\n      this._isInitialized = true;\n      this._pendingSubscribers.forEach(this._notifySubscriber);\n      this._pendingSubscribers = null;\n    }\n    /** Emits and completes the subscriber stream (should only emit once). */\n    _notifySubscriber(subscriber) {\n      subscriber.next();\n      subscriber.complete();\n    }\n  };\n}\n\n/** InjectionToken for datepicker that can be used to override default locale code. */\nconst MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {\n  providedIn: 'root',\n  factory: MAT_DATE_LOCALE_FACTORY\n});\n/** @docs-private */\nfunction MAT_DATE_LOCALE_FACTORY() {\n  return inject(LOCALE_ID);\n}\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nclass DateAdapter {\n  constructor() {\n    this._localeChanges = new Subject();\n    /** A stream that emits when the locale changes. */\n    this.localeChanges = this._localeChanges;\n  }\n  /**\n   * Given a potential date object, returns that same date object if it is\n   * a valid date, or `null` if it's not a valid date.\n   * @param obj The object to check.\n   * @returns A date or `null`.\n   */\n  getValidDateOrNull(obj) {\n    return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n  }\n  /**\n   * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n   * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n   * string). The default implementation does not allow any deserialization, it simply checks that\n   * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n   * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n   * support passing values from your backend directly to these properties by overriding this method\n   * to also deserialize the format used by your backend.\n   * @param value The value to be deserialized into a date object.\n   * @returns The deserialized date object, either a valid date, null if the value can be\n   *     deserialized into a null date (e.g. the empty string), or an invalid date.\n   */\n  deserialize(value) {\n    if (value == null || this.isDateInstance(value) && this.isValid(value)) {\n      return value;\n    }\n    return this.invalid();\n  }\n  /**\n   * Sets the locale used for all dates.\n   * @param locale The new locale.\n   */\n  setLocale(locale) {\n    this.locale = locale;\n    this._localeChanges.next();\n  }\n  /**\n   * Compares two dates.\n   * @param first The first date to compare.\n   * @param second The second date to compare.\n   * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n   *     a number greater than 0 if the first date is later.\n   */\n  compareDate(first, second) {\n    return this.getYear(first) - this.getYear(second) || this.getMonth(first) - this.getMonth(second) || this.getDate(first) - this.getDate(second);\n  }\n  /**\n   * Checks if two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n  sameDate(first, second) {\n    if (first && second) {\n      let firstValid = this.isValid(first);\n      let secondValid = this.isValid(second);\n      if (firstValid && secondValid) {\n        return !this.compareDate(first, second);\n      }\n      return firstValid == secondValid;\n    }\n    return first == second;\n  }\n  /**\n   * Clamp the given date between min and max dates.\n   * @param date The date to clamp.\n   * @param min The minimum value to allow. If null or omitted no min is enforced.\n   * @param max The maximum value to allow. If null or omitted no max is enforced.\n   * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n   *     otherwise `date`.\n   */\n  clampDate(date, min, max) {\n    if (min && this.compareDate(date, min) < 0) {\n      return min;\n    }\n    if (max && this.compareDate(date, max) > 0) {\n      return max;\n    }\n    return date;\n  }\n}\nconst MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');\n\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings an with out of bounds month, date, etc.\n */\nconst ISO_8601_REGEX = /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n/** Creates an array and fills it with values. */\nfunction range(length, valueFunction) {\n  const valuesArray = Array(length);\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n  return valuesArray;\n}\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\nclass NativeDateAdapter extends DateAdapter {\n  constructor(\n  /**\n   * @deprecated Now injected via inject(), param to be removed.\n   * @breaking-change 18.0.0\n   */\n  matDateLocale) {\n    super();\n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    this.useUtcForDisplay = false;\n    /** The injected locale. */\n    this._matDateLocale = inject(MAT_DATE_LOCALE, {\n      optional: true\n    });\n    if (matDateLocale !== undefined) {\n      this._matDateLocale = matDateLocale;\n    }\n    super.setLocale(this._matDateLocale);\n  }\n  getYear(date) {\n    return date.getFullYear();\n  }\n  getMonth(date) {\n    return date.getMonth();\n  }\n  getDate(date) {\n    return date.getDate();\n  }\n  getDayOfWeek(date) {\n    return date.getDay();\n  }\n  getMonthNames(style) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      month: style,\n      timeZone: 'utc'\n    });\n    return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n  }\n  getDateNames() {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      day: 'numeric',\n      timeZone: 'utc'\n    });\n    return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n  getDayOfWeekNames(style) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      weekday: style,\n      timeZone: 'utc'\n    });\n    return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n  getYearName(date) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      year: 'numeric',\n      timeZone: 'utc'\n    });\n    return this._format(dtf, date);\n  }\n  getFirstDayOfWeek() {\n    // We can't tell using native JS Date what the first day of the week is, we default to Sunday.\n    return 0;\n  }\n  getNumDaysInMonth(date) {\n    return this.getDate(this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0));\n  }\n  clone(date) {\n    return new Date(date.getTime());\n  }\n  createDate(year, month, date) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Check for invalid month and date (except upper bound on date which we have to check after\n      // creating the Date).\n      if (month < 0 || month > 11) {\n        throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n      }\n      if (date < 1) {\n        throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n      }\n    }\n    let result = this._createDateWithOverflow(year, month, date);\n    // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n    if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n    }\n    return result;\n  }\n  today() {\n    return new Date();\n  }\n  parse(value, parseFormat) {\n    // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n    // parameters.\n    if (typeof value == 'number') {\n      return new Date(value);\n    }\n    return value ? new Date(Date.parse(value)) : null;\n  }\n  format(date, displayFormat) {\n    if (!this.isValid(date)) {\n      throw Error('NativeDateAdapter: Cannot format invalid date.');\n    }\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      ...displayFormat,\n      timeZone: 'utc'\n    });\n    return this._format(dtf, date);\n  }\n  addCalendarYears(date, years) {\n    return this.addCalendarMonths(date, years * 12);\n  }\n  addCalendarMonths(date, months) {\n    let newDate = this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + months, this.getDate(date));\n    // It's possible to wind up in the wrong month if the original month has more days than the new\n    // month. In this case we want to go to the last day of the desired month.\n    // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n    // guarantee this.\n    if (this.getMonth(newDate) != ((this.getMonth(date) + months) % 12 + 12) % 12) {\n      newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n    }\n    return newDate;\n  }\n  addCalendarDays(date, days) {\n    return this._createDateWithOverflow(this.getYear(date), this.getMonth(date), this.getDate(date) + days);\n  }\n  toIso8601(date) {\n    return [date.getUTCFullYear(), this._2digit(date.getUTCMonth() + 1), this._2digit(date.getUTCDate())].join('-');\n  }\n  /**\n   * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n   * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n   * invalid date for all other values.\n   */\n  deserialize(value) {\n    if (typeof value === 'string') {\n      if (!value) {\n        return null;\n      }\n      // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n      // string is the right format first.\n      if (ISO_8601_REGEX.test(value)) {\n        let date = new Date(value);\n        if (this.isValid(date)) {\n          return date;\n        }\n      }\n    }\n    return super.deserialize(value);\n  }\n  isDateInstance(obj) {\n    return obj instanceof Date;\n  }\n  isValid(date) {\n    return !isNaN(date.getTime());\n  }\n  invalid() {\n    return new Date(NaN);\n  }\n  /** Creates a date but allows the month and date to overflow. */\n  _createDateWithOverflow(year, month, date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setFullYear` and `setHours` instead.\n    const d = new Date();\n    d.setFullYear(year, month, date);\n    d.setHours(0, 0, 0, 0);\n    return d;\n  }\n  /**\n   * Pads a number to make it two digits.\n   * @param n The number to pad.\n   * @returns The padded number.\n   */\n  _2digit(n) {\n    return ('00' + n).slice(-2);\n  }\n  /**\n   * When converting Date object to string, javascript built-in functions may return wrong\n   * results because it applies its internal DST rules. The DST rules around the world change\n   * very frequently, and the current valid rule is not always valid in previous years though.\n   * We work around this problem building a new Date object which has its internal UTC\n   * representation with the local date and time.\n   * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have\n   *    timeZone set to 'utc' to work fine.\n   * @param date Date from which we want to get the string representation according to dtf\n   * @returns A Date object with its UTC representation based on the passed in date info\n   */\n  _format(dtf, date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n    const d = new Date();\n    d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n    d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n    return dtf.format(d);\n  }\n  static {\n    this.ɵfac = function NativeDateAdapter_Factory(t) {\n      return new (t || NativeDateAdapter)(i0.ɵɵinject(MAT_DATE_LOCALE, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NativeDateAdapter,\n      factory: NativeDateAdapter.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NativeDateAdapter, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_DATE_LOCALE]\n    }]\n  }], null);\n})();\nconst MAT_NATIVE_DATE_FORMATS = {\n  parse: {\n    dateInput: null\n  },\n  display: {\n    dateInput: {\n      year: 'numeric',\n      month: 'numeric',\n      day: 'numeric'\n    },\n    monthYearLabel: {\n      year: 'numeric',\n      month: 'short'\n    },\n    dateA11yLabel: {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    },\n    monthYearA11yLabel: {\n      year: 'numeric',\n      month: 'long'\n    }\n  }\n};\nclass NativeDateModule {\n  static {\n    this.ɵfac = function NativeDateModule_Factory(t) {\n      return new (t || NativeDateModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NativeDateModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: DateAdapter,\n        useClass: NativeDateAdapter\n      }]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NativeDateModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: DateAdapter,\n        useClass: NativeDateAdapter\n      }]\n    }]\n  }], null, null);\n})();\nclass MatNativeDateModule {\n  static {\n    this.ɵfac = function MatNativeDateModule_Factory(t) {\n      return new (t || MatNativeDateModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatNativeDateModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [provideNativeDateAdapter()]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNativeDateModule, [{\n    type: NgModule,\n    args: [{\n      providers: [provideNativeDateAdapter()]\n    }]\n  }], null, null);\n})();\nfunction provideNativeDateAdapter(formats = MAT_NATIVE_DATE_FORMATS) {\n  return [{\n    provide: DateAdapter,\n    useClass: NativeDateAdapter\n  }, {\n    provide: MAT_DATE_FORMATS,\n    useValue: formats\n  }];\n}\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.dirty || form && form.submitted));\n  }\n  static {\n    this.ɵfac = function ShowOnDirtyErrorStateMatcher_Factory(t) {\n      return new (t || ShowOnDirtyErrorStateMatcher)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ShowOnDirtyErrorStateMatcher,\n      factory: ShowOnDirtyErrorStateMatcher.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShowOnDirtyErrorStateMatcher, [{\n    type: Injectable\n  }], null, null);\n})();\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.touched || form && form.submitted));\n  }\n  static {\n    this.ɵfac = function ErrorStateMatcher_Factory(t) {\n      return new (t || ErrorStateMatcher)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ErrorStateMatcher,\n      factory: ErrorStateMatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ErrorStateMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\nclass MatLine {\n  static {\n    this.ɵfac = function MatLine_Factory(t) {\n      return new (t || MatLine)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatLine,\n      selectors: [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]],\n      hostAttrs: [1, \"mat-line\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLine, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-line], [matLine]',\n      host: {\n        'class': 'mat-line'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nfunction setLines(lines, element, prefix = 'mat') {\n  // Note: doesn't need to unsubscribe, because `changes`\n  // gets completed by Angular when the view is destroyed.\n  lines.changes.pipe(startWith(lines)).subscribe(({\n    length\n  }) => {\n    setClass(element, `${prefix}-2-line`, false);\n    setClass(element, `${prefix}-3-line`, false);\n    setClass(element, `${prefix}-multi-line`, false);\n    if (length === 2 || length === 3) {\n      setClass(element, `${prefix}-${length}-line`, true);\n    } else if (length > 3) {\n      setClass(element, `${prefix}-multi-line`, true);\n    }\n  });\n}\n/** Adds or removes a class from an element. */\nfunction setClass(element, className, isAdd) {\n  element.nativeElement.classList.toggle(className, isAdd);\n}\nclass MatLineModule {\n  static {\n    this.ɵfac = function MatLineModule_Factory(t) {\n      return new (t || MatLineModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatLineModule,\n      imports: [MatCommonModule, MatLine],\n      exports: [MatLine, MatCommonModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLineModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatLine],\n      exports: [MatLine, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/** Possible states for a ripple element. */\nvar RippleState;\n(function (RippleState) {\n  RippleState[RippleState[\"FADING_IN\"] = 0] = \"FADING_IN\";\n  RippleState[RippleState[\"VISIBLE\"] = 1] = \"VISIBLE\";\n  RippleState[RippleState[\"FADING_OUT\"] = 2] = \"FADING_OUT\";\n  RippleState[RippleState[\"HIDDEN\"] = 3] = \"HIDDEN\";\n})(RippleState || (RippleState = {}));\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n  constructor(_renderer, /** Reference to the ripple HTML element. */\n  element, /** Ripple configuration used for the ripple. */\n  config, /* Whether animations are forcibly disabled for ripples through CSS. */\n  _animationForciblyDisabledThroughCss = false) {\n    this._renderer = _renderer;\n    this.element = element;\n    this.config = config;\n    this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n    /** Current state of the ripple. */\n    this.state = RippleState.HIDDEN;\n  }\n  /** Fades out the ripple element. */\n  fadeOut() {\n    this._renderer.fadeOutRipple(this);\n  }\n}\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n  constructor() {\n    this._events = new Map();\n    /** Event handler that is bound and which dispatches the events to the different targets. */\n    this._delegateEventHandler = event => {\n      const target = _getEventTarget(event);\n      if (target) {\n        this._events.get(event.type)?.forEach((handlers, element) => {\n          if (element === target || element.contains(target)) {\n            handlers.forEach(handler => handler.handleEvent(event));\n          }\n        });\n      }\n    };\n  }\n  /** Adds an event handler. */\n  addHandler(ngZone, name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (handlersForEvent) {\n      const handlersForElement = handlersForEvent.get(element);\n      if (handlersForElement) {\n        handlersForElement.add(handler);\n      } else {\n        handlersForEvent.set(element, new Set([handler]));\n      }\n    } else {\n      this._events.set(name, new Map([[element, new Set([handler])]]));\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n      });\n    }\n  }\n  /** Removes an event handler. */\n  removeHandler(name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (!handlersForEvent) {\n      return;\n    }\n    const handlersForElement = handlersForEvent.get(element);\n    if (!handlersForElement) {\n      return;\n    }\n    handlersForElement.delete(handler);\n    if (handlersForElement.size === 0) {\n      handlersForEvent.delete(element);\n    }\n    if (handlersForEvent.size === 0) {\n      this._events.delete(name);\n      document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n    }\n  }\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n  enterDuration: 225,\n  exitDuration: 150\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n  static {\n    this._eventManager = new RippleEventManager();\n  }\n  constructor(_target, _ngZone, elementOrElementRef, _platform) {\n    this._target = _target;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    /** Whether the pointer is currently down or not. */\n    this._isPointerDown = false;\n    /**\n     * Map of currently active ripple references.\n     * The ripple reference is mapped to its element event listeners.\n     * The reason why `| null` is used is that event listeners are added only\n     * when the condition is truthy (see the `_startFadeOutTransition` method).\n     */\n    this._activeRipples = new Map();\n    /** Whether pointer-up event listeners have been registered. */\n    this._pointerUpEventsRegistered = false;\n    // Only do anything if we're on the browser.\n    if (_platform.isBrowser) {\n      this._containerElement = coerceElement(elementOrElementRef);\n    }\n  }\n  /**\n   * Fades in a ripple at the given coordinates.\n   * @param x Coordinate within the element, along the X axis at which to start the ripple.\n   * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n   * @param config Extra ripple options.\n   */\n  fadeInRipple(x, y, config = {}) {\n    const containerRect = this._containerRect = this._containerRect || this._containerElement.getBoundingClientRect();\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...config.animation\n    };\n    if (config.centered) {\n      x = containerRect.left + containerRect.width / 2;\n      y = containerRect.top + containerRect.height / 2;\n    }\n    const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n    const offsetX = x - containerRect.left;\n    const offsetY = y - containerRect.top;\n    const enterDuration = animationConfig.enterDuration;\n    const ripple = document.createElement('div');\n    ripple.classList.add('mat-ripple-element');\n    ripple.style.left = `${offsetX - radius}px`;\n    ripple.style.top = `${offsetY - radius}px`;\n    ripple.style.height = `${radius * 2}px`;\n    ripple.style.width = `${radius * 2}px`;\n    // If a custom color has been specified, set it as inline style. If no color is\n    // set, the default color will be applied through the ripple theme styles.\n    if (config.color != null) {\n      ripple.style.backgroundColor = config.color;\n    }\n    ripple.style.transitionDuration = `${enterDuration}ms`;\n    this._containerElement.appendChild(ripple);\n    // By default the browser does not recalculate the styles of dynamically created\n    // ripple elements. This is critical to ensure that the `scale` animates properly.\n    // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n    // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n    const computedStyles = window.getComputedStyle(ripple);\n    const userTransitionProperty = computedStyles.transitionProperty;\n    const userTransitionDuration = computedStyles.transitionDuration;\n    // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n    // `transition: none` or `display: none`). This is technically unexpected since animations are\n    // controlled through the animation config, but this exists for backwards compatibility. This\n    // logic does not need to be super accurate since it covers some edge cases which can be easily\n    // avoided by users.\n    const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n    // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n    // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n    userTransitionDuration === '0s' || userTransitionDuration === '0s, 0s' ||\n    // If the container is 0x0, it's likely `display: none`.\n    containerRect.width === 0 && containerRect.height === 0;\n    // Exposed reference to the ripple that will be returned.\n    const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n    // Start the enter animation by setting the transform/scale to 100%. The animation will\n    // execute as part of this statement because we forced a style recalculation before.\n    // Note: We use a 3d transform here in order to avoid an issue in Safari where\n    // the ripples aren't clipped when inside the shadow DOM (see #24028).\n    ripple.style.transform = 'scale3d(1, 1, 1)';\n    rippleRef.state = RippleState.FADING_IN;\n    if (!config.persistent) {\n      this._mostRecentTransientRipple = rippleRef;\n    }\n    let eventListeners = null;\n    // Do not register the `transition` event listener if fade-in and fade-out duration\n    // are set to zero. The events won't fire anyway and we can save resources here.\n    if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n      this._ngZone.runOutsideAngular(() => {\n        const onTransitionEnd = () => this._finishRippleTransition(rippleRef);\n        const onTransitionCancel = () => this._destroyRipple(rippleRef);\n        ripple.addEventListener('transitionend', onTransitionEnd);\n        // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n        // directly as otherwise we would keep it part of the ripple container forever.\n        // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n        ripple.addEventListener('transitioncancel', onTransitionCancel);\n        eventListeners = {\n          onTransitionEnd,\n          onTransitionCancel\n        };\n      });\n    }\n    // Add the ripple reference to the list of all active ripples.\n    this._activeRipples.set(rippleRef, eventListeners);\n    // In case there is no fade-in transition duration, we need to manually call the transition\n    // end listener because `transitionend` doesn't fire if there is no transition.\n    if (animationForciblyDisabledThroughCss || !enterDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n    return rippleRef;\n  }\n  /** Fades out a ripple reference. */\n  fadeOutRipple(rippleRef) {\n    // For ripples already fading out or hidden, this should be a noop.\n    if (rippleRef.state === RippleState.FADING_OUT || rippleRef.state === RippleState.HIDDEN) {\n      return;\n    }\n    const rippleEl = rippleRef.element;\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...rippleRef.config.animation\n    };\n    // This starts the fade-out transition and will fire the transition end listener that\n    // removes the ripple element from the DOM.\n    rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n    rippleEl.style.opacity = '0';\n    rippleRef.state = RippleState.FADING_OUT;\n    // In case there is no fade-out transition duration, we need to manually call the\n    // transition end listener because `transitionend` doesn't fire if there is no transition.\n    if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n  }\n  /** Fades out all currently active ripples. */\n  fadeOutAll() {\n    this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n  }\n  /** Fades out all currently active non-persistent ripples. */\n  fadeOutAllNonPersistent() {\n    this._getActiveRipples().forEach(ripple => {\n      if (!ripple.config.persistent) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  /** Sets up the trigger event listeners */\n  setupTriggerEvents(elementOrElementRef) {\n    const element = coerceElement(elementOrElementRef);\n    if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n      return;\n    }\n    // Remove all previously registered event listeners from the trigger element.\n    this._removeTriggerEvents();\n    this._triggerElement = element;\n    // Use event delegation for the trigger events since they're\n    // set up during creation and are performance-sensitive.\n    pointerDownEvents.forEach(type => {\n      RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n    });\n  }\n  /**\n   * Handles all registered events.\n   * @docs-private\n   */\n  handleEvent(event) {\n    if (event.type === 'mousedown') {\n      this._onMousedown(event);\n    } else if (event.type === 'touchstart') {\n      this._onTouchStart(event);\n    } else {\n      this._onPointerUp();\n    }\n    // If pointer-up events haven't been registered yet, do so now.\n    // We do this on-demand in order to reduce the total number of event listeners\n    // registered by the ripples, which speeds up the rendering time for large UIs.\n    if (!this._pointerUpEventsRegistered) {\n      // The events for hiding the ripple are bound directly on the trigger, because:\n      // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n      // delegation will be diminished by having to look through all the data structures often.\n      // 2. They aren't as performance-sensitive, because they're bound only after the user\n      // has interacted with an element.\n      this._ngZone.runOutsideAngular(() => {\n        pointerUpEvents.forEach(type => {\n          this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n        });\n      });\n      this._pointerUpEventsRegistered = true;\n    }\n  }\n  /** Method that will be called if the fade-in or fade-in transition completed. */\n  _finishRippleTransition(rippleRef) {\n    if (rippleRef.state === RippleState.FADING_IN) {\n      this._startFadeOutTransition(rippleRef);\n    } else if (rippleRef.state === RippleState.FADING_OUT) {\n      this._destroyRipple(rippleRef);\n    }\n  }\n  /**\n   * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n   * is not held down anymore.\n   */\n  _startFadeOutTransition(rippleRef) {\n    const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n    const {\n      persistent\n    } = rippleRef.config;\n    rippleRef.state = RippleState.VISIBLE;\n    // When the timer runs out while the user has kept their pointer down, we want to\n    // keep only the persistent ripples and the latest transient ripple. We do this,\n    // because we don't want stacked transient ripples to appear after their enter\n    // animation has finished.\n    if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n      rippleRef.fadeOut();\n    }\n  }\n  /** Destroys the given ripple by removing it from the DOM and updating its state. */\n  _destroyRipple(rippleRef) {\n    const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n    this._activeRipples.delete(rippleRef);\n    // Clear out the cached bounding rect if we have no more ripples.\n    if (!this._activeRipples.size) {\n      this._containerRect = null;\n    }\n    // If the current ref is the most recent transient ripple, unset it\n    // avoid memory leaks.\n    if (rippleRef === this._mostRecentTransientRipple) {\n      this._mostRecentTransientRipple = null;\n    }\n    rippleRef.state = RippleState.HIDDEN;\n    if (eventListeners !== null) {\n      rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n      rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n    }\n    rippleRef.element.remove();\n  }\n  /** Function being called whenever the trigger is being pressed using mouse. */\n  _onMousedown(event) {\n    // Screen readers will fire fake mouse events for space/enter. Skip launching a\n    // ripple in this case for consistency with the non-screen-reader experience.\n    const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n    const isSyntheticEvent = this._lastTouchStartEvent && Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n    if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n      this._isPointerDown = true;\n      this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n    }\n  }\n  /** Function being called whenever the trigger is being pressed using touch. */\n  _onTouchStart(event) {\n    if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n      // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n      // events will launch a second ripple if we don't ignore mouse events for a specific\n      // time after a touchstart event.\n      this._lastTouchStartEvent = Date.now();\n      this._isPointerDown = true;\n      // Use `changedTouches` so we skip any touches where the user put\n      // their finger down, but used another finger to tap the element again.\n      const touches = event.changedTouches;\n      // According to the typings the touches should always be defined, but in some cases\n      // the browser appears to not assign them in tests which leads to flakes.\n      if (touches) {\n        for (let i = 0; i < touches.length; i++) {\n          this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n        }\n      }\n    }\n  }\n  /** Function being called whenever the trigger is being released. */\n  _onPointerUp() {\n    if (!this._isPointerDown) {\n      return;\n    }\n    this._isPointerDown = false;\n    // Fade-out all ripples that are visible and not persistent.\n    this._getActiveRipples().forEach(ripple => {\n      // By default, only ripples that are completely visible will fade out on pointer release.\n      // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n      const isVisible = ripple.state === RippleState.VISIBLE || ripple.config.terminateOnPointerUp && ripple.state === RippleState.FADING_IN;\n      if (!ripple.config.persistent && isVisible) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  _getActiveRipples() {\n    return Array.from(this._activeRipples.keys());\n  }\n  /** Removes previously registered event listeners from the trigger element. */\n  _removeTriggerEvents() {\n    const trigger = this._triggerElement;\n    if (trigger) {\n      pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n      if (this._pointerUpEventsRegistered) {\n        pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n        this._pointerUpEventsRegistered = false;\n      }\n    }\n  }\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n  const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n  const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n  return Math.sqrt(distX * distX + distY * distY);\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n  /**\n   * Whether click events will not trigger the ripple. Ripples can be still launched manually\n   * by using the `launch()` method.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value) {\n      this.fadeOutAllNonPersistent();\n    }\n    this._disabled = value;\n    this._setupTriggerEventsIfEnabled();\n  }\n  /**\n   * The element that triggers the ripple when click events are received.\n   * Defaults to the directive's host element.\n   */\n  get trigger() {\n    return this._trigger || this._elementRef.nativeElement;\n  }\n  set trigger(trigger) {\n    this._trigger = trigger;\n    this._setupTriggerEventsIfEnabled();\n  }\n  constructor(_elementRef, ngZone, platform, globalOptions, _animationMode) {\n    this._elementRef = _elementRef;\n    this._animationMode = _animationMode;\n    /**\n     * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n     * will be the distance from the center of the ripple to the furthest corner of the host element's\n     * bounding rectangle.\n     */\n    this.radius = 0;\n    this._disabled = false;\n    /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n    this._isInitialized = false;\n    this._globalOptions = globalOptions || {};\n    this._rippleRenderer = new RippleRenderer(this, ngZone, _elementRef, platform);\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n    this._setupTriggerEventsIfEnabled();\n  }\n  ngOnDestroy() {\n    this._rippleRenderer._removeTriggerEvents();\n  }\n  /** Fades out all currently showing ripple elements. */\n  fadeOutAll() {\n    this._rippleRenderer.fadeOutAll();\n  }\n  /** Fades out all currently showing non-persistent ripple elements. */\n  fadeOutAllNonPersistent() {\n    this._rippleRenderer.fadeOutAllNonPersistent();\n  }\n  /**\n   * Ripple configuration from the directive's input values.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleConfig() {\n    return {\n      centered: this.centered,\n      radius: this.radius,\n      color: this.color,\n      animation: {\n        ...this._globalOptions.animation,\n        ...(this._animationMode === 'NoopAnimations' ? {\n          enterDuration: 0,\n          exitDuration: 0\n        } : {}),\n        ...this.animation\n      },\n      terminateOnPointerUp: this._globalOptions.terminateOnPointerUp\n    };\n  }\n  /**\n   * Whether ripples on pointer-down are disabled or not.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleDisabled() {\n    return this.disabled || !!this._globalOptions.disabled;\n  }\n  /** Sets up the trigger event listeners if ripples are enabled. */\n  _setupTriggerEventsIfEnabled() {\n    if (!this.disabled && this._isInitialized) {\n      this._rippleRenderer.setupTriggerEvents(this.trigger);\n    }\n  }\n  /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n  launch(configOrX, y = 0, config) {\n    if (typeof configOrX === 'number') {\n      return this._rippleRenderer.fadeInRipple(configOrX, y, {\n        ...this.rippleConfig,\n        ...config\n      });\n    } else {\n      return this._rippleRenderer.fadeInRipple(0, 0, {\n        ...this.rippleConfig,\n        ...configOrX\n      });\n    }\n  }\n  static {\n    this.ɵfac = function MatRipple_Factory(t) {\n      return new (t || MatRipple)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.Platform), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRipple,\n      selectors: [[\"\", \"mat-ripple\", \"\"], [\"\", \"matRipple\", \"\"]],\n      hostAttrs: [1, \"mat-ripple\"],\n      hostVars: 2,\n      hostBindings: function MatRipple_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-ripple-unbounded\", ctx.unbounded);\n        }\n      },\n      inputs: {\n        color: [i0.ɵɵInputFlags.None, \"matRippleColor\", \"color\"],\n        unbounded: [i0.ɵɵInputFlags.None, \"matRippleUnbounded\", \"unbounded\"],\n        centered: [i0.ɵɵInputFlags.None, \"matRippleCentered\", \"centered\"],\n        radius: [i0.ɵɵInputFlags.None, \"matRippleRadius\", \"radius\"],\n        animation: [i0.ɵɵInputFlags.None, \"matRippleAnimation\", \"animation\"],\n        disabled: [i0.ɵɵInputFlags.None, \"matRippleDisabled\", \"disabled\"],\n        trigger: [i0.ɵɵInputFlags.None, \"matRippleTrigger\", \"trigger\"]\n      },\n      exportAs: [\"matRipple\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRipple, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-ripple], [matRipple]',\n      exportAs: 'matRipple',\n      host: {\n        'class': 'mat-ripple',\n        '[class.mat-ripple-unbounded]': 'unbounded'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$1.Platform\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    color: [{\n      type: Input,\n      args: ['matRippleColor']\n    }],\n    unbounded: [{\n      type: Input,\n      args: ['matRippleUnbounded']\n    }],\n    centered: [{\n      type: Input,\n      args: ['matRippleCentered']\n    }],\n    radius: [{\n      type: Input,\n      args: ['matRippleRadius']\n    }],\n    animation: [{\n      type: Input,\n      args: ['matRippleAnimation']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matRippleDisabled']\n    }],\n    trigger: [{\n      type: Input,\n      args: ['matRippleTrigger']\n    }]\n  });\n})();\nclass MatRippleModule {\n  static {\n    this.ɵfac = function MatRippleModule_Factory(t) {\n      return new (t || MatRippleModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatRippleModule,\n      imports: [MatCommonModule, MatRipple],\n      exports: [MatRipple, MatCommonModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRipple],\n      exports: [MatRipple, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\nclass MatPseudoCheckbox {\n  constructor(_animationMode) {\n    this._animationMode = _animationMode;\n    /** Display state of the checkbox. */\n    this.state = 'unchecked';\n    /** Whether the checkbox is disabled. */\n    this.disabled = false;\n    /**\n     * Appearance of the pseudo checkbox. Default appearance of 'full' renders a checkmark/mixedmark\n     * indicator inside a square box. 'minimal' appearance only renders the checkmark/mixedmark.\n     */\n    this.appearance = 'full';\n  }\n  static {\n    this.ɵfac = function MatPseudoCheckbox_Factory(t) {\n      return new (t || MatPseudoCheckbox)(i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatPseudoCheckbox,\n      selectors: [[\"mat-pseudo-checkbox\"]],\n      hostAttrs: [1, \"mat-pseudo-checkbox\"],\n      hostVars: 12,\n      hostBindings: function MatPseudoCheckbox_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-pseudo-checkbox-indeterminate\", ctx.state === \"indeterminate\")(\"mat-pseudo-checkbox-checked\", ctx.state === \"checked\")(\"mat-pseudo-checkbox-disabled\", ctx.disabled)(\"mat-pseudo-checkbox-minimal\", ctx.appearance === \"minimal\")(\"mat-pseudo-checkbox-full\", ctx.appearance === \"full\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n        }\n      },\n      inputs: {\n        state: \"state\",\n        disabled: \"disabled\",\n        appearance: \"appearance\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function MatPseudoCheckbox_Template(rf, ctx) {},\n      styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-minimal-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox-full{border-color:var(--mat-full-pseudo-checkbox-unselected-icon-color);border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-full-pseudo-checkbox-disabled-unselected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-full-pseudo-checkbox-selected-icon-color);border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-full-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-full-pseudo-checkbox-disabled-selected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-full-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckbox, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'mat-pseudo-checkbox',\n      template: '',\n      host: {\n        'class': 'mat-pseudo-checkbox',\n        '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n        '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n        '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n        '[class.mat-pseudo-checkbox-minimal]': 'appearance === \"minimal\"',\n        '[class.mat-pseudo-checkbox-full]': 'appearance === \"full\"',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"'\n      },\n      standalone: true,\n      styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-minimal-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox-full{border-color:var(--mat-full-pseudo-checkbox-unselected-icon-color);border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-full-pseudo-checkbox-disabled-unselected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-full-pseudo-checkbox-selected-icon-color);border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-full-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-full-pseudo-checkbox-disabled-selected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-full-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    state: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    appearance: [{\n      type: Input\n    }]\n  });\n})();\nclass MatPseudoCheckboxModule {\n  static {\n    this.ɵfac = function MatPseudoCheckboxModule_Factory(t) {\n      return new (t || MatPseudoCheckboxModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatPseudoCheckboxModule,\n      imports: [MatCommonModule, MatPseudoCheckbox],\n      exports: [MatPseudoCheckbox]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatPseudoCheckbox],\n      exports: [MatPseudoCheckbox]\n    }]\n  }], null, null);\n})();\n\n/**\n * Injection token used to provide the parent component to options.\n */\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n// Counter for unique group ids.\nlet _uniqueOptgroupIdCounter = 0;\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup {\n  constructor(parent) {\n    /** whether the option group is disabled. */\n    this.disabled = false;\n    /** Unique id for the underlying label. */\n    this._labelId = `mat-optgroup-label-${_uniqueOptgroupIdCounter++}`;\n    this._inert = parent?.inertGroups ?? false;\n  }\n  static {\n    this.ɵfac = function MatOptgroup_Factory(t) {\n      return new (t || MatOptgroup)(i0.ɵɵdirectiveInject(MAT_OPTION_PARENT_COMPONENT, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatOptgroup,\n      selectors: [[\"mat-optgroup\"]],\n      hostAttrs: [1, \"mat-mdc-optgroup\"],\n      hostVars: 3,\n      hostBindings: function MatOptgroup_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx._inert ? null : \"group\")(\"aria-disabled\", ctx._inert ? null : ctx.disabled.toString())(\"aria-labelledby\", ctx._inert ? null : ctx._labelId);\n        }\n      },\n      inputs: {\n        label: \"label\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      exportAs: [\"matOptgroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_OPTGROUP,\n        useExisting: MatOptgroup\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 5,\n      vars: 4,\n      consts: [[\"role\", \"presentation\", 1, \"mat-mdc-optgroup-label\", 3, \"id\"], [1, \"mdc-list-item__primary-text\"]],\n      template: function MatOptgroup_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"span\", 0)(1, \"span\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵprojection(3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵprojection(4, 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-list-item--disabled\", ctx.disabled);\n          i0.ɵɵproperty(\"id\", ctx._labelId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.label, \" \");\n        }\n      },\n      styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color);font-family:var(--mat-optgroup-label-text-font);line-height:var(--mat-optgroup-label-text-line-height);font-size:var(--mat-optgroup-label-text-size);letter-spacing:var(--mat-optgroup-label-text-tracking);font-weight:var(--mat-optgroup-label-text-weight)}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptgroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-optgroup',\n      exportAs: 'matOptgroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-optgroup',\n        '[attr.role]': '_inert ? null : \"group\"',\n        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n        '[attr.aria-labelledby]': '_inert ? null : _labelId'\n      },\n      providers: [{\n        provide: MAT_OPTGROUP,\n        useExisting: MatOptgroup\n      }],\n      standalone: true,\n      template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\",\n      styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color);font-family:var(--mat-optgroup-label-text-font);line-height:var(--mat-optgroup-label-text-line-height);font-size:var(--mat-optgroup-label-text-size);letter-spacing:var(--mat-optgroup-label-text-tracking);font-weight:var(--mat-optgroup-label-text-weight)}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal}\"]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_OPTION_PARENT_COMPONENT]\n    }, {\n      type: Optional\n    }]\n  }], {\n    label: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Option IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueIdCounter = 0;\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n  constructor(/** Reference to the option that emitted the event. */\n  source, /** Whether the change in the option's value was a result of a user action. */\n  isUserInput = false) {\n    this.source = source;\n    this.isUserInput = isUserInput;\n  }\n}\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption {\n  /** Whether the wrapping component is in multiple selection mode. */\n  get multiple() {\n    return this._parent && this._parent.multiple;\n  }\n  /** Whether or not the option is currently selected. */\n  get selected() {\n    return this._selected;\n  }\n  /** Whether the option is disabled. */\n  get disabled() {\n    return this.group && this.group.disabled || this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  /** Whether ripples for the option are disabled. */\n  get disableRipple() {\n    return !!(this._parent && this._parent.disableRipple);\n  }\n  /** Whether to display checkmark for single-selection. */\n  get hideSingleSelectionIndicator() {\n    return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n  }\n  constructor(_element, _changeDetectorRef, _parent, group) {\n    this._element = _element;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._parent = _parent;\n    this.group = group;\n    this._selected = false;\n    this._active = false;\n    this._disabled = false;\n    this._mostRecentViewValue = '';\n    /** The unique ID of the option. */\n    this.id = `mat-option-${_uniqueIdCounter++}`;\n    /** Event emitted when the option is selected or deselected. */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onSelectionChange = new EventEmitter();\n    /** Emits when the state of the option changes and any parents have to be notified. */\n    this._stateChanges = new Subject();\n  }\n  /**\n   * Whether or not the option is currently active and ready to be selected.\n   * An active option displays styles as if it is focused, but the\n   * focus is actually retained somewhere else. This comes in handy\n   * for components like autocomplete where focus must remain on the input.\n   */\n  get active() {\n    return this._active;\n  }\n  /**\n   * The displayed value of the option. It is necessary to show the selected option in the\n   * select's trigger.\n   */\n  get viewValue() {\n    // TODO(kara): Add input property alternative for node envs.\n    return (this._text?.nativeElement.textContent || '').trim();\n  }\n  /** Selects the option. */\n  select(emitEvent = true) {\n    if (!this._selected) {\n      this._selected = true;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Deselects the option. */\n  deselect(emitEvent = true) {\n    if (this._selected) {\n      this._selected = false;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Sets focus onto this option. */\n  focus(_origin, options) {\n    // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n    // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n    const element = this._getHostElement();\n    if (typeof element.focus === 'function') {\n      element.focus(options);\n    }\n  }\n  /**\n   * This method sets display styles on the option to make it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setActiveStyles() {\n    if (!this._active) {\n      this._active = true;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method removes display styles on the option that made it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setInactiveStyles() {\n    if (this._active) {\n      this._active = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    return this.viewValue;\n  }\n  /** Ensures the option is selected when activated from the keyboard. */\n  _handleKeydown(event) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n      this._selectViaInteraction();\n      // Prevent the page from scrolling down and form submits.\n      event.preventDefault();\n    }\n  }\n  /**\n   * `Selects the option while indicating the selection came from the user. Used to\n   * determine if the select's view -> model callback should be invoked.`\n   */\n  _selectViaInteraction() {\n    if (!this.disabled) {\n      this._selected = this.multiple ? !this._selected : true;\n      this._changeDetectorRef.markForCheck();\n      this._emitSelectionChangeEvent(true);\n    }\n  }\n  /** Returns the correct tabindex for the option depending on disabled state. */\n  // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n  // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n  // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Gets the host DOM element. */\n  _getHostElement() {\n    return this._element.nativeElement;\n  }\n  ngAfterViewChecked() {\n    // Since parent components could be using the option's label to display the selected values\n    // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n    // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n    // relatively cheap, however we still limit them only to selected options in order to avoid\n    // hitting the DOM too often.\n    if (this._selected) {\n      const viewValue = this.viewValue;\n      if (viewValue !== this._mostRecentViewValue) {\n        if (this._mostRecentViewValue) {\n          this._stateChanges.next();\n        }\n        this._mostRecentViewValue = viewValue;\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  /** Emits the selection change event. */\n  _emitSelectionChangeEvent(isUserInput = false) {\n    this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n  }\n  static {\n    this.ɵfac = function MatOption_Factory(t) {\n      return new (t || MatOption)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_OPTION_PARENT_COMPONENT, 8), i0.ɵɵdirectiveInject(MAT_OPTGROUP, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatOption,\n      selectors: [[\"mat-option\"]],\n      viewQuery: function MatOption_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c2, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._text = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"option\", 1, \"mat-mdc-option\", \"mdc-list-item\"],\n      hostVars: 11,\n      hostBindings: function MatOption_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatOption_click_HostBindingHandler() {\n            return ctx._selectViaInteraction();\n          })(\"keydown\", function MatOption_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"aria-selected\", ctx.selected)(\"aria-disabled\", ctx.disabled.toString());\n          i0.ɵɵclassProp(\"mdc-list-item--selected\", ctx.selected)(\"mat-mdc-option-multiple\", ctx.multiple)(\"mat-mdc-option-active\", ctx.active)(\"mdc-list-item--disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        value: \"value\",\n        id: \"id\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        onSelectionChange: \"onSelectionChange\"\n      },\n      exportAs: [\"matOption\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c4,\n      decls: 8,\n      vars: 5,\n      consts: [[\"text\", \"\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\", \"state\"], [1, \"mdc-list-item__primary-text\"], [\"state\", \"checked\", \"aria-hidden\", \"true\", \"appearance\", \"minimal\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\"], [1, \"cdk-visually-hidden\"], [\"aria-hidden\", \"true\", \"mat-ripple\", \"\", 1, \"mat-mdc-option-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\"]],\n      template: function MatOption_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵtemplate(0, MatOption_Conditional_0_Template, 1, 2, \"mat-pseudo-checkbox\", 1);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementStart(2, \"span\", 2, 0);\n          i0.ɵɵprojection(4, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, MatOption_Conditional_5_Template, 1, 1, \"mat-pseudo-checkbox\", 3)(6, MatOption_Conditional_6_Template, 2, 1, \"span\", 4);\n          i0.ɵɵelement(7, \"div\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.multiple ? 0 : -1);\n          i0.ɵɵadvance(5);\n          i0.ɵɵconditional(5, !ctx.multiple && ctx.selected && !ctx.hideSingleSelectionIndicator ? 5 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(6, ctx.group && ctx.group._inert ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx.disabled || ctx.disableRipple);\n        }\n      },\n      dependencies: [MatPseudoCheckbox, MatRipple],\n      styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color);font-family:var(--mat-option-label-text-font);line-height:var(--mat-option-label-text-line-height);font-size:var(--mat-option-label-text-size);letter-spacing:var(--mat-option-label-text-tracking);font-weight:var(--mat-option-label-text-weight);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color)}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color)}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOption, [{\n    type: Component,\n    args: [{\n      selector: 'mat-option',\n      exportAs: 'matOption',\n      host: {\n        'role': 'option',\n        '[class.mdc-list-item--selected]': 'selected',\n        '[class.mat-mdc-option-multiple]': 'multiple',\n        '[class.mat-mdc-option-active]': 'active',\n        '[class.mdc-list-item--disabled]': 'disabled',\n        '[id]': 'id',\n        // Set aria-selected to false for non-selected items and true for selected items. Conform to\n        // [WAI ARIA Listbox authoring practices guide](\n        //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n        // selected option has either aria-selected or aria-checked  set to true. All options that are\n        // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n        // aria-selected implementation of Chips and List components.\n        //\n        // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n        // every option as \"selected\" (#21491).\n        '[attr.aria-selected]': 'selected',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '(click)': '_selectViaInteraction()',\n        '(keydown)': '_handleKeydown($event)',\n        'class': 'mat-mdc-option mdc-list-item'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      imports: [MatPseudoCheckbox, MatRipple],\n      template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n@if (multiple) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n        aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n}\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        state=\\\"checked\\\"\\n        aria-hidden=\\\"true\\\"\\n        appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n}\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n@if (group && group._inert) {\\n    <span class=\\\"cdk-visually-hidden\\\">({{ group.label }})</span>\\n}\\n\\n<div class=\\\"mat-mdc-option-ripple mat-mdc-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\",\n      styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color);font-family:var(--mat-option-label-text-font);line-height:var(--mat-option-label-text-line-height);font-size:var(--mat-option-label-text-size);letter-spacing:var(--mat-option-label-text-tracking);font-weight:var(--mat-option-label-text-weight);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color)}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color)}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_OPTION_PARENT_COMPONENT]\n    }]\n  }, {\n    type: MatOptgroup,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_OPTGROUP]\n    }]\n  }], {\n    value: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    onSelectionChange: [{\n      type: Output\n    }],\n    _text: [{\n      type: ViewChild,\n      args: ['text', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n  if (optionGroups.length) {\n    let optionsArray = options.toArray();\n    let groups = optionGroups.toArray();\n    let groupCounter = 0;\n    for (let i = 0; i < optionIndex + 1; i++) {\n      if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n        groupCounter++;\n      }\n    }\n    return groupCounter;\n  }\n  return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n  if (optionOffset < currentScrollPosition) {\n    return optionOffset;\n  }\n  if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n    return Math.max(0, optionOffset - panelHeight + optionHeight);\n  }\n  return currentScrollPosition;\n}\nclass MatOptionModule {\n  static {\n    this.ɵfac = function MatOptionModule_Factory(t) {\n      return new (t || MatOptionModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatOptionModule,\n      imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n      exports: [MatOption, MatOptgroup]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n      exports: [MatOption, MatOptgroup]\n    }]\n  }], null, null);\n})();\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = {\n  capture: true\n};\n/** The events that should trigger the initialization of the ripple. */\nconst rippleInteractionEvents = ['focus', 'click', 'mouseenter', 'touchstart'];\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\nclass MatRippleLoader {\n  constructor() {\n    this._document = inject(DOCUMENT, {\n      optional: true\n    });\n    this._animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    this._globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    });\n    this._platform = inject(Platform);\n    this._ngZone = inject(NgZone);\n    this._hosts = new Map();\n    /** Handles creating and attaching component internals when a component it is initially interacted with. */\n    this._onInteraction = event => {\n      if (!(event.target instanceof HTMLElement)) {\n        return;\n      }\n      const eventTarget = event.target;\n      // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n      const element = eventTarget.closest(`[${matRippleUninitialized}]`);\n      if (element) {\n        this._createRipple(element);\n      }\n    };\n    this._ngZone.runOutsideAngular(() => {\n      for (const event of rippleInteractionEvents) {\n        this._document?.addEventListener(event, this._onInteraction, eventListenerOptions);\n      }\n    });\n  }\n  ngOnDestroy() {\n    const hosts = this._hosts.keys();\n    for (const host of hosts) {\n      this.destroyRipple(host);\n    }\n    for (const event of rippleInteractionEvents) {\n      this._document?.removeEventListener(event, this._onInteraction, eventListenerOptions);\n    }\n  }\n  /**\n   * Configures the ripple that will be rendered by the ripple loader.\n   *\n   * Stores the given information about how the ripple should be configured on the host\n   * element so that it can later be retrived & used when the ripple is actually created.\n   */\n  configureRipple(host, config) {\n    // Indicates that the ripple has not yet been rendered for this component.\n    host.setAttribute(matRippleUninitialized, '');\n    // Store the additional class name(s) that should be added to the ripple element.\n    if (config.className || !host.hasAttribute(matRippleClassName)) {\n      host.setAttribute(matRippleClassName, config.className || '');\n    }\n    // Store whether the ripple should be centered.\n    if (config.centered) {\n      host.setAttribute(matRippleCentered, '');\n    }\n    if (config.disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    }\n  }\n  /** Returns the ripple instance for the given host element. */\n  getRipple(host) {\n    const ripple = this._hosts.get(host);\n    return ripple || this._createRipple(host);\n  }\n  /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n  setDisabled(host, disabled) {\n    const ripple = this._hosts.get(host);\n    // If the ripple has already been instantiated, just disable it.\n    if (ripple) {\n      ripple.disabled = disabled;\n      return;\n    }\n    // Otherwise, set an attribute so we know what the\n    // disabled state should be when the ripple is initialized.\n    if (disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    } else {\n      host.removeAttribute(matRippleDisabled);\n    }\n  }\n  /** Creates a MatRipple and appends it to the given element. */\n  _createRipple(host) {\n    if (!this._document) {\n      return;\n    }\n    const existingRipple = this._hosts.get(host);\n    if (existingRipple) {\n      return existingRipple;\n    }\n    // Create the ripple element.\n    host.querySelector('.mat-ripple')?.remove();\n    const rippleEl = this._document.createElement('span');\n    rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName));\n    host.append(rippleEl);\n    // Create the MatRipple.\n    const ripple = new MatRipple(new ElementRef(rippleEl), this._ngZone, this._platform, this._globalRippleOptions ? this._globalRippleOptions : undefined, this._animationMode ? this._animationMode : undefined);\n    ripple._isInitialized = true;\n    ripple.trigger = host;\n    ripple.centered = host.hasAttribute(matRippleCentered);\n    ripple.disabled = host.hasAttribute(matRippleDisabled);\n    this.attachRipple(host, ripple);\n    return ripple;\n  }\n  attachRipple(host, ripple) {\n    host.removeAttribute(matRippleUninitialized);\n    this._hosts.set(host, ripple);\n  }\n  destroyRipple(host) {\n    const ripple = this._hosts.get(host);\n    if (ripple) {\n      // Since this directive is created manually, it needs to be destroyed manually too.\n      // tslint:disable-next-line:no-lifecycle-invocation\n      ripple.ngOnDestroy();\n      this._hosts.delete(host);\n    }\n  }\n  static {\n    this.ɵfac = function MatRippleLoader_Factory(t) {\n      return new (t || MatRippleLoader)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatRippleLoader,\n      factory: MatRippleLoader.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Internal shared component used as a container in form field controls.\n * Not to be confused with `mat-form-field` which MDC calls a \"text field\".\n * @docs-private\n */\nclass _MatInternalFormField {\n  static {\n    this.ɵfac = function _MatInternalFormField_Factory(t) {\n      return new (t || _MatInternalFormField)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _MatInternalFormField,\n      selectors: [[\"div\", \"mat-internal-form-field\", \"\"]],\n      hostAttrs: [1, \"mdc-form-field\", \"mat-internal-form-field\"],\n      hostVars: 2,\n      hostBindings: function _MatInternalFormField_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-form-field--align-end\", ctx.labelPosition === \"before\");\n        }\n      },\n      inputs: {\n        labelPosition: \"labelPosition\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c5,\n      ngContentSelectors: _c6,\n      decls: 1,\n      vars: 0,\n      template: function _MatInternalFormField_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\".mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mdc-form-field{font-family:var(--mdc-form-field-label-text-font);line-height:var(--mdc-form-field-label-text-line-height);font-size:var(--mdc-form-field-label-text-size);font-weight:var(--mdc-form-field-label-text-weight);letter-spacing:var(--mdc-form-field-label-text-tracking);color:var(--mdc-form-field-label-text-color)}.mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatInternalFormField, [{\n    type: Component,\n    args: [{\n      selector: 'div[mat-internal-form-field]',\n      standalone: true,\n      template: '<ng-content></ng-content>',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mdc-form-field mat-internal-form-field',\n        '[class.mdc-form-field--align-end]': 'labelPosition === \"before\"'\n      },\n      styles: [\".mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mdc-form-field{font-family:var(--mdc-form-field-label-text-font);line-height:var(--mdc-form-field-label-text-line-height);font-size:var(--mdc-form-field-label-text-size);font-weight:var(--mdc-form-field-label-text-weight);letter-spacing:var(--mdc-form-field-label-text-tracking);color:var(--mdc-form-field-label-text-color)}.mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}\"]\n    }]\n  }], null, {\n    labelPosition: [{\n      type: Input,\n      args: [{\n        required: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AnimationCurves, AnimationDurations, DateAdapter, ErrorStateMatcher, MATERIAL_SANITY_CHECKS, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_DATE_LOCALE_FACTORY, MAT_NATIVE_DATE_FORMATS, MAT_OPTGROUP, MAT_OPTION_PARENT_COMPONENT, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatLine, MatLineModule, MatNativeDateModule, MatOptgroup, MatOption, MatOptionModule, MatOptionSelectionChange, MatPseudoCheckbox, MatPseudoCheckboxModule, MatRipple, MatRippleLoader, MatRippleModule, NativeDateAdapter, NativeDateModule, RippleRef, RippleRenderer, RippleState, ShowOnDirtyErrorStateMatcher, VERSION, _ErrorStateTracker, _MatInternalFormField, _countGroupLabelsBeforeOption, _getOptionScrollPosition, defaultRippleAnimationConfig, mixinColor, mixinDisableRipple, mixinDisabled, mixinErrorState, mixinInitialized, mixinTabIndex, provideNativeDateAdapter, setLines };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,UAAU,IAAI,QAAQ,SAAS;;;ACYrC,IAAM,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,GAAG,CAAC,cAAc,CAAC,CAAC;AACpD,IAAM,MAAM,CAAC,KAAK,0BAA0B;AAC5C,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG;AAChC,IAAM,MAAM,CAAC,YAAY,GAAG;AAC5B,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,uBAAuB,CAAC;AAAA,EAC1C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,QAAQ,EAAE,SAAS,OAAO,WAAW,YAAY,WAAW;AAAA,EAC/F;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,uBAAuB,CAAC;AAAA,EAC1C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,QAAQ;AAAA,EAC3C;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,MAAM,OAAO,GAAG;AAAA,EACpD;AACF;AACA,IAAM,MAAM,CAAC,2BAA2B,EAAE;AAC1C,IAAM,MAAM,CAAC,GAAG;AAChB,IAAMA,WAAU,IAAI,QAAQ,SAAS;AAGrC,IAAM,kBAAN,MAAsB;AAAA,EACpB,OAAO;AACL,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,cAAc;AAAA,EACrB;AACF;AAEA,IAAM,qBAAN,MAAyB;AAAA,EACvB,OAAO;AACL,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO;AACL,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,UAAU;AAAA,EACjB;AACF;AAGA,SAAS,iCAAiC;AACxC,SAAO;AACT;AAEA,IAAM,yBAAyB,IAAI,eAAe,qBAAqB;AAAA,EACrE,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAOD,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,0BAA0B,eAAe,WAAW;AAC9D,SAAK,gBAAgB;AACrB,SAAK,YAAY;AAEjB,SAAK,uBAAuB;AAG5B,6BAAyB,qCAAqC;AAC9D,QAAI,CAAC,KAAK,sBAAsB;AAC9B,WAAK,uBAAuB;AAC5B,UAAI,OAAO,cAAc,eAAe,WAAW;AAEjD,cAAM,WAAW,OAAO,UAAU;AAAA,UAChC,UAAU;AAAA,QACZ,CAAC;AACD,YAAI,KAAK,gBAAgB,SAAS,GAAG;AACnC,iCAAuB,KAAK,SAAS;AAAA,QACvC;AACA,YAAI,KAAK,gBAAgB,OAAO,GAAG;AACjC,+BAAqB,KAAK,WAAW,CAAC,CAAC,UAAU,SAAS;AAAA,QAC5D;AACA,YAAI,KAAK,gBAAgB,SAAS,GAAG;AACnC,gCAAsB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,MAAM;AACpB,QAAI,mBAAmB,GAAG;AACxB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,KAAK,kBAAkB,WAAW;AAC3C,aAAO,KAAK;AAAA,IACd;AACA,WAAO,CAAC,CAAC,KAAK,cAAc,IAAI;AAAA,EAClC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAoB,SAAY,wBAAwB,GAAM,SAAS,wBAAwB,CAAC,GAAM,SAAS,QAAQ,CAAC;AAAA,IAC3I;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,UAAU;AAAA,MACpB,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,YAAY,UAAU;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU;AAAA,MACpB,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAEH,SAAS,uBAAuB,KAAK;AACnC,MAAI,CAAC,IAAI,SAAS;AAChB,YAAQ,KAAK,sHAA2H;AAAA,EAC1I;AACF;AAEA,SAAS,qBAAqB,KAAK,WAAW;AAG5C,MAAI,CAAC,IAAI,QAAQ,CAAC,WAAW;AAC3B;AAAA,EACF;AACA,QAAM,cAAc,IAAI,cAAc,KAAK;AAC3C,cAAY,UAAU,IAAI,yBAAyB;AACnD,MAAI,KAAK,YAAY,WAAW;AAChC,QAAM,gBAAgB,iBAAiB,WAAW;AAIlD,MAAI,iBAAiB,cAAc,YAAY,QAAQ;AACrD,YAAQ,KAAK,oLAA8L;AAAA,EAC7M;AACA,cAAY,OAAO;AACrB;AAEA,SAAS,wBAAwB;AAC/B,MAAIA,SAAQ,SAAS,QAAU,MAAM;AACnC,YAAQ,KAAK,mCAAmCA,SAAQ,OAAO,+CAAoD,QAAU,OAAO,qEAA0E;AAAA,EAChN;AACF;AA4EA,IAAM,qBAAN,MAAyB;AAAA,EACvB,YAAY,iBAAiB,WAAW,kBAAkB,aAAa,eAAe;AACpF,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AAErB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,mBAAmB;AACjB,UAAM,WAAW,KAAK;AACtB,UAAM,SAAS,KAAK,oBAAoB,KAAK;AAC7C,UAAM,UAAU,KAAK,WAAW,KAAK;AACrC,UAAM,UAAU,KAAK,YAAY,KAAK,UAAU,UAAU;AAC1D,UAAM,WAAW,SAAS,aAAa,SAAS,MAAM,KAAK;AAC3D,QAAI,aAAa,UAAU;AACzB,WAAK,aAAa;AAClB,WAAK,cAAc,KAAK;AAAA,IAC1B;AAAA,EACF;AACF;AAsFA,IAAM,kBAAkB,IAAI,eAAe,mBAAmB;AAAA,EAC5D,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAED,SAAS,0BAA0B;AACjC,SAAO,OAAO,SAAS;AACzB;AAEA,IAAM,cAAN,MAAkB;AAAA,EAChB,cAAc;AACZ,SAAK,iBAAiB,IAAI,QAAQ;AAElC,SAAK,gBAAgB,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,KAAK;AACtB,WAAO,KAAK,eAAe,GAAG,KAAK,KAAK,QAAQ,GAAG,IAAI,MAAM;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,YAAY,OAAO;AACjB,QAAI,SAAS,QAAQ,KAAK,eAAe,KAAK,KAAK,KAAK,QAAQ,KAAK,GAAG;AACtE,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,QAAQ;AAChB,SAAK,SAAS;AACd,SAAK,eAAe,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,OAAO,QAAQ;AACzB,WAAO,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS,MAAM,KAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM;AAAA,EAChJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO,QAAQ;AACtB,QAAI,SAAS,QAAQ;AACnB,UAAI,aAAa,KAAK,QAAQ,KAAK;AACnC,UAAI,cAAc,KAAK,QAAQ,MAAM;AACrC,UAAI,cAAc,aAAa;AAC7B,eAAO,CAAC,KAAK,YAAY,OAAO,MAAM;AAAA,MACxC;AACA,aAAO,cAAc;AAAA,IACvB;AACA,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,MAAM,KAAK,KAAK;AACxB,QAAI,OAAO,KAAK,YAAY,MAAM,GAAG,IAAI,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,QAAI,OAAO,KAAK,YAAY,MAAM,GAAG,IAAI,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,mBAAmB,IAAI,eAAe,kBAAkB;AAO9D,IAAM,iBAAiB;AAEvB,SAAS,MAAM,QAAQ,eAAe;AACpC,QAAM,cAAc,MAAM,MAAM;AAChC,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,gBAAY,CAAC,IAAI,cAAc,CAAC;AAAA,EAClC;AACA,SAAO;AACT;AAEA,IAAM,oBAAN,MAAM,2BAA0B,YAAY;AAAA,EAC1C,YAKA,eAAe;AACb,UAAM;AAKN,SAAK,mBAAmB;AAExB,SAAK,iBAAiB,OAAO,iBAAiB;AAAA,MAC5C,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,kBAAkB,QAAW;AAC/B,WAAK,iBAAiB;AAAA,IACxB;AACA,UAAM,UAAU,KAAK,cAAc;AAAA,EACrC;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,SAAS,MAAM;AACb,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,MAAM,IAAI,KAAK,eAAe,KAAK,QAAQ;AAAA,MAC/C,OAAO;AAAA,MACP,UAAU;AAAA,IACZ,CAAC;AACD,WAAO,MAAM,IAAI,OAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,MAAM,GAAG,CAAC,CAAC,CAAC;AAAA,EAC/D;AAAA,EACA,eAAe;AACb,UAAM,MAAM,IAAI,KAAK,eAAe,KAAK,QAAQ;AAAA,MAC/C,KAAK;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AACD,WAAO,MAAM,IAAI,OAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA,EACnE;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,MAAM,IAAI,KAAK,eAAe,KAAK,QAAQ;AAAA,MAC/C,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AACD,WAAO,MAAM,GAAG,OAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA,EAClE;AAAA,EACA,YAAY,MAAM;AAChB,UAAM,MAAM,IAAI,KAAK,eAAe,KAAK,QAAQ;AAAA,MAC/C,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,CAAC;AACD,WAAO,KAAK,QAAQ,KAAK,IAAI;AAAA,EAC/B;AAAA,EACA,oBAAoB;AAElB,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,MAAM;AACtB,WAAO,KAAK,QAAQ,KAAK,wBAAwB,KAAK,QAAQ,IAAI,GAAG,KAAK,SAAS,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,EAClG;AAAA,EACA,MAAM,MAAM;AACV,WAAO,IAAI,KAAK,KAAK,QAAQ,CAAC;AAAA,EAChC;AAAA,EACA,WAAW,MAAM,OAAO,MAAM;AAC5B,QAAI,OAAO,cAAc,eAAe,WAAW;AAGjD,UAAI,QAAQ,KAAK,QAAQ,IAAI;AAC3B,cAAM,MAAM,wBAAwB,KAAK,4CAA4C;AAAA,MACvF;AACA,UAAI,OAAO,GAAG;AACZ,cAAM,MAAM,iBAAiB,IAAI,mCAAmC;AAAA,MACtE;AAAA,IACF;AACA,QAAI,SAAS,KAAK,wBAAwB,MAAM,OAAO,IAAI;AAE3D,QAAI,OAAO,SAAS,KAAK,UAAU,OAAO,cAAc,eAAe,YAAY;AACjF,YAAM,MAAM,iBAAiB,IAAI,2BAA2B,KAAK,IAAI;AAAA,IACvE;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,WAAO,oBAAI,KAAK;AAAA,EAClB;AAAA,EACA,MAAM,OAAO,aAAa;AAGxB,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,IAAI,KAAK,KAAK;AAAA,IACvB;AACA,WAAO,QAAQ,IAAI,KAAK,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,EAC/C;AAAA,EACA,OAAO,MAAM,eAAe;AAC1B,QAAI,CAAC,KAAK,QAAQ,IAAI,GAAG;AACvB,YAAM,MAAM,gDAAgD;AAAA,IAC9D;AACA,UAAM,MAAM,IAAI,KAAK,eAAe,KAAK,QAAQ,iCAC5C,gBAD4C;AAAA,MAE/C,UAAU;AAAA,IACZ,EAAC;AACD,WAAO,KAAK,QAAQ,KAAK,IAAI;AAAA,EAC/B;AAAA,EACA,iBAAiB,MAAM,OAAO;AAC5B,WAAO,KAAK,kBAAkB,MAAM,QAAQ,EAAE;AAAA,EAChD;AAAA,EACA,kBAAkB,MAAM,QAAQ;AAC9B,QAAI,UAAU,KAAK,wBAAwB,KAAK,QAAQ,IAAI,GAAG,KAAK,SAAS,IAAI,IAAI,QAAQ,KAAK,QAAQ,IAAI,CAAC;AAK/G,QAAI,KAAK,SAAS,OAAO,OAAO,KAAK,SAAS,IAAI,IAAI,UAAU,KAAK,MAAM,IAAI;AAC7E,gBAAU,KAAK,wBAAwB,KAAK,QAAQ,OAAO,GAAG,KAAK,SAAS,OAAO,GAAG,CAAC;AAAA,IACzF;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,MAAM,MAAM;AAC1B,WAAO,KAAK,wBAAwB,KAAK,QAAQ,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,QAAQ,IAAI,IAAI,IAAI;AAAA,EACxG;AAAA,EACA,UAAU,MAAM;AACd,WAAO,CAAC,KAAK,eAAe,GAAG,KAAK,QAAQ,KAAK,YAAY,IAAI,CAAC,GAAG,KAAK,QAAQ,KAAK,WAAW,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,EAChH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,MACT;AAGA,UAAI,eAAe,KAAK,KAAK,GAAG;AAC9B,YAAI,OAAO,IAAI,KAAK,KAAK;AACzB,YAAI,KAAK,QAAQ,IAAI,GAAG;AACtB,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO,MAAM,YAAY,KAAK;AAAA,EAChC;AAAA,EACA,eAAe,KAAK;AAClB,WAAO,eAAe;AAAA,EACxB;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,CAAC,MAAM,KAAK,QAAQ,CAAC;AAAA,EAC9B;AAAA,EACA,UAAU;AACR,WAAO,oBAAI,KAAK,GAAG;AAAA,EACrB;AAAA;AAAA,EAEA,wBAAwB,MAAM,OAAO,MAAM;AAGzC,UAAM,IAAI,oBAAI,KAAK;AACnB,MAAE,YAAY,MAAM,OAAO,IAAI;AAC/B,MAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AACrB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,GAAG;AACT,YAAQ,OAAO,GAAG,MAAM,EAAE;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,QAAQ,KAAK,MAAM;AAGjB,UAAM,IAAI,oBAAI,KAAK;AACnB,MAAE,eAAe,KAAK,YAAY,GAAG,KAAK,SAAS,GAAG,KAAK,QAAQ,CAAC;AACpE,MAAE,YAAY,KAAK,SAAS,GAAG,KAAK,WAAW,GAAG,KAAK,WAAW,GAAG,KAAK,gBAAgB,CAAC;AAC3F,WAAO,IAAI,OAAO,CAAC;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,SAAS,iBAAiB,CAAC,CAAC;AAAA,IACrE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mBAAkB;AAAA,IAC7B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,0BAA0B;AAAA,EAC9B,OAAO;AAAA,IACL,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,WAAW;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA,IACA,oBAAoB;AAAA,MAClB,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAqB;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,yBAAyB,CAAC;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,yBAAyB,CAAC;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,yBAAyB,UAAU,yBAAyB;AACnE,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACH;AAGA,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,aAAa,SAAS,MAAM;AAC1B,WAAO,CAAC,EAAE,WAAW,QAAQ,YAAY,QAAQ,SAAS,QAAQ,KAAK;AAAA,EACzE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,GAAG;AAC3D,aAAO,KAAK,KAAK,+BAA8B;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,8BAA6B;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,aAAa,SAAS,MAAM;AAC1B,WAAO,CAAC,EAAE,WAAW,QAAQ,YAAY,QAAQ,WAAW,QAAQ,KAAK;AAAA,EAC3E;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAmB;AAAA,IACtC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mBAAkB;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,OAAO;AACL,SAAK,OAAO,SAAS,gBAAgB,GAAG;AACtC,aAAO,KAAK,KAAK,UAAS;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,YAAY,EAAE,GAAG,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MACrD,WAAW,CAAC,GAAG,UAAU;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAyBH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,OAAO;AAAA,MAClC,SAAS,CAAC,SAAS,eAAe;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,iBAAiB,eAAe;AAAA,IAC5C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,OAAO;AAAA,MAClC,SAAS,CAAC,SAAS,eAAe;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAI;AAAA,CACH,SAAUC,cAAa;AACtB,EAAAA,aAAYA,aAAY,WAAW,IAAI,CAAC,IAAI;AAC5C,EAAAA,aAAYA,aAAY,SAAS,IAAI,CAAC,IAAI;AAC1C,EAAAA,aAAYA,aAAY,YAAY,IAAI,CAAC,IAAI;AAC7C,EAAAA,aAAYA,aAAY,QAAQ,IAAI,CAAC,IAAI;AAC3C,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAIpC,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,WACZ,SACA,QACA,uCAAuC,OAAO;AAC5C,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,uCAAuC;AAE5C,SAAK,QAAQ,YAAY;AAAA,EAC3B;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,UAAU,cAAc,IAAI;AAAA,EACnC;AACF;AAGA,IAAM,iCAAiC,gCAAgC;AAAA,EACrE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,qBAAN,MAAyB;AAAA,EACvB,cAAc;AACZ,SAAK,UAAU,oBAAI,IAAI;AAEvB,SAAK,wBAAwB,WAAS;AACpC,YAAM,SAAS,gBAAgB,KAAK;AACpC,UAAI,QAAQ;AACV,aAAK,QAAQ,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,YAAY;AAC3D,cAAI,YAAY,UAAU,QAAQ,SAAS,MAAM,GAAG;AAClD,qBAAS,QAAQ,aAAW,QAAQ,YAAY,KAAK,CAAC;AAAA,UACxD;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,WAAW,QAAQ,MAAM,SAAS,SAAS;AACzC,UAAM,mBAAmB,KAAK,QAAQ,IAAI,IAAI;AAC9C,QAAI,kBAAkB;AACpB,YAAM,qBAAqB,iBAAiB,IAAI,OAAO;AACvD,UAAI,oBAAoB;AACtB,2BAAmB,IAAI,OAAO;AAAA,MAChC,OAAO;AACL,yBAAiB,IAAI,SAAS,oBAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AAAA,MAClD;AAAA,IACF,OAAO;AACL,WAAK,QAAQ,IAAI,MAAM,oBAAI,IAAI,CAAC,CAAC,SAAS,oBAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,aAAO,kBAAkB,MAAM;AAC7B,iBAAS,iBAAiB,MAAM,KAAK,uBAAuB,8BAA8B;AAAA,MAC5F,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,MAAM,SAAS,SAAS;AACpC,UAAM,mBAAmB,KAAK,QAAQ,IAAI,IAAI;AAC9C,QAAI,CAAC,kBAAkB;AACrB;AAAA,IACF;AACA,UAAM,qBAAqB,iBAAiB,IAAI,OAAO;AACvD,QAAI,CAAC,oBAAoB;AACvB;AAAA,IACF;AACA,uBAAmB,OAAO,OAAO;AACjC,QAAI,mBAAmB,SAAS,GAAG;AACjC,uBAAiB,OAAO,OAAO;AAAA,IACjC;AACA,QAAI,iBAAiB,SAAS,GAAG;AAC/B,WAAK,QAAQ,OAAO,IAAI;AACxB,eAAS,oBAAoB,MAAM,KAAK,uBAAuB,8BAA8B;AAAA,IAC/F;AAAA,EACF;AACF;AAMA,IAAM,+BAA+B;AAAA,EACnC,eAAe;AAAA,EACf,cAAc;AAChB;AAKA,IAAM,2BAA2B;AAEjC,IAAM,+BAA+B,gCAAgC;AAAA,EACnE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,oBAAoB,CAAC,aAAa,YAAY;AAEpD,IAAM,kBAAkB,CAAC,WAAW,cAAc,YAAY,aAAa;AAQ3E,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,gBAAgB,IAAI,mBAAmB;AAAA,EAC9C;AAAA,EACA,YAAY,SAAS,SAAS,qBAAqB,WAAW;AAC5D,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,YAAY;AAEjB,SAAK,iBAAiB;AAOtB,SAAK,iBAAiB,oBAAI,IAAI;AAE9B,SAAK,6BAA6B;AAElC,QAAI,UAAU,WAAW;AACvB,WAAK,oBAAoB,cAAc,mBAAmB;AAAA,IAC5D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,GAAG,GAAG,SAAS,CAAC,GAAG;AAC9B,UAAM,gBAAgB,KAAK,iBAAiB,KAAK,kBAAkB,KAAK,kBAAkB,sBAAsB;AAChH,UAAM,kBAAkB,kCACnB,+BACA,OAAO;AAEZ,QAAI,OAAO,UAAU;AACnB,UAAI,cAAc,OAAO,cAAc,QAAQ;AAC/C,UAAI,cAAc,MAAM,cAAc,SAAS;AAAA,IACjD;AACA,UAAM,SAAS,OAAO,UAAU,yBAAyB,GAAG,GAAG,aAAa;AAC5E,UAAM,UAAU,IAAI,cAAc;AAClC,UAAM,UAAU,IAAI,cAAc;AAClC,UAAM,gBAAgB,gBAAgB;AACtC,UAAM,SAAS,SAAS,cAAc,KAAK;AAC3C,WAAO,UAAU,IAAI,oBAAoB;AACzC,WAAO,MAAM,OAAO,GAAG,UAAU,MAAM;AACvC,WAAO,MAAM,MAAM,GAAG,UAAU,MAAM;AACtC,WAAO,MAAM,SAAS,GAAG,SAAS,CAAC;AACnC,WAAO,MAAM,QAAQ,GAAG,SAAS,CAAC;AAGlC,QAAI,OAAO,SAAS,MAAM;AACxB,aAAO,MAAM,kBAAkB,OAAO;AAAA,IACxC;AACA,WAAO,MAAM,qBAAqB,GAAG,aAAa;AAClD,SAAK,kBAAkB,YAAY,MAAM;AAKzC,UAAM,iBAAiB,OAAO,iBAAiB,MAAM;AACrD,UAAM,yBAAyB,eAAe;AAC9C,UAAM,yBAAyB,eAAe;AAM9C,UAAM,sCAAsC,2BAA2B;AAAA;AAAA,IAGvE,2BAA2B,QAAQ,2BAA2B;AAAA,IAE9D,cAAc,UAAU,KAAK,cAAc,WAAW;AAEtD,UAAM,YAAY,IAAI,UAAU,MAAM,QAAQ,QAAQ,mCAAmC;AAKzF,WAAO,MAAM,YAAY;AACzB,cAAU,QAAQ,YAAY;AAC9B,QAAI,CAAC,OAAO,YAAY;AACtB,WAAK,6BAA6B;AAAA,IACpC;AACA,QAAI,iBAAiB;AAGrB,QAAI,CAAC,wCAAwC,iBAAiB,gBAAgB,eAAe;AAC3F,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAM,kBAAkB,MAAM,KAAK,wBAAwB,SAAS;AACpE,cAAM,qBAAqB,MAAM,KAAK,eAAe,SAAS;AAC9D,eAAO,iBAAiB,iBAAiB,eAAe;AAIxD,eAAO,iBAAiB,oBAAoB,kBAAkB;AAC9D,yBAAiB;AAAA,UACf;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,SAAK,eAAe,IAAI,WAAW,cAAc;AAGjD,QAAI,uCAAuC,CAAC,eAAe;AACzD,WAAK,wBAAwB,SAAS;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc,WAAW;AAEvB,QAAI,UAAU,UAAU,YAAY,cAAc,UAAU,UAAU,YAAY,QAAQ;AACxF;AAAA,IACF;AACA,UAAM,WAAW,UAAU;AAC3B,UAAM,kBAAkB,kCACnB,+BACA,UAAU,OAAO;AAItB,aAAS,MAAM,qBAAqB,GAAG,gBAAgB,YAAY;AACnE,aAAS,MAAM,UAAU;AACzB,cAAU,QAAQ,YAAY;AAG9B,QAAI,UAAU,wCAAwC,CAAC,gBAAgB,cAAc;AACnF,WAAK,wBAAwB,SAAS;AAAA,IACxC;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,SAAK,kBAAkB,EAAE,QAAQ,YAAU,OAAO,QAAQ,CAAC;AAAA,EAC7D;AAAA;AAAA,EAEA,0BAA0B;AACxB,SAAK,kBAAkB,EAAE,QAAQ,YAAU;AACzC,UAAI,CAAC,OAAO,OAAO,YAAY;AAC7B,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,mBAAmB,qBAAqB;AACtC,UAAM,UAAU,cAAc,mBAAmB;AACjD,QAAI,CAAC,KAAK,UAAU,aAAa,CAAC,WAAW,YAAY,KAAK,iBAAiB;AAC7E;AAAA,IACF;AAEA,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB;AAGvB,sBAAkB,QAAQ,UAAQ;AAChC,sBAAe,cAAc,WAAW,KAAK,SAAS,MAAM,SAAS,IAAI;AAAA,IAC3E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,OAAO;AACjB,QAAI,MAAM,SAAS,aAAa;AAC9B,WAAK,aAAa,KAAK;AAAA,IACzB,WAAW,MAAM,SAAS,cAAc;AACtC,WAAK,cAAc,KAAK;AAAA,IAC1B,OAAO;AACL,WAAK,aAAa;AAAA,IACpB;AAIA,QAAI,CAAC,KAAK,4BAA4B;AAMpC,WAAK,QAAQ,kBAAkB,MAAM;AACnC,wBAAgB,QAAQ,UAAQ;AAC9B,eAAK,gBAAgB,iBAAiB,MAAM,MAAM,4BAA4B;AAAA,QAChF,CAAC;AAAA,MACH,CAAC;AACD,WAAK,6BAA6B;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB,WAAW;AACjC,QAAI,UAAU,UAAU,YAAY,WAAW;AAC7C,WAAK,wBAAwB,SAAS;AAAA,IACxC,WAAW,UAAU,UAAU,YAAY,YAAY;AACrD,WAAK,eAAe,SAAS;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,WAAW;AACjC,UAAM,8BAA8B,cAAc,KAAK;AACvD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,UAAU;AACd,cAAU,QAAQ,YAAY;AAK9B,QAAI,CAAC,eAAe,CAAC,+BAA+B,CAAC,KAAK,iBAAiB;AACzE,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,WAAW;AACxB,UAAM,iBAAiB,KAAK,eAAe,IAAI,SAAS,KAAK;AAC7D,SAAK,eAAe,OAAO,SAAS;AAEpC,QAAI,CAAC,KAAK,eAAe,MAAM;AAC7B,WAAK,iBAAiB;AAAA,IACxB;AAGA,QAAI,cAAc,KAAK,4BAA4B;AACjD,WAAK,6BAA6B;AAAA,IACpC;AACA,cAAU,QAAQ,YAAY;AAC9B,QAAI,mBAAmB,MAAM;AAC3B,gBAAU,QAAQ,oBAAoB,iBAAiB,eAAe,eAAe;AACrF,gBAAU,QAAQ,oBAAoB,oBAAoB,eAAe,kBAAkB;AAAA,IAC7F;AACA,cAAU,QAAQ,OAAO;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa,OAAO;AAGlB,UAAM,kBAAkB,gCAAgC,KAAK;AAC7D,UAAM,mBAAmB,KAAK,wBAAwB,KAAK,IAAI,IAAI,KAAK,uBAAuB;AAC/F,QAAI,CAAC,KAAK,QAAQ,kBAAkB,CAAC,mBAAmB,CAAC,kBAAkB;AACzE,WAAK,iBAAiB;AACtB,WAAK,aAAa,MAAM,SAAS,MAAM,SAAS,KAAK,QAAQ,YAAY;AAAA,IAC3E;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,QAAQ,kBAAkB,CAAC,iCAAiC,KAAK,GAAG;AAI5E,WAAK,uBAAuB,KAAK,IAAI;AACrC,WAAK,iBAAiB;AAGtB,YAAM,UAAU,MAAM;AAGtB,UAAI,SAAS;AACX,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,eAAK,aAAa,QAAQ,CAAC,EAAE,SAAS,QAAQ,CAAC,EAAE,SAAS,KAAK,QAAQ,YAAY;AAAA,QACrF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,eAAe;AACb,QAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,IACF;AACA,SAAK,iBAAiB;AAEtB,SAAK,kBAAkB,EAAE,QAAQ,YAAU;AAGzC,YAAM,YAAY,OAAO,UAAU,YAAY,WAAW,OAAO,OAAO,wBAAwB,OAAO,UAAU,YAAY;AAC7H,UAAI,CAAC,OAAO,OAAO,cAAc,WAAW;AAC1C,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,WAAO,MAAM,KAAK,KAAK,eAAe,KAAK,CAAC;AAAA,EAC9C;AAAA;AAAA,EAEA,uBAAuB;AACrB,UAAM,UAAU,KAAK;AACrB,QAAI,SAAS;AACX,wBAAkB,QAAQ,UAAQ,gBAAe,cAAc,cAAc,MAAM,SAAS,IAAI,CAAC;AACjG,UAAI,KAAK,4BAA4B;AACnC,wBAAgB,QAAQ,UAAQ,QAAQ,oBAAoB,MAAM,MAAM,4BAA4B,CAAC;AACrG,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACF;AAIA,SAAS,yBAAyB,GAAG,GAAG,MAAM;AAC5C,QAAM,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC;AACxE,QAAM,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,IAAI,IAAI,KAAK,MAAM,CAAC;AACxE,SAAO,KAAK,KAAK,QAAQ,QAAQ,QAAQ,KAAK;AAChD;AAGA,IAAM,4BAA4B,IAAI,eAAe,2BAA2B;AAChF,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,OAAO;AACT,WAAK,wBAAwB;AAAA,IAC/B;AACA,SAAK,YAAY;AACjB,SAAK,6BAA6B;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACZ,WAAO,KAAK,YAAY,KAAK,YAAY;AAAA,EAC3C;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAChB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,YAAY,aAAa,QAAQ,UAAU,eAAe,gBAAgB;AACxE,SAAK,cAAc;AACnB,SAAK,iBAAiB;AAMtB,SAAK,SAAS;AACd,SAAK,YAAY;AAEjB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB,iBAAiB,CAAC;AACxC,SAAK,kBAAkB,IAAI,eAAe,MAAM,QAAQ,aAAa,QAAQ;AAAA,EAC/E;AAAA,EACA,WAAW;AACT,SAAK,iBAAiB;AACtB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,qBAAqB;AAAA,EAC5C;AAAA;AAAA,EAEA,aAAa;AACX,SAAK,gBAAgB,WAAW;AAAA,EAClC;AAAA;AAAA,EAEA,0BAA0B;AACxB,SAAK,gBAAgB,wBAAwB;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,eAAe;AACjB,WAAO;AAAA,MACL,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,WAAW,iDACN,KAAK,eAAe,YACnB,KAAK,mBAAmB,mBAAmB;AAAA,QAC7C,eAAe;AAAA,QACf,cAAc;AAAA,MAChB,IAAI,CAAC,IACF,KAAK;AAAA,MAEV,sBAAsB,KAAK,eAAe;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,iBAAiB;AACnB,WAAO,KAAK,YAAY,CAAC,CAAC,KAAK,eAAe;AAAA,EAChD;AAAA;AAAA,EAEA,+BAA+B;AAC7B,QAAI,CAAC,KAAK,YAAY,KAAK,gBAAgB;AACzC,WAAK,gBAAgB,mBAAmB,KAAK,OAAO;AAAA,IACtD;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,WAAW,IAAI,GAAG,QAAQ;AAC/B,QAAI,OAAO,cAAc,UAAU;AACjC,aAAO,KAAK,gBAAgB,aAAa,WAAW,GAAG,kCAClD,KAAK,eACL,OACJ;AAAA,IACH,OAAO;AACL,aAAO,KAAK,gBAAgB,aAAa,GAAG,GAAG,kCAC1C,KAAK,eACL,UACJ;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kBAAkB,GAAG;AACxC,aAAO,KAAK,KAAK,YAAc,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAuB,QAAQ,GAAM,kBAAkB,2BAA2B,CAAC,GAAM,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,IAC3O;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,GAAG,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,MACzD,WAAW,CAAC,GAAG,YAAY;AAAA,MAC3B,UAAU;AAAA,MACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,wBAAwB,IAAI,SAAS;AAAA,QACtD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO,CAAI,WAAa,MAAM,kBAAkB,OAAO;AAAA,QACvD,WAAW,CAAI,WAAa,MAAM,sBAAsB,WAAW;AAAA,QACnE,UAAU,CAAI,WAAa,MAAM,qBAAqB,UAAU;AAAA,QAChE,QAAQ,CAAI,WAAa,MAAM,mBAAmB,QAAQ;AAAA,QAC1D,WAAW,CAAI,WAAa,MAAM,sBAAsB,WAAW;AAAA,QACnE,UAAU,CAAI,WAAa,MAAM,qBAAqB,UAAU;AAAA,QAChE,SAAS,CAAI,WAAa,MAAM,oBAAoB,SAAS;AAAA,MAC/D;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,gCAAgC;AAAA,MAClC;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,SAAS;AAAA,MACpC,SAAS,CAAC,WAAW,eAAe;AAAA,IACtC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,iBAAiB,eAAe;AAAA,IAC5C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,SAAS;AAAA,MACpC,SAAS,CAAC,WAAW,eAAe;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAeH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,gBAAgB;AAC1B,SAAK,iBAAiB;AAEtB,SAAK,QAAQ;AAEb,SAAK,WAAW;AAKhB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,IACpF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,MACnC,WAAW,CAAC,GAAG,qBAAqB;AAAA,MACpC,UAAU;AAAA,MACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,qCAAqC,IAAI,UAAU,eAAe,EAAE,+BAA+B,IAAI,UAAU,SAAS,EAAE,gCAAgC,IAAI,QAAQ,EAAE,+BAA+B,IAAI,eAAe,SAAS,EAAE,4BAA4B,IAAI,eAAe,MAAM,EAAE,2BAA2B,IAAI,mBAAmB,gBAAgB;AAAA,QACjX;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,MACd;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AAAA,MAAC;AAAA,MACxD,QAAQ,CAAC,u9FAAy9F;AAAA,MACl+F,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,6CAA6C;AAAA,QAC7C,uCAAuC;AAAA,QACvC,wCAAwC;AAAA,QACxC,uCAAuC;AAAA,QACvC,oCAAoC;AAAA,QACpC,mCAAmC;AAAA,MACrC;AAAA,MACA,YAAY;AAAA,MACZ,QAAQ,CAAC,u9FAAy9F;AAAA,IACp+F,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAAyB;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,iBAAiB;AAAA,MAC5C,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,eAAe;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB;AAAA,MAC5C,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,8BAA8B,IAAI,eAAe,6BAA6B;AAsBpF,IAAI,2BAA2B;AAM/B,IAAM,eAAe,IAAI,eAAe,aAAa;AAIrD,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,YAAY,QAAQ;AAElB,SAAK,WAAW;AAEhB,SAAK,WAAW,sBAAsB,0BAA0B;AAChE,SAAK,SAAS,QAAQ,eAAe;AAAA,EACvC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,GAAG;AAC1C,aAAO,KAAK,KAAK,cAAgB,kBAAkB,6BAA6B,CAAC,CAAC;AAAA,IACpF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,MAC5B,WAAW,CAAC,GAAG,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,QAAQ,IAAI,SAAS,OAAO,OAAO,EAAE,iBAAiB,IAAI,SAAS,OAAO,IAAI,SAAS,SAAS,CAAC,EAAE,mBAAmB,IAAI,SAAS,OAAO,IAAI,QAAQ;AAAA,QACvK;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,gBAAgB;AAAA,MACjG;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,0BAA6B,mBAAmB;AAAA,MACxD,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,QAAQ,gBAAgB,GAAG,0BAA0B,GAAG,IAAI,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,MAC3G,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB,GAAG;AACtB,UAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC5C,UAAG,OAAO,CAAC;AACX,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa,EAAE;AAClB,UAAG,aAAa,GAAG,CAAC;AAAA,QACtB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,2BAA2B,IAAI,QAAQ;AACtD,UAAG,WAAW,MAAM,IAAI,QAAQ;AAChC,UAAG,UAAU,CAAC;AACd,UAAG,mBAAmB,IAAI,IAAI,OAAO,GAAG;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,45BAA45B;AAAA,MACr6B,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,0BAA0B;AAAA,MAC5B;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ,CAAC,45BAA45B;AAAA,IACv6B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAI,mBAAmB;AAEvB,IAAM,2BAAN,MAA+B;AAAA,EAC7B,YACA,QACA,cAAc,OAAO;AACnB,SAAK,SAAS;AACd,SAAK,cAAc;AAAA,EACrB;AACF;AAIA,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA,EAEd,IAAI,WAAW;AACb,WAAO,KAAK,WAAW,KAAK,QAAQ;AAAA,EACtC;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,SAAS,KAAK,MAAM,YAAY,KAAK;AAAA,EACnD;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,CAAC,EAAE,KAAK,WAAW,KAAK,QAAQ;AAAA,EACzC;AAAA;AAAA,EAEA,IAAI,+BAA+B;AACjC,WAAO,CAAC,EAAE,KAAK,WAAW,KAAK,QAAQ;AAAA,EACzC;AAAA,EACA,YAAY,UAAU,oBAAoB,SAAS,OAAO;AACxD,SAAK,WAAW;AAChB,SAAK,qBAAqB;AAC1B,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,uBAAuB;AAE5B,SAAK,KAAK,cAAc,kBAAkB;AAG1C,SAAK,oBAAoB,IAAI,aAAa;AAE1C,SAAK,gBAAgB,IAAI,QAAQ;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AAEd,YAAQ,KAAK,OAAO,cAAc,eAAe,IAAI,KAAK;AAAA,EAC5D;AAAA;AAAA,EAEA,OAAO,YAAY,MAAM;AACvB,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY;AACjB,WAAK,mBAAmB,aAAa;AACrC,UAAI,WAAW;AACb,aAAK,0BAA0B;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,SAAS,YAAY,MAAM;AACzB,QAAI,KAAK,WAAW;AAClB,WAAK,YAAY;AACjB,WAAK,mBAAmB,aAAa;AACrC,UAAI,WAAW;AACb,aAAK,0BAA0B;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,MAAM,SAAS,SAAS;AAGtB,UAAM,UAAU,KAAK,gBAAgB;AACrC,QAAI,OAAO,QAAQ,UAAU,YAAY;AACvC,cAAQ,MAAM,OAAO;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AACf,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,QAAI,KAAK,SAAS;AAChB,WAAK,UAAU;AACf,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,SAAK,MAAM,YAAY,SAAS,MAAM,YAAY,UAAU,CAAC,eAAe,KAAK,GAAG;AAClF,WAAK,sBAAsB;AAE3B,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,YAAY,KAAK,WAAW,CAAC,KAAK,YAAY;AACnD,WAAK,mBAAmB,aAAa;AACrC,WAAK,0BAA0B,IAAI;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO,KAAK,WAAW,OAAO;AAAA,EAChC;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,qBAAqB;AAMnB,QAAI,KAAK,WAAW;AAClB,YAAM,YAAY,KAAK;AACvB,UAAI,cAAc,KAAK,sBAAsB;AAC3C,YAAI,KAAK,sBAAsB;AAC7B,eAAK,cAAc,KAAK;AAAA,QAC1B;AACA,aAAK,uBAAuB;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA;AAAA,EAEA,0BAA0B,cAAc,OAAO;AAC7C,SAAK,kBAAkB,KAAK,IAAI,yBAAyB,MAAM,WAAW,CAAC;AAAA,EAC7E;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kBAAkB,GAAG;AACxC,aAAO,KAAK,KAAK,YAAc,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,6BAA6B,CAAC,GAAM,kBAAkB,cAAc,CAAC,CAAC;AAAA,IAC1M;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,MAC1B,WAAW,SAAS,gBAAgB,IAAI,KAAK;AAC3C,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAAA,QAC9D;AAAA,MACF;AAAA,MACA,WAAW,CAAC,QAAQ,UAAU,GAAG,kBAAkB,eAAe;AAAA,MAClE,UAAU;AAAA,MACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,qCAAqC;AACnE,mBAAO,IAAI,sBAAsB;AAAA,UACnC,CAAC,EAAE,WAAW,SAAS,qCAAqC,QAAQ;AAClE,mBAAO,IAAI,eAAe,MAAM;AAAA,UAClC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,MAAM,IAAI,EAAE;AAC9B,UAAG,YAAY,iBAAiB,IAAI,QAAQ,EAAE,iBAAiB,IAAI,SAAS,SAAS,CAAC;AACtF,UAAG,YAAY,2BAA2B,IAAI,QAAQ,EAAE,2BAA2B,IAAI,QAAQ,EAAE,yBAAyB,IAAI,MAAM,EAAE,2BAA2B,IAAI,QAAQ;AAAA,QAC/K;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,IAAI;AAAA,QACJ,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,gBAAgB;AAAA,MACjG;AAAA,MACA,SAAS;AAAA,QACP,mBAAmB;AAAA,MACrB;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA6B,mBAAmB;AAAA,MAC9D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,eAAe,QAAQ,GAAG,kCAAkC,GAAG,YAAY,OAAO,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,SAAS,WAAW,eAAe,QAAQ,cAAc,WAAW,GAAG,kCAAkC,GAAG,UAAU,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,eAAe,QAAQ,cAAc,IAAI,GAAG,yBAAyB,2BAA2B,GAAG,oBAAoB,mBAAmB,CAAC;AAAA,MACnb,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB,GAAG;AACtB,UAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,uBAAuB,CAAC;AACjF,UAAG,aAAa,CAAC;AACjB,UAAG,eAAe,GAAG,QAAQ,GAAG,CAAC;AACjC,UAAG,aAAa,GAAG,CAAC;AACpB,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,uBAAuB,CAAC,EAAE,GAAG,kCAAkC,GAAG,GAAG,QAAQ,CAAC;AACvI,UAAG,UAAU,GAAG,OAAO,CAAC;AAAA,QAC1B;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,GAAG,IAAI,WAAW,IAAI,EAAE;AACzC,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,GAAG,CAAC,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,+BAA+B,IAAI,EAAE;AAC/F,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,SAAS,IAAI,MAAM,SAAS,IAAI,EAAE;AAC1D,UAAG,UAAU;AACb,UAAG,WAAW,oBAAoB,IAAI,gBAAgB,CAAC,EAAE,qBAAqB,IAAI,YAAY,IAAI,aAAa;AAAA,QACjH;AAAA,MACF;AAAA,MACA,cAAc,CAAC,mBAAmB,SAAS;AAAA,MAC3C,QAAQ,CAAC,6qGAAirG;AAAA,MAC1rG,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,mCAAmC;AAAA,QACnC,mCAAmC;AAAA,QACnC,iCAAiC;AAAA,QACjC,mCAAmC;AAAA,QACnC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUR,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,WAAW;AAAA,QACX,aAAa;AAAA,QACb,SAAS;AAAA,MACX;AAAA,MACA,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,SAAS,CAAC,mBAAmB,SAAS;AAAA,MACtC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,6qGAAirG;AAAA,IAC5rG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,SAAS,8BAA8B,aAAa,SAAS,cAAc;AACzE,MAAI,aAAa,QAAQ;AACvB,QAAI,eAAe,QAAQ,QAAQ;AACnC,QAAI,SAAS,aAAa,QAAQ;AAClC,QAAI,eAAe;AACnB,aAAS,IAAI,GAAG,IAAI,cAAc,GAAG,KAAK;AACxC,UAAI,aAAa,CAAC,EAAE,SAAS,aAAa,CAAC,EAAE,UAAU,OAAO,YAAY,GAAG;AAC3E;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AASA,SAAS,yBAAyB,cAAc,cAAc,uBAAuB,aAAa;AAChG,MAAI,eAAe,uBAAuB;AACxC,WAAO;AAAA,EACT;AACA,MAAI,eAAe,eAAe,wBAAwB,aAAa;AACrE,WAAO,KAAK,IAAI,GAAG,eAAe,cAAc,YAAY;AAAA,EAC9D;AACA,SAAO;AACT;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,iBAAiB,yBAAyB,WAAW,WAAW;AAAA,MAC3F,SAAS,CAAC,WAAW,WAAW;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,iBAAiB,iBAAiB,uBAAuB;AAAA,IACrE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB,yBAAyB,WAAW,WAAW;AAAA,MAC3F,SAAS,CAAC,WAAW,WAAW;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AACX;AAEA,IAAM,0BAA0B,CAAC,SAAS,SAAS,cAAc,YAAY;AAE7E,IAAM,yBAAyB;AAE/B,IAAM,qBAAqB;AAE3B,IAAM,oBAAoB;AAE1B,IAAM,oBAAoB;AAS1B,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,YAAY,OAAO,UAAU;AAAA,MAChC,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,iBAAiB,OAAO,uBAAuB;AAAA,MAClD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,uBAAuB,OAAO,2BAA2B;AAAA,MAC5D,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,YAAY,OAAO,QAAQ;AAChC,SAAK,UAAU,OAAO,MAAM;AAC5B,SAAK,SAAS,oBAAI,IAAI;AAEtB,SAAK,iBAAiB,WAAS;AAC7B,UAAI,EAAE,MAAM,kBAAkB,cAAc;AAC1C;AAAA,MACF;AACA,YAAM,cAAc,MAAM;AAE1B,YAAM,UAAU,YAAY,QAAQ,IAAI,sBAAsB,GAAG;AACjE,UAAI,SAAS;AACX,aAAK,cAAc,OAAO;AAAA,MAC5B;AAAA,IACF;AACA,SAAK,QAAQ,kBAAkB,MAAM;AACnC,iBAAW,SAAS,yBAAyB;AAC3C,aAAK,WAAW,iBAAiB,OAAO,KAAK,gBAAgB,oBAAoB;AAAA,MACnF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ,KAAK,OAAO,KAAK;AAC/B,eAAW,QAAQ,OAAO;AACxB,WAAK,cAAc,IAAI;AAAA,IACzB;AACA,eAAW,SAAS,yBAAyB;AAC3C,WAAK,WAAW,oBAAoB,OAAO,KAAK,gBAAgB,oBAAoB;AAAA,IACtF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,MAAM,QAAQ;AAE5B,SAAK,aAAa,wBAAwB,EAAE;AAE5C,QAAI,OAAO,aAAa,CAAC,KAAK,aAAa,kBAAkB,GAAG;AAC9D,WAAK,aAAa,oBAAoB,OAAO,aAAa,EAAE;AAAA,IAC9D;AAEA,QAAI,OAAO,UAAU;AACnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC;AACA,QAAI,OAAO,UAAU;AACnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC;AAAA,EACF;AAAA;AAAA,EAEA,UAAU,MAAM;AACd,UAAM,SAAS,KAAK,OAAO,IAAI,IAAI;AACnC,WAAO,UAAU,KAAK,cAAc,IAAI;AAAA,EAC1C;AAAA;AAAA,EAEA,YAAY,MAAM,UAAU;AAC1B,UAAM,SAAS,KAAK,OAAO,IAAI,IAAI;AAEnC,QAAI,QAAQ;AACV,aAAO,WAAW;AAClB;AAAA,IACF;AAGA,QAAI,UAAU;AACZ,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC,OAAO;AACL,WAAK,gBAAgB,iBAAiB;AAAA,IACxC;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,MAAM;AAClB,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,UAAM,iBAAiB,KAAK,OAAO,IAAI,IAAI;AAC3C,QAAI,gBAAgB;AAClB,aAAO;AAAA,IACT;AAEA,SAAK,cAAc,aAAa,GAAG,OAAO;AAC1C,UAAM,WAAW,KAAK,UAAU,cAAc,MAAM;AACpD,aAAS,UAAU,IAAI,cAAc,KAAK,aAAa,kBAAkB,CAAC;AAC1E,SAAK,OAAO,QAAQ;AAEpB,UAAM,SAAS,IAAI,UAAU,IAAI,WAAW,QAAQ,GAAG,KAAK,SAAS,KAAK,WAAW,KAAK,uBAAuB,KAAK,uBAAuB,QAAW,KAAK,iBAAiB,KAAK,iBAAiB,MAAS;AAC7M,WAAO,iBAAiB;AACxB,WAAO,UAAU;AACjB,WAAO,WAAW,KAAK,aAAa,iBAAiB;AACrD,WAAO,WAAW,KAAK,aAAa,iBAAiB;AACrD,SAAK,aAAa,MAAM,MAAM;AAC9B,WAAO;AAAA,EACT;AAAA,EACA,aAAa,MAAM,QAAQ;AACzB,SAAK,gBAAgB,sBAAsB;AAC3C,SAAK,OAAO,IAAI,MAAM,MAAM;AAAA,EAC9B;AAAA,EACA,cAAc,MAAM;AAClB,UAAM,SAAS,KAAK,OAAO,IAAI,IAAI;AACnC,QAAI,QAAQ;AAGV,aAAO,YAAY;AACnB,WAAK,OAAO,OAAO,IAAI;AAAA,IACzB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAOH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAAuB;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,OAAO,2BAA2B,EAAE,CAAC;AAAA,MAClD,WAAW,CAAC,GAAG,kBAAkB,yBAAyB;AAAA,MAC1D,UAAU;AAAA,MACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,6BAA6B,IAAI,kBAAkB,QAAQ;AAAA,QAC5E;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,eAAe;AAAA,MACjB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,o7CAAo7C;AAAA,MAC77C,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,qCAAqC;AAAA,MACvC;AAAA,MACA,QAAQ,CAAC,o7CAAo7C;AAAA,IAC/7C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;", "names": ["VERSION", "RippleState"]}