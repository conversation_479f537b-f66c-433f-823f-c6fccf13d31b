<!-- Agent Analytics Main Container -->
<div class="h-full flex flex-col" [ngClass]="isDarkMode ? 'bg-[var(--background-light-gray)]' : 'bg-gray-50'">

  <!-- Header Section -->
  <div class="flex-shrink-0 p-6 border-b"
    [ngClass]="isDarkMode ? 'border-[var(--hover-blue-gray)] bg-[var(--background-white)]' : 'border-gray-200 bg-white'">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3">
        <i class="ri-bar-chart-line text-2xl"
          [ngClass]="isDarkMode ? 'text-[var(--primary-purple)]' : 'text-[var(--primary-purple)]'"></i>
        <div>
          <h1 class="text-2xl font-semibold" [ngClass]="textClass">Agent Analytics</h1>
          <p class="mt-1" [ngClass]="secondaryTextClass">Monitor agent performance and recent activity</p>
        </div>
      </div>

      <div class="flex items-center gap-3">
        <button nz-button nzType="default" (click)="refreshData()">
          <i class="ri-refresh-line mr-2"></i>
          Refresh
        </button>
      </div>
    </div>
  </div>

  <!-- Tab Navigation -->
  <div class="flex-shrink-0 px-6 pt-4">
    <div class="flex space-x-1 border-b" [ngClass]="isDarkMode ? 'border-[var(--hover-blue-gray)]' : 'border-gray-200'">
      <button class="px-4 py-2 text-sm font-medium rounded-t-lg transition-colors" [ngClass]="{
          'bg-[var(--primary-purple)] text-white': activeTab === 'overview',
          'hover:bg-gray-100': activeTab !== 'overview' && !isDarkMode,
          'hover:bg-[var(--hover-blue-gray)]': activeTab !== 'overview' && isDarkMode,
          'text-gray-600': activeTab !== 'overview' && !isDarkMode,
          'text-[var(--text-medium-gray)]': activeTab !== 'overview' && isDarkMode
        }" (click)="onTabChange('overview')">
        Overview
      </button>
      <button class="px-4 py-2 text-sm font-medium rounded-t-lg transition-colors" [ngClass]="{
          'bg-[var(--primary-purple)] text-white': activeTab === 'performance',
          'hover:bg-gray-100': activeTab !== 'performance' && !isDarkMode,
          'hover:bg-[var(--hover-blue-gray)]': activeTab !== 'performance' && isDarkMode,
          'text-gray-600': activeTab !== 'performance' && !isDarkMode,
          'text-[var(--text-medium-gray)]': activeTab !== 'performance' && isDarkMode
        }" (click)="onTabChange('performance')">
        Performance
      </button>

    </div>
  </div>

  <!-- Content Area -->
  <div class="flex-1 p-6 overflow-auto">
    <div *ngIf="loading" class="flex justify-center items-center h-64">
      <nz-spin nzSize="large">
        <div class="text-center mt-4" [ngClass]="textClass">Loading agent analytics...</div>
      </nz-spin>
    </div>

    <!-- Error State -->
    <div *ngIf="!loading && hasError" class="flex items-center justify-center h-64">
      <div class="text-center">
        <i class="ri-error-warning-line text-4xl mb-4 text-red-500"></i>
        <h3 class="text-lg font-medium mb-2" [ngClass]="textClass">Failed to Load Analytics</h3>
        <p class="mb-4" [ngClass]="secondaryTextClass">Unable to connect to the analytics service</p>
        <button nz-button nzType="primary" (click)="refreshData()">
          <i class="ri-refresh-line mr-2"></i>
          Try Again
        </button>
      </div>
    </div>

    <!-- Overview Tab -->
    <div *ngIf="activeTab === 'overview' && !loading && !hasError">
      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <nz-card [nzBodyStyle]="{'padding': '20px'}" [ngClass]="cardClass">
          <nz-statistic nzTitle="Total Agents" [nzValue]="summary?.totalAgents || 0"
            [nzValueStyle]="{'color': isDarkMode ? 'var(--text-dark)' : '#1f2937'}">
          </nz-statistic>
        </nz-card>

        <nz-card [nzBodyStyle]="{'padding': '20px'}" [ngClass]="cardClass">
          <nz-statistic nzTitle="Active Agents" [nzValue]="summary?.activeAgents || 0"
            [nzValueStyle]="{'color': '#10A37F'}">
          </nz-statistic>
        </nz-card>

        <nz-card [nzBodyStyle]="{'padding': '20px'}" [ngClass]="cardClass">
          <nz-statistic nzTitle="Average Success Rate" [nzValue]="summary?.averagePerformance || 0" nzSuffix="%"
            [nzValueStyle]="{'color': '#6B46C1'}">
          </nz-statistic>
        </nz-card>
      </div>

      <!-- Recent Agent Activity -->
      <nz-card nzTitle="Recent Agent Activity" [ngClass]="cardClass">
        <div class="space-y-4">
          <div *ngFor="let agent of agents.slice(0, 8)"
            class="flex items-center justify-between py-3 border-b last:border-b-0"
            [ngClass]="isDarkMode ? 'border-[var(--hover-blue-gray)]' : 'border-gray-100'">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 rounded-full flex items-center justify-center bg-blue-100 text-blue-600">
                <i class="ri-robot-line"></i>
              </div>
              <div>
                <div class="font-medium" [ngClass]="textClass">{{ agent.agentName }}</div>
                <div class="text-sm" [ngClass]="secondaryTextClass">{{ agent.workArea }}</div>
              </div>
            </div>
            <div class="flex items-center gap-4">
              <div class="text-right">
                <div class="font-semibold" [ngClass]="getPerformanceTextColor(agent.successRate)">{{
                  agent.successRate }}%</div>
                <div class="text-sm" [ngClass]="secondaryTextClass">Success Rate</div>
              </div>
              <div class="text-right">
                <div class="font-semibold" [ngClass]="textClass">{{ agent.totalTasks }}</div>
                <div class="text-sm" [ngClass]="secondaryTextClass">Total Tasks</div>
              </div>
              <nz-tag [nzColor]="getStatusColor(agent.status || 'unknown')">{{ (agent.status || 'unknown') | titlecase
                }}</nz-tag>
            </div>
          </div>
        </div>
      </nz-card>
    </div>

    <!-- Performance Tab -->
    <div *ngIf="activeTab === 'performance' && !loading && !hasError">
      <nz-table #performanceTable [nzData]="agents" [nzPageSize]="15" [nzShowSizeChanger]="true">
        <thead>
          <tr>
            <th>Agent</th>
            <th>Work Area</th>
            <th>Total Tasks</th>
            <th>Success Rate</th>
            <th>Average Time</th>
            <th>Status</th>
            <th>Last Active</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let agent of performanceTable.data">
            <td>
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 rounded-full flex items-center justify-center bg-blue-100 text-blue-600">
                  <i class="ri-robot-line"></i>
                </div>
                <span class="font-medium" [ngClass]="textClass">{{ agent.agentName }}</span>
              </div>
            </td>
            <td>
              <span class="text-sm" [ngClass]="secondaryTextClass">{{ agent.workArea }}</span>
            </td>
            <td>
              <span class="font-medium" [ngClass]="textClass">{{ agent.totalTasks }}</span>
            </td>
            <td>
              <div class="flex items-center gap-2">
                <nz-progress [nzPercent]="agent.successRate" [nzStrokeColor]="getPerformanceColor(agent.successRate)"
                  nzSize="small" [nzShowInfo]="false" class="flex-1 max-w-[80px]">
                </nz-progress>
                <span class="text-sm font-medium" [ngClass]="getPerformanceTextColor(agent.successRate)">{{
                  agent.successRate }}%</span>
              </div>
            </td>
            <td>
              <span class="text-sm" [ngClass]="textClass">{{ formatDuration(agent.averageTime) }}</span>
            </td>
            <td>
              <nz-tag [nzColor]="getStatusColor(agent.status || 'unknown')">{{ (agent.status || 'unknown') | titlecase
                }}</nz-tag>
            </td>
            <td>
              <span class="text-sm" [ngClass]="secondaryTextClass">{{ getRelativeTime(agent.lastUpdated) }}</span>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </div>


  </div>
</div>