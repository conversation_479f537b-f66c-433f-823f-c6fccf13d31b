import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzIconModule } from 'ng-zorro-antd/icon';
// import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { AddPromptDialogComponent, AddPromptDialogData, AddPromptDialogResult } from './add-prompt-dialog.component';

import {
    AgentEvaluation,
    AgentDefinition,
    CreateAgentEvaluation,
    EvaluationResult
} from '../../models/agent-evaluation.model';

import {
    AgentEvaluationServiceProxy,
    AgentDefinitionServiceProxy,
    AgentEvaluationDto,
    CreateAgentEvaluationDto
} from '../../../shared/service-proxies/service-proxies';
import { catchError, delay, finalize, map, of, tap } from 'rxjs';

@Component({
    selector: 'app-agent-evaluation',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        NzCardModule,
        NzTableModule,
        NzFormModule,
        NzInputModule,
        NzButtonModule,
        NzSelectModule,
        NzModalModule,
        NzSpinModule,
        NzTagModule,
        NzToolTipModule,
        NzProgressModule,
        NzTabsModule,
        NzDividerModule,
        NzEmptyModule,
        NzBadgeModule,
        NzIconModule,
    ],
    templateUrl: './agent-evaluation.component.html',
    styleUrl: './agent-evaluation.component.css'
})
export class AgentEvaluationComponent implements OnInit {
    // Data
    agents: AgentDefinition[] = [];
    sortedAgents: AgentDefinition[] = [];
    evaluations: AgentEvaluation[] = [];
    filteredEvaluations: AgentEvaluation[] = [];
    paginatedEvaluations: AgentEvaluation[] = [];

    // Agent selection
    selectedAgent: string | null = null;
    selectedAgentForExecution: string | null = null;

    // Search filter function for agent selection
    agentFilterOption = (inputValue: string, item: any): boolean => {
        return item.nzLabel.toLowerCase().indexOf(inputValue.toLowerCase()) !== -1;
    };

    // Forms
    evaluationForm: FormGroup;
    bulkEvaluationForm: FormGroup;
    editForm: FormGroup;

    // UI state
    loading = true;
    submitting = false;
    executing = false;
    reevaluating = false;
    searchQuery: string = '';
    isBulkMode = false;
    hasExecutedPrompts = false;
    activeTabIndex = 0; // 0 = Prompts, 1 = Execution

    // Modals
    showDetailsModal = false;
    showEditModal = false;
    showDeleteConfirmModal = false;

    // Evaluation and execution data
    selectedEvaluation: AgentEvaluation | null = null;
    currentExecutionProgress: { total: number; completed: number } | null = null;
    executionLogs: { title: string; content: string | SafeHtml }[] = [];
    reEvaluationResult: EvaluationResult | null = null;

    // Edit/Delete state
    editingEvaluation: AgentEvaluation | null = null;
    deletingEvaluation: AgentEvaluation | null = null;

    // Pagination properties
    currentPage: number = 1;
    pageSize: number = 10;
    totalPages: number = 1;
    previousPageSize: number = 10;

    // Pagination dropdown state
    isPageSizeDropdownOpen = false;
    isDropdownAbove = false;

    // Custom agent dropdown state
    isAgentDropdownOpen = false;
    isAgentDropdownAbove = false;
    selectedAgentLabel: string = '';

    Math = Math;

    constructor(
        private fb: FormBuilder,
        private router: Router,
        private agentEvaluationServiceProxy: AgentEvaluationServiceProxy,
        private agentDefinitionServiceProxy: AgentDefinitionServiceProxy,
        private message: NzMessageService,
        private modalService: NzModalService,
        private sanitizer: DomSanitizer,
        private dialog: MatDialog
    ) {
        this.evaluationForm = this.fb.group({
            agentName: ['', Validators.required],
            prompt: ['', Validators.required],
            expectedOutput: ['']
        });

        this.bulkEvaluationForm = this.fb.group({
            agentName: ['', Validators.required],
            prompts: ['', Validators.required]
        });

        this.editForm = this.fb.group({
            prompt: ['', Validators.required],
            expectedOutput: ['']
        });
    }

    ngOnInit(): void {
        this.loadData();
    }

    loadData(): void {
        this.loading = true;

        // Load agent definitions and evaluations
        this.agentDefinitionServiceProxy.getAll()
            .pipe(
                catchError(error => {
                    this.message.error('Failed to load agent definitions');
                    console.error('Error loading agent definitions:', error);
                    this.loading = false;
                    return of([]);
                })
            )
            .subscribe({
                next: (agents: any) => {
                    this.agents = agents || [];
                    this.sortAgents(); // Sort agents after loading

                    // Now load evaluations
                    this.agentEvaluationServiceProxy.getAll()
                        .pipe(
                            map(dtos => dtos.map(dto => this.mapDtoToAgentEvaluation(dto))),
                            catchError(error => {
                                this.message.error('Failed to load evaluations');
                                console.error('Error loading evaluations:', error);
                                return of([]);
                            }),
                            finalize(() => this.loading = false)
                        )
                        .subscribe({
                            next: (evaluations: AgentEvaluation[]) => {
                                this.evaluations = evaluations || [];
                                this.updateFilteredEvaluations();

                                // Check if we have any executed prompts
                                this.hasExecutedPrompts = this.evaluations.some(e => e.output !== undefined && e.output !== null);
                            }
                        });
                }
            });
    }

    updateFilteredEvaluations(): void {
        let result = [...this.evaluations];

        // Search filter
        if (this.searchQuery) {
            result = result.filter(evaluation =>
                evaluation.prompt.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                (evaluation.output && evaluation.output.toLowerCase().includes(this.searchQuery.toLowerCase()))
            );
        }

        // Agent filter
        if (this.selectedAgent) {
            result = result.filter(evaluation => evaluation.agentName === this.selectedAgent);
        }

        this.filteredEvaluations = result;
        this.updatePagination();
    }

    get filteredEvaluationsGetter() {
        return this.filteredEvaluations;
    }

    /**
     * Sort agents with priority:
     * 1. Currently executing agent at the top
     * 2. Agents with more prompts (descending order)
     */
    sortAgents(): void {
        this.sortedAgents = [...this.agents].sort((a, b) => {
            const aIsCurrentlyRunning = this.executing && this.selectedAgentForExecution === a.agentName;
            const bIsCurrentlyRunning = this.executing && this.selectedAgentForExecution === b.agentName;

            // Priority 1: Currently running agent goes first
            if (aIsCurrentlyRunning && !bIsCurrentlyRunning) return -1;
            if (!aIsCurrentlyRunning && bIsCurrentlyRunning) return 1;

            // Priority 2: Sort by number of prompts (descending)
            const aPromptCount = this.getAgentPromptsCount(a.agentName);
            const bPromptCount = this.getAgentPromptsCount(b.agentName);

            if (aPromptCount !== bPromptCount) {
                return bPromptCount - aPromptCount; // Descending order
            }

            // If same prompt count, sort alphabetically by agent name
            return a.agentName.localeCompare(b.agentName);
        });
    }

    filterEvaluations(): void {
        this.updateFilteredEvaluations();
    }

    onAgentSelect(agentName: string | null): void {
        this.selectedAgent = agentName;
        this.filterEvaluations();
    }

    onSearch(): void {
        this.filterEvaluations();
    }

    onAgentForExecutionSelect(agentName: string | null): void {
        this.selectedAgentForExecution = agentName;
        this.sortAgents(); // Re-sort when execution agent selection changes
    }

    resetFilters(): void {
        this.searchQuery = '';
        this.selectedAgent = null;
        this.filterEvaluations();
    }

    updatePagination() {
        // Ensure pageSize is a number
        this.pageSize = Number(this.pageSize);

        // Check if page size has changed
        const pageSizeChanged = this.previousPageSize !== this.pageSize;

        // Calculate total pages (minimum 1 page)
        this.totalPages = Math.max(1, Math.ceil(this.filteredEvaluations.length / this.pageSize));

        // Reset to page 1 when page size changes
        if (pageSizeChanged) {
            this.currentPage = 1;
            console.log('Page size changed from', this.previousPageSize, 'to', this.pageSize, '- resetting to page 1');
        }

        // Ensure current page is within bounds
        if (this.currentPage < 1) this.currentPage = 1;
        if (this.currentPage > this.totalPages) this.currentPage = this.totalPages;

        // Get current page of evaluations
        if (this.filteredEvaluations.length === 0) {
            this.paginatedEvaluations = [];
        } else {
            const startIndex = (this.currentPage - 1) * this.pageSize;
            const endIndex = Math.min(startIndex + this.pageSize, this.filteredEvaluations.length);
            this.paginatedEvaluations = this.filteredEvaluations.slice(startIndex, endIndex);
        }

        // Store current page size for next comparison
        this.previousPageSize = this.pageSize;

        // Log pagination state for debugging
        console.log('Pagination updated:', {
            totalItems: this.filteredEvaluations.length,
            pageSize: this.pageSize,
            totalPages: this.totalPages,
            currentPage: this.currentPage,
            itemsOnCurrentPage: this.paginatedEvaluations.length
        });
    }

    previousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.updatePagination();
        }
    }

    nextPage() {
        if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.updatePagination();
        }
    }

    goToPage(page: number) {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            this.updatePagination();
        }
    }

    toggleBulkMode(): void {
        this.isBulkMode = !this.isBulkMode;
    }

    // These methods are now handled by the dialog component
    // Keeping them for backward compatibility if needed elsewhere
    submitEvaluation(): void {
        // This method is now handled by the AddPromptDialogComponent
        console.warn('submitEvaluation called - this should now be handled by AddPromptDialogComponent');
    }

    submitBulkEvaluations(): void {
        this.message.info('Bulk mode has been disabled. Please use single mode to add prompts with expected outputs.');
    }

    // Execute all evaluations for the selected agent
    executeEvaluations(): void {
        if (!this.selectedAgentForExecution) {
            this.message.warning('Please select an agent first');
            return;
        }

        // Count how many prompts are available for this agent
        const promptsToExecute = this.evaluations.filter(e => e.agentName === this.selectedAgentForExecution);

        if (promptsToExecute.length === 0) {
            this.message.warning('No prompts found for this agent. Please add prompts first.');
            return;
        }

        this.executing = true;
        this.sortAgents(); // Re-sort to show currently executing agent at top
        this.executionLogs = [];
        this.currentExecutionProgress = {
            total: promptsToExecute.length,
            completed: 0
        };

        // Reset any previous re-evaluation results
        this.resetReEvaluation();

        // Add execution start log
        this.addExecutionLog(
            'Starting execution',
            `Executing ${promptsToExecute.length} prompts for agent "${this.selectedAgentForExecution}"...`
        );

        // Execute all evaluations for the selected agent
        this.agentEvaluationServiceProxy.executeByAgentName(this.selectedAgentForExecution)
            .pipe(
                map(dtos => dtos.map(dto => this.mapDtoToAgentEvaluation(dto))),
                catchError(error => {
                    this.addExecutionLog(
                        'Error executing prompts',
                        `Failed to execute prompts: ${error.message || 'Unknown error'}`
                    );
                    this.executing = false;
                    this.sortAgents(); // Re-sort after execution completes
                    return of([]);
                })
            )
            .subscribe((results: AgentEvaluation[]) => {
                if (results && results.length > 0) {
                    this.currentExecutionProgress!.completed = results.length;
                    this.hasExecutedPrompts = true;

                    this.addExecutionLog(
                        'Execution Complete',
                        `Successfully executed ${results.length} prompts for agent "${this.selectedAgentForExecution}".`
                    );
                } else {
                    this.addExecutionLog(
                        'Execution Complete',
                        'No prompts were executed. Please make sure there are prompts available for this agent.'
                    );
                }

                this.executing = false;
                this.sortAgents(); // Re-sort after execution completes

                // Refresh data to get updated scores and outputs
                this.loadData();
            });
    }

    // Re-evaluate the agent based on all executed prompts
    reevaluateAgent(): void {
        if (!this.selectedAgentForExecution) {
            this.message.warning('Please select an agent first');
            return;
        }

        // Check if there are executed prompts for this agent
        const executedEvaluations = this.filteredEvaluations.filter(
            e => e.agentName === this.selectedAgentForExecution && e.output
        );

        if (executedEvaluations.length === 0) {
            this.message.warning('No executed prompts found for this agent. Please execute prompts first.');
            return;
        }

        this.reevaluating = true;
        this.addExecutionLog(
            'Starting re-evaluation',
            `Re-evaluating agent "${this.selectedAgentForExecution}" based on ${executedEvaluations.length} executed prompts...`
        );

        // Call the re-evaluation API with just the agent name
        this.agentEvaluationServiceProxy.reEvaluateByAgentName(this.selectedAgentForExecution)
            .pipe(
                map(dto => this.mapDtoToEvaluationResult(dto)),
                catchError(error => {
                    this.message.error('Failed to re-evaluate agent');
                    console.error('Error re-evaluating agent:', error);
                    this.addExecutionLog(
                        'Re-evaluation failed',
                        `Failed to re-evaluate agent: ${error.message || 'Unknown error'}`
                    );
                    this.reevaluating = false;
                    return of(null);
                }),
                finalize(() => this.reevaluating = false)
            )
            .subscribe((result: EvaluationResult | null) => {
                if (result) {
                    this.reEvaluationResult = result;
                    console.log('Received evaluation result:', result);
                    this.addExecutionLog(
                        'Re-evaluation complete',
                        `Successfully re-evaluated agent "${this.selectedAgentForExecution}" with score ${result.score}/10`
                    );

                    if (result.updatedPrompt) {
                        this.addExecutionLog(
                            'Updated Prompt Available',
                            'The evaluation includes an updated prompt suggestion'
                        );
                    }
                }
            });
    }    // Show details for a specific prompt
    showPromptDetails(evaluation: AgentEvaluation): void {
        this.selectedEvaluation = evaluation;
    }

    // Modal control methods
    closeDetailsModal(): void {
        this.selectedEvaluation = null;
    }
    resetExecution(): void {
        this.executionLogs = [];
        this.currentExecutionProgress = null;
    }

    resetReEvaluation(): void {
        this.reEvaluationResult = null;
    }

    // Helper methods
    getExecutionProgressPercent(): number {
        if (!this.currentExecutionProgress) return 0;

        const { total, completed } = this.currentExecutionProgress;
        return Math.round((completed / total) * 100);
    }

    addExecutionLog(title: string, content: string): void {
        const safeContent = this.sanitizer.bypassSecurityTrustHtml(content);
        this.executionLogs.push({ title, content: safeContent });

        // Auto-scroll to bottom
        setTimeout(() => {
            const logElement = document.querySelector('.execution-log');
            if (logElement) {
                logElement.scrollTop = logElement.scrollHeight;
            }
        }, 100);
    }

    truncateText(text: string, maxLength: number): string {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    formatDate(date: Date | string | undefined): string {
        if (!date) return 'N/A';
        return new Date(date).toLocaleString();
    }

    getScoreColor(score: number | undefined): string {
        if (score === undefined) return '';
        if (score >= 8) return 'success';
        if (score >= 5) return 'warning';
        return 'error';
    }

    // Helper methods to avoid complex expressions in templates
    getPromptsCount(): number {
        return this.evaluations.length;
    }

    getExecutedPromptsCount(): number {
        return this.evaluations.filter(e => e.output !== undefined && e.output !== null).length;
    }

    getAgentPromptsCount(agentName: string | null): number {
        if (!agentName) return 0;
        return this.evaluations.filter(e => e.agentName === agentName).length;
    }

    getAgentExecutedPromptsCount(agentName: string | null): number {
        if (!agentName) return 0;
        return this.evaluations.filter(e => e.agentName === agentName && e.output).length;
    }

    // Get summary text for agent selection
    getAgentSelectionSummary(): string {
        if (!this.selectedAgent) return `All Agents (${this.agents.length})`;
        const count = this.getAgentPromptsCount(this.selectedAgent);
        return `${this.selectedAgent} (${count} prompts)`;
    }

    hasAgentExecutedPrompts(agentName: string | null): boolean {
        if (!agentName) return false;
        return this.evaluations.filter(e => e.agentName === agentName && e.output).length > 0;
    }

    // Process improvement suggestions for UI display
    processImprovementSuggestions(suggestions: string): string[] {
        if (!suggestions) return [];

        // Split by newlines and filter out empty lines
        const lines = suggestions.split('\n').filter(line => line.trim().length > 0);

        // Process each line to remove common markers like "1.", "-", "*" etc.
        return lines.map(line => {
            // Remove common list markers
            return line.replace(/^\s*(\d+\.\s*|\-\s*|\*\s*)/, '').trim();
        });
    }

    // Handle tab change
    onTabChange(index: number): void {
        this.activeTabIndex = index;
    }

    // Open add prompt dialog
    openAddPromptDialog(): void {
        const dialogData: AddPromptDialogData = {
            agents: this.agents,
            mode: 'add'
        };

        const dialogRef = this.dialog.open(AddPromptDialogComponent, {
            width: '600px',
            maxWidth: '90vw',
            data: dialogData,
            disableClose: false,
            autoFocus: true
        });

        dialogRef.afterClosed().subscribe((result: AddPromptDialogResult) => {
            if (result && result.success) {
                this.loadData(); // Refresh the data
            }
        });
    }

    // Edit functionality
    openEditDialog(evaluation: AgentEvaluation): void {
        const dialogData: AddPromptDialogData = {
            agents: this.agents,
            editingEvaluation: evaluation,
            mode: 'edit'
        };

        const dialogRef = this.dialog.open(AddPromptDialogComponent, {
            width: '600px',
            maxWidth: '90vw',
            data: dialogData,
            disableClose: false,
            autoFocus: true
        });

        dialogRef.afterClosed().subscribe((result: AddPromptDialogResult) => {
            if (result && result.success) {
                this.loadData(); // Refresh the data
            }
        });
    }

    // These methods are now handled by the dialog component
    submitEditEvaluation(): void {
        // This method is now handled by the AddPromptDialogComponent
        console.warn('submitEditEvaluation called - this should now be handled by AddPromptDialogComponent');
    }

    closeEditModal(): void {
        this.showEditModal = false;
        this.editingEvaluation = null;
        this.editForm.reset();
    }

    // Delete functionality
    openDeleteConfirmDialog(evaluation: AgentEvaluation): void {
        this.deletingEvaluation = evaluation;
        this.showDeleteConfirmModal = true;
    }

    confirmDeleteEvaluation(): void {
        if (!this.deletingEvaluation) {
            return;
        }

        this.submitting = true;

        this.agentEvaluationServiceProxy.delete(this.deletingEvaluation.id)
            .pipe(
                finalize(() => this.submitting = false),
                catchError(error => {
                    this.message.error('Failed to delete evaluation');
                    console.error('Error deleting evaluation:', error);
                    return of(null);
                })
            )
            .subscribe(result => {
                if (result) {
                    this.message.success('Evaluation deleted successfully');
                    this.closeDeleteConfirmModal();
                    this.loadData();
                }
            });
    }

    closeDeleteConfirmModal(): void {
        this.showDeleteConfirmModal = false;
        this.deletingEvaluation = null;
    }

    /**
     * Maps a DTO from the service proxy to the application model
     */
    private mapDtoToAgentEvaluation(dto: AgentEvaluationDto): AgentEvaluation {
        return {
            id: dto.id || '',
            agentName: dto.agentName || '',
            prompt: dto.prompt || '',
            output: dto.output,
            expectedOutput: dto.expectedOutput,
            score: dto.score,
            createdAt: dto.createdAt?.toJSDate() || new Date(),
            evaluatedAt: dto.evaluatedAt?.toJSDate()
        };
    }

    /**
     * Maps an EvaluationResultDto to the application EvaluationResult model
     */
    private mapDtoToEvaluationResult(dto: any): EvaluationResult {
        return {
            evaluationId: dto.evaluationId || '',
            score: dto.score || 0,
            evaluation: dto.evaluation || '',
            improvementSuggestions: dto.improvementSuggestions || '',
            updatedPrompt: dto.updatedPrompt || ''
        };
    }

    /**
     * Get display label for agent in selection dropdown
     * Shows running indicator and prompt count
     */
    getAgentDisplayLabel(agent: AgentDefinition): string {
        const promptCount = this.getAgentPromptsCount(agent.agentName);
        const isRunning = this.executing && this.selectedAgentForExecution === agent.agentName;

        let label = agent.agentName;
        if (isRunning) {
            label = `🔄 ${label} (Running)`;
        }
        label += ` (${promptCount})`;

        return label;
    }

    /**
     * Get display label for agent in execution dropdown
     */
    getAgentExecutionDisplayLabel(agent: AgentDefinition): string {
        const promptCount = this.getAgentPromptsCount(agent.agentName);
        const isRunning = this.executing && this.selectedAgentForExecution === agent.agentName;

        let label = agent.agentName;
        if (isRunning) {
            label = `🔄 ${label} (Running)`;
        }
        label += ` (${promptCount} prompts)`;

        return label;
    }

    togglePageSizeDropdown() {
        this.isPageSizeDropdownOpen = !this.isPageSizeDropdownOpen;
        if (this.isPageSizeDropdownOpen) {
            setTimeout(() => {
                const btn = document.getElementById('pageSizeDropdownBtn');
                const dropdown = document.getElementById('pageSizeDropdownMenu');
                if (btn && dropdown) {
                    const rect = btn.getBoundingClientRect();
                    const dropdownHeight = dropdown.offsetHeight || 160;
                    const spaceBelow = window.innerHeight - rect.bottom;
                    this.isDropdownAbove = spaceBelow < dropdownHeight + 16;
                }
            }, 0);
        }
    }

    selectPageSize(size: number) {
        this.pageSize = size;
        this.isPageSizeDropdownOpen = false;
        this.currentPage = 1;
        this.updatePagination();
    }

    toggleAgentDropdown() {
        this.isAgentDropdownOpen = !this.isAgentDropdownOpen;
        if (this.isAgentDropdownOpen) {
            setTimeout(() => {
                const btn = document.getElementById('agentDropdownBtn');
                const dropdown = document.getElementById('agentDropdownMenu');
                if (btn && dropdown) {
                    const rect = btn.getBoundingClientRect();
                    const dropdownHeight = dropdown.offsetHeight || 160;
                    const spaceBelow = window.innerHeight - rect.bottom;
                    this.isAgentDropdownAbove = spaceBelow < dropdownHeight + 16;
                }
            }, 0);
        }
    }

    selectAgent(agentName: string) {
        this.evaluationForm.get('agentName')?.setValue(agentName);
        this.selectedAgentLabel = agentName || 'Select an agent';
        this.isAgentDropdownOpen = false;
    }
}
