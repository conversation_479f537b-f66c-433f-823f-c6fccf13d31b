<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 65px);">
  <!-- Main Container -->
  <div class="flex-1 overflow-hidden flex flex-col">
    <!-- Header - Teams-style with compact vertical spacing -->
    <div
      class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50">
      <!-- Left side with title and back button -->
      <div class="flex items-center gap-3">
        <button
          class="w-8 h-8 flex items-center justify-center rounded-md border-none cursor-pointer transition-all duration-200"
          style="background-color: var(--background-white); color: var(--text-medium-gray); border: 1px solid var(--hover-blue-gray);"
          onmouseover="this.style.backgroundColor='var(--hover-blue-gray)'"
          onmouseout="this.style.backgroundColor='var(--background-white)'" (click)="goBack()" title="Go Back">
          <i class="ri-arrow-left-line text-base"></i>
        </button>
        <div class="flex items-center gap-2">
          <i class="ri-file-text-line text-[var(--primary-purple)] text-xl"></i>
          <h1 class="text-lg font-medium text-[var(--text-dark)] truncate">{{ project.subject || 'Request Details' }}
          </h1>
        </div>
      </div>

      <!-- Right side with action buttons -->
      <div class="flex items-center gap-2">
        <button *ngIf="project.status === 'Open'"
          class="h-8 px-3 py-1 bg-orange-500 text-white text-sm font-medium rounded-md hover:bg-orange-600 transition-all duration-200 flex items-center justify-center gap-1"
          (click)="updateStatus('In Progress')" title="Mark as In Progress">
          <i class="ri-loader-2-line"></i>
          <span>Start Progress</span>
        </button>
        <button *ngIf="project.status === 'In Progress'"
          class="h-8 px-3 py-1 bg-green-500 text-white text-sm font-medium rounded-md hover:bg-green-600 transition-all duration-200 flex items-center justify-center gap-1"
          (click)="updateStatus('Completed')" title="Mark as Completed">
          <i class="ri-check-line"></i>
          <span>Complete</span>
        </button>

      </div>
    </div>

    <!-- Request Overview Card -->
    <div class="px-4 pt-4">
      <div
        class="bg-[var(--background-white)] rounded-lg shadow-sm border border-[var(--hover-blue-gray)] border-opacity-30 p-6 mb-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Status and Priority Column -->
          <div class="flex flex-col gap-3">
            <div class="flex items-center gap-2">
              <i class="ri-info-circle-line text-[var(--primary-purple)] text-lg"></i>
              <span class="text-sm font-medium text-[var(--text-dark)]">Status:</span>
              <span class="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium"
                [ngClass]="{
                  'bg-green-100 text-green-800': project.status === 'Completed',
                  'bg-orange-100 text-orange-800': project.status === 'In Progress',
                  'bg-blue-100 text-blue-800': project.status === 'Open',
                  'bg-red-100 text-red-800': project.status === 'Closed'
                }">
                {{ project.status }}
              </span>
            </div>
            <div class="flex items-center gap-2">
              <i class="ri-alert-circle-line text-orange-500 text-lg"></i>
              <span class="text-sm font-medium text-[var(--text-dark)]">Priority:</span>
              <span class="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium"
                [ngClass]="{
                  'bg-red-100 text-red-800': project.priority === 'high',
                  'bg-orange-100 text-orange-800': project.priority === 'Medium',
                  'bg-green-100 text-green-800': project.priority === 'Low'
                }">
                {{ project.priority | titlecase }}
              </span>
            </div>
          </div>

          <!-- Dates Column -->
          <div class="flex flex-col gap-3">
            <div class="flex items-center gap-2">
              <i class="ri-calendar-line text-blue-500 text-lg"></i>
              <span class="text-sm font-medium text-[var(--text-dark)]">Created:</span>
              <span class="text-sm text-[var(--text-dark)]">{{ project.createdDate | date:'MMM d, y' }}</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="ri-calendar-check-line text-blue-500 text-lg"></i>
              <span class="text-sm font-medium text-[var(--text-dark)]">Due:</span>
              <span class="text-sm text-[var(--text-dark)]">{{ project.completionDate | date:'MMM d, y' }}</span>
            </div>
          </div>

          <!-- Additional Info -->
          <div class="">
            <div class="border-t border-[var(--hover-blue-gray)]">
              <div class="flex items-center gap-2 mb-2">
                <i class="ri-file-text-line text-[var(--primary-purple)] text-lg mt-1"></i>
                <span class="text-sm font-medium text-[var(--text-dark)]">Message:</span>
              </div>
              <p class="text-sm text-[var(--text-dark)] leading-relaxed pl-6">{{ project.message }}</p>
            </div>
          </div>
        </div>


      </div>
    </div>

    <!-- Sticky Tabs -->
    <div class="px-4">
      <div
        class="bg-[var(--background-white)] rounded-lg shadow-sm border border-[var(--hover-blue-gray)] border-opacity-30 p-4 mb-4 flex justify-center gap-2 flex-wrap">
        <button
          class="h-8 px-3 py-1 text-sm font-medium transition-all duration-200 flex items-center justify-center gap-1 rounded-md border-none"
          [style.background-color]="tabs === 'insight' ? 'var(--primary-purple)' : 'var(--background-light-gray)'"
          [style.color]="tabs === 'insight' ? 'white' : 'var(--text-medium-gray)'"
          onmouseover="if(this.style.backgroundColor !== 'var(--primary-purple)') this.style.backgroundColor='var(--hover-blue-gray)'"
          onmouseout="if(this.style.backgroundColor !== 'var(--primary-purple)') this.style.backgroundColor='var(--background-light-gray)'"
          (click)="tabs='insight'" title="Insight">
          <i class="ri-lightbulb-line text-base"></i>
          <span>Insight</span>
          <span class="ml-1 px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded-full text-xs">{{ matchedDocuments.length ||
            0 }}</span>
        </button>
        <button
          class="h-8 px-3 py-1 text-sm font-medium transition-all duration-200 flex items-center justify-center gap-1 rounded-md border-none"
          [style.background-color]="tabs === 'tasks' ? 'var(--primary-purple)' : 'var(--background-light-gray)'"
          [style.color]="tabs === 'tasks' ? 'white' : 'var(--text-medium-gray)'"
          onmouseover="if(this.style.backgroundColor !== 'var(--primary-purple)') this.style.backgroundColor='var(--hover-blue-gray)'"
          onmouseout="if(this.style.backgroundColor !== 'var(--primary-purple)') this.style.backgroundColor='var(--background-light-gray)'"
          (click)="onSeeTasks()" title="Tasks">
          <i class="ri-task-line text-base"></i>
          <span>Tasks</span>
          <span class="ml-1 px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded-full text-xs">{{ tasks.length || 0
            }}</span>
        </button>
        <button
          class="h-8 px-3 py-1 text-sm font-medium transition-all duration-200 flex items-center justify-center gap-1 rounded-md border-none"
          [style.background-color]="tabs === 'comments' ? 'var(--primary-purple)' : 'var(--background-light-gray)'"
          [style.color]="tabs === 'comments' ? 'white' : 'var(--text-medium-gray)'"
          onmouseover="if(this.style.backgroundColor !== 'var(--primary-purple)') this.style.backgroundColor='var(--hover-blue-gray)'"
          onmouseout="if(this.style.backgroundColor !== 'var(--primary-purple)') this.style.backgroundColor='var(--background-light-gray)'"
          (click)="onSeeComments()" title="Comments">
          <i class="ri-chat-3-line text-base"></i>
          <span>Comments</span>
          <span class="ml-1 px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded-full text-xs">{{ comments.length || 0
            }}</span>
        </button>
        <button
          class="h-8 px-3 py-1 text-sm font-medium transition-all duration-200 flex items-center justify-center gap-1 rounded-md border-none"
          [style.background-color]="tabs === 'attachments' ? 'var(--primary-purple)' : 'var(--background-light-gray)'"
          [style.color]="tabs === 'attachments' ? 'white' : 'var(--text-medium-gray)'"
          onmouseover="if(this.style.backgroundColor !== 'var(--primary-purple)') this.style.backgroundColor='var(--hover-blue-gray)'"
          onmouseout="if(this.style.backgroundColor !== 'var(--primary-purple)') this.style.backgroundColor='var(--background-light-gray)'"
          (click)="onSeeAttachments()" title="Attachments">
          <i class="ri-attachment-line text-base"></i>
          <span>Attachments</span>
          <span class="ml-1 px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded-full text-xs">{{ project.filesName.length
            || 0 }}</span>
        </button>
      </div>
    </div>

    <!-- Tab Content -->
    <div class="flex-1 overflow-y-auto px-4 pb-4">
      <div
        class="bg-[var(--background-white)] rounded-lg shadow-sm border border-[var(--hover-blue-gray)] border-opacity-30 p-6">

        <!-- Insight Tab -->
        <div *ngIf="tabs === 'insight'">
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-4">Insight</h3>
          <div *ngIf="matchedDocuments && matchedDocuments.length > 0" class="space-y-4">
            <div *ngFor="let doc of matchedDocuments"
              class="flex items-center gap-3 p-3 bg-[var(--background-light-gray)] rounded-lg border border-[var(--hover-blue-gray)] hover:shadow-sm transition-all duration-200 cursor-pointer">
              <i class="ri-file-text-line text-blue-500 text-lg"></i>
              <span class="text-sm font-medium text-[var(--text-dark)] truncate flex-1">{{doc.title}}</span>
              <button
                class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs hover:bg-blue-200 transition-all duration-200"
                (click)="onSeeDocs(doc.id)" title="View Document">
                View
              </button>
            </div>
            <div *ngIf="project.summary"
              class="p-3 bg-[var(--background-light-gray)] rounded-lg border border-[var(--hover-blue-gray)]">
              <h4 class="text-sm font-medium mb-2 text-[var(--text-dark)]">Project Summary</h4>
              <p class="text-sm text-[var(--text-dark)]">{{ getProjectSummary() }}</p>
            </div>
          </div>
          <div *ngIf="!matchedDocuments || matchedDocuments.length === 0"
            class="text-center py-8 text-[var(--text-medium-gray)]">
            <i class="ri-folder-open-line text-4xl mb-2"></i>
            <p class="text-sm">No documents found for this project.</p>
          </div>
        </div>

        <!-- Tasks Tab -->
        <div *ngIf="tabs === 'tasks'">
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-4">Tasks</h3>

          <!-- Add Task Section -->
          <div class="bg-[var(--background-light-gray)] rounded-lg p-4 mb-6 border border-[var(--hover-blue-gray)]">
            <h4 class="text-base font-medium mb-3 text-[var(--text-dark)]">Add/Update Task</h4>
            <div class="flex flex-col gap-4">
              <textarea [(ngModel)]="newTaskMessage" placeholder="Type Your Task Here..."
                class="w-full p-3 rounded-lg border border-[var(--hover-blue-gray)] text-sm text-[var(--text-dark)] placeholder-[var(--text-medium-gray)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)]"
                style="background-color: var(--background-white);"></textarea>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="flex flex-col gap-1">
                  <label class="text-sm font-medium text-[var(--text-dark)]">Due Date:</label>
                  <input type="date" [(ngModel)]="startDate" id="startDate" name="startDate"
                    class="w-full p-2 rounded-lg border border-[var(--hover-blue-gray)] text-sm text-[var(--text-dark)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)]"
                    style="background-color: var(--background-white);" />
                </div>
                <div class="flex flex-col gap-1">
                  <label class="text-sm font-medium text-[var(--text-dark)]">Complexity:</label>
                  <select [(ngModel)]="taskComplexity" id="taskComplexity" name="taskComplexity"
                    class="w-full p-2 rounded-lg border border-[var(--hover-blue-gray)] text-sm text-[var(--text-dark)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)]"
                    style="background-color: var(--background-white);">
                    <option value="" disabled selected>Select complexity</option>
                    <option value="Low">Low</option>
                    <option value="Medium">Medium</option>
                    <option value="High">High</option>
                  </select>
                </div>
                <div class="flex gap-2 items-end">
                  <button
                    class="h-9 px-3 bg-[var(--background-light-gray)] text-[var(--text-medium-gray)] text-sm font-medium rounded-md hover:bg-[var(--hover-blue-gray)] transition-all duration-200 flex items-center gap-1"
                    (click)="assignMember(0)" title="Assign Member">
                    <i class="ri-user-add-line"></i>
                    Assign
                  </button>
                  <button
                    class="h-9 px-3 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center gap-1"
                    (click)="addTask()" title="{{editingTask ? 'Update' : 'Save'}} Task">
                    <i class="ri-save-line"></i>
                    {{editingTask ? 'Update' : 'Save'}}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Tasks List -->
          <div class="space-y-4" *ngIf="groupedTasks && groupedTasks.length > 0">
            <div *ngFor="let groupedTask of groupedTasks"
              class="bg-[var(--background-light-gray)] rounded-lg p-4 border border-[var(--hover-blue-gray)]">
              <h4 class="text-base font-medium text-[var(--text-dark)] mb-3">
                {{groupedTask.type}} ({{groupedTask.startDate | date:'d MMM'}} - {{groupedTask.endDate | date:'d MMM'}})
              </h4>
              <div *ngFor="let task of groupedTask.tasks"
                class="flex items-center justify-between p-3 rounded-lg hover:bg-[var(--background-white)] transition-all duration-200">
                <div class="flex items-center gap-3">
                  <input type="checkbox" [(ngModel)]="task.isCompleted"
                    class="h-4 w-4 text-[var(--primary-purple)] focus:ring-[var(--primary-purple)] border-[var(--hover-blue-gray)] rounded"
                    (change)="updateTaskStatus(task.id, task.isCompleted)" />
                  <div class="flex flex-col">
                    <span class="text-sm font-medium text-[var(--text-dark)]">{{ task.message }}</span>
                    <div class="flex gap-3 text-xs text-[var(--text-medium-gray)]">
                      <span>{{ task.dueDate ? (task.dueDate.toJSDate ? (task.dueDate.toJSDate() | date) : (task.dueDate | date)) : '' }}</span>
                      <span class="cursor-pointer hover:underline" (click)="assignMember(task)">
                        {{ task.assignedEmails || loginedUser.email || 'Unassigned' }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="flex gap-2">
                  <button class="w-8 h-8 flex items-center justify-center rounded-md transition-all duration-200"
                    style="background-color: var(--background-white); color: #f59e0b;"
                    onmouseover="this.style.backgroundColor='var(--hover-blue-gray)'"
                    onmouseout="this.style.backgroundColor='var(--background-white)'" (click)="editTask(task)"
                    title="Edit Task">
                    <i class="ri-edit-line text-sm"></i>
                  </button>
                  <button class="w-8 h-8 flex items-center justify-center rounded-md transition-all duration-200"
                    style="background-color: var(--background-white); color: #ef4444;"
                    onmouseover="this.style.backgroundColor='var(--hover-blue-gray)'"
                    onmouseout="this.style.backgroundColor='var(--background-white)'" (click)="deleteTask(task.id)"
                    title="Delete Task">
                    <i class="ri-delete-bin-line text-sm"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="!tasks || tasks.length === 0" class="text-center py-8 text-[var(--text-medium-gray)]">
            <i class="ri-task-line text-4xl mb-2"></i>
            <p class="text-sm">No tasks available. Add a new task above.</p>
          </div>
        </div>

        <!-- Comments Tab -->
        <div *ngIf="tabs === 'comments'">
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-4">Comments</h3>

          <!-- Add Comment Section -->
          <div class="bg-[var(--background-light-gray)] rounded-lg p-4 mb-6 border border-[var(--hover-blue-gray)]">
            <textarea [(ngModel)]="newComment.message"
              class="w-full p-3 rounded-lg border border-[var(--hover-blue-gray)] text-sm text-[var(--text-dark)] placeholder-[var(--text-medium-gray)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] resize-y"
              style="background-color: var(--background-white);" placeholder="Write your comment here..."></textarea>
            <div class="flex justify-end mt-3">
              <button
                class="h-8 px-4 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center gap-1"
                (click)="addComment()">
                <i class="ri-send-plane-line"></i>
                Send
              </button>
            </div>
          </div>

          <!-- Comments List -->
          <div class="space-y-4">
            <div *ngFor="let comment of comments"
              class="p-4 bg-[var(--background-light-gray)] rounded-lg border border-[var(--hover-blue-gray)] hover:shadow-sm transition-all duration-200">
              <div class="flex justify-between items-center mb-2">
                <div class="flex items-center gap-2">
                  <div
                    class="w-6 h-6 rounded-full bg-[var(--primary-purple)] text-white text-xs flex items-center justify-center">
                    {{ getUserInitial(comment.email, project.userEmail) }}
                  </div>
                  <span class="text-sm font-medium text-[var(--text-dark)]">{{ findUser(comment.email,
                    project.userEmail) }}</span>
                </div>
                <span class="text-xs text-[var(--text-medium-gray)]">{{ comment.timeStamp | date:'MMM d, y h:mm a'
                  }}</span>
              </div>
              <p class="text-sm text-[var(--text-dark)]">{{ comment.message }}</p>
            </div>

            <!-- Empty State -->
            <div *ngIf="!comments || comments.length === 0" class="text-center py-8 text-[var(--text-medium-gray)]">
              <i class="ri-chat-3-line text-4xl mb-2"></i>
              <p class="text-sm">No comments yet. Be the first to add one!</p>
            </div>
          </div>
        </div>

        <!-- Attachments Tab -->
        <div *ngIf="tabs === 'attachments'">
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-4">Attachments</h3>

          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div *ngFor="let file of getProjectFilesName()"
              class="bg-[var(--background-light-gray)] rounded-lg overflow-hidden border border-[var(--hover-blue-gray)] hover:shadow-sm transition-all duration-200">
              <div class="h-40 bg-[var(--background-white)] flex items-center justify-center">
                <img class="w-full h-full object-cover" [src]="getSafeUrl(file)" [alt]="file" />
              </div>
              <div class="p-3 flex justify-between items-center">
                <span class="text-sm font-medium text-[var(--text-dark)] truncate flex-1">{{ file }}</span>
                <div class="flex gap-1 ml-2">
                  <button
                    class="w-6 h-6 flex items-center justify-center rounded text-blue-500 hover:bg-blue-50 transition-all duration-200"
                    (click)="downloadAttachment(file)" title="Download">
                    <i class="ri-download-line text-sm"></i>
                  </button>
                  <button
                    class="w-6 h-6 flex items-center justify-center rounded text-red-500 hover:bg-red-50 transition-all duration-200"
                    (click)="deleteAttachement(file)" title="Delete">
                    <i class="ri-delete-bin-line text-sm"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div *ngIf="getProjectFilesName().length === 0"
              class="col-span-full text-center py-8 text-[var(--text-medium-gray)]">
              <i class="ri-attachment-line text-4xl mb-2"></i>
              <p class="text-sm">No attachments available.</p>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
