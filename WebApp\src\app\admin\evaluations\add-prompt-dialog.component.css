/* Dialog Container - using Tailwind utilities */
.dialog-container {
  @apply min-w-[500px] max-w-[700px] w-full;
}

/* Dialog Title */
.dialog-title {
  @apply flex items-center gap-3 text-2xl font-semibold mb-0 px-6 pt-6 pb-4;
  color: var(--text-dark);
}

.title-icon {
  @apply text-3xl w-7 h-7;
  color: var(--primary-purple);
}

/* Dialog Content */
.dialog-content {
  @apply px-6 pb-4 max-h-[70vh] overflow-y-auto;
}

.prompt-form {
  @apply flex flex-col gap-5;
}

.full-width {
  @apply w-full;
}

/* Material Form Field Customization with Tailwind approach */
::ng-deep .mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    background-color: var(--background-light-gray);
    @apply rounded-md;
  }

  .mat-mdc-form-field-focus-overlay {
    background-color: var(--primary-purple);
    @apply opacity-5;
  }

  .mdc-notched-outline__leading,
  .mdc-notched-outline__notch,
  .mdc-notched-outline__trailing {
    border-color: var(--hover-blue-gray);
    @apply border;
  }

  &.mat-focused {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: var(--primary-purple);
      @apply border-2;
    }
  }

  .mat-mdc-form-field-label {
    color: var(--text-medium-gray);
    @apply text-sm font-medium;
  }

  &.mat-focused .mat-mdc-form-field-label {
    color: var(--primary-purple);
  }
}

/* Prefix Icons */
::ng-deep .mat-mdc-form-field .mat-icon[matPrefix] {
  color: var(--primary-purple);
  @apply mr-2 text-xl w-5 h-5;
}

/* Textarea Styling */
::ng-deep .mat-mdc-input-element {
  color: var(--text-dark);
  @apply text-sm;

  &::placeholder {
    color: var(--text-medium-gray);
    @apply opacity-100;
  }
}

/* Info Note */
.info-note {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  margin-top: 8px;
}

.info-icon {
  color: #3b82f6;
  font-size: 1.25rem;
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  margin-top: 2px;
}

.info-content {
  flex: 1;
}

.info-content p {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #1e40af;
}

/* Dialog Actions */
.dialog-actions {
  padding: 16px 24px 24px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid var(--hover-blue-gray, #e5e7eb);
  margin-top: 16px;
}

.cancel-button {
  color: var(--text-medium-gray, #6b7280);
  border: 1px solid var(--hover-blue-gray, #e5e7eb);
  background-color: transparent;
  transition: all 0.2s ease;
}

.cancel-button:hover:not([disabled]) {
  background-color: var(--hover-blue-gray, #e5e7eb);
  color: var(--text-dark, #1a1a1a);
}

.submit-button {
  background-color: var(--primary-purple, #6366f1);
  color: white;
  min-width: 120px;
  position: relative;
  transition: all 0.2s ease;
}

.submit-button:hover:not([disabled]) {
  background-color: var(--secondary-purple, #4f46e5);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.submit-button .mat-icon {
  margin-right: 8px;
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .dialog-title {
    color: var(--text-dark-theme, #ffffff);
  }

  .info-note {
    background-color: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
  }

  .info-content p {
    color: #93c5fd;
  }

  ::ng-deep .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      background-color: var(--background-dark-gray, #374151);
    }

    .mat-mdc-form-field-label {
      color: var(--text-dark-theme-secondary, #d1d5db);
    }
  }

  ::ng-deep .mat-mdc-input-element {
    color: var(--text-dark-theme, #ffffff);

    &::placeholder {
      color: var(--text-dark-theme-secondary, #9ca3af);
    }
  }
}

/* Responsive Design */
@media (max-width: 600px) {
  .dialog-container {
    min-width: 90vw;
    max-width: 90vw;
  }

  .dialog-title {
    font-size: 1.25rem;
    padding: 16px 16px 12px 16px;
  }

  .dialog-content {
    padding: 0 16px 12px 16px;
  }

  .dialog-actions {
    padding: 12px 16px 16px 16px;
    flex-direction: column-reverse;
  }

  .cancel-button,
  .submit-button {
    width: 100%;
    margin: 0;
  }
}
