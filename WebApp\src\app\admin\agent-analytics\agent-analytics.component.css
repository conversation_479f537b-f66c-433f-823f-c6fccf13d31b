/* Agent Analytics Component Styles */

:host {
  display: block;
  height: 100%;
  width: 100%;
}

/* Card styling for consistent theme support */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:host-context(.dark) .ant-card {
  background-color: var(--background-white);
  border-color: var(--hover-blue-gray);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:host-context(.dark) .ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* Table styling */
.ant-table {
  background: transparent;
}

:host-context(.dark) .ant-table {
  background-color: var(--background-white);
  color: var(--text-dark);
}

:host-context(.dark) .ant-table-thead > tr > th {
  background-color: var(--hover-blue-gray);
  border-color: var(--hover-blue-gray);
  color: var(--text-dark);
}

:host-context(.dark) .ant-table-tbody > tr > td {
  border-color: var(--hover-blue-gray);
  color: var(--text-dark);
}

:host-context(.dark) .ant-table-tbody > tr:hover > td {
  background-color: var(--hover-blue-gray);
}

/* Progress bar styling */
.ant-progress-line {
  margin-bottom: 0;
}

.ant-progress-bg {
  border-radius: 4px;
}

/* Button styling */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  background-color: var(--primary-purple);
  border-color: var(--primary-purple);
}

.ant-btn-primary:hover {
  background-color: var(--secondary-purple);
  border-color: var(--secondary-purple);
}

:host-context(.dark) .ant-btn-default {
  background-color: var(--hover-blue-gray);
  border-color: var(--hover-blue-gray);
  color: var(--text-dark);
}

:host-context(.dark) .ant-btn-default:hover {
  background-color: var(--background-light-gray);
  border-color: var(--background-light-gray);
}

/* Select dropdown styling */
:host-context(.dark) .ant-select-selector {
  background-color: var(--background-white) !important;
  border-color: var(--hover-blue-gray) !important;
  color: var(--text-dark) !important;
}

:host-context(.dark) .ant-select-arrow {
  color: var(--text-medium-gray);
}

/* Tag styling */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
  border: none;
}

/* Alert styling */
.ant-alert {
  border-radius: 6px;
  border: none;
}

:host-context(.dark) .ant-alert-info {
  background-color: rgba(16, 163, 127, 0.1);
  border: 1px solid rgba(16, 163, 127, 0.3);
}

:host-context(.dark) .ant-alert-info .ant-alert-message,
:host-context(.dark) .ant-alert-info .ant-alert-description {
  color: var(--text-dark);
}

:host-context(.dark) .ant-alert-success {
  background-color: rgba(82, 196, 26, 0.1);
  border: 1px solid rgba(82, 196, 26, 0.3);
}

:host-context(.dark) .ant-alert-warning {
  background-color: rgba(250, 173, 20, 0.1);
  border: 1px solid rgba(250, 173, 20, 0.3);
}

/* Statistic styling */
:host-context(.dark) .ant-statistic-title {
  color: var(--text-medium-gray);
}

:host-context(.dark) .ant-statistic-content {
  color: var(--text-dark);
}

/* Spin loading styling */
:host-context(.dark) .ant-spin-dot-item {
  background-color: var(--primary-purple);
}

/* Custom scrollbar for content areas */
.overflow-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background-color: var(--hover-blue-gray);
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background-color: var(--secondary-purple);
}

/* Tab navigation styling */
.tab-button {
  position: relative;
  overflow: hidden;
}

.tab-button::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-purple);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.tab-button.active::before {
  transform: translateX(0);
}

/* Performance indicator colors */
.performance-excellent {
  color: #52c41a;
}

.performance-good {
  color: #faad14;
}

.performance-poor {
  color: #ff4d4f;
}

/* Agent type indicators */
.agent-ai {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.agent-human {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* Comparison cards */
.comparison-card {
  position: relative;
  overflow: hidden;
}

.comparison-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary-purple), var(--secondary-purple));
}

/* Filter panel animation */
.filter-panel {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .grid-cols-4,
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
  
  .flex-shrink-0 {
    padding: 1rem;
  }
}

/* Loading state */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

:host-context(.dark) .loading-overlay {
  background: rgba(52, 53, 65, 0.8);
}

/* Chart container styling */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Empty state styling */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Metric comparison styling */
.metric-comparison {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 6px;
  background: rgba(16, 163, 127, 0.05);
  border: 1px solid rgba(16, 163, 127, 0.2);
}

:host-context(.dark) .metric-comparison {
  background: rgba(16, 163, 127, 0.1);
  border-color: rgba(16, 163, 127, 0.3);
}
