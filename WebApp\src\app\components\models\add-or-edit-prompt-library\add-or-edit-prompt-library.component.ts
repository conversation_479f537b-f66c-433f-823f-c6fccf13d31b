import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { WorkspaceServiceProxy, AgentDefinitionServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { AuthService } from '../../../../shared/services/auth.service';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface Workspace {
  title: string;
}

interface Agent {
  agentName: string;
}

interface PromptData {
  id?: string;
  shortMessage: string;
  prompt: string;
  workspaceName?: string;
  agentName?: string;
}

@Component({
  selector: 'app-add-or-edit-prompt-library',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    ReactiveFormsModule
  ],
  templateUrl: './add-or-edit-prompt-library.component.html',
  styleUrls: ['./add-or-edit-prompt-library.component.css']
})
export class AddOrEditPromptLibraryComponent implements OnInit {
  promptForm: FormGroup;
  workspaces: Workspace[] = [];
  agents: Agent[] = [];
  promptData: any = {
    id: null,
    shortMessage: '',
    prompt: '',
    workspaceName: '',
    agentName: ''
  };
  isWorkspaceDropdownOpen = false;
  isWorkspaceDropdownAbove = false;
  isAgentDropdownOpen = false;
  isAgentDropdownAbove = false;

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<AddOrEditPromptLibraryComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { prompt?: PromptData },
    private workspaceService: WorkspaceServiceProxy,
    private agentService: AgentDefinitionServiceProxy,
    public authService: AuthService,
    private router: Router,
    private message: NzMessageService
  ) {
    this.promptForm = this.fb.group({
      shortMessage: ['', [Validators.required, Validators.maxLength(100)]],
      prompt: ['', [Validators.required, Validators.maxLength(1000)]],
      workspaceName: [''],
      agentName: ['']
    }, { validators: this.exclusiveSelectionValidator });
  }

  ngOnInit(): void {
    if (this.data.prompt) {
      this.promptData = { ...this.data.prompt };
    }
    this.loadWorkspaces();
    this.loadAgents();
  }

  loadWorkspaces(): void {
    if (this.authService.isAdmin()) {
      this.workspaceService.getAll().subscribe({
        next: (res: any) => {
          this.workspaces = res;
        },
        error: (error: any) => {
          this.message.error('Failed to load workspaces');
          console.error('Error loading workspaces:', error);
        }
      });
    } else {
      this.workspaceService.getWorkspacesByUserEmail().subscribe({
        next: (res: any) => {
          this.workspaces = res;
        },
        error: (error: any) => {
          this.message.error('Failed to load user workspaces');
          console.error('Error loading user workspaces:', error);
        }
      });
    }
  }

  loadAgents(): void {
    this.agentService.getAllAgentName().subscribe({
      next: (res: any) => {
        this.agents = res;
      },
      error: (error: any) => {
        this.message.error('Failed to load agents');
        console.error('Error loading agents:', error);
      }
    });
  }

  exclusiveSelectionValidator(form: FormGroup): { [key: string]: boolean } | null {
    const workspaceName = form.get('workspaceName')?.value;
    const agentName = form.get('agentName')?.value;
    return (workspaceName && agentName) ? { exclusiveSelection: true } : null;
  }

  toggleWorkspaceDropdown() {
    this.isWorkspaceDropdownOpen = !this.isWorkspaceDropdownOpen;
  }

  selectWorkspace(workspace: string) {
    this.promptData.workspaceName = workspace;
    this.isWorkspaceDropdownOpen = false;
  }

  toggleAgentDropdown() {
    this.isAgentDropdownOpen = !this.isAgentDropdownOpen;
  }

  selectAgent(agent: string) {
    this.promptData.agentName = agent;
    this.isAgentDropdownOpen = false;
  }

  onSubmit(): void {
    if (!this.promptData.shortMessage || !this.promptData.prompt) return;
    if (!this.promptData.workspaceName && !this.promptData.agentName) return;
    if (this.promptData.workspaceName && this.promptData.agentName) return;
    // Set the other field to null if one is selected
    if (this.promptData.workspaceName) {
      this.promptData.agentName = null;
    } else if (this.promptData.agentName) {
      this.promptData.workspaceName = null;
    }
    this.dialogRef.close(this.promptData);
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
