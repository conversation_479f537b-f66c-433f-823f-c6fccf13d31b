import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Location, CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { DateTime } from 'luxon';
import {
  CommentDto,
  CommentServiceProxy,
  ProjectsServiceProxy,
  ProjectTask,
  ProjectViewDto,
  TaskServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
import { getRemoteServiceBaseUrl } from '../../app.config';
import { AuthService } from '../../../shared/services/auth.service';

@Component({
  selector: 'app-request-detail',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './request-detail.component.html',
  styleUrl: './request-detail.component.css',
  providers: [ProjectsServiceProxy, CommentServiceProxy, TaskServiceProxy]
})
export class RequestDetailComponent implements OnInit {
  // Main project data
  project: any = null;
  tabs: string = 'insight';
  requestId: number = 0;
  matchedDocuments: any[] = [];
  tasks: ProjectTask[] = [];
  groupedTasks: any[] = [];
  comments: any[] = [];
  newTaskMessage: string = '';
  startDate: string = '';
  taskComplexity: string = '';
  editingTask: any = null;
  newComment: CommentDto = new CommentDto();
  loginedUser: any;
  baseUrl: string = getRemoteServiceBaseUrl();
  assignedEmails: string = '';
  taskId: number | null = null;
  isTaskUpdating: boolean = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private location: Location,
    private sanitizer: DomSanitizer,
    private _projectService: ProjectsServiceProxy,
    private _commentService: CommentServiceProxy,
    private _taskService: TaskServiceProxy,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.tabs = 'insight';
    this.loginedUser = this.authService.getUser();
    this.route.params.subscribe((params) => {
      this.requestId = params['id'] ? +params['id'] : 0;
      if (this.requestId) {
        this.loadProjectData(this.requestId);
        this.loadTasks(this.requestId);
        this.loadComments(this.requestId);
      }
    });
  }

  loadProjectData(id: number) {
    this._projectService.getById(id).subscribe((res: any) => {
      this.project = res;
      this.matchedDocuments = res.matchedDocuments || [];
    });
  }

  loadTasks(projectId: number) {
    this._taskService.getByProjectId(projectId).subscribe((res: any) => {
      this.tasks = res || [];
      this.filterTasksByType(this.tasks);
    });
  }

  filterTasksByType(tasks: any) {
    let filteredArrary = this.tasks.map((task: any) => {
      return { ...task, type: task.message.split('-')[0].trim() };
    });
    this.groupedTasks = filteredArrary.reduce((acc: any[], task: any) => {
      const existingGroup = acc.find((group) => group.type === task.type);
      if (existingGroup) {
        existingGroup.tasks.push(task);
        existingGroup.startDate = DateTime.min(DateTime.fromISO(existingGroup.startDate), DateTime.fromISO(task.createdAt)).toISODate();
        existingGroup.endDate = DateTime.max(DateTime.fromISO(existingGroup.endDate), DateTime.fromISO(task.dueDate)).toISODate();
      } else {
        acc.push({
          type: task.type,
          tasks: [task],
          startDate: task.dueDate,
          endDate: task.dueDate
        });
      }
      return acc;
    }, []);
  }

  loadComments(projectId: number) {
    this._commentService.getAllByProjectId(projectId).subscribe((res: any) => {
      this.comments = res || [];
      this.sortMessagesByDate();
    });
  }

  sortMessagesByDate() {
    this.comments.sort((a, b) => {
      return new Date(b.timeStamp || b.createdAt).getTime() - new Date(a.timeStamp || a.createdAt).getTime();
    });
  }

  // Status management
  updateStatus(newStatus: string): void {
    if (this.project) {
      this.project.status = newStatus;
      this._projectService.changeStatus(this.project.status, this.project.id).subscribe((res: any) => {
        this.loadProjectData(this.project.id);
        alert('Status updated successfully');
      });
    }
  }

  // Task management
  addTask(): void {
    if (this.newTaskMessage.trim() && this.startDate && this.project) {
      const newTask = new ProjectTask();
      newTask.message = this.newTaskMessage;
      newTask.projectId = this.project.id;
      newTask.isCompleted = false;
      newTask.dueDate = DateTime.fromISO(this.startDate);
      newTask.id = this.taskId ? this.taskId : 0;
      newTask.createdAt = DateTime.now();
      newTask.assignedEmails = this.assignedEmails ? this.assignedEmails : (this.loginedUser?.email || '');
      newTask.complexity = this.taskComplexity || '';
      this._taskService.createOrUpdate(newTask).subscribe((res) => {
        const index = this.tasks.findIndex((task) => task.id === res.id);
        if (index !== -1) {
          this.tasks[index] = res;
        } else {
          this.tasks.push(res);
        }
        this.filterTasksByType(this.tasks);
        alert('Task added/updated successfully');
        this.isTaskUpdating = false;
      });
      this.newTaskMessage = '';
      this.startDate = '';
      this.taskId = null;
      this.editingTask = null;
    }
  }

  editTask(task: any): void {
    this.taskId = task.id;
    this._taskService.getTaskById(task.id).subscribe((res) => {
      this.newTaskMessage = res.message ?? '';
      this.startDate = res.dueDate ? (DateTime.fromISO(res.dueDate.toString()).toISODate() || '') : '';
      this.taskComplexity = res.complexity ?? '';
      this.assignedEmails = typeof res.assignedEmails === 'string' ? res.assignedEmails : '';
    });
    this.editingTask = task;
    this.isTaskUpdating = true;
  }

  deleteTask(id: number): void {
    if (confirm('Are you sure you want to delete this task?')) {
      this._taskService.delete(id).subscribe(() => {
        this.tasks = this.tasks.filter((task) => task.id !== id);
        this.filterTasksByType(this.tasks);
        alert('Task deleted successfully');
      });
    }
  }

  updateTaskStatus(id: number, isCompleted: boolean) {
    this._taskService.updateTaskStatus(id, `${isCompleted}`).subscribe((res) => {
      const index = this.tasks.findIndex((task) => task.id === res.id);
      if (index !== -1) {
        this.tasks[index] = res;
      } else {
        this.tasks.push(res);
      }
      this.filterTasksByType(this.tasks);
      alert(res.isCompleted ? 'Task completed successfully' : 'Task reopened successfully');
    });
  }

  // Comments
  addComment(): void {
    if (!this.newComment.message?.trim() || !this.project) return;
    this.newComment.projectId = this.project.id;
    this._commentService.addComment(this.newComment).subscribe(() => {
      this.newComment = new CommentDto();
      this.loadComments(this.project.id);
      alert('Comment added successfully');
    });
  }

  // Attachments
  getSafeUrl(file: string): SafeUrl {
    return this.sanitizer.bypassSecurityTrustUrl(
      this.baseUrl + '/api/File/Getfile/' + file
    );
  }

  downloadAttachment(file: string): void {
    const url = this.baseUrl + '/api/File/Getfile/' + file;
    window.open(url, '_blank');
  }

  deleteAttachement(file: string): void {
    if (confirm('Are you sure you want to delete this attachment?')) {
      // TODO: Implement backend delete if available
      alert('Attachment deleted (implement backend call if needed)');
    }
  }

  // Navigation
  goBack(): void {
    this.location.back();
  }

  // Tab management
  onSeeTasks(): void {
    this.tabs = 'tasks';
  }
  onSeeComments(): void {
    this.tabs = 'comments';
  }
  onSeeAttachments(): void {
    this.tabs = 'attachments';
  }
  onSeeDocs(id: number): void {
    this.router.navigate([this.router.url, id]);
  }

  // Utility
  findUser(email: string, projectCreatedEmail?: string): string {
    if (email === projectCreatedEmail) {
      return 'User';
    }
    const user = this.authService.getUser();
    const userEmail = user?.email;
    if (userEmail === email) {
      return 'You';
    }
    return email;
  }
  getUserInitial(email: string, fallbackEmail?: string): string {
    const userName = this.findUser(email, fallbackEmail);
    return userName?.charAt(0)?.toUpperCase() || 'U';
  }
  getProjectFilesName(): string[] {
    return this.project?.filesName || [];
  }
  getProjectSummary(): string {
    return this.project?.summary?.slice(18) || '';
  }
  assignMember(task: any): void {
    // Stub for now
    console.log('Assign member called for task:', task);
  }
}
