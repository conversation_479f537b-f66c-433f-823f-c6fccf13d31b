import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TextFieldModule } from '@angular/cdk/text-field';
import { NzMessageService } from 'ng-zorro-antd/message';

import {
    AgentDefinition,
    CreateAgentEvaluation,
    AgentEvaluation
} from '../../models/agent-evaluation.model';

import {
    AgentEvaluationServiceProxy,
    CreateAgentEvaluationDto
} from '../../../shared/service-proxies/service-proxies';
import { catchError, finalize, of } from 'rxjs';

export interface AddPromptDialogData {
    agents: AgentDefinition[];
    editingEvaluation?: AgentEvaluation;
    mode: 'add' | 'edit';
}

export interface AddPromptDialogResult {
    success: boolean;
    data?: any;
}

@Component({
    selector: 'app-add-prompt-dialog',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatIconModule,
        MatProgressSpinnerModule,
        TextFieldModule
    ],
    templateUrl: './add-prompt-dialog.component.html',
    styleUrl: './add-prompt-dialog.component.css'
})
export class AddPromptDialogComponent implements OnInit {
    promptForm: FormGroup;
    submitting = false;
    agents: AgentDefinition[] = [];
    mode: 'add' | 'edit' = 'add';
    editingEvaluation?: AgentEvaluation;

    constructor(
        private fb: FormBuilder,
        private dialogRef: MatDialogRef<AddPromptDialogComponent>,
        @Inject(MAT_DIALOG_DATA) public data: AddPromptDialogData,
        private agentEvaluationServiceProxy: AgentEvaluationServiceProxy,
        private message: NzMessageService
    ) {
        this.agents = data.agents || [];
        this.mode = data.mode || 'add';
        this.editingEvaluation = data.editingEvaluation;

        this.promptForm = this.fb.group({
            agentName: [
                this.mode === 'edit' ? this.editingEvaluation?.agentName : '',
                Validators.required
            ],
            prompt: [
                this.mode === 'edit' ? this.editingEvaluation?.prompt : '',
                Validators.required
            ],
            expectedOutput: [
                this.mode === 'edit' ? this.editingEvaluation?.expectedOutput || '' : ''
            ]
        });

        // Disable agent selection in edit mode
        if (this.mode === 'edit') {
            this.promptForm.get('agentName')?.disable();
        }
    }

    ngOnInit(): void {
        // Any additional initialization if needed
    }

    get isEditMode(): boolean {
        return this.mode === 'edit';
    }

    get dialogTitle(): string {
        return this.isEditMode ? 'Edit Evaluation' : 'Add New Prompt';
    }

    get submitButtonText(): string {
        if (this.submitting) {
            return this.isEditMode ? 'Updating...' : 'Adding...';
        }
        return this.isEditMode ? 'Update' : 'Add';
    }

    onCancel(): void {
        this.dialogRef.close({ success: false });
    }

    onSubmit(): void {
        if (this.promptForm.invalid) {
            this.markFormGroupTouched();
            return;
        }

        if (this.isEditMode) {
            this.updateEvaluation();
        } else {
            this.createEvaluation();
        }
    }

    private createEvaluation(): void {
        this.submitting = true;
        const formValue = this.promptForm.value;
        const evaluation: CreateAgentEvaluation = {
            agentName: formValue.agentName,
            prompt: formValue.prompt,
            expectedOutput: formValue.expectedOutput
        };

        const dto = new CreateAgentEvaluationDto();
        dto.agentName = evaluation.agentName;
        dto.prompt = evaluation.prompt;
        dto.expectedOutput = evaluation.expectedOutput;

        this.agentEvaluationServiceProxy.runEvaluation(dto)
            .pipe(
                finalize(() => this.submitting = false),
                catchError(error => {
                    this.message.error('Failed to submit evaluation');
                    console.error('Error submitting evaluation:', error);
                    return of(null);
                })
            )
            .subscribe(result => {
                if (result) {
                    this.message.success('Evaluation submitted successfully');
                    this.dialogRef.close({ success: true, data: result });
                }
            });
    }

    private updateEvaluation(): void {
        if (!this.editingEvaluation) {
            return;
        }

        this.submitting = true;
        const formValue = this.promptForm.value;
        const updateData = {
            prompt: formValue.prompt,
            expectedOutput: formValue.expectedOutput
        };

        this.agentEvaluationServiceProxy.update(this.editingEvaluation.id, updateData)
            .pipe(
                finalize(() => this.submitting = false),
                catchError(error => {
                    this.message.error('Failed to update evaluation');
                    console.error('Error updating evaluation:', error);
                    return of(null);
                })
            )
            .subscribe(result => {
                if (result) {
                    this.message.success('Evaluation updated successfully');
                    this.dialogRef.close({ success: true, data: result });
                }
            });
    }

    private markFormGroupTouched(): void {
        Object.keys(this.promptForm.controls).forEach(key => {
            const control = this.promptForm.get(key);
            control?.markAsTouched();
        });
    }

    getErrorMessage(fieldName: string): string {
        const control = this.promptForm.get(fieldName);
        if (control?.hasError('required')) {
            switch (fieldName) {
                case 'agentName':
                    return 'Please select an agent';
                case 'prompt':
                    return 'Please enter a prompt';
                default:
                    return 'This field is required';
            }
        }
        return '';
    }

    hasError(fieldName: string): boolean {
        const control = this.promptForm.get(fieldName);
        return !!(control?.invalid && control?.touched);
    }
}
