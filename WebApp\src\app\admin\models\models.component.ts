import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EmbeddingConfigurationProvider, EmbeddingModelDto, ModelDetailsServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { FormsModule } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ChangeActiveModelComponent } from '../embedding/change-active-model/change-active-model.component';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { RemoveProviderPrefixPipe } from "../../../shared/pipes/remove-provider-prefix.pipe";
import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';

@Component({
  selector: 'app-models',
  standalone: true,
  imports: [CommonModule, FormsModule, ServiceProxyModule, NzModalModule, RemoveProviderPrefixPipe, SpinnerComponent],
  templateUrl: './models.component.html',
  // Assuming you'll create a separate HTML file for models
})
export class ModelsComponent {
  models: any = [];
  filteredModels: any = [];
  paginatedModels: any = [];
  searchModelsQuery = '';
  selectedProvider = '';
  isLoading = false; // Loading state for spinner
  uniqueProviders: any[] = [];
  embeddingModels: EmbeddingModelDto[] = [];
  activeEmbeddingModel: EmbeddingConfigurationProvider = new EmbeddingConfigurationProvider();

  // Pagination properties
  currentPage: number = 1;
  pageSize: number = 9;
  totalPages: number = 1;
  previousPageSize: number = 9;

  // For custom provider filter dropdown
  isProviderDropdownOpen = false;
  isProviderDropdownAbove = false;
  selectedProviderLabel: string = '';

  // For custom pagination page size dropdown
  isPageSizeDropdownOpen = false;
  isPageSizeDropdownAbove = false;

  Math = Math;

  constructor(
    private modelDetails: ModelDetailsServiceProxy,
    private nzMessageService: NzMessageService,
    public nzModalService: NzModalService,
  ) { }

  ngOnInit() {
    this.loadModels();
    this.selectedProviderLabel = this.getProviderLabel(this.selectedProvider);
  }

  loadModels() {
    this.isLoading = true; // Show spinner
    this.modelDetails.getAll().subscribe({
      next: (res: any) => {
        if (res) {
          this.models = res;
          this.updateFilteredModels();
          this.uniqueProviders = [...new Set(res.map((model: any) => model.modelProvider))];
        }
        this.modelDetails.current().subscribe({
          next: (res: any) => {
            if (res) {
              this.activeEmbeddingModel = res;
            }
            this.isLoading = false; // Hide spinner after all data is loaded
          },
          error: (error: any) => {
            console.error('Error loading current embedding model:', error);
            this.nzMessageService.error('Failed to load current embedding model');
            this.isLoading = false; // Hide spinner on error
          }
        });
      },
      error: (error: any) => {
        console.error('Error loading models:', error);
        this.nzMessageService.error('Failed to load models');
        this.isLoading = false; // Hide spinner on error
      }
    });
  }

  updateFilteredModels() {
    let result = [...this.models];

    // Search filter
    if (this.searchModelsQuery) {
      result = result.filter((model: any) =>
        model.modelName.toLowerCase().includes(this.searchModelsQuery.toLowerCase())
      );
    }

    // Provider filter
    if (this.selectedProvider) {
      result = result.filter((model: any) => model.modelProvider === this.selectedProvider);
    }

    this.filteredModels = result;
    this.updatePagination();
  }

  get filteredModelsGetter() {
    return this.filteredModels;
  }

  updateModelIsActive(model: any) {
    this.modelDetails
      .updateIsActive(model.modelName, !model.isActive)
      .subscribe({
        next: (res: any) => {
          if (!res.isError) {
            this.nzMessageService.success(res.message);
            this.loadModels(); // Reload models to reflect changes
          } else {
            this.nzMessageService.error(res.message || 'Failed to update model status');
          }
        },
        error: (error: any) => {
          console.error('Error updating model status:', error);
          this.nzMessageService.error('Failed to update model status');
        }
      });
  }

  filterModels() {
    this.updateFilteredModels();
  }

  toggleProviderDropdown() {
    this.isProviderDropdownOpen = !this.isProviderDropdownOpen;
    if (this.isProviderDropdownOpen) {
      setTimeout(() => {
        const btn = document.getElementById('providerDropdownBtn');
        const dropdown = document.getElementById('providerDropdownMenu');
        if (btn && dropdown) {
          const rect = btn.getBoundingClientRect();
          const dropdownHeight = dropdown.offsetHeight || 160;
          const spaceBelow = window.innerHeight - rect.bottom;
          this.isProviderDropdownAbove = spaceBelow < dropdownHeight + 16;
        }
      }, 0);
    }
  }

  selectProviderFilter(provider: string) {
    this.selectedProvider = provider;
    this.selectedProviderLabel = this.getProviderLabel(provider);
    this.isProviderDropdownOpen = false;
    this.filterModels();
  }

  getProviderLabel(provider: string): string {
    if (!provider) return 'All Providers';
    return provider;
  }

  togglePageSizeDropdown() {
    this.isPageSizeDropdownOpen = !this.isPageSizeDropdownOpen;
    if (this.isPageSizeDropdownOpen) {
      setTimeout(() => {
        const btn = document.getElementById('pageSizeDropdownBtn');
        const dropdown = document.getElementById('pageSizeDropdownMenu');
        if (btn && dropdown) {
          const rect = btn.getBoundingClientRect();
          const dropdownHeight = dropdown.offsetHeight || 160;
          const spaceBelow = window.innerHeight - rect.bottom;
          this.isPageSizeDropdownAbove = spaceBelow < dropdownHeight + 16;
        }
      }, 0);
    }
  }

  selectPageSize(size: number) {
    this.pageSize = size;
    this.isPageSizeDropdownOpen = false;
    this.currentPage = 1;
    this.updatePagination();
  }

  updatePagination() {
    // Ensure pageSize is a number
    this.pageSize = Number(this.pageSize);

    // Check if page size has changed
    const pageSizeChanged = this.previousPageSize !== this.pageSize;

    // Calculate total pages (minimum 1 page)
    this.totalPages = Math.max(1, Math.ceil(this.filteredModels.length / this.pageSize));

    // Reset to page 1 when page size changes
    if (pageSizeChanged) {
      this.currentPage = 1;
      console.log('Page size changed from', this.previousPageSize, 'to', this.pageSize, '- resetting to page 1');
    }

    // Ensure current page is within bounds
    if (this.currentPage < 1) this.currentPage = 1;
    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages;

    // Get current page of models
    if (this.filteredModels.length === 0) {
      this.paginatedModels = [];
    } else {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = Math.min(startIndex + this.pageSize, this.filteredModels.length);
      this.paginatedModels = this.filteredModels.slice(startIndex, endIndex);
    }

    // Store current page size for next comparison
    this.previousPageSize = this.pageSize;

    // Log pagination state for debugging
    console.log('Pagination updated:', {
      totalItems: this.filteredModels.length,
      pageSize: this.pageSize,
      totalPages: this.totalPages,
      currentPage: this.currentPage,
      itemsOnCurrentPage: this.paginatedModels.length
    });
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePagination();
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePagination();
    }
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePagination();
    }
  }

  openChangeModelDialog() {
    const modalRef = this.nzModalService.create({
      nzTitle: '',
      nzContent: ChangeActiveModelComponent,
      nzFooter: null,
      nzWidth: '400px',
      nzClassName: 'custom-modal-style'
    });

    const instance = modalRef.componentInstance;
    if (instance) {
      instance.modelChanged.subscribe((result: any) => {
        // Handle the model change event
        this.nzMessageService.success('Embedding model changed successfully');
        this.nzMessageService.info('Please Restart the Application to see the changes');
        setInterval(() => {
          this.nzMessageService.info('Please Restart the Application to see the changes of embedding');
        }, 5000);
      });
    }
  }
}
