/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* Custom checkbox styling */
.custom-checkbox {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid var(--hover-blue-gray);
  border-radius: 3px;
  background-color: transparent;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.custom-checkbox:checked {
  background-color: var(--primary-purple);
  border-color: var(--primary-purple);
}

.custom-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
}

.custom-checkbox:focus {
  outline: 2px solid var(--primary-purple);
  outline-offset: 2px;
}

/* Dark mode specific styles */
:host-context(.dark) input,
:host-context(.dark) select,
:host-context(.dark) button {
  color: var(--text-light);
  border-color: rgba(255, 255, 255, 0.1);
}

:host-context(.dark) .custom-checkbox {
  border-color: rgba(255, 255, 255, 0.3);
}

:host-context(.dark) .custom-checkbox:checked {
  background-color: var(--primary-purple);
  border-color: var(--primary-purple);
}

/* Responsive styles */
@media (max-width: 768px) {
  .fixed {
    padding: 1rem;
  }

  .w-full.max-w-md {
    max-width: 100%;
  }
}
