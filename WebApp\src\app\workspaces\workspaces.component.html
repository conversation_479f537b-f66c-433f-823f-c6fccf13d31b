<div class="bg-[var(--background-light-gray)] h-full p-[var(--padding-small)]" style="height: calc(100vh - 74px); overflow-y: auto;">
  <!-- Main Content -->
  <main class="flex-1 rounded-[var(--border-radius-large)] p-[var(--padding-medium)] sm:p-[var(--padding-large)] shadow-[var(--box-shadow)] overflow-auto bg-[var(--background-white)]">
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
      <!-- Add Workspace Card - Only visible to admin users -->
      <div *ngIf="isAdmin"
        class="border-2 border-dashed border-[var(--hover-blue-gray)] bg-[var(--background-white)] rounded-[var(--border-radius-large)] dark:border-[var(--hover-blue-gray)] dark:bg-[var(--background-white)] h-64 flex items-center flex-col justify-center cursor-pointer transition-[var(--transition-default)] dark:hover:bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)]/20 group"
        (click)="addWorkspace()">
        <i class="ri-add-line text-3xl sm:text-4xl text-[var(--text-medium-gray)] dark:text-[var(--text-dark)] transition-colors duration-300"></i>
        <span class="text-[var(--text-medium-gray)] dark:text-[var(--text-dark)] text-base sm:text-lg font-[var(--font-weight-bold)] mt-2 transition-colors duration-300">
          Add Workspace
        </span>
      </div>

      <!-- Workspace Cards -->
      @for (workspace of workspaceList; track $index) {
      <div class="relative border-2 border-[var(--hover-blue-gray)]  dark:bg-gray-800 bg-gray-300 bg-opacity-20 rounded-[var(--border-radius-large)] h-auto flex flex-col cursor-pointer p-[var(--padding-medium)] transition-[var(--transition-default)] ease-in-out hover:scale-105 hover:shadow-[0_4px_15px_var(--shadow-color)]  dark:border-[var(--hover-blue-gray)] group">
        <!-- Action Buttons -->
        <div *ngIf="isAdmin"
          class="p-2 flex justify-end gap-3 w-auto absolute z-50 right-3 top-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button
            class="action-button w-9 h-9 flex items-center justify-center rounded-md  bg-[var(--primary-purple)] transition-all duration-200 border-none shadow-sm"
            (click)="editWorkspace(workspace, $event)" aria-label="Edit Workspace">
            <i class="ri-edit-line text-white"></i>
          </button>
          <button
            class="action-button w-9 h-9 flex items-center justify-center rounded-md  bg-[var(--primary-purple)] transition-all duration-200 border-none shadow-sm"
            (click)="deleteWorkspace(workspace, $event)" aria-label="Delete Workspace">
            <i class="ri-delete-bin-line text-red-100"></i>
          </button>
        </div>

        <!-- Workspace Content -->
        <div class="flex flex-col h-full justify-between" (click)="viewWorkspace(workspace, $event)">
          <div class="space-y-3">
            <h2 class="text-[var(--text-dark)] text-xl sm:text-2xl font-[var(--font-weight-bold)] line-clamp-1 drop-shadow-sm group-hover:text-[var(--primary-purple)] transition-colors duration-300 dark:text-[var(--text-dark)]">
              {{ workspace.title }}
            </h2>

            <!-- Description -->
            <div class="bg-[var(--background-white)]/80 p-[var(--padding-small)] rounded-[var(--border-radius-small)] shadow-[var(--box-shadow)]">
              <p class="text-[var(--text-medium-gray)] text-[var(--font-size-body)] font-[var(--font-weight-regular)] line-clamp-2 dark:text-[var(--text-medium-gray)]">
                {{ workspace.description }}
              </p>
            </div>

            <!-- System Information -->
            <div class="bg-[var(--background-white)]/80 p-[var(--padding-small)] rounded-[var(--border-radius-small)] shadow-[var(--box-shadow)]">
              <h3 class="text-[var(--font-size-body)] font-[var(--font-weight-bold)] text-[var(--text-dark)] dark:text-[var(--text-dark)]">System Information:</h3>
              <p class="text-[var(--text-medium-gray)] text-[var(--font-size-body)] line-clamp-2 dark:text-[var(--text-medium-gray)]">
                {{ workspace.systemInformation }}
              </p>
            </div>

            <!-- Model Information -->
            <div class="bg-[var(--background-white)]/80 p-[var(--padding-small)] rounded-[var(--border-radius-small)] shadow-[var(--box-shadow)]">
              <h3 class="text-[var(--font-size-body)] font-[var(--font-weight-bold)] text-[var(--text-dark)] dark:text-[var(--text-dark)]">Model:</h3>
              <p class="text-[var(--text-medium-gray)] text-[var(--font-size-body)] dark:text-[var(--text-medium-gray)]">
                {{ workspace.modelName }}
              </p>
            </div>
          </div>

          <!-- Workspace Properties -->
          <div class="flex flex-wrap gap-2 mt-4">
            <div class="flex items-center px-2 py-1 rounded-full text-xs font-[var(--font-weight-regular)]"
                 [ngClass]="workspace.isDefault ? 'bg-[var(--active-tab-bg)] text-[var(--text-dark)]' : 'bg-red-100 text-red-800'">
              <i class="ri-checkbox-circle-fill mr-1" *ngIf="workspace.isDefault"></i>
              <i class="ri-close-circle-fill mr-1" *ngIf="!workspace.isDefault"></i>
              <span>Default Workspace</span>
            </div>

            <div class="flex items-center px-2 py-1 rounded-full text-xs font-[var(--font-weight-regular)]"
                 [ngClass]="workspace.isProjectManagement ? 'bg-[var(--active-tab-bg)] text-[var(--text-dark)]' : 'bg-red-100 text-red-800'">
              <i class="ri-checkbox-circle-fill mr-1" *ngIf="workspace.isProjectManagement"></i>
              <i class="ri-close-circle-fill mr-1" *ngIf="!workspace.isProjectManagement"></i>
              <span>Project Management</span>
            </div>
          </div>
        </div>

        <!-- Members Section -->
        <div class="mt-4 flex items-center border-t border-[var(--hover-blue-gray)]/50 pt-3">
          <div class="relative flex items-center gap-2">
            @if (workspace.members?.length) {
            <div *ngFor="let member of workspace.members?.slice(0, 4); let i = index" (click)="openDialog(workspace.id)"
                 class="flex items-center justify-center w-9 h-9 rounded-full bg-[var(--hover-blue-gray)] text-[var(--text-dark)] text-xs font-[var(--font-weight-bold)] border-2 border-[var(--background-white)] shadow-[var(--box-shadow)] hover:scale-110 transition-transform duration-200 dark:bg-[var(--hover-blue-gray)] dark:text-[var(--text-dark)]"
                 [ngStyle]="{ 'z-index': workspace.members.length + i, 'margin-left': i > 0 ? '-0.75rem' : '0' }">
              {{ member.name.slice(0, 2) | uppercase }}
            </div>
            <div *ngIf="workspace.members.length > 4"
                 class="flex items-center justify-center w-9 h-9 rounded-full bg-[var(--text-medium-gray)] text-[var(--background-white)] text-xs font-[var(--font-weight-bold)] border-2 border-[var(--background-white)] shadow-[var(--box-shadow)] dark:bg-[var(--text-medium-gray)]"
                 [ngStyle]="{ 'z-index': workspace.members.length + 3, 'margin-left': '-0.75rem' }">
              +{{ workspace.members.length - 4 }}
            </div>
            } @else {
            <div
              class="flex items-center justify-center h-8 text-[var(--font-size-body)] text-[var(--text-medium-gray)] hover:text-[var(--primary-purple)] transition-colors duration-200 dark:text-[var(--text-medium-gray)] dark:hover:text-[var(--primary-purple)]"
              *ngIf="isAdmin" (click)="openDialog(workspace.id)">
              <i class="ri-user-add-line"></i>
              <span class="ml-1.5 font-[var(--font-weight-regular)]">Add members</span>
            </div>

            <div *ngIf="!isAdmin" class="flex items-center justify-center h-8 text-[var(--font-size-body)] text-[var(--text-medium-gray)] dark:text-[var(--text-medium-gray)]">
              <span>No members</span>
            </div>
            }
          </div>
        </div>
      </div>
      }
    </div>
  </main>
</div>
