import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class SidebarService {
  private agentsSubject = new BehaviorSubject<string[]>([]);
  private workspacesSubject = new BehaviorSubject<string[]>([]);

  agents$ = this.agentsSubject.asObservable();
  workspaces$ = this.workspacesSubject.asObservable();

  updateItemPosition(itemType: 'agent' | 'workspace', itemName: string): void {
    if (itemType === 'agent') {
      const agents = this.agentsSubject.getValue();
      const updatedAgents = [itemName, ...agents.filter((agent) => agent !== itemName)];
      this.agentsSubject.next(updatedAgents);
    } else if (itemType === 'workspace') {
      const workspaces = this.workspacesSubject.getValue();
      const updatedWorkspaces = [itemName, ...workspaces.filter((workspace) => workspace !== itemName)];
      this.workspacesSubject.next(updatedWorkspaces);
    }
  }
}
