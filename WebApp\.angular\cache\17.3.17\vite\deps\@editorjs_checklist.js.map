{"version": 3, "sources": ["../../../../../node_modules/@editorjs/checklist/dist/checklist.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var e=document.createElement(\"style\");e.appendChild(document.createTextNode('.cdx-checklist{gap:6px;display:flex;flex-direction:column}.cdx-checklist__item{display:flex;box-sizing:content-box;align-items:flex-start}.cdx-checklist__item-text{outline:none;flex-grow:1;line-height:1.57em}.cdx-checklist__item-checkbox{width:22px;height:22px;display:flex;align-items:center;margin-right:8px;margin-top:calc(.785em - 11px);cursor:pointer}.cdx-checklist__item-checkbox svg{opacity:0;height:20px;width:20px;position:absolute;left:-1px;top:-1px;max-height:20px}@media (hover: hover){.cdx-checklist__item-checkbox:not(.cdx-checklist__item-checkbox--no-hover):hover .cdx-checklist__item-checkbox-check svg{opacity:1}}.cdx-checklist__item-checkbox-check{cursor:pointer;display:inline-block;flex-shrink:0;position:relative;width:20px;height:20px;box-sizing:border-box;margin-left:0;border-radius:5px;border:1px solid #C9C9C9;background:#fff}.cdx-checklist__item-checkbox-check:before{content:\"\";position:absolute;top:0;right:0;bottom:0;left:0;border-radius:100%;background-color:#369fff;visibility:hidden;pointer-events:none;transform:scale(1);transition:transform .4s ease-out,opacity .4s}@media (hover: hover){.cdx-checklist__item--checked .cdx-checklist__item-checkbox:not(.cdx-checklist__item--checked .cdx-checklist__item-checkbox--no-hover):hover .cdx-checklist__item-checkbox-check{background:#0059AB;border-color:#0059ab}}.cdx-checklist__item--checked .cdx-checklist__item-checkbox-check{background:#369FFF;border-color:#369fff}.cdx-checklist__item--checked .cdx-checklist__item-checkbox-check svg{opacity:1}.cdx-checklist__item--checked .cdx-checklist__item-checkbox-check svg path{stroke:#fff}.cdx-checklist__item--checked .cdx-checklist__item-checkbox-check:before{opacity:0;visibility:visible;transform:scale(2.5)}')),document.head.appendChild(e)}}catch(c){console.error(\"vite-plugin-css-injected-by-js\",c)}})();\nconst k = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M7 12L10.4884 15.8372C10.5677 15.9245 10.705 15.9245 10.7844 15.8372L17 9\"/></svg>', g = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M9.2 12L11.0586 13.8586C11.1367 13.9367 11.2633 13.9367 11.3414 13.8586L14.7 10.5\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>';\nfunction d() {\n  const s = document.activeElement, t = window.getSelection().getRangeAt(0), n = t.cloneRange();\n  return n.selectNodeContents(s), n.setStart(t.endContainer, t.endOffset), n.extractContents();\n}\nfunction C(s) {\n  const e = document.createElement(\"div\");\n  return e.appendChild(s), e.innerHTML;\n}\nfunction c(s, e = null, t = {}) {\n  const n = document.createElement(s);\n  Array.isArray(e) ? n.classList.add(...e) : e && n.classList.add(e);\n  for (const i in t)\n    n[i] = t[i];\n  return n;\n}\nfunction m(s) {\n  return s.innerHTML.replace(\"<br>\", \" \").trim();\n}\nfunction p(s, e = !1, t = void 0) {\n  const n = document.createRange(), i = window.getSelection();\n  n.selectNodeContents(s), t !== void 0 && (n.setStart(s, t), n.setEnd(s, t)), n.collapse(e), i.removeAllRanges(), i.addRange(n);\n}\nElement.prototype.matches || (Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector);\nElement.prototype.closest || (Element.prototype.closest = function(s) {\n  let e = this;\n  if (!document.documentElement.contains(e))\n    return null;\n  do {\n    if (e.matches(s))\n      return e;\n    e = e.parentElement || e.parentNode;\n  } while (e !== null && e.nodeType === 1);\n  return null;\n});\nclass f {\n  /**\n   * Notify core that read-only mode is supported\n   *\n   * @returns {boolean}\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Allow to use native Enter behaviour\n   *\n   * @returns {boolean}\n   * @public\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   *\n   * @returns {{icon: string, title: string}}\n   */\n  static get toolbox() {\n    return {\n      icon: g,\n      title: \"Checklist\"\n    };\n  }\n  /**\n   * Allow Checkbox Tool to be converted to/from other block\n   *\n   * @returns {{export: Function, import: Function}}\n   */\n  static get conversionConfig() {\n    return {\n      /**\n       * To create exported string from the checkbox, concatenate items by dot-symbol.\n       *\n       * @param {ChecklistData} data - checklist data to create a string from that\n       * @returns {string}\n       */\n      export: (e) => e.items.map(({ text: t }) => t).join(\". \"),\n      /**\n       * To create a checklist from other block's string, just put it at the first item\n       *\n       * @param {string} string - string to create list tool data from that\n       * @returns {ChecklistData}\n       */\n      import: (e) => ({\n        items: [\n          {\n            text: e,\n            checked: !1\n          }\n        ]\n      })\n    };\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   *\n   * @param {object} options - block constructor options\n   * @param {ChecklistData} options.data - previously saved data\n   * @param {object} options.config - user config for Tool\n   * @param {object} options.api - Editor.js API\n   * @param {boolean} options.readOnly - read only mode flag\n   */\n  constructor({ data: e, config: t, api: n, readOnly: i }) {\n    this._elements = {\n      wrapper: null,\n      items: []\n    }, this.readOnly = i, this.api = n, this.data = e || {};\n  }\n  /**\n   * Returns checklist tag with items\n   *\n   * @returns {Element}\n   */\n  render() {\n    return this._elements.wrapper = c(\"div\", [this.CSS.baseBlock, this.CSS.wrapper]), this.data.items || (this.data.items = [\n      {\n        text: \"\",\n        checked: !1\n      }\n    ]), this.data.items.forEach((e) => {\n      const t = this.createChecklistItem(e);\n      this._elements.wrapper.appendChild(t);\n    }), this.readOnly ? this._elements.wrapper : (this._elements.wrapper.addEventListener(\"keydown\", (e) => {\n      const [t, n] = [13, 8];\n      switch (e.keyCode) {\n        case t:\n          this.enterPressed(e);\n          break;\n        case n:\n          this.backspace(e);\n          break;\n      }\n    }, !1), this._elements.wrapper.addEventListener(\"click\", (e) => {\n      this.toggleCheckbox(e);\n    }), this._elements.wrapper);\n  }\n  /**\n   * Return Checklist data\n   *\n   * @returns {ChecklistData}\n   */\n  save() {\n    let e = this.items.map((t) => {\n      const n = this.getItemInput(t);\n      return {\n        text: m(n),\n        checked: t.classList.contains(this.CSS.itemChecked)\n      };\n    });\n    return e = e.filter((t) => t.text.trim().length !== 0), {\n      items: e\n    };\n  }\n  /**\n   * Validate data: check if Checklist has items\n   *\n   * @param {ChecklistData} savedData — data received after saving\n   * @returns {boolean} false if saved data is not correct, otherwise true\n   * @public\n   */\n  validate(e) {\n    return !!e.items.length;\n  }\n  /**\n   * Toggle checklist item state\n   *\n   * @param {MouseEvent} event - click\n   * @returns {void}\n   */\n  toggleCheckbox(e) {\n    const t = e.target.closest(`.${this.CSS.item}`), n = t.querySelector(`.${this.CSS.checkboxContainer}`);\n    n.contains(e.target) && (t.classList.toggle(this.CSS.itemChecked), n.classList.add(this.CSS.noHover), n.addEventListener(\"mouseleave\", () => this.removeSpecialHoverBehavior(n), { once: !0 }));\n  }\n  /**\n   * Create Checklist items\n   *\n   * @param {ChecklistItem} item - data.item\n   * @returns {Element} checkListItem - new element of checklist\n   */\n  createChecklistItem(e = {}) {\n    const t = c(\"div\", this.CSS.item), n = c(\"span\", this.CSS.checkbox), i = c(\"div\", this.CSS.checkboxContainer), o = c(\"div\", this.CSS.textField, {\n      innerHTML: e.text ? e.text : \"\",\n      contentEditable: !this.readOnly\n    });\n    return e.checked && t.classList.add(this.CSS.itemChecked), n.innerHTML = k, i.appendChild(n), t.appendChild(i), t.appendChild(o), t;\n  }\n  /**\n   * Append new elements to the list by pressing Enter\n   *\n   * @param {KeyboardEvent} event - keyboard event\n   */\n  enterPressed(e) {\n    e.preventDefault();\n    const t = this.items, n = document.activeElement.closest(`.${this.CSS.item}`);\n    if (t.indexOf(n) === t.length - 1 && m(this.getItemInput(n)).length === 0) {\n      const u = this.api.blocks.getCurrentBlockIndex();\n      n.remove(), this.api.blocks.insert(), this.api.caret.setToBlock(u + 1);\n      return;\n    }\n    const a = d(), l = C(a), r = this.createChecklistItem({\n      text: l,\n      checked: !1\n    });\n    this._elements.wrapper.insertBefore(r, n.nextSibling), p(this.getItemInput(r), !0);\n  }\n  /**\n   * Handle backspace\n   *\n   * @param {KeyboardEvent} event - keyboard event\n   */\n  backspace(e) {\n    const t = e.target.closest(`.${this.CSS.item}`), n = this.items.indexOf(t), i = this.items[n - 1];\n    if (!i || !(window.getSelection().focusOffset === 0))\n      return;\n    e.preventDefault();\n    const l = d(), r = this.getItemInput(i), h = r.childNodes.length;\n    r.appendChild(l), p(r, void 0, h), t.remove();\n  }\n  /**\n   * Styles\n   *\n   * @private\n   * @returns {object<string>}\n   */\n  get CSS() {\n    return {\n      baseBlock: this.api.styles.block,\n      wrapper: \"cdx-checklist\",\n      item: \"cdx-checklist__item\",\n      itemChecked: \"cdx-checklist__item--checked\",\n      noHover: \"cdx-checklist__item-checkbox--no-hover\",\n      checkbox: \"cdx-checklist__item-checkbox-check\",\n      textField: \"cdx-checklist__item-text\",\n      checkboxContainer: \"cdx-checklist__item-checkbox\"\n    };\n  }\n  /**\n   * Return all items elements\n   *\n   * @returns {Element[]}\n   */\n  get items() {\n    return Array.from(this._elements.wrapper.querySelectorAll(`.${this.CSS.item}`));\n  }\n  /**\n   * Removes class responsible for special hover behavior on an item\n   * \n   * @private\n   * @param {Element} el - item wrapper\n   * @returns {Element}\n   */\n  removeSpecialHoverBehavior(e) {\n    e.classList.remove(this.CSS.noHover);\n  }\n  /**\n   * Find and return item's content editable element\n   *\n   * @private\n   * @param {Element} el - item wrapper\n   * @returns {Element}\n   */\n  getItemInput(e) {\n    return e.querySelector(`.${this.CSS.textField}`);\n  }\n}\nexport {\n  f as default\n};\n"], "mappings": ";;;CAAC,WAAU;AAAC;AAAa,MAAG;AAAC,QAAG,OAAO,WAAS,KAAI;AAAC,UAAI,IAAE,SAAS,cAAc,OAAO;AAAE,QAAE,YAAY,SAAS,eAAe,2sDAA2sD,CAAC,GAAE,SAAS,KAAK,YAAY,CAAC;AAAA,IAAC;AAAA,EAAC,SAAOA,IAAE;AAAC,YAAQ,MAAM,kCAAiCA,EAAC;AAAA,EAAC;AAAC,GAAG;AAC36D,IAAM,IAAI;AAAV,IAAsQ,IAAI;AAC1Q,SAAS,IAAI;AACX,QAAM,IAAI,SAAS,eAAe,IAAI,OAAO,aAAa,EAAE,WAAW,CAAC,GAAG,IAAI,EAAE,WAAW;AAC5F,SAAO,EAAE,mBAAmB,CAAC,GAAG,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,GAAG,EAAE,gBAAgB;AAC7F;AACA,SAAS,EAAE,GAAG;AACZ,QAAM,IAAI,SAAS,cAAc,KAAK;AACtC,SAAO,EAAE,YAAY,CAAC,GAAG,EAAE;AAC7B;AACA,SAAS,EAAE,GAAG,IAAI,MAAM,IAAI,CAAC,GAAG;AAC9B,QAAM,IAAI,SAAS,cAAc,CAAC;AAClC,QAAM,QAAQ,CAAC,IAAI,EAAE,UAAU,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,UAAU,IAAI,CAAC;AACjE,aAAW,KAAK;AACd,MAAE,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE,UAAU,QAAQ,QAAQ,GAAG,EAAE,KAAK;AAC/C;AACA,SAAS,EAAE,GAAG,IAAI,OAAI,IAAI,QAAQ;AAChC,QAAM,IAAI,SAAS,YAAY,GAAG,IAAI,OAAO,aAAa;AAC1D,IAAE,mBAAmB,CAAC,GAAG,MAAM,WAAW,EAAE,SAAS,GAAG,CAAC,GAAG,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,gBAAgB,GAAG,EAAE,SAAS,CAAC;AAC/H;AACA,QAAQ,UAAU,YAAY,QAAQ,UAAU,UAAU,QAAQ,UAAU,qBAAqB,QAAQ,UAAU;AACnH,QAAQ,UAAU,YAAY,QAAQ,UAAU,UAAU,SAAS,GAAG;AACpE,MAAI,IAAI;AACR,MAAI,CAAC,SAAS,gBAAgB,SAAS,CAAC;AACtC,WAAO;AACT,KAAG;AACD,QAAI,EAAE,QAAQ,CAAC;AACb,aAAO;AACT,QAAI,EAAE,iBAAiB,EAAE;AAAA,EAC3B,SAAS,MAAM,QAAQ,EAAE,aAAa;AACtC,SAAO;AACT;AACA,IAAM,IAAN,MAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMN,WAAW,sBAAsB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,mBAAmB;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,UAAU;AACnB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,mBAAmB;AAC5B,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOL,QAAQ,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOxD,QAAQ,CAAC,OAAO;AAAA,QACd,OAAO;AAAA,UACL;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,GAAG,UAAU,EAAE,GAAG;AACvD,SAAK,YAAY;AAAA,MACf,SAAS;AAAA,MACT,OAAO,CAAC;AAAA,IACV,GAAG,KAAK,WAAW,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,KAAK,CAAC;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,WAAO,KAAK,UAAU,UAAU,EAAE,OAAO,CAAC,KAAK,IAAI,WAAW,KAAK,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,UAAU,KAAK,KAAK,QAAQ;AAAA,MACtH;AAAA,QACE,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,IAAI,KAAK,KAAK,MAAM,QAAQ,CAAC,MAAM;AACjC,YAAM,IAAI,KAAK,oBAAoB,CAAC;AACpC,WAAK,UAAU,QAAQ,YAAY,CAAC;AAAA,IACtC,CAAC,GAAG,KAAK,WAAW,KAAK,UAAU,WAAW,KAAK,UAAU,QAAQ,iBAAiB,WAAW,CAAC,MAAM;AACtG,YAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AACrB,cAAQ,EAAE,SAAS;AAAA,QACjB,KAAK;AACH,eAAK,aAAa,CAAC;AACnB;AAAA,QACF,KAAK;AACH,eAAK,UAAU,CAAC;AAChB;AAAA,MACJ;AAAA,IACF,GAAG,KAAE,GAAG,KAAK,UAAU,QAAQ,iBAAiB,SAAS,CAAC,MAAM;AAC9D,WAAK,eAAe,CAAC;AAAA,IACvB,CAAC,GAAG,KAAK,UAAU;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACL,QAAI,IAAI,KAAK,MAAM,IAAI,CAAC,MAAM;AAC5B,YAAM,IAAI,KAAK,aAAa,CAAC;AAC7B,aAAO;AAAA,QACL,MAAM,EAAE,CAAC;AAAA,QACT,SAAS,EAAE,UAAU,SAAS,KAAK,IAAI,WAAW;AAAA,MACpD;AAAA,IACF,CAAC;AACD,WAAO,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,KAAK,KAAK,EAAE,WAAW,CAAC,GAAG;AAAA,MACtD,OAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,GAAG;AACV,WAAO,CAAC,CAAC,EAAE,MAAM;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,GAAG;AAChB,UAAM,IAAI,EAAE,OAAO,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,IAAI,EAAE,cAAc,IAAI,KAAK,IAAI,iBAAiB,EAAE;AACrG,MAAE,SAAS,EAAE,MAAM,MAAM,EAAE,UAAU,OAAO,KAAK,IAAI,WAAW,GAAG,EAAE,UAAU,IAAI,KAAK,IAAI,OAAO,GAAG,EAAE,iBAAiB,cAAc,MAAM,KAAK,2BAA2B,CAAC,GAAG,EAAE,MAAM,KAAG,CAAC;AAAA,EAC/L;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,IAAI,CAAC,GAAG;AAC1B,UAAM,IAAI,EAAE,OAAO,KAAK,IAAI,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,IAAI,QAAQ,GAAG,IAAI,EAAE,OAAO,KAAK,IAAI,iBAAiB,GAAG,IAAI,EAAE,OAAO,KAAK,IAAI,WAAW;AAAA,MAC9I,WAAW,EAAE,OAAO,EAAE,OAAO;AAAA,MAC7B,iBAAiB,CAAC,KAAK;AAAA,IACzB,CAAC;AACD,WAAO,EAAE,WAAW,EAAE,UAAU,IAAI,KAAK,IAAI,WAAW,GAAG,EAAE,YAAY,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG;AAAA,EACpI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,GAAG;AACd,MAAE,eAAe;AACjB,UAAM,IAAI,KAAK,OAAO,IAAI,SAAS,cAAc,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE;AAC5E,QAAI,EAAE,QAAQ,CAAC,MAAM,EAAE,SAAS,KAAK,EAAE,KAAK,aAAa,CAAC,CAAC,EAAE,WAAW,GAAG;AACzE,YAAM,IAAI,KAAK,IAAI,OAAO,qBAAqB;AAC/C,QAAE,OAAO,GAAG,KAAK,IAAI,OAAO,OAAO,GAAG,KAAK,IAAI,MAAM,WAAW,IAAI,CAAC;AACrE;AAAA,IACF;AACA,UAAM,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,KAAK,oBAAoB;AAAA,MACpD,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AACD,SAAK,UAAU,QAAQ,aAAa,GAAG,EAAE,WAAW,GAAG,EAAE,KAAK,aAAa,CAAC,GAAG,IAAE;AAAA,EACnF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,GAAG;AACX,UAAM,IAAI,EAAE,OAAO,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,IAAI,KAAK,MAAM,QAAQ,CAAC,GAAG,IAAI,KAAK,MAAM,IAAI,CAAC;AAChG,QAAI,CAAC,KAAK,EAAE,OAAO,aAAa,EAAE,gBAAgB;AAChD;AACF,MAAE,eAAe;AACjB,UAAM,IAAI,EAAE,GAAG,IAAI,KAAK,aAAa,CAAC,GAAG,IAAI,EAAE,WAAW;AAC1D,MAAE,YAAY,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,GAAG,EAAE,OAAO;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,MAAM;AACR,WAAO;AAAA,MACL,WAAW,KAAK,IAAI,OAAO;AAAA,MAC3B,SAAS;AAAA,MACT,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS;AAAA,MACT,UAAU;AAAA,MACV,WAAW;AAAA,MACX,mBAAmB;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ;AACV,WAAO,MAAM,KAAK,KAAK,UAAU,QAAQ,iBAAiB,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,2BAA2B,GAAG;AAC5B,MAAE,UAAU,OAAO,KAAK,IAAI,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,GAAG;AACd,WAAO,EAAE,cAAc,IAAI,KAAK,IAAI,SAAS,EAAE;AAAA,EACjD;AACF;", "names": ["c"]}