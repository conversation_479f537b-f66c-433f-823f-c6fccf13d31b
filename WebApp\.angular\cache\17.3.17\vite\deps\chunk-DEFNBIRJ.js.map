{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-select.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, EventEmitter, Output, inject, NgZone, PLATFORM_ID, ViewChild, TemplateRef, Optional, Host, forwardRef, ElementRef, ContentChildren, NgModule } from '@angular/core';\nimport { Subject, fromEvent, BehaviorSubject, of, combineLatest, merge } from 'rxjs';\nimport { OverlayModule, CdkOverlayOrigin, CdkConnectedOverlay } from '@angular/cdk/overlay';\nimport * as i2 from '@angular/cdk/scrolling';\nimport { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport { NgIf, NgTemplateOutlet, isPlatformBrowser, NgSwitch, NgSwitchCase, NgSwitchDefault, NgFor, NgStyle } from '@angular/common';\nimport * as i8 from 'ng-zorro-antd/core/overlay';\nimport { NzOverlayModule, getPlacementName, POSITION_MAP } from 'ng-zorro-antd/core/overlay';\nimport * as i1$3 from 'ng-zorro-antd/empty';\nimport { NzEmptyModule } from 'ng-zorro-antd/empty';\nimport * as i1 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { takeUntil, startWith, distinctUntilChanged, withLatestFrom, map, switchMap } from 'rxjs/operators';\nimport * as i1$1 from 'ng-zorro-antd/core/services';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport * as i1$2 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { __decorate } from 'tslib';\nimport { InputBoolean, isNotNil, getStatusClassNames } from 'ng-zorro-antd/core/util';\nimport { BACKSPACE, ESCAPE, TAB, SPACE, ENTER, DOWN_ARROW, UP_ARROW } from '@angular/cdk/keycodes';\nimport * as i2$1 from '@angular/forms';\nimport { COMPOSITION_BUFFER_MODE, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { slideMotion } from 'ng-zorro-antd/core/animation';\nimport * as i2$2 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i7 from 'ng-zorro-antd/core/form';\nimport { NzFormPatchModule } from 'ng-zorro-antd/core/form';\nimport * as i1$5 from 'ng-zorro-antd/core/no-animation';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport { cancelRequestAnimationFrame, reqAnimFrame } from 'ng-zorro-antd/core/polyfill';\nimport * as i1$4 from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/platform';\nimport * as i5 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"*\"];\nfunction NzOptionItemGroupComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzLabel);\n  }\n}\nfunction NzOptionItemComponent_ng_template_1_ng_template_0_Template(rf, ctx) {}\nfunction NzOptionItemComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzOptionItemComponent_ng_template_1_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template);\n  }\n}\nfunction NzOptionItemComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate(ctx_r0.label);\n  }\n}\nfunction NzOptionItemComponent_div_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 7);\n  }\n}\nfunction NzOptionItemComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtemplate(1, NzOptionItemComponent_div_4_span_1_Template, 1, 0, \"span\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.icon)(\"ngIfElse\", ctx_r0.icon);\n  }\n}\nfunction NzOptionContainerComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"nz-embed-empty\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"specificContent\", ctx_r0.notFoundContent);\n  }\n}\nfunction NzOptionContainerComponent_ng_template_3_nz_option_item_group_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option-item-group\", 9);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"nzLabel\", item_r2.groupLabel);\n  }\n}\nfunction NzOptionContainerComponent_ng_template_3_nz_option_item_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-option-item\", 10);\n    i0.ɵɵlistener(\"itemHover\", function NzOptionContainerComponent_ng_template_3_nz_option_item_2_Template_nz_option_item_itemHover_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onItemHover($event));\n    })(\"itemClick\", function NzOptionContainerComponent_ng_template_3_nz_option_item_2_Template_nz_option_item_itemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onItemClick($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"icon\", ctx_r0.menuItemSelectedIcon)(\"customContent\", item_r2.nzCustomContent)(\"template\", item_r2.template)(\"grouped\", !!item_r2.groupLabel)(\"disabled\", item_r2.nzDisabled || ctx_r0.isMaxLimitReached && !ctx_r0.listOfSelectedValue.includes(item_r2[\"nzValue\"]))(\"showState\", ctx_r0.mode === \"tags\" || ctx_r0.mode === \"multiple\")(\"title\", item_r2.nzTitle)(\"label\", item_r2.nzLabel)(\"compareWith\", ctx_r0.compareWith)(\"activatedValue\", ctx_r0.activatedValue)(\"listOfSelectedValue\", ctx_r0.listOfSelectedValue)(\"value\", item_r2.nzValue);\n  }\n}\nfunction NzOptionContainerComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 6);\n    i0.ɵɵtemplate(1, NzOptionContainerComponent_ng_template_3_nz_option_item_group_1_Template, 1, 1, \"nz-option-item-group\", 7)(2, NzOptionContainerComponent_ng_template_3_nz_option_item_2_Template, 1, 12, \"nz-option-item\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngSwitch\", item_r2.type);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"group\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"item\");\n  }\n}\nfunction NzOptionContainerComponent_ng_template_4_Template(rf, ctx) {}\nfunction NzOptionComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzSelectArrowComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.listOfValue.length, \" / \", ctx_r0.nzMaxMultipleCount, \"\");\n  }\n}\nfunction NzSelectArrowComponent_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 5);\n  }\n}\nfunction NzSelectArrowComponent_ng_template_2_ng_container_0_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 9);\n  }\n}\nfunction NzSelectArrowComponent_ng_template_2_ng_container_0_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 10);\n  }\n}\nfunction NzSelectArrowComponent_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzSelectArrowComponent_ng_template_2_ng_container_0_span_1_Template, 1, 0, \"span\", 7)(2, NzSelectArrowComponent_ng_template_2_ng_container_0_span_2_Template, 1, 0, \"span\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.search);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.search);\n  }\n}\nfunction NzSelectArrowComponent_ng_template_2_ng_template_1_ng_container_0_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 12);\n  }\n  if (rf & 2) {\n    const suffixIcon_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"nzType\", suffixIcon_r2);\n  }\n}\nfunction NzSelectArrowComponent_ng_template_2_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzSelectArrowComponent_ng_template_2_ng_template_1_ng_container_0_span_1_Template, 1, 1, \"span\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const suffixIcon_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", suffixIcon_r2);\n  }\n}\nfunction NzSelectArrowComponent_ng_template_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzSelectArrowComponent_ng_template_2_ng_template_1_ng_container_0_Template, 2, 1, \"ng-container\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.suffixIcon);\n  }\n}\nfunction NzSelectArrowComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzSelectArrowComponent_ng_template_2_ng_container_0_Template, 3, 2, \"ng-container\", 6)(1, NzSelectArrowComponent_ng_template_2_ng_template_1_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const suffixTemplate_r3 = i0.ɵɵreference(2);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showArrow && !ctx_r0.suffixIcon)(\"ngIfElse\", suffixTemplate_r3);\n  }\n}\nfunction NzSelectArrowComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.feedbackIcon);\n  }\n}\nfunction NzSelectClearComponent_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 1);\n  }\n}\nconst _c1 = a0 => ({\n  $implicit: a0\n});\nfunction NzSelectItemComponent_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.label);\n  }\n}\nfunction NzSelectItemComponent_ng_container_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵtextInterpolate(ctx_r0.label);\n  }\n}\nfunction NzSelectItemComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzSelectItemComponent_ng_container_0_div_1_Template, 2, 1, \"div\", 3)(2, NzSelectItemComponent_ng_container_0_ng_template_2_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const labelTemplate_r2 = i0.ɵɵreference(3);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.deletable)(\"ngIfElse\", labelTemplate_r2);\n  }\n}\nfunction NzSelectItemComponent_span_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 7);\n  }\n}\nfunction NzSelectItemComponent_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵlistener(\"click\", function NzSelectItemComponent_span_1_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDelete($event));\n    });\n    i0.ɵɵtemplate(1, NzSelectItemComponent_span_1_span_1_Template, 1, 0, \"span\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.removeIcon)(\"ngIfElse\", ctx_r0.removeIcon);\n  }\n}\nfunction NzSelectPlaceholderComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.placeholder, \" \");\n  }\n}\nconst _c2 = [\"inputElement\"];\nconst _c3 = [\"mirrorElement\"];\nfunction NzSelectSearchComponent_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 4, 1);\n  }\n}\nfunction NzSelectTopControlComponent_ng_container_1_nz_select_item_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-select-item\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"deletable\", false)(\"disabled\", false)(\"removeIcon\", ctx_r1.removeIcon)(\"label\", ctx_r1.listOfTopItem[0].nzLabel)(\"contentTemplateOutlet\", ctx_r1.customTemplate)(\"contentTemplateOutletContext\", ctx_r1.listOfTopItem[0]);\n  }\n}\nfunction NzSelectTopControlComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"nz-select-search\", 4);\n    i0.ɵɵlistener(\"isComposingChange\", function NzSelectTopControlComponent_ng_container_1_Template_nz_select_search_isComposingChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.isComposingChange($event));\n    })(\"valueChange\", function NzSelectTopControlComponent_ng_container_1_Template_nz_select_search_valueChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onInputValueChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, NzSelectTopControlComponent_ng_container_1_nz_select_item_2_Template, 1, 6, \"nz-select-item\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzId\", ctx_r1.nzId)(\"disabled\", ctx_r1.disabled)(\"value\", ctx_r1.inputValue)(\"showInput\", ctx_r1.showSearch)(\"mirrorSync\", false)(\"autofocus\", ctx_r1.autofocus)(\"focusTrigger\", ctx_r1.open);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isShowSingleLabel);\n  }\n}\nfunction NzSelectTopControlComponent_ng_container_2_nz_select_item_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-select-item\", 9);\n    i0.ɵɵlistener(\"delete\", function NzSelectTopControlComponent_ng_container_2_nz_select_item_1_Template_nz_select_item_delete_0_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDeleteItem(item_r5.contentTemplateOutletContext));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"removeIcon\", ctx_r1.removeIcon)(\"label\", item_r5.nzLabel)(\"disabled\", item_r5.nzDisabled || ctx_r1.disabled)(\"contentTemplateOutlet\", item_r5.contentTemplateOutlet)(\"deletable\", true)(\"contentTemplateOutletContext\", item_r5.contentTemplateOutletContext);\n  }\n}\nfunction NzSelectTopControlComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzSelectTopControlComponent_ng_container_2_nz_select_item_1_Template, 1, 6, \"nz-select-item\", 7);\n    i0.ɵɵelementStart(2, \"nz-select-search\", 8);\n    i0.ɵɵlistener(\"isComposingChange\", function NzSelectTopControlComponent_ng_container_2_Template_nz_select_search_isComposingChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.isComposingChange($event));\n    })(\"valueChange\", function NzSelectTopControlComponent_ng_container_2_Template_nz_select_search_valueChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onInputValueChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.listOfSlicedItem)(\"ngForTrackBy\", ctx_r1.trackValue);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzId\", ctx_r1.nzId)(\"disabled\", ctx_r1.disabled)(\"value\", ctx_r1.inputValue)(\"autofocus\", ctx_r1.autofocus)(\"showInput\", true)(\"mirrorSync\", true)(\"focusTrigger\", ctx_r1.open);\n  }\n}\nfunction NzSelectTopControlComponent_nz_select_placeholder_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-select-placeholder\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"placeholder\", ctx_r1.placeHolder);\n  }\n}\nfunction NzSelectComponent_nz_select_arrow_2_ng_template_1_nz_form_item_feedback_icon_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-form-item-feedback-icon\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"status\", ctx_r1.status);\n  }\n}\nfunction NzSelectComponent_nz_select_arrow_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzSelectComponent_nz_select_arrow_2_ng_template_1_nz_form_item_feedback_icon_0_Template, 1, 1, \"nz-form-item-feedback-icon\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFeedback && !!ctx_r1.status);\n  }\n}\nfunction NzSelectComponent_nz_select_arrow_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nz-select-arrow\", 6);\n    i0.ɵɵtemplate(1, NzSelectComponent_nz_select_arrow_2_ng_template_1_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feedbackIconTpl_r3 = i0.ɵɵreference(2);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"showArrow\", ctx_r1.nzShowArrow)(\"loading\", ctx_r1.nzLoading)(\"search\", ctx_r1.nzOpen && ctx_r1.nzShowSearch)(\"suffixIcon\", ctx_r1.nzSuffixIcon)(\"feedbackIcon\", feedbackIconTpl_r3)(\"nzMaxMultipleCount\", ctx_r1.nzMaxMultipleCount)(\"listOfValue\", ctx_r1.listOfValue)(\"isMaxTagCountSet\", ctx_r1.isMaxTagCountSet);\n  }\n}\nfunction NzSelectComponent_nz_select_clear_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-select-clear\", 9);\n    i0.ɵɵlistener(\"clear\", function NzSelectComponent_nz_select_clear_3_Template_nz_select_clear_clear_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClearSelection());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"clearIcon\", ctx_r1.nzClearIcon);\n  }\n}\nfunction NzSelectComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-option-container\", 10);\n    i0.ɵɵlistener(\"keydown\", function NzSelectComponent_ng_template_4_Template_nz_option_container_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onKeyDown($event));\n    })(\"itemClick\", function NzSelectComponent_ng_template_4_Template_nz_option_container_itemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onItemClick($event));\n    })(\"scrollToBottom\", function NzSelectComponent_ng_template_4_Template_nz_option_container_scrollToBottom_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nzScrollToBottom.emit());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ant-select-dropdown-placement-bottomLeft\", ctx_r1.dropDownPosition === \"bottomLeft\")(\"ant-select-dropdown-placement-topLeft\", ctx_r1.dropDownPosition === \"topLeft\")(\"ant-select-dropdown-placement-bottomRight\", ctx_r1.dropDownPosition === \"bottomRight\")(\"ant-select-dropdown-placement-topRight\", ctx_r1.dropDownPosition === \"topRight\");\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.nzDropdownStyle)(\"itemSize\", ctx_r1.nzOptionHeightPx)(\"maxItemLength\", ctx_r1.nzOptionOverflowSize)(\"matchWidth\", ctx_r1.nzDropdownMatchSelectWidth)(\"@slideMotion\", \"enter\")(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation)(\"listOfContainerItem\", ctx_r1.listOfContainerItem)(\"menuItemSelectedIcon\", ctx_r1.nzMenuItemSelectedIcon)(\"notFoundContent\", ctx_r1.nzNotFoundContent)(\"activatedValue\", ctx_r1.activatedValue)(\"listOfSelectedValue\", ctx_r1.listOfValue)(\"dropdownRender\", ctx_r1.nzDropdownRender)(\"compareWith\", ctx_r1.compareWith)(\"mode\", ctx_r1.nzMode)(\"isMaxLimitReached\", ctx_r1.isMaxLimitReached);\n  }\n}\nclass NzOptionGroupComponent {\n  constructor() {\n    this.nzLabel = null;\n    this.changes = new Subject();\n  }\n  ngOnChanges() {\n    this.changes.next();\n  }\n  static {\n    this.ɵfac = function NzOptionGroupComponent_Factory(t) {\n      return new (t || NzOptionGroupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzOptionGroupComponent,\n      selectors: [[\"nz-option-group\"]],\n      inputs: {\n        nzLabel: \"nzLabel\"\n      },\n      exportAs: [\"nzOptionGroup\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzOptionGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzOptionGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-option-group',\n      exportAs: 'nzOptionGroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: ` <ng-content></ng-content> `,\n      standalone: true\n    }]\n  }], null, {\n    nzLabel: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzOptionItemGroupComponent {\n  constructor() {\n    this.nzLabel = null;\n  }\n  static {\n    this.ɵfac = function NzOptionItemGroupComponent_Factory(t) {\n      return new (t || NzOptionItemGroupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzOptionItemGroupComponent,\n      selectors: [[\"nz-option-item-group\"]],\n      hostAttrs: [1, \"ant-select-item\", \"ant-select-item-group\"],\n      inputs: {\n        nzLabel: \"nzLabel\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"nzStringTemplateOutlet\"]],\n      template: function NzOptionItemGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzOptionItemGroupComponent_ng_container_0_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzLabel);\n        }\n      },\n      dependencies: [NzOutletModule, i1.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzOptionItemGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-option-item-group',\n      template: ` <ng-container *nzStringTemplateOutlet=\"nzLabel\">{{ nzLabel }}</ng-container> `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'ant-select-item ant-select-item-group'\n      },\n      imports: [NzOutletModule],\n      standalone: true\n    }]\n  }], () => [], {\n    nzLabel: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzOptionItemComponent {\n  constructor(elementRef, ngZone, destroy$) {\n    this.elementRef = elementRef;\n    this.ngZone = ngZone;\n    this.destroy$ = destroy$;\n    this.selected = false;\n    this.activated = false;\n    this.grouped = false;\n    this.customContent = false;\n    this.template = null;\n    this.disabled = false;\n    this.showState = false;\n    this.label = null;\n    this.value = null;\n    this.activatedValue = null;\n    this.listOfSelectedValue = [];\n    this.icon = null;\n    this.itemClick = new EventEmitter();\n    this.itemHover = new EventEmitter();\n  }\n  ngOnChanges(changes) {\n    const {\n      value,\n      activatedValue,\n      listOfSelectedValue\n    } = changes;\n    if (value || listOfSelectedValue) {\n      this.selected = this.listOfSelectedValue.some(v => this.compareWith(v, this.value));\n    }\n    if (value || activatedValue) {\n      this.activated = this.compareWith(this.activatedValue, this.value);\n    }\n  }\n  ngOnInit() {\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.elementRef.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(() => {\n        if (!this.disabled) {\n          this.ngZone.run(() => this.itemClick.emit(this.value));\n        }\n      });\n      fromEvent(this.elementRef.nativeElement, 'mouseenter').pipe(takeUntil(this.destroy$)).subscribe(() => {\n        if (!this.disabled) {\n          this.ngZone.run(() => this.itemHover.emit(this.value));\n        }\n      });\n    });\n  }\n  static {\n    this.ɵfac = function NzOptionItemComponent_Factory(t) {\n      return new (t || NzOptionItemComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.NzDestroyService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzOptionItemComponent,\n      selectors: [[\"nz-option-item\"]],\n      hostAttrs: [1, \"ant-select-item\", \"ant-select-item-option\"],\n      hostVars: 9,\n      hostBindings: function NzOptionItemComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"title\", ctx.title);\n          i0.ɵɵclassProp(\"ant-select-item-option-grouped\", ctx.grouped)(\"ant-select-item-option-selected\", ctx.selected && !ctx.disabled)(\"ant-select-item-option-disabled\", ctx.disabled)(\"ant-select-item-option-active\", ctx.activated && !ctx.disabled);\n        }\n      },\n      inputs: {\n        grouped: \"grouped\",\n        customContent: \"customContent\",\n        template: \"template\",\n        disabled: \"disabled\",\n        showState: \"showState\",\n        title: \"title\",\n        label: \"label\",\n        value: \"value\",\n        activatedValue: \"activatedValue\",\n        listOfSelectedValue: \"listOfSelectedValue\",\n        icon: \"icon\",\n        compareWith: \"compareWith\"\n      },\n      outputs: {\n        itemClick: \"itemClick\",\n        itemHover: \"itemHover\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzDestroyService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 3,\n      consts: [[\"noCustomContent\", \"\"], [1, \"ant-select-item-option-content\"], [3, \"ngIf\", \"ngIfElse\"], [\"class\", \"ant-select-item-option-state\", \"style\", \"user-select: none\", \"unselectable\", \"on\", 4, \"ngIf\"], [3, \"ngTemplateOutlet\"], [\"unselectable\", \"on\", 1, \"ant-select-item-option-state\", 2, \"user-select\", \"none\"], [\"nz-icon\", \"\", \"nzType\", \"check\", \"class\", \"ant-select-selected-icon\", 4, \"ngIf\", \"ngIfElse\"], [\"nz-icon\", \"\", \"nzType\", \"check\", 1, \"ant-select-selected-icon\"]],\n      template: function NzOptionItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, NzOptionItemComponent_ng_template_1_Template, 1, 1, \"ng-template\", 2)(2, NzOptionItemComponent_ng_template_2_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, NzOptionItemComponent_div_4_Template, 2, 2, \"div\", 3);\n        }\n        if (rf & 2) {\n          const noCustomContent_r2 = i0.ɵɵreference(3);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.customContent)(\"ngIfElse\", noCustomContent_r2);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.showState && ctx.selected);\n        }\n      },\n      dependencies: [NgIf, NgTemplateOutlet, NzIconModule, i1$2.NzIconDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzOptionItemComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-option-item',\n      template: `\n    <div class=\"ant-select-item-option-content\">\n      <ng-template [ngIf]=\"customContent\" [ngIfElse]=\"noCustomContent\">\n        <ng-template [ngTemplateOutlet]=\"template\"></ng-template>\n      </ng-template>\n      <ng-template #noCustomContent>{{ label }}</ng-template>\n    </div>\n    <div *ngIf=\"showState && selected\" class=\"ant-select-item-option-state\" style=\"user-select: none\" unselectable=\"on\">\n      <span nz-icon nzType=\"check\" class=\"ant-select-selected-icon\" *ngIf=\"!icon; else icon\"></span>\n    </div>\n  `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'ant-select-item ant-select-item-option',\n        '[attr.title]': 'title',\n        '[class.ant-select-item-option-grouped]': 'grouped',\n        '[class.ant-select-item-option-selected]': 'selected && !disabled',\n        '[class.ant-select-item-option-disabled]': 'disabled',\n        '[class.ant-select-item-option-active]': 'activated && !disabled'\n      },\n      providers: [NzDestroyService],\n      imports: [NgIf, NgTemplateOutlet, NzIconModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$1.NzDestroyService\n  }], {\n    grouped: [{\n      type: Input\n    }],\n    customContent: [{\n      type: Input\n    }],\n    template: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    showState: [{\n      type: Input\n    }],\n    title: [{\n      type: Input\n    }],\n    label: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    activatedValue: [{\n      type: Input\n    }],\n    listOfSelectedValue: [{\n      type: Input\n    }],\n    icon: [{\n      type: Input\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    itemClick: [{\n      type: Output\n    }],\n    itemHover: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzOptionContainerComponent {\n  constructor() {\n    this.notFoundContent = undefined;\n    this.menuItemSelectedIcon = null;\n    this.dropdownRender = null;\n    this.activatedValue = null;\n    this.listOfSelectedValue = [];\n    this.mode = 'default';\n    this.matchWidth = true;\n    this.itemSize = 32;\n    this.maxItemLength = 8;\n    this.isMaxLimitReached = false;\n    this.listOfContainerItem = [];\n    this.itemClick = new EventEmitter();\n    this.scrollToBottom = new EventEmitter();\n    this.scrolledIndex = 0;\n    this.ngZone = inject(NgZone);\n    this.platformId = inject(PLATFORM_ID);\n  }\n  onItemClick(value) {\n    this.itemClick.emit(value);\n  }\n  onItemHover(value) {\n    // TODO: keydown.enter won't activate this value\n    this.activatedValue = value;\n  }\n  trackValue(_index, option) {\n    return option.key;\n  }\n  onScrolledIndexChange(index) {\n    this.scrolledIndex = index;\n    if (index === this.listOfContainerItem.length - this.maxItemLength - 1) {\n      this.scrollToBottom.emit();\n    }\n  }\n  scrollToActivatedValue() {\n    const index = this.listOfContainerItem.findIndex(item => this.compareWith(item.key, this.activatedValue));\n    if (index < this.scrolledIndex || index >= this.scrolledIndex + this.maxItemLength) {\n      this.cdkVirtualScrollViewport.scrollToIndex(index || 0);\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      listOfContainerItem,\n      activatedValue\n    } = changes;\n    if (listOfContainerItem || activatedValue) {\n      this.scrollToActivatedValue();\n    }\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.ngZone.runOutsideAngular(() => setTimeout(() => this.scrollToActivatedValue()));\n    }\n  }\n  static {\n    this.ɵfac = function NzOptionContainerComponent_Factory(t) {\n      return new (t || NzOptionContainerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzOptionContainerComponent,\n      selectors: [[\"nz-option-container\"]],\n      viewQuery: function NzOptionContainerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkVirtualScrollViewport, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cdkVirtualScrollViewport = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-select-dropdown\"],\n      inputs: {\n        notFoundContent: \"notFoundContent\",\n        menuItemSelectedIcon: \"menuItemSelectedIcon\",\n        dropdownRender: \"dropdownRender\",\n        activatedValue: \"activatedValue\",\n        listOfSelectedValue: \"listOfSelectedValue\",\n        compareWith: \"compareWith\",\n        mode: \"mode\",\n        matchWidth: \"matchWidth\",\n        itemSize: \"itemSize\",\n        maxItemLength: \"maxItemLength\",\n        isMaxLimitReached: \"isMaxLimitReached\",\n        listOfContainerItem: \"listOfContainerItem\"\n      },\n      outputs: {\n        itemClick: \"itemClick\",\n        scrollToBottom: \"scrollToBottom\"\n      },\n      exportAs: [\"nzOptionContainer\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 14,\n      consts: [[\"class\", \"ant-select-item-empty\", 4, \"ngIf\"], [3, \"scrolledIndexChange\", \"itemSize\", \"maxBufferPx\", \"minBufferPx\"], [\"cdkVirtualFor\", \"\", 3, \"cdkVirtualForOf\", \"cdkVirtualForTrackBy\", \"cdkVirtualForTemplateCacheSize\"], [3, \"ngTemplateOutlet\"], [1, \"ant-select-item-empty\"], [\"nzComponentName\", \"select\", 3, \"specificContent\"], [3, \"ngSwitch\"], [3, \"nzLabel\", 4, \"ngSwitchCase\"], [3, \"icon\", \"customContent\", \"template\", \"grouped\", \"disabled\", \"showState\", \"title\", \"label\", \"compareWith\", \"activatedValue\", \"listOfSelectedValue\", \"value\", \"itemHover\", \"itemClick\", 4, \"ngSwitchCase\"], [3, \"nzLabel\"], [3, \"itemHover\", \"itemClick\", \"icon\", \"customContent\", \"template\", \"grouped\", \"disabled\", \"showState\", \"title\", \"label\", \"compareWith\", \"activatedValue\", \"listOfSelectedValue\", \"value\"]],\n      template: function NzOptionContainerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\");\n          i0.ɵɵtemplate(1, NzOptionContainerComponent_div_1_Template, 2, 1, \"div\", 0);\n          i0.ɵɵelementStart(2, \"cdk-virtual-scroll-viewport\", 1);\n          i0.ɵɵlistener(\"scrolledIndexChange\", function NzOptionContainerComponent_Template_cdk_virtual_scroll_viewport_scrolledIndexChange_2_listener($event) {\n            return ctx.onScrolledIndexChange($event);\n          });\n          i0.ɵɵtemplate(3, NzOptionContainerComponent_ng_template_3_Template, 3, 3, \"ng-template\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, NzOptionContainerComponent_ng_template_4_Template, 0, 0, \"ng-template\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listOfContainerItem.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"height\", ctx.listOfContainerItem.length * ctx.itemSize, \"px\")(\"max-height\", ctx.itemSize * ctx.maxItemLength, \"px\");\n          i0.ɵɵclassProp(\"full-width\", !ctx.matchWidth);\n          i0.ɵɵproperty(\"itemSize\", ctx.itemSize)(\"maxBufferPx\", ctx.itemSize * ctx.maxItemLength)(\"minBufferPx\", ctx.itemSize * ctx.maxItemLength);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"cdkVirtualForOf\", ctx.listOfContainerItem)(\"cdkVirtualForTrackBy\", ctx.trackValue)(\"cdkVirtualForTemplateCacheSize\", 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.dropdownRender);\n        }\n      },\n      dependencies: [NzEmptyModule, i1$3.NzEmbedEmptyComponent, NgIf, NgSwitch, NzOptionItemGroupComponent, NgSwitchCase, NzOptionItemComponent, NgTemplateOutlet, OverlayModule, i2.CdkFixedSizeVirtualScroll, i2.CdkVirtualForOf, i2.CdkVirtualScrollViewport, NzOverlayModule],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzOptionContainerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-option-container',\n      exportAs: 'nzOptionContainer',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      preserveWhitespaces: false,\n      template: `\n    <div>\n      <div *ngIf=\"listOfContainerItem.length === 0\" class=\"ant-select-item-empty\">\n        <nz-embed-empty nzComponentName=\"select\" [specificContent]=\"notFoundContent!\"></nz-embed-empty>\n      </div>\n      <cdk-virtual-scroll-viewport\n        [class.full-width]=\"!matchWidth\"\n        [itemSize]=\"itemSize\"\n        [maxBufferPx]=\"itemSize * maxItemLength\"\n        [minBufferPx]=\"itemSize * maxItemLength\"\n        (scrolledIndexChange)=\"onScrolledIndexChange($event)\"\n        [style.height.px]=\"listOfContainerItem.length * itemSize\"\n        [style.max-height.px]=\"itemSize * maxItemLength\"\n      >\n        <ng-template\n          cdkVirtualFor\n          [cdkVirtualForOf]=\"listOfContainerItem\"\n          [cdkVirtualForTrackBy]=\"trackValue\"\n          [cdkVirtualForTemplateCacheSize]=\"0\"\n          let-item\n        >\n          <ng-container [ngSwitch]=\"item.type\">\n            <nz-option-item-group *ngSwitchCase=\"'group'\" [nzLabel]=\"item.groupLabel\"></nz-option-item-group>\n            <nz-option-item\n              *ngSwitchCase=\"'item'\"\n              [icon]=\"menuItemSelectedIcon\"\n              [customContent]=\"item.nzCustomContent\"\n              [template]=\"item.template\"\n              [grouped]=\"!!item.groupLabel\"\n              [disabled]=\"item.nzDisabled || (isMaxLimitReached && !listOfSelectedValue.includes(item['nzValue']))\"\n              [showState]=\"mode === 'tags' || mode === 'multiple'\"\n              [title]=\"item.nzTitle\"\n              [label]=\"item.nzLabel\"\n              [compareWith]=\"compareWith\"\n              [activatedValue]=\"activatedValue\"\n              [listOfSelectedValue]=\"listOfSelectedValue\"\n              [value]=\"item.nzValue\"\n              (itemHover)=\"onItemHover($event)\"\n              (itemClick)=\"onItemClick($event)\"\n            ></nz-option-item>\n          </ng-container>\n        </ng-template>\n      </cdk-virtual-scroll-viewport>\n      <ng-template [ngTemplateOutlet]=\"dropdownRender\"></ng-template>\n    </div>\n  `,\n      host: {\n        class: 'ant-select-dropdown'\n      },\n      imports: [NzEmptyModule, NgIf, NgSwitch, NzOptionItemGroupComponent, NgSwitchCase, NzOptionItemComponent, NgTemplateOutlet, OverlayModule, NzOverlayModule],\n      standalone: true\n    }]\n  }], null, {\n    notFoundContent: [{\n      type: Input\n    }],\n    menuItemSelectedIcon: [{\n      type: Input\n    }],\n    dropdownRender: [{\n      type: Input\n    }],\n    activatedValue: [{\n      type: Input\n    }],\n    listOfSelectedValue: [{\n      type: Input\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    matchWidth: [{\n      type: Input\n    }],\n    itemSize: [{\n      type: Input\n    }],\n    maxItemLength: [{\n      type: Input\n    }],\n    isMaxLimitReached: [{\n      type: Input\n    }],\n    listOfContainerItem: [{\n      type: Input\n    }],\n    itemClick: [{\n      type: Output\n    }],\n    scrollToBottom: [{\n      type: Output\n    }],\n    cdkVirtualScrollViewport: [{\n      type: ViewChild,\n      args: [CdkVirtualScrollViewport, {\n        static: true\n      }]\n    }]\n  });\n})();\nclass NzOptionComponent {\n  constructor(nzOptionGroupComponent, destroy$) {\n    this.nzOptionGroupComponent = nzOptionGroupComponent;\n    this.destroy$ = destroy$;\n    this.changes = new Subject();\n    this.groupLabel = null;\n    this.nzLabel = null;\n    this.nzValue = null;\n    this.nzDisabled = false;\n    this.nzHide = false;\n    this.nzCustomContent = false;\n  }\n  ngOnInit() {\n    if (this.nzOptionGroupComponent) {\n      this.nzOptionGroupComponent.changes.pipe(startWith(true), takeUntil(this.destroy$)).subscribe(() => {\n        this.groupLabel = this.nzOptionGroupComponent.nzLabel;\n      });\n    }\n  }\n  ngOnChanges() {\n    this.changes.next();\n  }\n  static {\n    this.ɵfac = function NzOptionComponent_Factory(t) {\n      return new (t || NzOptionComponent)(i0.ɵɵdirectiveInject(NzOptionGroupComponent, 8), i0.ɵɵdirectiveInject(i1$1.NzDestroyService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzOptionComponent,\n      selectors: [[\"nz-option\"]],\n      viewQuery: function NzOptionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n        }\n      },\n      inputs: {\n        nzTitle: \"nzTitle\",\n        nzLabel: \"nzLabel\",\n        nzValue: \"nzValue\",\n        nzKey: \"nzKey\",\n        nzDisabled: \"nzDisabled\",\n        nzHide: \"nzHide\",\n        nzCustomContent: \"nzCustomContent\"\n      },\n      exportAs: [\"nzOption\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzDestroyService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzOptionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzOptionComponent_ng_template_0_Template, 1, 0, \"ng-template\");\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzOptionComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzOptionComponent.prototype, \"nzHide\", void 0);\n__decorate([InputBoolean()], NzOptionComponent.prototype, \"nzCustomContent\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzOptionComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-option',\n      exportAs: 'nzOption',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [NzDestroyService],\n      template: `\n    <ng-template>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n      standalone: true\n    }]\n  }], () => [{\n    type: NzOptionGroupComponent,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i1$1.NzDestroyService\n  }], {\n    template: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzLabel: [{\n      type: Input\n    }],\n    nzValue: [{\n      type: Input\n    }],\n    nzKey: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzHide: [{\n      type: Input\n    }],\n    nzCustomContent: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSelectArrowComponent {\n  constructor() {\n    this.listOfValue = [];\n    this.loading = false;\n    this.search = false;\n    this.showArrow = false;\n    this.isMaxTagCountSet = false;\n    this.suffixIcon = null;\n    this.feedbackIcon = null;\n    this.nzMaxMultipleCount = Infinity;\n  }\n  static {\n    this.ɵfac = function NzSelectArrowComponent_Factory(t) {\n      return new (t || NzSelectArrowComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSelectArrowComponent,\n      selectors: [[\"nz-select-arrow\"]],\n      hostAttrs: [1, \"ant-select-arrow\"],\n      hostVars: 2,\n      hostBindings: function NzSelectArrowComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-select-arrow-loading\", ctx.loading);\n        }\n      },\n      inputs: {\n        listOfValue: \"listOfValue\",\n        loading: \"loading\",\n        search: \"search\",\n        showArrow: \"showArrow\",\n        isMaxTagCountSet: \"isMaxTagCountSet\",\n        suffixIcon: \"suffixIcon\",\n        feedbackIcon: \"feedbackIcon\",\n        nzMaxMultipleCount: \"nzMaxMultipleCount\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 4,\n      consts: [[\"defaultArrow\", \"\"], [\"suffixTemplate\", \"\"], [4, \"ngIf\"], [\"nz-icon\", \"\", \"nzType\", \"loading\", 4, \"ngIf\", \"ngIfElse\"], [4, \"nzStringTemplateOutlet\"], [\"nz-icon\", \"\", \"nzType\", \"loading\"], [4, \"ngIf\", \"ngIfElse\"], [\"nz-icon\", \"\", \"nzType\", \"down\", 4, \"ngIf\"], [\"nz-icon\", \"\", \"nzType\", \"search\", 4, \"ngIf\"], [\"nz-icon\", \"\", \"nzType\", \"down\"], [\"nz-icon\", \"\", \"nzType\", \"search\"], [\"nz-icon\", \"\", 3, \"nzType\", 4, \"ngIf\"], [\"nz-icon\", \"\", 3, \"nzType\"]],\n      template: function NzSelectArrowComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzSelectArrowComponent_ng_container_0_Template, 3, 2, \"ng-container\", 2)(1, NzSelectArrowComponent_span_1_Template, 1, 0, \"span\", 3)(2, NzSelectArrowComponent_ng_template_2_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, NzSelectArrowComponent_ng_container_4_Template, 2, 1, \"ng-container\", 4);\n        }\n        if (rf & 2) {\n          const defaultArrow_r4 = i0.ɵɵreference(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isMaxTagCountSet);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading)(\"ngIfElse\", defaultArrow_r4);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.feedbackIcon);\n        }\n      },\n      dependencies: [NzIconModule, i1$2.NzIconDirective, NgIf, NzOutletModule, i1.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSelectArrowComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-select-arrow',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-container *ngIf=\"isMaxTagCountSet\">\n      <span>{{ listOfValue.length }} / {{ nzMaxMultipleCount }}</span>\n    </ng-container>\n    <span nz-icon nzType=\"loading\" *ngIf=\"loading; else defaultArrow\"></span>\n    <ng-template #defaultArrow>\n      <ng-container *ngIf=\"showArrow && !suffixIcon; else suffixTemplate\">\n        <span nz-icon nzType=\"down\" *ngIf=\"!search\"></span>\n        <span nz-icon nzType=\"search\" *ngIf=\"search\"></span>\n      </ng-container>\n      <ng-template #suffixTemplate>\n        <ng-container *nzStringTemplateOutlet=\"suffixIcon; let suffixIcon\">\n          <span *ngIf=\"suffixIcon\" nz-icon [nzType]=\"suffixIcon\"></span>\n        </ng-container>\n      </ng-template>\n    </ng-template>\n    <ng-container *nzStringTemplateOutlet=\"feedbackIcon\">{{ feedbackIcon }}</ng-container>\n  `,\n      host: {\n        class: 'ant-select-arrow',\n        '[class.ant-select-arrow-loading]': 'loading'\n      },\n      imports: [NzIconModule, NgIf, NzOutletModule],\n      standalone: true\n    }]\n  }], () => [], {\n    listOfValue: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input\n    }],\n    search: [{\n      type: Input\n    }],\n    showArrow: [{\n      type: Input\n    }],\n    isMaxTagCountSet: [{\n      type: Input\n    }],\n    suffixIcon: [{\n      type: Input\n    }],\n    feedbackIcon: [{\n      type: Input\n    }],\n    nzMaxMultipleCount: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSelectClearComponent {\n  constructor() {\n    this.clearIcon = null;\n    this.clear = new EventEmitter();\n  }\n  onClick(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    this.clear.emit(e);\n  }\n  static {\n    this.ɵfac = function NzSelectClearComponent_Factory(t) {\n      return new (t || NzSelectClearComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSelectClearComponent,\n      selectors: [[\"nz-select-clear\"]],\n      hostAttrs: [1, \"ant-select-clear\"],\n      hostBindings: function NzSelectClearComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function NzSelectClearComponent_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          });\n        }\n      },\n      inputs: {\n        clearIcon: \"clearIcon\"\n      },\n      outputs: {\n        clear: \"clear\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 2,\n      consts: [[\"nz-icon\", \"\", \"nzType\", \"close-circle\", \"nzTheme\", \"fill\", \"class\", \"ant-select-close-icon\", 4, \"ngIf\", \"ngIfElse\"], [\"nz-icon\", \"\", \"nzType\", \"close-circle\", \"nzTheme\", \"fill\", 1, \"ant-select-close-icon\"]],\n      template: function NzSelectClearComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzSelectClearComponent_span_0_Template, 1, 0, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.clearIcon)(\"ngIfElse\", ctx.clearIcon);\n        }\n      },\n      dependencies: [NzIconModule, i1$2.NzIconDirective, NgIf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSelectClearComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-select-clear',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <span\n      nz-icon\n      nzType=\"close-circle\"\n      nzTheme=\"fill\"\n      *ngIf=\"!clearIcon; else clearIcon\"\n      class=\"ant-select-close-icon\"\n    ></span>\n  `,\n      host: {\n        class: 'ant-select-clear',\n        '(click)': 'onClick($event)'\n      },\n      imports: [NzIconModule, NgIf],\n      standalone: true\n    }]\n  }], () => [], {\n    clearIcon: [{\n      type: Input\n    }],\n    clear: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSelectItemComponent {\n  constructor() {\n    this.disabled = false;\n    this.label = null;\n    this.deletable = false;\n    this.removeIcon = null;\n    this.contentTemplateOutletContext = null;\n    this.contentTemplateOutlet = null;\n    this.delete = new EventEmitter();\n  }\n  onDelete(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    if (!this.disabled) {\n      this.delete.next(e);\n    }\n  }\n  static {\n    this.ɵfac = function NzSelectItemComponent_Factory(t) {\n      return new (t || NzSelectItemComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSelectItemComponent,\n      selectors: [[\"nz-select-item\"]],\n      hostAttrs: [1, \"ant-select-selection-item\"],\n      hostVars: 3,\n      hostBindings: function NzSelectItemComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"title\", ctx.label);\n          i0.ɵɵclassProp(\"ant-select-selection-item-disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        label: \"label\",\n        deletable: \"deletable\",\n        removeIcon: \"removeIcon\",\n        contentTemplateOutletContext: \"contentTemplateOutletContext\",\n        contentTemplateOutlet: \"contentTemplateOutlet\"\n      },\n      outputs: {\n        delete: \"delete\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 5,\n      consts: [[\"labelTemplate\", \"\"], [4, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"], [\"class\", \"ant-select-selection-item-remove\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"ant-select-selection-item-content\", 4, \"ngIf\", \"ngIfElse\"], [1, \"ant-select-selection-item-content\"], [1, \"ant-select-selection-item-remove\", 3, \"click\"], [\"nz-icon\", \"\", \"nzType\", \"close\", 4, \"ngIf\", \"ngIfElse\"], [\"nz-icon\", \"\", \"nzType\", \"close\"]],\n      template: function NzSelectItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzSelectItemComponent_ng_container_0_Template, 4, 2, \"ng-container\", 1)(1, NzSelectItemComponent_span_1_Template, 2, 2, \"span\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.contentTemplateOutlet)(\"nzStringTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c1, ctx.contentTemplateOutletContext));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.deletable && !ctx.disabled);\n        }\n      },\n      dependencies: [NzOutletModule, i1.NzStringTemplateOutletDirective, NgIf, NzIconModule, i1$2.NzIconDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSelectItemComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-select-item',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-container *nzStringTemplateOutlet=\"contentTemplateOutlet; context: { $implicit: contentTemplateOutletContext }\">\n      <div class=\"ant-select-selection-item-content\" *ngIf=\"deletable; else labelTemplate\">{{ label }}</div>\n      <ng-template #labelTemplate>{{ label }}</ng-template>\n    </ng-container>\n    <span *ngIf=\"deletable && !disabled\" class=\"ant-select-selection-item-remove\" (click)=\"onDelete($event)\">\n      <span nz-icon nzType=\"close\" *ngIf=\"!removeIcon; else removeIcon\"></span>\n    </span>\n  `,\n      host: {\n        class: 'ant-select-selection-item',\n        '[attr.title]': 'label',\n        '[class.ant-select-selection-item-disabled]': 'disabled'\n      },\n      imports: [NzOutletModule, NgIf, NzIconModule],\n      standalone: true\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input\n    }],\n    label: [{\n      type: Input\n    }],\n    deletable: [{\n      type: Input\n    }],\n    removeIcon: [{\n      type: Input\n    }],\n    contentTemplateOutletContext: [{\n      type: Input\n    }],\n    contentTemplateOutlet: [{\n      type: Input\n    }],\n    delete: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSelectPlaceholderComponent {\n  constructor() {\n    this.placeholder = null;\n  }\n  static {\n    this.ɵfac = function NzSelectPlaceholderComponent_Factory(t) {\n      return new (t || NzSelectPlaceholderComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSelectPlaceholderComponent,\n      selectors: [[\"nz-select-placeholder\"]],\n      hostAttrs: [1, \"ant-select-selection-placeholder\"],\n      inputs: {\n        placeholder: \"placeholder\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"nzStringTemplateOutlet\"]],\n      template: function NzSelectPlaceholderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzSelectPlaceholderComponent_ng_container_0_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.placeholder);\n        }\n      },\n      dependencies: [NzOutletModule, i1.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSelectPlaceholderComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-select-placeholder',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-container *nzStringTemplateOutlet=\"placeholder\">\n      {{ placeholder }}\n    </ng-container>\n  `,\n      host: {\n        class: 'ant-select-selection-placeholder'\n      },\n      imports: [NzOutletModule],\n      standalone: true\n    }]\n  }], () => [], {\n    placeholder: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSelectSearchComponent {\n  setCompositionState(isComposing) {\n    this.isComposingChange.next(isComposing);\n  }\n  onValueChange(value) {\n    this.value = value;\n    this.valueChange.next(value);\n    if (this.mirrorSync) {\n      this.syncMirrorWidth();\n    }\n  }\n  clearInputValue() {\n    const inputDOM = this.inputElement.nativeElement;\n    inputDOM.value = '';\n    this.onValueChange('');\n  }\n  syncMirrorWidth() {\n    const mirrorDOM = this.mirrorElement.nativeElement;\n    const hostDOM = this.elementRef.nativeElement;\n    const inputDOM = this.inputElement.nativeElement;\n    this.renderer.removeStyle(hostDOM, 'width');\n    this.renderer.setProperty(mirrorDOM, 'textContent', `${inputDOM.value}\\u00a0`);\n    this.renderer.setStyle(hostDOM, 'width', `${mirrorDOM.scrollWidth}px`);\n  }\n  focus() {\n    this.focusMonitor.focusVia(this.inputElement, 'keyboard');\n  }\n  blur() {\n    this.inputElement.nativeElement.blur();\n  }\n  constructor(elementRef, renderer, focusMonitor) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.focusMonitor = focusMonitor;\n    this.nzId = null;\n    this.disabled = false;\n    this.mirrorSync = false;\n    this.showInput = true;\n    this.focusTrigger = false;\n    this.value = '';\n    this.autofocus = false;\n    this.valueChange = new EventEmitter();\n    this.isComposingChange = new EventEmitter();\n  }\n  ngOnChanges(changes) {\n    const inputDOM = this.inputElement.nativeElement;\n    const {\n      focusTrigger,\n      showInput\n    } = changes;\n    if (showInput) {\n      if (this.showInput) {\n        this.renderer.removeAttribute(inputDOM, 'readonly');\n      } else {\n        this.renderer.setAttribute(inputDOM, 'readonly', 'readonly');\n      }\n    }\n    // IE11 cannot input value if focused before removing readonly\n    if (focusTrigger && focusTrigger.currentValue === true && focusTrigger.previousValue === false) {\n      inputDOM.focus();\n    }\n  }\n  ngAfterViewInit() {\n    if (this.mirrorSync) {\n      this.syncMirrorWidth();\n    }\n    if (this.autofocus) {\n      this.focus();\n    }\n  }\n  static {\n    this.ɵfac = function NzSelectSearchComponent_Factory(t) {\n      return new (t || NzSelectSearchComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1$4.FocusMonitor));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSelectSearchComponent,\n      selectors: [[\"nz-select-search\"]],\n      viewQuery: function NzSelectSearchComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c2, 7);\n          i0.ɵɵviewQuery(_c3, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.mirrorElement = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-select-selection-search\"],\n      inputs: {\n        nzId: \"nzId\",\n        disabled: \"disabled\",\n        mirrorSync: \"mirrorSync\",\n        showInput: \"showInput\",\n        focusTrigger: \"focusTrigger\",\n        value: \"value\",\n        autofocus: \"autofocus\"\n      },\n      outputs: {\n        valueChange: \"valueChange\",\n        isComposingChange: \"isComposingChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: COMPOSITION_BUFFER_MODE,\n        useValue: false\n      }]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 7,\n      consts: [[\"inputElement\", \"\"], [\"mirrorElement\", \"\"], [\"autocomplete\", \"off\", 1, \"ant-select-selection-search-input\", 3, \"ngModelChange\", \"compositionstart\", \"compositionend\", \"ngModel\", \"disabled\"], [\"class\", \"ant-select-selection-search-mirror\", 4, \"ngIf\"], [1, \"ant-select-selection-search-mirror\"]],\n      template: function NzSelectSearchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"input\", 2, 0);\n          i0.ɵɵlistener(\"ngModelChange\", function NzSelectSearchComponent_Template_input_ngModelChange_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onValueChange($event));\n          })(\"compositionstart\", function NzSelectSearchComponent_Template_input_compositionstart_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.setCompositionState(true));\n          })(\"compositionend\", function NzSelectSearchComponent_Template_input_compositionend_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.setCompositionState(false));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, NzSelectSearchComponent_span_2_Template, 2, 0, \"span\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"opacity\", ctx.showInput ? null : 0);\n          i0.ɵɵproperty(\"ngModel\", ctx.value)(\"disabled\", ctx.disabled);\n          i0.ɵɵattribute(\"id\", ctx.nzId)(\"autofocus\", ctx.autofocus ? \"autofocus\" : null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.mirrorSync);\n        }\n      },\n      dependencies: [FormsModule, i2$1.DefaultValueAccessor, i2$1.NgControlStatus, i2$1.NgModel, NgIf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSelectSearchComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-select-search',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <input\n      #inputElement\n      [attr.id]=\"nzId\"\n      autocomplete=\"off\"\n      class=\"ant-select-selection-search-input\"\n      [ngModel]=\"value\"\n      [attr.autofocus]=\"autofocus ? 'autofocus' : null\"\n      [disabled]=\"disabled\"\n      [style.opacity]=\"showInput ? null : 0\"\n      (ngModelChange)=\"onValueChange($event)\"\n      (compositionstart)=\"setCompositionState(true)\"\n      (compositionend)=\"setCompositionState(false)\"\n    />\n    <span #mirrorElement *ngIf=\"mirrorSync\" class=\"ant-select-selection-search-mirror\"></span>\n  `,\n      host: {\n        class: 'ant-select-selection-search'\n      },\n      providers: [{\n        provide: COMPOSITION_BUFFER_MODE,\n        useValue: false\n      }],\n      imports: [FormsModule, NgIf],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i1$4.FocusMonitor\n  }], {\n    nzId: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    mirrorSync: [{\n      type: Input\n    }],\n    showInput: [{\n      type: Input\n    }],\n    focusTrigger: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    autofocus: [{\n      type: Input\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    isComposingChange: [{\n      type: Output\n    }],\n    inputElement: [{\n      type: ViewChild,\n      args: ['inputElement', {\n        static: true\n      }]\n    }],\n    mirrorElement: [{\n      type: ViewChild,\n      args: ['mirrorElement', {\n        static: false\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSelectTopControlComponent {\n  updateTemplateVariable() {\n    const isSelectedValueEmpty = this.listOfTopItem.length === 0;\n    this.isShowPlaceholder = isSelectedValueEmpty && !this.isComposing && !this.inputValue;\n    this.isShowSingleLabel = !isSelectedValueEmpty && !this.isComposing && !this.inputValue;\n  }\n  isComposingChange(isComposing) {\n    this.isComposing = isComposing;\n    this.updateTemplateVariable();\n  }\n  onInputValueChange(value) {\n    if (value !== this.inputValue) {\n      this.inputValue = value;\n      this.updateTemplateVariable();\n      this.inputValueChange.emit(value);\n      this.tokenSeparate(value, this.tokenSeparators);\n    }\n  }\n  tokenSeparate(inputValue, tokenSeparators) {\n    const includesSeparators = (str, separators) => {\n      // eslint-disable-next-line @typescript-eslint/prefer-for-of\n      for (let i = 0; i < separators.length; ++i) {\n        if (str.lastIndexOf(separators[i]) > 0) {\n          return true;\n        }\n      }\n      return false;\n    };\n    const splitBySeparators = (str, separators) => {\n      const reg = new RegExp(`[${separators.join()}]`);\n      const array = str.split(reg).filter(token => token);\n      return [...new Set(array)];\n    };\n    if (inputValue && inputValue.length && tokenSeparators.length && this.mode !== 'default' && includesSeparators(inputValue, tokenSeparators)) {\n      const listOfLabel = splitBySeparators(inputValue, tokenSeparators);\n      this.tokenize.next(listOfLabel);\n    }\n  }\n  clearInputValue() {\n    if (this.nzSelectSearchComponent) {\n      this.nzSelectSearchComponent.clearInputValue();\n    }\n  }\n  focus() {\n    if (this.nzSelectSearchComponent) {\n      this.nzSelectSearchComponent.focus();\n    }\n  }\n  blur() {\n    if (this.nzSelectSearchComponent) {\n      this.nzSelectSearchComponent.blur();\n    }\n  }\n  trackValue(_index, option) {\n    return option.nzValue;\n  }\n  onDeleteItem(item) {\n    if (!this.disabled && !item.nzDisabled) {\n      this.deleteItem.next(item);\n    }\n  }\n  constructor(elementRef, ngZone, noAnimation) {\n    this.elementRef = elementRef;\n    this.ngZone = ngZone;\n    this.noAnimation = noAnimation;\n    this.nzId = null;\n    this.showSearch = false;\n    this.placeHolder = null;\n    this.open = false;\n    this.maxTagCount = Infinity;\n    this.autofocus = false;\n    this.disabled = false;\n    this.mode = 'default';\n    this.customTemplate = null;\n    this.maxTagPlaceholder = null;\n    this.removeIcon = null;\n    this.listOfTopItem = [];\n    this.tokenSeparators = [];\n    this.tokenize = new EventEmitter();\n    this.inputValueChange = new EventEmitter();\n    this.deleteItem = new EventEmitter();\n    this.listOfSlicedItem = [];\n    this.isShowPlaceholder = true;\n    this.isShowSingleLabel = false;\n    this.isComposing = false;\n    this.inputValue = null;\n    this.destroy$ = new Subject();\n  }\n  ngOnChanges(changes) {\n    const {\n      listOfTopItem,\n      maxTagCount,\n      customTemplate,\n      maxTagPlaceholder\n    } = changes;\n    if (listOfTopItem) {\n      this.updateTemplateVariable();\n    }\n    if (listOfTopItem || maxTagCount || customTemplate || maxTagPlaceholder) {\n      const listOfSlicedItem = this.listOfTopItem.slice(0, this.maxTagCount).map(o => ({\n        nzLabel: o.nzLabel,\n        nzValue: o.nzValue,\n        nzDisabled: o.nzDisabled,\n        contentTemplateOutlet: this.customTemplate,\n        contentTemplateOutletContext: o\n      }));\n      if (this.listOfTopItem.length > this.maxTagCount) {\n        const exceededLabel = `+ ${this.listOfTopItem.length - this.maxTagCount} ...`;\n        const listOfSelectedValue = this.listOfTopItem.map(item => item.nzValue);\n        const exceededItem = {\n          nzLabel: exceededLabel,\n          nzValue: '$$__nz_exceeded_item',\n          nzDisabled: true,\n          contentTemplateOutlet: this.maxTagPlaceholder,\n          contentTemplateOutletContext: listOfSelectedValue.slice(this.maxTagCount)\n        };\n        listOfSlicedItem.push(exceededItem);\n      }\n      this.listOfSlicedItem = listOfSlicedItem;\n    }\n  }\n  ngOnInit() {\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.elementRef.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        // `HTMLElement.focus()` is a native DOM API which doesn't require Angular to run change detection.\n        if (event.target !== this.nzSelectSearchComponent.inputElement.nativeElement) {\n          this.nzSelectSearchComponent.focus();\n        }\n      });\n      fromEvent(this.elementRef.nativeElement, 'keydown').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        if (event.target instanceof HTMLInputElement) {\n          const inputValue = event.target.value;\n          if (event.keyCode === BACKSPACE && this.mode !== 'default' && !inputValue && this.listOfTopItem.length > 0) {\n            event.preventDefault();\n            // Run change detection only if the user has pressed the `Backspace` key and the following condition is met.\n            this.ngZone.run(() => this.onDeleteItem(this.listOfTopItem[this.listOfTopItem.length - 1]));\n          }\n        }\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n  }\n  static {\n    this.ɵfac = function NzSelectTopControlComponent_Factory(t) {\n      return new (t || NzSelectTopControlComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$5.NzNoAnimationDirective, 9));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSelectTopControlComponent,\n      selectors: [[\"nz-select-top-control\"]],\n      viewQuery: function NzSelectTopControlComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(NzSelectSearchComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzSelectSearchComponent = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-select-selector\"],\n      inputs: {\n        nzId: \"nzId\",\n        showSearch: \"showSearch\",\n        placeHolder: \"placeHolder\",\n        open: \"open\",\n        maxTagCount: \"maxTagCount\",\n        autofocus: \"autofocus\",\n        disabled: \"disabled\",\n        mode: \"mode\",\n        customTemplate: \"customTemplate\",\n        maxTagPlaceholder: \"maxTagPlaceholder\",\n        removeIcon: \"removeIcon\",\n        listOfTopItem: \"listOfTopItem\",\n        tokenSeparators: \"tokenSeparators\"\n      },\n      outputs: {\n        tokenize: \"tokenize\",\n        inputValueChange: \"inputValueChange\",\n        deleteItem: \"deleteItem\"\n      },\n      exportAs: [\"nzSelectTopControl\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 3,\n      consts: [[3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [4, \"ngSwitchDefault\"], [3, \"placeholder\", 4, \"ngIf\"], [3, \"isComposingChange\", \"valueChange\", \"nzId\", \"disabled\", \"value\", \"showInput\", \"mirrorSync\", \"autofocus\", \"focusTrigger\"], [3, \"deletable\", \"disabled\", \"removeIcon\", \"label\", \"contentTemplateOutlet\", \"contentTemplateOutletContext\", 4, \"ngIf\"], [3, \"deletable\", \"disabled\", \"removeIcon\", \"label\", \"contentTemplateOutlet\", \"contentTemplateOutletContext\"], [3, \"removeIcon\", \"label\", \"disabled\", \"contentTemplateOutlet\", \"deletable\", \"contentTemplateOutletContext\", \"delete\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"isComposingChange\", \"valueChange\", \"nzId\", \"disabled\", \"value\", \"autofocus\", \"showInput\", \"mirrorSync\", \"focusTrigger\"], [3, \"delete\", \"removeIcon\", \"label\", \"disabled\", \"contentTemplateOutlet\", \"deletable\", \"contentTemplateOutletContext\"], [3, \"placeholder\"]],\n      template: function NzSelectTopControlComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainerStart(0, 0);\n          i0.ɵɵtemplate(1, NzSelectTopControlComponent_ng_container_1_Template, 3, 8, \"ng-container\", 1)(2, NzSelectTopControlComponent_ng_container_2_Template, 3, 9, \"ng-container\", 2);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(3, NzSelectTopControlComponent_nz_select_placeholder_3_Template, 1, 1, \"nz-select-placeholder\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngSwitch\", ctx.mode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"default\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowPlaceholder);\n        }\n      },\n      dependencies: [NgSwitch, NzSelectSearchComponent, NgSwitchCase, NzSelectItemComponent, NgIf, NgSwitchDefault, NgFor, NzSelectPlaceholderComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSelectTopControlComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-select-top-control',\n      exportAs: 'nzSelectTopControl',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <!--single mode-->\n    <ng-container [ngSwitch]=\"mode\">\n      <ng-container *ngSwitchCase=\"'default'\">\n        <nz-select-search\n          [nzId]=\"nzId\"\n          [disabled]=\"disabled\"\n          [value]=\"inputValue!\"\n          [showInput]=\"showSearch\"\n          [mirrorSync]=\"false\"\n          [autofocus]=\"autofocus\"\n          [focusTrigger]=\"open\"\n          (isComposingChange)=\"isComposingChange($event)\"\n          (valueChange)=\"onInputValueChange($event)\"\n        ></nz-select-search>\n        <nz-select-item\n          *ngIf=\"isShowSingleLabel\"\n          [deletable]=\"false\"\n          [disabled]=\"false\"\n          [removeIcon]=\"removeIcon\"\n          [label]=\"listOfTopItem[0].nzLabel\"\n          [contentTemplateOutlet]=\"customTemplate\"\n          [contentTemplateOutletContext]=\"listOfTopItem[0]\"\n        ></nz-select-item>\n      </ng-container>\n      <ng-container *ngSwitchDefault>\n        <!--multiple or tags mode-->\n        <nz-select-item\n          *ngFor=\"let item of listOfSlicedItem; trackBy: trackValue\"\n          [removeIcon]=\"removeIcon\"\n          [label]=\"item.nzLabel\"\n          [disabled]=\"item.nzDisabled || disabled\"\n          [contentTemplateOutlet]=\"item.contentTemplateOutlet\"\n          [deletable]=\"true\"\n          [contentTemplateOutletContext]=\"item.contentTemplateOutletContext\"\n          (delete)=\"onDeleteItem(item.contentTemplateOutletContext)\"\n        ></nz-select-item>\n        <nz-select-search\n          [nzId]=\"nzId\"\n          [disabled]=\"disabled\"\n          [value]=\"inputValue!\"\n          [autofocus]=\"autofocus\"\n          [showInput]=\"true\"\n          [mirrorSync]=\"true\"\n          [focusTrigger]=\"open\"\n          (isComposingChange)=\"isComposingChange($event)\"\n          (valueChange)=\"onInputValueChange($event)\"\n        ></nz-select-search>\n      </ng-container>\n    </ng-container>\n    <nz-select-placeholder *ngIf=\"isShowPlaceholder\" [placeholder]=\"placeHolder\"></nz-select-placeholder>\n  `,\n      host: {\n        class: 'ant-select-selector'\n      },\n      imports: [NgSwitch, NzSelectSearchComponent, NgSwitchCase, NzSelectItemComponent, NgIf, NgSwitchDefault, NgFor, NzSelectPlaceholderComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$5.NzNoAnimationDirective,\n    decorators: [{\n      type: Host\n    }, {\n      type: Optional\n    }]\n  }], {\n    nzId: [{\n      type: Input\n    }],\n    showSearch: [{\n      type: Input\n    }],\n    placeHolder: [{\n      type: Input\n    }],\n    open: [{\n      type: Input\n    }],\n    maxTagCount: [{\n      type: Input\n    }],\n    autofocus: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    customTemplate: [{\n      type: Input\n    }],\n    maxTagPlaceholder: [{\n      type: Input\n    }],\n    removeIcon: [{\n      type: Input\n    }],\n    listOfTopItem: [{\n      type: Input\n    }],\n    tokenSeparators: [{\n      type: Input\n    }],\n    tokenize: [{\n      type: Output\n    }],\n    inputValueChange: [{\n      type: Output\n    }],\n    deleteItem: [{\n      type: Output\n    }],\n    nzSelectSearchComponent: [{\n      type: ViewChild,\n      args: [NzSelectSearchComponent]\n    }]\n  });\n})();\nconst defaultFilterOption = (searchValue, item) => {\n  if (item && item.nzLabel) {\n    return item.nzLabel.toString().toLowerCase().indexOf(searchValue.toLowerCase()) > -1;\n  } else {\n    return false;\n  }\n};\nconst NZ_CONFIG_MODULE_NAME = 'select';\nclass NzSelectComponent {\n  set nzShowArrow(value) {\n    this._nzShowArrow = value;\n  }\n  get nzShowArrow() {\n    return this._nzShowArrow === undefined ? this.nzMode === 'default' : this._nzShowArrow;\n  }\n  get isMaxTagCountSet() {\n    return this.nzMaxMultipleCount !== Infinity;\n  }\n  generateTagItem(value) {\n    return {\n      nzValue: value,\n      nzLabel: value,\n      type: 'item'\n    };\n  }\n  onItemClick(value) {\n    this.activatedValue = value;\n    if (this.nzMode === 'default') {\n      if (this.listOfValue.length === 0 || !this.compareWith(this.listOfValue[0], value)) {\n        this.updateListOfValue([value]);\n      }\n      this.setOpenState(false);\n    } else {\n      const targetIndex = this.listOfValue.findIndex(o => this.compareWith(o, value));\n      if (targetIndex !== -1) {\n        const listOfValueAfterRemoved = this.listOfValue.filter((_, i) => i !== targetIndex);\n        this.updateListOfValue(listOfValueAfterRemoved);\n      } else if (this.listOfValue.length < this.nzMaxMultipleCount) {\n        const listOfValueAfterAdded = [...this.listOfValue, value];\n        this.updateListOfValue(listOfValueAfterAdded);\n      }\n      this.focus();\n      if (this.nzAutoClearSearchValue) {\n        this.clearInput();\n      }\n    }\n  }\n  onItemDelete(item) {\n    const listOfSelectedValue = this.listOfValue.filter(v => !this.compareWith(v, item.nzValue));\n    this.updateListOfValue(listOfSelectedValue);\n    this.clearInput();\n  }\n  updateListOfContainerItem() {\n    let listOfContainerItem = this.listOfTagAndTemplateItem.filter(item => !item.nzHide).filter(item => {\n      if (!this.nzServerSearch && this.searchValue) {\n        return this.nzFilterOption(this.searchValue, item);\n      } else {\n        return true;\n      }\n    });\n    if (this.nzMode === 'tags' && this.searchValue) {\n      const matchedItem = this.listOfTagAndTemplateItem.find(item => item.nzLabel === this.searchValue);\n      if (!matchedItem) {\n        const tagItem = this.generateTagItem(this.searchValue);\n        listOfContainerItem = [tagItem, ...listOfContainerItem];\n        this.activatedValue = tagItem.nzValue;\n      } else {\n        this.activatedValue = matchedItem.nzValue;\n      }\n    }\n    const activatedItem = listOfContainerItem.find(item => item.nzLabel === this.searchValue) || listOfContainerItem.find(item => this.compareWith(item.nzValue, this.activatedValue)) || listOfContainerItem.find(item => this.compareWith(item.nzValue, this.listOfValue[0])) || listOfContainerItem[0];\n    this.activatedValue = activatedItem && activatedItem.nzValue || null;\n    let listOfGroupLabel = [];\n    if (this.isReactiveDriven) {\n      listOfGroupLabel = [...new Set(this.nzOptions.filter(o => o.groupLabel).map(o => o.groupLabel))];\n    } else {\n      if (this.listOfNzOptionGroupComponent) {\n        listOfGroupLabel = this.listOfNzOptionGroupComponent.map(o => o.nzLabel);\n      }\n    }\n    /** insert group item **/\n    listOfGroupLabel.forEach(label => {\n      const index = listOfContainerItem.findIndex(item => label === item.groupLabel);\n      if (index > -1) {\n        const groupItem = {\n          groupLabel: label,\n          type: 'group',\n          key: label\n        };\n        listOfContainerItem.splice(index, 0, groupItem);\n      }\n    });\n    this.listOfContainerItem = [...listOfContainerItem];\n    this.updateCdkConnectedOverlayPositions();\n  }\n  clearInput() {\n    this.nzSelectTopControlComponent.clearInputValue();\n  }\n  updateListOfValue(listOfValue) {\n    const covertListToModel = (list, mode) => {\n      if (mode === 'default') {\n        if (list.length > 0) {\n          return list[0];\n        } else {\n          return null;\n        }\n      } else {\n        return list;\n      }\n    };\n    const model = covertListToModel(listOfValue, this.nzMode);\n    if (this.value !== model) {\n      this.listOfValue = listOfValue;\n      this.listOfValue$.next(listOfValue);\n      this.value = model;\n      this.onChange(this.value);\n    }\n    this.isMaxLimitReached = this.nzMaxMultipleCount !== Infinity && this.listOfValue.length === this.nzMaxMultipleCount;\n  }\n  onTokenSeparate(listOfLabel) {\n    const listOfMatchedValue = this.listOfTagAndTemplateItem.filter(item => listOfLabel.findIndex(label => label === item.nzLabel) !== -1).map(item => item.nzValue).filter(item => this.listOfValue.findIndex(v => this.compareWith(v, item)) === -1);\n    if (this.nzMode === 'multiple') {\n      this.updateListOfValue([...this.listOfValue, ...listOfMatchedValue]);\n    } else if (this.nzMode === 'tags') {\n      const listOfUnMatchedLabel = listOfLabel.filter(label => this.listOfTagAndTemplateItem.findIndex(item => item.nzLabel === label) === -1);\n      this.updateListOfValue([...this.listOfValue, ...listOfMatchedValue, ...listOfUnMatchedLabel]);\n    }\n    this.clearInput();\n  }\n  onKeyDown(e) {\n    if (this.nzDisabled) {\n      return;\n    }\n    const listOfFilteredOptionNotDisabled = this.listOfContainerItem.filter(item => item.type === 'item').filter(item => !item.nzDisabled);\n    const activatedIndex = listOfFilteredOptionNotDisabled.findIndex(item => this.compareWith(item.nzValue, this.activatedValue));\n    switch (e.keyCode) {\n      case UP_ARROW:\n        e.preventDefault();\n        if (this.nzOpen && listOfFilteredOptionNotDisabled.length > 0) {\n          const preIndex = activatedIndex > 0 ? activatedIndex - 1 : listOfFilteredOptionNotDisabled.length - 1;\n          this.activatedValue = listOfFilteredOptionNotDisabled[preIndex].nzValue;\n        }\n        break;\n      case DOWN_ARROW:\n        e.preventDefault();\n        if (this.nzOpen && listOfFilteredOptionNotDisabled.length > 0) {\n          const nextIndex = activatedIndex < listOfFilteredOptionNotDisabled.length - 1 ? activatedIndex + 1 : 0;\n          this.activatedValue = listOfFilteredOptionNotDisabled[nextIndex].nzValue;\n        } else {\n          this.setOpenState(true);\n        }\n        break;\n      case ENTER:\n        e.preventDefault();\n        if (this.nzOpen) {\n          if (isNotNil(this.activatedValue) && activatedIndex !== -1) {\n            this.onItemClick(this.activatedValue);\n          }\n        } else {\n          this.setOpenState(true);\n        }\n        break;\n      case SPACE:\n        if (!this.nzOpen) {\n          this.setOpenState(true);\n          e.preventDefault();\n        }\n        break;\n      case TAB:\n        if (this.nzSelectOnTab) {\n          if (this.nzOpen) {\n            e.preventDefault();\n            if (isNotNil(this.activatedValue)) {\n              this.onItemClick(this.activatedValue);\n            }\n          }\n        } else {\n          this.setOpenState(false);\n        }\n        break;\n      case ESCAPE:\n        /**\n         * Skip the ESCAPE processing, it will be handled in {@link onOverlayKeyDown}.\n         */\n        break;\n      default:\n        if (!this.nzOpen) {\n          this.setOpenState(true);\n        }\n    }\n  }\n  setOpenState(value) {\n    if (this.nzOpen !== value) {\n      this.nzOpen = value;\n      this.nzOpenChange.emit(value);\n      this.onOpenChange();\n      this.cdr.markForCheck();\n    }\n  }\n  onOpenChange() {\n    this.updateCdkConnectedOverlayStatus();\n    if (this.nzAutoClearSearchValue) {\n      this.clearInput();\n    }\n  }\n  onInputValueChange(value) {\n    this.searchValue = value;\n    this.updateListOfContainerItem();\n    this.nzOnSearch.emit(value);\n    this.updateCdkConnectedOverlayPositions();\n  }\n  onClearSelection() {\n    this.updateListOfValue([]);\n  }\n  onClickOutside(event) {\n    if (!this.host.nativeElement.contains(event.target)) {\n      this.setOpenState(false);\n    }\n  }\n  focus() {\n    this.nzSelectTopControlComponent.focus();\n  }\n  blur() {\n    this.nzSelectTopControlComponent.blur();\n  }\n  onPositionChange(position) {\n    const placement = getPlacementName(position);\n    this.dropDownPosition = placement;\n  }\n  updateCdkConnectedOverlayStatus() {\n    if (this.platform.isBrowser && this.originElement.nativeElement) {\n      const triggerWidth = this.triggerWidth;\n      cancelRequestAnimationFrame(this.requestId);\n      this.requestId = reqAnimFrame(() => {\n        // Blink triggers style and layout pipelines anytime the `getBoundingClientRect()` is called, which may cause a\n        // frame drop. That's why it's scheduled through the `requestAnimationFrame` to unload the composite thread.\n        this.triggerWidth = this.originElement.nativeElement.getBoundingClientRect().width;\n        if (triggerWidth !== this.triggerWidth) {\n          // The `requestAnimationFrame` will trigger change detection, but we're inside an `OnPush` component which won't have\n          // the `ChecksEnabled` state. Calling `markForCheck()` will allow Angular to run the change detection from the root component\n          // down to the `nz-select`. But we'll trigger only local change detection if the `triggerWidth` has been changed.\n          this.cdr.detectChanges();\n        }\n      });\n    }\n  }\n  updateCdkConnectedOverlayPositions() {\n    reqAnimFrame(() => {\n      this.cdkConnectedOverlay?.overlayRef?.updatePosition();\n    });\n  }\n  constructor(ngZone, destroy$, nzConfigService, cdr, host, renderer, platform, focusMonitor, directionality, noAnimation, nzFormStatusService, nzFormNoStatusService) {\n    this.ngZone = ngZone;\n    this.destroy$ = destroy$;\n    this.nzConfigService = nzConfigService;\n    this.cdr = cdr;\n    this.host = host;\n    this.renderer = renderer;\n    this.platform = platform;\n    this.focusMonitor = focusMonitor;\n    this.directionality = directionality;\n    this.noAnimation = noAnimation;\n    this.nzFormStatusService = nzFormStatusService;\n    this.nzFormNoStatusService = nzFormNoStatusService;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzId = null;\n    this.nzSize = 'default';\n    this.nzStatus = '';\n    this.nzOptionHeightPx = 32;\n    this.nzOptionOverflowSize = 8;\n    this.nzDropdownClassName = null;\n    this.nzDropdownMatchSelectWidth = true;\n    this.nzDropdownStyle = null;\n    this.nzNotFoundContent = undefined;\n    this.nzPlaceHolder = null;\n    this.nzPlacement = null;\n    this.nzMaxTagCount = Infinity;\n    this.nzDropdownRender = null;\n    this.nzCustomTemplate = null;\n    this.nzSuffixIcon = null;\n    this.nzClearIcon = null;\n    this.nzRemoveIcon = null;\n    this.nzMenuItemSelectedIcon = null;\n    this.nzTokenSeparators = [];\n    this.nzMaxTagPlaceholder = null;\n    this.nzMaxMultipleCount = Infinity;\n    this.nzMode = 'default';\n    this.nzFilterOption = defaultFilterOption;\n    this.compareWith = (o1, o2) => o1 === o2;\n    this.nzAllowClear = false;\n    this.nzBorderless = false;\n    this.nzShowSearch = false;\n    this.nzLoading = false;\n    this.nzAutoFocus = false;\n    this.nzAutoClearSearchValue = true;\n    this.nzServerSearch = false;\n    this.nzDisabled = false;\n    this.nzOpen = false;\n    this.nzSelectOnTab = false;\n    this.nzBackdrop = false;\n    this.nzOptions = [];\n    this.nzOnSearch = new EventEmitter();\n    this.nzScrollToBottom = new EventEmitter();\n    this.nzOpenChange = new EventEmitter();\n    this.nzBlur = new EventEmitter();\n    this.nzFocus = new EventEmitter();\n    this.listOfValue$ = new BehaviorSubject([]);\n    this.listOfTemplateItem$ = new BehaviorSubject([]);\n    this.listOfTagAndTemplateItem = [];\n    this.searchValue = '';\n    this.isReactiveDriven = false;\n    this.requestId = -1;\n    this.isNzDisableFirstChange = true;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.dropDownPosition = 'bottomLeft';\n    this.triggerWidth = null;\n    this.listOfContainerItem = [];\n    this.listOfTopItem = [];\n    this.activatedValue = null;\n    this.listOfValue = [];\n    this.focused = false;\n    this.dir = 'ltr';\n    this.positions = [];\n    this.isMaxLimitReached = false;\n    // status\n    this.prefixCls = 'ant-select';\n    this.statusCls = {};\n    this.status = '';\n    this.hasFeedback = false;\n  }\n  writeValue(modelValue) {\n    /** https://github.com/angular/angular/issues/14988 **/\n    if (this.value !== modelValue) {\n      this.value = modelValue;\n      const covertModelToList = (model, mode) => {\n        if (model === null || model === undefined) {\n          return [];\n        } else if (mode === 'default') {\n          return [model];\n        } else {\n          return model;\n        }\n      };\n      const listOfValue = covertModelToList(modelValue, this.nzMode);\n      this.listOfValue = listOfValue;\n      this.listOfValue$.next(listOfValue);\n      this.cdr.markForCheck();\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n    this.isNzDisableFirstChange = false;\n    if (this.nzDisabled) {\n      this.setOpenState(false);\n    }\n    this.cdr.markForCheck();\n  }\n  ngOnChanges(changes) {\n    const {\n      nzOpen,\n      nzDisabled,\n      nzOptions,\n      nzStatus,\n      nzPlacement\n    } = changes;\n    if (nzOpen) {\n      this.onOpenChange();\n    }\n    if (nzDisabled && this.nzDisabled) {\n      this.setOpenState(false);\n    }\n    if (nzOptions) {\n      this.isReactiveDriven = true;\n      const listOfOptions = this.nzOptions || [];\n      const listOfTransformedItem = listOfOptions.map(item => {\n        return {\n          template: item.label instanceof TemplateRef ? item.label : null,\n          nzTitle: this.getTitle(item.title, item.label),\n          nzLabel: typeof item.label === 'string' || typeof item.label === 'number' ? item.label : null,\n          nzValue: item.value,\n          nzDisabled: item.disabled || false,\n          nzHide: item.hide || false,\n          nzCustomContent: item.label instanceof TemplateRef,\n          groupLabel: item.groupLabel || null,\n          type: 'item',\n          key: item.key === undefined ? item.value : item.key\n        };\n      });\n      this.listOfTemplateItem$.next(listOfTransformedItem);\n    }\n    if (nzStatus) {\n      this.setStatusStyles(this.nzStatus, this.hasFeedback);\n    }\n    if (nzPlacement) {\n      const {\n        currentValue\n      } = nzPlacement;\n      this.dropDownPosition = currentValue;\n      const listOfPlacement = ['bottomLeft', 'topLeft', 'bottomRight', 'topRight'];\n      if (currentValue && listOfPlacement.includes(currentValue)) {\n        this.positions = [POSITION_MAP[currentValue]];\n      } else {\n        this.positions = listOfPlacement.map(e => POSITION_MAP[e]);\n      }\n    }\n  }\n  ngOnInit() {\n    this.nzFormStatusService?.formStatusChanges.pipe(distinctUntilChanged((pre, cur) => {\n      return pre.status === cur.status && pre.hasFeedback === cur.hasFeedback;\n    }), withLatestFrom(this.nzFormNoStatusService ? this.nzFormNoStatusService.noFormStatus : of(false)), map(([{\n      status,\n      hasFeedback\n    }, noStatus]) => ({\n      status: noStatus ? '' : status,\n      hasFeedback\n    })), takeUntil(this.destroy$)).subscribe(({\n      status,\n      hasFeedback\n    }) => {\n      this.setStatusStyles(status, hasFeedback);\n    });\n    this.focusMonitor.monitor(this.host, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      if (!focusOrigin) {\n        this.focused = false;\n        this.cdr.markForCheck();\n        this.nzBlur.emit();\n        Promise.resolve().then(() => {\n          this.onTouched();\n        });\n      } else {\n        this.focused = true;\n        this.cdr.markForCheck();\n        this.nzFocus.emit();\n      }\n    });\n    combineLatest([this.listOfValue$, this.listOfTemplateItem$]).pipe(takeUntil(this.destroy$)).subscribe(([listOfSelectedValue, listOfTemplateItem]) => {\n      const listOfTagItem = listOfSelectedValue.filter(() => this.nzMode === 'tags').filter(value => listOfTemplateItem.findIndex(o => this.compareWith(o.nzValue, value)) === -1).map(value => this.listOfTopItem.find(o => this.compareWith(o.nzValue, value)) || this.generateTagItem(value));\n      this.listOfTagAndTemplateItem = [...listOfTemplateItem, ...listOfTagItem];\n      this.listOfTopItem = this.listOfValue.map(v => [...this.listOfTagAndTemplateItem, ...this.listOfTopItem].find(item => this.compareWith(v, item.nzValue))).filter(item => !!item);\n      this.updateListOfContainerItem();\n    });\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.nzConfigService.getConfigChangeEventForComponent('select').pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.cdr.markForCheck();\n    });\n    this.dir = this.directionality.value;\n    this.ngZone.runOutsideAngular(() => fromEvent(this.host.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(() => {\n      if (this.nzOpen && this.nzShowSearch || this.nzDisabled) {\n        return;\n      }\n      this.ngZone.run(() => this.setOpenState(!this.nzOpen));\n    }));\n    // Caretaker note: we could've added this listener within the template `(overlayKeydown)=\"...\"`,\n    // but with this approach, it'll run change detection on each keyboard click, and also it'll run\n    // `markForCheck()` internally, which means the whole component tree (starting from the root and\n    // going down to the select component) will be re-checked and updated (if needed).\n    // This is safe to do that manually since `setOpenState()` calls `markForCheck()` if needed.\n    this.cdkConnectedOverlay.overlayKeydown.pipe(takeUntil(this.destroy$)).subscribe(event => {\n      if (event.keyCode === ESCAPE) {\n        this.setOpenState(false);\n      }\n    });\n  }\n  ngAfterContentInit() {\n    if (!this.isReactiveDriven) {\n      merge(this.listOfNzOptionGroupComponent.changes, this.listOfNzOptionComponent.changes).pipe(startWith(true), switchMap(() => merge(...[this.listOfNzOptionComponent.changes, this.listOfNzOptionGroupComponent.changes, ...this.listOfNzOptionComponent.map(option => option.changes), ...this.listOfNzOptionGroupComponent.map(option => option.changes)]).pipe(startWith(true))), takeUntil(this.destroy$)).subscribe(() => {\n        const listOfOptionInterface = this.listOfNzOptionComponent.toArray().map(item => {\n          const {\n            template,\n            nzLabel,\n            nzValue,\n            nzKey,\n            nzDisabled,\n            nzHide,\n            nzCustomContent,\n            groupLabel\n          } = item;\n          return {\n            template,\n            nzLabel,\n            nzValue,\n            nzDisabled,\n            nzHide,\n            nzCustomContent,\n            groupLabel,\n            nzTitle: this.getTitle(item.nzTitle, item.nzLabel),\n            type: 'item',\n            key: nzKey === undefined ? nzValue : nzKey\n          };\n        });\n        this.listOfTemplateItem$.next(listOfOptionInterface);\n        this.cdr.markForCheck();\n      });\n    }\n  }\n  ngOnDestroy() {\n    cancelRequestAnimationFrame(this.requestId);\n    this.focusMonitor.stopMonitoring(this.host);\n  }\n  setStatusStyles(status, hasFeedback) {\n    this.status = status;\n    this.hasFeedback = hasFeedback;\n    this.cdr.markForCheck();\n    // render status if nzStatus is set\n    this.statusCls = getStatusClassNames(this.prefixCls, status, hasFeedback);\n    Object.keys(this.statusCls).forEach(status => {\n      if (this.statusCls[status]) {\n        this.renderer.addClass(this.host.nativeElement, status);\n      } else {\n        this.renderer.removeClass(this.host.nativeElement, status);\n      }\n    });\n  }\n  getTitle(title, label) {\n    let rawTitle = undefined;\n    if (title === undefined) {\n      if (typeof label === 'string' || typeof label === 'number') {\n        rawTitle = label.toString();\n      }\n    } else if (typeof title === 'string' || typeof title === 'number') {\n      rawTitle = title.toString();\n    }\n    return rawTitle;\n  }\n  static {\n    this.ɵfac = function NzSelectComponent_Factory(t) {\n      return new (t || NzSelectComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.NzDestroyService), i0.ɵɵdirectiveInject(i2$2.NzConfigService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(i1$4.FocusMonitor), i0.ɵɵdirectiveInject(i5.Directionality, 8), i0.ɵɵdirectiveInject(i1$5.NzNoAnimationDirective, 9), i0.ɵɵdirectiveInject(i7.NzFormStatusService, 8), i0.ɵɵdirectiveInject(i7.NzFormNoStatusService, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSelectComponent,\n      selectors: [[\"nz-select\"]],\n      contentQueries: function NzSelectComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzOptionComponent, 5);\n          i0.ɵɵcontentQuery(dirIndex, NzOptionGroupComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzOptionComponent = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzOptionGroupComponent = _t);\n        }\n      },\n      viewQuery: function NzSelectComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkOverlayOrigin, 7, ElementRef);\n          i0.ɵɵviewQuery(CdkConnectedOverlay, 7);\n          i0.ɵɵviewQuery(NzSelectTopControlComponent, 7);\n          i0.ɵɵviewQuery(NzOptionGroupComponent, 7, ElementRef);\n          i0.ɵɵviewQuery(NzSelectTopControlComponent, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.originElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cdkConnectedOverlay = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzSelectTopControlComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzOptionGroupComponentElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzSelectTopControlComponentElement = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-select\"],\n      hostVars: 26,\n      hostBindings: function NzSelectComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-select-in-form-item\", !!ctx.nzFormStatusService)(\"ant-select-lg\", ctx.nzSize === \"large\")(\"ant-select-sm\", ctx.nzSize === \"small\")(\"ant-select-show-arrow\", ctx.nzShowArrow)(\"ant-select-disabled\", ctx.nzDisabled)(\"ant-select-show-search\", (ctx.nzShowSearch || ctx.nzMode !== \"default\") && !ctx.nzDisabled)(\"ant-select-allow-clear\", ctx.nzAllowClear)(\"ant-select-borderless\", ctx.nzBorderless)(\"ant-select-open\", ctx.nzOpen)(\"ant-select-focused\", ctx.nzOpen || ctx.focused)(\"ant-select-single\", ctx.nzMode === \"default\")(\"ant-select-multiple\", ctx.nzMode !== \"default\")(\"ant-select-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzId: \"nzId\",\n        nzSize: \"nzSize\",\n        nzStatus: \"nzStatus\",\n        nzOptionHeightPx: \"nzOptionHeightPx\",\n        nzOptionOverflowSize: \"nzOptionOverflowSize\",\n        nzDropdownClassName: \"nzDropdownClassName\",\n        nzDropdownMatchSelectWidth: \"nzDropdownMatchSelectWidth\",\n        nzDropdownStyle: \"nzDropdownStyle\",\n        nzNotFoundContent: \"nzNotFoundContent\",\n        nzPlaceHolder: \"nzPlaceHolder\",\n        nzPlacement: \"nzPlacement\",\n        nzMaxTagCount: \"nzMaxTagCount\",\n        nzDropdownRender: \"nzDropdownRender\",\n        nzCustomTemplate: \"nzCustomTemplate\",\n        nzSuffixIcon: \"nzSuffixIcon\",\n        nzClearIcon: \"nzClearIcon\",\n        nzRemoveIcon: \"nzRemoveIcon\",\n        nzMenuItemSelectedIcon: \"nzMenuItemSelectedIcon\",\n        nzTokenSeparators: \"nzTokenSeparators\",\n        nzMaxTagPlaceholder: \"nzMaxTagPlaceholder\",\n        nzMaxMultipleCount: \"nzMaxMultipleCount\",\n        nzMode: \"nzMode\",\n        nzFilterOption: \"nzFilterOption\",\n        compareWith: \"compareWith\",\n        nzAllowClear: \"nzAllowClear\",\n        nzBorderless: \"nzBorderless\",\n        nzShowSearch: \"nzShowSearch\",\n        nzLoading: \"nzLoading\",\n        nzAutoFocus: \"nzAutoFocus\",\n        nzAutoClearSearchValue: \"nzAutoClearSearchValue\",\n        nzServerSearch: \"nzServerSearch\",\n        nzDisabled: \"nzDisabled\",\n        nzOpen: \"nzOpen\",\n        nzSelectOnTab: \"nzSelectOnTab\",\n        nzBackdrop: \"nzBackdrop\",\n        nzOptions: \"nzOptions\",\n        nzShowArrow: \"nzShowArrow\"\n      },\n      outputs: {\n        nzOnSearch: \"nzOnSearch\",\n        nzScrollToBottom: \"nzScrollToBottom\",\n        nzOpenChange: \"nzOpenChange\",\n        nzBlur: \"nzBlur\",\n        nzFocus: \"nzFocus\"\n      },\n      exportAs: [\"nzSelect\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzDestroyService, {\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzSelectComponent),\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 25,\n      consts: [[\"origin\", \"cdkOverlayOrigin\"], [\"feedbackIconTpl\", \"\"], [\"cdkOverlayOrigin\", \"\", 3, \"inputValueChange\", \"tokenize\", \"deleteItem\", \"keydown\", \"nzId\", \"open\", \"disabled\", \"mode\", \"nzNoAnimation\", \"maxTagPlaceholder\", \"removeIcon\", \"placeHolder\", \"maxTagCount\", \"customTemplate\", \"tokenSeparators\", \"showSearch\", \"autofocus\", \"listOfTopItem\"], [3, \"showArrow\", \"loading\", \"search\", \"suffixIcon\", \"feedbackIcon\", \"nzMaxMultipleCount\", \"listOfValue\", \"isMaxTagCountSet\", 4, \"ngIf\"], [3, \"clearIcon\", \"clear\", 4, \"ngIf\"], [\"cdkConnectedOverlay\", \"\", \"nzConnectedOverlay\", \"\", 3, \"overlayOutsideClick\", \"detach\", \"positionChange\", \"cdkConnectedOverlayHasBackdrop\", \"cdkConnectedOverlayMinWidth\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayTransformOriginOn\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayPositions\"], [3, \"showArrow\", \"loading\", \"search\", \"suffixIcon\", \"feedbackIcon\", \"nzMaxMultipleCount\", \"listOfValue\", \"isMaxTagCountSet\"], [3, \"status\", 4, \"ngIf\"], [3, \"status\"], [3, \"clear\", \"clearIcon\"], [3, \"keydown\", \"itemClick\", \"scrollToBottom\", \"ngStyle\", \"itemSize\", \"maxItemLength\", \"matchWidth\", \"nzNoAnimation\", \"listOfContainerItem\", \"menuItemSelectedIcon\", \"notFoundContent\", \"activatedValue\", \"listOfSelectedValue\", \"dropdownRender\", \"compareWith\", \"mode\", \"isMaxLimitReached\"]],\n      template: function NzSelectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nz-select-top-control\", 2, 0);\n          i0.ɵɵlistener(\"inputValueChange\", function NzSelectComponent_Template_nz_select_top_control_inputValueChange_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputValueChange($event));\n          })(\"tokenize\", function NzSelectComponent_Template_nz_select_top_control_tokenize_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTokenSeparate($event));\n          })(\"deleteItem\", function NzSelectComponent_Template_nz_select_top_control_deleteItem_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onItemDelete($event));\n          })(\"keydown\", function NzSelectComponent_Template_nz_select_top_control_keydown_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onKeyDown($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, NzSelectComponent_nz_select_arrow_2_Template, 3, 8, \"nz-select-arrow\", 3)(3, NzSelectComponent_nz_select_clear_3_Template, 1, 1, \"nz-select-clear\", 4)(4, NzSelectComponent_ng_template_4_Template, 1, 24, \"ng-template\", 5);\n          i0.ɵɵlistener(\"overlayOutsideClick\", function NzSelectComponent_Template_ng_template_overlayOutsideClick_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onClickOutside($event));\n          })(\"detach\", function NzSelectComponent_Template_ng_template_detach_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.setOpenState(false));\n          })(\"positionChange\", function NzSelectComponent_Template_ng_template_positionChange_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPositionChange($event));\n          });\n        }\n        if (rf & 2) {\n          const origin_r6 = i0.ɵɵreference(1);\n          i0.ɵɵproperty(\"nzId\", ctx.nzId)(\"open\", ctx.nzOpen)(\"disabled\", ctx.nzDisabled)(\"mode\", ctx.nzMode)(\"@.disabled\", !!(ctx.noAnimation == null ? null : ctx.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx.noAnimation == null ? null : ctx.noAnimation.nzNoAnimation)(\"maxTagPlaceholder\", ctx.nzMaxTagPlaceholder)(\"removeIcon\", ctx.nzRemoveIcon)(\"placeHolder\", ctx.nzPlaceHolder)(\"maxTagCount\", ctx.nzMaxTagCount)(\"customTemplate\", ctx.nzCustomTemplate)(\"tokenSeparators\", ctx.nzTokenSeparators)(\"showSearch\", ctx.nzShowSearch)(\"autofocus\", ctx.nzAutoFocus)(\"listOfTopItem\", ctx.listOfTopItem);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.nzShowArrow || ctx.hasFeedback && !!ctx.status || ctx.isMaxTagCountSet);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.nzAllowClear && !ctx.nzDisabled && ctx.listOfValue.length);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"cdkConnectedOverlayHasBackdrop\", ctx.nzBackdrop)(\"cdkConnectedOverlayMinWidth\", ctx.nzDropdownMatchSelectWidth ? null : ctx.triggerWidth)(\"cdkConnectedOverlayWidth\", ctx.nzDropdownMatchSelectWidth ? ctx.triggerWidth : null)(\"cdkConnectedOverlayOrigin\", origin_r6)(\"cdkConnectedOverlayTransformOriginOn\", \".ant-select-dropdown\")(\"cdkConnectedOverlayPanelClass\", ctx.nzDropdownClassName)(\"cdkConnectedOverlayOpen\", ctx.nzOpen)(\"cdkConnectedOverlayPositions\", ctx.positions);\n        }\n      },\n      dependencies: [NzSelectTopControlComponent, CdkOverlayOrigin, NzNoAnimationDirective, NzSelectArrowComponent, NgIf, NzFormPatchModule, i7.NzFormItemFeedbackIconComponent, NzSelectClearComponent, CdkConnectedOverlay, NzOverlayModule, i8.NzConnectedOverlayDirective, NzOptionContainerComponent, NgStyle],\n      encapsulation: 2,\n      data: {\n        animation: [slideMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n__decorate([WithConfig()], NzSelectComponent.prototype, \"nzOptionHeightPx\", void 0);\n__decorate([WithConfig()], NzSelectComponent.prototype, \"nzSuffixIcon\", void 0);\n__decorate([InputBoolean()], NzSelectComponent.prototype, \"nzAllowClear\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzSelectComponent.prototype, \"nzBorderless\", void 0);\n__decorate([InputBoolean()], NzSelectComponent.prototype, \"nzShowSearch\", void 0);\n__decorate([InputBoolean()], NzSelectComponent.prototype, \"nzLoading\", void 0);\n__decorate([InputBoolean()], NzSelectComponent.prototype, \"nzAutoFocus\", void 0);\n__decorate([InputBoolean()], NzSelectComponent.prototype, \"nzAutoClearSearchValue\", void 0);\n__decorate([InputBoolean()], NzSelectComponent.prototype, \"nzServerSearch\", void 0);\n__decorate([InputBoolean()], NzSelectComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzSelectComponent.prototype, \"nzOpen\", void 0);\n__decorate([InputBoolean()], NzSelectComponent.prototype, \"nzSelectOnTab\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzSelectComponent.prototype, \"nzBackdrop\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSelectComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-select',\n      exportAs: 'nzSelect',\n      preserveWhitespaces: false,\n      providers: [NzDestroyService, {\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzSelectComponent),\n        multi: true\n      }],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      animations: [slideMotion],\n      template: `\n    <nz-select-top-control\n      cdkOverlayOrigin\n      #origin=\"cdkOverlayOrigin\"\n      [nzId]=\"nzId\"\n      [open]=\"nzOpen\"\n      [disabled]=\"nzDisabled\"\n      [mode]=\"nzMode\"\n      [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n      [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n      [maxTagPlaceholder]=\"nzMaxTagPlaceholder\"\n      [removeIcon]=\"nzRemoveIcon\"\n      [placeHolder]=\"nzPlaceHolder\"\n      [maxTagCount]=\"nzMaxTagCount\"\n      [customTemplate]=\"nzCustomTemplate\"\n      [tokenSeparators]=\"nzTokenSeparators\"\n      [showSearch]=\"nzShowSearch\"\n      [autofocus]=\"nzAutoFocus\"\n      [listOfTopItem]=\"listOfTopItem\"\n      (inputValueChange)=\"onInputValueChange($event)\"\n      (tokenize)=\"onTokenSeparate($event)\"\n      (deleteItem)=\"onItemDelete($event)\"\n      (keydown)=\"onKeyDown($event)\"\n    ></nz-select-top-control>\n    <nz-select-arrow\n      *ngIf=\"nzShowArrow || (hasFeedback && !!status) || isMaxTagCountSet\"\n      [showArrow]=\"nzShowArrow\"\n      [loading]=\"nzLoading\"\n      [search]=\"nzOpen && nzShowSearch\"\n      [suffixIcon]=\"nzSuffixIcon\"\n      [feedbackIcon]=\"feedbackIconTpl\"\n      [nzMaxMultipleCount]=\"nzMaxMultipleCount\"\n      [listOfValue]=\"listOfValue\"\n      [isMaxTagCountSet]=\"isMaxTagCountSet\"\n    >\n      <ng-template #feedbackIconTpl>\n        <nz-form-item-feedback-icon *ngIf=\"hasFeedback && !!status\" [status]=\"status\"></nz-form-item-feedback-icon>\n      </ng-template>\n    </nz-select-arrow>\n\n    <nz-select-clear\n      *ngIf=\"nzAllowClear && !nzDisabled && listOfValue.length\"\n      [clearIcon]=\"nzClearIcon\"\n      (clear)=\"onClearSelection()\"\n    ></nz-select-clear>\n    <ng-template\n      cdkConnectedOverlay\n      nzConnectedOverlay\n      [cdkConnectedOverlayHasBackdrop]=\"nzBackdrop\"\n      [cdkConnectedOverlayMinWidth]=\"$any(nzDropdownMatchSelectWidth ? null : triggerWidth)\"\n      [cdkConnectedOverlayWidth]=\"$any(nzDropdownMatchSelectWidth ? triggerWidth : null)\"\n      [cdkConnectedOverlayOrigin]=\"origin\"\n      [cdkConnectedOverlayTransformOriginOn]=\"'.ant-select-dropdown'\"\n      [cdkConnectedOverlayPanelClass]=\"nzDropdownClassName!\"\n      [cdkConnectedOverlayOpen]=\"nzOpen\"\n      [cdkConnectedOverlayPositions]=\"positions\"\n      (overlayOutsideClick)=\"onClickOutside($event)\"\n      (detach)=\"setOpenState(false)\"\n      (positionChange)=\"onPositionChange($event)\"\n    >\n      <nz-option-container\n        [ngStyle]=\"nzDropdownStyle\"\n        [itemSize]=\"nzOptionHeightPx\"\n        [maxItemLength]=\"nzOptionOverflowSize\"\n        [matchWidth]=\"nzDropdownMatchSelectWidth\"\n        [class.ant-select-dropdown-placement-bottomLeft]=\"dropDownPosition === 'bottomLeft'\"\n        [class.ant-select-dropdown-placement-topLeft]=\"dropDownPosition === 'topLeft'\"\n        [class.ant-select-dropdown-placement-bottomRight]=\"dropDownPosition === 'bottomRight'\"\n        [class.ant-select-dropdown-placement-topRight]=\"dropDownPosition === 'topRight'\"\n        [@slideMotion]=\"'enter'\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        [listOfContainerItem]=\"listOfContainerItem\"\n        [menuItemSelectedIcon]=\"nzMenuItemSelectedIcon\"\n        [notFoundContent]=\"nzNotFoundContent\"\n        [activatedValue]=\"activatedValue\"\n        [listOfSelectedValue]=\"listOfValue\"\n        [dropdownRender]=\"nzDropdownRender\"\n        [compareWith]=\"compareWith\"\n        [mode]=\"nzMode\"\n        [isMaxLimitReached]=\"isMaxLimitReached\"\n        (keydown)=\"onKeyDown($event)\"\n        (itemClick)=\"onItemClick($event)\"\n        (scrollToBottom)=\"nzScrollToBottom.emit()\"\n      ></nz-option-container>\n    </ng-template>\n  `,\n      host: {\n        class: 'ant-select',\n        '[class.ant-select-in-form-item]': '!!nzFormStatusService',\n        '[class.ant-select-lg]': 'nzSize === \"large\"',\n        '[class.ant-select-sm]': 'nzSize === \"small\"',\n        '[class.ant-select-show-arrow]': `nzShowArrow`,\n        '[class.ant-select-disabled]': 'nzDisabled',\n        '[class.ant-select-show-search]': `(nzShowSearch || nzMode !== 'default') && !nzDisabled`,\n        '[class.ant-select-allow-clear]': 'nzAllowClear',\n        '[class.ant-select-borderless]': 'nzBorderless',\n        '[class.ant-select-open]': 'nzOpen',\n        '[class.ant-select-focused]': 'nzOpen || focused',\n        '[class.ant-select-single]': `nzMode === 'default'`,\n        '[class.ant-select-multiple]': `nzMode !== 'default'`,\n        '[class.ant-select-rtl]': `dir === 'rtl'`\n      },\n      imports: [NzSelectTopControlComponent, CdkOverlayOrigin, NzNoAnimationDirective, NzSelectArrowComponent, NgIf, NzFormPatchModule, NzSelectClearComponent, CdkConnectedOverlay, NzOverlayModule, NzOptionContainerComponent, NgStyle],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i1$1.NzDestroyService\n  }, {\n    type: i2$2.NzConfigService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i3.Platform\n  }, {\n    type: i1$4.FocusMonitor\n  }, {\n    type: i5.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i1$5.NzNoAnimationDirective,\n    decorators: [{\n      type: Host\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: i7.NzFormStatusService,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i7.NzFormNoStatusService,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzId: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    nzOptionHeightPx: [{\n      type: Input\n    }],\n    nzOptionOverflowSize: [{\n      type: Input\n    }],\n    nzDropdownClassName: [{\n      type: Input\n    }],\n    nzDropdownMatchSelectWidth: [{\n      type: Input\n    }],\n    nzDropdownStyle: [{\n      type: Input\n    }],\n    nzNotFoundContent: [{\n      type: Input\n    }],\n    nzPlaceHolder: [{\n      type: Input\n    }],\n    nzPlacement: [{\n      type: Input\n    }],\n    nzMaxTagCount: [{\n      type: Input\n    }],\n    nzDropdownRender: [{\n      type: Input\n    }],\n    nzCustomTemplate: [{\n      type: Input\n    }],\n    nzSuffixIcon: [{\n      type: Input\n    }],\n    nzClearIcon: [{\n      type: Input\n    }],\n    nzRemoveIcon: [{\n      type: Input\n    }],\n    nzMenuItemSelectedIcon: [{\n      type: Input\n    }],\n    nzTokenSeparators: [{\n      type: Input\n    }],\n    nzMaxTagPlaceholder: [{\n      type: Input\n    }],\n    nzMaxMultipleCount: [{\n      type: Input\n    }],\n    nzMode: [{\n      type: Input\n    }],\n    nzFilterOption: [{\n      type: Input\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    nzAllowClear: [{\n      type: Input\n    }],\n    nzBorderless: [{\n      type: Input\n    }],\n    nzShowSearch: [{\n      type: Input\n    }],\n    nzLoading: [{\n      type: Input\n    }],\n    nzAutoFocus: [{\n      type: Input\n    }],\n    nzAutoClearSearchValue: [{\n      type: Input\n    }],\n    nzServerSearch: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzOpen: [{\n      type: Input\n    }],\n    nzSelectOnTab: [{\n      type: Input\n    }],\n    nzBackdrop: [{\n      type: Input\n    }],\n    nzOptions: [{\n      type: Input\n    }],\n    nzShowArrow: [{\n      type: Input\n    }],\n    nzOnSearch: [{\n      type: Output\n    }],\n    nzScrollToBottom: [{\n      type: Output\n    }],\n    nzOpenChange: [{\n      type: Output\n    }],\n    nzBlur: [{\n      type: Output\n    }],\n    nzFocus: [{\n      type: Output\n    }],\n    originElement: [{\n      type: ViewChild,\n      args: [CdkOverlayOrigin, {\n        static: true,\n        read: ElementRef\n      }]\n    }],\n    cdkConnectedOverlay: [{\n      type: ViewChild,\n      args: [CdkConnectedOverlay, {\n        static: true\n      }]\n    }],\n    nzSelectTopControlComponent: [{\n      type: ViewChild,\n      args: [NzSelectTopControlComponent, {\n        static: true\n      }]\n    }],\n    listOfNzOptionComponent: [{\n      type: ContentChildren,\n      args: [NzOptionComponent, {\n        descendants: true\n      }]\n    }],\n    listOfNzOptionGroupComponent: [{\n      type: ContentChildren,\n      args: [NzOptionGroupComponent, {\n        descendants: true\n      }]\n    }],\n    nzOptionGroupComponentElement: [{\n      type: ViewChild,\n      args: [NzOptionGroupComponent, {\n        static: true,\n        read: ElementRef\n      }]\n    }],\n    nzSelectTopControlComponentElement: [{\n      type: ViewChild,\n      args: [NzSelectTopControlComponent, {\n        static: true,\n        read: ElementRef\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSelectModule {\n  static {\n    this.ɵfac = function NzSelectModule_Factory(t) {\n      return new (t || NzSelectModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzSelectModule,\n      imports: [NzOptionComponent, NzSelectComponent, NzOptionContainerComponent, NzOptionGroupComponent, NzOptionItemComponent, NzSelectTopControlComponent, NzSelectSearchComponent, NzSelectItemComponent, NzSelectClearComponent, NzSelectArrowComponent, NzSelectPlaceholderComponent, NzOptionItemGroupComponent],\n      exports: [NzOptionComponent, NzSelectComponent, NzOptionGroupComponent, NzSelectArrowComponent, NzSelectClearComponent, NzSelectItemComponent, NzSelectPlaceholderComponent, NzSelectSearchComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzSelectComponent, NzOptionContainerComponent, NzOptionItemComponent, NzSelectTopControlComponent, NzSelectSearchComponent, NzSelectItemComponent, NzSelectClearComponent, NzSelectArrowComponent, NzSelectPlaceholderComponent, NzOptionItemGroupComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSelectModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzOptionComponent, NzSelectComponent, NzOptionContainerComponent, NzOptionGroupComponent, NzOptionItemComponent, NzSelectTopControlComponent, NzSelectSearchComponent, NzSelectItemComponent, NzSelectClearComponent, NzSelectArrowComponent, NzSelectPlaceholderComponent, NzOptionItemGroupComponent],\n      exports: [NzOptionComponent, NzSelectComponent, NzOptionGroupComponent, NzSelectArrowComponent, NzSelectClearComponent, NzSelectItemComponent, NzSelectPlaceholderComponent, NzSelectSearchComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzOptionComponent, NzOptionContainerComponent, NzOptionGroupComponent, NzOptionItemComponent, NzOptionItemGroupComponent, NzSelectArrowComponent, NzSelectClearComponent, NzSelectComponent, NzSelectItemComponent, NzSelectModule, NzSelectPlaceholderComponent, NzSelectSearchComponent, NzSelectTopControlComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAAC;AAC9E,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,eAAe,CAAC;AAAA,EACrG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,QAAQ;AAAA,EACnD;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,kBAAkB,OAAO,KAAK;AAAA,EACnC;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,QAAQ,CAAC;AAC7E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,IAAI,EAAE,YAAY,OAAO,IAAI;AAAA,EAC7D;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,kBAAkB,CAAC;AACnC,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,mBAAmB,OAAO,eAAe;AAAA,EACzD;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AACzF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,wBAAwB,CAAC;AAAA,EAC3C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,WAAW,WAAW,QAAQ,UAAU;AAAA,EAC7C;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,kBAAkB,EAAE;AACzC,IAAG,WAAW,aAAa,SAAS,uGAAuG,QAAQ;AACjJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,aAAa,SAAS,uGAAuG,QAAQ;AACtI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,OAAO,oBAAoB,EAAE,iBAAiB,QAAQ,eAAe,EAAE,YAAY,QAAQ,QAAQ,EAAE,WAAW,CAAC,CAAC,QAAQ,UAAU,EAAE,YAAY,QAAQ,cAAc,OAAO,qBAAqB,CAAC,OAAO,oBAAoB,SAAS,QAAQ,SAAS,CAAC,CAAC,EAAE,aAAa,OAAO,SAAS,UAAU,OAAO,SAAS,UAAU,EAAE,SAAS,QAAQ,OAAO,EAAE,SAAS,QAAQ,OAAO,EAAE,eAAe,OAAO,WAAW,EAAE,kBAAkB,OAAO,cAAc,EAAE,uBAAuB,OAAO,mBAAmB,EAAE,SAAS,QAAQ,OAAO;AAAA,EACpiB;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,GAAG,CAAC;AAC/B,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,wBAAwB,CAAC,EAAE,GAAG,oEAAoE,GAAG,IAAI,kBAAkB,CAAC;AAC7N,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,WAAW,YAAY,QAAQ,IAAI;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO;AACrC,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,MAAM;AAAA,EACtC;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAAC;AACrE,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,IAAI,OAAO,YAAY,QAAQ,OAAO,OAAO,oBAAoB,EAAE;AAAA,EAC3F;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,oEAAoE,IAAI,KAAK;AACpF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,oEAAoE,IAAI,KAAK;AACpF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,qEAAqE,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,qEAAqE,GAAG,GAAG,QAAQ,CAAC;AAC9L,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,MAAM;AACpC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,MAAM;AAAA,EACrC;AACF;AACA,SAAS,kFAAkF,IAAI,KAAK;AAClG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,gBAAmB,cAAc,EAAE;AACzC,IAAG,WAAW,UAAU,aAAa;AAAA,EACvC;AACF;AACA,SAAS,2EAA2E,IAAI,KAAK;AAC3F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,mFAAmF,GAAG,GAAG,QAAQ,EAAE;AACpH,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,gBAAgB,IAAI;AAC1B,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,aAAa;AAAA,EACrC;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4EAA4E,GAAG,GAAG,gBAAgB,CAAC;AAAA,EACtH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,0BAA0B,OAAO,UAAU;AAAA,EAC3D;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,6DAA6D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,EACjO;AACA,MAAI,KAAK,GAAG;AACV,UAAM,oBAAuB,YAAY,CAAC;AAC1C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,OAAO,aAAa,CAAC,OAAO,UAAU,EAAE,YAAY,iBAAiB;AAAA,EAC7F;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY;AAAA,EAC1C;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK;AAAA,EACnC;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,kBAAkB,OAAO,KAAK;AAAA,EACnC;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,6DAA6D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC7M,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,YAAY,CAAC;AACzC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,SAAS,EAAE,YAAY,gBAAgB;AAAA,EACtE;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,SAAS,SAAS,4DAA4D,QAAQ;AAClG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,MAAM,CAAC;AAAA,IAC/C,CAAC;AACD,IAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,QAAQ,CAAC;AAC9E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,UAAU,EAAE,YAAY,OAAO,UAAU;AAAA,EACzE;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,aAAa,GAAG;AAAA,EACpD;AACF;AACA,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,eAAe;AAC5B,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,GAAG,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,qEAAqE,IAAI,KAAK;AACrF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB,CAAC;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,KAAK,EAAE,YAAY,KAAK,EAAE,cAAc,OAAO,UAAU,EAAE,SAAS,OAAO,cAAc,CAAC,EAAE,OAAO,EAAE,yBAAyB,OAAO,cAAc,EAAE,gCAAgC,OAAO,cAAc,CAAC,CAAC;AAAA,EACzO;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,oBAAoB,CAAC;AAC1C,IAAG,WAAW,qBAAqB,SAAS,kGAAkG,QAAQ;AACpJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,kBAAkB,MAAM,CAAC;AAAA,IACxD,CAAC,EAAE,eAAe,SAAS,4FAA4F,QAAQ;AAC7H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,sEAAsE,GAAG,GAAG,kBAAkB,CAAC;AAChH,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,IAAI,EAAE,YAAY,OAAO,QAAQ,EAAE,SAAS,OAAO,UAAU,EAAE,aAAa,OAAO,UAAU,EAAE,cAAc,KAAK,EAAE,aAAa,OAAO,SAAS,EAAE,gBAAgB,OAAO,IAAI;AAC3M,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,iBAAiB;AAAA,EAChD;AACF;AACA,SAAS,qEAAqE,IAAI,KAAK;AACrF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,kBAAkB,CAAC;AACxC,IAAG,WAAW,UAAU,SAAS,wGAAwG;AACvI,YAAM,UAAa,cAAc,GAAG,EAAE;AACtC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,QAAQ,4BAA4B,CAAC;AAAA,IACjF,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,UAAU,EAAE,SAAS,QAAQ,OAAO,EAAE,YAAY,QAAQ,cAAc,OAAO,QAAQ,EAAE,yBAAyB,QAAQ,qBAAqB,EAAE,aAAa,IAAI,EAAE,gCAAgC,QAAQ,4BAA4B;AAAA,EAC7Q;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,sEAAsE,GAAG,GAAG,kBAAkB,CAAC;AAChH,IAAG,eAAe,GAAG,oBAAoB,CAAC;AAC1C,IAAG,WAAW,qBAAqB,SAAS,kGAAkG,QAAQ;AACpJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,kBAAkB,MAAM,CAAC;AAAA,IACxD,CAAC,EAAE,eAAe,SAAS,4FAA4F,QAAQ;AAC7H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,gBAAgB,EAAE,gBAAgB,OAAO,UAAU;AACnF,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,IAAI,EAAE,YAAY,OAAO,QAAQ,EAAE,SAAS,OAAO,UAAU,EAAE,aAAa,OAAO,SAAS,EAAE,aAAa,IAAI,EAAE,cAAc,IAAI,EAAE,gBAAgB,OAAO,IAAI;AAAA,EAC/L;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,yBAAyB,EAAE;AAAA,EAC7C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,eAAe,OAAO,WAAW;AAAA,EACjD;AACF;AACA,SAAS,wFAAwF,IAAI,KAAK;AACxG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,8BAA8B,CAAC;AAAA,EACjD;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,MAAM;AAAA,EACvC;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yFAAyF,GAAG,GAAG,8BAA8B,CAAC;AAAA,EACjJ;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,eAAe,CAAC,CAAC,OAAO,MAAM;AAAA,EAC7D;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,mBAAmB,CAAC;AACzC,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACpI,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,qBAAwB,YAAY,CAAC;AAC3C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,WAAW,EAAE,WAAW,OAAO,SAAS,EAAE,UAAU,OAAO,UAAU,OAAO,YAAY,EAAE,cAAc,OAAO,YAAY,EAAE,gBAAgB,kBAAkB,EAAE,sBAAsB,OAAO,kBAAkB,EAAE,eAAe,OAAO,WAAW,EAAE,oBAAoB,OAAO,gBAAgB;AAAA,EACpU;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,mBAAmB,CAAC;AACzC,IAAG,WAAW,SAAS,SAAS,gFAAgF;AAC9G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,CAAC;AAAA,IACjD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,WAAW;AAAA,EAC/C;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,uBAAuB,EAAE;AAC9C,IAAG,WAAW,WAAW,SAAS,gFAAgF,QAAQ;AACxH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,MAAM,CAAC;AAAA,IAChD,CAAC,EAAE,aAAa,SAAS,kFAAkF,QAAQ;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,kBAAkB,SAAS,yFAAyF;AACrH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,KAAK,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,4CAA4C,OAAO,qBAAqB,YAAY,EAAE,yCAAyC,OAAO,qBAAqB,SAAS,EAAE,6CAA6C,OAAO,qBAAqB,aAAa,EAAE,0CAA0C,OAAO,qBAAqB,UAAU;AAC7V,IAAG,WAAW,WAAW,OAAO,eAAe,EAAE,YAAY,OAAO,gBAAgB,EAAE,iBAAiB,OAAO,oBAAoB,EAAE,cAAc,OAAO,0BAA0B,EAAE,gBAAgB,OAAO,EAAE,cAAc,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,cAAc,EAAE,iBAAiB,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,aAAa,EAAE,uBAAuB,OAAO,mBAAmB,EAAE,wBAAwB,OAAO,sBAAsB,EAAE,mBAAmB,OAAO,iBAAiB,EAAE,kBAAkB,OAAO,cAAc,EAAE,uBAAuB,OAAO,WAAW,EAAE,kBAAkB,OAAO,gBAAgB,EAAE,eAAe,OAAO,WAAW,EAAE,QAAQ,OAAO,MAAM,EAAE,qBAAqB,OAAO,iBAAiB;AAAA,EAC5vB;AACF;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,UAAU;AACf,SAAK,UAAU,IAAI,QAAQ;AAAA,EAC7B;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ,KAAK;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAAwB;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,QAAQ;AAAA,QACN,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,cAAc;AACZ,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,GAAG;AACzD,aAAO,KAAK,KAAK,6BAA4B;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,MACpC,WAAW,CAAC,GAAG,mBAAmB,uBAAuB;AAAA,MACzD,QAAQ;AAAA,QACN,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,CAAC;AAAA,MACtC,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,gBAAgB,CAAC;AAAA,QAC9F;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,0BAA0B,IAAI,OAAO;AAAA,QACrD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,+BAA+B;AAAA,MACjE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,cAAc;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,YAAY,QAAQ,UAAU;AACxC,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,iBAAiB;AACtB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,OAAO;AACZ,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,YAAY,IAAI,aAAa;AAAA,EACpC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS,qBAAqB;AAChC,WAAK,WAAW,KAAK,oBAAoB,KAAK,OAAK,KAAK,YAAY,GAAG,KAAK,KAAK,CAAC;AAAA,IACpF;AACA,QAAI,SAAS,gBAAgB;AAC3B,WAAK,YAAY,KAAK,YAAY,KAAK,gBAAgB,KAAK,KAAK;AAAA,IACnE;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,OAAO,kBAAkB,MAAM;AAClC,gBAAU,KAAK,WAAW,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC/F,YAAI,CAAC,KAAK,UAAU;AAClB,eAAK,OAAO,IAAI,MAAM,KAAK,UAAU,KAAK,KAAK,KAAK,CAAC;AAAA,QACvD;AAAA,MACF,CAAC;AACD,gBAAU,KAAK,WAAW,eAAe,YAAY,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACpG,YAAI,CAAC,KAAK,UAAU;AAClB,eAAK,OAAO,IAAI,MAAM,KAAK,UAAU,KAAK,KAAK,KAAK,CAAC;AAAA,QACvD;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAA0B,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAuB,gBAAgB,CAAC;AAAA,IAC3J;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,MAC9B,WAAW,CAAC,GAAG,mBAAmB,wBAAwB;AAAA,MAC1D,UAAU;AAAA,MACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,SAAS,IAAI,KAAK;AACjC,UAAG,YAAY,kCAAkC,IAAI,OAAO,EAAE,mCAAmC,IAAI,YAAY,CAAC,IAAI,QAAQ,EAAE,mCAAmC,IAAI,QAAQ,EAAE,iCAAiC,IAAI,aAAa,CAAC,IAAI,QAAQ;AAAA,QAClP;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,eAAe;AAAA,QACf,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA,MACA,SAAS;AAAA,QACP,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,GAAM,sBAAyB,mBAAmB;AAAA,MACrG,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAS,gCAAgC,SAAS,qBAAqB,gBAAgB,MAAM,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,gBAAgB,MAAM,GAAG,gCAAgC,GAAG,eAAe,MAAM,GAAG,CAAC,WAAW,IAAI,UAAU,SAAS,SAAS,4BAA4B,GAAG,QAAQ,UAAU,GAAG,CAAC,WAAW,IAAI,UAAU,SAAS,GAAG,0BAA0B,CAAC;AAAA,MAC3d,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,8CAA8C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC/L,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,OAAO,CAAC;AAAA,QACvE;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,qBAAwB,YAAY,CAAC;AAC3C,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,aAAa,EAAE,YAAY,kBAAkB;AACvE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,aAAa,IAAI,QAAQ;AAAA,QACrD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,MAAM,kBAAkB,cAAmB,eAAe;AAAA,MACzE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,0CAA0C;AAAA,QAC1C,2CAA2C;AAAA,QAC3C,2CAA2C;AAAA,QAC3C,yCAAyC;AAAA,MAC3C;AAAA,MACA,WAAW,CAAC,gBAAgB;AAAA,MAC5B,SAAS,CAAC,MAAM,kBAAkB,YAAY;AAAA,MAC9C,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,cAAc;AACZ,SAAK,kBAAkB;AACvB,SAAK,uBAAuB;AAC5B,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,OAAO;AACZ,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,iBAAiB,IAAI,aAAa;AACvC,SAAK,gBAAgB;AACrB,SAAK,SAAS,OAAO,MAAM;AAC3B,SAAK,aAAa,OAAO,WAAW;AAAA,EACtC;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,YAAY,OAAO;AAEjB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW,QAAQ,QAAQ;AACzB,WAAO,OAAO;AAAA,EAChB;AAAA,EACA,sBAAsB,OAAO;AAC3B,SAAK,gBAAgB;AACrB,QAAI,UAAU,KAAK,oBAAoB,SAAS,KAAK,gBAAgB,GAAG;AACtE,WAAK,eAAe,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,UAAM,QAAQ,KAAK,oBAAoB,UAAU,UAAQ,KAAK,YAAY,KAAK,KAAK,KAAK,cAAc,CAAC;AACxG,QAAI,QAAQ,KAAK,iBAAiB,SAAS,KAAK,gBAAgB,KAAK,eAAe;AAClF,WAAK,yBAAyB,cAAc,SAAS,CAAC;AAAA,IACxD;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,uBAAuB,gBAAgB;AACzC,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,WAAK,OAAO,kBAAkB,MAAM,WAAW,MAAM,KAAK,uBAAuB,CAAC,CAAC;AAAA,IACrF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,GAAG;AACzD,aAAO,KAAK,KAAK,6BAA4B;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,MACnC,WAAW,SAAS,iCAAiC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,0BAA0B,CAAC;AAAA,QAC5C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B,GAAG;AAAA,QACjF;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,qBAAqB;AAAA,MACpC,QAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,sBAAsB;AAAA,QACtB,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,aAAa;AAAA,QACb,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACvB;AAAA,MACA,SAAS;AAAA,QACP,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB;AAAA,MACA,UAAU,CAAC,mBAAmB;AAAA,MAC9B,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,SAAS,yBAAyB,GAAG,MAAM,GAAG,CAAC,GAAG,uBAAuB,YAAY,eAAe,aAAa,GAAG,CAAC,iBAAiB,IAAI,GAAG,mBAAmB,wBAAwB,gCAAgC,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,mBAAmB,UAAU,GAAG,iBAAiB,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,WAAW,GAAG,cAAc,GAAG,CAAC,GAAG,QAAQ,iBAAiB,YAAY,WAAW,YAAY,aAAa,SAAS,SAAS,eAAe,kBAAkB,uBAAuB,SAAS,aAAa,aAAa,GAAG,cAAc,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,aAAa,aAAa,QAAQ,iBAAiB,YAAY,WAAW,YAAY,aAAa,SAAS,SAAS,eAAe,kBAAkB,uBAAuB,OAAO,CAAC;AAAA,MAC5xB,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,KAAK;AAC1B,UAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,OAAO,CAAC;AAC1E,UAAG,eAAe,GAAG,+BAA+B,CAAC;AACrD,UAAG,WAAW,uBAAuB,SAAS,+FAA+F,QAAQ;AACnJ,mBAAO,IAAI,sBAAsB,MAAM;AAAA,UACzC,CAAC;AACD,UAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,eAAe,CAAC;AAC1F,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,eAAe,CAAC;AAC1F,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,oBAAoB,WAAW,CAAC;AAC1D,UAAG,UAAU;AACb,UAAG,YAAY,UAAU,IAAI,oBAAoB,SAAS,IAAI,UAAU,IAAI,EAAE,cAAc,IAAI,WAAW,IAAI,eAAe,IAAI;AAClI,UAAG,YAAY,cAAc,CAAC,IAAI,UAAU;AAC5C,UAAG,WAAW,YAAY,IAAI,QAAQ,EAAE,eAAe,IAAI,WAAW,IAAI,aAAa,EAAE,eAAe,IAAI,WAAW,IAAI,aAAa;AACxI,UAAG,UAAU;AACb,UAAG,WAAW,mBAAmB,IAAI,mBAAmB,EAAE,wBAAwB,IAAI,UAAU,EAAE,kCAAkC,CAAC;AACrI,UAAG,UAAU;AACb,UAAG,WAAW,oBAAoB,IAAI,cAAc;AAAA,QACtD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,eAAoB,uBAAuB,MAAM,UAAU,4BAA4B,cAAc,uBAAuB,kBAAkB,eAAkB,2BAA8B,iBAAoB,0BAA0B,eAAe;AAAA,MAC1Q,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,qBAAqB;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA8CV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,eAAe,MAAM,UAAU,4BAA4B,cAAc,uBAAuB,kBAAkB,eAAe,eAAe;AAAA,MAC1J,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,QAC/B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,wBAAwB,UAAU;AAC5C,SAAK,yBAAyB;AAC9B,SAAK,WAAW;AAChB,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,WAAW;AACT,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB,QAAQ,KAAK,UAAU,IAAI,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAClG,aAAK,aAAa,KAAK,uBAAuB;AAAA,MAChD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ,KAAK;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAkB,wBAAwB,CAAC,GAAM,kBAAuB,gBAAgB,CAAC;AAAA,IAClI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,MACzB,WAAW,SAAS,wBAAwB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,aAAa,CAAC;AAAA,QAC/B;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,QACjE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,UAAU;AAAA,MACrB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,GAAM,sBAAyB,mBAAmB;AAAA,MACrG,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,aAAa;AAAA,QAChF;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,UAAU,MAAM;AAC1E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,mBAAmB,MAAM;AAAA,CAClF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC,gBAAgB;AAAA,MAC5B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,cAAc,CAAC;AACpB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAAwB;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,WAAW,CAAC,GAAG,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,cAAc,SAAS,oCAAoC,IAAI,KAAK;AAClE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,4BAA4B,IAAI,OAAO;AAAA,QACxD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,kBAAkB;AAAA,QAClB,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,oBAAoB;AAAA,MACtB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,UAAU,WAAW,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,WAAW,IAAI,UAAU,SAAS,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,WAAW,IAAI,UAAU,QAAQ,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,UAAU,UAAU,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,UAAU,MAAM,GAAG,CAAC,WAAW,IAAI,UAAU,QAAQ,GAAG,CAAC,WAAW,IAAI,GAAG,UAAU,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,QAAQ,CAAC;AAAA,MAC1c,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,wCAAwC,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,+CAA+C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,gDAAgD,GAAG,GAAG,gBAAgB,CAAC;AAAA,QAC7U;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,kBAAqB,YAAY,CAAC;AACxC,UAAG,WAAW,QAAQ,IAAI,gBAAgB;AAC1C,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,OAAO,EAAE,YAAY,eAAe;AAC9D,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,0BAA0B,IAAI,YAAY;AAAA,QAC1D;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAmB,iBAAiB,MAAM,gBAAmB,+BAA+B;AAAA,MAC3G,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,oCAAoC;AAAA,MACtC;AAAA,MACA,SAAS,CAAC,cAAc,MAAM,cAAc;AAAA,MAC5C,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,YAAY;AACjB,SAAK,QAAQ,IAAI,aAAa;AAAA,EAChC;AAAA,EACA,QAAQ,GAAG;AACT,MAAE,eAAe;AACjB,MAAE,gBAAgB;AAClB,SAAK,MAAM,KAAK,CAAC;AAAA,EACnB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAAwB;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,WAAW,CAAC,GAAG,kBAAkB;AAAA,MACjC,cAAc,SAAS,oCAAoC,IAAI,KAAK;AAClE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,gDAAgD,QAAQ;AACtF,mBAAO,IAAI,QAAQ,MAAM;AAAA,UAC3B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,WAAW;AAAA,MACb;AAAA,MACA,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,WAAW,IAAI,UAAU,gBAAgB,WAAW,QAAQ,SAAS,yBAAyB,GAAG,QAAQ,UAAU,GAAG,CAAC,WAAW,IAAI,UAAU,gBAAgB,WAAW,QAAQ,GAAG,uBAAuB,CAAC;AAAA,MACxN,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,QAAQ,CAAC;AAAA,QAC1E;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAQ,CAAC,IAAI,SAAS,EAAE,YAAY,IAAI,SAAS;AAAA,QACjE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAmB,iBAAiB,IAAI;AAAA,MACvD,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,SAAS,CAAC,cAAc,IAAI;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,cAAc;AACZ,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,+BAA+B;AACpC,SAAK,wBAAwB;AAC7B,SAAK,SAAS,IAAI,aAAa;AAAA,EACjC;AAAA,EACA,SAAS,GAAG;AACV,MAAE,eAAe;AACjB,MAAE,gBAAgB;AAClB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,OAAO,KAAK,CAAC;AAAA,IACpB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAAuB;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,MAC9B,WAAW,CAAC,GAAG,2BAA2B;AAAA,MAC1C,UAAU;AAAA,MACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,SAAS,IAAI,KAAK;AACjC,UAAG,YAAY,sCAAsC,IAAI,QAAQ;AAAA,QACnE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,8BAA8B;AAAA,QAC9B,uBAAuB;AAAA,MACzB;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,GAAG,0BAA0B,+BAA+B,GAAG,CAAC,SAAS,oCAAoC,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,SAAS,qCAAqC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,mCAAmC,GAAG,CAAC,GAAG,oCAAoC,GAAG,OAAO,GAAG,CAAC,WAAW,IAAI,UAAU,SAAS,GAAG,QAAQ,UAAU,GAAG,CAAC,WAAW,IAAI,UAAU,OAAO,CAAC;AAAA,MACza,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,uCAAuC,GAAG,GAAG,QAAQ,CAAC;AAAA,QACpJ;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,0BAA0B,IAAI,qBAAqB,EAAE,iCAAoC,gBAAgB,GAAG,KAAK,IAAI,4BAA4B,CAAC;AAChK,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,aAAa,CAAC,IAAI,QAAQ;AAAA,QACtD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,iCAAiC,MAAM,cAAmB,eAAe;AAAA,MAC3G,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,8CAA8C;AAAA,MAChD;AAAA,MACA,SAAS,CAAC,gBAAgB,MAAM,YAAY;AAAA,MAC5C,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,cAAc;AACZ,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,GAAG;AAC3D,aAAO,KAAK,KAAK,+BAA8B;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,MACrC,WAAW,CAAC,GAAG,kCAAkC;AAAA,MACjD,QAAQ;AAAA,QACN,aAAa;AAAA,MACf;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,CAAC;AAAA,MACtC,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,gBAAgB,CAAC;AAAA,QAChG;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,0BAA0B,IAAI,WAAW;AAAA,QACzD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,+BAA+B;AAAA,MACjE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,cAAc;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,oBAAoB,aAAa;AAC/B,SAAK,kBAAkB,KAAK,WAAW;AAAA,EACzC;AAAA,EACA,cAAc,OAAO;AACnB,SAAK,QAAQ;AACb,SAAK,YAAY,KAAK,KAAK;AAC3B,QAAI,KAAK,YAAY;AACnB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,UAAM,WAAW,KAAK,aAAa;AACnC,aAAS,QAAQ;AACjB,SAAK,cAAc,EAAE;AAAA,EACvB;AAAA,EACA,kBAAkB;AAChB,UAAM,YAAY,KAAK,cAAc;AACrC,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM,WAAW,KAAK,aAAa;AACnC,SAAK,SAAS,YAAY,SAAS,OAAO;AAC1C,SAAK,SAAS,YAAY,WAAW,eAAe,GAAG,SAAS,KAAK,GAAQ;AAC7E,SAAK,SAAS,SAAS,SAAS,SAAS,GAAG,UAAU,WAAW,IAAI;AAAA,EACvE;AAAA,EACA,QAAQ;AACN,SAAK,aAAa,SAAS,KAAK,cAAc,UAAU;AAAA,EAC1D;AAAA,EACA,OAAO;AACL,SAAK,aAAa,cAAc,KAAK;AAAA,EACvC;AAAA,EACA,YAAY,YAAY,UAAU,cAAc;AAC9C,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,oBAAoB,IAAI,aAAa;AAAA,EAC5C;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,WAAW,KAAK,aAAa;AACnC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,WAAW;AACb,UAAI,KAAK,WAAW;AAClB,aAAK,SAAS,gBAAgB,UAAU,UAAU;AAAA,MACpD,OAAO;AACL,aAAK,SAAS,aAAa,UAAU,YAAY,UAAU;AAAA,MAC7D;AAAA,IACF;AAEA,QAAI,gBAAgB,aAAa,iBAAiB,QAAQ,aAAa,kBAAkB,OAAO;AAC9F,eAAS,MAAM;AAAA,IACjB;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,YAAY;AACnB,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAA4B,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAuB,YAAY,CAAC;AAAA,IAC5J;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,WAAW,SAAS,8BAA8B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,QACtE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,6BAA6B;AAAA,MAC5C,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,cAAc;AAAA,QACd,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,SAAS;AAAA,QACP,aAAa;AAAA,QACb,mBAAmB;AAAA,MACrB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC,CAAC,GAAM,sBAAyB,mBAAmB;AAAA,MACpD,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,gBAAgB,OAAO,GAAG,qCAAqC,GAAG,iBAAiB,oBAAoB,kBAAkB,WAAW,UAAU,GAAG,CAAC,SAAS,sCAAsC,GAAG,MAAM,GAAG,CAAC,GAAG,oCAAoC,CAAC;AAAA,MAC7S,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,UAAG,WAAW,iBAAiB,SAAS,gEAAgE,QAAQ;AAC9G,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,cAAc,MAAM,CAAC;AAAA,UACjD,CAAC,EAAE,oBAAoB,SAAS,qEAAqE;AACnG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,oBAAoB,IAAI,CAAC;AAAA,UACrD,CAAC,EAAE,kBAAkB,SAAS,mEAAmE;AAC/F,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,oBAAoB,KAAK,CAAC;AAAA,UACtD,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,QAAQ,CAAC;AAAA,QAC3E;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,WAAW,IAAI,YAAY,OAAO,CAAC;AAClD,UAAG,WAAW,WAAW,IAAI,KAAK,EAAE,YAAY,IAAI,QAAQ;AAC5D,UAAG,YAAY,MAAM,IAAI,IAAI,EAAE,aAAa,IAAI,YAAY,cAAc,IAAI;AAC9E,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,UAAU;AAAA,QACtC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,aAAkB,sBAA2B,iBAAsB,SAAS,IAAI;AAAA,MAC/F,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAgBV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,MACD,SAAS,CAAC,aAAa,IAAI;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,yBAAyB;AACvB,UAAM,uBAAuB,KAAK,cAAc,WAAW;AAC3D,SAAK,oBAAoB,wBAAwB,CAAC,KAAK,eAAe,CAAC,KAAK;AAC5E,SAAK,oBAAoB,CAAC,wBAAwB,CAAC,KAAK,eAAe,CAAC,KAAK;AAAA,EAC/E;AAAA,EACA,kBAAkB,aAAa;AAC7B,SAAK,cAAc;AACnB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,mBAAmB,OAAO;AACxB,QAAI,UAAU,KAAK,YAAY;AAC7B,WAAK,aAAa;AAClB,WAAK,uBAAuB;AAC5B,WAAK,iBAAiB,KAAK,KAAK;AAChC,WAAK,cAAc,OAAO,KAAK,eAAe;AAAA,IAChD;AAAA,EACF;AAAA,EACA,cAAc,YAAY,iBAAiB;AACzC,UAAM,qBAAqB,CAAC,KAAK,eAAe;AAE9C,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAC1C,YAAI,IAAI,YAAY,WAAW,CAAC,CAAC,IAAI,GAAG;AACtC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,UAAM,oBAAoB,CAAC,KAAK,eAAe;AAC7C,YAAM,MAAM,IAAI,OAAO,IAAI,WAAW,KAAK,CAAC,GAAG;AAC/C,YAAM,QAAQ,IAAI,MAAM,GAAG,EAAE,OAAO,WAAS,KAAK;AAClD,aAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAAA,IAC3B;AACA,QAAI,cAAc,WAAW,UAAU,gBAAgB,UAAU,KAAK,SAAS,aAAa,mBAAmB,YAAY,eAAe,GAAG;AAC3I,YAAM,cAAc,kBAAkB,YAAY,eAAe;AACjE,WAAK,SAAS,KAAK,WAAW;AAAA,IAChC;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,yBAAyB;AAChC,WAAK,wBAAwB,gBAAgB;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,QAAQ;AACN,QAAI,KAAK,yBAAyB;AAChC,WAAK,wBAAwB,MAAM;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,KAAK,yBAAyB;AAChC,WAAK,wBAAwB,KAAK;AAAA,IACpC;AAAA,EACF;AAAA,EACA,WAAW,QAAQ,QAAQ;AACzB,WAAO,OAAO;AAAA,EAChB;AAAA,EACA,aAAa,MAAM;AACjB,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,YAAY;AACtC,WAAK,WAAW,KAAK,IAAI;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,YAAY,YAAY,QAAQ,aAAa;AAC3C,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,OAAO;AACZ,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AACzB,SAAK,aAAa;AAClB,SAAK,gBAAgB,CAAC;AACtB,SAAK,kBAAkB,CAAC;AACxB,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,mBAAmB,CAAC;AACzB,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AACzB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,eAAe;AACjB,WAAK,uBAAuB;AAAA,IAC9B;AACA,QAAI,iBAAiB,eAAe,kBAAkB,mBAAmB;AACvE,YAAM,mBAAmB,KAAK,cAAc,MAAM,GAAG,KAAK,WAAW,EAAE,IAAI,QAAM;AAAA,QAC/E,SAAS,EAAE;AAAA,QACX,SAAS,EAAE;AAAA,QACX,YAAY,EAAE;AAAA,QACd,uBAAuB,KAAK;AAAA,QAC5B,8BAA8B;AAAA,MAChC,EAAE;AACF,UAAI,KAAK,cAAc,SAAS,KAAK,aAAa;AAChD,cAAM,gBAAgB,KAAK,KAAK,cAAc,SAAS,KAAK,WAAW;AACvE,cAAM,sBAAsB,KAAK,cAAc,IAAI,UAAQ,KAAK,OAAO;AACvE,cAAM,eAAe;AAAA,UACnB,SAAS;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,uBAAuB,KAAK;AAAA,UAC5B,8BAA8B,oBAAoB,MAAM,KAAK,WAAW;AAAA,QAC1E;AACA,yBAAiB,KAAK,YAAY;AAAA,MACpC;AACA,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,OAAO,kBAAkB,MAAM;AAClC,gBAAU,KAAK,WAAW,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAElG,YAAI,MAAM,WAAW,KAAK,wBAAwB,aAAa,eAAe;AAC5E,eAAK,wBAAwB,MAAM;AAAA,QACrC;AAAA,MACF,CAAC;AACD,gBAAU,KAAK,WAAW,eAAe,SAAS,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AACpG,YAAI,MAAM,kBAAkB,kBAAkB;AAC5C,gBAAM,aAAa,MAAM,OAAO;AAChC,cAAI,MAAM,YAAY,aAAa,KAAK,SAAS,aAAa,CAAC,cAAc,KAAK,cAAc,SAAS,GAAG;AAC1G,kBAAM,eAAe;AAErB,iBAAK,OAAO,IAAI,MAAM,KAAK,aAAa,KAAK,cAAc,KAAK,cAAc,SAAS,CAAC,CAAC,CAAC;AAAA,UAC5F;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,GAAG;AAC1D,aAAO,KAAK,KAAK,8BAAgC,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAuB,wBAAwB,CAAC,CAAC;AAAA,IAC1K;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,MACrC,WAAW,SAAS,kCAAkC,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,yBAAyB,CAAC;AAAA,QAC3C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,0BAA0B,GAAG;AAAA,QAChF;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,qBAAqB;AAAA,MACpC,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,MAAM;AAAA,QACN,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM;AAAA,QACN,gBAAgB;AAAA,QAChB,mBAAmB;AAAA,QACnB,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,iBAAiB;AAAA,MACnB;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,YAAY;AAAA,MACd;AAAA,MACA,UAAU,CAAC,oBAAoB;AAAA,MAC/B,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,eAAe,GAAG,MAAM,GAAG,CAAC,GAAG,qBAAqB,eAAe,QAAQ,YAAY,SAAS,aAAa,cAAc,aAAa,cAAc,GAAG,CAAC,GAAG,aAAa,YAAY,cAAc,SAAS,yBAAyB,gCAAgC,GAAG,MAAM,GAAG,CAAC,GAAG,aAAa,YAAY,cAAc,SAAS,yBAAyB,8BAA8B,GAAG,CAAC,GAAG,cAAc,SAAS,YAAY,yBAAyB,aAAa,gCAAgC,UAAU,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,GAAG,qBAAqB,eAAe,QAAQ,YAAY,SAAS,aAAa,aAAa,cAAc,cAAc,GAAG,CAAC,GAAG,UAAU,cAAc,SAAS,YAAY,yBAAyB,aAAa,8BAA8B,GAAG,CAAC,GAAG,aAAa,CAAC;AAAA,MACl3B,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,GAAG,CAAC;AAC/B,UAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC;AAC9K,UAAG,sBAAsB;AACzB,UAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,yBAAyB,CAAC;AAAA,QACjH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,YAAY,IAAI,IAAI;AAClC,UAAG,UAAU;AACb,UAAG,WAAW,gBAAgB,SAAS;AACvC,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,iBAAiB;AAAA,QAC7C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,UAAU,yBAAyB,cAAc,uBAAuB,MAAM,iBAAiB,SAAO,4BAA4B;AAAA,MACjJ,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoDV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,UAAU,yBAAyB,cAAc,uBAAuB,MAAM,iBAAiB,SAAO,4BAA4B;AAAA,MAC5I,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,sBAAsB,CAAC,aAAa,SAAS;AACjD,MAAI,QAAQ,KAAK,SAAS;AACxB,WAAO,KAAK,QAAQ,SAAS,EAAE,YAAY,EAAE,QAAQ,YAAY,YAAY,CAAC,IAAI;AAAA,EACpF,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,wBAAwB;AAC9B,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,iBAAiB,SAAY,KAAK,WAAW,YAAY,KAAK;AAAA,EAC5E;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,uBAAuB;AAAA,EACrC;AAAA,EACA,gBAAgB,OAAO;AACrB,WAAO;AAAA,MACL,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,iBAAiB;AACtB,QAAI,KAAK,WAAW,WAAW;AAC7B,UAAI,KAAK,YAAY,WAAW,KAAK,CAAC,KAAK,YAAY,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG;AAClF,aAAK,kBAAkB,CAAC,KAAK,CAAC;AAAA,MAChC;AACA,WAAK,aAAa,KAAK;AAAA,IACzB,OAAO;AACL,YAAM,cAAc,KAAK,YAAY,UAAU,OAAK,KAAK,YAAY,GAAG,KAAK,CAAC;AAC9E,UAAI,gBAAgB,IAAI;AACtB,cAAM,0BAA0B,KAAK,YAAY,OAAO,CAAC,GAAG,MAAM,MAAM,WAAW;AACnF,aAAK,kBAAkB,uBAAuB;AAAA,MAChD,WAAW,KAAK,YAAY,SAAS,KAAK,oBAAoB;AAC5D,cAAM,wBAAwB,CAAC,GAAG,KAAK,aAAa,KAAK;AACzD,aAAK,kBAAkB,qBAAqB;AAAA,MAC9C;AACA,WAAK,MAAM;AACX,UAAI,KAAK,wBAAwB;AAC/B,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa,MAAM;AACjB,UAAM,sBAAsB,KAAK,YAAY,OAAO,OAAK,CAAC,KAAK,YAAY,GAAG,KAAK,OAAO,CAAC;AAC3F,SAAK,kBAAkB,mBAAmB;AAC1C,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,4BAA4B;AAC1B,QAAI,sBAAsB,KAAK,yBAAyB,OAAO,UAAQ,CAAC,KAAK,MAAM,EAAE,OAAO,UAAQ;AAClG,UAAI,CAAC,KAAK,kBAAkB,KAAK,aAAa;AAC5C,eAAO,KAAK,eAAe,KAAK,aAAa,IAAI;AAAA,MACnD,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,QAAI,KAAK,WAAW,UAAU,KAAK,aAAa;AAC9C,YAAM,cAAc,KAAK,yBAAyB,KAAK,UAAQ,KAAK,YAAY,KAAK,WAAW;AAChG,UAAI,CAAC,aAAa;AAChB,cAAM,UAAU,KAAK,gBAAgB,KAAK,WAAW;AACrD,8BAAsB,CAAC,SAAS,GAAG,mBAAmB;AACtD,aAAK,iBAAiB,QAAQ;AAAA,MAChC,OAAO;AACL,aAAK,iBAAiB,YAAY;AAAA,MACpC;AAAA,IACF;AACA,UAAM,gBAAgB,oBAAoB,KAAK,UAAQ,KAAK,YAAY,KAAK,WAAW,KAAK,oBAAoB,KAAK,UAAQ,KAAK,YAAY,KAAK,SAAS,KAAK,cAAc,CAAC,KAAK,oBAAoB,KAAK,UAAQ,KAAK,YAAY,KAAK,SAAS,KAAK,YAAY,CAAC,CAAC,CAAC,KAAK,oBAAoB,CAAC;AACpS,SAAK,iBAAiB,iBAAiB,cAAc,WAAW;AAChE,QAAI,mBAAmB,CAAC;AACxB,QAAI,KAAK,kBAAkB;AACzB,yBAAmB,CAAC,GAAG,IAAI,IAAI,KAAK,UAAU,OAAO,OAAK,EAAE,UAAU,EAAE,IAAI,OAAK,EAAE,UAAU,CAAC,CAAC;AAAA,IACjG,OAAO;AACL,UAAI,KAAK,8BAA8B;AACrC,2BAAmB,KAAK,6BAA6B,IAAI,OAAK,EAAE,OAAO;AAAA,MACzE;AAAA,IACF;AAEA,qBAAiB,QAAQ,WAAS;AAChC,YAAM,QAAQ,oBAAoB,UAAU,UAAQ,UAAU,KAAK,UAAU;AAC7E,UAAI,QAAQ,IAAI;AACd,cAAM,YAAY;AAAA,UAChB,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,KAAK;AAAA,QACP;AACA,4BAAoB,OAAO,OAAO,GAAG,SAAS;AAAA,MAChD;AAAA,IACF,CAAC;AACD,SAAK,sBAAsB,CAAC,GAAG,mBAAmB;AAClD,SAAK,mCAAmC;AAAA,EAC1C;AAAA,EACA,aAAa;AACX,SAAK,4BAA4B,gBAAgB;AAAA,EACnD;AAAA,EACA,kBAAkB,aAAa;AAC7B,UAAM,oBAAoB,CAAC,MAAM,SAAS;AACxC,UAAI,SAAS,WAAW;AACtB,YAAI,KAAK,SAAS,GAAG;AACnB,iBAAO,KAAK,CAAC;AAAA,QACf,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,QAAQ,kBAAkB,aAAa,KAAK,MAAM;AACxD,QAAI,KAAK,UAAU,OAAO;AACxB,WAAK,cAAc;AACnB,WAAK,aAAa,KAAK,WAAW;AAClC,WAAK,QAAQ;AACb,WAAK,SAAS,KAAK,KAAK;AAAA,IAC1B;AACA,SAAK,oBAAoB,KAAK,uBAAuB,YAAY,KAAK,YAAY,WAAW,KAAK;AAAA,EACpG;AAAA,EACA,gBAAgB,aAAa;AAC3B,UAAM,qBAAqB,KAAK,yBAAyB,OAAO,UAAQ,YAAY,UAAU,WAAS,UAAU,KAAK,OAAO,MAAM,EAAE,EAAE,IAAI,UAAQ,KAAK,OAAO,EAAE,OAAO,UAAQ,KAAK,YAAY,UAAU,OAAK,KAAK,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE;AACjP,QAAI,KAAK,WAAW,YAAY;AAC9B,WAAK,kBAAkB,CAAC,GAAG,KAAK,aAAa,GAAG,kBAAkB,CAAC;AAAA,IACrE,WAAW,KAAK,WAAW,QAAQ;AACjC,YAAM,uBAAuB,YAAY,OAAO,WAAS,KAAK,yBAAyB,UAAU,UAAQ,KAAK,YAAY,KAAK,MAAM,EAAE;AACvI,WAAK,kBAAkB,CAAC,GAAG,KAAK,aAAa,GAAG,oBAAoB,GAAG,oBAAoB,CAAC;AAAA,IAC9F;AACA,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,UAAU,GAAG;AACX,QAAI,KAAK,YAAY;AACnB;AAAA,IACF;AACA,UAAM,kCAAkC,KAAK,oBAAoB,OAAO,UAAQ,KAAK,SAAS,MAAM,EAAE,OAAO,UAAQ,CAAC,KAAK,UAAU;AACrI,UAAM,iBAAiB,gCAAgC,UAAU,UAAQ,KAAK,YAAY,KAAK,SAAS,KAAK,cAAc,CAAC;AAC5H,YAAQ,EAAE,SAAS;AAAA,MACjB,KAAK;AACH,UAAE,eAAe;AACjB,YAAI,KAAK,UAAU,gCAAgC,SAAS,GAAG;AAC7D,gBAAM,WAAW,iBAAiB,IAAI,iBAAiB,IAAI,gCAAgC,SAAS;AACpG,eAAK,iBAAiB,gCAAgC,QAAQ,EAAE;AAAA,QAClE;AACA;AAAA,MACF,KAAK;AACH,UAAE,eAAe;AACjB,YAAI,KAAK,UAAU,gCAAgC,SAAS,GAAG;AAC7D,gBAAM,YAAY,iBAAiB,gCAAgC,SAAS,IAAI,iBAAiB,IAAI;AACrG,eAAK,iBAAiB,gCAAgC,SAAS,EAAE;AAAA,QACnE,OAAO;AACL,eAAK,aAAa,IAAI;AAAA,QACxB;AACA;AAAA,MACF,KAAK;AACH,UAAE,eAAe;AACjB,YAAI,KAAK,QAAQ;AACf,cAAI,SAAS,KAAK,cAAc,KAAK,mBAAmB,IAAI;AAC1D,iBAAK,YAAY,KAAK,cAAc;AAAA,UACtC;AAAA,QACF,OAAO;AACL,eAAK,aAAa,IAAI;AAAA,QACxB;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,aAAa,IAAI;AACtB,YAAE,eAAe;AAAA,QACnB;AACA;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe;AACtB,cAAI,KAAK,QAAQ;AACf,cAAE,eAAe;AACjB,gBAAI,SAAS,KAAK,cAAc,GAAG;AACjC,mBAAK,YAAY,KAAK,cAAc;AAAA,YACtC;AAAA,UACF;AAAA,QACF,OAAO;AACL,eAAK,aAAa,KAAK;AAAA,QACzB;AACA;AAAA,MACF,KAAK;AAIH;AAAA,MACF;AACE,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,aAAa,IAAI;AAAA,QACxB;AAAA,IACJ;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,KAAK,WAAW,OAAO;AACzB,WAAK,SAAS;AACd,WAAK,aAAa,KAAK,KAAK;AAC5B,WAAK,aAAa;AAClB,WAAK,IAAI,aAAa;AAAA,IACxB;AAAA,EACF;AAAA,EACA,eAAe;AACb,SAAK,gCAAgC;AACrC,QAAI,KAAK,wBAAwB;AAC/B,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,mBAAmB,OAAO;AACxB,SAAK,cAAc;AACnB,SAAK,0BAA0B;AAC/B,SAAK,WAAW,KAAK,KAAK;AAC1B,SAAK,mCAAmC;AAAA,EAC1C;AAAA,EACA,mBAAmB;AACjB,SAAK,kBAAkB,CAAC,CAAC;AAAA,EAC3B;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,CAAC,KAAK,KAAK,cAAc,SAAS,MAAM,MAAM,GAAG;AACnD,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,QAAQ;AACN,SAAK,4BAA4B,MAAM;AAAA,EACzC;AAAA,EACA,OAAO;AACL,SAAK,4BAA4B,KAAK;AAAA,EACxC;AAAA,EACA,iBAAiB,UAAU;AACzB,UAAM,YAAY,iBAAiB,QAAQ;AAC3C,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,kCAAkC;AAChC,QAAI,KAAK,SAAS,aAAa,KAAK,cAAc,eAAe;AAC/D,YAAM,eAAe,KAAK;AAC1B,kCAA4B,KAAK,SAAS;AAC1C,WAAK,YAAY,aAAa,MAAM;AAGlC,aAAK,eAAe,KAAK,cAAc,cAAc,sBAAsB,EAAE;AAC7E,YAAI,iBAAiB,KAAK,cAAc;AAItC,eAAK,IAAI,cAAc;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,qCAAqC;AACnC,iBAAa,MAAM;AACjB,WAAK,qBAAqB,YAAY,eAAe;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EACA,YAAY,QAAQ,UAAU,iBAAiB,KAAK,MAAM,UAAU,UAAU,cAAc,gBAAgB,aAAa,qBAAqB,uBAAuB;AACnK,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,sBAAsB;AAC3B,SAAK,wBAAwB;AAC7B,SAAK,gBAAgB;AACrB,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,uBAAuB;AAC5B,SAAK,sBAAsB;AAC3B,SAAK,6BAA6B;AAClC,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,yBAAyB;AAC9B,SAAK,oBAAoB,CAAC;AAC1B,SAAK,sBAAsB;AAC3B,SAAK,qBAAqB;AAC1B,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,cAAc,CAAC,IAAI,OAAO,OAAO;AACtC,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,yBAAyB;AAC9B,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,YAAY,CAAC;AAClB,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,eAAe,IAAI,gBAAgB,CAAC,CAAC;AAC1C,SAAK,sBAAsB,IAAI,gBAAgB,CAAC,CAAC;AACjD,SAAK,2BAA2B,CAAC;AACjC,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,YAAY;AACjB,SAAK,yBAAyB;AAC9B,SAAK,WAAW,MAAM;AAAA,IAAC;AACvB,SAAK,YAAY,MAAM;AAAA,IAAC;AACxB,SAAK,mBAAmB;AACxB,SAAK,eAAe;AACpB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,gBAAgB,CAAC;AACtB,SAAK,iBAAiB;AACtB,SAAK,cAAc,CAAC;AACpB,SAAK,UAAU;AACf,SAAK,MAAM;AACX,SAAK,YAAY,CAAC;AAClB,SAAK,oBAAoB;AAEzB,SAAK,YAAY;AACjB,SAAK,YAAY,CAAC;AAClB,SAAK,SAAS;AACd,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,WAAW,YAAY;AAErB,QAAI,KAAK,UAAU,YAAY;AAC7B,WAAK,QAAQ;AACb,YAAM,oBAAoB,CAAC,OAAO,SAAS;AACzC,YAAI,UAAU,QAAQ,UAAU,QAAW;AACzC,iBAAO,CAAC;AAAA,QACV,WAAW,SAAS,WAAW;AAC7B,iBAAO,CAAC,KAAK;AAAA,QACf,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,cAAc,kBAAkB,YAAY,KAAK,MAAM;AAC7D,WAAK,cAAc;AACnB,WAAK,aAAa,KAAK,WAAW;AAClC,WAAK,IAAI,aAAa;AAAA,IACxB;AAAA,EACF;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,SAAK,yBAAyB;AAC9B,QAAI,KAAK,YAAY;AACnB,WAAK,aAAa,KAAK;AAAA,IACzB;AACA,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ;AACV,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,cAAc,KAAK,YAAY;AACjC,WAAK,aAAa,KAAK;AAAA,IACzB;AACA,QAAI,WAAW;AACb,WAAK,mBAAmB;AACxB,YAAM,gBAAgB,KAAK,aAAa,CAAC;AACzC,YAAM,wBAAwB,cAAc,IAAI,UAAQ;AACtD,eAAO;AAAA,UACL,UAAU,KAAK,iBAAiB,cAAc,KAAK,QAAQ;AAAA,UAC3D,SAAS,KAAK,SAAS,KAAK,OAAO,KAAK,KAAK;AAAA,UAC7C,SAAS,OAAO,KAAK,UAAU,YAAY,OAAO,KAAK,UAAU,WAAW,KAAK,QAAQ;AAAA,UACzF,SAAS,KAAK;AAAA,UACd,YAAY,KAAK,YAAY;AAAA,UAC7B,QAAQ,KAAK,QAAQ;AAAA,UACrB,iBAAiB,KAAK,iBAAiB;AAAA,UACvC,YAAY,KAAK,cAAc;AAAA,UAC/B,MAAM;AAAA,UACN,KAAK,KAAK,QAAQ,SAAY,KAAK,QAAQ,KAAK;AAAA,QAClD;AAAA,MACF,CAAC;AACD,WAAK,oBAAoB,KAAK,qBAAqB;AAAA,IACrD;AACA,QAAI,UAAU;AACZ,WAAK,gBAAgB,KAAK,UAAU,KAAK,WAAW;AAAA,IACtD;AACA,QAAI,aAAa;AACf,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,WAAK,mBAAmB;AACxB,YAAM,kBAAkB,CAAC,cAAc,WAAW,eAAe,UAAU;AAC3E,UAAI,gBAAgB,gBAAgB,SAAS,YAAY,GAAG;AAC1D,aAAK,YAAY,CAAC,aAAa,YAAY,CAAC;AAAA,MAC9C,OAAO;AACL,aAAK,YAAY,gBAAgB,IAAI,OAAK,aAAa,CAAC,CAAC;AAAA,MAC3D;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,qBAAqB,kBAAkB,KAAK,qBAAqB,CAAC,KAAK,QAAQ;AAClF,aAAO,IAAI,WAAW,IAAI,UAAU,IAAI,gBAAgB,IAAI;AAAA,IAC9D,CAAC,GAAG,eAAe,KAAK,wBAAwB,KAAK,sBAAsB,eAAe,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,MAC1G;AAAA,MACA;AAAA,IACF,GAAG,QAAQ,OAAO;AAAA,MAChB,QAAQ,WAAW,KAAK;AAAA,MACxB;AAAA,IACF,EAAE,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC;AAAA,MACxC;AAAA,MACA;AAAA,IACF,MAAM;AACJ,WAAK,gBAAgB,QAAQ,WAAW;AAAA,IAC1C,CAAC;AACD,SAAK,aAAa,QAAQ,KAAK,MAAM,IAAI,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACjG,UAAI,CAAC,aAAa;AAChB,aAAK,UAAU;AACf,aAAK,IAAI,aAAa;AACtB,aAAK,OAAO,KAAK;AACjB,gBAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,eAAK,UAAU;AAAA,QACjB,CAAC;AAAA,MACH,OAAO;AACL,aAAK,UAAU;AACf,aAAK,IAAI,aAAa;AACtB,aAAK,QAAQ,KAAK;AAAA,MACpB;AAAA,IACF,CAAC;AACD,kBAAc,CAAC,KAAK,cAAc,KAAK,mBAAmB,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,qBAAqB,kBAAkB,MAAM;AACnJ,YAAM,gBAAgB,oBAAoB,OAAO,MAAM,KAAK,WAAW,MAAM,EAAE,OAAO,WAAS,mBAAmB,UAAU,OAAK,KAAK,YAAY,EAAE,SAAS,KAAK,CAAC,MAAM,EAAE,EAAE,IAAI,WAAS,KAAK,cAAc,KAAK,OAAK,KAAK,YAAY,EAAE,SAAS,KAAK,CAAC,KAAK,KAAK,gBAAgB,KAAK,CAAC;AACzR,WAAK,2BAA2B,CAAC,GAAG,oBAAoB,GAAG,aAAa;AACxE,WAAK,gBAAgB,KAAK,YAAY,IAAI,OAAK,CAAC,GAAG,KAAK,0BAA0B,GAAG,KAAK,aAAa,EAAE,KAAK,UAAQ,KAAK,YAAY,GAAG,KAAK,OAAO,CAAC,CAAC,EAAE,OAAO,UAAQ,CAAC,CAAC,IAAI;AAC/K,WAAK,0BAA0B;AAAA,IACjC,CAAC;AACD,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,gBAAgB,iCAAiC,QAAQ,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC7G,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,OAAO,kBAAkB,MAAM,UAAU,KAAK,KAAK,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC7H,UAAI,KAAK,UAAU,KAAK,gBAAgB,KAAK,YAAY;AACvD;AAAA,MACF;AACA,WAAK,OAAO,IAAI,MAAM,KAAK,aAAa,CAAC,KAAK,MAAM,CAAC;AAAA,IACvD,CAAC,CAAC;AAMF,SAAK,oBAAoB,eAAe,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AACxF,UAAI,MAAM,YAAY,QAAQ;AAC5B,aAAK,aAAa,KAAK;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,kBAAkB;AAC1B,YAAM,KAAK,6BAA6B,SAAS,KAAK,wBAAwB,OAAO,EAAE,KAAK,UAAU,IAAI,GAAG,UAAU,MAAM,MAAM,GAAG,CAAC,KAAK,wBAAwB,SAAS,KAAK,6BAA6B,SAAS,GAAG,KAAK,wBAAwB,IAAI,YAAU,OAAO,OAAO,GAAG,GAAG,KAAK,6BAA6B,IAAI,YAAU,OAAO,OAAO,CAAC,CAAC,EAAE,KAAK,UAAU,IAAI,CAAC,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC5Z,cAAM,wBAAwB,KAAK,wBAAwB,QAAQ,EAAE,IAAI,UAAQ;AAC/E,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,OAAO;AAAA,YACjD,MAAM;AAAA,YACN,KAAK,UAAU,SAAY,UAAU;AAAA,UACvC;AAAA,QACF,CAAC;AACD,aAAK,oBAAoB,KAAK,qBAAqB;AACnD,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,gCAA4B,KAAK,SAAS;AAC1C,SAAK,aAAa,eAAe,KAAK,IAAI;AAAA,EAC5C;AAAA,EACA,gBAAgB,QAAQ,aAAa;AACnC,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,IAAI,aAAa;AAEtB,SAAK,YAAY,oBAAoB,KAAK,WAAW,QAAQ,WAAW;AACxE,WAAO,KAAK,KAAK,SAAS,EAAE,QAAQ,CAAAA,YAAU;AAC5C,UAAI,KAAK,UAAUA,OAAM,GAAG;AAC1B,aAAK,SAAS,SAAS,KAAK,KAAK,eAAeA,OAAM;AAAA,MACxD,OAAO;AACL,aAAK,SAAS,YAAY,KAAK,KAAK,eAAeA,OAAM;AAAA,MAC3D;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,SAAS,OAAO,OAAO;AACrB,QAAI,WAAW;AACf,QAAI,UAAU,QAAW;AACvB,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAC1D,mBAAW,MAAM,SAAS;AAAA,MAC5B;AAAA,IACF,WAAW,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AACjE,iBAAW,MAAM,SAAS;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAqB,MAAM,GAAM,kBAAuB,gBAAgB,GAAM,kBAAuB,eAAe,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,QAAQ,GAAM,kBAAuB,YAAY,GAAM,kBAAqB,gBAAgB,CAAC,GAAM,kBAAuB,wBAAwB,CAAC,GAAM,kBAAqB,qBAAqB,CAAC,GAAM,kBAAqB,uBAAuB,CAAC,CAAC;AAAA,IACriB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,MACzB,gBAAgB,SAAS,iCAAiC,IAAI,KAAK,UAAU;AAC3E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,mBAAmB,CAAC;AAChD,UAAG,eAAe,UAAU,wBAAwB,CAAC;AAAA,QACvD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,0BAA0B;AAC3E,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,+BAA+B;AAAA,QAClF;AAAA,MACF;AAAA,MACA,WAAW,SAAS,wBAAwB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,kBAAkB,GAAG,UAAU;AAC9C,UAAG,YAAY,qBAAqB,CAAC;AACrC,UAAG,YAAY,6BAA6B,CAAC;AAC7C,UAAG,YAAY,wBAAwB,GAAG,UAAU;AACpD,UAAG,YAAY,6BAA6B,GAAG,UAAU;AAAA,QAC3D;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB,GAAG;AAC1E,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,8BAA8B,GAAG;AAClF,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gCAAgC,GAAG;AACpF,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qCAAqC,GAAG;AAAA,QAC3F;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,YAAY;AAAA,MAC3B,UAAU;AAAA,MACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,2BAA2B,CAAC,CAAC,IAAI,mBAAmB,EAAE,iBAAiB,IAAI,WAAW,OAAO,EAAE,iBAAiB,IAAI,WAAW,OAAO,EAAE,yBAAyB,IAAI,WAAW,EAAE,uBAAuB,IAAI,UAAU,EAAE,2BAA2B,IAAI,gBAAgB,IAAI,WAAW,cAAc,CAAC,IAAI,UAAU,EAAE,0BAA0B,IAAI,YAAY,EAAE,yBAAyB,IAAI,YAAY,EAAE,mBAAmB,IAAI,MAAM,EAAE,sBAAsB,IAAI,UAAU,IAAI,OAAO,EAAE,qBAAqB,IAAI,WAAW,SAAS,EAAE,uBAAuB,IAAI,WAAW,SAAS,EAAE,kBAAkB,IAAI,QAAQ,KAAK;AAAA,QACjnB;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,4BAA4B;AAAA,QAC5B,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,aAAa;AAAA,QACb,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,QAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,WAAW;AAAA,QACX,aAAa;AAAA,QACb,wBAAwB;AAAA,QACxB,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,aAAa;AAAA,MACf;AAAA,MACA,SAAS;AAAA,QACP,YAAY;AAAA,QACZ,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,UAAU;AAAA,MACrB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,kBAAkB;AAAA,QAClD,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,kBAAiB;AAAA,QAC/C,OAAO;AAAA,MACT,CAAC,CAAC,GAAM,sBAAyB,mBAAmB;AAAA,MACpD,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,UAAU,kBAAkB,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,oBAAoB,IAAI,GAAG,oBAAoB,YAAY,cAAc,WAAW,QAAQ,QAAQ,YAAY,QAAQ,iBAAiB,qBAAqB,cAAc,eAAe,eAAe,kBAAkB,mBAAmB,cAAc,aAAa,eAAe,GAAG,CAAC,GAAG,aAAa,WAAW,UAAU,cAAc,gBAAgB,sBAAsB,eAAe,oBAAoB,GAAG,MAAM,GAAG,CAAC,GAAG,aAAa,SAAS,GAAG,MAAM,GAAG,CAAC,uBAAuB,IAAI,sBAAsB,IAAI,GAAG,uBAAuB,UAAU,kBAAkB,kCAAkC,+BAA+B,4BAA4B,6BAA6B,wCAAwC,iCAAiC,2BAA2B,8BAA8B,GAAG,CAAC,GAAG,aAAa,WAAW,UAAU,cAAc,gBAAgB,sBAAsB,eAAe,kBAAkB,GAAG,CAAC,GAAG,UAAU,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,SAAS,WAAW,GAAG,CAAC,GAAG,WAAW,aAAa,kBAAkB,WAAW,YAAY,iBAAiB,cAAc,iBAAiB,uBAAuB,wBAAwB,mBAAmB,kBAAkB,uBAAuB,kBAAkB,eAAe,QAAQ,mBAAmB,CAAC;AAAA,MACx1C,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,eAAe,GAAG,yBAAyB,GAAG,CAAC;AAClD,UAAG,WAAW,oBAAoB,SAAS,6EAA6E,QAAQ;AAC9H,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,mBAAmB,MAAM,CAAC;AAAA,UACtD,CAAC,EAAE,YAAY,SAAS,qEAAqE,QAAQ;AACnG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,gBAAgB,MAAM,CAAC;AAAA,UACnD,CAAC,EAAE,cAAc,SAAS,uEAAuE,QAAQ;AACvG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,aAAa,MAAM,CAAC;AAAA,UAChD,CAAC,EAAE,WAAW,SAAS,oEAAoE,QAAQ;AACjG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,UAAU,MAAM,CAAC;AAAA,UAC7C,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,mBAAmB,CAAC,EAAE,GAAG,8CAA8C,GAAG,GAAG,mBAAmB,CAAC,EAAE,GAAG,0CAA0C,GAAG,IAAI,eAAe,CAAC;AAC5O,UAAG,WAAW,uBAAuB,SAAS,sEAAsE,QAAQ;AAC1H,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,eAAe,MAAM,CAAC;AAAA,UAClD,CAAC,EAAE,UAAU,SAAS,2DAA2D;AAC/E,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,aAAa,KAAK,CAAC;AAAA,UAC/C,CAAC,EAAE,kBAAkB,SAAS,iEAAiE,QAAQ;AACrG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,iBAAiB,MAAM,CAAC;AAAA,UACpD,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,YAAe,YAAY,CAAC;AAClC,UAAG,WAAW,QAAQ,IAAI,IAAI,EAAE,QAAQ,IAAI,MAAM,EAAE,YAAY,IAAI,UAAU,EAAE,QAAQ,IAAI,MAAM,EAAE,cAAc,CAAC,EAAE,IAAI,eAAe,OAAO,OAAO,IAAI,YAAY,cAAc,EAAE,iBAAiB,IAAI,eAAe,OAAO,OAAO,IAAI,YAAY,aAAa,EAAE,qBAAqB,IAAI,mBAAmB,EAAE,cAAc,IAAI,YAAY,EAAE,eAAe,IAAI,aAAa,EAAE,eAAe,IAAI,aAAa,EAAE,kBAAkB,IAAI,gBAAgB,EAAE,mBAAmB,IAAI,iBAAiB,EAAE,cAAc,IAAI,YAAY,EAAE,aAAa,IAAI,WAAW,EAAE,iBAAiB,IAAI,aAAa;AAC3kB,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,eAAe,IAAI,eAAe,CAAC,CAAC,IAAI,UAAU,IAAI,gBAAgB;AAChG,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,gBAAgB,CAAC,IAAI,cAAc,IAAI,YAAY,MAAM;AACnF,UAAG,UAAU;AACb,UAAG,WAAW,kCAAkC,IAAI,UAAU,EAAE,+BAA+B,IAAI,6BAA6B,OAAO,IAAI,YAAY,EAAE,4BAA4B,IAAI,6BAA6B,IAAI,eAAe,IAAI,EAAE,6BAA6B,SAAS,EAAE,wCAAwC,sBAAsB,EAAE,iCAAiC,IAAI,mBAAmB,EAAE,2BAA2B,IAAI,MAAM,EAAE,gCAAgC,IAAI,SAAS;AAAA,QACve;AAAA,MACF;AAAA,MACA,cAAc,CAAC,6BAA6B,kBAAkB,wBAAwB,wBAAwB,MAAM,mBAAsB,iCAAiC,wBAAwB,qBAAqB,iBAAoB,6BAA6B,4BAA4B,OAAO;AAAA,MAC5S,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,WAAW;AAAA,MACzB;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,WAAW,CAAC,GAAG,kBAAkB,WAAW,oBAAoB,MAAM;AAClF,WAAW,CAAC,WAAW,CAAC,GAAG,kBAAkB,WAAW,gBAAgB,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,gBAAgB,MAAM;AAChF,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,kBAAkB,WAAW,gBAAgB,MAAM;AAC9F,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,gBAAgB,MAAM;AAChF,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,aAAa,MAAM;AAC7E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,eAAe,MAAM;AAC/E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,0BAA0B,MAAM;AAC1F,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,kBAAkB,MAAM;AAClF,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,UAAU,MAAM;AAC1E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,iBAAiB,MAAM;AACjF,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAAA,CAC3F,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,WAAW,CAAC,kBAAkB;AAAA,QAC5B,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,iBAAiB;AAAA,QAC/C,OAAO;AAAA,MACT,CAAC;AAAA,MACD,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,YAAY,CAAC,WAAW;AAAA,MACxB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuFV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,mCAAmC;AAAA,QACnC,yBAAyB;AAAA,QACzB,yBAAyB;AAAA,QACzB,iCAAiC;AAAA,QACjC,+BAA+B;AAAA,QAC/B,kCAAkC;AAAA,QAClC,kCAAkC;AAAA,QAClC,iCAAiC;AAAA,QACjC,2BAA2B;AAAA,QAC3B,8BAA8B;AAAA,QAC9B,6BAA6B;AAAA,QAC7B,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,MAC5B;AAAA,MACA,SAAS,CAAC,6BAA6B,kBAAkB,wBAAwB,wBAAwB,MAAM,mBAAmB,wBAAwB,qBAAqB,iBAAiB,4BAA4B,OAAO;AAAA,MACnO,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,6BAA6B,CAAC;AAAA,MAC5B,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,QAClC,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,QAC7B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,+BAA+B,CAAC;AAAA,MAC9B,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,QAC7B,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oCAAoC,CAAC;AAAA,MACnC,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,QAClC,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,mBAAmB,mBAAmB,4BAA4B,wBAAwB,uBAAuB,6BAA6B,yBAAyB,uBAAuB,wBAAwB,wBAAwB,8BAA8B,0BAA0B;AAAA,MAChT,SAAS,CAAC,mBAAmB,mBAAmB,wBAAwB,wBAAwB,wBAAwB,uBAAuB,8BAA8B,uBAAuB;AAAA,IACtM,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,mBAAmB,4BAA4B,uBAAuB,6BAA6B,yBAAyB,uBAAuB,wBAAwB,wBAAwB,8BAA8B,0BAA0B;AAAA,IACvQ,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,mBAAmB,mBAAmB,4BAA4B,wBAAwB,uBAAuB,6BAA6B,yBAAyB,uBAAuB,wBAAwB,wBAAwB,8BAA8B,0BAA0B;AAAA,MAChT,SAAS,CAAC,mBAAmB,mBAAmB,wBAAwB,wBAAwB,wBAAwB,uBAAuB,8BAA8B,uBAAuB;AAAA,IACtM,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["status"]}