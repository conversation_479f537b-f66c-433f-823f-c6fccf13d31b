<!-- Add API Form Overlay -->
<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] animate-fadeIn">
  <div class="p-4 pb-6 bg-[var(--background-white)] dark:bg-[var(--background-dark)] z-10 shadow-[var(--box-shadow)] max-h-[70vh] overflow-auto w-full max-w-md">
    <div class="flex items-center justify-between mb-5">
      <div class="flex items-center gap-2">
        <div
          class="w-8 h-8 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center border border-[var(--primary-purple)] border-opacity-20">
          <i class="ri-database-2-line text-white text-lg"></i>
        </div>
        <h3 class="text-base font-medium text-[var(--text-dark)] dark:text-[var(--text-light)]">Add API Provider </h3>
      </div>
      <button (click)="resetForm()"
        class="px-3 p-1 bg-[var(--primary-purple)] rounded-md border-none  hover:bg-[var(--secondary-purple)] text-white transition-[var(--transition-default)]">
        <i class="ri-close-line  text-lg"></i>
      </button>
    </div>

    <div class="space-y-4">
      <div>
        <label class="text-sm font-medium text-[var(--text-dark)] dark:text-[var(--text-light)] mb-1">
          Token URL <span class="text-red-500">*</span>
        </label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <i class="ri-link text-[var(--text-medium-gray)]"></i>
          </div>
          <input type="text" [(ngModel)]="apiData.tokenUrl"
            (input)="isCredentialsValid = false; isTokenUrlValid = false;" [ngClass]="{'border-red-500 focus:ring-red-500': apiData.tokenUrl && !isTokenUrlValid,
                      'border-green-500 focus:ring-green-500': apiData.tokenUrl && isTokenUrlValid}"
            class="w-full h-10 pl-10 pr-4 py-2 text-sm p-3 rounded-md border border-[var(--hover-blue-gray)] focus:border-none focus:outline-[var(--primary-purple)] bg-[var(--background-light-gray)] dark:bg-[var(--background-dark-gray)] outline-none transition-[var(--transition-default)] text-[var(--text-dark)] dark:text-[var(--text-light)]"
            placeholder="Enter token URL">
          <div *ngIf="apiData.tokenUrl && isTokenUrlValid" class="absolute inset-y-0 right-3 flex items-center">
            <i class="ri-check-line text-green-500"></i>
          </div>
        </div>
        <p class="text-xs text-[var(--text-medium-gray)] mt-1">Example: https://api.openai.com</p>
      </div>

      <div>
        <div class="flex items-center justify-between mb-1">
          <label class="text-sm font-medium text-[var(--text-dark)] dark:text-[var(--text-light)]">
            API Key <span class="text-red-500" *ngIf="!noApiMode">*</span>
          </label>
          <div class="flex items-center">
            <button type="button" (click)="noApiMode = !noApiMode; toggleNoApiMode()"
              class="flex items-center px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 border"
              [ngClass]="noApiMode ?
                'bg-[var(--primary-purple)] text-white border-[var(--primary-purple)]' :
                'bg-[var(--hover-blue-gray)] text-[var(--text-medium-gray)] border-[var(--hover-blue-gray)]'">
              <i class="ri-toggle-line mr-1.5 text-base"
                [ngClass]="noApiMode ? 'text-white' : 'text-[var(--text-medium-gray)]'"></i>
              No API Mode
            </button>
          </div>
        </div>
        <div class="relative" *ngIf="!noApiMode">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <i class="ri-key-2-line text-[var(--text-medium-gray)]"></i>
          </div>
          <input type="text" [(ngModel)]="apiData.apiKey" (input)="isCredentialsValid = false"
            class="w-full h-10 pl-10 pr-4 py-2 text-sm  p-3 rounded-md border border-[var(--hover-blue-gray)] focus:border-none focus:outline-[var(--primary-purple)] bg-[var(--background-light-gray)] dark:bg-[var(--background-dark-gray)] outline-none transition-[var(--transition-default)] text-[var(--text-dark)] dark:text-[var(--text-light)]"
            placeholder="Enter API key">
        </div>
        <div *ngIf="noApiMode"
          class="flex items-center h-10 px-4 bg-[var(--primary-purple)] bg-opacity-10 rounded-md border border-[var(--primary-purple)] border-opacity-20 animate-fadeIn">
          <div class="flex items-center justify-center">
            <span class="relative flex h-2.5 w-2.5 mr-3">
              <span
                class="animate-ping absolute inline-flex h-full w-full rounded-full bg-[var(--primary-purple)] opacity-75"></span>
              <span class="relative inline-flex rounded-full h-2.5 w-2.5 bg-[var(--primary-purple)]"></span>
            </span>
          </div>
          <span class="text-sm font-medium text-white">Using "no api" mode</span>
        </div>
        <p class="text-xs text-[var(--text-medium-gray)] mt-1" *ngIf="!noApiMode">Your API key will be securely stored
        </p>
        <p class="text-xs text-[var(--text-medium-gray)] mt-1" *ngIf="noApiMode">
          <i class="ri-information-line text-[var(--primary-purple)] mr-1"></i>
          No API key required - system will use the default "no api" value
        </p>
      </div>

      <!-- Validation Message -->
      <div *ngIf="validationMessage" [ngClass]="{'bg-green-100 border-green-300 text-green-800': isCredentialsValid,
                   'bg-red-100 border-red-300 text-red-800': !isCredentialsValid}"
        class="p-3 rounded-md border animate-fadeIn">
        <div class="flex items-center">
          <i *ngIf="isCredentialsValid" class="ri-checkbox-circle-line text-green-600 mr-2"></i>
          <i *ngIf="!isCredentialsValid" class="ri-error-warning-line text-red-600 mr-2"></i>
          <span>{{ validationMessage }}</span>
        </div>
      </div>

      <div class="mt-4">
        <div class="flex items-center mb-2">
          <input type="checkbox" id="useCustomModels" [(ngModel)]="apiData.hasCustomModels" class="custom-checkbox" />
          <label for="useCustomModels" class="ml-2 text-sm font-medium text-[var(--text-dark)] dark:text-[var(--text-light)]">
            Use Custom Models
          </label>
        </div>
        <p class="text-xs text-[var(--text-medium-gray)] mb-2">
          Instead of automatically extracting models from the API, you can specify custom model names
        </p>

        <div *ngIf="apiData.hasCustomModels" class="mt-2">
          <label class="text-sm font-medium text-[var(--text-dark)] dark:text-[var(--text-light)] mb-1">
            Custom Model Names
          </label>
          <div class="relative">
            <textarea [(ngModel)]="apiData.customModelsText" (input)="updateCustomModels()"
              class="w-full p-3 h-[80px] rounded-md border border-[var(--hover-blue-gray)] focus:border-none focus:outline-[var(--primary-purple)] bg-[var(--background-light-gray)] dark:bg-[var(--background-dark-gray)] outline-none transition-[var(--transition-default)] text-[var(--text-dark)] dark:text-[var(--text-light)]"
              placeholder="Enter model names, one per line"></textarea>
          </div>
          <p class="text-xs text-[var(--text-medium-gray)] mt-1">Enter each model name on a new line</p>
        </div>
      </div>

      <div class="pt-2">
        <button (click)="validateCredentials()" [disabled]="isValidating"
          class="w-full h-10 px-4 py-2 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center gap-2 border-none"
          [ngClass]="{'opacity-70 cursor-not-allowed': isValidating}">
          <i *ngIf="isValidating" class="ri-loader-4-line animate-spin"></i>
          <i *ngIf="!isValidating" class="ri-check-line"></i>
          <span>{{ isValidating ? 'Validating...' : 'Validate Credentials' }}</span>
        </button>
      </div>
    </div>

    <div class="flex justify-end gap-3 mt-8 pt-4 border-t border-[var(--hover-blue-gray)] relative z-10">
      <button (click)="onCancel()"
        class="bg-[var(--hover-blue-gray)] text-[var(--text-dark)] px-4 py-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] transition-[var(--transition-default)] outline-none border-none cursor-pointer hover:shadow-md flex items-center gap-2">
        <i class="ri-close-line"></i>
        <span>Cancel</span>
      </button>

      <button (click)="saveApi()"
        [ngClass]="{'bg-[var(--primary-purple)] hover:bg-opacity-90 hover:scale-[1.02] transform': isCredentialsValid, 'bg-gray-400 cursor-not-allowed': !isCredentialsValid}"
        class="bg-[var(--primary-purple)] text-[var(--background-white)] px-4 py-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] transition-[var(--transition-default)] outline-none border-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 hover:shadow-md"
        [disabled]="!isCredentialsValid">
        <i class="ri-save-line"></i>
        <span>Save</span>
      </button>
    </div>

         <!-- Status Indicator (Conditional) -->
     <div *ngIf="isCredentialsValid"
       class="absolute top-6 right-6 flex items-center gap-2 bg-green-100 dark:bg-green-900 bg-opacity-80 px-3 py-1 rounded-full z-20 animate-fadeIn">
       <span class="relative flex h-2 w-2">
         <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
         <span class="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
       </span>
       <span class="text-xs font-medium text-green-800 dark:text-green-200">Validated</span>
     </div>
   </div>
 </div>
