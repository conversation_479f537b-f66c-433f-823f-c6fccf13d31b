{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-render.mjs", "../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-auto-complete.mjs"], "sourcesContent": ["import { InjectionToken, inject, Injector, afterNextRender } from '@angular/core';\nimport { Observable } from 'rxjs';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * An injection token representing `afterNextRender` as an observable rather\n * than a callback-based API has been added. This might be necessary in code\n * where streams of data are already being used and we need to wait until\n * the change detection ends before performing any tasks.\n */\nconst NZ_AFTER_NEXT_RENDER$ = new InjectionToken('nz-after-next-render', {\n    providedIn: 'root',\n    factory: () => {\n        const injector = inject(Injector);\n        return new Observable(subscriber => {\n            const ref = afterNextRender(() => {\n                subscriber.next();\n                subscriber.complete();\n            }, { injector });\n            return () => ref.destroy();\n        });\n    }\n});\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NZ_AFTER_NEXT_RENDER$ };\n\n", "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, EventEmitter, Optional, Output, forwardRef, Directive, Inject, inject, TemplateRef, Host, ContentChildren, ViewChildren, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { __decorate } from 'tslib';\nimport { Subject, fromEvent, Subscription, defer, merge } from 'rxjs';\nimport { filter, takeUntil, tap, delay, switchMap } from 'rxjs/operators';\nimport { scrollIntoView, InputBoolean } from 'ng-zorro-antd/core/util';\nimport { UP_ARROW, DOWN_ARROW, ESCAPE, TAB, ENTER } from '@angular/cdk/keycodes';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { OverlayConfig, ConnectionPositionPair } from '@angular/cdk/overlay';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { DOCUMENT, NgClass, NgFor, NgStyle, NgTemplateOutlet } from '@angular/common';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i2 from 'ng-zorro-antd/input';\nimport { slideMotion } from 'ng-zorro-antd/core/animation';\nimport * as i2$1 from 'ng-zorro-antd/core/no-animation';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport { NZ_AFTER_NEXT_RENDER$ } from 'ng-zorro-antd/core/render';\nimport * as i1$2 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [[[\"nz-auto-option\"]]];\nconst _c1 = [\"nz-auto-option\"];\nfunction NzAutocompleteOptgroupComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzLabel);\n  }\n}\nconst _c2 = [\"*\"];\nconst _c3 = [\"panel\"];\nconst _c4 = [\"content\"];\nfunction NzAutocompleteComponent_ng_template_0_4_ng_template_0_Template(rf, ctx) {}\nfunction NzAutocompleteComponent_ng_template_0_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzAutocompleteComponent_ng_template_0_4_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction NzAutocompleteComponent_ng_template_0_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzAutocompleteComponent_ng_template_0_ng_template_7_nz_auto_option_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nz-auto-option\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzValue\", option_r3.value)(\"nzLabel\", option_r3.label);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction NzAutocompleteComponent_ng_template_0_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzAutocompleteComponent_ng_template_0_ng_template_7_nz_auto_option_0_Template, 2, 3, \"nz-auto-option\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.normalizedDataSource);\n  }\n}\nfunction NzAutocompleteComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3, 0);\n    i0.ɵɵlistener(\"@slideMotion.done\", function NzAutocompleteComponent_ng_template_0_Template_div_animation_slideMotion_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAnimationEvent($event));\n    });\n    i0.ɵɵelementStart(2, \"div\", 4)(3, \"div\", 5);\n    i0.ɵɵtemplate(4, NzAutocompleteComponent_ng_template_0_4_Template, 1, 0, null, 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, NzAutocompleteComponent_ng_template_0_ng_template_5_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(7, NzAutocompleteComponent_ng_template_0_ng_template_7_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const contentTemplate_r4 = i0.ɵɵreference(6);\n    const optionsTemplate_r5 = i0.ɵɵreference(8);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ant-select-dropdown-hidden\", !ctx_r1.showPanel)(\"ant-select-dropdown-rtl\", ctx_r1.dir === \"rtl\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.nzOverlayClassName)(\"ngStyle\", ctx_r1.nzOverlayStyle)(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation)(\"@slideMotion\", undefined)(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.nzDataSource ? optionsTemplate_r5 : contentTemplate_r4);\n  }\n}\nclass NzAutocompleteOptgroupComponent {\n  constructor() {}\n  static {\n    this.ɵfac = function NzAutocompleteOptgroupComponent_Factory(t) {\n      return new (t || NzAutocompleteOptgroupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzAutocompleteOptgroupComponent,\n      selectors: [[\"nz-auto-optgroup\"]],\n      inputs: {\n        nzLabel: \"nzLabel\"\n      },\n      exportAs: [\"nzAutoOptgroup\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 3,\n      vars: 1,\n      consts: [[1, \"ant-select-item\", \"ant-select-item-group\"], [4, \"nzStringTemplateOutlet\"]],\n      template: function NzAutocompleteOptgroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, NzAutocompleteOptgroupComponent_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzLabel);\n        }\n      },\n      dependencies: [NzOutletModule, i1.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzAutocompleteOptgroupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-auto-optgroup',\n      exportAs: 'nzAutoOptgroup',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      imports: [NzOutletModule],\n      template: `\n    <div class=\"ant-select-item ant-select-item-group\">\n      <ng-container *nzStringTemplateOutlet=\"nzLabel\">{{ nzLabel }}</ng-container>\n    </div>\n    <ng-content select=\"nz-auto-option\"></ng-content>\n  `\n    }]\n  }], () => [], {\n    nzLabel: [{\n      type: Input\n    }]\n  });\n})();\nclass NzOptionSelectionChange {\n  constructor(source, isUserInput = false) {\n    this.source = source;\n    this.isUserInput = isUserInput;\n  }\n}\nclass NzAutocompleteOptionComponent {\n  constructor(ngZone, changeDetectorRef, element, nzAutocompleteOptgroupComponent) {\n    this.ngZone = ngZone;\n    this.changeDetectorRef = changeDetectorRef;\n    this.element = element;\n    this.nzAutocompleteOptgroupComponent = nzAutocompleteOptgroupComponent;\n    this.nzDisabled = false;\n    this.selectionChange = new EventEmitter();\n    this.mouseEntered = new EventEmitter();\n    this.active = false;\n    this.selected = false;\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.element.nativeElement, 'mouseenter').pipe(filter(() => this.mouseEntered.observers.length > 0), takeUntil(this.destroy$)).subscribe(() => {\n        this.ngZone.run(() => this.mouseEntered.emit(this));\n      });\n      fromEvent(this.element.nativeElement, 'mousedown').pipe(takeUntil(this.destroy$)).subscribe(event => event.preventDefault());\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n  }\n  select(emit = true) {\n    this.selected = true;\n    this.changeDetectorRef.markForCheck();\n    if (emit) {\n      this.emitSelectionChangeEvent();\n    }\n  }\n  deselect() {\n    this.selected = false;\n    this.changeDetectorRef.markForCheck();\n    this.emitSelectionChangeEvent();\n  }\n  /** Git display label */\n  getLabel() {\n    return this.nzLabel || this.nzValue.toString();\n  }\n  /** Set active (only styles) */\n  setActiveStyles() {\n    if (!this.active) {\n      this.active = true;\n      this.changeDetectorRef.markForCheck();\n    }\n  }\n  /** Unset active (only styles) */\n  setInactiveStyles() {\n    if (this.active) {\n      this.active = false;\n      this.changeDetectorRef.markForCheck();\n    }\n  }\n  scrollIntoViewIfNeeded() {\n    scrollIntoView(this.element.nativeElement);\n  }\n  selectViaInteraction() {\n    if (!this.nzDisabled) {\n      this.selected = !this.selected;\n      if (this.selected) {\n        this.setActiveStyles();\n      } else {\n        this.setInactiveStyles();\n      }\n      this.emitSelectionChangeEvent(true);\n      this.changeDetectorRef.markForCheck();\n    }\n  }\n  emitSelectionChangeEvent(isUserInput = false) {\n    this.selectionChange.emit(new NzOptionSelectionChange(this, isUserInput));\n  }\n  static {\n    this.ɵfac = function NzAutocompleteOptionComponent_Factory(t) {\n      return new (t || NzAutocompleteOptionComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NzAutocompleteOptgroupComponent, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzAutocompleteOptionComponent,\n      selectors: [[\"nz-auto-option\"]],\n      hostAttrs: [\"role\", \"menuitem\", 1, \"ant-select-item\", \"ant-select-item-option\"],\n      hostVars: 10,\n      hostBindings: function NzAutocompleteOptionComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function NzAutocompleteOptionComponent_click_HostBindingHandler() {\n            return ctx.selectViaInteraction();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-selected\", ctx.selected.toString())(\"aria-disabled\", ctx.nzDisabled.toString());\n          i0.ɵɵclassProp(\"ant-select-item-option-grouped\", ctx.nzAutocompleteOptgroupComponent)(\"ant-select-item-option-selected\", ctx.selected)(\"ant-select-item-option-active\", ctx.active)(\"ant-select-item-option-disabled\", ctx.nzDisabled);\n        }\n      },\n      inputs: {\n        nzValue: \"nzValue\",\n        nzLabel: \"nzLabel\",\n        nzDisabled: \"nzDisabled\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\",\n        mouseEntered: \"mouseEntered\"\n      },\n      exportAs: [\"nzAutoOption\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"ant-select-item-option-content\"]],\n      template: function NzAutocompleteOptionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzAutocompleteOptionComponent.prototype, \"nzDisabled\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzAutocompleteOptionComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-auto-option',\n      exportAs: 'nzAutoOption',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      template: `\n    <div class=\"ant-select-item-option-content\">\n      <ng-content></ng-content>\n    </div>\n  `,\n      host: {\n        role: 'menuitem',\n        class: 'ant-select-item ant-select-item-option',\n        '[class.ant-select-item-option-grouped]': 'nzAutocompleteOptgroupComponent',\n        '[class.ant-select-item-option-selected]': 'selected',\n        '[class.ant-select-item-option-active]': 'active',\n        '[class.ant-select-item-option-disabled]': 'nzDisabled',\n        '[attr.aria-selected]': 'selected.toString()',\n        '[attr.aria-disabled]': 'nzDisabled.toString()',\n        '(click)': 'selectViaInteraction()'\n      }\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: NzAutocompleteOptgroupComponent,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzValue: [{\n      type: Input\n    }],\n    nzLabel: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    mouseEntered: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst NZ_AUTOCOMPLETE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => NzAutocompleteTriggerDirective),\n  multi: true\n};\nfunction getNzAutocompleteMissingPanelError() {\n  return Error('Attempting to open an undefined instance of `nz-autocomplete`. ' + 'Make sure that the id passed to the `nzAutocomplete` is correct and that ' + \"you're attempting to open it after the ngAfterContentInit hook.\");\n}\nclass NzAutocompleteTriggerDirective {\n  /** Current active option */\n  get activeOption() {\n    if (this.nzAutocomplete && this.nzAutocomplete.options.length) {\n      return this.nzAutocomplete.activeItem;\n    } else {\n      return null;\n    }\n  }\n  constructor(ngZone, elementRef, overlay, viewContainerRef, nzInputGroupWhitSuffixOrPrefixDirective, document) {\n    this.ngZone = ngZone;\n    this.elementRef = elementRef;\n    this.overlay = overlay;\n    this.viewContainerRef = viewContainerRef;\n    this.nzInputGroupWhitSuffixOrPrefixDirective = nzInputGroupWhitSuffixOrPrefixDirective;\n    this.document = document;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.panelOpen = false;\n    this.destroy$ = new Subject();\n    this.overlayRef = null;\n    this.portal = null;\n    this.previousValue = null;\n  }\n  ngAfterViewInit() {\n    if (this.nzAutocomplete) {\n      this.nzAutocomplete.animationStateChange.pipe(takeUntil(this.destroy$)).subscribe(event => {\n        if (event.toState === 'void') {\n          if (this.overlayRef) {\n            this.overlayRef.dispose();\n            this.overlayRef = null;\n          }\n        }\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.destroyPanel();\n  }\n  writeValue(value) {\n    this.ngZone.runOutsideAngular(() => Promise.resolve(null).then(() => this.setTriggerValue(value)));\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    const element = this.elementRef.nativeElement;\n    element.disabled = isDisabled;\n    this.closePanel();\n  }\n  openPanel() {\n    this.previousValue = this.elementRef.nativeElement.value;\n    this.attachOverlay();\n    this.updateStatus();\n  }\n  closePanel() {\n    if (this.panelOpen) {\n      this.nzAutocomplete.isOpen = this.panelOpen = false;\n      if (this.overlayRef && this.overlayRef.hasAttached()) {\n        this.overlayRef.detach();\n        this.selectionChangeSubscription.unsubscribe();\n        this.overlayOutsideClickSubscription.unsubscribe();\n        this.optionsChangeSubscription.unsubscribe();\n        this.portal = null;\n      }\n    }\n  }\n  handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n    if (keyCode === ESCAPE) {\n      event.preventDefault();\n    }\n    if (this.panelOpen && (keyCode === ESCAPE || keyCode === TAB)) {\n      // Reset value when tab / ESC close\n      if (this.activeOption && this.activeOption.getLabel() !== this.previousValue) {\n        this.setTriggerValue(this.previousValue);\n      }\n      this.closePanel();\n    } else if (this.panelOpen && keyCode === ENTER) {\n      if (this.nzAutocomplete.showPanel) {\n        event.preventDefault();\n        if (this.activeOption) {\n          this.activeOption.selectViaInteraction();\n        } else {\n          this.closePanel();\n        }\n      }\n    } else if (this.panelOpen && isArrowKey && this.nzAutocomplete.showPanel) {\n      event.stopPropagation();\n      event.preventDefault();\n      if (keyCode === UP_ARROW) {\n        this.nzAutocomplete.setPreviousItemActive();\n      } else {\n        this.nzAutocomplete.setNextItemActive();\n      }\n      if (this.activeOption) {\n        this.activeOption.scrollIntoViewIfNeeded();\n      }\n      this.doBackfill();\n    }\n  }\n  handleInput(event) {\n    const target = event.target;\n    const document = this.document;\n    let value = target.value;\n    if (target.type === 'number') {\n      value = value === '' ? null : parseFloat(value);\n    }\n    if (this.previousValue !== value) {\n      this.previousValue = value;\n      this.onChange(value);\n      if (this.canOpen() && document.activeElement === event.target) {\n        this.openPanel();\n      }\n    }\n  }\n  handleFocus() {\n    if (this.canOpen()) {\n      this.openPanel();\n    }\n  }\n  handleBlur() {\n    this.onTouched();\n  }\n  /**\n   * Subscription data source changes event\n   */\n  subscribeOptionsChange() {\n    const optionChanges = this.nzAutocomplete.options.changes.pipe(tap(() => this.positionStrategy.reapplyLastPosition()), delay(0));\n    return optionChanges.subscribe(() => {\n      this.resetActiveItem();\n      if (this.panelOpen) {\n        this.overlayRef.updatePosition();\n      }\n    });\n  }\n  /**\n   * Subscription option changes event and set the value\n   */\n  subscribeSelectionChange() {\n    return this.nzAutocomplete.selectionChange.subscribe(option => {\n      this.setValueAndClose(option);\n    });\n  }\n  subscribeOverlayOutsideClick() {\n    return this.overlayRef.outsidePointerEvents().pipe(filter(e => !this.elementRef.nativeElement.contains(e.target))).subscribe(() => {\n      this.closePanel();\n    });\n  }\n  attachOverlay() {\n    if (!this.nzAutocomplete) {\n      throw getNzAutocompleteMissingPanelError();\n    }\n    if (!this.portal && this.nzAutocomplete.template) {\n      this.portal = new TemplatePortal(this.nzAutocomplete.template, this.viewContainerRef);\n    }\n    if (!this.overlayRef) {\n      this.overlayRef = this.overlay.create(this.getOverlayConfig());\n    }\n    if (this.overlayRef && !this.overlayRef.hasAttached()) {\n      this.overlayRef.attach(this.portal);\n      this.selectionChangeSubscription = this.subscribeSelectionChange();\n      this.optionsChangeSubscription = this.subscribeOptionsChange();\n      this.overlayOutsideClickSubscription = this.subscribeOverlayOutsideClick();\n      this.overlayRef.detachments().pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.closePanel();\n      });\n    }\n    this.nzAutocomplete.isOpen = this.panelOpen = true;\n  }\n  updateStatus() {\n    if (this.overlayRef) {\n      this.overlayRef.updateSize({\n        width: this.nzAutocomplete.nzWidth || this.getHostWidth()\n      });\n    }\n    this.nzAutocomplete.setVisibility();\n    this.resetActiveItem();\n    if (this.activeOption) {\n      this.activeOption.scrollIntoViewIfNeeded();\n    }\n  }\n  destroyPanel() {\n    if (this.overlayRef) {\n      this.closePanel();\n    }\n  }\n  getOverlayConfig() {\n    return new OverlayConfig({\n      positionStrategy: this.getOverlayPosition(),\n      disposeOnNavigation: true,\n      scrollStrategy: this.overlay.scrollStrategies.reposition(),\n      // default host element width\n      width: this.nzAutocomplete.nzWidth || this.getHostWidth()\n    });\n  }\n  getConnectedElement() {\n    return this.nzInputGroupWhitSuffixOrPrefixDirective ? this.nzInputGroupWhitSuffixOrPrefixDirective.elementRef : this.elementRef;\n  }\n  getHostWidth() {\n    return this.getConnectedElement().nativeElement.getBoundingClientRect().width;\n  }\n  getOverlayPosition() {\n    const positions = [new ConnectionPositionPair({\n      originX: 'start',\n      originY: 'bottom'\n    }, {\n      overlayX: 'start',\n      overlayY: 'top'\n    }), new ConnectionPositionPair({\n      originX: 'start',\n      originY: 'top'\n    }, {\n      overlayX: 'start',\n      overlayY: 'bottom'\n    })];\n    this.positionStrategy = this.overlay.position().flexibleConnectedTo(this.getConnectedElement()).withFlexibleDimensions(false).withPush(false).withPositions(positions).withTransformOriginOn('.ant-select-dropdown');\n    return this.positionStrategy;\n  }\n  resetActiveItem() {\n    const index = this.nzAutocomplete.getOptionIndex(this.previousValue);\n    this.nzAutocomplete.clearSelectedOptions(null, true);\n    if (index !== -1) {\n      this.nzAutocomplete.setActiveItem(index);\n      this.nzAutocomplete.activeItem.select(false);\n    } else {\n      this.nzAutocomplete.setActiveItem(this.nzAutocomplete.nzDefaultActiveFirstOption ? 0 : -1);\n    }\n  }\n  setValueAndClose(option) {\n    const value = option.nzValue;\n    this.setTriggerValue(option.getLabel());\n    this.onChange(value);\n    this.elementRef.nativeElement.focus();\n    this.closePanel();\n  }\n  setTriggerValue(value) {\n    const option = this.nzAutocomplete.getOption(value);\n    const displayValue = option ? option.getLabel() : value;\n    this.elementRef.nativeElement.value = displayValue != null ? displayValue : '';\n    if (!this.nzAutocomplete.nzBackfill) {\n      this.previousValue = displayValue;\n    }\n  }\n  doBackfill() {\n    if (this.nzAutocomplete.nzBackfill && this.nzAutocomplete.activeItem) {\n      this.setTriggerValue(this.nzAutocomplete.activeItem.getLabel());\n    }\n  }\n  canOpen() {\n    const element = this.elementRef.nativeElement;\n    return !element.readOnly && !element.disabled;\n  }\n  static {\n    this.ɵfac = function NzAutocompleteTriggerDirective_Factory(t) {\n      return new (t || NzAutocompleteTriggerDirective)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$1.Overlay), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i2.NzInputGroupWhitSuffixOrPrefixDirective, 8), i0.ɵɵdirectiveInject(DOCUMENT, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzAutocompleteTriggerDirective,\n      selectors: [[\"input\", \"nzAutocomplete\", \"\"], [\"textarea\", \"nzAutocomplete\", \"\"]],\n      hostAttrs: [\"autocomplete\", \"off\", \"aria-autocomplete\", \"list\"],\n      hostBindings: function NzAutocompleteTriggerDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focusin\", function NzAutocompleteTriggerDirective_focusin_HostBindingHandler() {\n            return ctx.handleFocus();\n          })(\"blur\", function NzAutocompleteTriggerDirective_blur_HostBindingHandler() {\n            return ctx.handleBlur();\n          })(\"input\", function NzAutocompleteTriggerDirective_input_HostBindingHandler($event) {\n            return ctx.handleInput($event);\n          })(\"keydown\", function NzAutocompleteTriggerDirective_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          });\n        }\n      },\n      inputs: {\n        nzAutocomplete: \"nzAutocomplete\"\n      },\n      exportAs: [\"nzAutocompleteTrigger\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NZ_AUTOCOMPLETE_VALUE_ACCESSOR])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzAutocompleteTriggerDirective, [{\n    type: Directive,\n    args: [{\n      selector: `input[nzAutocomplete], textarea[nzAutocomplete]`,\n      exportAs: 'nzAutocompleteTrigger',\n      providers: [NZ_AUTOCOMPLETE_VALUE_ACCESSOR],\n      standalone: true,\n      host: {\n        autocomplete: 'off',\n        'aria-autocomplete': 'list',\n        '(focusin)': 'handleFocus()',\n        '(blur)': 'handleBlur()',\n        '(input)': 'handleInput($event)',\n        '(keydown)': 'handleKeydown($event)'\n      }\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1$1.Overlay\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i2.NzInputGroupWhitSuffixOrPrefixDirective,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    nzAutocomplete: [{\n      type: Input\n    }]\n  });\n})();\nfunction normalizeDataSource(value) {\n  return value?.map(item => {\n    if (typeof item === 'number' || typeof item === 'string') {\n      return {\n        label: item.toString(),\n        value: item.toString()\n      };\n    }\n    return item;\n  });\n}\nclass NzAutocompleteComponent {\n  /**\n   * Options accessor, its source may be content or dataSource\n   */\n  get options() {\n    // first dataSource\n    if (this.nzDataSource) {\n      return this.fromDataSourceOptions;\n    } else {\n      return this.fromContentOptions;\n    }\n  }\n  constructor(changeDetectorRef, directionality, noAnimation) {\n    this.changeDetectorRef = changeDetectorRef;\n    this.directionality = directionality;\n    this.noAnimation = noAnimation;\n    this.nzOverlayClassName = '';\n    this.nzOverlayStyle = {};\n    this.nzDefaultActiveFirstOption = true;\n    this.nzBackfill = false;\n    this.compareWith = (o1, o2) => o1 === o2;\n    this.selectionChange = new EventEmitter();\n    this.showPanel = true;\n    this.isOpen = false;\n    this.activeItem = null;\n    this.dir = 'ltr';\n    this.normalizedDataSource = [];\n    this.destroy$ = new Subject();\n    this.animationStateChange = new EventEmitter();\n    this.activeItemIndex = -1;\n    this.selectionChangeSubscription = Subscription.EMPTY;\n    this.optionMouseEnterSubscription = Subscription.EMPTY;\n    this.dataSourceChangeSubscription = Subscription.EMPTY;\n    /** Options changes listener */\n    this.optionSelectionChanges = defer(() => {\n      if (this.options) {\n        return merge(...this.options.map(option => option.selectionChange));\n      }\n      return this.afterNextRender$.pipe(switchMap(() => this.optionSelectionChanges));\n    });\n    this.optionMouseEnter = defer(() => {\n      if (this.options) {\n        return merge(...this.options.map(option => option.mouseEntered));\n      }\n      return this.afterNextRender$.pipe(switchMap(() => this.optionMouseEnter));\n    });\n    this.afterNextRender$ = inject(NZ_AFTER_NEXT_RENDER$);\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.changeDetectorRef.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzDataSource\n    } = changes;\n    if (nzDataSource) {\n      this.normalizedDataSource = normalizeDataSource(nzDataSource.currentValue);\n    }\n  }\n  onAnimationEvent(event) {\n    this.animationStateChange.emit(event);\n  }\n  ngAfterContentInit() {\n    if (!this.nzDataSource) {\n      this.optionsInit();\n    }\n  }\n  ngAfterViewInit() {\n    if (this.nzDataSource) {\n      this.optionsInit();\n    }\n  }\n  ngOnDestroy() {\n    this.dataSourceChangeSubscription.unsubscribe();\n    this.selectionChangeSubscription.unsubscribe();\n    this.optionMouseEnterSubscription.unsubscribe();\n    // Caretaker note: we have to set these subscriptions to `null` since these will be closed subscriptions, but they\n    // still keep references to destinations (which are `SafeSubscriber`s). Destinations keep referencing `next` functions,\n    // which we pass, for instance, to `this.optionSelectionChanges.subscribe(...)`.\n    this.dataSourceChangeSubscription = this.selectionChangeSubscription = this.optionMouseEnterSubscription = null;\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setVisibility() {\n    this.showPanel = !!this.options.length;\n    this.changeDetectorRef.markForCheck();\n  }\n  setActiveItem(index) {\n    const activeItem = this.options.get(index);\n    if (activeItem && !activeItem.active) {\n      this.activeItem = activeItem;\n      this.activeItemIndex = index;\n      this.clearSelectedOptions(this.activeItem);\n      this.activeItem.setActiveStyles();\n    } else {\n      this.activeItem = null;\n      this.activeItemIndex = -1;\n      this.clearSelectedOptions();\n    }\n    this.changeDetectorRef.markForCheck();\n  }\n  setNextItemActive() {\n    const nextIndex = this.activeItemIndex + 1 <= this.options.length - 1 ? this.activeItemIndex + 1 : 0;\n    this.setActiveItem(nextIndex);\n  }\n  setPreviousItemActive() {\n    const previousIndex = this.activeItemIndex - 1 < 0 ? this.options.length - 1 : this.activeItemIndex - 1;\n    this.setActiveItem(previousIndex);\n  }\n  getOptionIndex(value) {\n    return this.options.reduce((result, current, index) => result === -1 ? this.compareWith(value, current.nzValue) ? index : -1 : result, -1);\n  }\n  getOption(value) {\n    return this.options.find(item => this.compareWith(value, item.nzValue)) || null;\n  }\n  optionsInit() {\n    this.setVisibility();\n    this.subscribeOptionChanges();\n    const changes = this.nzDataSource ? this.fromDataSourceOptions.changes : this.fromContentOptions.changes;\n    // async\n    this.dataSourceChangeSubscription = changes.subscribe(e => {\n      if (!e.dirty && this.isOpen) {\n        setTimeout(() => this.setVisibility());\n      }\n      this.subscribeOptionChanges();\n    });\n  }\n  /**\n   * Clear the status of options\n   */\n  clearSelectedOptions(skip, deselect = false) {\n    this.options.forEach(option => {\n      if (option !== skip) {\n        if (deselect) {\n          option.deselect();\n        }\n        option.setInactiveStyles();\n      }\n    });\n  }\n  subscribeOptionChanges() {\n    this.selectionChangeSubscription.unsubscribe();\n    this.selectionChangeSubscription = this.optionSelectionChanges.pipe(filter(event => event.isUserInput)).subscribe(event => {\n      event.source.select();\n      event.source.setActiveStyles();\n      this.activeItem = event.source;\n      this.activeItemIndex = this.getOptionIndex(this.activeItem.nzValue);\n      this.clearSelectedOptions(event.source, true);\n      this.selectionChange.emit(event.source);\n    });\n    this.optionMouseEnterSubscription.unsubscribe();\n    this.optionMouseEnterSubscription = this.optionMouseEnter.subscribe(event => {\n      event.setActiveStyles();\n      this.activeItem = event;\n      this.activeItemIndex = this.getOptionIndex(this.activeItem.nzValue);\n      this.clearSelectedOptions(event);\n    });\n  }\n  static {\n    this.ɵfac = function NzAutocompleteComponent_Factory(t) {\n      return new (t || NzAutocompleteComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$2.Directionality, 8), i0.ɵɵdirectiveInject(i2$1.NzNoAnimationDirective, 9));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzAutocompleteComponent,\n      selectors: [[\"nz-autocomplete\"]],\n      contentQueries: function NzAutocompleteComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzAutocompleteOptionComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fromContentOptions = _t);\n        }\n      },\n      viewQuery: function NzAutocompleteComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(NzAutocompleteOptionComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fromDataSourceOptions = _t);\n        }\n      },\n      inputs: {\n        nzWidth: \"nzWidth\",\n        nzOverlayClassName: \"nzOverlayClassName\",\n        nzOverlayStyle: \"nzOverlayStyle\",\n        nzDefaultActiveFirstOption: \"nzDefaultActiveFirstOption\",\n        nzBackfill: \"nzBackfill\",\n        compareWith: \"compareWith\",\n        nzDataSource: \"nzDataSource\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\"\n      },\n      exportAs: [\"nzAutocomplete\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 1,\n      vars: 0,\n      consts: [[\"panel\", \"\"], [\"contentTemplate\", \"\"], [\"optionsTemplate\", \"\"], [1, \"ant-select-dropdown\", \"ant-select-dropdown-placement-bottomLeft\", 3, \"ngClass\", \"ngStyle\", \"nzNoAnimation\"], [2, \"max-height\", \"256px\", \"overflow-y\", \"auto\", \"overflow-anchor\", \"none\"], [2, \"display\", \"flex\", \"flex-direction\", \"column\"], [4, \"ngTemplateOutlet\"], [3, \"nzValue\", \"nzLabel\", 4, \"ngFor\", \"ngForOf\"], [3, \"nzValue\", \"nzLabel\"]],\n      template: function NzAutocompleteComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzAutocompleteComponent_ng_template_0_Template, 9, 10, \"ng-template\");\n        }\n      },\n      dependencies: [NgClass, NgFor, NgStyle, NgTemplateOutlet, NzAutocompleteOptionComponent, NzNoAnimationDirective],\n      encapsulation: 2,\n      data: {\n        animation: [slideMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzAutocompleteComponent.prototype, \"nzDefaultActiveFirstOption\", void 0);\n__decorate([InputBoolean()], NzAutocompleteComponent.prototype, \"nzBackfill\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzAutocompleteComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-autocomplete',\n      exportAs: 'nzAutocomplete',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      imports: [NgClass, NgFor, NgStyle, NgTemplateOutlet, NzAutocompleteOptionComponent, NzNoAnimationDirective],\n      template: `\n    <ng-template>\n      <div\n        #panel\n        class=\"ant-select-dropdown ant-select-dropdown-placement-bottomLeft\"\n        [class.ant-select-dropdown-hidden]=\"!showPanel\"\n        [class.ant-select-dropdown-rtl]=\"dir === 'rtl'\"\n        [ngClass]=\"nzOverlayClassName\"\n        [ngStyle]=\"nzOverlayStyle\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        @slideMotion\n        (@slideMotion.done)=\"onAnimationEvent($event)\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n      >\n        <div style=\"max-height: 256px; overflow-y: auto; overflow-anchor: none;\">\n          <div style=\"display: flex; flex-direction: column;\">\n            <ng-template *ngTemplateOutlet=\"nzDataSource ? optionsTemplate : contentTemplate\"></ng-template>\n          </div>\n        </div>\n      </div>\n      <ng-template #contentTemplate>\n        <ng-content></ng-content>\n      </ng-template>\n      <ng-template #optionsTemplate>\n        <nz-auto-option *ngFor=\"let option of normalizedDataSource\" [nzValue]=\"option.value\" [nzLabel]=\"option.label\">\n          {{ option.label }}\n        </nz-auto-option>\n      </ng-template>\n    </ng-template>\n  `,\n      animations: [slideMotion]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1$2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2$1.NzNoAnimationDirective,\n    decorators: [{\n      type: Host\n    }, {\n      type: Optional\n    }]\n  }], {\n    nzWidth: [{\n      type: Input\n    }],\n    nzOverlayClassName: [{\n      type: Input\n    }],\n    nzOverlayStyle: [{\n      type: Input\n    }],\n    nzDefaultActiveFirstOption: [{\n      type: Input\n    }],\n    nzBackfill: [{\n      type: Input\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    nzDataSource: [{\n      type: Input\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    fromContentOptions: [{\n      type: ContentChildren,\n      args: [NzAutocompleteOptionComponent, {\n        descendants: true\n      }]\n    }],\n    fromDataSourceOptions: [{\n      type: ViewChildren,\n      args: [NzAutocompleteOptionComponent]\n    }],\n    template: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: false\n      }]\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel', {\n        static: false\n      }]\n    }],\n    content: [{\n      type: ViewChild,\n      args: ['content', {\n        static: false\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzAutocompleteModule {\n  static {\n    this.ɵfac = function NzAutocompleteModule_Factory(t) {\n      return new (t || NzAutocompleteModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzAutocompleteModule,\n      imports: [NzAutocompleteComponent, NzAutocompleteOptionComponent, NzAutocompleteTriggerDirective, NzAutocompleteOptgroupComponent],\n      exports: [NzAutocompleteComponent, NzAutocompleteOptionComponent, NzAutocompleteTriggerDirective, NzAutocompleteOptgroupComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzAutocompleteOptgroupComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzAutocompleteModule, [{\n    type: NgModule,\n    args: [{\n      exports: [NzAutocompleteComponent, NzAutocompleteOptionComponent, NzAutocompleteTriggerDirective, NzAutocompleteOptgroupComponent],\n      imports: [NzAutocompleteComponent, NzAutocompleteOptionComponent, NzAutocompleteTriggerDirective, NzAutocompleteOptgroupComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NZ_AUTOCOMPLETE_VALUE_ACCESSOR, NzAutocompleteComponent, NzAutocompleteModule, NzAutocompleteOptgroupComponent, NzAutocompleteOptionComponent, NzAutocompleteTriggerDirective, NzOptionSelectionChange, getNzAutocompleteMissingPanelError };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,IAAM,wBAAwB,IAAI,eAAe,wBAAwB;AAAA,EACrE,YAAY;AAAA,EACZ,SAAS,MAAM;AACX,UAAM,WAAW,OAAO,QAAQ;AAChC,WAAO,IAAI,WAAW,gBAAc;AAChC,YAAM,MAAM,gBAAgB,MAAM;AAC9B,mBAAW,KAAK;AAChB,mBAAW,SAAS;AAAA,MACxB,GAAG,EAAE,SAAS,CAAC;AACf,aAAO,MAAM,IAAI,QAAQ;AAAA,IAC7B,CAAC;AAAA,EACL;AACJ,CAAC;;;ACAD,IAAM,MAAM,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACjC,IAAM,MAAM,CAAC,gBAAgB;AAC7B,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,SAAS;AACtB,SAAS,+DAA+D,IAAI,KAAK;AAAC;AAClF,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gEAAgE,GAAG,GAAG,aAAa;AAAA,EACtG;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,kBAAkB,CAAC;AACxC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,WAAW,WAAW,UAAU,KAAK,EAAE,WAAW,UAAU,KAAK;AACpE,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,UAAU,OAAO,GAAG;AAAA,EACjD;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,+EAA+E,GAAG,GAAG,kBAAkB,CAAC;AAAA,EAC3H;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,oBAAoB;AAAA,EACtD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,WAAW,qBAAqB,SAAS,yFAAyF,QAAQ;AAC3I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,IAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,MAAM,CAAC;AAChF,IAAG,aAAa,EAAE,EAAE;AACpB,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,8DAA8D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,EAClQ;AACA,MAAI,KAAK,GAAG;AACV,UAAM,qBAAwB,YAAY,CAAC;AAC3C,UAAM,qBAAwB,YAAY,CAAC;AAC3C,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,8BAA8B,CAAC,OAAO,SAAS,EAAE,2BAA2B,OAAO,QAAQ,KAAK;AAC/G,IAAG,WAAW,WAAW,OAAO,kBAAkB,EAAE,WAAW,OAAO,cAAc,EAAE,iBAAiB,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,aAAa,EAAE,gBAAgB,MAAS,EAAE,cAAc,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,cAAc;AAC9R,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,eAAe,qBAAqB,kBAAkB;AAAA,EACjG;AACF;AACA,IAAM,kCAAN,MAAM,iCAAgC;AAAA,EACpC,cAAc;AAAA,EAAC;AAAA,EACf,OAAO;AACL,SAAK,OAAO,SAAS,wCAAwC,GAAG;AAC9D,aAAO,KAAK,KAAK,kCAAiC;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,QAAQ;AAAA,QACN,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,gBAAgB;AAAA,MAC3B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,mBAAmB,uBAAuB,GAAG,CAAC,GAAG,wBAAwB,CAAC;AAAA,MACvF,UAAU,SAAS,yCAAyC,IAAI,KAAK;AACnE,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB,GAAG;AACtB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,CAAC;AACjG,UAAG,aAAa;AAChB,UAAG,aAAa,CAAC;AAAA,QACnB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,0BAA0B,IAAI,OAAO;AAAA,QACrD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,+BAA+B;AAAA,MACjE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iCAAiC,CAAC;AAAA,IACxG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc;AAAA,MACxB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAA8B;AAAA,EAC5B,YAAY,QAAQ,cAAc,OAAO;AACvC,SAAK,SAAS;AACd,SAAK,cAAc;AAAA,EACrB;AACF;AACA,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,YAAY,QAAQ,mBAAmB,SAAS,iCAAiC;AAC/E,SAAK,SAAS;AACd,SAAK,oBAAoB;AACzB,SAAK,UAAU;AACf,SAAK,kCAAkC;AACvC,SAAK,aAAa;AAClB,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,SAAK,OAAO,kBAAkB,MAAM;AAClC,gBAAU,KAAK,QAAQ,eAAe,YAAY,EAAE,KAAK,OAAO,MAAM,KAAK,aAAa,UAAU,SAAS,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACvJ,aAAK,OAAO,IAAI,MAAM,KAAK,aAAa,KAAK,IAAI,CAAC;AAAA,MACpD,CAAC;AACD,gBAAU,KAAK,QAAQ,eAAe,WAAW,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS,MAAM,eAAe,CAAC;AAAA,IAC7H,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,MAAM;AAClB,SAAK,WAAW;AAChB,SAAK,kBAAkB,aAAa;AACpC,QAAI,MAAM;AACR,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,WAAW;AAChB,SAAK,kBAAkB,aAAa;AACpC,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,WAAW,KAAK,QAAQ,SAAS;AAAA,EAC/C;AAAA;AAAA,EAEA,kBAAkB;AAChB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,SAAS;AACd,WAAK,kBAAkB,aAAa;AAAA,IACtC;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB;AAClB,QAAI,KAAK,QAAQ;AACf,WAAK,SAAS;AACd,WAAK,kBAAkB,aAAa;AAAA,IACtC;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,mBAAe,KAAK,QAAQ,aAAa;AAAA,EAC3C;AAAA,EACA,uBAAuB;AACrB,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,WAAW,CAAC,KAAK;AACtB,UAAI,KAAK,UAAU;AACjB,aAAK,gBAAgB;AAAA,MACvB,OAAO;AACL,aAAK,kBAAkB;AAAA,MACzB;AACA,WAAK,yBAAyB,IAAI;AAClC,WAAK,kBAAkB,aAAa;AAAA,IACtC;AAAA,EACF;AAAA,EACA,yBAAyB,cAAc,OAAO;AAC5C,SAAK,gBAAgB,KAAK,IAAI,wBAAwB,MAAM,WAAW,CAAC;AAAA,EAC1E;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sCAAsC,GAAG;AAC5D,aAAO,KAAK,KAAK,gCAAkC,kBAAqB,MAAM,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,iCAAiC,CAAC,CAAC;AAAA,IAC5N;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,MAC9B,WAAW,CAAC,QAAQ,YAAY,GAAG,mBAAmB,wBAAwB;AAAA,MAC9E,UAAU;AAAA,MACV,cAAc,SAAS,2CAA2C,IAAI,KAAK;AACzE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,yDAAyD;AACvF,mBAAO,IAAI,qBAAqB;AAAA,UAClC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,iBAAiB,IAAI,SAAS,SAAS,CAAC,EAAE,iBAAiB,IAAI,WAAW,SAAS,CAAC;AACnG,UAAG,YAAY,kCAAkC,IAAI,+BAA+B,EAAE,mCAAmC,IAAI,QAAQ,EAAE,iCAAiC,IAAI,MAAM,EAAE,mCAAmC,IAAI,UAAU;AAAA,QACvO;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,YAAY;AAAA,MACd;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,QACjB,cAAc;AAAA,MAChB;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,gCAAgC,CAAC;AAAA,MAC9C,UAAU,SAAS,uCAAuC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAAA,QAClB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,8BAA8B,WAAW,cAAc,MAAM;AAAA,CACzF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,0CAA0C;AAAA,QAC1C,2CAA2C;AAAA,QAC3C,yCAAyC;AAAA,QACzC,2CAA2C;AAAA,QAC3C,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,iCAAiC;AAAA,EACrC,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,8BAA8B;AAAA,EAC5D,OAAO;AACT;AACA,SAAS,qCAAqC;AAC5C,SAAO,MAAM,yMAAmN;AAClO;AACA,IAAM,iCAAN,MAAM,gCAA+B;AAAA;AAAA,EAEnC,IAAI,eAAe;AACjB,QAAI,KAAK,kBAAkB,KAAK,eAAe,QAAQ,QAAQ;AAC7D,aAAO,KAAK,eAAe;AAAA,IAC7B,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,YAAY,QAAQ,YAAY,SAAS,kBAAkB,yCAAyC,UAAU;AAC5G,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,mBAAmB;AACxB,SAAK,0CAA0C;AAC/C,SAAK,WAAW;AAChB,SAAK,WAAW,MAAM;AAAA,IAAC;AACvB,SAAK,YAAY,MAAM;AAAA,IAAC;AACxB,SAAK,YAAY;AACjB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,qBAAqB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AACzF,YAAI,MAAM,YAAY,QAAQ;AAC5B,cAAI,KAAK,YAAY;AACnB,iBAAK,WAAW,QAAQ;AACxB,iBAAK,aAAa;AAAA,UACpB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AACvB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,OAAO,kBAAkB,MAAM,QAAQ,QAAQ,IAAI,EAAE,KAAK,MAAM,KAAK,gBAAgB,KAAK,CAAC,CAAC;AAAA,EACnG;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,YAAY;AAC3B,UAAM,UAAU,KAAK,WAAW;AAChC,YAAQ,WAAW;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,YAAY;AACV,SAAK,gBAAgB,KAAK,WAAW,cAAc;AACnD,SAAK,cAAc;AACnB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,aAAa;AACX,QAAI,KAAK,WAAW;AAClB,WAAK,eAAe,SAAS,KAAK,YAAY;AAC9C,UAAI,KAAK,cAAc,KAAK,WAAW,YAAY,GAAG;AACpD,aAAK,WAAW,OAAO;AACvB,aAAK,4BAA4B,YAAY;AAC7C,aAAK,gCAAgC,YAAY;AACjD,aAAK,0BAA0B,YAAY;AAC3C,aAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,UAAU,MAAM;AACtB,UAAM,aAAa,YAAY,YAAY,YAAY;AACvD,QAAI,YAAY,QAAQ;AACtB,YAAM,eAAe;AAAA,IACvB;AACA,QAAI,KAAK,cAAc,YAAY,UAAU,YAAY,MAAM;AAE7D,UAAI,KAAK,gBAAgB,KAAK,aAAa,SAAS,MAAM,KAAK,eAAe;AAC5E,aAAK,gBAAgB,KAAK,aAAa;AAAA,MACzC;AACA,WAAK,WAAW;AAAA,IAClB,WAAW,KAAK,aAAa,YAAY,OAAO;AAC9C,UAAI,KAAK,eAAe,WAAW;AACjC,cAAM,eAAe;AACrB,YAAI,KAAK,cAAc;AACrB,eAAK,aAAa,qBAAqB;AAAA,QACzC,OAAO;AACL,eAAK,WAAW;AAAA,QAClB;AAAA,MACF;AAAA,IACF,WAAW,KAAK,aAAa,cAAc,KAAK,eAAe,WAAW;AACxE,YAAM,gBAAgB;AACtB,YAAM,eAAe;AACrB,UAAI,YAAY,UAAU;AACxB,aAAK,eAAe,sBAAsB;AAAA,MAC5C,OAAO;AACL,aAAK,eAAe,kBAAkB;AAAA,MACxC;AACA,UAAI,KAAK,cAAc;AACrB,aAAK,aAAa,uBAAuB;AAAA,MAC3C;AACA,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,SAAS,MAAM;AACrB,UAAM,WAAW,KAAK;AACtB,QAAI,QAAQ,OAAO;AACnB,QAAI,OAAO,SAAS,UAAU;AAC5B,cAAQ,UAAU,KAAK,OAAO,WAAW,KAAK;AAAA,IAChD;AACA,QAAI,KAAK,kBAAkB,OAAO;AAChC,WAAK,gBAAgB;AACrB,WAAK,SAAS,KAAK;AACnB,UAAI,KAAK,QAAQ,KAAK,SAAS,kBAAkB,MAAM,QAAQ;AAC7D,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,QAAQ,GAAG;AAClB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,aAAa;AACX,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,yBAAyB;AACvB,UAAM,gBAAgB,KAAK,eAAe,QAAQ,QAAQ,KAAK,IAAI,MAAM,KAAK,iBAAiB,oBAAoB,CAAC,GAAG,MAAM,CAAC,CAAC;AAC/H,WAAO,cAAc,UAAU,MAAM;AACnC,WAAK,gBAAgB;AACrB,UAAI,KAAK,WAAW;AAClB,aAAK,WAAW,eAAe;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,2BAA2B;AACzB,WAAO,KAAK,eAAe,gBAAgB,UAAU,YAAU;AAC7D,WAAK,iBAAiB,MAAM;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,+BAA+B;AAC7B,WAAO,KAAK,WAAW,qBAAqB,EAAE,KAAK,OAAO,OAAK,CAAC,KAAK,WAAW,cAAc,SAAS,EAAE,MAAM,CAAC,CAAC,EAAE,UAAU,MAAM;AACjI,WAAK,WAAW;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,gBAAgB;AACxB,YAAM,mCAAmC;AAAA,IAC3C;AACA,QAAI,CAAC,KAAK,UAAU,KAAK,eAAe,UAAU;AAChD,WAAK,SAAS,IAAI,eAAe,KAAK,eAAe,UAAU,KAAK,gBAAgB;AAAA,IACtF;AACA,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,KAAK,QAAQ,OAAO,KAAK,iBAAiB,CAAC;AAAA,IAC/D;AACA,QAAI,KAAK,cAAc,CAAC,KAAK,WAAW,YAAY,GAAG;AACrD,WAAK,WAAW,OAAO,KAAK,MAAM;AAClC,WAAK,8BAA8B,KAAK,yBAAyB;AACjE,WAAK,4BAA4B,KAAK,uBAAuB;AAC7D,WAAK,kCAAkC,KAAK,6BAA6B;AACzE,WAAK,WAAW,YAAY,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC3E,aAAK,WAAW;AAAA,MAClB,CAAC;AAAA,IACH;AACA,SAAK,eAAe,SAAS,KAAK,YAAY;AAAA,EAChD;AAAA,EACA,eAAe;AACb,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,WAAW;AAAA,QACzB,OAAO,KAAK,eAAe,WAAW,KAAK,aAAa;AAAA,MAC1D,CAAC;AAAA,IACH;AACA,SAAK,eAAe,cAAc;AAClC,SAAK,gBAAgB;AACrB,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,uBAAuB;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,eAAe;AACb,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,WAAO,IAAI,cAAc;AAAA,MACvB,kBAAkB,KAAK,mBAAmB;AAAA,MAC1C,qBAAqB;AAAA,MACrB,gBAAgB,KAAK,QAAQ,iBAAiB,WAAW;AAAA;AAAA,MAEzD,OAAO,KAAK,eAAe,WAAW,KAAK,aAAa;AAAA,IAC1D,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,0CAA0C,KAAK,wCAAwC,aAAa,KAAK;AAAA,EACvH;AAAA,EACA,eAAe;AACb,WAAO,KAAK,oBAAoB,EAAE,cAAc,sBAAsB,EAAE;AAAA,EAC1E;AAAA,EACA,qBAAqB;AACnB,UAAM,YAAY,CAAC,IAAI,uBAAuB;AAAA,MAC5C,SAAS;AAAA,MACT,SAAS;AAAA,IACX,GAAG;AAAA,MACD,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,IAAI,uBAAuB;AAAA,MAC7B,SAAS;AAAA,MACT,SAAS;AAAA,IACX,GAAG;AAAA,MACD,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,CAAC;AACF,SAAK,mBAAmB,KAAK,QAAQ,SAAS,EAAE,oBAAoB,KAAK,oBAAoB,CAAC,EAAE,uBAAuB,KAAK,EAAE,SAAS,KAAK,EAAE,cAAc,SAAS,EAAE,sBAAsB,sBAAsB;AACnN,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,UAAM,QAAQ,KAAK,eAAe,eAAe,KAAK,aAAa;AACnE,SAAK,eAAe,qBAAqB,MAAM,IAAI;AACnD,QAAI,UAAU,IAAI;AAChB,WAAK,eAAe,cAAc,KAAK;AACvC,WAAK,eAAe,WAAW,OAAO,KAAK;AAAA,IAC7C,OAAO;AACL,WAAK,eAAe,cAAc,KAAK,eAAe,6BAA6B,IAAI,EAAE;AAAA,IAC3F;AAAA,EACF;AAAA,EACA,iBAAiB,QAAQ;AACvB,UAAM,QAAQ,OAAO;AACrB,SAAK,gBAAgB,OAAO,SAAS,CAAC;AACtC,SAAK,SAAS,KAAK;AACnB,SAAK,WAAW,cAAc,MAAM;AACpC,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,gBAAgB,OAAO;AACrB,UAAM,SAAS,KAAK,eAAe,UAAU,KAAK;AAClD,UAAM,eAAe,SAAS,OAAO,SAAS,IAAI;AAClD,SAAK,WAAW,cAAc,QAAQ,gBAAgB,OAAO,eAAe;AAC5E,QAAI,CAAC,KAAK,eAAe,YAAY;AACnC,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,aAAa;AACX,QAAI,KAAK,eAAe,cAAc,KAAK,eAAe,YAAY;AACpE,WAAK,gBAAgB,KAAK,eAAe,WAAW,SAAS,CAAC;AAAA,IAChE;AAAA,EACF;AAAA,EACA,UAAU;AACR,UAAM,UAAU,KAAK,WAAW;AAChC,WAAO,CAAC,QAAQ,YAAY,CAAC,QAAQ;AAAA,EACvC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uCAAuC,GAAG;AAC7D,aAAO,KAAK,KAAK,iCAAmC,kBAAqB,MAAM,GAAM,kBAAqB,UAAU,GAAM,kBAAuB,OAAO,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,yCAAyC,CAAC,GAAM,kBAAkB,UAAU,CAAC,CAAC;AAAA,IAC9S;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,SAAS,kBAAkB,EAAE,GAAG,CAAC,YAAY,kBAAkB,EAAE,CAAC;AAAA,MAC/E,WAAW,CAAC,gBAAgB,OAAO,qBAAqB,MAAM;AAAA,MAC9D,cAAc,SAAS,4CAA4C,IAAI,KAAK;AAC1E,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,SAAS,4DAA4D;AAC5F,mBAAO,IAAI,YAAY;AAAA,UACzB,CAAC,EAAE,QAAQ,SAAS,yDAAyD;AAC3E,mBAAO,IAAI,WAAW;AAAA,UACxB,CAAC,EAAE,SAAS,SAAS,wDAAwD,QAAQ;AACnF,mBAAO,IAAI,YAAY,MAAM;AAAA,UAC/B,CAAC,EAAE,WAAW,SAAS,0DAA0D,QAAQ;AACvF,mBAAO,IAAI,cAAc,MAAM;AAAA,UACjC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,gBAAgB;AAAA,MAClB;AAAA,MACA,UAAU,CAAC,uBAAuB;AAAA,MAClC,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,8BAA8B,CAAC,CAAC;AAAA,IACpE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,8BAA8B;AAAA,MAC1C,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,cAAc;AAAA,QACd,qBAAqB;AAAA,QACrB,aAAa;AAAA,QACb,UAAU;AAAA,QACV,WAAW;AAAA,QACX,aAAa;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,oBAAoB,OAAO;AAClC,SAAO,OAAO,IAAI,UAAQ;AACxB,QAAI,OAAO,SAAS,YAAY,OAAO,SAAS,UAAU;AACxD,aAAO;AAAA,QACL,OAAO,KAAK,SAAS;AAAA,QACrB,OAAO,KAAK,SAAS;AAAA,MACvB;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;AACA,IAAM,0BAAN,MAAM,yBAAwB;AAAA;AAAA;AAAA;AAAA,EAI5B,IAAI,UAAU;AAEZ,QAAI,KAAK,cAAc;AACrB,aAAO,KAAK;AAAA,IACd,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAAA,EACA,YAAY,mBAAmB,gBAAgB,aAAa;AAC1D,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB,CAAC;AACvB,SAAK,6BAA6B;AAClC,SAAK,aAAa;AAClB,SAAK,cAAc,CAAC,IAAI,OAAO,OAAO;AACtC,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,uBAAuB,CAAC;AAC7B,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,uBAAuB,IAAI,aAAa;AAC7C,SAAK,kBAAkB;AACvB,SAAK,8BAA8B,aAAa;AAChD,SAAK,+BAA+B,aAAa;AACjD,SAAK,+BAA+B,aAAa;AAEjD,SAAK,yBAAyB,MAAM,MAAM;AACxC,UAAI,KAAK,SAAS;AAChB,eAAO,MAAM,GAAG,KAAK,QAAQ,IAAI,YAAU,OAAO,eAAe,CAAC;AAAA,MACpE;AACA,aAAO,KAAK,iBAAiB,KAAK,UAAU,MAAM,KAAK,sBAAsB,CAAC;AAAA,IAChF,CAAC;AACD,SAAK,mBAAmB,MAAM,MAAM;AAClC,UAAI,KAAK,SAAS;AAChB,eAAO,MAAM,GAAG,KAAK,QAAQ,IAAI,YAAU,OAAO,YAAY,CAAC;AAAA,MACjE;AACA,aAAO,KAAK,iBAAiB,KAAK,UAAU,MAAM,KAAK,gBAAgB,CAAC;AAAA,IAC1E,CAAC;AACD,SAAK,mBAAmB,OAAO,qBAAqB;AAAA,EACtD;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,kBAAkB,cAAc;AAAA,IACvC,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,cAAc;AAChB,WAAK,uBAAuB,oBAAoB,aAAa,YAAY;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,qBAAqB,KAAK,KAAK;AAAA,EACtC;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,cAAc;AACrB,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,6BAA6B,YAAY;AAC9C,SAAK,4BAA4B,YAAY;AAC7C,SAAK,6BAA6B,YAAY;AAI9C,SAAK,+BAA+B,KAAK,8BAA8B,KAAK,+BAA+B;AAC3G,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,gBAAgB;AACd,SAAK,YAAY,CAAC,CAAC,KAAK,QAAQ;AAChC,SAAK,kBAAkB,aAAa;AAAA,EACtC;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,aAAa,KAAK,QAAQ,IAAI,KAAK;AACzC,QAAI,cAAc,CAAC,WAAW,QAAQ;AACpC,WAAK,aAAa;AAClB,WAAK,kBAAkB;AACvB,WAAK,qBAAqB,KAAK,UAAU;AACzC,WAAK,WAAW,gBAAgB;AAAA,IAClC,OAAO;AACL,WAAK,aAAa;AAClB,WAAK,kBAAkB;AACvB,WAAK,qBAAqB;AAAA,IAC5B;AACA,SAAK,kBAAkB,aAAa;AAAA,EACtC;AAAA,EACA,oBAAoB;AAClB,UAAM,YAAY,KAAK,kBAAkB,KAAK,KAAK,QAAQ,SAAS,IAAI,KAAK,kBAAkB,IAAI;AACnG,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA,EACA,wBAAwB;AACtB,UAAM,gBAAgB,KAAK,kBAAkB,IAAI,IAAI,KAAK,QAAQ,SAAS,IAAI,KAAK,kBAAkB;AACtG,SAAK,cAAc,aAAa;AAAA,EAClC;AAAA,EACA,eAAe,OAAO;AACpB,WAAO,KAAK,QAAQ,OAAO,CAAC,QAAQ,SAAS,UAAU,WAAW,KAAK,KAAK,YAAY,OAAO,QAAQ,OAAO,IAAI,QAAQ,KAAK,QAAQ,EAAE;AAAA,EAC3I;AAAA,EACA,UAAU,OAAO;AACf,WAAO,KAAK,QAAQ,KAAK,UAAQ,KAAK,YAAY,OAAO,KAAK,OAAO,CAAC,KAAK;AAAA,EAC7E;AAAA,EACA,cAAc;AACZ,SAAK,cAAc;AACnB,SAAK,uBAAuB;AAC5B,UAAM,UAAU,KAAK,eAAe,KAAK,sBAAsB,UAAU,KAAK,mBAAmB;AAEjG,SAAK,+BAA+B,QAAQ,UAAU,OAAK;AACzD,UAAI,CAAC,EAAE,SAAS,KAAK,QAAQ;AAC3B,mBAAW,MAAM,KAAK,cAAc,CAAC;AAAA,MACvC;AACA,WAAK,uBAAuB;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB,MAAM,WAAW,OAAO;AAC3C,SAAK,QAAQ,QAAQ,YAAU;AAC7B,UAAI,WAAW,MAAM;AACnB,YAAI,UAAU;AACZ,iBAAO,SAAS;AAAA,QAClB;AACA,eAAO,kBAAkB;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,yBAAyB;AACvB,SAAK,4BAA4B,YAAY;AAC7C,SAAK,8BAA8B,KAAK,uBAAuB,KAAK,OAAO,WAAS,MAAM,WAAW,CAAC,EAAE,UAAU,WAAS;AACzH,YAAM,OAAO,OAAO;AACpB,YAAM,OAAO,gBAAgB;AAC7B,WAAK,aAAa,MAAM;AACxB,WAAK,kBAAkB,KAAK,eAAe,KAAK,WAAW,OAAO;AAClE,WAAK,qBAAqB,MAAM,QAAQ,IAAI;AAC5C,WAAK,gBAAgB,KAAK,MAAM,MAAM;AAAA,IACxC,CAAC;AACD,SAAK,6BAA6B,YAAY;AAC9C,SAAK,+BAA+B,KAAK,iBAAiB,UAAU,WAAS;AAC3E,YAAM,gBAAgB;AACtB,WAAK,aAAa;AAClB,WAAK,kBAAkB,KAAK,eAAe,KAAK,WAAW,OAAO;AAClE,WAAK,qBAAqB,KAAK;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAA4B,kBAAqB,iBAAiB,GAAM,kBAAuB,gBAAgB,CAAC,GAAM,kBAAuB,wBAAwB,CAAC,CAAC;AAAA,IAC1L;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,gBAAgB,SAAS,uCAAuC,IAAI,KAAK,UAAU;AACjF,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,+BAA+B,CAAC;AAAA,QAC9D;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB;AAAA,QACxE;AAAA,MACF;AAAA,MACA,WAAW,SAAS,8BAA8B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,aAAa,CAAC;AAC7B,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,+BAA+B,CAAC;AAAA,QACjD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAC5D,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAC9D,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,wBAAwB;AAAA,QAC3E;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,QAChB,4BAA4B;AAAA,QAC5B,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,gBAAgB;AAAA,MAC3B,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,uBAAuB,4CAA4C,GAAG,WAAW,WAAW,eAAe,GAAG,CAAC,GAAG,cAAc,SAAS,cAAc,QAAQ,mBAAmB,MAAM,GAAG,CAAC,GAAG,WAAW,QAAQ,kBAAkB,QAAQ,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,WAAW,WAAW,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,WAAW,SAAS,CAAC;AAAA,MACja,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,gDAAgD,GAAG,IAAI,aAAa;AAAA,QACvF;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,SAAO,SAAS,kBAAkB,+BAA+B,sBAAsB;AAAA,MAC/G,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,WAAW;AAAA,MACzB;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,wBAAwB,WAAW,8BAA8B,MAAM;AACpG,WAAW,CAAC,aAAa,CAAC,GAAG,wBAAwB,WAAW,cAAc,MAAM;AAAA,CACnF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,YAAY;AAAA,MACZ,SAAS,CAAC,SAAS,SAAO,SAAS,kBAAkB,+BAA+B,sBAAsB;AAAA,MAC1G,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA8BV,YAAY,CAAC,WAAW;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,QACpC,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,QACd,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAsB;AAAA,IACzC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,yBAAyB,+BAA+B,gCAAgC,+BAA+B;AAAA,MACjI,SAAS,CAAC,yBAAyB,+BAA+B,gCAAgC,+BAA+B;AAAA,IACnI,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,+BAA+B;AAAA,IAC3C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,yBAAyB,+BAA+B,gCAAgC,+BAA+B;AAAA,MACjI,SAAS,CAAC,yBAAyB,+BAA+B,gCAAgC,+BAA+B;AAAA,IACnI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}