{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-switch.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport { LEFT_ARROW, RIGHT_ARROW, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, ViewChild, Input, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject, fromEvent } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i6 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i4 from 'ng-zorro-antd/core/wave';\nimport { NzWaveModule } from 'ng-zorro-antd/core/wave';\nimport * as i5 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nconst _c0 = [\"switchElement\"];\nfunction NzSwitchComponent_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n}\nfunction NzSwitchComponent_ng_container_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzCheckedChildren);\n  }\n}\nfunction NzSwitchComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzSwitchComponent_ng_container_5_ng_container_1_Template, 2, 1, \"ng-container\", 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzCheckedChildren);\n  }\n}\nfunction NzSwitchComponent_ng_template_6_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzUnCheckedChildren);\n  }\n}\nfunction NzSwitchComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzSwitchComponent_ng_template_6_ng_container_0_Template, 2, 1, \"ng-container\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzUnCheckedChildren);\n  }\n}\nconst NZ_CONFIG_MODULE_NAME = 'switch';\nclass NzSwitchComponent {\n  updateValue(value) {\n    if (this.isChecked !== value) {\n      this.isChecked = value;\n      this.onChange(this.isChecked);\n    }\n  }\n  focus() {\n    this.focusMonitor.focusVia(this.switchElement.nativeElement, 'keyboard');\n  }\n  blur() {\n    this.switchElement.nativeElement.blur();\n  }\n  constructor(nzConfigService, host, ngZone, cdr, focusMonitor, directionality) {\n    this.nzConfigService = nzConfigService;\n    this.host = host;\n    this.ngZone = ngZone;\n    this.cdr = cdr;\n    this.focusMonitor = focusMonitor;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.isChecked = false;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.nzLoading = false;\n    this.nzDisabled = false;\n    this.nzControl = false;\n    this.nzCheckedChildren = null;\n    this.nzUnCheckedChildren = null;\n    this.nzSize = 'default';\n    this.nzId = null;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.isNzDisableFirstChange = true;\n  }\n  ngOnInit() {\n    this.directionality.change.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.host.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        event.preventDefault();\n        if (this.nzControl || this.nzDisabled || this.nzLoading) {\n          return;\n        }\n        this.ngZone.run(() => {\n          this.updateValue(!this.isChecked);\n          this.cdr.markForCheck();\n        });\n      });\n      fromEvent(this.switchElement.nativeElement, 'keydown').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        if (this.nzControl || this.nzDisabled || this.nzLoading) {\n          return;\n        }\n        const {\n          keyCode\n        } = event;\n        if (keyCode !== LEFT_ARROW && keyCode !== RIGHT_ARROW && keyCode !== SPACE && keyCode !== ENTER) {\n          return;\n        }\n        event.preventDefault();\n        this.ngZone.run(() => {\n          if (keyCode === LEFT_ARROW) {\n            this.updateValue(false);\n          } else if (keyCode === RIGHT_ARROW) {\n            this.updateValue(true);\n          } else if (keyCode === SPACE || keyCode === ENTER) {\n            this.updateValue(!this.isChecked);\n          }\n          this.cdr.markForCheck();\n        });\n      });\n    });\n  }\n  ngAfterViewInit() {\n    this.focusMonitor.monitor(this.switchElement.nativeElement, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      if (!focusOrigin) {\n        /** https://github.com/angular/angular/issues/17793 **/\n        Promise.resolve().then(() => this.onTouched());\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.focusMonitor.stopMonitoring(this.switchElement.nativeElement);\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  writeValue(value) {\n    this.isChecked = value;\n    this.cdr.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n  static {\n    this.ɵfac = function NzSwitchComponent_Factory(t) {\n      return new (t || NzSwitchComponent)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i3.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSwitchComponent,\n      selectors: [[\"nz-switch\"]],\n      viewQuery: function NzSwitchComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.switchElement = _t.first);\n        }\n      },\n      inputs: {\n        nzLoading: \"nzLoading\",\n        nzDisabled: \"nzDisabled\",\n        nzControl: \"nzControl\",\n        nzCheckedChildren: \"nzCheckedChildren\",\n        nzUnCheckedChildren: \"nzUnCheckedChildren\",\n        nzSize: \"nzSize\",\n        nzId: \"nzId\"\n      },\n      exportAs: [\"nzSwitch\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzSwitchComponent),\n        multi: true\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 9,\n      vars: 16,\n      consts: [[\"switchElement\", \"\"], [\"uncheckTemplate\", \"\"], [\"nz-wave\", \"\", \"type\", \"button\", 1, \"ant-switch\", 3, \"disabled\", \"nzWaveExtraNode\"], [1, \"ant-switch-handle\"], [\"nz-icon\", \"\", \"nzType\", \"loading\", \"class\", \"ant-switch-loading-icon\", 4, \"ngIf\"], [1, \"ant-switch-inner\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"ant-click-animating-node\"], [\"nz-icon\", \"\", \"nzType\", \"loading\", 1, \"ant-switch-loading-icon\"], [4, \"nzStringTemplateOutlet\"]],\n      template: function NzSwitchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 2, 0)(2, \"span\", 3);\n          i0.ɵɵtemplate(3, NzSwitchComponent_span_3_Template, 1, 0, \"span\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"span\", 5);\n          i0.ɵɵtemplate(5, NzSwitchComponent_ng_container_5_Template, 2, 1, \"ng-container\", 6)(6, NzSwitchComponent_ng_template_6_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const uncheckTemplate_r2 = i0.ɵɵreference(7);\n          i0.ɵɵclassProp(\"ant-switch-checked\", ctx.isChecked)(\"ant-switch-loading\", ctx.nzLoading)(\"ant-switch-disabled\", ctx.nzDisabled)(\"ant-switch-small\", ctx.nzSize === \"small\")(\"ant-switch-rtl\", ctx.dir === \"rtl\");\n          i0.ɵɵproperty(\"disabled\", ctx.nzDisabled)(\"nzWaveExtraNode\", true);\n          i0.ɵɵattribute(\"id\", ctx.nzId);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.nzLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isChecked)(\"ngIfElse\", uncheckTemplate_r2);\n        }\n      },\n      dependencies: [NzWaveModule, i4.NzWaveDirective, NzIconModule, i5.NzIconDirective, NgIf, NzOutletModule, i6.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzSwitchComponent.prototype, \"nzLoading\", void 0);\n__decorate([InputBoolean()], NzSwitchComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzSwitchComponent.prototype, \"nzControl\", void 0);\n__decorate([WithConfig()], NzSwitchComponent.prototype, \"nzSize\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSwitchComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-switch',\n      exportAs: 'nzSwitch',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzSwitchComponent),\n        multi: true\n      }],\n      template: `\n    <button\n      nz-wave\n      type=\"button\"\n      class=\"ant-switch\"\n      #switchElement\n      [attr.id]=\"nzId\"\n      [disabled]=\"nzDisabled\"\n      [class.ant-switch-checked]=\"isChecked\"\n      [class.ant-switch-loading]=\"nzLoading\"\n      [class.ant-switch-disabled]=\"nzDisabled\"\n      [class.ant-switch-small]=\"nzSize === 'small'\"\n      [class.ant-switch-rtl]=\"dir === 'rtl'\"\n      [nzWaveExtraNode]=\"true\"\n    >\n      <span class=\"ant-switch-handle\">\n        <span *ngIf=\"nzLoading\" nz-icon nzType=\"loading\" class=\"ant-switch-loading-icon\"></span>\n      </span>\n      <span class=\"ant-switch-inner\">\n        <ng-container *ngIf=\"isChecked; else uncheckTemplate\">\n          <ng-container *nzStringTemplateOutlet=\"nzCheckedChildren\">{{ nzCheckedChildren }}</ng-container>\n        </ng-container>\n        <ng-template #uncheckTemplate>\n          <ng-container *nzStringTemplateOutlet=\"nzUnCheckedChildren\">{{ nzUnCheckedChildren }}</ng-container>\n        </ng-template>\n      </span>\n      <div class=\"ant-click-animating-node\"></div>\n    </button>\n  `,\n      imports: [NzWaveModule, NzIconModule, NgIf, NzOutletModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.FocusMonitor\n  }, {\n    type: i3.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    switchElement: [{\n      type: ViewChild,\n      args: ['switchElement', {\n        static: true\n      }]\n    }],\n    nzLoading: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzControl: [{\n      type: Input\n    }],\n    nzCheckedChildren: [{\n      type: Input\n    }],\n    nzUnCheckedChildren: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzId: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSwitchModule {\n  static {\n    this.ɵfac = function NzSwitchModule_Factory(t) {\n      return new (t || NzSwitchModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzSwitchModule,\n      imports: [NzSwitchComponent],\n      exports: [NzSwitchComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzSwitchComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSwitchModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzSwitchComponent],\n      exports: [NzSwitchComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzSwitchComponent, NzSwitchModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAM,MAAM,CAAC,eAAe;AAC5B,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,iBAAiB;AAAA,EAC/C;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,gBAAgB,CAAC;AAClG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,iBAAiB;AAAA,EAClE;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,mBAAmB;AAAA,EACjD;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,CAAC;AAAA,EACnG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,0BAA0B,OAAO,mBAAmB;AAAA,EACpE;AACF;AACA,IAAM,wBAAwB;AAC9B,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,OAAO;AACjB,QAAI,KAAK,cAAc,OAAO;AAC5B,WAAK,YAAY;AACjB,WAAK,SAAS,KAAK,SAAS;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,QAAQ;AACN,SAAK,aAAa,SAAS,KAAK,cAAc,eAAe,UAAU;AAAA,EACzE;AAAA,EACA,OAAO;AACL,SAAK,cAAc,cAAc,KAAK;AAAA,EACxC;AAAA,EACA,YAAY,iBAAiB,MAAM,QAAQ,KAAK,cAAc,gBAAgB;AAC5E,SAAK,kBAAkB;AACvB,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,YAAY;AACjB,SAAK,WAAW,MAAM;AAAA,IAAC;AACvB,SAAK,YAAY,MAAM;AAAA,IAAC;AACxB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,sBAAsB;AAC3B,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,WAAW;AACT,SAAK,eAAe,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAC/E,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,OAAO,kBAAkB,MAAM;AAClC,gBAAU,KAAK,KAAK,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC5F,cAAM,eAAe;AACrB,YAAI,KAAK,aAAa,KAAK,cAAc,KAAK,WAAW;AACvD;AAAA,QACF;AACA,aAAK,OAAO,IAAI,MAAM;AACpB,eAAK,YAAY,CAAC,KAAK,SAAS;AAChC,eAAK,IAAI,aAAa;AAAA,QACxB,CAAC;AAAA,MACH,CAAC;AACD,gBAAU,KAAK,cAAc,eAAe,SAAS,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AACvG,YAAI,KAAK,aAAa,KAAK,cAAc,KAAK,WAAW;AACvD;AAAA,QACF;AACA,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,YAAY,cAAc,YAAY,eAAe,YAAY,SAAS,YAAY,OAAO;AAC/F;AAAA,QACF;AACA,cAAM,eAAe;AACrB,aAAK,OAAO,IAAI,MAAM;AACpB,cAAI,YAAY,YAAY;AAC1B,iBAAK,YAAY,KAAK;AAAA,UACxB,WAAW,YAAY,aAAa;AAClC,iBAAK,YAAY,IAAI;AAAA,UACvB,WAAW,YAAY,SAAS,YAAY,OAAO;AACjD,iBAAK,YAAY,CAAC,KAAK,SAAS;AAAA,UAClC;AACA,eAAK,IAAI,aAAa;AAAA,QACxB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,SAAK,aAAa,QAAQ,KAAK,cAAc,eAAe,IAAI,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACxH,UAAI,CAAC,aAAa;AAEhB,gBAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,UAAU,CAAC;AAAA,MAC/C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,eAAe,KAAK,cAAc,aAAa;AACjE,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,YAAY;AACjB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,SAAK,yBAAyB;AAC9B,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAqB,eAAe,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACnR;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,MACzB,WAAW,SAAS,wBAAwB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,QACtE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,QACrB,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA,UAAU,CAAC,UAAU;AAAA,MACrB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,kBAAiB;AAAA,QAC/C,OAAO;AAAA,MACT,CAAC,CAAC,GAAM,mBAAmB;AAAA,MAC3B,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,WAAW,IAAI,QAAQ,UAAU,GAAG,cAAc,GAAG,YAAY,iBAAiB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,WAAW,IAAI,UAAU,WAAW,SAAS,2BAA2B,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,WAAW,IAAI,UAAU,WAAW,GAAG,yBAAyB,GAAG,CAAC,GAAG,wBAAwB,CAAC;AAAA,MAClb,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,UAAU,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC;AACjD,UAAG,WAAW,GAAG,mCAAmC,GAAG,GAAG,QAAQ,CAAC;AACnE,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,UAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,0CAA0C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACzL,UAAG,aAAa;AAChB,UAAG,UAAU,GAAG,OAAO,CAAC;AACxB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,qBAAwB,YAAY,CAAC;AAC3C,UAAG,YAAY,sBAAsB,IAAI,SAAS,EAAE,sBAAsB,IAAI,SAAS,EAAE,uBAAuB,IAAI,UAAU,EAAE,oBAAoB,IAAI,WAAW,OAAO,EAAE,kBAAkB,IAAI,QAAQ,KAAK;AAC/M,UAAG,WAAW,YAAY,IAAI,UAAU,EAAE,mBAAmB,IAAI;AACjE,UAAG,YAAY,MAAM,IAAI,IAAI;AAC7B,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,SAAS;AACnC,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,SAAS,EAAE,YAAY,kBAAkB;AAAA,QACrE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAiB,iBAAiB,cAAiB,iBAAiB,MAAM,gBAAmB,+BAA+B;AAAA,MAC3I,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,aAAa,MAAM;AAC7E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,aAAa,MAAM;AAC7E,WAAW,CAAC,WAAW,CAAC,GAAG,kBAAkB,WAAW,UAAU,MAAM;AAAA,CACvE,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,iBAAiB;AAAA,QAC/C,OAAO;AAAA,MACT,CAAC;AAAA,MACD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA6BV,SAAS,CAAC,cAAc,cAAc,MAAM,cAAc;AAAA,MAC1D,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB;AAAA,MAC3B,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB;AAAA,MAC3B,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}