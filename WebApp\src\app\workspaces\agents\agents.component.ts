import { CommonModule } from '@angular/common';
import { Component, model } from '@angular/core';
import { FormsModule } from '@angular/forms';

import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { Router, RouterLink } from '@angular/router';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import {
  AgentDefinitionServiceProxy,
  AiServiceProxy,
  ModelDetailsServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { NzModalService } from 'ng-zorro-antd/modal';
import { AddOrEditAgentDialogComponent } from '../../dialogs/add-or-edit-agent-dialog/add-or-edit-agent-dialog.component';
import { RemoveProviderPrefixPipe } from "../../../shared/pipes/remove-provider-prefix.pipe";
import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';
@Component({
  selector: 'app-agents',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    NzInputModule,
    NzIconModule,
    NzAutocompleteModule,
    NzIconModule,
    NzBreadCrumbModule,
    ServiceProxyModule,
    RouterLink,
    RemoveProviderPrefixPipe,
    SpinnerComponent
  ],
  templateUrl: './agents.component.html',
  styleUrl: './agents.component.css',
  providers: [NzModalService],
})
export class AgentsComponent {
  agents: any[] = [];
  filteredAgents: any[] = [];
  paginatedAgents: any[] = [];
  showForm: boolean = false;
  isEditing: boolean = false;
  isLoading: boolean = false; // Loading state for spinner
  currentAgent: any = {};
  modelSearchQuery: string = '';
  searchQuery: string = '';

  // Pagination properties
  currentPage: number = 1;
  pageSize: number = 6;
  totalPages: number = 1;
  previousPageSize: number = 6;

  // Make Math available to the template
  Math = Math;

  filteredModels: any[] = [];
  models: any = [];
  workspaceName: string = '';
  plugins: any[] = [];

  constructor(
    private modelDetailsService: ModelDetailsServiceProxy, // Inject your service here
    private agentService: AgentDefinitionServiceProxy,
    private router: Router,
    private modalService: NzModalService, // Inject the modal service
    private aiService: AiServiceProxy
  ) { }
  ngOnInit(): void {
    const routerSegments = this.router.url.split('/');

    // Check if the URL is for settings or workspace
    if (routerSegments.includes('settings') && routerSegments.includes('agents')) {
      // Load all agents for settings
      this.loadAllAgents();
    } else if (routerSegments.includes('chat') && routerSegments.includes('workspace')) {
      // Extract workspace name from the URL
      const workspaceIndex = routerSegments.indexOf('workspace');
      if (workspaceIndex !== -1 && routerSegments.length > workspaceIndex + 1) {
        this.workspaceName = decodeURIComponent(routerSegments[workspaceIndex + 1]);
        console.log('Workspace name:', this.workspaceName);

        // Load agents for the specific workspace
        this.loadAgents();
      }
    }

    // Load models and plugins (common for both contexts)
    this.loadModels();
    this.loadPlugins();
  }
  loadAgents() {
    // Fetch agents from the service and assign to local array
    this.isLoading = true; // Show spinner
    this.agentService
      .getAllByWorkspace(this.workspaceName)
      .subscribe({
        next: (agents: any) => {
          this.agents = agents;
          this.filteredAgents = [...this.agents];
          this.updatePagination();
          console.log(this.agents);
          this.isLoading = false; // Hide spinner
        },
        error: (error: any) => {
          console.error('Error loading agents:', error);
          this.isLoading = false; // Hide spinner on error
        }
      });
  }

  loadAllAgents() {
    // Fetch all agents from the service and assign to local array
    this.isLoading = true; // Show spinner
    this.agentService.getAll().subscribe({
      next: (agents: any) => {
        this.agents = agents;
        this.filteredAgents = [...this.agents];
        this.updatePagination();
        this.isLoading = false; // Hide spinner
      },
      error: (error: any) => {
        console.error('Error loading all agents:', error);
        this.isLoading = false; // Hide spinner on error
      }
    });
  }

  /**
   * Filter agents based on search query
   */
  filterAgents() {
    if (!this.searchQuery) {
      this.filteredAgents = [...this.agents];
    } else {
      const query = this.searchQuery.toLowerCase();
      this.filteredAgents = this.agents.filter(agent =>
        agent.agentName.toLowerCase().includes(query) ||
        agent.instructions.toLowerCase().includes(query) ||
        agent.modelName.toLowerCase().includes(query) ||
        agent.tools.some((tool: string) => tool.toLowerCase().includes(query))
      );
    }

    // Reset to first page when filtering
    this.currentPage = 1;
    this.updatePagination();
  }

  /**
   * Update pagination based on current page and page size
   */
  updatePagination() {
    // Ensure pageSize is a number
    this.pageSize = Number(this.pageSize);

    // Check if page size has changed
    const pageSizeChanged = this.previousPageSize !== this.pageSize;

    // Calculate total pages
    this.totalPages = Math.ceil(this.filteredAgents.length / this.pageSize);

    // Reset to page 1 when page size changes
    if (pageSizeChanged) {
      this.currentPage = 1;
      console.log('Page size changed from', this.previousPageSize, 'to', this.pageSize, '- resetting to page 1');
    }

    // Ensure current page is within bounds
    if (this.currentPage < 1) this.currentPage = 1;
    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages || 1;

    // Get current page of agents
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.filteredAgents.length);
    this.paginatedAgents = this.filteredAgents.slice(startIndex, endIndex);

    // Store current page size for next comparison
    this.previousPageSize = this.pageSize;

    // Log pagination state for debugging
    console.log('Pagination updated:', {
      totalItems: this.filteredAgents.length,
      pageSize: this.pageSize,
      totalPages: this.totalPages,
      currentPage: this.currentPage,
      itemsOnCurrentPage: this.paginatedAgents.length
    });
  }

  /**
   * Go to a specific page
   */
  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePagination();
    }
  }

  /**
   * Go to the previous page
   */
  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePagination();
    }
  }

  /**
   * Go to the next page
   */
  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePagination();
    }
  }

  loadModels() {
    // Fetch models from the service and assign to filteredModels
    this.modelDetailsService.getAllActiveModel().subscribe((models: any) => {
      this.filteredModels = models;
      this.models = models;
    });
  }
  loadPlugins() {
    this.aiService.getPluginClassNames().subscribe((res: any) => {
      // this.plugins = res.message;
      // console.log(this.plugins);
      this.plugins = [...res.message];
    });
  }
  saveAgent() {
    this.currentAgent.Workspace = this.workspaceName;

    this.agentService.createOrUpdate(this.currentAgent).subscribe((res) => {
      this.loadAgents(); // refresh agents from backend
      console.log(res);
    });
    this.showForm = false;
  }



  deleteAgent(agentName: string) {
    // Find the agent object by name
    const agent = this.agents.find(a => a.agentName === agentName);
    if (!agent) {
      console.error('Agent not found:', agentName);
      return;
    }

    // Check if this is a system agent
    if (agent.isSystemAgent) {
      this.modalService.warning({
        nzTitle: 'Cannot Delete System Agent',
        nzContent: `The agent "${agentName}" is a system agent and cannot be deleted. System agents are protected from deletion to maintain system integrity.`,
        nzOkText: 'Understood',
        nzWidth: 420,
        nzCentered: true,
        nzClassName: 'system-agent-warning-modal'
      });
      return;
    }

    console.log('Delete agent clicked:', agent);

    // Use NzModalService.confirm for delete confirmation
    this.modalService.confirm({
      nzTitle: 'Confirm Agent Deletion',
      nzContent: `Are you sure you want to delete the agent "${agentName}"? This action cannot be undone.`,
      nzOkText: 'Delete',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Cancel',
      nzWidth: 420,
      nzCentered: true,
      nzMaskClosable: false,
      nzClosable: true,
      nzClassName: 'delete-agent-modal',
      nzBodyStyle: {
        'text-align': 'center',
        'padding': '24px'
      },
      nzOnOk: () => {
        console.log('Deleting agent:', agent.agentName);

        // Call the API to delete the agent
        this.agentService.delete(agent.agentName).subscribe({
          next: (res) => {
            if (res) {
              console.log('Agent deleted successfully:', res);

              // Remove the agent from the local arrays without reloading
              this.agents = this.agents.filter(a => a.agentName !== agentName);
              this.filteredAgents = this.filteredAgents.filter(a => a.agentName !== agentName);

              // Update pagination to reflect the new count
              this.updatePagination();

              console.log('Agent card removed from UI');
            }
          },
          error: (error) => {
            console.error('Error deleting agent:', error);

            // Show error message if it's a system agent protection error
            if (error.error && error.error.message && error.error.message.includes('system agent')) {
              this.modalService.error({
                nzTitle: 'Delete Failed',
                nzContent: error.error.message,
                nzOkText: 'OK',
                nzWidth: 420,
                nzCentered: true
              });
            } else {
              this.modalService.error({
                nzTitle: 'Delete Failed',
                nzContent: 'An error occurred while deleting the agent. Please try again.',
                nzOkText: 'OK',
                nzWidth: 420,
                nzCentered: true
              });
            }
          }
        });

      },
      nzOnCancel: () => {
        console.log('Delete cancelled');
      }
    });

  }

  makeSystemAgent(agentName: string) {
    // Find the agent object by name
    const agent = this.agents.find(a => a.agentName === agentName);
    if (!agent) {
      console.error('Agent not found:', agentName);
      return;
    }

    this.modalService.confirm({
      nzTitle: `Protect Agent from Deletion`,
      nzContent: `Are you sure you want to make "${agentName}" a system agent? This will protect it from deletion. You can only remove this protection through the edit dialog.`,
      nzOkText: 'Protect Agent',
      nzOkType: 'primary',
      nzCancelText: 'Cancel',
      nzWidth: 450,
      nzCentered: true,
      nzMaskClosable: false,
      nzClosable: true,
      nzOnOk: () => {
        this.agentService.toggleSystemAgent(agentName).subscribe({
          next: (res) => {
            if (res && !res.isError) {
              console.log('Agent protected successfully:', res);

              // Update the local agent object
              agent.isSystemAgent = true;

              // Show success message
              this.modalService.success({
                nzTitle: 'Agent Protected',
                nzContent: `"${agentName}" is now protected from deletion.`,
                nzOkText: 'OK',
                nzWidth: 420,
                nzCentered: true
              });
            }
          },
          error: (error) => {
            console.error('Error protecting agent:', error);
            this.modalService.error({
              nzTitle: 'Protection Failed',
              nzContent: 'An error occurred while protecting the agent. Please try again.',
              nzOkText: 'OK',
              nzWidth: 420,
              nzCentered: true
            });
          }
        });
      },
      nzOnCancel: () => {
        console.log('Agent protection cancelled');
      }
    });
  }

  resetForm() {
    this.currentAgent = {};
    this.showForm = false;
    this.isEditing = false;
  }

  onChange(event: any) {
    const query = event.target.value.toLowerCase();
    // Filter local dummy models by query
    this.filteredModels = this.models.filter((m: any) =>
      m.modelName.toLowerCase().includes(query)
    );
  }

  updateModel(modelName: string) {
    this.currentAgent.modelName = modelName;
  }

  cancel() {
    this.resetForm();
  }

  // Helper methods for stats
  getSystemAgentsCount(): number {
    return this.agents.filter(agent => agent.isSystemAgent).length;
  }

  getDeletableAgentsCount(): number {
    return this.agents.filter(agent => !agent.isSystemAgent).length;
  }
}
