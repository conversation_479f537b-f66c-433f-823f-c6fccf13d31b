{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-tabs.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, Input, Directive, Optional, Inject, EventEmitter, ChangeDetectionStrategy, ViewEncapsulation, Output, ViewChild, ContentChildren, Host, Self, InjectionToken, TemplateRef, ContentChild, QueryList, NgModule } from '@angular/core';\nimport * as i1 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i2 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { reqAnimFrame } from 'ng-zorro-antd/core/polyfill';\nimport * as i4 from '@angular/cdk/a11y';\nimport { FocusKeyManager, A11yModule } from '@angular/cdk/a11y';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { hasModifierKey, SPACE, ENTER, DOWN_ARROW, RIGHT_ARROW, UP_ARROW, LEFT_ARROW } from '@angular/cdk/keycodes';\nimport { NgIf, NgForOf, NgTemplateOutlet, NgStyle } from '@angular/common';\nimport { fromEvent, Subscription, animationFrameScheduler, asapScheduler, Subject, of, merge } from 'rxjs';\nimport { takeUntil, auditTime, startWith, first, filter, delay } from 'rxjs/operators';\nimport { NzDropdownMenuComponent, NzDropDownDirective } from 'ng-zorro-antd/dropdown';\nimport * as i3 from 'ng-zorro-antd/menu';\nimport { NzMenuModule } from 'ng-zorro-antd/menu';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport * as i2$1 from 'ng-zorro-antd/cdk/resize-observer';\nimport * as i3$1 from '@angular/cdk/bidi';\nimport { tabSwitchMotion } from 'ng-zorro-antd/core/animation';\nimport { __decorate } from 'tslib';\nimport { InputBoolean, wrapIntoObservable } from 'ng-zorro-antd/core/util';\nimport * as i1$2 from '@angular/router';\nimport { NavigationEnd } from '@angular/router';\nimport * as i1$3 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport { PREFIX } from 'ng-zorro-antd/core/logger';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction NzTabAddButtonComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const icon_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzType\", icon_r1);\n  }\n}\nconst _c0 = () => ({\n  minWidth: \"46px\"\n});\nconst _c1 = () => ({\n  visible: false\n});\nfunction NzTabNavOperationComponent_ul_5_li_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r3.tab.label, \" \");\n  }\n}\nfunction NzTabNavOperationComponent_ul_5_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 8);\n    i0.ɵɵlistener(\"click\", function NzTabNavOperationComponent_ul_5_li_1_Template_li_click_0_listener() {\n      const item_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onSelect(item_r3));\n    })(\"contextmenu\", function NzTabNavOperationComponent_ul_5_li_1_Template_li_contextmenu_0_listener($event) {\n      const item_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onContextmenu(item_r3, $event));\n    });\n    i0.ɵɵtemplate(1, NzTabNavOperationComponent_ul_5_li_1_ng_container_1_Template, 2, 1, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵclassProp(\"ant-tabs-dropdown-menu-item-disabled\", item_r3.disabled);\n    i0.ɵɵproperty(\"nzSelected\", item_r3.active)(\"nzDisabled\", item_r3.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", item_r3.tab.label)(\"nzStringTemplateOutletContext\", i0.ɵɵpureFunction0(6, _c1));\n  }\n}\nfunction NzTabNavOperationComponent_ul_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 6);\n    i0.ɵɵtemplate(1, NzTabNavOperationComponent_ul_5_li_1_Template, 2, 7, \"li\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.items);\n  }\n}\nfunction NzTabNavOperationComponent_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function NzTabNavOperationComponent_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.addClicked.emit());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"addIcon\", ctx_r3.addIcon);\n  }\n}\nconst _c2 = [\"navWarp\"];\nconst _c3 = [\"navList\"];\nconst _c4 = [\"*\"];\nfunction NzTabNavBarComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function NzTabNavBarComponent_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addClicked.emit());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"addIcon\", ctx_r2.addIcon);\n    i0.ɵɵattribute(\"tabindex\", -1);\n  }\n}\nfunction NzTabNavBarComponent_div_8_ng_template_1_Template(rf, ctx) {}\nfunction NzTabNavBarComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, NzTabNavBarComponent_div_8_ng_template_1_Template, 0, 0, \"ng-template\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.extraTemplate);\n  }\n}\nconst _c5 = [\"nz-tab-body\", \"\"];\nfunction NzTabBodyComponent_ng_container_0_ng_template_1_Template(rf, ctx) {}\nfunction NzTabBodyComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTabBodyComponent_ng_container_0_ng_template_1_Template, 0, 0, \"ng-template\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.content);\n  }\n}\nfunction NzTabCloseButtonComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const icon_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzType\", icon_r1);\n  }\n}\nconst _c6 = [\"contentTemplate\"];\nconst _c7 = [[[\"\", \"nz-tab-link\", \"\"]], \"*\"];\nconst _c8 = [\"[nz-tab-link]\", \"*\"];\nfunction NzTabComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzTabComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nconst _c9 = () => ({\n  visible: true\n});\nfunction NzTabSetComponent_nz_tabs_nav_0_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tab_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r5.label);\n  }\n}\nfunction NzTabSetComponent_nz_tabs_nav_0_div_1_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function NzTabSetComponent_nz_tabs_nav_0_div_1_button_3_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const i_r6 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClose(i_r6, $event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"closeIcon\", tab_r5.nzCloseIcon);\n  }\n}\nfunction NzTabSetComponent_nz_tabs_nav_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function NzTabSetComponent_nz_tabs_nav_0_div_1_Template_div_click_0_listener($event) {\n      const ctx_r3 = i0.ɵɵrestoreView(_r3);\n      const tab_r5 = ctx_r3.$implicit;\n      const i_r6 = ctx_r3.index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clickNavItem(tab_r5, i_r6, $event));\n    })(\"contextmenu\", function NzTabSetComponent_nz_tabs_nav_0_div_1_Template_div_contextmenu_0_listener($event) {\n      const tab_r5 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.contextmenuNavItem(tab_r5, $event));\n    });\n    i0.ɵɵelementStart(1, \"button\", 7);\n    i0.ɵɵtemplate(2, NzTabSetComponent_nz_tabs_nav_0_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 8)(3, NzTabSetComponent_nz_tabs_nav_0_div_1_button_3_Template, 1, 1, \"button\", 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tab_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"margin-right\", ctx_r1.position === \"horizontal\" ? ctx_r1.nzTabBarGutter : null, \"px\")(\"margin-bottom\", ctx_r1.position === \"vertical\" ? ctx_r1.nzTabBarGutter : null, \"px\");\n    i0.ɵɵclassProp(\"ant-tabs-tab-active\", ctx_r1.nzSelectedIndex === i_r6)(\"ant-tabs-tab-disabled\", tab_r5.nzDisabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", ctx_r1.getTabContentId(i_r6))(\"disabled\", tab_r5.nzDisabled)(\"tab\", tab_r5)(\"active\", ctx_r1.nzSelectedIndex === i_r6);\n    i0.ɵɵattribute(\"tabIndex\", ctx_r1.getTabIndex(tab_r5, i_r6))(\"aria-disabled\", tab_r5.nzDisabled)(\"aria-selected\", ctx_r1.nzSelectedIndex === i_r6 && !ctx_r1.nzHideAll)(\"aria-controls\", ctx_r1.getTabContentId(i_r6));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", tab_r5.label)(\"nzStringTemplateOutletContext\", i0.ɵɵpureFunction0(19, _c9));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", tab_r5.nzClosable && ctx_r1.closable && !tab_r5.nzDisabled);\n  }\n}\nfunction NzTabSetComponent_nz_tabs_nav_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-tabs-nav\", 4);\n    i0.ɵɵlistener(\"tabScroll\", function NzTabSetComponent_nz_tabs_nav_0_Template_nz_tabs_nav_tabScroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nzTabListScroll.emit($event));\n    })(\"selectFocusedIndex\", function NzTabSetComponent_nz_tabs_nav_0_Template_nz_tabs_nav_selectFocusedIndex_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setSelectedIndex($event));\n    })(\"addClicked\", function NzTabSetComponent_nz_tabs_nav_0_Template_nz_tabs_nav_addClicked_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAdd());\n    });\n    i0.ɵɵtemplate(1, NzTabSetComponent_nz_tabs_nav_0_div_1_Template, 4, 20, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.nzTabBarStyle)(\"selectedIndex\", ctx_r1.nzSelectedIndex || 0)(\"inkBarAnimated\", ctx_r1.inkBarAnimated)(\"addable\", ctx_r1.addable)(\"addIcon\", ctx_r1.nzAddIcon)(\"hideBar\", ctx_r1.nzHideAll)(\"position\", ctx_r1.position)(\"extraTemplate\", ctx_r1.nzTabBarExtraContent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.tabs);\n  }\n}\nfunction NzTabSetComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 11);\n  }\n  if (rf & 2) {\n    const tab_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r1.getTabContentId(i_r9))(\"active\", ctx_r1.nzSelectedIndex === i_r9 && !ctx_r1.nzHideAll)(\"content\", tab_r8.content)(\"forceRender\", tab_r8.nzForceRender)(\"animated\", ctx_r1.tabPaneAnimated);\n    i0.ɵɵattribute(\"aria-labelledby\", ctx_r1.getTabContentId(i_r9));\n  }\n}\nclass NzTabAddButtonComponent {\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n    this.addIcon = 'plus';\n    this.element = this.elementRef.nativeElement;\n  }\n  getElementWidth() {\n    return this.element?.offsetWidth || 0;\n  }\n  getElementHeight() {\n    return this.element?.offsetHeight || 0;\n  }\n  static {\n    this.ɵfac = function NzTabAddButtonComponent_Factory(t) {\n      return new (t || NzTabAddButtonComponent)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTabAddButtonComponent,\n      selectors: [[\"nz-tab-add-button\"], [\"button\", \"nz-tab-add-button\", \"\"]],\n      hostAttrs: [\"aria-label\", \"Add tab\", \"type\", \"button\", 1, \"ant-tabs-nav-add\"],\n      inputs: {\n        addIcon: \"addIcon\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"nzStringTemplateOutlet\"], [\"nz-icon\", \"\", \"nzTheme\", \"outline\", 3, \"nzType\"]],\n      template: function NzTabAddButtonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzTabAddButtonComponent_ng_container_0_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.addIcon);\n        }\n      },\n      dependencies: [NzOutletModule, i1.NzStringTemplateOutletDirective, NzIconModule, i2.NzIconDirective],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabAddButtonComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-tab-add-button, button[nz-tab-add-button]',\n      template: `\n    <ng-container *nzStringTemplateOutlet=\"addIcon; let icon\">\n      <span nz-icon [nzType]=\"icon\" nzTheme=\"outline\"></span>\n    </ng-container>\n  `,\n      host: {\n        class: 'ant-tabs-nav-add',\n        'aria-label': 'Add tab',\n        type: 'button'\n      },\n      imports: [NzOutletModule, NzIconModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    addIcon: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTabsInkBarDirective {\n  get _animated() {\n    return this.animationMode !== 'NoopAnimations' && this.animated;\n  }\n  constructor(elementRef, ngZone, animationMode) {\n    this.elementRef = elementRef;\n    this.ngZone = ngZone;\n    this.animationMode = animationMode;\n    this.position = 'horizontal';\n    this.animated = true;\n  }\n  alignToElement(element) {\n    this.ngZone.runOutsideAngular(() => {\n      reqAnimFrame(() => this.setStyles(element));\n    });\n  }\n  setStyles(element) {\n    const inkBar = this.elementRef.nativeElement;\n    if (this.position === 'horizontal') {\n      inkBar.style.top = '';\n      inkBar.style.height = '';\n      inkBar.style.left = this.getLeftPosition(element);\n      inkBar.style.width = this.getElementWidth(element);\n    } else {\n      inkBar.style.left = '';\n      inkBar.style.width = '';\n      inkBar.style.top = this.getTopPosition(element);\n      inkBar.style.height = this.getElementHeight(element);\n    }\n  }\n  getLeftPosition(element) {\n    return element ? `${element.offsetLeft || 0}px` : '0';\n  }\n  getElementWidth(element) {\n    return element ? `${element.offsetWidth || 0}px` : '0';\n  }\n  getTopPosition(element) {\n    return element ? `${element.offsetTop || 0}px` : '0';\n  }\n  getElementHeight(element) {\n    return element ? `${element.offsetHeight || 0}px` : '0';\n  }\n  static {\n    this.ɵfac = function NzTabsInkBarDirective_Factory(t) {\n      return new (t || NzTabsInkBarDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTabsInkBarDirective,\n      selectors: [[\"nz-tabs-ink-bar\"], [\"\", \"nz-tabs-ink-bar\", \"\"]],\n      hostAttrs: [1, \"ant-tabs-ink-bar\"],\n      hostVars: 2,\n      hostBindings: function NzTabsInkBarDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-tabs-ink-bar-animated\", ctx._animated);\n        }\n      },\n      inputs: {\n        position: \"position\",\n        animated: \"animated\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabsInkBarDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'nz-tabs-ink-bar, [nz-tabs-ink-bar]',\n      host: {\n        class: 'ant-tabs-ink-bar',\n        '[class.ant-tabs-ink-bar-animated]': '_animated'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    position: [{\n      type: Input\n    }],\n    animated: [{\n      type: Input\n    }]\n  });\n})();\nclass NzTabNavItemDirective {\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n    this.disabled = false;\n    this.active = false;\n    this.el = elementRef.nativeElement;\n    this.parentElement = this.el.parentElement;\n  }\n  focus() {\n    this.el.focus();\n  }\n  get width() {\n    return this.parentElement.offsetWidth;\n  }\n  get height() {\n    return this.parentElement.offsetHeight;\n  }\n  get left() {\n    return this.parentElement.offsetLeft;\n  }\n  get top() {\n    return this.parentElement.offsetTop;\n  }\n  static {\n    this.ɵfac = function NzTabNavItemDirective_Factory(t) {\n      return new (t || NzTabNavItemDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTabNavItemDirective,\n      selectors: [[\"\", \"nzTabNavItem\", \"\"]],\n      inputs: {\n        disabled: \"disabled\",\n        tab: \"tab\",\n        active: \"active\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabNavItemDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzTabNavItem]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    disabled: [{\n      type: Input\n    }],\n    tab: [{\n      type: Input\n    }],\n    active: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTabNavOperationComponent {\n  constructor(cdr, elementRef) {\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.items = [];\n    this.addable = false;\n    this.addIcon = 'plus';\n    this.addClicked = new EventEmitter();\n    this.selected = new EventEmitter();\n    this.menuOpened = false;\n    this.element = this.elementRef.nativeElement;\n  }\n  onSelect(item) {\n    if (!item.disabled) {\n      // ignore nzCanDeactivate\n      item.tab.nzClick.emit();\n      this.selected.emit(item);\n    }\n  }\n  onContextmenu(item, e) {\n    if (!item.disabled) {\n      item.tab.nzContextmenu.emit(e);\n    }\n  }\n  showItems() {\n    clearTimeout(this.closeAnimationWaitTimeoutId);\n    this.menuOpened = true;\n    this.cdr.markForCheck();\n  }\n  menuVisChange(visible) {\n    if (!visible) {\n      this.closeAnimationWaitTimeoutId = setTimeout(() => {\n        this.menuOpened = false;\n        this.cdr.markForCheck();\n      }, 150);\n    }\n  }\n  getElementWidth() {\n    return this.element?.offsetWidth || 0;\n  }\n  getElementHeight() {\n    return this.element?.offsetHeight || 0;\n  }\n  ngOnDestroy() {\n    clearTimeout(this.closeAnimationWaitTimeoutId);\n  }\n  static {\n    this.ɵfac = function NzTabNavOperationComponent_Factory(t) {\n      return new (t || NzTabNavOperationComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTabNavOperationComponent,\n      selectors: [[\"nz-tab-nav-operation\"]],\n      hostAttrs: [1, \"ant-tabs-nav-operations\"],\n      hostVars: 2,\n      hostBindings: function NzTabNavOperationComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-tabs-nav-operations-hidden\", ctx.items.length === 0);\n        }\n      },\n      inputs: {\n        items: \"items\",\n        addable: \"addable\",\n        addIcon: \"addIcon\"\n      },\n      outputs: {\n        addClicked: \"addClicked\",\n        selected: \"selected\"\n      },\n      exportAs: [\"nzTabNavOperation\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 6,\n      consts: [[\"dropdownTrigger\", \"nzDropdown\"], [\"menu\", \"nzDropdownMenu\"], [\"nz-dropdown\", \"\", \"type\", \"button\", \"tabindex\", \"-1\", \"aria-hidden\", \"true\", \"nzOverlayClassName\", \"nz-tabs-dropdown\", 1, \"ant-tabs-nav-more\", 3, \"nzVisibleChange\", \"mouseenter\", \"nzDropdownMenu\", \"nzOverlayStyle\", \"nzMatchWidthElement\"], [\"nz-icon\", \"\", \"nzType\", \"ellipsis\"], [\"nz-menu\", \"\", 4, \"ngIf\"], [\"nz-tab-add-button\", \"\", 3, \"addIcon\", \"click\", 4, \"ngIf\"], [\"nz-menu\", \"\"], [\"nz-menu-item\", \"\", \"class\", \"ant-tabs-dropdown-menu-item\", 3, \"ant-tabs-dropdown-menu-item-disabled\", \"nzSelected\", \"nzDisabled\", \"click\", \"contextmenu\", 4, \"ngFor\", \"ngForOf\"], [\"nz-menu-item\", \"\", 1, \"ant-tabs-dropdown-menu-item\", 3, \"click\", \"contextmenu\", \"nzSelected\", \"nzDisabled\"], [4, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"], [\"nz-tab-add-button\", \"\", 3, \"click\", \"addIcon\"]],\n      template: function NzTabNavOperationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"button\", 2, 0);\n          i0.ɵɵlistener(\"nzVisibleChange\", function NzTabNavOperationComponent_Template_button_nzVisibleChange_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.menuVisChange($event));\n          })(\"mouseenter\", function NzTabNavOperationComponent_Template_button_mouseenter_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.showItems());\n          });\n          i0.ɵɵelement(2, \"span\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nz-dropdown-menu\", null, 1);\n          i0.ɵɵtemplate(5, NzTabNavOperationComponent_ul_5_Template, 2, 1, \"ul\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, NzTabNavOperationComponent_button_6_Template, 1, 1, \"button\", 5);\n        }\n        if (rf & 2) {\n          const menu_r6 = i0.ɵɵreference(4);\n          i0.ɵɵproperty(\"nzDropdownMenu\", menu_r6)(\"nzOverlayStyle\", i0.ɵɵpureFunction0(5, _c0))(\"nzMatchWidthElement\", null);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.menuOpened);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.addable);\n        }\n      },\n      dependencies: [NzIconModule, i2.NzIconDirective, NgIf, NgForOf, NzOutletModule, i1.NzStringTemplateOutletDirective, NzTabAddButtonComponent, NzDropdownMenuComponent, NzMenuModule, i3.NzMenuDirective, i3.NzMenuItemComponent, NzDropDownDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabNavOperationComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-tab-nav-operation',\n      exportAs: 'nzTabNavOperation',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <button\n      nz-dropdown\n      class=\"ant-tabs-nav-more\"\n      type=\"button\"\n      tabindex=\"-1\"\n      aria-hidden=\"true\"\n      nzOverlayClassName=\"nz-tabs-dropdown\"\n      #dropdownTrigger=\"nzDropdown\"\n      [nzDropdownMenu]=\"menu\"\n      [nzOverlayStyle]=\"{ minWidth: '46px' }\"\n      [nzMatchWidthElement]=\"null\"\n      (nzVisibleChange)=\"menuVisChange($event)\"\n      (mouseenter)=\"showItems()\"\n    >\n      <span nz-icon nzType=\"ellipsis\"></span>\n    </button>\n    <nz-dropdown-menu #menu=\"nzDropdownMenu\">\n      <ul nz-menu *ngIf=\"menuOpened\">\n        <li\n          nz-menu-item\n          *ngFor=\"let item of items\"\n          class=\"ant-tabs-dropdown-menu-item\"\n          [class.ant-tabs-dropdown-menu-item-disabled]=\"item.disabled\"\n          [nzSelected]=\"item.active\"\n          [nzDisabled]=\"item.disabled\"\n          (click)=\"onSelect(item)\"\n          (contextmenu)=\"onContextmenu(item, $event)\"\n        >\n          <ng-container *nzStringTemplateOutlet=\"item.tab.label; context: { visible: false }\">\n            {{ item.tab.label }}\n          </ng-container>\n        </li>\n      </ul>\n    </nz-dropdown-menu>\n    <button *ngIf=\"addable\" nz-tab-add-button [addIcon]=\"addIcon\" (click)=\"addClicked.emit()\"></button>\n  `,\n      host: {\n        class: 'ant-tabs-nav-operations',\n        '[class.ant-tabs-nav-operations-hidden]': 'items.length === 0'\n      },\n      imports: [NzIconModule, NgIf, NgForOf, NzOutletModule, NzTabAddButtonComponent, NzDropdownMenuComponent, NzMenuModule, NzDropDownDirective],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }], {\n    items: [{\n      type: Input\n    }],\n    addable: [{\n      type: Input\n    }],\n    addIcon: [{\n      type: Input\n    }],\n    addClicked: [{\n      type: Output\n    }],\n    selected: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst MIN_SWIPE_DISTANCE = 0.1;\nconst STOP_SWIPE_DISTANCE = 0.01;\nconst REFRESH_INTERVAL = 20;\nconst SPEED_OFF_MULTIPLE = 0.995 ** REFRESH_INTERVAL;\nclass NzTabScrollListDirective {\n  constructor(ngZone, elementRef) {\n    this.ngZone = ngZone;\n    this.elementRef = elementRef;\n    this.lastWheelDirection = null;\n    this.lastWheelTimestamp = 0;\n    this.lastTimestamp = 0;\n    this.lastTimeDiff = 0;\n    this.lastMixedWheel = 0;\n    this.lastWheelPrevent = false;\n    this.touchPosition = null;\n    this.lastOffset = null;\n    this.motion = -1;\n    this.unsubscribe = () => void 0;\n    this.offsetChange = new EventEmitter();\n    this.tabScroll = new EventEmitter();\n    this.onTouchEnd = e => {\n      if (!this.touchPosition) {\n        return;\n      }\n      const lastOffset = this.lastOffset;\n      const lastTimeDiff = this.lastTimeDiff;\n      this.lastOffset = this.touchPosition = null;\n      if (lastOffset) {\n        const distanceX = lastOffset.x / lastTimeDiff;\n        const distanceY = lastOffset.y / lastTimeDiff;\n        const absX = Math.abs(distanceX);\n        const absY = Math.abs(distanceY);\n        // Skip swipe if low distance\n        if (Math.max(absX, absY) < MIN_SWIPE_DISTANCE) {\n          return;\n        }\n        let currentX = distanceX;\n        let currentY = distanceY;\n        this.motion = window.setInterval(() => {\n          if (Math.abs(currentX) < STOP_SWIPE_DISTANCE && Math.abs(currentY) < STOP_SWIPE_DISTANCE) {\n            window.clearInterval(this.motion);\n            return;\n          }\n          currentX *= SPEED_OFF_MULTIPLE;\n          currentY *= SPEED_OFF_MULTIPLE;\n          this.onOffset(currentX * REFRESH_INTERVAL, currentY * REFRESH_INTERVAL, e);\n        }, REFRESH_INTERVAL);\n      }\n    };\n    this.onTouchMove = e => {\n      if (!this.touchPosition) {\n        return;\n      }\n      e.preventDefault();\n      const {\n        screenX,\n        screenY\n      } = e.touches[0];\n      const offsetX = screenX - this.touchPosition.x;\n      const offsetY = screenY - this.touchPosition.y;\n      this.onOffset(offsetX, offsetY, e);\n      const now = Date.now();\n      this.lastTimeDiff = now - this.lastTimestamp;\n      this.lastTimestamp = now;\n      this.lastOffset = {\n        x: offsetX,\n        y: offsetY\n      };\n      this.touchPosition = {\n        x: screenX,\n        y: screenY\n      };\n    };\n    this.onTouchStart = e => {\n      const {\n        screenX,\n        screenY\n      } = e.touches[0];\n      this.touchPosition = {\n        x: screenX,\n        y: screenY\n      };\n      window.clearInterval(this.motion);\n    };\n    this.onWheel = e => {\n      const {\n        deltaX,\n        deltaY\n      } = e;\n      let mixed;\n      const absX = Math.abs(deltaX);\n      const absY = Math.abs(deltaY);\n      if (absX === absY) {\n        mixed = this.lastWheelDirection === 'x' ? deltaX : deltaY;\n      } else if (absX > absY) {\n        mixed = deltaX;\n        this.lastWheelDirection = 'x';\n      } else {\n        mixed = deltaY;\n        this.lastWheelDirection = 'y';\n      }\n      // Optimize mac touch scroll\n      const now = Date.now();\n      const absMixed = Math.abs(mixed);\n      if (now - this.lastWheelTimestamp > 100 || absMixed - this.lastMixedWheel > 10) {\n        this.lastWheelPrevent = false;\n      }\n      this.onOffset(-mixed, -mixed, e);\n      if (e.defaultPrevented || this.lastWheelPrevent) {\n        this.lastWheelPrevent = true;\n      }\n      this.lastWheelTimestamp = now;\n      this.lastMixedWheel = absMixed;\n    };\n  }\n  ngOnInit() {\n    this.unsubscribe = this.ngZone.runOutsideAngular(() => {\n      const el = this.elementRef.nativeElement;\n      const wheel$ = fromEvent(el, 'wheel');\n      const touchstart$ = fromEvent(el, 'touchstart');\n      const touchmove$ = fromEvent(el, 'touchmove');\n      const touchend$ = fromEvent(el, 'touchend');\n      const subscription = new Subscription();\n      subscription.add(this.subscribeWrap('wheel', wheel$, this.onWheel));\n      subscription.add(this.subscribeWrap('touchstart', touchstart$, this.onTouchStart));\n      subscription.add(this.subscribeWrap('touchmove', touchmove$, this.onTouchMove));\n      subscription.add(this.subscribeWrap('touchend', touchend$, this.onTouchEnd));\n      return () => {\n        subscription.unsubscribe();\n      };\n    });\n  }\n  subscribeWrap(type, observable, handler) {\n    return observable.subscribe(event => {\n      this.tabScroll.emit({\n        type,\n        event\n      });\n      if (!event.defaultPrevented) {\n        handler(event);\n      }\n    });\n  }\n  onOffset(x, y, event) {\n    this.ngZone.run(() => {\n      this.offsetChange.emit({\n        x,\n        y,\n        event\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe();\n  }\n  static {\n    this.ɵfac = function NzTabScrollListDirective_Factory(t) {\n      return new (t || NzTabScrollListDirective)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTabScrollListDirective,\n      selectors: [[\"\", \"nzTabScrollList\", \"\"]],\n      outputs: {\n        offsetChange: \"offsetChange\",\n        tabScroll: \"tabScroll\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabScrollListDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzTabScrollList]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }], {\n    offsetChange: [{\n      type: Output\n    }],\n    tabScroll: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst RESIZE_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\nconst CSS_TRANSFORM_TIME = 150;\nclass NzTabNavBarComponent {\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(value) {\n    const newValue = coerceNumberProperty(value);\n    if (this._selectedIndex !== newValue) {\n      this._selectedIndex = value;\n      this.selectedIndexChanged = true;\n      if (this.keyManager) {\n        this.keyManager.updateActiveItem(value);\n      }\n    }\n  }\n  /** Tracks which element has focus; used for keyboard navigation */\n  get focusIndex() {\n    return this.keyManager ? this.keyManager.activeItemIndex : 0;\n  }\n  /** When the focus index is set, we must manually send focus to the correct label */\n  set focusIndex(value) {\n    if (!this.isValidIndex(value) || this.focusIndex === value || !this.keyManager) {\n      return;\n    }\n    this.keyManager.setActiveItem(value);\n  }\n  get showAddButton() {\n    return this.hiddenItems.length === 0 && this.addable;\n  }\n  constructor(cdr, ngZone, viewportRuler, nzResizeObserver, dir) {\n    this.cdr = cdr;\n    this.ngZone = ngZone;\n    this.viewportRuler = viewportRuler;\n    this.nzResizeObserver = nzResizeObserver;\n    this.dir = dir;\n    this.indexFocused = new EventEmitter();\n    this.selectFocusedIndex = new EventEmitter();\n    this.addClicked = new EventEmitter();\n    this.tabScroll = new EventEmitter();\n    this.position = 'horizontal';\n    this.addable = false;\n    this.hideBar = false;\n    this.addIcon = 'plus';\n    this.inkBarAnimated = true;\n    this.translate = null;\n    this.transformX = 0;\n    this.transformY = 0;\n    this.pingLeft = false;\n    this.pingRight = false;\n    this.pingTop = false;\n    this.pingBottom = false;\n    this.hiddenItems = [];\n    this.destroy$ = new Subject();\n    this._selectedIndex = 0;\n    this.wrapperWidth = 0;\n    this.wrapperHeight = 0;\n    this.scrollListWidth = 0;\n    this.scrollListHeight = 0;\n    this.operationWidth = 0;\n    this.operationHeight = 0;\n    this.addButtonWidth = 0;\n    this.addButtonHeight = 0;\n    this.selectedIndexChanged = false;\n  }\n  ngAfterViewInit() {\n    const dirChange = this.dir ? this.dir.change.asObservable() : of(null);\n    const resize = this.viewportRuler.change(150);\n    const realign = () => {\n      this.updateScrollListPosition();\n      this.alignInkBarToSelectedTab();\n    };\n    this.keyManager = new FocusKeyManager(this.items).withHorizontalOrientation(this.getLayoutDirection()).withWrap();\n    this.keyManager.updateActiveItem(this.selectedIndex);\n    reqAnimFrame(realign);\n    merge(this.nzResizeObserver.observe(this.navWarpRef), this.nzResizeObserver.observe(this.navListRef)).pipe(takeUntil(this.destroy$), auditTime(16, RESIZE_SCHEDULER)).subscribe(() => {\n      realign();\n    });\n    merge(dirChange, resize, this.items.changes).pipe(takeUntil(this.destroy$)).subscribe(() => {\n      Promise.resolve().then(realign);\n      this.keyManager.withHorizontalOrientation(this.getLayoutDirection());\n    });\n    this.keyManager.change.pipe(takeUntil(this.destroy$)).subscribe(newFocusIndex => {\n      this.indexFocused.emit(newFocusIndex);\n      this.setTabFocus(newFocusIndex);\n      this.scrollToTab(this.keyManager.activeItem);\n    });\n  }\n  ngAfterContentChecked() {\n    if (this.selectedIndexChanged) {\n      this.updateScrollListPosition();\n      this.alignInkBarToSelectedTab();\n      this.selectedIndexChanged = false;\n      this.cdr.markForCheck();\n    }\n  }\n  ngOnDestroy() {\n    clearTimeout(this.lockAnimationTimeoutId);\n    clearTimeout(this.cssTransformTimeWaitingId);\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  onSelectedFromMenu(tab) {\n    const tabIndex = this.items.toArray().findIndex(e => e === tab);\n    if (tabIndex !== -1) {\n      this.keyManager.updateActiveItem(tabIndex);\n      if (this.focusIndex !== this.selectedIndex) {\n        this.selectFocusedIndex.emit(this.focusIndex);\n        this.scrollToTab(tab);\n      }\n    }\n  }\n  onOffsetChange(e) {\n    if (this.position === 'horizontal') {\n      if (!this.lockAnimationTimeoutId) {\n        if (this.transformX >= 0 && e.x > 0) {\n          return;\n        }\n        if (this.transformX <= this.wrapperWidth - this.scrollListWidth && e.x < 0) {\n          return;\n        }\n      }\n      e.event.preventDefault();\n      this.transformX = this.clampTransformX(this.transformX + e.x);\n      this.setTransform(this.transformX, 0);\n    } else {\n      if (!this.lockAnimationTimeoutId) {\n        if (this.transformY >= 0 && e.y > 0) {\n          return;\n        }\n        if (this.transformY <= this.wrapperHeight - this.scrollListHeight && e.y < 0) {\n          return;\n        }\n      }\n      e.event.preventDefault();\n      this.transformY = this.clampTransformY(this.transformY + e.y);\n      this.setTransform(0, this.transformY);\n    }\n    this.lockAnimation();\n    this.setVisibleRange();\n    this.setPingStatus();\n  }\n  handleKeydown(event) {\n    const inNavigationList = this.navWarpRef.nativeElement.contains(event.target);\n    if (hasModifierKey(event) || !inNavigationList) {\n      return;\n    }\n    switch (event.keyCode) {\n      case LEFT_ARROW:\n      case UP_ARROW:\n      case RIGHT_ARROW:\n      case DOWN_ARROW:\n        this.lockAnimation();\n        this.keyManager.onKeydown(event);\n        break;\n      case ENTER:\n      case SPACE:\n        if (this.focusIndex !== this.selectedIndex) {\n          this.selectFocusedIndex.emit(this.focusIndex);\n        }\n        break;\n      default:\n        this.keyManager.onKeydown(event);\n    }\n  }\n  isValidIndex(index) {\n    if (!this.items) {\n      return true;\n    }\n    const tab = this.items ? this.items.toArray()[index] : null;\n    return !!tab && !tab.disabled;\n  }\n  scrollToTab(tab) {\n    if (!this.items.find(e => e === tab)) {\n      return;\n    }\n    const tabs = this.items.toArray();\n    if (this.position === 'horizontal') {\n      let newTransform = this.transformX;\n      if (this.getLayoutDirection() === 'rtl') {\n        const right = tabs[0].left + tabs[0].width - tab.left - tab.width;\n        if (right < this.transformX) {\n          newTransform = right;\n        } else if (right + tab.width > this.transformX + this.wrapperWidth) {\n          newTransform = right + tab.width - this.wrapperWidth;\n        }\n      } else if (tab.left < -this.transformX) {\n        newTransform = -tab.left;\n      } else if (tab.left + tab.width > -this.transformX + this.wrapperWidth) {\n        newTransform = -(tab.left + tab.width - this.wrapperWidth);\n      }\n      this.transformX = newTransform;\n      this.transformY = 0;\n      this.setTransform(newTransform, 0);\n    } else {\n      let newTransform = this.transformY;\n      if (tab.top < -this.transformY) {\n        newTransform = -tab.top;\n      } else if (tab.top + tab.height > -this.transformY + this.wrapperHeight) {\n        newTransform = -(tab.top + tab.height - this.wrapperHeight);\n      }\n      this.transformY = newTransform;\n      this.transformX = 0;\n      this.setTransform(0, newTransform);\n    }\n    clearTimeout(this.cssTransformTimeWaitingId);\n    this.cssTransformTimeWaitingId = setTimeout(() => {\n      this.setVisibleRange();\n    }, CSS_TRANSFORM_TIME);\n  }\n  lockAnimation() {\n    if (!this.lockAnimationTimeoutId) {\n      this.ngZone.runOutsideAngular(() => {\n        this.navListRef.nativeElement.style.transition = 'none';\n        this.lockAnimationTimeoutId = setTimeout(() => {\n          this.navListRef.nativeElement.style.transition = '';\n          this.lockAnimationTimeoutId = undefined;\n        }, CSS_TRANSFORM_TIME);\n      });\n    }\n  }\n  setTransform(x, y) {\n    this.navListRef.nativeElement.style.transform = `translate(${x}px, ${y}px)`;\n  }\n  clampTransformX(transform) {\n    const scrollWidth = this.wrapperWidth - this.scrollListWidth;\n    if (this.getLayoutDirection() === 'rtl') {\n      return Math.max(Math.min(scrollWidth, transform), 0);\n    } else {\n      return Math.min(Math.max(scrollWidth, transform), 0);\n    }\n  }\n  clampTransformY(transform) {\n    return Math.min(Math.max(this.wrapperHeight - this.scrollListHeight, transform), 0);\n  }\n  updateScrollListPosition() {\n    this.resetSizes();\n    this.transformX = this.clampTransformX(this.transformX);\n    this.transformY = this.clampTransformY(this.transformY);\n    this.setVisibleRange();\n    this.setPingStatus();\n    if (this.keyManager) {\n      this.keyManager.updateActiveItem(this.keyManager.activeItemIndex);\n      if (this.keyManager.activeItem) {\n        this.scrollToTab(this.keyManager.activeItem);\n      }\n    }\n  }\n  resetSizes() {\n    this.addButtonWidth = this.addBtnRef ? this.addBtnRef.getElementWidth() : 0;\n    this.addButtonHeight = this.addBtnRef ? this.addBtnRef.getElementHeight() : 0;\n    this.operationWidth = this.operationRef.getElementWidth();\n    this.operationHeight = this.operationRef.getElementHeight();\n    this.wrapperWidth = this.navWarpRef.nativeElement.offsetWidth || 0;\n    this.wrapperHeight = this.navWarpRef.nativeElement.offsetHeight || 0;\n    this.scrollListHeight = this.navListRef.nativeElement.offsetHeight || 0;\n    this.scrollListWidth = this.navListRef.nativeElement.offsetWidth || 0;\n  }\n  alignInkBarToSelectedTab() {\n    const selectedItem = this.items && this.items.length ? this.items.toArray()[this.selectedIndex] : null;\n    const selectedItemElement = selectedItem ? selectedItem.elementRef.nativeElement : null;\n    if (selectedItemElement) {\n      /**\n       * .ant-tabs-nav-list - Target offset parent element\n       *   └──.ant-tabs-tab\n       *        └──.ant-tabs-tab-btn - Currently focused element\n       */\n      this.inkBar.alignToElement(selectedItemElement.parentElement);\n    }\n  }\n  setPingStatus() {\n    const ping = {\n      top: false,\n      right: false,\n      bottom: false,\n      left: false\n    };\n    const navWarp = this.navWarpRef.nativeElement;\n    if (this.position === 'horizontal') {\n      if (this.getLayoutDirection() === 'rtl') {\n        ping.right = this.transformX > 0;\n        ping.left = this.transformX + this.wrapperWidth < this.scrollListWidth;\n      } else {\n        ping.left = this.transformX < 0;\n        ping.right = -this.transformX + this.wrapperWidth < this.scrollListWidth;\n      }\n    } else {\n      ping.top = this.transformY < 0;\n      ping.bottom = -this.transformY + this.wrapperHeight < this.scrollListHeight;\n    }\n    Object.keys(ping).forEach(pos => {\n      const className = `ant-tabs-nav-wrap-ping-${pos}`;\n      if (ping[pos]) {\n        navWarp.classList.add(className);\n      } else {\n        navWarp.classList.remove(className);\n      }\n    });\n  }\n  setVisibleRange() {\n    let unit;\n    let position;\n    let transformSize;\n    let basicSize;\n    let tabContentSize;\n    let addSize;\n    const tabs = this.items.toArray();\n    const DEFAULT_SIZE = {\n      width: 0,\n      height: 0,\n      left: 0,\n      top: 0,\n      right: 0\n    };\n    const getOffset = index => {\n      let offset;\n      const size = tabs[index] || DEFAULT_SIZE;\n      if (position === 'right') {\n        offset = tabs[0].left + tabs[0].width - tabs[index].left - tabs[index].width;\n      } else {\n        offset = size[position];\n      }\n      return offset;\n    };\n    if (this.position === 'horizontal') {\n      unit = 'width';\n      basicSize = this.wrapperWidth;\n      tabContentSize = this.scrollListWidth - (this.hiddenItems.length ? this.operationWidth : 0);\n      addSize = this.addButtonWidth;\n      transformSize = Math.abs(this.transformX);\n      if (this.getLayoutDirection() === 'rtl') {\n        position = 'right';\n        this.pingRight = this.transformX > 0;\n        this.pingLeft = this.transformX + this.wrapperWidth < this.scrollListWidth;\n      } else {\n        this.pingLeft = this.transformX < 0;\n        this.pingRight = -this.transformX + this.wrapperWidth < this.scrollListWidth;\n        position = 'left';\n      }\n    } else {\n      unit = 'height';\n      basicSize = this.wrapperHeight;\n      tabContentSize = this.scrollListHeight - (this.hiddenItems.length ? this.operationHeight : 0);\n      addSize = this.addButtonHeight;\n      position = 'top';\n      transformSize = -this.transformY;\n      this.pingTop = this.transformY < 0;\n      this.pingBottom = -this.transformY + this.wrapperHeight < this.scrollListHeight;\n    }\n    let mergedBasicSize = basicSize;\n    if (tabContentSize + addSize > basicSize) {\n      mergedBasicSize = basicSize - addSize;\n    }\n    if (!tabs.length) {\n      this.hiddenItems = [];\n      this.cdr.markForCheck();\n      return;\n    }\n    const len = tabs.length;\n    let endIndex = len;\n    for (let i = 0; i < len; i += 1) {\n      const offset = getOffset(i);\n      const size = tabs[i] || DEFAULT_SIZE;\n      if (offset + size[unit] > transformSize + mergedBasicSize) {\n        endIndex = i - 1;\n        break;\n      }\n    }\n    let startIndex = 0;\n    for (let i = len - 1; i >= 0; i -= 1) {\n      const offset = getOffset(i);\n      if (offset < transformSize) {\n        startIndex = i + 1;\n        break;\n      }\n    }\n    const startHiddenTabs = tabs.slice(0, startIndex);\n    const endHiddenTabs = tabs.slice(endIndex + 1);\n    this.hiddenItems = [...startHiddenTabs, ...endHiddenTabs];\n    this.cdr.markForCheck();\n  }\n  getLayoutDirection() {\n    return this.dir && this.dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  setTabFocus(_tabIndex) {}\n  ngOnChanges(changes) {\n    const {\n      position\n    } = changes;\n    // The first will be aligning in ngAfterViewInit\n    if (position && !position.isFirstChange()) {\n      this.alignInkBarToSelectedTab();\n      this.lockAnimation();\n      this.updateScrollListPosition();\n    }\n  }\n  static {\n    this.ɵfac = function NzTabNavBarComponent_Factory(t) {\n      return new (t || NzTabNavBarComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i2$1.NzResizeObserver), i0.ɵɵdirectiveInject(i3$1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTabNavBarComponent,\n      selectors: [[\"nz-tabs-nav\"]],\n      contentQueries: function NzTabNavBarComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzTabNavItemDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      viewQuery: function NzTabNavBarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c2, 7);\n          i0.ɵɵviewQuery(_c3, 7);\n          i0.ɵɵviewQuery(NzTabNavOperationComponent, 7);\n          i0.ɵɵviewQuery(NzTabAddButtonComponent, 5);\n          i0.ɵɵviewQuery(NzTabsInkBarDirective, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.navWarpRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.navListRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.operationRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.addBtnRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inkBar = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-tabs-nav\"],\n      hostBindings: function NzTabNavBarComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function NzTabNavBarComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          });\n        }\n      },\n      inputs: {\n        position: \"position\",\n        addable: \"addable\",\n        hideBar: \"hideBar\",\n        addIcon: \"addIcon\",\n        inkBarAnimated: \"inkBarAnimated\",\n        extraTemplate: \"extraTemplate\",\n        selectedIndex: \"selectedIndex\"\n      },\n      outputs: {\n        indexFocused: \"indexFocused\",\n        selectFocusedIndex: \"selectFocusedIndex\",\n        addClicked: \"addClicked\",\n        tabScroll: \"tabScroll\"\n      },\n      exportAs: [\"nzTabsNav\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c4,\n      decls: 9,\n      vars: 16,\n      consts: [[\"navWarp\", \"\"], [\"navList\", \"\"], [1, \"ant-tabs-nav-wrap\"], [\"nzTabScrollList\", \"\", \"role\", \"tablist\", 1, \"ant-tabs-nav-list\", 3, \"offsetChange\", \"tabScroll\"], [\"role\", \"tab\", \"nz-tab-add-button\", \"\", 3, \"addIcon\", \"click\", 4, \"ngIf\"], [\"nz-tabs-ink-bar\", \"\", 3, \"hidden\", \"position\", \"animated\"], [3, \"addClicked\", \"selected\", \"addIcon\", \"addable\", \"items\"], [\"class\", \"ant-tabs-extra-content\", 4, \"ngIf\"], [\"role\", \"tab\", \"nz-tab-add-button\", \"\", 3, \"click\", \"addIcon\"], [1, \"ant-tabs-extra-content\"], [3, \"ngTemplateOutlet\"]],\n      template: function NzTabNavBarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 2, 0)(2, \"div\", 3, 1);\n          i0.ɵɵlistener(\"offsetChange\", function NzTabNavBarComponent_Template_div_offsetChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onOffsetChange($event));\n          })(\"tabScroll\", function NzTabNavBarComponent_Template_div_tabScroll_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.tabScroll.emit($event));\n          });\n          i0.ɵɵprojection(4);\n          i0.ɵɵtemplate(5, NzTabNavBarComponent_button_5_Template, 1, 2, \"button\", 4);\n          i0.ɵɵelement(6, \"div\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"nz-tab-nav-operation\", 6);\n          i0.ɵɵlistener(\"addClicked\", function NzTabNavBarComponent_Template_nz_tab_nav_operation_addClicked_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.addClicked.emit());\n          })(\"selected\", function NzTabNavBarComponent_Template_nz_tab_nav_operation_selected_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectedFromMenu($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, NzTabNavBarComponent_div_8_Template, 2, 1, \"div\", 7);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-tabs-nav-wrap-ping-left\", ctx.pingLeft)(\"ant-tabs-nav-wrap-ping-right\", ctx.pingRight)(\"ant-tabs-nav-wrap-ping-top\", ctx.pingTop)(\"ant-tabs-nav-wrap-ping-bottom\", ctx.pingBottom);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"hidden\", ctx.hideBar)(\"position\", ctx.position)(\"animated\", ctx.inkBarAnimated);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"addIcon\", ctx.addIcon)(\"addable\", ctx.addable)(\"items\", ctx.hiddenItems);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.extraTemplate);\n        }\n      },\n      dependencies: [NzTabScrollListDirective, NgIf, NzTabAddButtonComponent, NzTabsInkBarDirective, NzTabNavOperationComponent, NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabNavBarComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-tabs-nav',\n      exportAs: 'nzTabsNav',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <div\n      class=\"ant-tabs-nav-wrap\"\n      [class.ant-tabs-nav-wrap-ping-left]=\"pingLeft\"\n      [class.ant-tabs-nav-wrap-ping-right]=\"pingRight\"\n      [class.ant-tabs-nav-wrap-ping-top]=\"pingTop\"\n      [class.ant-tabs-nav-wrap-ping-bottom]=\"pingBottom\"\n      #navWarp\n    >\n      <div\n        class=\"ant-tabs-nav-list\"\n        #navList\n        nzTabScrollList\n        (offsetChange)=\"onOffsetChange($event)\"\n        (tabScroll)=\"tabScroll.emit($event)\"\n        role=\"tablist\"\n      >\n        <ng-content></ng-content>\n        <button\n          role=\"tab\"\n          [attr.tabindex]=\"-1\"\n          *ngIf=\"showAddButton\"\n          nz-tab-add-button\n          [addIcon]=\"addIcon\"\n          (click)=\"addClicked.emit()\"\n        ></button>\n        <div nz-tabs-ink-bar [hidden]=\"hideBar\" [position]=\"position\" [animated]=\"inkBarAnimated\"></div>\n      </div>\n    </div>\n    <nz-tab-nav-operation\n      (addClicked)=\"addClicked.emit()\"\n      (selected)=\"onSelectedFromMenu($event)\"\n      [addIcon]=\"addIcon\"\n      [addable]=\"addable\"\n      [items]=\"hiddenItems\"\n    ></nz-tab-nav-operation>\n    <div class=\"ant-tabs-extra-content\" *ngIf=\"extraTemplate\">\n      <ng-template [ngTemplateOutlet]=\"extraTemplate\"></ng-template>\n    </div>\n  `,\n      host: {\n        class: 'ant-tabs-nav',\n        '(keydown)': 'handleKeydown($event)'\n      },\n      imports: [NzTabScrollListDirective, NgIf, NzTabAddButtonComponent, NzTabsInkBarDirective, NzTabNavOperationComponent, NgTemplateOutlet],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$1.ViewportRuler\n  }, {\n    type: i2$1.NzResizeObserver\n  }, {\n    type: i3$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    indexFocused: [{\n      type: Output\n    }],\n    selectFocusedIndex: [{\n      type: Output\n    }],\n    addClicked: [{\n      type: Output\n    }],\n    tabScroll: [{\n      type: Output\n    }],\n    position: [{\n      type: Input\n    }],\n    addable: [{\n      type: Input\n    }],\n    hideBar: [{\n      type: Input\n    }],\n    addIcon: [{\n      type: Input\n    }],\n    inkBarAnimated: [{\n      type: Input\n    }],\n    extraTemplate: [{\n      type: Input\n    }],\n    selectedIndex: [{\n      type: Input\n    }],\n    navWarpRef: [{\n      type: ViewChild,\n      args: ['navWarp', {\n        static: true\n      }]\n    }],\n    navListRef: [{\n      type: ViewChild,\n      args: ['navList', {\n        static: true\n      }]\n    }],\n    operationRef: [{\n      type: ViewChild,\n      args: [NzTabNavOperationComponent, {\n        static: true\n      }]\n    }],\n    addBtnRef: [{\n      type: ViewChild,\n      args: [NzTabAddButtonComponent, {\n        static: false\n      }]\n    }],\n    inkBar: [{\n      type: ViewChild,\n      args: [NzTabsInkBarDirective, {\n        static: true\n      }]\n    }],\n    items: [{\n      type: ContentChildren,\n      args: [NzTabNavItemDirective, {\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTabBodyComponent {\n  constructor() {\n    this.content = null;\n    this.active = false;\n    this.animated = true;\n    this.forceRender = false;\n    /**\n     * If this tab is ever activated, then the content should always be rendered.\n     */\n    this.hasBeenActive = false;\n  }\n  ngOnChanges(changes) {\n    const {\n      active\n    } = changes;\n    if (active?.currentValue) {\n      this.hasBeenActive = true;\n    }\n  }\n  static {\n    this.ɵfac = function NzTabBodyComponent_Factory(t) {\n      return new (t || NzTabBodyComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTabBodyComponent,\n      selectors: [[\"\", \"nz-tab-body\", \"\"]],\n      hostAttrs: [1, \"ant-tabs-tabpane\"],\n      hostVars: 10,\n      hostBindings: function NzTabBodyComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@tabSwitchMotion\", ctx.active ? \"enter\" : \"leave\")(\"@.disabled\", !ctx.animated);\n          i0.ɵɵattribute(\"tabindex\", ctx.active ? 0 : -1)(\"aria-hidden\", !ctx.active);\n          i0.ɵɵstyleProp(\"overflow-y\", ctx.animated ? ctx.active ? null : \"none\" : null);\n          i0.ɵɵclassProp(\"ant-tabs-tabpane-active\", ctx.active)(\"ant-tabs-tabpane-hidden\", ctx.animated ? null : !ctx.active);\n        }\n      },\n      inputs: {\n        content: \"content\",\n        active: \"active\",\n        animated: \"animated\",\n        forceRender: \"forceRender\"\n      },\n      exportAs: [\"nzTabBody\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c5,\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"ngIf\"], [3, \"ngTemplateOutlet\"]],\n      template: function NzTabBodyComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzTabBodyComponent_ng_container_0_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.hasBeenActive || ctx.forceRender);\n        }\n      },\n      dependencies: [NgIf, NgTemplateOutlet],\n      encapsulation: 2,\n      data: {\n        animation: [tabSwitchMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabBodyComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-tab-body]',\n      exportAs: 'nzTabBody',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-container *ngIf=\"hasBeenActive || forceRender\">\n      <ng-template [ngTemplateOutlet]=\"content\"></ng-template>\n    </ng-container>\n  `,\n      host: {\n        class: 'ant-tabs-tabpane',\n        '[class.ant-tabs-tabpane-active]': 'active',\n        '[class.ant-tabs-tabpane-hidden]': 'animated ? null : !active',\n        '[attr.tabindex]': 'active ? 0 : -1',\n        '[attr.aria-hidden]': '!active',\n        '[style.overflow-y]': 'animated ? active ? null : \"none\" : null',\n        '[@tabSwitchMotion]': `active ? 'enter' : 'leave'`,\n        '[@.disabled]': `!animated`\n      },\n      imports: [NgIf, NgTemplateOutlet],\n      standalone: true,\n      animations: [tabSwitchMotion]\n    }]\n  }], null, {\n    content: [{\n      type: Input\n    }],\n    active: [{\n      type: Input\n    }],\n    animated: [{\n      type: Input\n    }],\n    forceRender: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTabCloseButtonComponent {\n  constructor() {\n    this.closeIcon = 'close';\n  }\n  static {\n    this.ɵfac = function NzTabCloseButtonComponent_Factory(t) {\n      return new (t || NzTabCloseButtonComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTabCloseButtonComponent,\n      selectors: [[\"nz-tab-close-button\"], [\"button\", \"nz-tab-close-button\", \"\"]],\n      hostAttrs: [\"aria-label\", \"Close tab\", \"type\", \"button\", 1, \"ant-tabs-tab-remove\"],\n      inputs: {\n        closeIcon: \"closeIcon\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"nzStringTemplateOutlet\"], [\"nz-icon\", \"\", \"nzTheme\", \"outline\", 3, \"nzType\"]],\n      template: function NzTabCloseButtonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzTabCloseButtonComponent_ng_container_0_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.closeIcon);\n        }\n      },\n      dependencies: [NzOutletModule, i1.NzStringTemplateOutletDirective, NzIconModule, i2.NzIconDirective],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabCloseButtonComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-tab-close-button, button[nz-tab-close-button]',\n      template: `\n    <ng-container *nzStringTemplateOutlet=\"closeIcon; let icon\">\n      <span nz-icon [nzType]=\"icon\" nzTheme=\"outline\"></span>\n    </ng-container>\n  `,\n      host: {\n        class: 'ant-tabs-tab-remove',\n        'aria-label': 'Close tab',\n        type: 'button'\n      },\n      imports: [NzOutletModule, NzIconModule],\n      standalone: true\n    }]\n  }], () => [], {\n    closeIcon: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Fix https://github.com/angular/angular/issues/8563\n */\nclass NzTabLinkTemplateDirective {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n  }\n  static {\n    this.ɵfac = function NzTabLinkTemplateDirective_Factory(t) {\n      return new (t || NzTabLinkTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef, 1));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTabLinkTemplateDirective,\n      selectors: [[\"ng-template\", \"nzTabLink\", \"\"]],\n      exportAs: [\"nzTabLinkTemplate\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabLinkTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[nzTabLink]',\n      exportAs: 'nzTabLinkTemplate',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef,\n    decorators: [{\n      type: Host\n    }]\n  }], null);\n})();\n/**\n * This component is for catching `routerLink` directive.\n */\nclass NzTabLinkDirective {\n  constructor(elementRef, routerLink) {\n    this.elementRef = elementRef;\n    this.routerLink = routerLink;\n  }\n  static {\n    this.ɵfac = function NzTabLinkDirective_Factory(t) {\n      return new (t || NzTabLinkDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$2.RouterLink, 10));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTabLinkDirective,\n      selectors: [[\"a\", \"nz-tab-link\", \"\"]],\n      exportAs: [\"nzTabLink\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabLinkDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'a[nz-tab-link]',\n      exportAs: 'nzTabLink',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1$2.RouterLink,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }]\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass NzTabDirective {\n  static {\n    this.ɵfac = function NzTabDirective_Factory(t) {\n      return new (t || NzTabDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTabDirective,\n      selectors: [[\"\", \"nz-tab\", \"\"]],\n      exportAs: [\"nzTab\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-tab]',\n      exportAs: 'nzTab',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Used to provide a tab set to a tab without causing a circular dependency.\n */\nconst NZ_TAB_SET = new InjectionToken('NZ_TAB_SET');\nclass NzTabComponent {\n  get content() {\n    return this.template || this.contentTemplate;\n  }\n  get label() {\n    return this.nzTitle || this.nzTabLinkTemplateDirective?.templateRef;\n  }\n  constructor(closestTabSet) {\n    this.closestTabSet = closestTabSet;\n    this.nzTitle = '';\n    this.nzClosable = false;\n    this.nzCloseIcon = 'close';\n    this.nzDisabled = false;\n    this.nzForceRender = false;\n    this.nzSelect = new EventEmitter();\n    this.nzDeselect = new EventEmitter();\n    this.nzClick = new EventEmitter();\n    this.nzContextmenu = new EventEmitter();\n    this.template = null;\n    this.isActive = false;\n    this.position = null;\n    this.origin = null;\n    this.stateChanges = new Subject();\n  }\n  ngOnChanges(changes) {\n    const {\n      nzTitle,\n      nzDisabled,\n      nzForceRender\n    } = changes;\n    if (nzTitle || nzDisabled || nzForceRender) {\n      this.stateChanges.next();\n    }\n  }\n  ngOnDestroy() {\n    this.stateChanges.complete();\n  }\n  static {\n    this.ɵfac = function NzTabComponent_Factory(t) {\n      return new (t || NzTabComponent)(i0.ɵɵdirectiveInject(NZ_TAB_SET));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTabComponent,\n      selectors: [[\"nz-tab\"]],\n      contentQueries: function NzTabComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzTabLinkTemplateDirective, 5);\n          i0.ɵɵcontentQuery(dirIndex, NzTabDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NzTabLinkDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzTabLinkTemplateDirective = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.linkDirective = _t.first);\n        }\n      },\n      viewQuery: function NzTabComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c6, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentTemplate = _t.first);\n        }\n      },\n      inputs: {\n        nzTitle: \"nzTitle\",\n        nzClosable: \"nzClosable\",\n        nzCloseIcon: \"nzCloseIcon\",\n        nzDisabled: \"nzDisabled\",\n        nzForceRender: \"nzForceRender\"\n      },\n      outputs: {\n        nzSelect: \"nzSelect\",\n        nzDeselect: \"nzDeselect\",\n        nzClick: \"nzClick\",\n        nzContextmenu: \"nzContextmenu\"\n      },\n      exportAs: [\"nzTab\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c8,\n      decls: 4,\n      vars: 0,\n      consts: [[\"tabLinkTemplate\", \"\"], [\"contentTemplate\", \"\"]],\n      template: function NzTabComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c7);\n          i0.ɵɵtemplate(0, NzTabComponent_ng_template_0_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, NzTabComponent_ng_template_2_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzTabComponent.prototype, \"nzClosable\", void 0);\n__decorate([InputBoolean()], NzTabComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzTabComponent.prototype, \"nzForceRender\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-tab',\n      exportAs: 'nzTab',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-template #tabLinkTemplate>\n      <ng-content select=\"[nz-tab-link]\"></ng-content>\n    </ng-template>\n    <ng-template #contentTemplate><ng-content></ng-content></ng-template>\n  `,\n      standalone: true\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NZ_TAB_SET]\n    }]\n  }], {\n    nzTitle: [{\n      type: Input\n    }],\n    nzClosable: [{\n      type: Input\n    }],\n    nzCloseIcon: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzForceRender: [{\n      type: Input\n    }],\n    nzSelect: [{\n      type: Output\n    }],\n    nzDeselect: [{\n      type: Output\n    }],\n    nzClick: [{\n      type: Output\n    }],\n    nzContextmenu: [{\n      type: Output\n    }],\n    nzTabLinkTemplateDirective: [{\n      type: ContentChild,\n      args: [NzTabLinkTemplateDirective, {\n        static: false\n      }]\n    }],\n    template: [{\n      type: ContentChild,\n      args: [NzTabDirective, {\n        static: false,\n        read: TemplateRef\n      }]\n    }],\n    linkDirective: [{\n      type: ContentChild,\n      args: [NzTabLinkDirective, {\n        static: false\n      }]\n    }],\n    contentTemplate: [{\n      type: ViewChild,\n      args: ['contentTemplate', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTabChangeEvent {}\nconst NZ_CONFIG_MODULE_NAME = 'tabs';\nlet nextId = 0;\nclass NzTabSetComponent {\n  get nzSelectedIndex() {\n    return this.selectedIndex;\n  }\n  set nzSelectedIndex(value) {\n    this.indexToSelect = coerceNumberProperty(value, null);\n  }\n  get position() {\n    return ['top', 'bottom'].indexOf(this.nzTabPosition) === -1 ? 'vertical' : 'horizontal';\n  }\n  get addable() {\n    return this.nzType === 'editable-card' && !this.nzHideAdd;\n  }\n  get closable() {\n    return this.nzType === 'editable-card';\n  }\n  get line() {\n    return this.nzType === 'line';\n  }\n  get inkBarAnimated() {\n    return this.line && (typeof this.nzAnimated === 'boolean' ? this.nzAnimated : this.nzAnimated.inkBar);\n  }\n  get tabPaneAnimated() {\n    return typeof this.nzAnimated === 'boolean' ? this.nzAnimated : this.nzAnimated.tabPane;\n  }\n  constructor(nzConfigService, ngZone, cdr, directionality, router) {\n    this.nzConfigService = nzConfigService;\n    this.ngZone = ngZone;\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.router = router;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzTabPosition = 'top';\n    this.nzCanDeactivate = null;\n    this.nzAddIcon = 'plus';\n    this.nzTabBarStyle = null;\n    this.nzType = 'line';\n    this.nzSize = 'default';\n    this.nzAnimated = true;\n    this.nzTabBarGutter = undefined;\n    this.nzHideAdd = false;\n    this.nzCentered = false;\n    this.nzHideAll = false;\n    this.nzLinkRouter = false;\n    this.nzLinkExact = true;\n    this.nzSelectChange = new EventEmitter(true);\n    this.nzSelectedIndexChange = new EventEmitter();\n    this.nzTabListScroll = new EventEmitter();\n    this.nzClose = new EventEmitter();\n    this.nzAdd = new EventEmitter();\n    // Pick up only direct descendants under ivy rendering engine\n    // We filter out only the tabs that belong to this tab set in `tabs`.\n    this.allTabs = new QueryList();\n    // All the direct tabs for this tab set\n    this.tabs = new QueryList();\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.indexToSelect = 0;\n    this.selectedIndex = null;\n    this.tabLabelSubscription = Subscription.EMPTY;\n    this.tabsSubscription = Subscription.EMPTY;\n    this.canDeactivateSubscription = Subscription.EMPTY;\n    this.tabSetId = nextId++;\n  }\n  ngOnInit() {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.tabs.destroy();\n    this.tabLabelSubscription.unsubscribe();\n    this.tabsSubscription.unsubscribe();\n    this.canDeactivateSubscription.unsubscribe();\n  }\n  ngAfterContentInit() {\n    this.ngZone.runOutsideAngular(() => {\n      Promise.resolve().then(() => this.setUpRouter());\n    });\n    this.subscribeToTabLabels();\n    this.subscribeToAllTabChanges();\n    // Subscribe to changes in the amount of tabs, in order to be\n    // able to re-render the content as new tabs are added or removed.\n    this.tabsSubscription = this.tabs.changes.subscribe(() => {\n      const indexToSelect = this.clampTabIndex(this.indexToSelect);\n      // Maintain the previously-selected tab if a new tab is added or removed and there is no\n      // explicit change that selects a different tab.\n      if (indexToSelect === this.selectedIndex) {\n        const tabs = this.tabs.toArray();\n        for (let i = 0; i < tabs.length; i++) {\n          if (tabs[i].isActive) {\n            // Assign both to the `indexToSelect` and `selectedIndex` so we don't fire a changed\n            // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n            // adding a tab within the `nzSelectedIndexChange` event.\n            this.indexToSelect = this.selectedIndex = i;\n            break;\n          }\n        }\n      }\n      this.subscribeToTabLabels();\n      this.cdr.markForCheck();\n    });\n  }\n  ngAfterContentChecked() {\n    // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n    // the amount of tabs changes before the actual change detection runs.\n    const indexToSelect = this.indexToSelect = this.clampTabIndex(this.indexToSelect);\n    // If there is a change in selected index, emit a change event. Should not trigger if\n    // the selected index has not yet been initialized.\n    if (this.selectedIndex !== indexToSelect) {\n      const isFirstRun = this.selectedIndex == null;\n      if (!isFirstRun) {\n        this.nzSelectChange.emit(this.createChangeEvent(indexToSelect));\n      }\n      // Changing these values after change detection has run\n      // since the checked content may contain references to them.\n      Promise.resolve().then(() => {\n        this.tabs.forEach((tab, index) => tab.isActive = index === indexToSelect);\n        if (!isFirstRun) {\n          this.nzSelectedIndexChange.emit(indexToSelect);\n        }\n      });\n    }\n    // Setup the position for each tab and optionally setup an origin on the next selected tab.\n    this.tabs.forEach((tab, index) => {\n      tab.position = index - indexToSelect;\n      // If there is already a selected tab, then set up an origin for the next selected tab\n      // if it doesn't have one already.\n      if (this.selectedIndex != null && tab.position === 0 && !tab.origin) {\n        tab.origin = indexToSelect - this.selectedIndex;\n      }\n    });\n    if (this.selectedIndex !== indexToSelect) {\n      this.selectedIndex = indexToSelect;\n      this.cdr.markForCheck();\n    }\n  }\n  onClose(index, e) {\n    e.preventDefault();\n    e.stopPropagation();\n    this.nzClose.emit({\n      index\n    });\n  }\n  onAdd() {\n    this.nzAdd.emit();\n  }\n  clampTabIndex(index) {\n    return Math.min(this.tabs.length - 1, Math.max(index || 0, 0));\n  }\n  createChangeEvent(index) {\n    const event = new NzTabChangeEvent();\n    event.index = index;\n    if (this.tabs && this.tabs.length) {\n      event.tab = this.tabs.toArray()[index];\n      this.tabs.forEach((tab, i) => {\n        if (i !== index) {\n          tab.nzDeselect.emit();\n        }\n      });\n      event.tab.nzSelect.emit();\n    }\n    return event;\n  }\n  subscribeToTabLabels() {\n    if (this.tabLabelSubscription) {\n      this.tabLabelSubscription.unsubscribe();\n    }\n    this.tabLabelSubscription = merge(...this.tabs.map(tab => tab.stateChanges)).subscribe(() => this.cdr.markForCheck());\n  }\n  subscribeToAllTabChanges() {\n    this.allTabs.changes.pipe(startWith(this.allTabs)).subscribe(tabs => {\n      this.tabs.reset(tabs.filter(tab => tab.closestTabSet === this));\n      this.tabs.notifyOnChanges();\n    });\n  }\n  canDeactivateFun(pre, next) {\n    if (typeof this.nzCanDeactivate === 'function') {\n      const observable = wrapIntoObservable(this.nzCanDeactivate(pre, next));\n      return observable.pipe(first(), takeUntil(this.destroy$));\n    } else {\n      return of(true);\n    }\n  }\n  clickNavItem(tab, index, e) {\n    if (!tab.nzDisabled) {\n      // ignore nzCanDeactivate\n      tab.nzClick.emit();\n      if (!this.isRouterLinkClickEvent(index, e)) {\n        this.setSelectedIndex(index);\n      }\n    }\n  }\n  isRouterLinkClickEvent(index, event) {\n    const target = event.target;\n    if (this.nzLinkRouter) {\n      return !!this.tabs.toArray()[index]?.linkDirective?.elementRef.nativeElement.contains(target);\n    } else {\n      return false;\n    }\n  }\n  contextmenuNavItem(tab, e) {\n    if (!tab.nzDisabled) {\n      // ignore nzCanDeactivate\n      tab.nzContextmenu.emit(e);\n    }\n  }\n  setSelectedIndex(index) {\n    this.canDeactivateSubscription.unsubscribe();\n    this.canDeactivateSubscription = this.canDeactivateFun(this.selectedIndex, index).subscribe(can => {\n      if (can) {\n        this.nzSelectedIndex = index;\n        this.tabNavBarRef.focusIndex = index;\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  getTabIndex(tab, index) {\n    if (tab.nzDisabled) {\n      return null;\n    }\n    return this.selectedIndex === index ? 0 : -1;\n  }\n  getTabContentId(i) {\n    return `nz-tabs-${this.tabSetId}-tab-${i}`;\n  }\n  setUpRouter() {\n    if (this.nzLinkRouter) {\n      if (!this.router) {\n        throw new Error(`${PREFIX} you should import 'RouterModule' if you want to use 'nzLinkRouter'!`);\n      }\n      this.router.events.pipe(takeUntil(this.destroy$), filter(e => e instanceof NavigationEnd), startWith(true), delay(0)).subscribe(() => {\n        this.updateRouterActive();\n        this.cdr.markForCheck();\n      });\n    }\n  }\n  updateRouterActive() {\n    if (this.router.navigated) {\n      const index = this.findShouldActiveTabIndex();\n      if (index !== this.selectedIndex) {\n        this.setSelectedIndex(index);\n      }\n      this.nzHideAll = index === -1;\n    }\n  }\n  findShouldActiveTabIndex() {\n    const tabs = this.tabs.toArray();\n    const isActive = this.isLinkActive(this.router);\n    return tabs.findIndex(tab => {\n      const c = tab.linkDirective;\n      return c ? isActive(c.routerLink) : false;\n    });\n  }\n  isLinkActive(router) {\n    return link => link ? router.isActive(link.urlTree || '', {\n      paths: this.nzLinkExact ? 'exact' : 'subset',\n      queryParams: this.nzLinkExact ? 'exact' : 'subset',\n      fragment: 'ignored',\n      matrixParams: 'ignored'\n    }) : false;\n  }\n  static {\n    this.ɵfac = function NzTabSetComponent_Factory(t) {\n      return new (t || NzTabSetComponent)(i0.ɵɵdirectiveInject(i1$3.NzConfigService), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3$1.Directionality, 8), i0.ɵɵdirectiveInject(i1$2.Router, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTabSetComponent,\n      selectors: [[\"nz-tabset\"]],\n      contentQueries: function NzTabSetComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzTabComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.allTabs = _t);\n        }\n      },\n      viewQuery: function NzTabSetComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(NzTabNavBarComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabNavBarRef = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-tabs\"],\n      hostVars: 24,\n      hostBindings: function NzTabSetComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-tabs-card\", ctx.nzType === \"card\" || ctx.nzType === \"editable-card\")(\"ant-tabs-editable\", ctx.nzType === \"editable-card\")(\"ant-tabs-editable-card\", ctx.nzType === \"editable-card\")(\"ant-tabs-centered\", ctx.nzCentered)(\"ant-tabs-rtl\", ctx.dir === \"rtl\")(\"ant-tabs-top\", ctx.nzTabPosition === \"top\")(\"ant-tabs-bottom\", ctx.nzTabPosition === \"bottom\")(\"ant-tabs-left\", ctx.nzTabPosition === \"left\")(\"ant-tabs-right\", ctx.nzTabPosition === \"right\")(\"ant-tabs-default\", ctx.nzSize === \"default\")(\"ant-tabs-small\", ctx.nzSize === \"small\")(\"ant-tabs-large\", ctx.nzSize === \"large\");\n        }\n      },\n      inputs: {\n        nzSelectedIndex: \"nzSelectedIndex\",\n        nzTabPosition: \"nzTabPosition\",\n        nzTabBarExtraContent: \"nzTabBarExtraContent\",\n        nzCanDeactivate: \"nzCanDeactivate\",\n        nzAddIcon: \"nzAddIcon\",\n        nzTabBarStyle: \"nzTabBarStyle\",\n        nzType: \"nzType\",\n        nzSize: \"nzSize\",\n        nzAnimated: \"nzAnimated\",\n        nzTabBarGutter: \"nzTabBarGutter\",\n        nzHideAdd: \"nzHideAdd\",\n        nzCentered: \"nzCentered\",\n        nzHideAll: \"nzHideAll\",\n        nzLinkRouter: \"nzLinkRouter\",\n        nzLinkExact: \"nzLinkExact\"\n      },\n      outputs: {\n        nzSelectChange: \"nzSelectChange\",\n        nzSelectedIndexChange: \"nzSelectedIndexChange\",\n        nzTabListScroll: \"nzTabListScroll\",\n        nzClose: \"nzClose\",\n        nzAdd: \"nzAdd\"\n      },\n      exportAs: [\"nzTabset\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NZ_TAB_SET,\n        useExisting: NzTabSetComponent\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 12,\n      consts: [[3, \"ngStyle\", \"selectedIndex\", \"inkBarAnimated\", \"addable\", \"addIcon\", \"hideBar\", \"position\", \"extraTemplate\", \"tabScroll\", \"selectFocusedIndex\", \"addClicked\", 4, \"ngIf\"], [1, \"ant-tabs-content-holder\"], [1, \"ant-tabs-content\"], [\"role\", \"tabpanel\", \"nz-tab-body\", \"\", 3, \"id\", \"active\", \"content\", \"forceRender\", \"animated\", 4, \"ngFor\", \"ngForOf\"], [3, \"tabScroll\", \"selectFocusedIndex\", \"addClicked\", \"ngStyle\", \"selectedIndex\", \"inkBarAnimated\", \"addable\", \"addIcon\", \"hideBar\", \"position\", \"extraTemplate\"], [\"class\", \"ant-tabs-tab\", 3, \"margin-right\", \"margin-bottom\", \"ant-tabs-tab-active\", \"ant-tabs-tab-disabled\", \"click\", \"contextmenu\", 4, \"ngFor\", \"ngForOf\"], [1, \"ant-tabs-tab\", 3, \"click\", \"contextmenu\"], [\"type\", \"button\", \"role\", \"tab\", \"nzTabNavItem\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"ant-tabs-tab-btn\", 3, \"id\", \"disabled\", \"tab\", \"active\"], [4, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"], [\"type\", \"button\", \"nz-tab-close-button\", \"\", 3, \"closeIcon\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"nz-tab-close-button\", \"\", 3, \"click\", \"closeIcon\"], [\"role\", \"tabpanel\", \"nz-tab-body\", \"\", 3, \"id\", \"active\", \"content\", \"forceRender\", \"animated\"]],\n      template: function NzTabSetComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzTabSetComponent_nz_tabs_nav_0_Template, 2, 9, \"nz-tabs-nav\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, NzTabSetComponent_div_3_Template, 1, 6, \"div\", 3);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.tabs.length || ctx.addable);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"ant-tabs-content-top\", ctx.nzTabPosition === \"top\")(\"ant-tabs-content-bottom\", ctx.nzTabPosition === \"bottom\")(\"ant-tabs-content-left\", ctx.nzTabPosition === \"left\")(\"ant-tabs-content-right\", ctx.nzTabPosition === \"right\")(\"ant-tabs-content-animated\", ctx.tabPaneAnimated);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n        }\n      },\n      dependencies: [NzTabNavBarComponent, NgIf, NgStyle, NgForOf, NzTabNavItemDirective, A11yModule, i4.CdkMonitorFocus, NzOutletModule, i1.NzStringTemplateOutletDirective, NzTabCloseButtonComponent, NzTabBodyComponent],\n      encapsulation: 2\n    });\n  }\n}\n__decorate([WithConfig()], NzTabSetComponent.prototype, \"nzType\", void 0);\n__decorate([WithConfig()], NzTabSetComponent.prototype, \"nzSize\", void 0);\n__decorate([WithConfig()], NzTabSetComponent.prototype, \"nzAnimated\", void 0);\n__decorate([WithConfig()], NzTabSetComponent.prototype, \"nzTabBarGutter\", void 0);\n__decorate([InputBoolean()], NzTabSetComponent.prototype, \"nzHideAdd\", void 0);\n__decorate([InputBoolean()], NzTabSetComponent.prototype, \"nzCentered\", void 0);\n__decorate([InputBoolean()], NzTabSetComponent.prototype, \"nzHideAll\", void 0);\n__decorate([InputBoolean()], NzTabSetComponent.prototype, \"nzLinkRouter\", void 0);\n__decorate([InputBoolean()], NzTabSetComponent.prototype, \"nzLinkExact\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabSetComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-tabset',\n      exportAs: 'nzTabset',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [{\n        provide: NZ_TAB_SET,\n        useExisting: NzTabSetComponent\n      }],\n      template: `\n    <nz-tabs-nav\n      *ngIf=\"tabs.length || addable\"\n      [ngStyle]=\"nzTabBarStyle\"\n      [selectedIndex]=\"nzSelectedIndex || 0\"\n      [inkBarAnimated]=\"inkBarAnimated\"\n      [addable]=\"addable\"\n      [addIcon]=\"nzAddIcon\"\n      [hideBar]=\"nzHideAll\"\n      [position]=\"position\"\n      [extraTemplate]=\"nzTabBarExtraContent\"\n      (tabScroll)=\"nzTabListScroll.emit($event)\"\n      (selectFocusedIndex)=\"setSelectedIndex($event)\"\n      (addClicked)=\"onAdd()\"\n    >\n      <div\n        class=\"ant-tabs-tab\"\n        [style.margin-right.px]=\"position === 'horizontal' ? nzTabBarGutter : null\"\n        [style.margin-bottom.px]=\"position === 'vertical' ? nzTabBarGutter : null\"\n        [class.ant-tabs-tab-active]=\"nzSelectedIndex === i\"\n        [class.ant-tabs-tab-disabled]=\"tab.nzDisabled\"\n        (click)=\"clickNavItem(tab, i, $event)\"\n        (contextmenu)=\"contextmenuNavItem(tab, $event)\"\n        *ngFor=\"let tab of tabs; let i = index\"\n      >\n        <button\n          type=\"button\"\n          role=\"tab\"\n          [id]=\"getTabContentId(i)\"\n          [attr.tabIndex]=\"getTabIndex(tab, i)\"\n          [attr.aria-disabled]=\"tab.nzDisabled\"\n          [attr.aria-selected]=\"nzSelectedIndex === i && !nzHideAll\"\n          [attr.aria-controls]=\"getTabContentId(i)\"\n          [disabled]=\"tab.nzDisabled\"\n          [tab]=\"tab\"\n          [active]=\"nzSelectedIndex === i\"\n          class=\"ant-tabs-tab-btn\"\n          nzTabNavItem\n          cdkMonitorElementFocus\n        >\n          <ng-container *nzStringTemplateOutlet=\"tab.label; context: { visible: true }\">{{ tab.label }}</ng-container>\n          <button\n            type=\"button\"\n            nz-tab-close-button\n            *ngIf=\"tab.nzClosable && closable && !tab.nzDisabled\"\n            [closeIcon]=\"tab.nzCloseIcon\"\n            (click)=\"onClose(i, $event)\"\n          ></button>\n        </button>\n      </div>\n    </nz-tabs-nav>\n    <div class=\"ant-tabs-content-holder\">\n      <div\n        class=\"ant-tabs-content\"\n        [class.ant-tabs-content-top]=\"nzTabPosition === 'top'\"\n        [class.ant-tabs-content-bottom]=\"nzTabPosition === 'bottom'\"\n        [class.ant-tabs-content-left]=\"nzTabPosition === 'left'\"\n        [class.ant-tabs-content-right]=\"nzTabPosition === 'right'\"\n        [class.ant-tabs-content-animated]=\"tabPaneAnimated\"\n      >\n        <div\n          role=\"tabpanel\"\n          [id]=\"getTabContentId(i)\"\n          [attr.aria-labelledby]=\"getTabContentId(i)\"\n          nz-tab-body\n          *ngFor=\"let tab of tabs; let i = index\"\n          [active]=\"nzSelectedIndex === i && !nzHideAll\"\n          [content]=\"tab.content\"\n          [forceRender]=\"tab.nzForceRender\"\n          [animated]=\"tabPaneAnimated\"\n        ></div>\n      </div>\n    </div>\n  `,\n      host: {\n        class: 'ant-tabs',\n        '[class.ant-tabs-card]': `nzType === 'card' || nzType === 'editable-card'`,\n        '[class.ant-tabs-editable]': `nzType === 'editable-card'`,\n        '[class.ant-tabs-editable-card]': `nzType === 'editable-card'`,\n        '[class.ant-tabs-centered]': `nzCentered`,\n        '[class.ant-tabs-rtl]': `dir === 'rtl'`,\n        '[class.ant-tabs-top]': `nzTabPosition === 'top'`,\n        '[class.ant-tabs-bottom]': `nzTabPosition === 'bottom'`,\n        '[class.ant-tabs-left]': `nzTabPosition === 'left'`,\n        '[class.ant-tabs-right]': `nzTabPosition === 'right'`,\n        '[class.ant-tabs-default]': `nzSize === 'default'`,\n        '[class.ant-tabs-small]': `nzSize === 'small'`,\n        '[class.ant-tabs-large]': `nzSize === 'large'`\n      },\n      imports: [NzTabNavBarComponent, NgIf, NgStyle, NgForOf, NzTabNavItemDirective, A11yModule, NzOutletModule, NzTabCloseButtonComponent, NzTabBodyComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$3.NzConfigService\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i3$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i1$2.Router,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzSelectedIndex: [{\n      type: Input\n    }],\n    nzTabPosition: [{\n      type: Input\n    }],\n    nzTabBarExtraContent: [{\n      type: Input\n    }],\n    nzCanDeactivate: [{\n      type: Input\n    }],\n    nzAddIcon: [{\n      type: Input\n    }],\n    nzTabBarStyle: [{\n      type: Input\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzAnimated: [{\n      type: Input\n    }],\n    nzTabBarGutter: [{\n      type: Input\n    }],\n    nzHideAdd: [{\n      type: Input\n    }],\n    nzCentered: [{\n      type: Input\n    }],\n    nzHideAll: [{\n      type: Input\n    }],\n    nzLinkRouter: [{\n      type: Input\n    }],\n    nzLinkExact: [{\n      type: Input\n    }],\n    nzSelectChange: [{\n      type: Output\n    }],\n    nzSelectedIndexChange: [{\n      type: Output\n    }],\n    nzTabListScroll: [{\n      type: Output\n    }],\n    nzClose: [{\n      type: Output\n    }],\n    nzAdd: [{\n      type: Output\n    }],\n    allTabs: [{\n      type: ContentChildren,\n      args: [NzTabComponent, {\n        descendants: true\n      }]\n    }],\n    tabNavBarRef: [{\n      type: ViewChild,\n      args: [NzTabNavBarComponent, {\n        static: false\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst DIRECTIVES = [NzTabSetComponent, NzTabComponent, NzTabNavBarComponent, NzTabNavItemDirective, NzTabsInkBarDirective, NzTabScrollListDirective, NzTabNavOperationComponent, NzTabAddButtonComponent, NzTabCloseButtonComponent, NzTabDirective, NzTabBodyComponent, NzTabLinkDirective, NzTabLinkTemplateDirective];\nclass NzTabsModule {\n  static {\n    this.ɵfac = function NzTabsModule_Factory(t) {\n      return new (t || NzTabsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzTabsModule,\n      imports: [NzTabSetComponent, NzTabComponent, NzTabNavBarComponent, NzTabNavItemDirective, NzTabsInkBarDirective, NzTabScrollListDirective, NzTabNavOperationComponent, NzTabAddButtonComponent, NzTabCloseButtonComponent, NzTabDirective, NzTabBodyComponent, NzTabLinkDirective, NzTabLinkTemplateDirective],\n      exports: [NzTabSetComponent, NzTabComponent, NzTabNavBarComponent, NzTabNavItemDirective, NzTabsInkBarDirective, NzTabScrollListDirective, NzTabNavOperationComponent, NzTabAddButtonComponent, NzTabCloseButtonComponent, NzTabDirective, NzTabBodyComponent, NzTabLinkDirective, NzTabLinkTemplateDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzTabSetComponent, NzTabNavBarComponent, NzTabNavOperationComponent, NzTabAddButtonComponent, NzTabCloseButtonComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTabsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [DIRECTIVES],\n      exports: [DIRECTIVES]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NZ_TAB_SET, NzTabChangeEvent, NzTabComponent, NzTabDirective, NzTabLinkDirective, NzTabLinkTemplateDirective, NzTabSetComponent, NzTabsModule, NzTabAddButtonComponent as ɵNzTabAddButtonComponent, NzTabBodyComponent as ɵNzTabBodyComponent, NzTabCloseButtonComponent as ɵNzTabCloseButtonComponent, NzTabNavBarComponent as ɵNzTabNavBarComponent, NzTabNavItemDirective as ɵNzTabNavItemDirective, NzTabNavOperationComponent as ɵNzTabNavOperationComponent, NzTabScrollListDirective as ɵNzTabScrollListDirective, NzTabsInkBarDirective as ɵNzTabsInkBarDirective };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,OAAO;AAAA,EACjC;AACF;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,UAAU;AACZ;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,SAAS;AACX;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,QAAQ,IAAI,OAAO,GAAG;AAAA,EACnD;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,SAAS,SAAS,oEAAoE;AAClG,YAAM,UAAa,cAAc,GAAG,EAAE;AACtC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,SAAS,OAAO,CAAC;AAAA,IAChD,CAAC,EAAE,eAAe,SAAS,wEAAwE,QAAQ;AACzG,YAAM,UAAa,cAAc,GAAG,EAAE;AACtC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,cAAc,SAAS,MAAM,CAAC;AAAA,IAC7D,CAAC;AACD,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,gBAAgB,CAAC;AACtG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,YAAY,wCAAwC,QAAQ,QAAQ;AACvE,IAAG,WAAW,cAAc,QAAQ,MAAM,EAAE,cAAc,QAAQ,QAAQ;AAC1E,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,QAAQ,IAAI,KAAK,EAAE,iCAAoC,gBAAgB,GAAG,GAAG,CAAC;AAAA,EACxH;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,MAAM,CAAC;AAC7E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,KAAK;AAAA,EACvC;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,uEAAuE;AACrG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,KAAK,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,OAAO;AAAA,EACzC;AACF;AACA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,iEAAiE;AAC/F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,KAAK,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,OAAO;AACvC,IAAG,YAAY,YAAY,EAAE;AAAA,EAC/B;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAAC;AACrE,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,eAAe,EAAE;AAC3F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,aAAa;AAAA,EACxD;AACF;AACA,IAAM,MAAM,CAAC,eAAe,EAAE;AAC9B,SAAS,yDAAyD,IAAI,KAAK;AAAC;AAC5E,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,eAAe,CAAC;AACjG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,OAAO;AAAA,EAClD;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,OAAO;AAAA,EACjC;AACF;AACA,IAAM,MAAM,CAAC,iBAAiB;AAC9B,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC,GAAG,GAAG;AAC3C,IAAM,MAAM,CAAC,iBAAiB,GAAG;AACjC,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,SAAS;AACX;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,EAAE;AAClC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK;AAAA,EACnC;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,gFAAgF,QAAQ;AACtH,MAAG,cAAc,GAAG;AACpB,YAAM,OAAU,cAAc,EAAE;AAChC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,QAAQ,MAAM,MAAM,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,EAAE;AAClC,IAAG,WAAW,aAAa,OAAO,WAAW;AAAA,EAC/C;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,oEAAoE,QAAQ;AAC1G,YAAM,SAAY,cAAc,GAAG;AACnC,YAAM,SAAS,OAAO;AACtB,YAAM,OAAO,OAAO;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,QAAQ,MAAM,MAAM,CAAC;AAAA,IACjE,CAAC,EAAE,eAAe,SAAS,0EAA0E,QAAQ;AAC3G,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,mBAAmB,QAAQ,MAAM,CAAC;AAAA,IACjE,CAAC;AACD,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,GAAG,+DAA+D,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,yDAAyD,GAAG,GAAG,UAAU,CAAC;AACtL,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,gBAAgB,OAAO,aAAa,eAAe,OAAO,iBAAiB,MAAM,IAAI,EAAE,iBAAiB,OAAO,aAAa,aAAa,OAAO,iBAAiB,MAAM,IAAI;AAC1L,IAAG,YAAY,uBAAuB,OAAO,oBAAoB,IAAI,EAAE,yBAAyB,OAAO,UAAU;AACjH,IAAG,UAAU;AACb,IAAG,WAAW,MAAM,OAAO,gBAAgB,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,EAAE,OAAO,MAAM,EAAE,UAAU,OAAO,oBAAoB,IAAI;AACzI,IAAG,YAAY,YAAY,OAAO,YAAY,QAAQ,IAAI,CAAC,EAAE,iBAAiB,OAAO,UAAU,EAAE,iBAAiB,OAAO,oBAAoB,QAAQ,CAAC,OAAO,SAAS,EAAE,iBAAiB,OAAO,gBAAgB,IAAI,CAAC;AACrN,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,KAAK,EAAE,iCAAoC,gBAAgB,IAAI,GAAG,CAAC;AAClH,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc,OAAO,YAAY,CAAC,OAAO,UAAU;AAAA,EAClF;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,eAAe,CAAC;AACrC,IAAG,WAAW,aAAa,SAAS,0EAA0E,QAAQ;AACpH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,KAAK,MAAM,CAAC;AAAA,IAC3D,CAAC,EAAE,sBAAsB,SAAS,mFAAmF,QAAQ;AAC3H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,cAAc,SAAS,6EAA6E;AACrG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,MAAM,CAAC;AAAA,IACtC,CAAC;AACD,IAAG,WAAW,GAAG,gDAAgD,GAAG,IAAI,OAAO,CAAC;AAChF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,aAAa,EAAE,iBAAiB,OAAO,mBAAmB,CAAC,EAAE,kBAAkB,OAAO,cAAc,EAAE,WAAW,OAAO,OAAO,EAAE,WAAW,OAAO,SAAS,EAAE,WAAW,OAAO,SAAS,EAAE,YAAY,OAAO,QAAQ,EAAE,iBAAiB,OAAO,oBAAoB;AACpS,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,IAAI;AAAA,EACtC;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,MAAM,OAAO,gBAAgB,IAAI,CAAC,EAAE,UAAU,OAAO,oBAAoB,QAAQ,CAAC,OAAO,SAAS,EAAE,WAAW,OAAO,OAAO,EAAE,eAAe,OAAO,aAAa,EAAE,YAAY,OAAO,eAAe;AACpN,IAAG,YAAY,mBAAmB,OAAO,gBAAgB,IAAI,CAAC;AAAA,EAChE;AACF;AACA,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,YAAY;AACtB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,UAAU,KAAK,WAAW;AAAA,EACjC;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,SAAS,eAAe;AAAA,EACtC;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,SAAS,gBAAgB;AAAA,EACvC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAA4B,kBAAqB,UAAU,CAAC;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,GAAG,CAAC,UAAU,qBAAqB,EAAE,CAAC;AAAA,MACtE,WAAW,CAAC,cAAc,WAAW,QAAQ,UAAU,GAAG,kBAAkB;AAAA,MAC5E,QAAQ;AAAA,QACN,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,GAAG,CAAC,WAAW,IAAI,WAAW,WAAW,GAAG,QAAQ,CAAC;AAAA,MAC1F,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC;AAAA,QAC3F;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,0BAA0B,IAAI,OAAO;AAAA,QACrD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,iCAAiC,cAAiB,eAAe;AAAA,MACnG,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,cAAc;AAAA,QACd,MAAM;AAAA,MACR;AAAA,MACA,SAAS,CAAC,gBAAgB,YAAY;AAAA,MACtC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,IAAI,YAAY;AACd,WAAO,KAAK,kBAAkB,oBAAoB,KAAK;AAAA,EACzD;AAAA,EACA,YAAY,YAAY,QAAQ,eAAe;AAC7C,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,eAAe,SAAS;AACtB,SAAK,OAAO,kBAAkB,MAAM;AAClC,mBAAa,MAAM,KAAK,UAAU,OAAO,CAAC;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EACA,UAAU,SAAS;AACjB,UAAM,SAAS,KAAK,WAAW;AAC/B,QAAI,KAAK,aAAa,cAAc;AAClC,aAAO,MAAM,MAAM;AACnB,aAAO,MAAM,SAAS;AACtB,aAAO,MAAM,OAAO,KAAK,gBAAgB,OAAO;AAChD,aAAO,MAAM,QAAQ,KAAK,gBAAgB,OAAO;AAAA,IACnD,OAAO;AACL,aAAO,MAAM,OAAO;AACpB,aAAO,MAAM,QAAQ;AACrB,aAAO,MAAM,MAAM,KAAK,eAAe,OAAO;AAC9C,aAAO,MAAM,SAAS,KAAK,iBAAiB,OAAO;AAAA,IACrD;AAAA,EACF;AAAA,EACA,gBAAgB,SAAS;AACvB,WAAO,UAAU,GAAG,QAAQ,cAAc,CAAC,OAAO;AAAA,EACpD;AAAA,EACA,gBAAgB,SAAS;AACvB,WAAO,UAAU,GAAG,QAAQ,eAAe,CAAC,OAAO;AAAA,EACrD;AAAA,EACA,eAAe,SAAS;AACtB,WAAO,UAAU,GAAG,QAAQ,aAAa,CAAC,OAAO;AAAA,EACnD;AAAA,EACA,iBAAiB,SAAS;AACxB,WAAO,UAAU,GAAG,QAAQ,gBAAgB,CAAC,OAAO;AAAA,EACtD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAA0B,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,IAC9J;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,GAAG,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,MAC5D,WAAW,CAAC,GAAG,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,6BAA6B,IAAI,SAAS;AAAA,QAC3D;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,qCAAqC;AAAA,MACvC;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,YAAY;AACtB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,KAAK,WAAW;AACrB,SAAK,gBAAgB,KAAK,GAAG;AAAA,EAC/B;AAAA,EACA,QAAQ;AACN,SAAK,GAAG,MAAM;AAAA,EAChB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAA0B,kBAAqB,UAAU,CAAC;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,MACpC,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,KAAK;AAAA,QACL,QAAQ;AAAA,MACV;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,YAAY,KAAK,YAAY;AAC3B,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,QAAQ,CAAC;AACd,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,aAAa;AAClB,SAAK,UAAU,KAAK,WAAW;AAAA,EACjC;AAAA,EACA,SAAS,MAAM;AACb,QAAI,CAAC,KAAK,UAAU;AAElB,WAAK,IAAI,QAAQ,KAAK;AACtB,WAAK,SAAS,KAAK,IAAI;AAAA,IACzB;AAAA,EACF;AAAA,EACA,cAAc,MAAM,GAAG;AACrB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,IAAI,cAAc,KAAK,CAAC;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,YAAY;AACV,iBAAa,KAAK,2BAA2B;AAC7C,SAAK,aAAa;AAClB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,cAAc,SAAS;AACrB,QAAI,CAAC,SAAS;AACZ,WAAK,8BAA8B,WAAW,MAAM;AAClD,aAAK,aAAa;AAClB,aAAK,IAAI,aAAa;AAAA,MACxB,GAAG,GAAG;AAAA,IACR;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,SAAS,eAAe;AAAA,EACtC;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,SAAS,gBAAgB;AAAA,EACvC;AAAA,EACA,cAAc;AACZ,iBAAa,KAAK,2BAA2B;AAAA,EAC/C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,GAAG;AACzD,aAAO,KAAK,KAAK,6BAA+B,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,CAAC;AAAA,IAC9H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,MACpC,WAAW,CAAC,GAAG,yBAAyB;AAAA,MACxC,UAAU;AAAA,MACV,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,kCAAkC,IAAI,MAAM,WAAW,CAAC;AAAA,QACzE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ;AAAA,MACA,UAAU,CAAC,mBAAmB;AAAA,MAC9B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,mBAAmB,YAAY,GAAG,CAAC,QAAQ,gBAAgB,GAAG,CAAC,eAAe,IAAI,QAAQ,UAAU,YAAY,MAAM,eAAe,QAAQ,sBAAsB,oBAAoB,GAAG,qBAAqB,GAAG,mBAAmB,cAAc,kBAAkB,kBAAkB,qBAAqB,GAAG,CAAC,WAAW,IAAI,UAAU,UAAU,GAAG,CAAC,WAAW,IAAI,GAAG,MAAM,GAAG,CAAC,qBAAqB,IAAI,GAAG,WAAW,SAAS,GAAG,MAAM,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,gBAAgB,IAAI,SAAS,+BAA+B,GAAG,wCAAwC,cAAc,cAAc,SAAS,eAAe,GAAG,SAAS,SAAS,GAAG,CAAC,gBAAgB,IAAI,GAAG,+BAA+B,GAAG,SAAS,eAAe,cAAc,YAAY,GAAG,CAAC,GAAG,0BAA0B,+BAA+B,GAAG,CAAC,qBAAqB,IAAI,GAAG,SAAS,SAAS,CAAC;AAAA,MAC71B,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,eAAe,GAAG,UAAU,GAAG,CAAC;AACnC,UAAG,WAAW,mBAAmB,SAAS,sEAAsE,QAAQ;AACtH,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,cAAc,MAAM,CAAC;AAAA,UACjD,CAAC,EAAE,cAAc,SAAS,mEAAmE;AAC3F,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,UAAU,CAAC;AAAA,UACvC,CAAC;AACD,UAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,oBAAoB,MAAM,CAAC;AAChD,UAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,MAAM,CAAC;AACxE,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,UAAU,CAAC;AAAA,QAClF;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,UAAa,YAAY,CAAC;AAChC,UAAG,WAAW,kBAAkB,OAAO,EAAE,kBAAqB,gBAAgB,GAAG,GAAG,CAAC,EAAE,uBAAuB,IAAI;AAClH,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,UAAU;AACpC,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,OAAO;AAAA,QACnC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAiB,iBAAiB,MAAM,SAAS,gBAAmB,iCAAiC,yBAAyB,yBAAyB,cAAiB,iBAAoB,qBAAqB,mBAAmB;AAAA,MACnP,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqCV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,0CAA0C;AAAA,MAC5C;AAAA,MACA,SAAS,CAAC,cAAc,MAAM,SAAS,gBAAgB,yBAAyB,yBAAyB,cAAc,mBAAmB;AAAA,MAC1I,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB;AAC5B,IAAM,mBAAmB;AACzB,IAAM,qBAAqB,SAAS;AACpC,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,QAAQ,YAAY;AAC9B,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,cAAc,MAAM;AACzB,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,aAAa,OAAK;AACrB,UAAI,CAAC,KAAK,eAAe;AACvB;AAAA,MACF;AACA,YAAM,aAAa,KAAK;AACxB,YAAM,eAAe,KAAK;AAC1B,WAAK,aAAa,KAAK,gBAAgB;AACvC,UAAI,YAAY;AACd,cAAM,YAAY,WAAW,IAAI;AACjC,cAAM,YAAY,WAAW,IAAI;AACjC,cAAM,OAAO,KAAK,IAAI,SAAS;AAC/B,cAAM,OAAO,KAAK,IAAI,SAAS;AAE/B,YAAI,KAAK,IAAI,MAAM,IAAI,IAAI,oBAAoB;AAC7C;AAAA,QACF;AACA,YAAI,WAAW;AACf,YAAI,WAAW;AACf,aAAK,SAAS,OAAO,YAAY,MAAM;AACrC,cAAI,KAAK,IAAI,QAAQ,IAAI,uBAAuB,KAAK,IAAI,QAAQ,IAAI,qBAAqB;AACxF,mBAAO,cAAc,KAAK,MAAM;AAChC;AAAA,UACF;AACA,sBAAY;AACZ,sBAAY;AACZ,eAAK,SAAS,WAAW,kBAAkB,WAAW,kBAAkB,CAAC;AAAA,QAC3E,GAAG,gBAAgB;AAAA,MACrB;AAAA,IACF;AACA,SAAK,cAAc,OAAK;AACtB,UAAI,CAAC,KAAK,eAAe;AACvB;AAAA,MACF;AACA,QAAE,eAAe;AACjB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,EAAE,QAAQ,CAAC;AACf,YAAM,UAAU,UAAU,KAAK,cAAc;AAC7C,YAAM,UAAU,UAAU,KAAK,cAAc;AAC7C,WAAK,SAAS,SAAS,SAAS,CAAC;AACjC,YAAM,MAAM,KAAK,IAAI;AACrB,WAAK,eAAe,MAAM,KAAK;AAC/B,WAAK,gBAAgB;AACrB,WAAK,aAAa;AAAA,QAChB,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,WAAK,gBAAgB;AAAA,QACnB,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AACA,SAAK,eAAe,OAAK;AACvB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,EAAE,QAAQ,CAAC;AACf,WAAK,gBAAgB;AAAA,QACnB,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,aAAO,cAAc,KAAK,MAAM;AAAA,IAClC;AACA,SAAK,UAAU,OAAK;AAClB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI;AACJ,YAAM,OAAO,KAAK,IAAI,MAAM;AAC5B,YAAM,OAAO,KAAK,IAAI,MAAM;AAC5B,UAAI,SAAS,MAAM;AACjB,gBAAQ,KAAK,uBAAuB,MAAM,SAAS;AAAA,MACrD,WAAW,OAAO,MAAM;AACtB,gBAAQ;AACR,aAAK,qBAAqB;AAAA,MAC5B,OAAO;AACL,gBAAQ;AACR,aAAK,qBAAqB;AAAA,MAC5B;AAEA,YAAM,MAAM,KAAK,IAAI;AACrB,YAAM,WAAW,KAAK,IAAI,KAAK;AAC/B,UAAI,MAAM,KAAK,qBAAqB,OAAO,WAAW,KAAK,iBAAiB,IAAI;AAC9E,aAAK,mBAAmB;AAAA,MAC1B;AACA,WAAK,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC;AAC/B,UAAI,EAAE,oBAAoB,KAAK,kBAAkB;AAC/C,aAAK,mBAAmB;AAAA,MAC1B;AACA,WAAK,qBAAqB;AAC1B,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,cAAc,KAAK,OAAO,kBAAkB,MAAM;AACrD,YAAM,KAAK,KAAK,WAAW;AAC3B,YAAM,SAAS,UAAU,IAAI,OAAO;AACpC,YAAM,cAAc,UAAU,IAAI,YAAY;AAC9C,YAAM,aAAa,UAAU,IAAI,WAAW;AAC5C,YAAM,YAAY,UAAU,IAAI,UAAU;AAC1C,YAAM,eAAe,IAAI,aAAa;AACtC,mBAAa,IAAI,KAAK,cAAc,SAAS,QAAQ,KAAK,OAAO,CAAC;AAClE,mBAAa,IAAI,KAAK,cAAc,cAAc,aAAa,KAAK,YAAY,CAAC;AACjF,mBAAa,IAAI,KAAK,cAAc,aAAa,YAAY,KAAK,WAAW,CAAC;AAC9E,mBAAa,IAAI,KAAK,cAAc,YAAY,WAAW,KAAK,UAAU,CAAC;AAC3E,aAAO,MAAM;AACX,qBAAa,YAAY;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc,MAAM,YAAY,SAAS;AACvC,WAAO,WAAW,UAAU,WAAS;AACnC,WAAK,UAAU,KAAK;AAAA,QAClB;AAAA,QACA;AAAA,MACF,CAAC;AACD,UAAI,CAAC,MAAM,kBAAkB;AAC3B,gBAAQ,KAAK;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,SAAS,GAAG,GAAG,OAAO;AACpB,SAAK,OAAO,IAAI,MAAM;AACpB,WAAK,aAAa,KAAK;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,GAAG;AACvD,aAAO,KAAK,KAAK,2BAA6B,kBAAqB,MAAM,GAAM,kBAAqB,UAAU,CAAC;AAAA,IACjH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,MACvC,SAAS;AAAA,QACP,cAAc;AAAA,QACd,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,mBAAmB,OAAO,0BAA0B,cAAc,0BAA0B;AAClG,IAAM,qBAAqB;AAC3B,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,UAAM,WAAW,qBAAqB,KAAK;AAC3C,QAAI,KAAK,mBAAmB,UAAU;AACpC,WAAK,iBAAiB;AACtB,WAAK,uBAAuB;AAC5B,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,iBAAiB,KAAK;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,aAAa,KAAK,WAAW,kBAAkB;AAAA,EAC7D;AAAA;AAAA,EAEA,IAAI,WAAW,OAAO;AACpB,QAAI,CAAC,KAAK,aAAa,KAAK,KAAK,KAAK,eAAe,SAAS,CAAC,KAAK,YAAY;AAC9E;AAAA,IACF;AACA,SAAK,WAAW,cAAc,KAAK;AAAA,EACrC;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,YAAY,WAAW,KAAK,KAAK;AAAA,EAC/C;AAAA,EACA,YAAY,KAAK,QAAQ,eAAe,kBAAkB,KAAK;AAC7D,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AACxB,SAAK,MAAM;AACX,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,qBAAqB,IAAI,aAAa;AAC3C,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,cAAc,CAAC;AACpB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AACvB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,kBAAkB;AAChB,UAAM,YAAY,KAAK,MAAM,KAAK,IAAI,OAAO,aAAa,IAAI,GAAG,IAAI;AACrE,UAAM,SAAS,KAAK,cAAc,OAAO,GAAG;AAC5C,UAAM,UAAU,MAAM;AACpB,WAAK,yBAAyB;AAC9B,WAAK,yBAAyB;AAAA,IAChC;AACA,SAAK,aAAa,IAAI,gBAAgB,KAAK,KAAK,EAAE,0BAA0B,KAAK,mBAAmB,CAAC,EAAE,SAAS;AAChH,SAAK,WAAW,iBAAiB,KAAK,aAAa;AACnD,iBAAa,OAAO;AACpB,UAAM,KAAK,iBAAiB,QAAQ,KAAK,UAAU,GAAG,KAAK,iBAAiB,QAAQ,KAAK,UAAU,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,GAAG,UAAU,IAAI,gBAAgB,CAAC,EAAE,UAAU,MAAM;AACpL,cAAQ;AAAA,IACV,CAAC;AACD,UAAM,WAAW,QAAQ,KAAK,MAAM,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC1F,cAAQ,QAAQ,EAAE,KAAK,OAAO;AAC9B,WAAK,WAAW,0BAA0B,KAAK,mBAAmB,CAAC;AAAA,IACrE,CAAC;AACD,SAAK,WAAW,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,mBAAiB;AAC/E,WAAK,aAAa,KAAK,aAAa;AACpC,WAAK,YAAY,aAAa;AAC9B,WAAK,YAAY,KAAK,WAAW,UAAU;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA,EACA,wBAAwB;AACtB,QAAI,KAAK,sBAAsB;AAC7B,WAAK,yBAAyB;AAC9B,WAAK,yBAAyB;AAC9B,WAAK,uBAAuB;AAC5B,WAAK,IAAI,aAAa;AAAA,IACxB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,iBAAa,KAAK,sBAAsB;AACxC,iBAAa,KAAK,yBAAyB;AAC3C,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,mBAAmB,KAAK;AACtB,UAAM,WAAW,KAAK,MAAM,QAAQ,EAAE,UAAU,OAAK,MAAM,GAAG;AAC9D,QAAI,aAAa,IAAI;AACnB,WAAK,WAAW,iBAAiB,QAAQ;AACzC,UAAI,KAAK,eAAe,KAAK,eAAe;AAC1C,aAAK,mBAAmB,KAAK,KAAK,UAAU;AAC5C,aAAK,YAAY,GAAG;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,GAAG;AAChB,QAAI,KAAK,aAAa,cAAc;AAClC,UAAI,CAAC,KAAK,wBAAwB;AAChC,YAAI,KAAK,cAAc,KAAK,EAAE,IAAI,GAAG;AACnC;AAAA,QACF;AACA,YAAI,KAAK,cAAc,KAAK,eAAe,KAAK,mBAAmB,EAAE,IAAI,GAAG;AAC1E;AAAA,QACF;AAAA,MACF;AACA,QAAE,MAAM,eAAe;AACvB,WAAK,aAAa,KAAK,gBAAgB,KAAK,aAAa,EAAE,CAAC;AAC5D,WAAK,aAAa,KAAK,YAAY,CAAC;AAAA,IACtC,OAAO;AACL,UAAI,CAAC,KAAK,wBAAwB;AAChC,YAAI,KAAK,cAAc,KAAK,EAAE,IAAI,GAAG;AACnC;AAAA,QACF;AACA,YAAI,KAAK,cAAc,KAAK,gBAAgB,KAAK,oBAAoB,EAAE,IAAI,GAAG;AAC5E;AAAA,QACF;AAAA,MACF;AACA,QAAE,MAAM,eAAe;AACvB,WAAK,aAAa,KAAK,gBAAgB,KAAK,aAAa,EAAE,CAAC;AAC5D,WAAK,aAAa,GAAG,KAAK,UAAU;AAAA,IACtC;AACA,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,mBAAmB,KAAK,WAAW,cAAc,SAAS,MAAM,MAAM;AAC5E,QAAI,eAAe,KAAK,KAAK,CAAC,kBAAkB;AAC9C;AAAA,IACF;AACA,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,aAAK,cAAc;AACnB,aAAK,WAAW,UAAU,KAAK;AAC/B;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,YAAI,KAAK,eAAe,KAAK,eAAe;AAC1C,eAAK,mBAAmB,KAAK,KAAK,UAAU;AAAA,QAC9C;AACA;AAAA,MACF;AACE,aAAK,WAAW,UAAU,KAAK;AAAA,IACnC;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,CAAC,KAAK,OAAO;AACf,aAAO;AAAA,IACT;AACA,UAAM,MAAM,KAAK,QAAQ,KAAK,MAAM,QAAQ,EAAE,KAAK,IAAI;AACvD,WAAO,CAAC,CAAC,OAAO,CAAC,IAAI;AAAA,EACvB;AAAA,EACA,YAAY,KAAK;AACf,QAAI,CAAC,KAAK,MAAM,KAAK,OAAK,MAAM,GAAG,GAAG;AACpC;AAAA,IACF;AACA,UAAM,OAAO,KAAK,MAAM,QAAQ;AAChC,QAAI,KAAK,aAAa,cAAc;AAClC,UAAI,eAAe,KAAK;AACxB,UAAI,KAAK,mBAAmB,MAAM,OAAO;AACvC,cAAM,QAAQ,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,EAAE,QAAQ,IAAI,OAAO,IAAI;AAC5D,YAAI,QAAQ,KAAK,YAAY;AAC3B,yBAAe;AAAA,QACjB,WAAW,QAAQ,IAAI,QAAQ,KAAK,aAAa,KAAK,cAAc;AAClE,yBAAe,QAAQ,IAAI,QAAQ,KAAK;AAAA,QAC1C;AAAA,MACF,WAAW,IAAI,OAAO,CAAC,KAAK,YAAY;AACtC,uBAAe,CAAC,IAAI;AAAA,MACtB,WAAW,IAAI,OAAO,IAAI,QAAQ,CAAC,KAAK,aAAa,KAAK,cAAc;AACtE,uBAAe,EAAE,IAAI,OAAO,IAAI,QAAQ,KAAK;AAAA,MAC/C;AACA,WAAK,aAAa;AAClB,WAAK,aAAa;AAClB,WAAK,aAAa,cAAc,CAAC;AAAA,IACnC,OAAO;AACL,UAAI,eAAe,KAAK;AACxB,UAAI,IAAI,MAAM,CAAC,KAAK,YAAY;AAC9B,uBAAe,CAAC,IAAI;AAAA,MACtB,WAAW,IAAI,MAAM,IAAI,SAAS,CAAC,KAAK,aAAa,KAAK,eAAe;AACvE,uBAAe,EAAE,IAAI,MAAM,IAAI,SAAS,KAAK;AAAA,MAC/C;AACA,WAAK,aAAa;AAClB,WAAK,aAAa;AAClB,WAAK,aAAa,GAAG,YAAY;AAAA,IACnC;AACA,iBAAa,KAAK,yBAAyB;AAC3C,SAAK,4BAA4B,WAAW,MAAM;AAChD,WAAK,gBAAgB;AAAA,IACvB,GAAG,kBAAkB;AAAA,EACvB;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,wBAAwB;AAChC,WAAK,OAAO,kBAAkB,MAAM;AAClC,aAAK,WAAW,cAAc,MAAM,aAAa;AACjD,aAAK,yBAAyB,WAAW,MAAM;AAC7C,eAAK,WAAW,cAAc,MAAM,aAAa;AACjD,eAAK,yBAAyB;AAAA,QAChC,GAAG,kBAAkB;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa,GAAG,GAAG;AACjB,SAAK,WAAW,cAAc,MAAM,YAAY,aAAa,CAAC,OAAO,CAAC;AAAA,EACxE;AAAA,EACA,gBAAgB,WAAW;AACzB,UAAM,cAAc,KAAK,eAAe,KAAK;AAC7C,QAAI,KAAK,mBAAmB,MAAM,OAAO;AACvC,aAAO,KAAK,IAAI,KAAK,IAAI,aAAa,SAAS,GAAG,CAAC;AAAA,IACrD,OAAO;AACL,aAAO,KAAK,IAAI,KAAK,IAAI,aAAa,SAAS,GAAG,CAAC;AAAA,IACrD;AAAA,EACF;AAAA,EACA,gBAAgB,WAAW;AACzB,WAAO,KAAK,IAAI,KAAK,IAAI,KAAK,gBAAgB,KAAK,kBAAkB,SAAS,GAAG,CAAC;AAAA,EACpF;AAAA,EACA,2BAA2B;AACzB,SAAK,WAAW;AAChB,SAAK,aAAa,KAAK,gBAAgB,KAAK,UAAU;AACtD,SAAK,aAAa,KAAK,gBAAgB,KAAK,UAAU;AACtD,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,iBAAiB,KAAK,WAAW,eAAe;AAChE,UAAI,KAAK,WAAW,YAAY;AAC9B,aAAK,YAAY,KAAK,WAAW,UAAU;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa;AACX,SAAK,iBAAiB,KAAK,YAAY,KAAK,UAAU,gBAAgB,IAAI;AAC1E,SAAK,kBAAkB,KAAK,YAAY,KAAK,UAAU,iBAAiB,IAAI;AAC5E,SAAK,iBAAiB,KAAK,aAAa,gBAAgB;AACxD,SAAK,kBAAkB,KAAK,aAAa,iBAAiB;AAC1D,SAAK,eAAe,KAAK,WAAW,cAAc,eAAe;AACjE,SAAK,gBAAgB,KAAK,WAAW,cAAc,gBAAgB;AACnE,SAAK,mBAAmB,KAAK,WAAW,cAAc,gBAAgB;AACtE,SAAK,kBAAkB,KAAK,WAAW,cAAc,eAAe;AAAA,EACtE;AAAA,EACA,2BAA2B;AACzB,UAAM,eAAe,KAAK,SAAS,KAAK,MAAM,SAAS,KAAK,MAAM,QAAQ,EAAE,KAAK,aAAa,IAAI;AAClG,UAAM,sBAAsB,eAAe,aAAa,WAAW,gBAAgB;AACnF,QAAI,qBAAqB;AAMvB,WAAK,OAAO,eAAe,oBAAoB,aAAa;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,UAAM,OAAO;AAAA,MACX,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AACA,UAAM,UAAU,KAAK,WAAW;AAChC,QAAI,KAAK,aAAa,cAAc;AAClC,UAAI,KAAK,mBAAmB,MAAM,OAAO;AACvC,aAAK,QAAQ,KAAK,aAAa;AAC/B,aAAK,OAAO,KAAK,aAAa,KAAK,eAAe,KAAK;AAAA,MACzD,OAAO;AACL,aAAK,OAAO,KAAK,aAAa;AAC9B,aAAK,QAAQ,CAAC,KAAK,aAAa,KAAK,eAAe,KAAK;AAAA,MAC3D;AAAA,IACF,OAAO;AACL,WAAK,MAAM,KAAK,aAAa;AAC7B,WAAK,SAAS,CAAC,KAAK,aAAa,KAAK,gBAAgB,KAAK;AAAA,IAC7D;AACA,WAAO,KAAK,IAAI,EAAE,QAAQ,SAAO;AAC/B,YAAM,YAAY,0BAA0B,GAAG;AAC/C,UAAI,KAAK,GAAG,GAAG;AACb,gBAAQ,UAAU,IAAI,SAAS;AAAA,MACjC,OAAO;AACL,gBAAQ,UAAU,OAAO,SAAS;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,UAAM,OAAO,KAAK,MAAM,QAAQ;AAChC,UAAM,eAAe;AAAA,MACnB,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AACA,UAAM,YAAY,WAAS;AACzB,UAAI;AACJ,YAAM,OAAO,KAAK,KAAK,KAAK;AAC5B,UAAI,aAAa,SAAS;AACxB,iBAAS,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,EAAE,QAAQ,KAAK,KAAK,EAAE,OAAO,KAAK,KAAK,EAAE;AAAA,MACzE,OAAO;AACL,iBAAS,KAAK,QAAQ;AAAA,MACxB;AACA,aAAO;AAAA,IACT;AACA,QAAI,KAAK,aAAa,cAAc;AAClC,aAAO;AACP,kBAAY,KAAK;AACjB,uBAAiB,KAAK,mBAAmB,KAAK,YAAY,SAAS,KAAK,iBAAiB;AACzF,gBAAU,KAAK;AACf,sBAAgB,KAAK,IAAI,KAAK,UAAU;AACxC,UAAI,KAAK,mBAAmB,MAAM,OAAO;AACvC,mBAAW;AACX,aAAK,YAAY,KAAK,aAAa;AACnC,aAAK,WAAW,KAAK,aAAa,KAAK,eAAe,KAAK;AAAA,MAC7D,OAAO;AACL,aAAK,WAAW,KAAK,aAAa;AAClC,aAAK,YAAY,CAAC,KAAK,aAAa,KAAK,eAAe,KAAK;AAC7D,mBAAW;AAAA,MACb;AAAA,IACF,OAAO;AACL,aAAO;AACP,kBAAY,KAAK;AACjB,uBAAiB,KAAK,oBAAoB,KAAK,YAAY,SAAS,KAAK,kBAAkB;AAC3F,gBAAU,KAAK;AACf,iBAAW;AACX,sBAAgB,CAAC,KAAK;AACtB,WAAK,UAAU,KAAK,aAAa;AACjC,WAAK,aAAa,CAAC,KAAK,aAAa,KAAK,gBAAgB,KAAK;AAAA,IACjE;AACA,QAAI,kBAAkB;AACtB,QAAI,iBAAiB,UAAU,WAAW;AACxC,wBAAkB,YAAY;AAAA,IAChC;AACA,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,cAAc,CAAC;AACpB,WAAK,IAAI,aAAa;AACtB;AAAA,IACF;AACA,UAAM,MAAM,KAAK;AACjB,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,YAAM,SAAS,UAAU,CAAC;AAC1B,YAAM,OAAO,KAAK,CAAC,KAAK;AACxB,UAAI,SAAS,KAAK,IAAI,IAAI,gBAAgB,iBAAiB;AACzD,mBAAW,IAAI;AACf;AAAA,MACF;AAAA,IACF;AACA,QAAI,aAAa;AACjB,aAAS,IAAI,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG;AACpC,YAAM,SAAS,UAAU,CAAC;AAC1B,UAAI,SAAS,eAAe;AAC1B,qBAAa,IAAI;AACjB;AAAA,MACF;AAAA,IACF;AACA,UAAM,kBAAkB,KAAK,MAAM,GAAG,UAAU;AAChD,UAAM,gBAAgB,KAAK,MAAM,WAAW,CAAC;AAC7C,SAAK,cAAc,CAAC,GAAG,iBAAiB,GAAG,aAAa;AACxD,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,OAAO,KAAK,IAAI,UAAU,QAAQ,QAAQ;AAAA,EACxD;AAAA,EACA,YAAY,WAAW;AAAA,EAAC;AAAA,EACxB,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAEJ,QAAI,YAAY,CAAC,SAAS,cAAc,GAAG;AACzC,WAAK,yBAAyB;AAC9B,WAAK,cAAc;AACnB,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,kBAAqB,iBAAiB,GAAM,kBAAqB,MAAM,GAAM,kBAAuB,aAAa,GAAM,kBAAuB,gBAAgB,GAAM,kBAAuB,gBAAgB,CAAC,CAAC;AAAA,IACzP;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,MAC3B,gBAAgB,SAAS,oCAAoC,IAAI,KAAK,UAAU;AAC9E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,uBAAuB,CAAC;AAAA,QACtD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ;AAAA,QAC3D;AAAA,MACF;AAAA,MACA,WAAW,SAAS,2BAA2B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,4BAA4B,CAAC;AAC5C,UAAG,YAAY,yBAAyB,CAAC;AACzC,UAAG,YAAY,uBAAuB,CAAC;AAAA,QACzC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAChE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS,GAAG;AAAA,QAC/D;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,cAAc;AAAA,MAC7B,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,SAAS,gDAAgD,QAAQ;AACxF,mBAAO,IAAI,cAAc,MAAM;AAAA,UACjC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,eAAe;AAAA,MACjB;AAAA,MACA,SAAS;AAAA,QACP,cAAc;AAAA,QACd,oBAAoB;AAAA,QACpB,YAAY;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,mBAAmB,IAAI,QAAQ,WAAW,GAAG,qBAAqB,GAAG,gBAAgB,WAAW,GAAG,CAAC,QAAQ,OAAO,qBAAqB,IAAI,GAAG,WAAW,SAAS,GAAG,MAAM,GAAG,CAAC,mBAAmB,IAAI,GAAG,UAAU,YAAY,UAAU,GAAG,CAAC,GAAG,cAAc,YAAY,WAAW,WAAW,OAAO,GAAG,CAAC,SAAS,0BAA0B,GAAG,MAAM,GAAG,CAAC,QAAQ,OAAO,qBAAqB,IAAI,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,MACxhB,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,GAAG,CAAC;AAChD,UAAG,WAAW,gBAAgB,SAAS,0DAA0D,QAAQ;AACvG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,eAAe,MAAM,CAAC;AAAA,UAClD,CAAC,EAAE,aAAa,SAAS,uDAAuD,QAAQ;AACtF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,UAAU,KAAK,MAAM,CAAC;AAAA,UAClD,CAAC;AACD,UAAG,aAAa,CAAC;AACjB,UAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,UAAU,CAAC;AAC1E,UAAG,UAAU,GAAG,OAAO,CAAC;AACxB,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,GAAG,wBAAwB,CAAC;AAC9C,UAAG,WAAW,cAAc,SAAS,2EAA2E;AAC9G,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,WAAW,KAAK,CAAC;AAAA,UAC7C,CAAC,EAAE,YAAY,SAAS,uEAAuE,QAAQ;AACrG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,mBAAmB,MAAM,CAAC;AAAA,UACtD,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,OAAO,CAAC;AAAA,QACtE;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,+BAA+B,IAAI,QAAQ,EAAE,gCAAgC,IAAI,SAAS,EAAE,8BAA8B,IAAI,OAAO,EAAE,iCAAiC,IAAI,UAAU;AACrM,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,aAAa;AACvC,UAAG,UAAU;AACb,UAAG,WAAW,UAAU,IAAI,OAAO,EAAE,YAAY,IAAI,QAAQ,EAAE,YAAY,IAAI,cAAc;AAC7F,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,OAAO,EAAE,WAAW,IAAI,OAAO,EAAE,SAAS,IAAI,WAAW;AACtF,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,aAAa;AAAA,QACzC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,0BAA0B,MAAM,yBAAyB,uBAAuB,4BAA4B,gBAAgB;AAAA,MAC3I,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAwCV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,MACf;AAAA,MACA,SAAS,CAAC,0BAA0B,MAAM,yBAAyB,uBAAuB,4BAA4B,gBAAgB;AAAA,MACtI,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,QACjC,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,QAC9B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,QAC5B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,QAC5B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,cAAc;AAInB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ,cAAc;AACxB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,WAAW,CAAC,GAAG,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,oBAAoB,IAAI,SAAS,UAAU,OAAO,EAAE,cAAc,CAAC,IAAI,QAAQ;AAC1G,UAAG,YAAY,YAAY,IAAI,SAAS,IAAI,EAAE,EAAE,eAAe,CAAC,IAAI,MAAM;AAC1E,UAAG,YAAY,cAAc,IAAI,WAAW,IAAI,SAAS,OAAO,SAAS,IAAI;AAC7E,UAAG,YAAY,2BAA2B,IAAI,MAAM,EAAE,2BAA2B,IAAI,WAAW,OAAO,CAAC,IAAI,MAAM;AAAA,QACpH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,MAC7C,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,gBAAgB,CAAC;AAAA,QACtF;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAQ,IAAI,iBAAiB,IAAI,WAAW;AAAA,QAC5D;AAAA,MACF;AAAA,MACA,cAAc,CAAC,MAAM,gBAAgB;AAAA,MACrC,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,eAAe;AAAA,MAC7B;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,mCAAmC;AAAA,QACnC,mCAAmC;AAAA,QACnC,mBAAmB;AAAA,QACnB,sBAAsB;AAAA,QACtB,sBAAsB;AAAA,QACtB,sBAAsB;AAAA,QACtB,gBAAgB;AAAA,MAClB;AAAA,MACA,SAAS,CAAC,MAAM,gBAAgB;AAAA,MAChC,YAAY;AAAA,MACZ,YAAY,CAAC,eAAe;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,cAAc;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,GAAG;AACxD,aAAO,KAAK,KAAK,4BAA2B;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,GAAG,CAAC,UAAU,uBAAuB,EAAE,CAAC;AAAA,MAC1E,WAAW,CAAC,cAAc,aAAa,QAAQ,UAAU,GAAG,qBAAqB;AAAA,MACjF,QAAQ;AAAA,QACN,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,GAAG,CAAC,WAAW,IAAI,WAAW,WAAW,GAAG,QAAQ,CAAC;AAAA,MAC1F,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,gBAAgB,CAAC;AAAA,QAC7F;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,0BAA0B,IAAI,SAAS;AAAA,QACvD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,iCAAiC,cAAiB,eAAe;AAAA,MACnG,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,cAAc;AAAA,QACd,MAAM;AAAA,MACR;AAAA,MACA,SAAS,CAAC,gBAAgB,YAAY;AAAA,MACtC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AASH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,GAAG;AACzD,aAAO,KAAK,KAAK,6BAA+B,kBAAqB,aAAa,CAAC,CAAC;AAAA,IACtF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,aAAa,EAAE,CAAC;AAAA,MAC5C,UAAU,CAAC,mBAAmB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAIH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,YAAY,YAAY;AAClC,SAAK,aAAa;AAClB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAuB,kBAAqB,UAAU,GAAM,kBAAuB,YAAY,EAAE,CAAC;AAAA,IACrH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,KAAK,eAAe,EAAE,CAAC;AAAA,MACpC,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAOH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,UAAU,EAAE,CAAC;AAAA,MAC9B,UAAU,CAAC,OAAO;AAAA,MAClB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,aAAa,IAAI,eAAe,YAAY;AAClD,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,IAAI,UAAU;AACZ,WAAO,KAAK,YAAY,KAAK;AAAA,EAC/B;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,WAAW,KAAK,4BAA4B;AAAA,EAC1D;AAAA,EACA,YAAY,eAAe;AACzB,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,eAAe,IAAI,QAAQ;AAAA,EAClC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,cAAc,eAAe;AAC1C,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAmB,kBAAkB,UAAU,CAAC;AAAA,IACnE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,QAAQ,CAAC;AAAA,MACtB,gBAAgB,SAAS,8BAA8B,IAAI,KAAK,UAAU;AACxE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,4BAA4B,CAAC;AACzD,UAAG,eAAe,UAAU,gBAAgB,GAAG,WAAW;AAC1D,UAAG,eAAe,UAAU,oBAAoB,CAAC;AAAA,QACnD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,6BAA6B,GAAG;AACjF,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,QACtE;AAAA,MACF;AAAA,MACA,WAAW,SAAS,qBAAqB,IAAI,KAAK;AAChD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AAAA,QACxE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,eAAe;AAAA,MACjB;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,eAAe;AAAA,MACjB;AAAA,MACA,UAAU,CAAC,OAAO;AAAA,MAClB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,mBAAmB,EAAE,CAAC;AAAA,MACzD,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB,GAAG;AACtB,UAAG,WAAW,GAAG,uCAAuC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,uCAAuC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QACpN;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,eAAe,WAAW,cAAc,MAAM;AAC3E,WAAW,CAAC,aAAa,CAAC,GAAG,eAAe,WAAW,cAAc,MAAM;AAC3E,WAAW,CAAC,aAAa,CAAC,GAAG,eAAe,WAAW,iBAAiB,MAAM;AAAA,CAC7E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,QACjC,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,mBAAN,MAAuB;AAAC;AACxB,IAAM,wBAAwB;AAC9B,IAAI,SAAS;AACb,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB,OAAO;AACzB,SAAK,gBAAgB,qBAAqB,OAAO,IAAI;AAAA,EACvD;AAAA,EACA,IAAI,WAAW;AACb,WAAO,CAAC,OAAO,QAAQ,EAAE,QAAQ,KAAK,aAAa,MAAM,KAAK,aAAa;AAAA,EAC7E;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,WAAW,mBAAmB,CAAC,KAAK;AAAA,EAClD;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,SAAS,OAAO,KAAK,eAAe,YAAY,KAAK,aAAa,KAAK,WAAW;AAAA,EAChG;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,OAAO,KAAK,eAAe,YAAY,KAAK,aAAa,KAAK,WAAW;AAAA,EAClF;AAAA,EACA,YAAY,iBAAiB,QAAQ,KAAK,gBAAgB,QAAQ;AAChE,SAAK,kBAAkB;AACvB,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,gBAAgB;AACrB,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,iBAAiB,IAAI,aAAa,IAAI;AAC3C,SAAK,wBAAwB,IAAI,aAAa;AAC9C,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,QAAQ,IAAI,aAAa;AAG9B,SAAK,UAAU,IAAI,UAAU;AAE7B,SAAK,OAAO,IAAI,UAAU;AAC1B,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,uBAAuB,aAAa;AACzC,SAAK,mBAAmB,aAAa;AACrC,SAAK,4BAA4B,aAAa;AAC9C,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW;AACT,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AACvB,SAAK,KAAK,QAAQ;AAClB,SAAK,qBAAqB,YAAY;AACtC,SAAK,iBAAiB,YAAY;AAClC,SAAK,0BAA0B,YAAY;AAAA,EAC7C;AAAA,EACA,qBAAqB;AACnB,SAAK,OAAO,kBAAkB,MAAM;AAClC,cAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,YAAY,CAAC;AAAA,IACjD,CAAC;AACD,SAAK,qBAAqB;AAC1B,SAAK,yBAAyB;AAG9B,SAAK,mBAAmB,KAAK,KAAK,QAAQ,UAAU,MAAM;AACxD,YAAM,gBAAgB,KAAK,cAAc,KAAK,aAAa;AAG3D,UAAI,kBAAkB,KAAK,eAAe;AACxC,cAAM,OAAO,KAAK,KAAK,QAAQ;AAC/B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,KAAK,CAAC,EAAE,UAAU;AAIpB,iBAAK,gBAAgB,KAAK,gBAAgB;AAC1C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,WAAK,qBAAqB;AAC1B,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,wBAAwB;AAGtB,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,cAAc,KAAK,aAAa;AAGhF,QAAI,KAAK,kBAAkB,eAAe;AACxC,YAAM,aAAa,KAAK,iBAAiB;AACzC,UAAI,CAAC,YAAY;AACf,aAAK,eAAe,KAAK,KAAK,kBAAkB,aAAa,CAAC;AAAA,MAChE;AAGA,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,aAAK,KAAK,QAAQ,CAAC,KAAK,UAAU,IAAI,WAAW,UAAU,aAAa;AACxE,YAAI,CAAC,YAAY;AACf,eAAK,sBAAsB,KAAK,aAAa;AAAA,QAC/C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,SAAK,KAAK,QAAQ,CAAC,KAAK,UAAU;AAChC,UAAI,WAAW,QAAQ;AAGvB,UAAI,KAAK,iBAAiB,QAAQ,IAAI,aAAa,KAAK,CAAC,IAAI,QAAQ;AACnE,YAAI,SAAS,gBAAgB,KAAK;AAAA,MACpC;AAAA,IACF,CAAC;AACD,QAAI,KAAK,kBAAkB,eAAe;AACxC,WAAK,gBAAgB;AACrB,WAAK,IAAI,aAAa;AAAA,IACxB;AAAA,EACF;AAAA,EACA,QAAQ,OAAO,GAAG;AAChB,MAAE,eAAe;AACjB,MAAE,gBAAgB;AAClB,SAAK,QAAQ,KAAK;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,QAAQ;AACN,SAAK,MAAM,KAAK;AAAA,EAClB;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,IAAI,KAAK,KAAK,SAAS,GAAG,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;AAAA,EAC/D;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,QAAQ,IAAI,iBAAiB;AACnC,UAAM,QAAQ;AACd,QAAI,KAAK,QAAQ,KAAK,KAAK,QAAQ;AACjC,YAAM,MAAM,KAAK,KAAK,QAAQ,EAAE,KAAK;AACrC,WAAK,KAAK,QAAQ,CAAC,KAAK,MAAM;AAC5B,YAAI,MAAM,OAAO;AACf,cAAI,WAAW,KAAK;AAAA,QACtB;AAAA,MACF,CAAC;AACD,YAAM,IAAI,SAAS,KAAK;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB,YAAY;AAAA,IACxC;AACA,SAAK,uBAAuB,MAAM,GAAG,KAAK,KAAK,IAAI,SAAO,IAAI,YAAY,CAAC,EAAE,UAAU,MAAM,KAAK,IAAI,aAAa,CAAC;AAAA,EACtH;AAAA,EACA,2BAA2B;AACzB,SAAK,QAAQ,QAAQ,KAAK,UAAU,KAAK,OAAO,CAAC,EAAE,UAAU,UAAQ;AACnE,WAAK,KAAK,MAAM,KAAK,OAAO,SAAO,IAAI,kBAAkB,IAAI,CAAC;AAC9D,WAAK,KAAK,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,KAAK,MAAM;AAC1B,QAAI,OAAO,KAAK,oBAAoB,YAAY;AAC9C,YAAM,aAAa,mBAAmB,KAAK,gBAAgB,KAAK,IAAI,CAAC;AACrE,aAAO,WAAW,KAAK,MAAM,GAAG,UAAU,KAAK,QAAQ,CAAC;AAAA,IAC1D,OAAO;AACL,aAAO,GAAG,IAAI;AAAA,IAChB;AAAA,EACF;AAAA,EACA,aAAa,KAAK,OAAO,GAAG;AAC1B,QAAI,CAAC,IAAI,YAAY;AAEnB,UAAI,QAAQ,KAAK;AACjB,UAAI,CAAC,KAAK,uBAAuB,OAAO,CAAC,GAAG;AAC1C,aAAK,iBAAiB,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,uBAAuB,OAAO,OAAO;AACnC,UAAM,SAAS,MAAM;AACrB,QAAI,KAAK,cAAc;AACrB,aAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,EAAE,KAAK,GAAG,eAAe,WAAW,cAAc,SAAS,MAAM;AAAA,IAC9F,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,mBAAmB,KAAK,GAAG;AACzB,QAAI,CAAC,IAAI,YAAY;AAEnB,UAAI,cAAc,KAAK,CAAC;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,0BAA0B,YAAY;AAC3C,SAAK,4BAA4B,KAAK,iBAAiB,KAAK,eAAe,KAAK,EAAE,UAAU,SAAO;AACjG,UAAI,KAAK;AACP,aAAK,kBAAkB;AACvB,aAAK,aAAa,aAAa;AAC/B,aAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,KAAK,OAAO;AACtB,QAAI,IAAI,YAAY;AAClB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,kBAAkB,QAAQ,IAAI;AAAA,EAC5C;AAAA,EACA,gBAAgB,GAAG;AACjB,WAAO,WAAW,KAAK,QAAQ,QAAQ,CAAC;AAAA,EAC1C;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,cAAc;AACrB,UAAI,CAAC,KAAK,QAAQ;AAChB,cAAM,IAAI,MAAM,GAAG,MAAM,sEAAsE;AAAA,MACjG;AACA,WAAK,OAAO,OAAO,KAAK,UAAU,KAAK,QAAQ,GAAG,OAAO,OAAK,aAAa,aAAa,GAAG,UAAU,IAAI,GAAG,MAAM,CAAC,CAAC,EAAE,UAAU,MAAM;AACpI,aAAK,mBAAmB;AACxB,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,OAAO,WAAW;AACzB,YAAM,QAAQ,KAAK,yBAAyB;AAC5C,UAAI,UAAU,KAAK,eAAe;AAChC,aAAK,iBAAiB,KAAK;AAAA,MAC7B;AACA,WAAK,YAAY,UAAU;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,UAAM,OAAO,KAAK,KAAK,QAAQ;AAC/B,UAAM,WAAW,KAAK,aAAa,KAAK,MAAM;AAC9C,WAAO,KAAK,UAAU,SAAO;AAC3B,YAAM,IAAI,IAAI;AACd,aAAO,IAAI,SAAS,EAAE,UAAU,IAAI;AAAA,IACtC,CAAC;AAAA,EACH;AAAA,EACA,aAAa,QAAQ;AACnB,WAAO,UAAQ,OAAO,OAAO,SAAS,KAAK,WAAW,IAAI;AAAA,MACxD,OAAO,KAAK,cAAc,UAAU;AAAA,MACpC,aAAa,KAAK,cAAc,UAAU;AAAA,MAC1C,UAAU;AAAA,MACV,cAAc;AAAA,IAChB,CAAC,IAAI;AAAA,EACP;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAuB,eAAe,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,iBAAiB,GAAM,kBAAuB,gBAAgB,CAAC,GAAM,kBAAuB,QAAQ,CAAC,CAAC;AAAA,IACjP;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,MACzB,gBAAgB,SAAS,iCAAiC,IAAI,KAAK,UAAU;AAC3E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,gBAAgB,CAAC;AAAA,QAC/C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU;AAAA,QAC7D;AAAA,MACF;AAAA,MACA,WAAW,SAAS,wBAAwB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,sBAAsB,CAAC;AAAA,QACxC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,QACrE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,UAAU;AAAA,MACzB,UAAU;AAAA,MACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,iBAAiB,IAAI,WAAW,UAAU,IAAI,WAAW,eAAe,EAAE,qBAAqB,IAAI,WAAW,eAAe,EAAE,0BAA0B,IAAI,WAAW,eAAe,EAAE,qBAAqB,IAAI,UAAU,EAAE,gBAAgB,IAAI,QAAQ,KAAK,EAAE,gBAAgB,IAAI,kBAAkB,KAAK,EAAE,mBAAmB,IAAI,kBAAkB,QAAQ,EAAE,iBAAiB,IAAI,kBAAkB,MAAM,EAAE,kBAAkB,IAAI,kBAAkB,OAAO,EAAE,oBAAoB,IAAI,WAAW,SAAS,EAAE,kBAAkB,IAAI,WAAW,OAAO,EAAE,kBAAkB,IAAI,WAAW,OAAO;AAAA,QACllB;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,eAAe;AAAA,QACf,sBAAsB;AAAA,QACtB,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,eAAe;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,MACf;AAAA,MACA,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,uBAAuB;AAAA,QACvB,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU,CAAC,UAAU;AAAA,MACrB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,mBAAmB;AAAA,MAC3B,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,WAAW,iBAAiB,kBAAkB,WAAW,WAAW,WAAW,YAAY,iBAAiB,aAAa,sBAAsB,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,QAAQ,YAAY,eAAe,IAAI,GAAG,MAAM,UAAU,WAAW,eAAe,YAAY,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,aAAa,sBAAsB,cAAc,WAAW,iBAAiB,kBAAkB,WAAW,WAAW,WAAW,YAAY,eAAe,GAAG,CAAC,SAAS,gBAAgB,GAAG,gBAAgB,iBAAiB,uBAAuB,yBAAyB,SAAS,eAAe,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,gBAAgB,GAAG,SAAS,aAAa,GAAG,CAAC,QAAQ,UAAU,QAAQ,OAAO,gBAAgB,IAAI,0BAA0B,IAAI,GAAG,oBAAoB,GAAG,MAAM,YAAY,OAAO,QAAQ,GAAG,CAAC,GAAG,0BAA0B,+BAA+B,GAAG,CAAC,QAAQ,UAAU,uBAAuB,IAAI,GAAG,aAAa,SAAS,GAAG,MAAM,GAAG,CAAC,QAAQ,UAAU,uBAAuB,IAAI,GAAG,SAAS,WAAW,GAAG,CAAC,QAAQ,YAAY,eAAe,IAAI,GAAG,MAAM,UAAU,WAAW,eAAe,UAAU,CAAC;AAAA,MACrqC,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,eAAe,CAAC;AACjF,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,UAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,OAAO,CAAC;AACjE,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAQ,IAAI,KAAK,UAAU,IAAI,OAAO;AACpD,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,wBAAwB,IAAI,kBAAkB,KAAK,EAAE,2BAA2B,IAAI,kBAAkB,QAAQ,EAAE,yBAAyB,IAAI,kBAAkB,MAAM,EAAE,0BAA0B,IAAI,kBAAkB,OAAO,EAAE,6BAA6B,IAAI,eAAe;AAC/R,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,IAAI;AAAA,QACnC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,sBAAsB,MAAM,SAAS,SAAS,uBAAuB,YAAe,iBAAiB,gBAAmB,iCAAiC,2BAA2B,kBAAkB;AAAA,MACrN,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,WAAW,CAAC,GAAG,kBAAkB,WAAW,UAAU,MAAM;AACxE,WAAW,CAAC,WAAW,CAAC,GAAG,kBAAkB,WAAW,UAAU,MAAM;AACxE,WAAW,CAAC,WAAW,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAC5E,WAAW,CAAC,WAAW,CAAC,GAAG,kBAAkB,WAAW,kBAAkB,MAAM;AAChF,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,aAAa,MAAM;AAC7E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,aAAa,MAAM;AAC7E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,gBAAgB,MAAM;AAChF,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,eAAe,MAAM;AAAA,CAC9E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA0EV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,yBAAyB;AAAA,QACzB,6BAA6B;AAAA,QAC7B,kCAAkC;AAAA,QAClC,6BAA6B;AAAA,QAC7B,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,yBAAyB;AAAA,QACzB,0BAA0B;AAAA,QAC1B,4BAA4B;AAAA,QAC5B,0BAA0B;AAAA,QAC1B,0BAA0B;AAAA,MAC5B;AAAA,MACA,SAAS,CAAC,sBAAsB,MAAM,SAAS,SAAS,uBAAuB,YAAY,gBAAgB,2BAA2B,kBAAkB;AAAA,MACxJ,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,QAC3B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,aAAa,CAAC,mBAAmB,gBAAgB,sBAAsB,uBAAuB,uBAAuB,0BAA0B,4BAA4B,yBAAyB,2BAA2B,gBAAgB,oBAAoB,oBAAoB,0BAA0B;AACvT,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAc;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,mBAAmB,gBAAgB,sBAAsB,uBAAuB,uBAAuB,0BAA0B,4BAA4B,yBAAyB,2BAA2B,gBAAgB,oBAAoB,oBAAoB,0BAA0B;AAAA,MAC7S,SAAS,CAAC,mBAAmB,gBAAgB,sBAAsB,uBAAuB,uBAAuB,0BAA0B,4BAA4B,yBAAyB,2BAA2B,gBAAgB,oBAAoB,oBAAoB,0BAA0B;AAAA,IAC/S,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,mBAAmB,sBAAsB,4BAA4B,yBAAyB,yBAAyB;AAAA,IACnI,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU;AAAA,MACpB,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}