<!-- Settings Sidebar Content -->
<div class="flex-1 flex flex-col h-full border-r " [ngClass]="{
       'bg-[#343541]': themeService.isDarkMode(),
       'bg-slate-100': !themeService.isDarkMode()
     }">
  <!-- Header -->
  <div
    class="px-[var(--padding-small)] py-[var(--padding-small)] border-b flex justify-between flex-col gap-2 w-[16rem]"
    [ngClass]="{'border-[#3a3a45]': themeService.isDarkMode(), 'border-[var(--hover-blue-gray)]': !themeService.isDarkMode()}">
    <div class="flex items-center justify-between w-full">
      <div class="flex items-center gap-2">
        <i class="ri-settings-3-line text-xl"
          [ngClass]="{'text-[#10A37F] text-[var(--primary-purple)]': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}"></i>
        <span class="font-bold text-lg"
          [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">Settings</span>
      </div>
    </div>
  </div>

  <!-- Admin Navigation -->
  <div class="flex-1 flex flex-col overflow-y-auto">
    <div class="space-y-1 *:no-underline transition-[var(--transition-default)] p-4">
      <div class="mb-4 flex items-center gap-2">
        <span class="text-xs uppercase font-medium"
          [ngClass]="{'text-[#ACACBE]': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">General</span>
        <div class="flex-1 h-[1px] opacity-70"
          [ngClass]="{'bg-[#3a3a45]': themeService.isDarkMode(), 'bg-[var(--hover-blue-gray)]': !themeService.isDarkMode()}">
        </div>
      </div>

      <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isSettingsTabActive('prompt-library') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isSettingsTabActive('prompt-library') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToSettings($event, 'prompt-library')">
        <!-- Active indicator bar -->
        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isSettingsTabActive('prompt-library'),
            'opacity-0': !isSettingsTabActive('prompt-library'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-code-line text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('prompt-library') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('prompt-library') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('prompt-library') && themeService.isDarkMode(),
            '': !isSettingsTabActive('prompt-library') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('prompt-library') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('prompt-library') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('prompt-library') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('prompt-library') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">Prompts Library</span>
      </div>

      <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isSettingsTabActive('connections') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isSettingsTabActive('connections') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToSettings($event, 'connections')">
        <!-- Active indicator bar -->
        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isSettingsTabActive('connections'),
            'opacity-0': !isSettingsTabActive('connections'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-link text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('connections') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('connections') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('connections') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('connections') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('connections') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('connections') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('connections') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('connections') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">Connections</span>
      </div>

      <!-- Database Connections -->
      <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isSettingsTabActive('database-connections') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isSettingsTabActive('database-connections') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToSettings($event, 'database-connections')" *ngIf="authService.isAdmin()">
        <!-- Active indicator bar -->
        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isSettingsTabActive('database-connections'),
            'opacity-0': !isSettingsTabActive('database-connections'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-database-2-line text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('database-connections') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('database-connections') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('database-connections') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('database-connections') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('database-connections') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('database-connections') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('database-connections') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('database-connections') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">Database Connections</span>
      </div>

      <div *ngIf="authService.isAdmin()" class="mt-6 mb-4 flex items-center gap-2">
        <span class="text-xs uppercase font-medium"
          [ngClass]="{'text-[#ACACBE]': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">Administration</span>
        <div class="flex-1 h-[1px] opacity-70"
          [ngClass]="{'bg-[#3a3a45]': themeService.isDarkMode(), 'bg-[var(--hover-blue-gray)]': !themeService.isDarkMode()}">
        </div>
      </div>

      <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isSettingsTabActive('user-management') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isSettingsTabActive('user-management') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToSettings($event, 'user-management')" *ngIf="authService.isAdmin()">
        <!-- Active indicator bar -->
        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isSettingsTabActive('user-management'),
            'opacity-0': !isSettingsTabActive('user-management'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-user-settings-line text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('user-management') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('user-management') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('user-management') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('user-management') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('user-management') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('user-management') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('user-management') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('user-management') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">User Management</span>
      </div>

      <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isSettingsTabActive('models') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isSettingsTabActive('models') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToSettings($event, 'models')" *ngIf="authService.isAdmin()">
        <!-- Active indicator bar -->
        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isSettingsTabActive('models'),
            'opacity-0': !isSettingsTabActive('models'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-brain-line text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('models') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('models') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('models') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('models') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('models') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('models') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('models') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('models') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">Models</span>
      </div>

      <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isSettingsTabActive('agents') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isSettingsTabActive('agents') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToSettings($event, 'agents')" *ngIf="authService.isAdmin()">
        <!-- Active indicator bar -->
        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isSettingsTabActive('agents'),
            'opacity-0': !isSettingsTabActive('agents'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-robot-line text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('agents') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('agents') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('agents') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('agents') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('agents') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('agents') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('agents') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('agents') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">Agents</span>
      </div>

      <!-- Agent Analytics Menu Item -->
      <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isSettingsTabActive('agent-analytics') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isSettingsTabActive('agent-analytics') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToSettings($event, 'agent-analytics')" *ngIf="authService.isAdmin()">
        <!-- Active indicator bar -->
        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isSettingsTabActive('agent-analytics'),
            'opacity-0': !isSettingsTabActive('agent-analytics'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-bar-chart-line text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('agent-analytics') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('agent-analytics') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('agent-analytics') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('agent-analytics') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('agent-analytics') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('agent-analytics') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('agent-analytics') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('agent-analytics') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">Agent Analytics</span>
      </div>

      <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isSettingsTabActive('evaluations') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isSettingsTabActive('evaluations') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToSettings($event, 'evaluations')" *ngIf="authService.isAdmin()">
        <!-- Active indicator bar -->
        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isSettingsTabActive('evaluations'),
            'opacity-0': !isSettingsTabActive('evaluations'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-test-tube-line text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('evaluations') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('evaluations') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('evaluations') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('evaluations') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('evaluations') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('evaluations') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('evaluations') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('evaluations') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">Agent Evaluation</span>
      </div>

      <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isSettingsTabActive('plugins') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isSettingsTabActive('plugins') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToSettings($event, 'plugins')" *ngIf="authService.isAdmin()">
        <!-- Active indicator bar -->
        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isSettingsTabActive('plugins'),
            'opacity-0': !isSettingsTabActive('plugins'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-puzzle-line text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('plugins') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('plugins') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('plugins') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('plugins') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('plugins') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('plugins') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('plugins') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('plugins') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">Plugins</span>
      </div>

      <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isSettingsTabActive('memory') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isSettingsTabActive('memory') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToSettings($event, 'memory')" *ngIf="authService.isAdmin()">
        <!-- Active indicator bar -->
        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isSettingsTabActive('memory'),
            'opacity-0': !isSettingsTabActive('memory'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-sticky-note-line text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('memory') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('memory') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('memory') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('memory') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('memory') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('memory') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('memory') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('memory') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">Memory</span>
      </div>

      <!-- <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isSettingsTabActive('files') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isSettingsTabActive('files') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToSettings($event, 'files')" *ngIf="authService.isAdmin()">

        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isSettingsTabActive('files'),
            'opacity-0': !isSettingsTabActive('files'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>

        <i class="ri-file-line text-lg transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('files') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('files') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('files') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('files') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('files') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('files') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('files') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('files') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">Files</span>
      </div> -->


      <div class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
          'bg-[var(--secondary-purple)]': isSettingsTabActive('workspace') && !themeService.isDarkMode(),
          'bg-[var(--active-tab-bg)]': isSettingsTabActive('workspace') && themeService.isDarkMode(),
          'hover:bg-[var(--primary-purple)]': true,
          'hover:text-white': true
        }" (click)="navigateToSettings($event, 'workspace')" *ngIf="authService.isAdmin()">
        <!-- Active indicator bar -->
        <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
            'opacity-100': isSettingsTabActive('workspace'),
            'opacity-0': !isSettingsTabActive('workspace'),
            'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
          }"></div>


        <i class="ri-home-line transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('workspace') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('workspace') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('workspace') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('workspace') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-medium text-sm transition-colors duration-200" [ngClass]="{
            'text-black': isSettingsTabActive('workspace') && !themeService.isDarkMode(),
            'text-white': isSettingsTabActive('workspace') && themeService.isDarkMode(),
            'text-[#ACACBE]': !isSettingsTabActive('workspace') && themeService.isDarkMode(),
            'text-[var(--text-dark)]': !isSettingsTabActive('workspace') && !themeService.isDarkMode(),
            'group-hover:text-white': themeService.isDarkMode(),
            'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
          }">Workspace</span>
      </div>

    </div>
  </div>
</div>
