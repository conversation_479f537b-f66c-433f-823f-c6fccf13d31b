export interface AgentEvaluation {
    id: string;
    agentName: string;
    prompt: string;
    output?: string;
    expectedOutput?: string;
    score?: number;
    createdAt: Date;
    evaluatedAt?: Date;
}

export interface CreateAgentEvaluation {
    agentName: string;
    prompt: string;
    expectedOutput?: string;
}

export interface EvaluationResult {
    evaluationId: string;
    score: number;
    evaluation: string;
    improvementSuggestions: string;
    updatedPrompt?: string;
}

export interface AgentDefinition {
    guid: string;
    agentName: string;
    instructions: string;
    userInstructions: string;
    modelName: string;
    workspace: string;
    toolsArray: string[];
}
