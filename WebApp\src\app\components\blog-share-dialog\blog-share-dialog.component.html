<div class="blog-share-dialog">
  <div class="dialog-header">
    <div class="flex items-center gap-3">
      <div
        class="w-10 h-10 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center border border-[var(--primary-purple)] border-opacity-20">
        <i class="ri-share-line text-[var(--primary-purple)] text-xl"></i>
      </div>
      <h3 class="text-lg font-medium text-[var(--text-dark)]">Share Blog Post</h3>
    </div>
  </div>

  <div class="dialog-body mt-4">
    <div class="mb-4">
      <label class="font-medium text-[var(--text-dark)] block mb-2">Share URL</label>
      <input
        type="url"
        id="shareUrl"
        [(ngModel)]="shareUrl"
        class="w-full p-2 bg-[var(--background-white)] text-[var(--text-dark)] border border-[var(--hover-blue-gray)] rounded-md focus:outline-none focus:border-[var(--primary-purple)] transition-all"
        placeholder="https://your-blog-platform.com/api/share" />
      <p class="text-xs text-[var(--text-medium-gray)] mt-1">Enter the URL where you want to share this blog post</p>
    </div>

    <div class="mb-4">
      <label class="font-medium text-[var(--text-dark)] block mb-2">Blog Title</label>
      <input
        type="text"
        [(ngModel)]="blogTitle"
        class="w-full p-3 bg-[var(--background-white)] text-[var(--text-dark)] border border-[var(--hover-blue-gray)] rounded-md focus:outline-none focus:border-[var(--primary-purple)] transition-all"
        placeholder="Enter blog title..." />
      <p class="text-xs text-[var(--text-medium-gray)] mt-1">Edit the title as needed</p>
    </div>

    <div class="mb-4">
      <div class="flex items-center justify-between mb-2">
        <label class="font-medium text-[var(--text-dark)]" for="blogContent">Blog Content</label>
        <button
          type="button"
          (click)="copyBlogContent()"
          class="text-xs bg-[var(--hover-blue-gray)] hover:bg-[var(--primary-purple)] text-[var(--text-dark)] hover:text-white px-3 py-1.5 rounded-md transition-all">
          <i class="ri-file-copy-line mr-1"></i>Copy
        </button>
      </div>
      <textarea
        id="blogContent"
        [(ngModel)]="blogContent"
        class="w-full p-3 bg-[var(--background-white)] text-[var(--text-dark)] border border-[var(--hover-blue-gray)] rounded-md focus:outline-none focus:border-[var(--primary-purple)] min-h-[300px] font-mono text-sm transition-all resize-vertical"
        placeholder="Your blog content will appear here..."></textarea>
      <p class="text-xs text-[var(--text-medium-gray)] mt-1">Edit the blog content as needed before sharing</p>
    </div>

    <div class="flex justify-end gap-3 mt-6">
      <button
        type="button"
        (click)="cancel()"
        class="px-4 py-2 text-sm border border-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md bg-[var(--hover-blue-gray)] transition-all">
        Cancel
      </button>

      <button
        type="button"
        (click)="sendBlog()"
        [disabled]="isLoading || !shareUrl.trim() || !blogContent.trim() || !blogTitle.trim()"
        class="px-4 py-2 text-sm bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] disabled:opacity-50 disabled:cursor-not-allowed text-[var(--background-white)] rounded-md transition-all">
        <i *ngIf="isLoading" class="ri-loader-4-line animate-spin mr-1"></i>
        {{ isLoading ? 'Sharing...' : 'Share Blog' }}
      </button>
    </div>
  </div>
</div>
