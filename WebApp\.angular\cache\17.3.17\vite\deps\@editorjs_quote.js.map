{"version": 3, "sources": ["../../../../../node_modules/@editorjs/quote/dist/quote.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var t=document.createElement(\"style\");t.appendChild(document.createTextNode(\".cdx-quote-icon svg{transform:rotate(180deg)}.cdx-quote{margin:0}.cdx-quote__text{min-height:158px;margin-bottom:10px}.cdx-quote [contentEditable=true][data-placeholder]:before{position:absolute;content:attr(data-placeholder);color:#707684;font-weight:400;opacity:0}.cdx-quote [contentEditable=true][data-placeholder]:empty:before{opacity:1}.cdx-quote [contentEditable=true][data-placeholder]:empty:focus:before{opacity:0}.cdx-quote-settings{display:flex}.cdx-quote-settings .cdx-settings-button{width:50%}\")),document.head.appendChild(t)}}catch(e){console.error(\"vite-plugin-css-injected-by-js\",e)}})();\nconst De = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M18 7L6 7\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M18 17H6\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 12L8 12\"/></svg>', He = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M17 7L5 7\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M17 17H5\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M13 12L5 12\"/></svg>', Re = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 10.8182L9 10.8182C8.80222 10.8182 8.60888 10.7649 8.44443 10.665C8.27998 10.5651 8.15181 10.4231 8.07612 10.257C8.00043 10.0909 7.98063 9.90808 8.01922 9.73174C8.0578 9.55539 8.15304 9.39341 8.29289 9.26627C8.43275 9.13913 8.61093 9.05255 8.80491 9.01747C8.99889 8.98239 9.19996 9.00039 9.38268 9.0692C9.56541 9.13801 9.72159 9.25453 9.83147 9.40403C9.94135 9.55353 10 9.72929 10 9.90909L10 12.1818C10 12.664 9.78929 13.1265 9.41421 13.4675C9.03914 13.8084 8.53043 14 8 14\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 10.8182L15 10.8182C14.8022 10.8182 14.6089 10.7649 14.4444 10.665C14.28 10.5651 14.1518 10.4231 14.0761 10.257C14.0004 10.0909 13.9806 9.90808 14.0192 9.73174C14.0578 9.55539 14.153 9.39341 14.2929 9.26627C14.4327 9.13913 14.6109 9.05255 14.8049 9.01747C14.9989 8.98239 15.2 9.00039 15.3827 9.0692C15.5654 9.13801 15.7216 9.25453 15.8315 9.40403C15.9414 9.55353 16 9.72929 16 9.90909L16 12.1818C16 12.664 15.7893 13.1265 15.4142 13.4675C15.0391 13.8084 14.5304 14 14 14\"/></svg>';\nvar b = typeof globalThis < \"u\" ? globalThis : typeof window < \"u\" ? window : typeof global < \"u\" ? global : typeof self < \"u\" ? self : {};\nfunction Fe(e) {\n  if (e.__esModule)\n    return e;\n  var t = e.default;\n  if (typeof t == \"function\") {\n    var n = function r() {\n      return this instanceof r ? Reflect.construct(t, arguments, this.constructor) : t.apply(this, arguments);\n    };\n    n.prototype = t.prototype;\n  } else\n    n = {};\n  return Object.defineProperty(n, \"__esModule\", { value: !0 }), Object.keys(e).forEach(function(r) {\n    var i = Object.getOwnPropertyDescriptor(e, r);\n    Object.defineProperty(n, r, i.get ? i : {\n      enumerable: !0,\n      get: function() {\n        return e[r];\n      }\n    });\n  }), n;\n}\nvar v = {}, P = {}, j = {};\nObject.defineProperty(j, \"__esModule\", { value: !0 });\nj.allInputsSelector = We;\nfunction We() {\n  var e = [\"text\", \"password\", \"email\", \"number\", \"search\", \"tel\", \"url\"];\n  return \"[contenteditable=true], textarea, input:not([type]), \" + e.map(function(t) {\n    return 'input[type=\"'.concat(t, '\"]');\n  }).join(\", \");\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.allInputsSelector = void 0;\n  var t = j;\n  Object.defineProperty(e, \"allInputsSelector\", { enumerable: !0, get: function() {\n    return t.allInputsSelector;\n  } });\n})(P);\nvar c = {}, T = {};\nObject.defineProperty(T, \"__esModule\", { value: !0 });\nT.isNativeInput = Ue;\nfunction Ue(e) {\n  var t = [\n    \"INPUT\",\n    \"TEXTAREA\"\n  ];\n  return e && e.tagName ? t.includes(e.tagName) : !1;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isNativeInput = void 0;\n  var t = T;\n  Object.defineProperty(e, \"isNativeInput\", { enumerable: !0, get: function() {\n    return t.isNativeInput;\n  } });\n})(c);\nvar ie = {}, C = {};\nObject.defineProperty(C, \"__esModule\", { value: !0 });\nC.append = qe;\nfunction qe(e, t) {\n  Array.isArray(t) ? t.forEach(function(n) {\n    e.appendChild(n);\n  }) : e.appendChild(t);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.append = void 0;\n  var t = C;\n  Object.defineProperty(e, \"append\", { enumerable: !0, get: function() {\n    return t.append;\n  } });\n})(ie);\nvar L = {}, S = {};\nObject.defineProperty(S, \"__esModule\", { value: !0 });\nS.blockElements = ze;\nfunction ze() {\n  return [\n    \"address\",\n    \"article\",\n    \"aside\",\n    \"blockquote\",\n    \"canvas\",\n    \"div\",\n    \"dl\",\n    \"dt\",\n    \"fieldset\",\n    \"figcaption\",\n    \"figure\",\n    \"footer\",\n    \"form\",\n    \"h1\",\n    \"h2\",\n    \"h3\",\n    \"h4\",\n    \"h5\",\n    \"h6\",\n    \"header\",\n    \"hgroup\",\n    \"hr\",\n    \"li\",\n    \"main\",\n    \"nav\",\n    \"noscript\",\n    \"ol\",\n    \"output\",\n    \"p\",\n    \"pre\",\n    \"ruby\",\n    \"section\",\n    \"table\",\n    \"tbody\",\n    \"thead\",\n    \"tr\",\n    \"tfoot\",\n    \"ul\",\n    \"video\"\n  ];\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.blockElements = void 0;\n  var t = S;\n  Object.defineProperty(e, \"blockElements\", { enumerable: !0, get: function() {\n    return t.blockElements;\n  } });\n})(L);\nvar ae = {}, M = {};\nObject.defineProperty(M, \"__esModule\", { value: !0 });\nM.calculateBaseline = Ge;\nfunction Ge(e) {\n  var t = window.getComputedStyle(e), n = parseFloat(t.fontSize), r = parseFloat(t.lineHeight) || n * 1.2, i = parseFloat(t.paddingTop), a = parseFloat(t.borderTopWidth), l = parseFloat(t.marginTop), u = n * 0.8, d = (r - n) / 2, s = l + a + i + d + u;\n  return s;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.calculateBaseline = void 0;\n  var t = M;\n  Object.defineProperty(e, \"calculateBaseline\", { enumerable: !0, get: function() {\n    return t.calculateBaseline;\n  } });\n})(ae);\nvar le = {}, k = {}, w = {}, N = {};\nObject.defineProperty(N, \"__esModule\", { value: !0 });\nN.isContentEditable = Ke;\nfunction Ke(e) {\n  return e.contentEditable === \"true\";\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isContentEditable = void 0;\n  var t = N;\n  Object.defineProperty(e, \"isContentEditable\", { enumerable: !0, get: function() {\n    return t.isContentEditable;\n  } });\n})(w);\nObject.defineProperty(k, \"__esModule\", { value: !0 });\nk.canSetCaret = Qe;\nvar Xe = c, Ye = w;\nfunction Qe(e) {\n  var t = !0;\n  if ((0, Xe.isNativeInput)(e))\n    switch (e.type) {\n      case \"file\":\n      case \"checkbox\":\n      case \"radio\":\n      case \"hidden\":\n      case \"submit\":\n      case \"button\":\n      case \"image\":\n      case \"reset\":\n        t = !1;\n        break;\n    }\n  else\n    t = (0, Ye.isContentEditable)(e);\n  return t;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.canSetCaret = void 0;\n  var t = k;\n  Object.defineProperty(e, \"canSetCaret\", { enumerable: !0, get: function() {\n    return t.canSetCaret;\n  } });\n})(le);\nvar y = {}, I = {};\nfunction Ve(e, t, n) {\n  const r = n.value !== void 0 ? \"value\" : \"get\", i = n[r], a = `#${t}Cache`;\n  if (n[r] = function(...l) {\n    return this[a] === void 0 && (this[a] = i.apply(this, l)), this[a];\n  }, r === \"get\" && n.set) {\n    const l = n.set;\n    n.set = function(u) {\n      delete e[a], l.apply(this, u);\n    };\n  }\n  return n;\n}\nfunction ue() {\n  const e = {\n    win: !1,\n    mac: !1,\n    x11: !1,\n    linux: !1\n  }, t = Object.keys(e).find((n) => window.navigator.appVersion.toLowerCase().indexOf(n) !== -1);\n  return t !== void 0 && (e[t] = !0), e;\n}\nfunction A(e) {\n  return e != null && e !== \"\" && (typeof e != \"object\" || Object.keys(e).length > 0);\n}\nfunction Ze(e) {\n  return !A(e);\n}\nconst Je = () => typeof window < \"u\" && window.navigator !== null && A(window.navigator.platform) && (/iP(ad|hone|od)/.test(window.navigator.platform) || window.navigator.platform === \"MacIntel\" && window.navigator.maxTouchPoints > 1);\nfunction xe(e) {\n  const t = ue();\n  return e = e.replace(/shift/gi, \"⇧\").replace(/backspace/gi, \"⌫\").replace(/enter/gi, \"⏎\").replace(/up/gi, \"↑\").replace(/left/gi, \"→\").replace(/down/gi, \"↓\").replace(/right/gi, \"←\").replace(/escape/gi, \"⎋\").replace(/insert/gi, \"Ins\").replace(/delete/gi, \"␡\").replace(/\\+/gi, \"+\"), t.mac ? e = e.replace(/ctrl|cmd/gi, \"⌘\").replace(/alt/gi, \"⌥\") : e = e.replace(/cmd/gi, \"Ctrl\").replace(/windows/gi, \"WIN\"), e;\n}\nfunction et(e) {\n  return e[0].toUpperCase() + e.slice(1);\n}\nfunction tt(e) {\n  const t = document.createElement(\"div\");\n  t.style.position = \"absolute\", t.style.left = \"-999px\", t.style.bottom = \"-999px\", t.innerHTML = e, document.body.appendChild(t);\n  const n = window.getSelection(), r = document.createRange();\n  if (r.selectNode(t), n === null)\n    throw new Error(\"Cannot copy text to clipboard\");\n  n.removeAllRanges(), n.addRange(r), document.execCommand(\"copy\"), document.body.removeChild(t);\n}\nfunction nt(e, t, n) {\n  let r;\n  return (...i) => {\n    const a = this, l = () => {\n      r = void 0, n !== !0 && e.apply(a, i);\n    }, u = n === !0 && r !== void 0;\n    window.clearTimeout(r), r = window.setTimeout(l, t), u && e.apply(a, i);\n  };\n}\nfunction o(e) {\n  return Object.prototype.toString.call(e).match(/\\s([a-zA-Z]+)/)[1].toLowerCase();\n}\nfunction rt(e) {\n  return o(e) === \"boolean\";\n}\nfunction oe(e) {\n  return o(e) === \"function\" || o(e) === \"asyncfunction\";\n}\nfunction it(e) {\n  return oe(e) && /^\\s*class\\s+/.test(e.toString());\n}\nfunction at(e) {\n  return o(e) === \"number\";\n}\nfunction g(e) {\n  return o(e) === \"object\";\n}\nfunction lt(e) {\n  return Promise.resolve(e) === e;\n}\nfunction ut(e) {\n  return o(e) === \"string\";\n}\nfunction ot(e) {\n  return o(e) === \"undefined\";\n}\nfunction O(e, ...t) {\n  if (!t.length)\n    return e;\n  const n = t.shift();\n  if (g(e) && g(n))\n    for (const r in n)\n      g(n[r]) ? (e[r] === void 0 && Object.assign(e, { [r]: {} }), O(e[r], n[r])) : Object.assign(e, { [r]: n[r] });\n  return O(e, ...t);\n}\nfunction st(e, t, n) {\n  const r = `«${t}» is deprecated and will be removed in the next major release. Please use the «${n}» instead.`;\n  e && console.warn(r);\n}\nfunction ct(e) {\n  try {\n    return new URL(e).href;\n  } catch {\n  }\n  return e.substring(0, 2) === \"//\" ? window.location.protocol + e : window.location.origin + e;\n}\nfunction dt(e) {\n  return e > 47 && e < 58 || e === 32 || e === 13 || e === 229 || e > 64 && e < 91 || e > 95 && e < 112 || e > 185 && e < 193 || e > 218 && e < 223;\n}\nconst ft = {\n  BACKSPACE: 8,\n  TAB: 9,\n  ENTER: 13,\n  SHIFT: 16,\n  CTRL: 17,\n  ALT: 18,\n  ESC: 27,\n  SPACE: 32,\n  LEFT: 37,\n  UP: 38,\n  DOWN: 40,\n  RIGHT: 39,\n  DELETE: 46,\n  META: 91,\n  SLASH: 191\n}, pt = {\n  LEFT: 0,\n  WHEEL: 1,\n  RIGHT: 2,\n  BACKWARD: 3,\n  FORWARD: 4\n};\nclass vt {\n  constructor() {\n    this.completed = Promise.resolve();\n  }\n  /**\n   * Add new promise to queue\n   * @param operation - promise should be added to queue\n   */\n  add(t) {\n    return new Promise((n, r) => {\n      this.completed = this.completed.then(t).then(n).catch(r);\n    });\n  }\n}\nfunction gt(e, t, n = void 0) {\n  let r, i, a, l = null, u = 0;\n  n || (n = {});\n  const d = function() {\n    u = n.leading === !1 ? 0 : Date.now(), l = null, a = e.apply(r, i), l === null && (r = i = null);\n  };\n  return function() {\n    const s = Date.now();\n    !u && n.leading === !1 && (u = s);\n    const f = t - (s - u);\n    return r = this, i = arguments, f <= 0 || f > t ? (l && (clearTimeout(l), l = null), u = s, a = e.apply(r, i), l === null && (r = i = null)) : !l && n.trailing !== !1 && (l = setTimeout(d, f)), a;\n  };\n}\nconst mt = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  PromiseQueue: vt,\n  beautifyShortcut: xe,\n  cacheable: Ve,\n  capitalize: et,\n  copyTextToClipboard: tt,\n  debounce: nt,\n  deepMerge: O,\n  deprecationAssert: st,\n  getUserOS: ue,\n  getValidUrl: ct,\n  isBoolean: rt,\n  isClass: it,\n  isEmpty: Ze,\n  isFunction: oe,\n  isIosDevice: Je,\n  isNumber: at,\n  isObject: g,\n  isPrintableKey: dt,\n  isPromise: lt,\n  isString: ut,\n  isUndefined: ot,\n  keyCodes: ft,\n  mouseButtons: pt,\n  notEmpty: A,\n  throttle: gt,\n  typeOf: o\n}, Symbol.toStringTag, { value: \"Module\" })), $ = /* @__PURE__ */ Fe(mt);\nObject.defineProperty(I, \"__esModule\", { value: !0 });\nI.containsOnlyInlineElements = _t;\nvar bt = $, yt = L;\nfunction _t(e) {\n  var t;\n  (0, bt.isString)(e) ? (t = document.createElement(\"div\"), t.innerHTML = e) : t = e;\n  var n = function(r) {\n    return !(0, yt.blockElements)().includes(r.tagName.toLowerCase()) && Array.from(r.children).every(n);\n  };\n  return Array.from(t.children).every(n);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.containsOnlyInlineElements = void 0;\n  var t = I;\n  Object.defineProperty(e, \"containsOnlyInlineElements\", { enumerable: !0, get: function() {\n    return t.containsOnlyInlineElements;\n  } });\n})(y);\nvar se = {}, B = {}, _ = {}, D = {};\nObject.defineProperty(D, \"__esModule\", { value: !0 });\nD.make = ht;\nfunction ht(e, t, n) {\n  var r;\n  t === void 0 && (t = null), n === void 0 && (n = {});\n  var i = document.createElement(e);\n  if (Array.isArray(t)) {\n    var a = t.filter(function(u) {\n      return u !== void 0;\n    });\n    (r = i.classList).add.apply(r, a);\n  } else\n    t !== null && i.classList.add(t);\n  for (var l in n)\n    Object.prototype.hasOwnProperty.call(n, l) && (i[l] = n[l]);\n  return i;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.make = void 0;\n  var t = D;\n  Object.defineProperty(e, \"make\", { enumerable: !0, get: function() {\n    return t.make;\n  } });\n})(_);\nObject.defineProperty(B, \"__esModule\", { value: !0 });\nB.fragmentToString = Ot;\nvar Et = _;\nfunction Ot(e) {\n  var t = (0, Et.make)(\"div\");\n  return t.appendChild(e), t.innerHTML;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.fragmentToString = void 0;\n  var t = B;\n  Object.defineProperty(e, \"fragmentToString\", { enumerable: !0, get: function() {\n    return t.fragmentToString;\n  } });\n})(se);\nvar ce = {}, H = {};\nObject.defineProperty(H, \"__esModule\", { value: !0 });\nH.getContentLength = jt;\nvar Pt = c;\nfunction jt(e) {\n  var t, n;\n  return (0, Pt.isNativeInput)(e) ? e.value.length : e.nodeType === Node.TEXT_NODE ? e.length : (n = (t = e.textContent) === null || t === void 0 ? void 0 : t.length) !== null && n !== void 0 ? n : 0;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.getContentLength = void 0;\n  var t = H;\n  Object.defineProperty(e, \"getContentLength\", { enumerable: !0, get: function() {\n    return t.getContentLength;\n  } });\n})(ce);\nvar R = {}, F = {}, re = b && b.__spreadArray || function(e, t, n) {\n  if (n || arguments.length === 2)\n    for (var r = 0, i = t.length, a; r < i; r++)\n      (a || !(r in t)) && (a || (a = Array.prototype.slice.call(t, 0, r)), a[r] = t[r]);\n  return e.concat(a || Array.prototype.slice.call(t));\n};\nObject.defineProperty(F, \"__esModule\", { value: !0 });\nF.getDeepestBlockElements = de;\nvar Tt = y;\nfunction de(e) {\n  return (0, Tt.containsOnlyInlineElements)(e) ? [e] : Array.from(e.children).reduce(function(t, n) {\n    return re(re([], t, !0), de(n), !0);\n  }, []);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.getDeepestBlockElements = void 0;\n  var t = F;\n  Object.defineProperty(e, \"getDeepestBlockElements\", { enumerable: !0, get: function() {\n    return t.getDeepestBlockElements;\n  } });\n})(R);\nvar fe = {}, W = {}, h = {}, U = {};\nObject.defineProperty(U, \"__esModule\", { value: !0 });\nU.isLineBreakTag = Ct;\nfunction Ct(e) {\n  return [\n    \"BR\",\n    \"WBR\"\n  ].includes(e.tagName);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isLineBreakTag = void 0;\n  var t = U;\n  Object.defineProperty(e, \"isLineBreakTag\", { enumerable: !0, get: function() {\n    return t.isLineBreakTag;\n  } });\n})(h);\nvar E = {}, q = {};\nObject.defineProperty(q, \"__esModule\", { value: !0 });\nq.isSingleTag = Lt;\nfunction Lt(e) {\n  return [\n    \"AREA\",\n    \"BASE\",\n    \"BR\",\n    \"COL\",\n    \"COMMAND\",\n    \"EMBED\",\n    \"HR\",\n    \"IMG\",\n    \"INPUT\",\n    \"KEYGEN\",\n    \"LINK\",\n    \"META\",\n    \"PARAM\",\n    \"SOURCE\",\n    \"TRACK\",\n    \"WBR\"\n  ].includes(e.tagName);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isSingleTag = void 0;\n  var t = q;\n  Object.defineProperty(e, \"isSingleTag\", { enumerable: !0, get: function() {\n    return t.isSingleTag;\n  } });\n})(E);\nObject.defineProperty(W, \"__esModule\", { value: !0 });\nW.getDeepestNode = pe;\nvar St = c, Mt = h, kt = E;\nfunction pe(e, t) {\n  t === void 0 && (t = !1);\n  var n = t ? \"lastChild\" : \"firstChild\", r = t ? \"previousSibling\" : \"nextSibling\";\n  if (e.nodeType === Node.ELEMENT_NODE && e[n]) {\n    var i = e[n];\n    if ((0, kt.isSingleTag)(i) && !(0, St.isNativeInput)(i) && !(0, Mt.isLineBreakTag)(i))\n      if (i[r])\n        i = i[r];\n      else if (i.parentNode !== null && i.parentNode[r])\n        i = i.parentNode[r];\n      else\n        return i.parentNode;\n    return pe(i, t);\n  }\n  return e;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.getDeepestNode = void 0;\n  var t = W;\n  Object.defineProperty(e, \"getDeepestNode\", { enumerable: !0, get: function() {\n    return t.getDeepestNode;\n  } });\n})(fe);\nvar ve = {}, z = {}, p = b && b.__spreadArray || function(e, t, n) {\n  if (n || arguments.length === 2)\n    for (var r = 0, i = t.length, a; r < i; r++)\n      (a || !(r in t)) && (a || (a = Array.prototype.slice.call(t, 0, r)), a[r] = t[r]);\n  return e.concat(a || Array.prototype.slice.call(t));\n};\nObject.defineProperty(z, \"__esModule\", { value: !0 });\nz.findAllInputs = $t;\nvar wt = y, Nt = R, It = P, At = c;\nfunction $t(e) {\n  return Array.from(e.querySelectorAll((0, It.allInputsSelector)())).reduce(function(t, n) {\n    return (0, At.isNativeInput)(n) || (0, wt.containsOnlyInlineElements)(n) ? p(p([], t, !0), [n], !1) : p(p([], t, !0), (0, Nt.getDeepestBlockElements)(n), !0);\n  }, []);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.findAllInputs = void 0;\n  var t = z;\n  Object.defineProperty(e, \"findAllInputs\", { enumerable: !0, get: function() {\n    return t.findAllInputs;\n  } });\n})(ve);\nvar ge = {}, G = {};\nObject.defineProperty(G, \"__esModule\", { value: !0 });\nG.isCollapsedWhitespaces = Bt;\nfunction Bt(e) {\n  return !/[^\\t\\n\\r ]/.test(e);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isCollapsedWhitespaces = void 0;\n  var t = G;\n  Object.defineProperty(e, \"isCollapsedWhitespaces\", { enumerable: !0, get: function() {\n    return t.isCollapsedWhitespaces;\n  } });\n})(ge);\nvar K = {}, X = {};\nObject.defineProperty(X, \"__esModule\", { value: !0 });\nX.isElement = Ht;\nvar Dt = $;\nfunction Ht(e) {\n  return (0, Dt.isNumber)(e) ? !1 : !!e && !!e.nodeType && e.nodeType === Node.ELEMENT_NODE;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isElement = void 0;\n  var t = X;\n  Object.defineProperty(e, \"isElement\", { enumerable: !0, get: function() {\n    return t.isElement;\n  } });\n})(K);\nvar me = {}, Y = {}, Q = {}, V = {};\nObject.defineProperty(V, \"__esModule\", { value: !0 });\nV.isLeaf = Rt;\nfunction Rt(e) {\n  return e === null ? !1 : e.childNodes.length === 0;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isLeaf = void 0;\n  var t = V;\n  Object.defineProperty(e, \"isLeaf\", { enumerable: !0, get: function() {\n    return t.isLeaf;\n  } });\n})(Q);\nvar Z = {}, J = {};\nObject.defineProperty(J, \"__esModule\", { value: !0 });\nJ.isNodeEmpty = zt;\nvar Ft = h, Wt = K, Ut = c, qt = E;\nfunction zt(e, t) {\n  var n = \"\";\n  return (0, qt.isSingleTag)(e) && !(0, Ft.isLineBreakTag)(e) ? !1 : ((0, Wt.isElement)(e) && (0, Ut.isNativeInput)(e) ? n = e.value : e.textContent !== null && (n = e.textContent.replace(\"​\", \"\")), t !== void 0 && (n = n.replace(new RegExp(t, \"g\"), \"\")), n.trim().length === 0);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isNodeEmpty = void 0;\n  var t = J;\n  Object.defineProperty(e, \"isNodeEmpty\", { enumerable: !0, get: function() {\n    return t.isNodeEmpty;\n  } });\n})(Z);\nObject.defineProperty(Y, \"__esModule\", { value: !0 });\nY.isEmpty = Xt;\nvar Gt = Q, Kt = Z;\nfunction Xt(e, t) {\n  e.normalize();\n  for (var n = [e]; n.length > 0; ) {\n    var r = n.shift();\n    if (r) {\n      if (e = r, (0, Gt.isLeaf)(e) && !(0, Kt.isNodeEmpty)(e, t))\n        return !1;\n      n.push.apply(n, Array.from(e.childNodes));\n    }\n  }\n  return !0;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isEmpty = void 0;\n  var t = Y;\n  Object.defineProperty(e, \"isEmpty\", { enumerable: !0, get: function() {\n    return t.isEmpty;\n  } });\n})(me);\nvar be = {}, x = {};\nObject.defineProperty(x, \"__esModule\", { value: !0 });\nx.isFragment = Qt;\nvar Yt = $;\nfunction Qt(e) {\n  return (0, Yt.isNumber)(e) ? !1 : !!e && !!e.nodeType && e.nodeType === Node.DOCUMENT_FRAGMENT_NODE;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isFragment = void 0;\n  var t = x;\n  Object.defineProperty(e, \"isFragment\", { enumerable: !0, get: function() {\n    return t.isFragment;\n  } });\n})(be);\nvar ye = {}, ee = {};\nObject.defineProperty(ee, \"__esModule\", { value: !0 });\nee.isHTMLString = Zt;\nvar Vt = _;\nfunction Zt(e) {\n  var t = (0, Vt.make)(\"div\");\n  return t.innerHTML = e, t.childElementCount > 0;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isHTMLString = void 0;\n  var t = ee;\n  Object.defineProperty(e, \"isHTMLString\", { enumerable: !0, get: function() {\n    return t.isHTMLString;\n  } });\n})(ye);\nvar _e = {}, te = {};\nObject.defineProperty(te, \"__esModule\", { value: !0 });\nte.offset = Jt;\nfunction Jt(e) {\n  var t = e.getBoundingClientRect(), n = window.pageXOffset || document.documentElement.scrollLeft, r = window.pageYOffset || document.documentElement.scrollTop, i = t.top + r, a = t.left + n;\n  return {\n    top: i,\n    left: a,\n    bottom: i + t.height,\n    right: a + t.width\n  };\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.offset = void 0;\n  var t = te;\n  Object.defineProperty(e, \"offset\", { enumerable: !0, get: function() {\n    return t.offset;\n  } });\n})(_e);\nvar he = {}, ne = {};\nObject.defineProperty(ne, \"__esModule\", { value: !0 });\nne.prepend = xt;\nfunction xt(e, t) {\n  Array.isArray(t) ? (t = t.reverse(), t.forEach(function(n) {\n    return e.prepend(n);\n  })) : e.prepend(t);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.prepend = void 0;\n  var t = ne;\n  Object.defineProperty(e, \"prepend\", { enumerable: !0, get: function() {\n    return t.prepend;\n  } });\n})(he);\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.prepend = e.offset = e.make = e.isLineBreakTag = e.isSingleTag = e.isNodeEmpty = e.isLeaf = e.isHTMLString = e.isFragment = e.isEmpty = e.isElement = e.isContentEditable = e.isCollapsedWhitespaces = e.findAllInputs = e.isNativeInput = e.allInputsSelector = e.getDeepestNode = e.getDeepestBlockElements = e.getContentLength = e.fragmentToString = e.containsOnlyInlineElements = e.canSetCaret = e.calculateBaseline = e.blockElements = e.append = void 0;\n  var t = P;\n  Object.defineProperty(e, \"allInputsSelector\", { enumerable: !0, get: function() {\n    return t.allInputsSelector;\n  } });\n  var n = c;\n  Object.defineProperty(e, \"isNativeInput\", { enumerable: !0, get: function() {\n    return n.isNativeInput;\n  } });\n  var r = ie;\n  Object.defineProperty(e, \"append\", { enumerable: !0, get: function() {\n    return r.append;\n  } });\n  var i = L;\n  Object.defineProperty(e, \"blockElements\", { enumerable: !0, get: function() {\n    return i.blockElements;\n  } });\n  var a = ae;\n  Object.defineProperty(e, \"calculateBaseline\", { enumerable: !0, get: function() {\n    return a.calculateBaseline;\n  } });\n  var l = le;\n  Object.defineProperty(e, \"canSetCaret\", { enumerable: !0, get: function() {\n    return l.canSetCaret;\n  } });\n  var u = y;\n  Object.defineProperty(e, \"containsOnlyInlineElements\", { enumerable: !0, get: function() {\n    return u.containsOnlyInlineElements;\n  } });\n  var d = se;\n  Object.defineProperty(e, \"fragmentToString\", { enumerable: !0, get: function() {\n    return d.fragmentToString;\n  } });\n  var s = ce;\n  Object.defineProperty(e, \"getContentLength\", { enumerable: !0, get: function() {\n    return s.getContentLength;\n  } });\n  var f = R;\n  Object.defineProperty(e, \"getDeepestBlockElements\", { enumerable: !0, get: function() {\n    return f.getDeepestBlockElements;\n  } });\n  var Oe = fe;\n  Object.defineProperty(e, \"getDeepestNode\", { enumerable: !0, get: function() {\n    return Oe.getDeepestNode;\n  } });\n  var Pe = ve;\n  Object.defineProperty(e, \"findAllInputs\", { enumerable: !0, get: function() {\n    return Pe.findAllInputs;\n  } });\n  var je = ge;\n  Object.defineProperty(e, \"isCollapsedWhitespaces\", { enumerable: !0, get: function() {\n    return je.isCollapsedWhitespaces;\n  } });\n  var Te = w;\n  Object.defineProperty(e, \"isContentEditable\", { enumerable: !0, get: function() {\n    return Te.isContentEditable;\n  } });\n  var Ce = K;\n  Object.defineProperty(e, \"isElement\", { enumerable: !0, get: function() {\n    return Ce.isElement;\n  } });\n  var Le = me;\n  Object.defineProperty(e, \"isEmpty\", { enumerable: !0, get: function() {\n    return Le.isEmpty;\n  } });\n  var Se = be;\n  Object.defineProperty(e, \"isFragment\", { enumerable: !0, get: function() {\n    return Se.isFragment;\n  } });\n  var Me = ye;\n  Object.defineProperty(e, \"isHTMLString\", { enumerable: !0, get: function() {\n    return Me.isHTMLString;\n  } });\n  var ke = Q;\n  Object.defineProperty(e, \"isLeaf\", { enumerable: !0, get: function() {\n    return ke.isLeaf;\n  } });\n  var we = Z;\n  Object.defineProperty(e, \"isNodeEmpty\", { enumerable: !0, get: function() {\n    return we.isNodeEmpty;\n  } });\n  var Ne = h;\n  Object.defineProperty(e, \"isLineBreakTag\", { enumerable: !0, get: function() {\n    return Ne.isLineBreakTag;\n  } });\n  var Ie = E;\n  Object.defineProperty(e, \"isSingleTag\", { enumerable: !0, get: function() {\n    return Ie.isSingleTag;\n  } });\n  var Ae = _;\n  Object.defineProperty(e, \"make\", { enumerable: !0, get: function() {\n    return Ae.make;\n  } });\n  var $e = _e;\n  Object.defineProperty(e, \"offset\", { enumerable: !0, get: function() {\n    return $e.offset;\n  } });\n  var Be = he;\n  Object.defineProperty(e, \"prepend\", { enumerable: !0, get: function() {\n    return Be.prepend;\n  } });\n})(v);\nvar Ee = /* @__PURE__ */ ((e) => (e.Left = \"left\", e.Center = \"center\", e))(Ee || {});\nclass m {\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   * @param params - Quote Tool constructor params\n   * @param params.data - previously saved data\n   * @param params.config - user config for Tool\n   * @param params.api - editor.js api\n   * @param params.readOnly - read only mode flag\n   */\n  constructor({ data: t, config: n, api: r, readOnly: i, block: a }) {\n    const { DEFAULT_ALIGNMENT: l } = m;\n    this.api = r, this.readOnly = i, this.quotePlaceholder = r.i18n.t((n == null ? void 0 : n.quotePlaceholder) ?? m.DEFAULT_QUOTE_PLACEHOLDER), this.captionPlaceholder = r.i18n.t((n == null ? void 0 : n.captionPlaceholder) ?? m.DEFAULT_CAPTION_PLACEHOLDER), this.data = {\n      text: t.text || \"\",\n      caption: t.caption || \"\",\n      alignment: Object.values(Ee).includes(t.alignment) ? t.alignment : (n == null ? void 0 : n.defaultAlignment) ?? l\n    }, this.css = {\n      baseClass: this.api.styles.block,\n      wrapper: \"cdx-quote\",\n      text: \"cdx-quote__text\",\n      input: this.api.styles.input,\n      caption: \"cdx-quote__caption\"\n    }, this.block = a;\n  }\n  /**\n   * Notify core that read-only mode is supported\n   * @returns true\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   * @returns icon and title of the toolbox\n   */\n  static get toolbox() {\n    return {\n      icon: Re,\n      title: \"Quote\"\n    };\n  }\n  /**\n   * Empty Quote is not empty Block\n   * @returns true\n   */\n  static get contentless() {\n    return !0;\n  }\n  /**\n   * Allow to press Enter inside the Quote\n   * @returns true\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Default placeholder for quote text\n   * @returns 'Enter a quote'\n   */\n  static get DEFAULT_QUOTE_PLACEHOLDER() {\n    return \"Enter a quote\";\n  }\n  /**\n   * Default placeholder for quote caption\n   * @returns 'Enter a caption'\n   */\n  static get DEFAULT_CAPTION_PLACEHOLDER() {\n    return \"Enter a caption\";\n  }\n  /**\n   * Default quote alignment\n   * @returns Alignment.Left\n   */\n  static get DEFAULT_ALIGNMENT() {\n    return \"left\";\n  }\n  /**\n   * Allow Quote to be converted to/from other blocks\n   * @returns conversion config object\n   */\n  static get conversionConfig() {\n    return {\n      /**\n       * To create Quote data from string, simple fill 'text' property\n       */\n      import: \"text\",\n      /**\n       * To create string from Quote data, concatenate text and caption\n       * @param quoteData - Quote data object\n       * @returns string\n       */\n      export: function(t) {\n        return t.caption ? `${t.text} — ${t.caption}` : t.text;\n      }\n    };\n  }\n  /**\n   * Tool`s styles\n   * @returns CSS classes names\n   */\n  get CSS() {\n    return {\n      baseClass: this.api.styles.block,\n      wrapper: \"cdx-quote\",\n      text: \"cdx-quote__text\",\n      input: this.api.styles.input,\n      caption: \"cdx-quote__caption\"\n    };\n  }\n  /**\n   * Tool`s settings properties\n   * @returns settings properties\n   */\n  get settings() {\n    return [\n      {\n        name: \"left\",\n        icon: He\n      },\n      {\n        name: \"center\",\n        icon: De\n      }\n    ];\n  }\n  /**\n   * Create Quote Tool container with inputs\n   * @returns blockquote DOM element - Quote Tool container\n   */\n  render() {\n    const t = v.make(\"blockquote\", [\n      this.css.baseClass,\n      this.css.wrapper\n    ]), n = v.make(\"div\", [this.css.input, this.css.text], {\n      contentEditable: !this.readOnly,\n      innerHTML: this.data.text\n    }), r = v.make(\"div\", [this.css.input, this.css.caption], {\n      contentEditable: !this.readOnly,\n      innerHTML: this.data.caption\n    });\n    return n.dataset.placeholder = this.quotePlaceholder, r.dataset.placeholder = this.captionPlaceholder, t.appendChild(n), t.appendChild(r), t;\n  }\n  /**\n   * Extract Quote data from Quote Tool element\n   * @param quoteElement - Quote DOM element to save\n   * @returns Quote data object\n   */\n  save(t) {\n    const n = t.querySelector(`.${this.css.text}`), r = t.querySelector(`.${this.css.caption}`);\n    return Object.assign(this.data, {\n      text: (n == null ? void 0 : n.innerHTML) ?? \"\",\n      caption: (r == null ? void 0 : r.innerHTML) ?? \"\"\n    });\n  }\n  /**\n   * Sanitizer rules\n   * @returns sanitizer rules\n   */\n  static get sanitize() {\n    return {\n      text: {\n        br: !0\n      },\n      caption: {\n        br: !0\n      },\n      alignment: {}\n    };\n  }\n  /**\n   * Create wrapper for Tool`s settings buttons:\n   * 1. Left alignment\n   * 2. Center alignment\n   * @returns settings menu\n   */\n  renderSettings() {\n    const t = (n) => n && n[0].toUpperCase() + n.slice(1);\n    return this.settings.map((n) => ({\n      icon: n.icon,\n      label: this.api.i18n.t(`Align ${t(n.name)}`),\n      onActivate: () => this._toggleTune(n.name),\n      isActive: this.data.alignment === n.name,\n      closeOnActivate: !0\n    }));\n  }\n  /**\n   * Toggle quote`s alignment\n   * @param tune - alignment\n   */\n  _toggleTune(t) {\n    this.data.alignment = t, this.block.dispatchChange();\n  }\n}\nexport {\n  m as default\n};\n"], "mappings": ";;;CAAC,WAAU;AAAC;AAAa,MAAG;AAAC,QAAG,OAAO,WAAS,KAAI;AAAC,UAAI,IAAE,SAAS,cAAc,OAAO;AAAE,QAAE,YAAY,SAAS,eAAe,4fAA4f,CAAC,GAAE,SAAS,KAAK,YAAY,CAAC;AAAA,IAAC;AAAA,EAAC,SAAO,GAAE;AAAC,YAAQ,MAAM,kCAAiC,CAAC;AAAA,EAAC;AAAC,GAAG;AAC5tB,IAAM,KAAK;AAAX,IAA8W,KAAK;AAAnX,IAAstB,KAAK;AAC3tB,IAAI,IAAI,OAAO,aAAa,MAAM,aAAa,OAAO,SAAS,MAAM,SAAS,OAAO,SAAS,MAAM,SAAS,OAAO,OAAO,MAAM,OAAO,CAAC;AACzI,SAAS,GAAG,GAAG;AACb,MAAI,EAAE;AACJ,WAAO;AACT,MAAI,IAAI,EAAE;AACV,MAAI,OAAO,KAAK,YAAY;AAC1B,QAAI,IAAI,SAAS,IAAI;AACnB,aAAO,gBAAgB,IAAI,QAAQ,UAAU,GAAG,WAAW,KAAK,WAAW,IAAI,EAAE,MAAM,MAAM,SAAS;AAAA,IACxG;AACA,MAAE,YAAY,EAAE;AAAA,EAClB;AACE,QAAI,CAAC;AACP,SAAO,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,OAAO,KAAK,CAAC,EAAE,QAAQ,SAAS,GAAG;AAC/F,QAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,WAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,MACtC,YAAY;AAAA,MACZ,KAAK,WAAW;AACd,eAAO,EAAE,CAAC;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG;AACN;AACA,IAAI,IAAI,CAAC;AAAT,IAAY,IAAI,CAAC;AAAjB,IAAoB,IAAI,CAAC;AACzB,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,oBAAoB;AACtB,SAAS,KAAK;AACZ,MAAI,IAAI,CAAC,QAAQ,YAAY,SAAS,UAAU,UAAU,OAAO,KAAK;AACtE,SAAO,0DAA0D,EAAE,IAAI,SAAS,GAAG;AACjF,WAAO,eAAe,OAAO,GAAG,IAAI;AAAA,EACtC,CAAC,EAAE,KAAK,IAAI;AACd;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,oBAAoB;AAC7E,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,qBAAqB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC9E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,CAAC;AACJ,IAAI,IAAI,CAAC;AAAT,IAAY,IAAI,CAAC;AACjB,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,gBAAgB;AAClB,SAAS,GAAG,GAAG;AACb,MAAI,IAAI;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACA,SAAO,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,IAAI;AAClD;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,gBAAgB;AACzE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,iBAAiB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC1E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,CAAC;AACJ,IAAI,KAAK,CAAC;AAAV,IAAa,IAAI,CAAC;AAClB,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,SAAS;AACX,SAAS,GAAG,GAAG,GAAG;AAChB,QAAM,QAAQ,CAAC,IAAI,EAAE,QAAQ,SAAS,GAAG;AACvC,MAAE,YAAY,CAAC;AAAA,EACjB,CAAC,IAAI,EAAE,YAAY,CAAC;AACtB;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,SAAS;AAClE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,UAAU,EAAE,YAAY,MAAI,KAAK,WAAW;AACnE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,EAAE;AACL,IAAI,IAAI,CAAC;AAAT,IAAY,IAAI,CAAC;AACjB,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,gBAAgB;AAClB,SAAS,KAAK;AACZ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,gBAAgB;AACzE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,iBAAiB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC1E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,CAAC;AACJ,IAAI,KAAK,CAAC;AAAV,IAAa,IAAI,CAAC;AAClB,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,oBAAoB;AACtB,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,OAAO,iBAAiB,CAAC,GAAG,IAAI,WAAW,EAAE,QAAQ,GAAG,IAAI,WAAW,EAAE,UAAU,KAAK,IAAI,KAAK,IAAI,WAAW,EAAE,UAAU,GAAG,IAAI,WAAW,EAAE,cAAc,GAAG,IAAI,WAAW,EAAE,SAAS,GAAG,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI;AACxP,SAAO;AACT;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,oBAAoB;AAC7E,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,qBAAqB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC9E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,EAAE;AACL,IAAI,KAAK,CAAC;AAAV,IAAa,IAAI,CAAC;AAAlB,IAAqB,IAAI,CAAC;AAA1B,IAA6B,IAAI,CAAC;AAClC,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,oBAAoB;AACtB,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,oBAAoB;AAC/B;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,oBAAoB;AAC7E,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,qBAAqB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC9E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,CAAC;AACJ,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,cAAc;AAChB,IAAI,KAAK;AAAT,IAAY,KAAK;AACjB,SAAS,GAAG,GAAG;AACb,MAAI,IAAI;AACR,OAAK,GAAG,GAAG,eAAe,CAAC;AACzB,YAAQ,EAAE,MAAM;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,YAAI;AACJ;AAAA,IACJ;AAAA;AAEA,SAAK,GAAG,GAAG,mBAAmB,CAAC;AACjC,SAAO;AACT;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,cAAc;AACvE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,eAAe,EAAE,YAAY,MAAI,KAAK,WAAW;AACxE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,EAAE;AACL,IAAI,IAAI,CAAC;AAAT,IAAY,IAAI,CAAC;AACjB,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,QAAM,IAAI,EAAE,UAAU,SAAS,UAAU,OAAO,IAAI,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC;AACnE,MAAI,EAAE,CAAC,IAAI,YAAY,GAAG;AACxB,WAAO,KAAK,CAAC,MAAM,WAAW,KAAK,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC,IAAI,KAAK,CAAC;AAAA,EACnE,GAAG,MAAM,SAAS,EAAE,KAAK;AACvB,UAAM,IAAI,EAAE;AACZ,MAAE,MAAM,SAAS,GAAG;AAClB,aAAO,EAAE,CAAC,GAAG,EAAE,MAAM,MAAM,CAAC;AAAA,IAC9B;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,KAAK;AACZ,QAAM,IAAI;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT,GAAG,IAAI,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,OAAO,UAAU,WAAW,YAAY,EAAE,QAAQ,CAAC,MAAM,EAAE;AAC7F,SAAO,MAAM,WAAW,EAAE,CAAC,IAAI,OAAK;AACtC;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,KAAK,QAAQ,MAAM,OAAO,OAAO,KAAK,YAAY,OAAO,KAAK,CAAC,EAAE,SAAS;AACnF;AACA,SAAS,GAAG,GAAG;AACb,SAAO,CAAC,EAAE,CAAC;AACb;AACA,IAAM,KAAK,MAAM,OAAO,SAAS,OAAO,OAAO,cAAc,QAAQ,EAAE,OAAO,UAAU,QAAQ,MAAM,iBAAiB,KAAK,OAAO,UAAU,QAAQ,KAAK,OAAO,UAAU,aAAa,cAAc,OAAO,UAAU,iBAAiB;AACxO,SAAS,GAAG,GAAG;AACb,QAAM,IAAI,GAAG;AACb,SAAO,IAAI,EAAE,QAAQ,WAAW,GAAG,EAAE,QAAQ,eAAe,GAAG,EAAE,QAAQ,WAAW,GAAG,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,UAAU,GAAG,EAAE,QAAQ,UAAU,GAAG,EAAE,QAAQ,WAAW,GAAG,EAAE,QAAQ,YAAY,GAAG,EAAE,QAAQ,YAAY,KAAK,EAAE,QAAQ,YAAY,GAAG,EAAE,QAAQ,QAAQ,GAAG,GAAG,EAAE,MAAM,IAAI,EAAE,QAAQ,cAAc,GAAG,EAAE,QAAQ,SAAS,GAAG,IAAI,IAAI,EAAE,QAAQ,SAAS,MAAM,EAAE,QAAQ,aAAa,KAAK,GAAG;AACtZ;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,EAAE,YAAY,IAAI,EAAE,MAAM,CAAC;AACvC;AACA,SAAS,GAAG,GAAG;AACb,QAAM,IAAI,SAAS,cAAc,KAAK;AACtC,IAAE,MAAM,WAAW,YAAY,EAAE,MAAM,OAAO,UAAU,EAAE,MAAM,SAAS,UAAU,EAAE,YAAY,GAAG,SAAS,KAAK,YAAY,CAAC;AAC/H,QAAM,IAAI,OAAO,aAAa,GAAG,IAAI,SAAS,YAAY;AAC1D,MAAI,EAAE,WAAW,CAAC,GAAG,MAAM;AACzB,UAAM,IAAI,MAAM,+BAA+B;AACjD,IAAE,gBAAgB,GAAG,EAAE,SAAS,CAAC,GAAG,SAAS,YAAY,MAAM,GAAG,SAAS,KAAK,YAAY,CAAC;AAC/F;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI;AACJ,SAAO,IAAI,MAAM;AACf,UAAM,IAAI,MAAM,IAAI,MAAM;AACxB,UAAI,QAAQ,MAAM,QAAM,EAAE,MAAM,GAAG,CAAC;AAAA,IACtC,GAAG,IAAI,MAAM,QAAM,MAAM;AACzB,WAAO,aAAa,CAAC,GAAG,IAAI,OAAO,WAAW,GAAG,CAAC,GAAG,KAAK,EAAE,MAAM,GAAG,CAAC;AAAA,EACxE;AACF;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,eAAe,EAAE,CAAC,EAAE,YAAY;AACjF;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,MAAM;AAClB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,MAAM,cAAc,EAAE,CAAC,MAAM;AACzC;AACA,SAAS,GAAG,GAAG;AACb,SAAO,GAAG,CAAC,KAAK,eAAe,KAAK,EAAE,SAAS,CAAC;AAClD;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,MAAM;AAClB;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE,CAAC,MAAM;AAClB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,QAAQ,QAAQ,CAAC,MAAM;AAChC;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,MAAM;AAClB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,MAAM;AAClB;AACA,SAAS,EAAE,MAAM,GAAG;AAClB,MAAI,CAAC,EAAE;AACL,WAAO;AACT,QAAM,IAAI,EAAE,MAAM;AAClB,MAAI,EAAE,CAAC,KAAK,EAAE,CAAC;AACb,eAAW,KAAK;AACd,QAAE,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,MAAM,UAAU,OAAO,OAAO,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,OAAO,OAAO,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;AAChH,SAAO,EAAE,GAAG,GAAG,CAAC;AAClB;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,QAAM,IAAI,IAAI,CAAC,kFAAkF,CAAC;AAClG,OAAK,QAAQ,KAAK,CAAC;AACrB;AACA,SAAS,GAAG,GAAG;AACb,MAAI;AACF,WAAO,IAAI,IAAI,CAAC,EAAE;AAAA,EACpB,QAAQ;AAAA,EACR;AACA,SAAO,EAAE,UAAU,GAAG,CAAC,MAAM,OAAO,OAAO,SAAS,WAAW,IAAI,OAAO,SAAS,SAAS;AAC9F;AACA,SAAS,GAAG,GAAG;AACb,SAAO,IAAI,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI;AAChJ;AACA,IAAM,KAAK;AAAA,EACT,WAAW;AAAA,EACX,KAAK;AAAA,EACL,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AACT;AAhBA,IAgBG,KAAK;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AACX;AACA,IAAM,KAAN,MAAS;AAAA,EACP,cAAc;AACZ,SAAK,YAAY,QAAQ,QAAQ;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,GAAG;AACL,WAAO,IAAI,QAAQ,CAAC,GAAG,MAAM;AAC3B,WAAK,YAAY,KAAK,UAAU,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC;AAAA,IACzD,CAAC;AAAA,EACH;AACF;AACA,SAAS,GAAG,GAAG,GAAG,IAAI,QAAQ;AAC5B,MAAI,GAAG,GAAG,GAAG,IAAI,MAAM,IAAI;AAC3B,QAAM,IAAI,CAAC;AACX,QAAM,IAAI,WAAW;AACnB,QAAI,EAAE,YAAY,QAAK,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,IAAI,EAAE,MAAM,GAAG,CAAC,GAAG,MAAM,SAAS,IAAI,IAAI;AAAA,EAC7F;AACA,SAAO,WAAW;AAChB,UAAM,IAAI,KAAK,IAAI;AACnB,KAAC,KAAK,EAAE,YAAY,UAAO,IAAI;AAC/B,UAAM,IAAI,KAAK,IAAI;AACnB,WAAO,IAAI,MAAM,IAAI,WAAW,KAAK,KAAK,IAAI,KAAK,MAAM,aAAa,CAAC,GAAG,IAAI,OAAO,IAAI,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC,GAAG,MAAM,SAAS,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE,aAAa,UAAO,IAAI,WAAW,GAAG,CAAC,IAAI;AAAA,EACpM;AACF;AACA,IAAM,KAAqB,OAAO,OAAuB,OAAO,eAAe;AAAA,EAC7E,WAAW;AAAA,EACX,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,qBAAqB;AAAA,EACrB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,EACV,cAAc;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AACV,GAAG,OAAO,aAAa,EAAE,OAAO,SAAS,CAAC,CAAC;AA5B3C,IA4B8C,IAAoB,GAAG,EAAE;AACvE,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,6BAA6B;AAC/B,IAAI,KAAK;AAAT,IAAY,KAAK;AACjB,SAAS,GAAG,GAAG;AACb,MAAI;AACJ,GAAC,GAAG,GAAG,UAAU,CAAC,KAAK,IAAI,SAAS,cAAc,KAAK,GAAG,EAAE,YAAY,KAAK,IAAI;AACjF,MAAI,IAAI,SAAS,GAAG;AAClB,WAAO,EAAE,GAAG,GAAG,eAAe,EAAE,SAAS,EAAE,QAAQ,YAAY,CAAC,KAAK,MAAM,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;AAAA,EACrG;AACA,SAAO,MAAM,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;AACvC;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,6BAA6B;AACtF,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,8BAA8B,EAAE,YAAY,MAAI,KAAK,WAAW;AACvF,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,CAAC;AACJ,IAAI,KAAK,CAAC;AAAV,IAAa,IAAI,CAAC;AAAlB,IAAqB,IAAI,CAAC;AAA1B,IAA6B,IAAI,CAAC;AAClC,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,OAAO;AACT,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI;AACJ,QAAM,WAAW,IAAI,OAAO,MAAM,WAAW,IAAI,CAAC;AAClD,MAAI,IAAI,SAAS,cAAc,CAAC;AAChC,MAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,QAAI,IAAI,EAAE,OAAO,SAAS,GAAG;AAC3B,aAAO,MAAM;AAAA,IACf,CAAC;AACD,KAAC,IAAI,EAAE,WAAW,IAAI,MAAM,GAAG,CAAC;AAAA,EAClC;AACE,UAAM,QAAQ,EAAE,UAAU,IAAI,CAAC;AACjC,WAAS,KAAK;AACZ,WAAO,UAAU,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAC3D,SAAO;AACT;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,OAAO;AAChE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,QAAQ,EAAE,YAAY,MAAI,KAAK,WAAW;AACjE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,CAAC;AACJ,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,mBAAmB;AACrB,IAAI,KAAK;AACT,SAAS,GAAG,GAAG;AACb,MAAI,KAAK,GAAG,GAAG,MAAM,KAAK;AAC1B,SAAO,EAAE,YAAY,CAAC,GAAG,EAAE;AAC7B;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,mBAAmB;AAC5E,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,oBAAoB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC7E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,EAAE;AACL,IAAI,KAAK,CAAC;AAAV,IAAa,IAAI,CAAC;AAClB,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,mBAAmB;AACrB,IAAI,KAAK;AACT,SAAS,GAAG,GAAG;AACb,MAAI,GAAG;AACP,UAAQ,GAAG,GAAG,eAAe,CAAC,IAAI,EAAE,MAAM,SAAS,EAAE,aAAa,KAAK,YAAY,EAAE,UAAU,KAAK,IAAI,EAAE,iBAAiB,QAAQ,MAAM,SAAS,SAAS,EAAE,YAAY,QAAQ,MAAM,SAAS,IAAI;AACtM;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,mBAAmB;AAC5E,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,oBAAoB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC7E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,EAAE;AACL,IAAI,IAAI,CAAC;AAAT,IAAY,IAAI,CAAC;AAAjB,IAAoB,KAAK,KAAK,EAAE,iBAAiB,SAAS,GAAG,GAAG,GAAG;AACjE,MAAI,KAAK,UAAU,WAAW;AAC5B,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,GAAG,IAAI,GAAG;AACtC,OAAC,KAAK,EAAE,KAAK,QAAQ,MAAM,IAAI,MAAM,UAAU,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnF,SAAO,EAAE,OAAO,KAAK,MAAM,UAAU,MAAM,KAAK,CAAC,CAAC;AACpD;AACA,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,0BAA0B;AAC5B,IAAI,KAAK;AACT,SAAS,GAAG,GAAG;AACb,UAAQ,GAAG,GAAG,4BAA4B,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,KAAK,EAAE,QAAQ,EAAE,OAAO,SAAS,GAAG,GAAG;AAChG,WAAO,GAAG,GAAG,CAAC,GAAG,GAAG,IAAE,GAAG,GAAG,CAAC,GAAG,IAAE;AAAA,EACpC,GAAG,CAAC,CAAC;AACP;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,0BAA0B;AACnF,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,2BAA2B,EAAE,YAAY,MAAI,KAAK,WAAW;AACpF,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,CAAC;AACJ,IAAI,KAAK,CAAC;AAAV,IAAa,IAAI,CAAC;AAAlB,IAAqB,IAAI,CAAC;AAA1B,IAA6B,IAAI,CAAC;AAClC,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,iBAAiB;AACnB,SAAS,GAAG,GAAG;AACb,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF,EAAE,SAAS,EAAE,OAAO;AACtB;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,iBAAiB;AAC1E,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,kBAAkB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC3E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,CAAC;AACJ,IAAI,IAAI,CAAC;AAAT,IAAY,IAAI,CAAC;AACjB,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,cAAc;AAChB,SAAS,GAAG,GAAG;AACb,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,EAAE,SAAS,EAAE,OAAO;AACtB;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,cAAc;AACvE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,eAAe,EAAE,YAAY,MAAI,KAAK,WAAW;AACxE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,CAAC;AACJ,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,iBAAiB;AACnB,IAAI,KAAK;AAAT,IAAY,KAAK;AAAjB,IAAoB,KAAK;AACzB,SAAS,GAAG,GAAG,GAAG;AAChB,QAAM,WAAW,IAAI;AACrB,MAAI,IAAI,IAAI,cAAc,cAAc,IAAI,IAAI,oBAAoB;AACpE,MAAI,EAAE,aAAa,KAAK,gBAAgB,EAAE,CAAC,GAAG;AAC5C,QAAI,IAAI,EAAE,CAAC;AACX,SAAK,GAAG,GAAG,aAAa,CAAC,KAAK,EAAE,GAAG,GAAG,eAAe,CAAC,KAAK,EAAE,GAAG,GAAG,gBAAgB,CAAC;AAClF,UAAI,EAAE,CAAC;AACL,YAAI,EAAE,CAAC;AAAA,eACA,EAAE,eAAe,QAAQ,EAAE,WAAW,CAAC;AAC9C,YAAI,EAAE,WAAW,CAAC;AAAA;AAElB,eAAO,EAAE;AACb,WAAO,GAAG,GAAG,CAAC;AAAA,EAChB;AACA,SAAO;AACT;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,iBAAiB;AAC1E,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,kBAAkB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC3E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,EAAE;AACL,IAAI,KAAK,CAAC;AAAV,IAAa,IAAI,CAAC;AAAlB,IAAqB,IAAI,KAAK,EAAE,iBAAiB,SAAS,GAAG,GAAG,GAAG;AACjE,MAAI,KAAK,UAAU,WAAW;AAC5B,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,GAAG,IAAI,GAAG;AACtC,OAAC,KAAK,EAAE,KAAK,QAAQ,MAAM,IAAI,MAAM,UAAU,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnF,SAAO,EAAE,OAAO,KAAK,MAAM,UAAU,MAAM,KAAK,CAAC,CAAC;AACpD;AACA,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,gBAAgB;AAClB,IAAI,KAAK;AAAT,IAAY,KAAK;AAAjB,IAAoB,KAAK;AAAzB,IAA4B,KAAK;AACjC,SAAS,GAAG,GAAG;AACb,SAAO,MAAM,KAAK,EAAE,kBAAkB,GAAG,GAAG,mBAAmB,CAAC,CAAC,EAAE,OAAO,SAAS,GAAG,GAAG;AACvF,YAAQ,GAAG,GAAG,eAAe,CAAC,MAAM,GAAG,GAAG,4BAA4B,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAE,GAAG,CAAC,CAAC,GAAG,KAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAE,IAAI,GAAG,GAAG,yBAAyB,CAAC,GAAG,IAAE;AAAA,EAC9J,GAAG,CAAC,CAAC;AACP;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,gBAAgB;AACzE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,iBAAiB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC1E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,EAAE;AACL,IAAI,KAAK,CAAC;AAAV,IAAa,IAAI,CAAC;AAClB,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,yBAAyB;AAC3B,SAAS,GAAG,GAAG;AACb,SAAO,CAAC,aAAa,KAAK,CAAC;AAC7B;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,yBAAyB;AAClF,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,0BAA0B,EAAE,YAAY,MAAI,KAAK,WAAW;AACnF,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,EAAE;AACL,IAAI,IAAI,CAAC;AAAT,IAAY,IAAI,CAAC;AACjB,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,YAAY;AACd,IAAI,KAAK;AACT,SAAS,GAAG,GAAG;AACb,UAAQ,GAAG,GAAG,UAAU,CAAC,IAAI,QAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,EAAE,aAAa,KAAK;AAC/E;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,YAAY;AACrE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,aAAa,EAAE,YAAY,MAAI,KAAK,WAAW;AACtE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,CAAC;AACJ,IAAI,KAAK,CAAC;AAAV,IAAa,IAAI,CAAC;AAAlB,IAAqB,IAAI,CAAC;AAA1B,IAA6B,IAAI,CAAC;AAClC,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,SAAS;AACX,SAAS,GAAG,GAAG;AACb,SAAO,MAAM,OAAO,QAAK,EAAE,WAAW,WAAW;AACnD;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,SAAS;AAClE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,UAAU,EAAE,YAAY,MAAI,KAAK,WAAW;AACnE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,CAAC;AACJ,IAAI,IAAI,CAAC;AAAT,IAAY,IAAI,CAAC;AACjB,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,cAAc;AAChB,IAAI,KAAK;AAAT,IAAY,KAAK;AAAjB,IAAoB,KAAK;AAAzB,IAA4B,KAAK;AACjC,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI;AACR,UAAQ,GAAG,GAAG,aAAa,CAAC,KAAK,EAAE,GAAG,GAAG,gBAAgB,CAAC,IAAI,UAAO,GAAG,GAAG,WAAW,CAAC,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,IAAI,EAAE,QAAQ,EAAE,gBAAgB,SAAS,IAAI,EAAE,YAAY,QAAQ,KAAK,EAAE,IAAI,MAAM,WAAW,IAAI,EAAE,QAAQ,IAAI,OAAO,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW;AACpR;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,cAAc;AACvE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,eAAe,EAAE,YAAY,MAAI,KAAK,WAAW;AACxE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,CAAC;AACJ,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,UAAU;AACZ,IAAI,KAAK;AAAT,IAAY,KAAK;AACjB,SAAS,GAAG,GAAG,GAAG;AAChB,IAAE,UAAU;AACZ,WAAS,IAAI,CAAC,CAAC,GAAG,EAAE,SAAS,KAAK;AAChC,QAAI,IAAI,EAAE,MAAM;AAChB,QAAI,GAAG;AACL,UAAI,IAAI,IAAI,GAAG,GAAG,QAAQ,CAAC,KAAK,EAAE,GAAG,GAAG,aAAa,GAAG,CAAC;AACvD,eAAO;AACT,QAAE,KAAK,MAAM,GAAG,MAAM,KAAK,EAAE,UAAU,CAAC;AAAA,IAC1C;AAAA,EACF;AACA,SAAO;AACT;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,UAAU;AACnE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAI,KAAK,WAAW;AACpE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,EAAE;AACL,IAAI,KAAK,CAAC;AAAV,IAAa,IAAI,CAAC;AAClB,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AACpD,EAAE,aAAa;AACf,IAAI,KAAK;AACT,SAAS,GAAG,GAAG;AACb,UAAQ,GAAG,GAAG,UAAU,CAAC,IAAI,QAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,EAAE,aAAa,KAAK;AAC/E;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,aAAa;AACtE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,cAAc,EAAE,YAAY,MAAI,KAAK,WAAW;AACvE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,EAAE;AACL,IAAI,KAAK,CAAC;AAAV,IAAa,KAAK,CAAC;AACnB,OAAO,eAAe,IAAI,cAAc,EAAE,OAAO,KAAG,CAAC;AACrD,GAAG,eAAe;AAClB,IAAI,KAAK;AACT,SAAS,GAAG,GAAG;AACb,MAAI,KAAK,GAAG,GAAG,MAAM,KAAK;AAC1B,SAAO,EAAE,YAAY,GAAG,EAAE,oBAAoB;AAChD;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,eAAe;AACxE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,gBAAgB,EAAE,YAAY,MAAI,KAAK,WAAW;AACzE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,EAAE;AACL,IAAI,KAAK,CAAC;AAAV,IAAa,KAAK,CAAC;AACnB,OAAO,eAAe,IAAI,cAAc,EAAE,OAAO,KAAG,CAAC;AACrD,GAAG,SAAS;AACZ,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,EAAE,sBAAsB,GAAG,IAAI,OAAO,eAAe,SAAS,gBAAgB,YAAY,IAAI,OAAO,eAAe,SAAS,gBAAgB,WAAW,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,OAAO;AAC5L,SAAO;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,IACN,QAAQ,IAAI,EAAE;AAAA,IACd,OAAO,IAAI,EAAE;AAAA,EACf;AACF;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,SAAS;AAClE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,UAAU,EAAE,YAAY,MAAI,KAAK,WAAW;AACnE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,EAAE;AACL,IAAI,KAAK,CAAC;AAAV,IAAa,KAAK,CAAC;AACnB,OAAO,eAAe,IAAI,cAAc,EAAE,OAAO,KAAG,CAAC;AACrD,GAAG,UAAU;AACb,SAAS,GAAG,GAAG,GAAG;AAChB,QAAM,QAAQ,CAAC,KAAK,IAAI,EAAE,QAAQ,GAAG,EAAE,QAAQ,SAAS,GAAG;AACzD,WAAO,EAAE,QAAQ,CAAC;AAAA,EACpB,CAAC,KAAK,EAAE,QAAQ,CAAC;AACnB;AAAA,CACC,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,UAAU;AACnE,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAI,KAAK,WAAW;AACpE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACL,GAAG,EAAE;AAAA,CACJ,SAAS,GAAG;AACX,SAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,iBAAiB,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,EAAE,YAAY,EAAE,oBAAoB,EAAE,yBAAyB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,0BAA0B,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,6BAA6B,EAAE,cAAc,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,SAAS;AACrf,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,qBAAqB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC9E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACH,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,iBAAiB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC1E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACH,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,UAAU,EAAE,YAAY,MAAI,KAAK,WAAW;AACnE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACH,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,iBAAiB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC1E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACH,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,qBAAqB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC9E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACH,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,eAAe,EAAE,YAAY,MAAI,KAAK,WAAW;AACxE,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACH,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,8BAA8B,EAAE,YAAY,MAAI,KAAK,WAAW;AACvF,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACH,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,oBAAoB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC7E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACH,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,oBAAoB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC7E,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACH,MAAI,IAAI;AACR,SAAO,eAAe,GAAG,2BAA2B,EAAE,YAAY,MAAI,KAAK,WAAW;AACpF,WAAO,EAAE;AAAA,EACX,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,kBAAkB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC3E,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,iBAAiB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC1E,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,0BAA0B,EAAE,YAAY,MAAI,KAAK,WAAW;AACnF,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,qBAAqB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC9E,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,aAAa,EAAE,YAAY,MAAI,KAAK,WAAW;AACtE,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAI,KAAK,WAAW;AACpE,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,cAAc,EAAE,YAAY,MAAI,KAAK,WAAW;AACvE,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,gBAAgB,EAAE,YAAY,MAAI,KAAK,WAAW;AACzE,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,UAAU,EAAE,YAAY,MAAI,KAAK,WAAW;AACnE,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,eAAe,EAAE,YAAY,MAAI,KAAK,WAAW;AACxE,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,kBAAkB,EAAE,YAAY,MAAI,KAAK,WAAW;AAC3E,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,eAAe,EAAE,YAAY,MAAI,KAAK,WAAW;AACxE,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,QAAQ,EAAE,YAAY,MAAI,KAAK,WAAW;AACjE,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,UAAU,EAAE,YAAY,MAAI,KAAK,WAAW;AACnE,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACH,MAAI,KAAK;AACT,SAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAI,KAAK,WAAW;AACpE,WAAO,GAAG;AAAA,EACZ,EAAE,CAAC;AACL,GAAG,CAAC;AACJ,IAAI,MAAsB,CAAC,OAAO,EAAE,OAAO,QAAQ,EAAE,SAAS,UAAU,IAAI,MAAM,CAAC,CAAC;AACpF,IAAM,IAAN,MAAM,GAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASN,YAAY,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,GAAG,UAAU,GAAG,OAAO,EAAE,GAAG;AACjE,UAAM,EAAE,mBAAmB,EAAE,IAAI;AACjC,SAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,mBAAmB,EAAE,KAAK,GAAG,KAAK,OAAO,SAAS,EAAE,qBAAqB,GAAE,yBAAyB,GAAG,KAAK,qBAAqB,EAAE,KAAK,GAAG,KAAK,OAAO,SAAS,EAAE,uBAAuB,GAAE,2BAA2B,GAAG,KAAK,OAAO;AAAA,MACzQ,MAAM,EAAE,QAAQ;AAAA,MAChB,SAAS,EAAE,WAAW;AAAA,MACtB,WAAW,OAAO,OAAO,EAAE,EAAE,SAAS,EAAE,SAAS,IAAI,EAAE,aAAa,KAAK,OAAO,SAAS,EAAE,qBAAqB;AAAA,IAClH,GAAG,KAAK,MAAM;AAAA,MACZ,WAAW,KAAK,IAAI,OAAO;AAAA,MAC3B,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO,KAAK,IAAI,OAAO;AAAA,MACvB,SAAS;AAAA,IACX,GAAG,KAAK,QAAQ;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,sBAAsB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,UAAU;AACnB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,cAAc;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,mBAAmB;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,4BAA4B;AACrC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,8BAA8B;AACvC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,oBAAoB;AAC7B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,mBAAmB;AAC5B,WAAO;AAAA;AAAA;AAAA;AAAA,MAIL,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMR,QAAQ,SAAS,GAAG;AAClB,eAAO,EAAE,UAAU,GAAG,EAAE,IAAI,MAAM,EAAE,OAAO,KAAK,EAAE;AAAA,MACpD;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM;AACR,WAAO;AAAA,MACL,WAAW,KAAK,IAAI,OAAO;AAAA,MAC3B,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO,KAAK,IAAI,OAAO;AAAA,MACvB,SAAS;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO;AAAA,MACL;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,UAAM,IAAI,EAAE,KAAK,cAAc;AAAA,MAC7B,KAAK,IAAI;AAAA,MACT,KAAK,IAAI;AAAA,IACX,CAAC,GAAG,IAAI,EAAE,KAAK,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,IAAI,IAAI,GAAG;AAAA,MACrD,iBAAiB,CAAC,KAAK;AAAA,MACvB,WAAW,KAAK,KAAK;AAAA,IACvB,CAAC,GAAG,IAAI,EAAE,KAAK,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG;AAAA,MACxD,iBAAiB,CAAC,KAAK;AAAA,MACvB,WAAW,KAAK,KAAK;AAAA,IACvB,CAAC;AACD,WAAO,EAAE,QAAQ,cAAc,KAAK,kBAAkB,EAAE,QAAQ,cAAc,KAAK,oBAAoB,EAAE,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG;AAAA,EAC7I;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,GAAG;AACN,UAAM,IAAI,EAAE,cAAc,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,IAAI,EAAE,cAAc,IAAI,KAAK,IAAI,OAAO,EAAE;AAC1F,WAAO,OAAO,OAAO,KAAK,MAAM;AAAA,MAC9B,OAAO,KAAK,OAAO,SAAS,EAAE,cAAc;AAAA,MAC5C,UAAU,KAAK,OAAO,SAAS,EAAE,cAAc;AAAA,IACjD,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,IAAI;AAAA,MACN;AAAA,MACA,SAAS;AAAA,QACP,IAAI;AAAA,MACN;AAAA,MACA,WAAW,CAAC;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,UAAM,IAAI,CAAC,MAAM,KAAK,EAAE,CAAC,EAAE,YAAY,IAAI,EAAE,MAAM,CAAC;AACpD,WAAO,KAAK,SAAS,IAAI,CAAC,OAAO;AAAA,MAC/B,MAAM,EAAE;AAAA,MACR,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,EAAE,IAAI,CAAC,EAAE;AAAA,MAC3C,YAAY,MAAM,KAAK,YAAY,EAAE,IAAI;AAAA,MACzC,UAAU,KAAK,KAAK,cAAc,EAAE;AAAA,MACpC,iBAAiB;AAAA,IACnB,EAAE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,GAAG;AACb,SAAK,KAAK,YAAY,GAAG,KAAK,MAAM,eAAe;AAAA,EACrD;AACF;", "names": []}