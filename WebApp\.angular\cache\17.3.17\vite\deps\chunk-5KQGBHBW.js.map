{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-checkbox.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Output, forwardRef, Optional, ViewChild, Input, NgModule } from '@angular/core';\nimport * as i5 from '@angular/forms';\nimport { NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';\nimport { Subject, fromEvent } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport * as i4 from 'ng-zorro-antd/core/form';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"*\"];\nconst _c1 = [\"inputElement\"];\nconst _c2 = [\"nz-checkbox\", \"\"];\nconst _forTrack0 = ($index, $item) => $item.value;\nfunction NzCheckboxGroupComponent_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 1);\n    i0.ɵɵlistener(\"nzCheckedChange\", function NzCheckboxGroupComponent_For_1_Template_label_nzCheckedChange_0_listener($event) {\n      const option_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckedChange(option_r2, $event));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzDisabled\", option_r2.disabled || ctx_r2.nzDisabled)(\"nzChecked\", option_r2.checked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r2.label);\n  }\n}\nclass NzCheckboxWrapperComponent {\n  constructor() {\n    this.nzOnChange = new EventEmitter();\n    this.checkboxList = [];\n  }\n  addCheckbox(value) {\n    this.checkboxList.push(value);\n  }\n  removeCheckbox(value) {\n    this.checkboxList.splice(this.checkboxList.indexOf(value), 1);\n  }\n  onChange() {\n    const listOfCheckedValue = this.checkboxList.filter(item => item.nzChecked).map(item => item.nzValue);\n    this.nzOnChange.emit(listOfCheckedValue);\n  }\n  static {\n    this.ɵfac = function NzCheckboxWrapperComponent_Factory(t) {\n      return new (t || NzCheckboxWrapperComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzCheckboxWrapperComponent,\n      selectors: [[\"nz-checkbox-wrapper\"]],\n      hostAttrs: [1, \"ant-checkbox-group\"],\n      outputs: {\n        nzOnChange: \"nzOnChange\"\n      },\n      exportAs: [\"nzCheckboxWrapper\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzCheckboxWrapperComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxWrapperComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-checkbox-wrapper',\n      exportAs: 'nzCheckboxWrapper',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: ` <ng-content></ng-content> `,\n      host: {\n        class: 'ant-checkbox-group'\n      },\n      standalone: true\n    }]\n  }], null, {\n    nzOnChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NzCheckboxComponent {\n  innerCheckedChange(checked) {\n    if (!this.nzDisabled) {\n      this.nzChecked = checked;\n      this.onChange(this.nzChecked);\n      this.nzCheckedChange.emit(this.nzChecked);\n      if (this.nzCheckboxWrapperComponent) {\n        this.nzCheckboxWrapperComponent.onChange();\n      }\n    }\n  }\n  writeValue(value) {\n    this.nzChecked = value;\n    this.cdr.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n  focus() {\n    this.focusMonitor.focusVia(this.inputElement, 'keyboard');\n  }\n  blur() {\n    this.inputElement.nativeElement.blur();\n  }\n  constructor(ngZone, elementRef, nzCheckboxWrapperComponent, cdr, focusMonitor, directionality, nzFormStatusService) {\n    this.ngZone = ngZone;\n    this.elementRef = elementRef;\n    this.nzCheckboxWrapperComponent = nzCheckboxWrapperComponent;\n    this.cdr = cdr;\n    this.focusMonitor = focusMonitor;\n    this.directionality = directionality;\n    this.nzFormStatusService = nzFormStatusService;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.isNzDisableFirstChange = true;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.nzCheckedChange = new EventEmitter();\n    this.nzValue = null;\n    this.nzAutoFocus = false;\n    this.nzDisabled = false;\n    this.nzIndeterminate = false;\n    this.nzChecked = false;\n    this.nzId = null;\n  }\n  ngOnInit() {\n    this.focusMonitor.monitor(this.elementRef, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      if (!focusOrigin) {\n        Promise.resolve().then(() => this.onTouched());\n      }\n    });\n    if (this.nzCheckboxWrapperComponent) {\n      this.nzCheckboxWrapperComponent.addCheckbox(this);\n    }\n    this.directionality.change.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.elementRef.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        event.preventDefault();\n        this.focus();\n        if (this.nzDisabled) {\n          return;\n        }\n        this.ngZone.run(() => {\n          this.innerCheckedChange(!this.nzChecked);\n          this.cdr.markForCheck();\n        });\n      });\n      fromEvent(this.inputElement.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => event.stopPropagation());\n    });\n  }\n  ngAfterViewInit() {\n    if (this.nzAutoFocus) {\n      this.focus();\n    }\n  }\n  ngOnDestroy() {\n    this.focusMonitor.stopMonitoring(this.elementRef);\n    if (this.nzCheckboxWrapperComponent) {\n      this.nzCheckboxWrapperComponent.removeCheckbox(this);\n    }\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzCheckboxComponent_Factory(t) {\n      return new (t || NzCheckboxComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NzCheckboxWrapperComponent, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(i4.NzFormStatusService, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzCheckboxComponent,\n      selectors: [[\"\", \"nz-checkbox\", \"\"]],\n      viewQuery: function NzCheckboxComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c1, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputElement = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-checkbox-wrapper\"],\n      hostVars: 6,\n      hostBindings: function NzCheckboxComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-checkbox-wrapper-in-form-item\", !!ctx.nzFormStatusService)(\"ant-checkbox-wrapper-checked\", ctx.nzChecked)(\"ant-checkbox-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzValue: \"nzValue\",\n        nzAutoFocus: \"nzAutoFocus\",\n        nzDisabled: \"nzDisabled\",\n        nzIndeterminate: \"nzIndeterminate\",\n        nzChecked: \"nzChecked\",\n        nzId: \"nzId\"\n      },\n      outputs: {\n        nzCheckedChange: \"nzCheckedChange\"\n      },\n      exportAs: [\"nzCheckbox\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxComponent),\n        multi: true\n      }]), i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      ngContentSelectors: _c0,\n      decls: 6,\n      vars: 11,\n      consts: [[\"inputElement\", \"\"], [1, \"ant-checkbox\"], [\"type\", \"checkbox\", 1, \"ant-checkbox-input\", 3, \"ngModelChange\", \"checked\", \"ngModel\", \"disabled\"], [1, \"ant-checkbox-inner\"]],\n      template: function NzCheckboxComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"span\", 1)(1, \"input\", 2, 0);\n          i0.ɵɵlistener(\"ngModelChange\", function NzCheckboxComponent_Template_input_ngModelChange_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.innerCheckedChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"span\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"span\");\n          i0.ɵɵprojection(5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-checkbox-checked\", ctx.nzChecked && !ctx.nzIndeterminate)(\"ant-checkbox-disabled\", ctx.nzDisabled)(\"ant-checkbox-indeterminate\", ctx.nzIndeterminate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"checked\", ctx.nzChecked)(\"ngModel\", ctx.nzChecked)(\"disabled\", ctx.nzDisabled);\n          i0.ɵɵattribute(\"autofocus\", ctx.nzAutoFocus ? \"autofocus\" : null)(\"id\", ctx.nzId);\n        }\n      },\n      dependencies: [FormsModule, i5.CheckboxControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzCheckboxComponent.prototype, \"nzAutoFocus\", void 0);\n__decorate([InputBoolean()], NzCheckboxComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzCheckboxComponent.prototype, \"nzIndeterminate\", void 0);\n__decorate([InputBoolean()], NzCheckboxComponent.prototype, \"nzChecked\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-checkbox]',\n      exportAs: 'nzCheckbox',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span\n      class=\"ant-checkbox\"\n      [class.ant-checkbox-checked]=\"nzChecked && !nzIndeterminate\"\n      [class.ant-checkbox-disabled]=\"nzDisabled\"\n      [class.ant-checkbox-indeterminate]=\"nzIndeterminate\"\n    >\n      <input\n        #inputElement\n        type=\"checkbox\"\n        class=\"ant-checkbox-input\"\n        [attr.autofocus]=\"nzAutoFocus ? 'autofocus' : null\"\n        [attr.id]=\"nzId\"\n        [checked]=\"nzChecked\"\n        [ngModel]=\"nzChecked\"\n        [disabled]=\"nzDisabled\"\n        (ngModelChange)=\"innerCheckedChange($event)\"\n      />\n      <span class=\"ant-checkbox-inner\"></span>\n    </span>\n    <span><ng-content></ng-content></span>\n  `,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxComponent),\n        multi: true\n      }],\n      host: {\n        class: 'ant-checkbox-wrapper',\n        '[class.ant-checkbox-wrapper-in-form-item]': '!!nzFormStatusService',\n        '[class.ant-checkbox-wrapper-checked]': 'nzChecked',\n        '[class.ant-checkbox-rtl]': `dir === 'rtl'`\n      },\n      imports: [FormsModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: NzCheckboxWrapperComponent,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.FocusMonitor\n  }, {\n    type: i3.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.NzFormStatusService,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    inputElement: [{\n      type: ViewChild,\n      args: ['inputElement', {\n        static: true\n      }]\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }],\n    nzValue: [{\n      type: Input\n    }],\n    nzAutoFocus: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzIndeterminate: [{\n      type: Input\n    }],\n    nzChecked: [{\n      type: Input\n    }],\n    nzId: [{\n      type: Input\n    }]\n  });\n})();\nclass NzCheckboxGroupComponent {\n  onCheckedChange(option, checked) {\n    option.checked = checked;\n    this.onChange(this.options);\n  }\n  constructor(elementRef, focusMonitor, cdr, directionality) {\n    this.elementRef = elementRef;\n    this.focusMonitor = focusMonitor;\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.options = [];\n    this.nzDisabled = false;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.isNzDisableFirstChange = true;\n  }\n  ngOnInit() {\n    this.focusMonitor.monitor(this.elementRef, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      if (!focusOrigin) {\n        Promise.resolve().then(() => this.onTouched());\n      }\n    });\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnDestroy() {\n    this.focusMonitor.stopMonitoring(this.elementRef);\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  writeValue(value) {\n    this.options = value;\n    this.cdr.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n  static {\n    this.ɵfac = function NzCheckboxGroupComponent_Factory(t) {\n      return new (t || NzCheckboxGroupComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzCheckboxGroupComponent,\n      selectors: [[\"nz-checkbox-group\"]],\n      hostAttrs: [1, \"ant-checkbox-group\"],\n      hostVars: 2,\n      hostBindings: function NzCheckboxGroupComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-checkbox-group-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzDisabled: \"nzDisabled\"\n      },\n      exportAs: [\"nzCheckboxGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxGroupComponent),\n        multi: true\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"nz-checkbox\", \"\", 1, \"ant-checkbox-group-item\", 3, \"nzDisabled\", \"nzChecked\"], [\"nz-checkbox\", \"\", 1, \"ant-checkbox-group-item\", 3, \"nzCheckedChange\", \"nzDisabled\", \"nzChecked\"]],\n      template: function NzCheckboxGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵrepeaterCreate(0, NzCheckboxGroupComponent_For_1_Template, 3, 3, \"label\", 0, _forTrack0);\n        }\n        if (rf & 2) {\n          i0.ɵɵrepeater(ctx.options);\n        }\n      },\n      dependencies: [NzCheckboxComponent],\n      encapsulation: 2\n    });\n  }\n}\n__decorate([InputBoolean()], NzCheckboxGroupComponent.prototype, \"nzDisabled\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-checkbox-group',\n      exportAs: 'nzCheckboxGroup',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    @for (option of options; track option.value) {\n      <label\n        nz-checkbox\n        class=\"ant-checkbox-group-item\"\n        [nzDisabled]=\"option.disabled || nzDisabled\"\n        [nzChecked]=\"option.checked!\"\n        (nzCheckedChange)=\"onCheckedChange(option, $event)\"\n      >\n        <span>{{ option.label }}</span>\n      </label>\n    }\n  `,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxGroupComponent),\n        multi: true\n      }],\n      host: {\n        class: 'ant-checkbox-group',\n        '[class.ant-checkbox-group-rtl]': `dir === 'rtl'`\n      },\n      imports: [NzCheckboxComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i2.FocusMonitor\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i3.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzDisabled: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCheckboxModule {\n  static {\n    this.ɵfac = function NzCheckboxModule_Factory(t) {\n      return new (t || NzCheckboxModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzCheckboxModule,\n      imports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent],\n      exports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzCheckboxComponent, NzCheckboxGroupComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent],\n      exports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxModule, NzCheckboxWrapperComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,eAAe,EAAE;AAC9B,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,WAAW,mBAAmB,SAAS,yEAAyE,QAAQ;AACzH,YAAM,YAAe,cAAc,GAAG,EAAE;AACxC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,WAAW,MAAM,CAAC;AAAA,IACjE,CAAC;AACD,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,UAAU,YAAY,OAAO,UAAU,EAAE,aAAa,UAAU,OAAO;AACnG,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,UAAU,KAAK;AAAA,EACtC;AACF;AACA,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,cAAc;AACZ,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,eAAe,CAAC;AAAA,EACvB;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,aAAa,KAAK,KAAK;AAAA,EAC9B;AAAA,EACA,eAAe,OAAO;AACpB,SAAK,aAAa,OAAO,KAAK,aAAa,QAAQ,KAAK,GAAG,CAAC;AAAA,EAC9D;AAAA,EACA,WAAW;AACT,UAAM,qBAAqB,KAAK,aAAa,OAAO,UAAQ,KAAK,SAAS,EAAE,IAAI,UAAQ,KAAK,OAAO;AACpG,SAAK,WAAW,KAAK,kBAAkB;AAAA,EACzC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,GAAG;AACzD,aAAO,KAAK,KAAK,6BAA4B;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,MACnC,WAAW,CAAC,GAAG,oBAAoB;AAAA,MACnC,SAAS;AAAA,QACP,YAAY;AAAA,MACd;AAAA,MACA,UAAU,CAAC,mBAAmB;AAAA,MAC9B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,mBAAmB,SAAS;AAC1B,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,YAAY;AACjB,WAAK,SAAS,KAAK,SAAS;AAC5B,WAAK,gBAAgB,KAAK,KAAK,SAAS;AACxC,UAAI,KAAK,4BAA4B;AACnC,aAAK,2BAA2B,SAAS;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,YAAY;AACjB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,SAAK,yBAAyB;AAC9B,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,QAAQ;AACN,SAAK,aAAa,SAAS,KAAK,cAAc,UAAU;AAAA,EAC1D;AAAA,EACA,OAAO;AACL,SAAK,aAAa,cAAc,KAAK;AAAA,EACvC;AAAA,EACA,YAAY,QAAQ,YAAY,4BAA4B,KAAK,cAAc,gBAAgB,qBAAqB;AAClH,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,6BAA6B;AAClC,SAAK,MAAM;AACX,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,sBAAsB;AAC3B,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,yBAAyB;AAC9B,SAAK,WAAW,MAAM;AAAA,IAAC;AACvB,SAAK,YAAY,MAAM;AAAA,IAAC;AACxB,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,WAAW;AACT,SAAK,aAAa,QAAQ,KAAK,YAAY,IAAI,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACvG,UAAI,CAAC,aAAa;AAChB,gBAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,UAAU,CAAC;AAAA,MAC/C;AAAA,IACF,CAAC;AACD,QAAI,KAAK,4BAA4B;AACnC,WAAK,2BAA2B,YAAY,IAAI;AAAA,IAClD;AACA,SAAK,eAAe,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAC/E,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,OAAO,kBAAkB,MAAM;AAClC,gBAAU,KAAK,WAAW,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAClG,cAAM,eAAe;AACrB,aAAK,MAAM;AACX,YAAI,KAAK,YAAY;AACnB;AAAA,QACF;AACA,aAAK,OAAO,IAAI,MAAM;AACpB,eAAK,mBAAmB,CAAC,KAAK,SAAS;AACvC,eAAK,IAAI,aAAa;AAAA,QACxB,CAAC;AAAA,MACH,CAAC;AACD,gBAAU,KAAK,aAAa,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS,MAAM,gBAAgB,CAAC;AAAA,IAC/H,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,aAAa;AACpB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,eAAe,KAAK,UAAU;AAChD,QAAI,KAAK,4BAA4B;AACnC,WAAK,2BAA2B,eAAe,IAAI;AAAA,IACrD;AACA,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAwB,kBAAqB,MAAM,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,4BAA4B,CAAC,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,gBAAgB,CAAC,GAAM,kBAAqB,qBAAqB,CAAC,CAAC;AAAA,IACjV;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,WAAW,SAAS,0BAA0B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,QACrE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,sBAAsB;AAAA,MACrC,UAAU;AAAA,MACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,qCAAqC,CAAC,CAAC,IAAI,mBAAmB,EAAE,gCAAgC,IAAI,SAAS,EAAE,oBAAoB,IAAI,QAAQ,KAAK;AAAA,QACrK;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,MAAM;AAAA,MACR;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,oBAAmB;AAAA,QACjD,OAAO;AAAA,MACT,CAAC,CAAC,GAAM,mBAAmB;AAAA,MAC3B,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,QAAQ,YAAY,GAAG,sBAAsB,GAAG,iBAAiB,WAAW,WAAW,UAAU,GAAG,CAAC,GAAG,oBAAoB,CAAC;AAAA,MAClL,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,SAAS,GAAG,CAAC;AAChD,UAAG,WAAW,iBAAiB,SAAS,4DAA4D,QAAQ;AAC1G,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,mBAAmB,MAAM,CAAC;AAAA,UACtD,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,MAAM;AAC3B,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,wBAAwB,IAAI,aAAa,CAAC,IAAI,eAAe,EAAE,yBAAyB,IAAI,UAAU,EAAE,8BAA8B,IAAI,eAAe;AACxK,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,SAAS,EAAE,WAAW,IAAI,SAAS,EAAE,YAAY,IAAI,UAAU;AAC5F,UAAG,YAAY,aAAa,IAAI,cAAc,cAAc,IAAI,EAAE,MAAM,IAAI,IAAI;AAAA,QAClF;AAAA,MACF;AAAA,MACA,cAAc,CAAC,aAAgB,8BAAiC,iBAAoB,OAAO;AAAA,MAC3F,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,eAAe,MAAM;AACjF,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,cAAc,MAAM;AAChF,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,mBAAmB,MAAM;AACrF,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,aAAa,MAAM;AAAA,CAC9E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAsBV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,mBAAmB;AAAA,QACjD,OAAO;AAAA,MACT,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,6CAA6C;AAAA,QAC7C,wCAAwC;AAAA,QACxC,4BAA4B;AAAA,MAC9B;AAAA,MACA,SAAS,CAAC,WAAW;AAAA,MACrB,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,gBAAgB,QAAQ,SAAS;AAC/B,WAAO,UAAU;AACjB,SAAK,SAAS,KAAK,OAAO;AAAA,EAC5B;AAAA,EACA,YAAY,YAAY,cAAc,KAAK,gBAAgB;AACzD,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,WAAW,MAAM;AAAA,IAAC;AACvB,SAAK,YAAY,MAAM;AAAA,IAAC;AACxB,SAAK,UAAU,CAAC;AAChB,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,WAAW;AACT,SAAK,aAAa,QAAQ,KAAK,YAAY,IAAI,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACvG,UAAI,CAAC,aAAa;AAChB,gBAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,UAAU,CAAC;AAAA,MAC/C;AAAA,IACF,CAAC;AACD,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,eAAe,KAAK,UAAU;AAChD,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,UAAU;AACf,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,SAAK,yBAAyB;AAC9B,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,GAAG;AACvD,aAAO,KAAK,KAAK,2BAA6B,kBAAqB,UAAU,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IAC/M;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,WAAW,CAAC,GAAG,oBAAoB;AAAA,MACnC,UAAU;AAAA,MACV,cAAc,SAAS,sCAAsC,IAAI,KAAK;AACpE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,0BAA0B,IAAI,QAAQ,KAAK;AAAA,QAC5D;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY;AAAA,MACd;AAAA,MACA,UAAU,CAAC,iBAAiB;AAAA,MAC5B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,yBAAwB;AAAA,QACtD,OAAO;AAAA,MACT,CAAC,CAAC,GAAM,mBAAmB;AAAA,MAC3B,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,eAAe,IAAI,GAAG,2BAA2B,GAAG,cAAc,WAAW,GAAG,CAAC,eAAe,IAAI,GAAG,2BAA2B,GAAG,mBAAmB,cAAc,WAAW,CAAC;AAAA,MAC5L,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,iBAAiB,GAAG,yCAAyC,GAAG,GAAG,SAAS,GAAG,UAAU;AAAA,QAC9F;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,IAAI,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,cAAc,CAAC,mBAAmB;AAAA,MAClC,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,yBAAyB,WAAW,cAAc,MAAM;AAAA,CACpF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,wBAAwB;AAAA,QACtD,OAAO;AAAA,MACT,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,kCAAkC;AAAA,MACpC;AAAA,MACA,SAAS,CAAC,mBAAmB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,qBAAqB,0BAA0B,0BAA0B;AAAA,MACnF,SAAS,CAAC,qBAAqB,0BAA0B,0BAA0B;AAAA,IACrF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,qBAAqB,wBAAwB;AAAA,IACzD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,qBAAqB,0BAA0B,0BAA0B;AAAA,MACnF,SAAS,CAAC,qBAAqB,0BAA0B,0BAA0B;AAAA,IACrF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}