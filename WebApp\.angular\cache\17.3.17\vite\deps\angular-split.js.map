{"version": 3, "sources": ["../../../../../node_modules/angular-split/fesm2022/angular-split.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Injector, Input, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Inject, ContentChild, Output, ViewChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { Observable, Subject } from 'rxjs';\nimport { debounceTime } from 'rxjs/operators';\nconst _c0 = [\"gutterEls\"];\nconst _c1 = [\"*\"];\nconst _c2 = (a0, a1, a2, a3, a4, a5) => ({\n  areaBefore: a0,\n  areaAfter: a1,\n  gutterNum: a2,\n  first: a3,\n  last: a4,\n  isDragged: a5\n});\nfunction SplitComponent_ng_template_1_div_0_ng_container_2_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction SplitComponent_ng_template_1_div_0_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SplitComponent_ng_template_1_div_0_ng_container_2_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const injector_r4 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    const area_r6 = ctx_r4.$implicit;\n    const index_r2 = ctx_r4.index;\n    const first_r7 = ctx_r4.first;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.customGutter.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction6(3, _c2, area_r6, ctx_r2.displayedAreas[index_r2 + 1], index_r2 + 1, first_r7, index_r2 === ctx_r2.displayedAreas.length - 2, ctx_r2.draggedGutterNum === index_r2 + 1))(\"ngTemplateOutletInjector\", injector_r4);\n  }\n}\nfunction SplitComponent_ng_template_1_div_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SplitComponent_ng_template_1_div_0_ng_container_2_ng_container_1_Template, 2, 10, \"ng-container\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const index_r2 = i0.ɵɵnextContext(2).index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"asSplitGutterDynamicInjector\", index_r2 + 1);\n  }\n}\nfunction SplitComponent_ng_template_1_div_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 8);\n  }\n}\nfunction SplitComponent_ng_template_1_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4, 0);\n    i0.ɵɵlistener(\"keydown\", function SplitComponent_ng_template_1_div_0_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const index_r2 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.startKeyboardDrag($event, index_r2 * 2 + 1, index_r2 + 1));\n    })(\"mousedown\", function SplitComponent_ng_template_1_div_0_Template_div_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const index_r2 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.startMouseDrag($event, index_r2 * 2 + 1, index_r2 + 1));\n    })(\"touchstart\", function SplitComponent_ng_template_1_div_0_Template_div_touchstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const index_r2 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.startMouseDrag($event, index_r2 * 2 + 1, index_r2 + 1));\n    })(\"mouseup\", function SplitComponent_ng_template_1_div_0_Template_div_mouseup_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const index_r2 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clickGutter($event, index_r2 + 1));\n    })(\"touchend\", function SplitComponent_ng_template_1_div_0_Template_div_touchend_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const index_r2 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clickGutter($event, index_r2 + 1));\n    });\n    i0.ɵɵtemplate(2, SplitComponent_ng_template_1_div_0_ng_container_2_Template, 2, 1, \"ng-container\", 5)(3, SplitComponent_ng_template_1_div_0_ng_template_3_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const defaultGutterTpl_r8 = i0.ɵɵreference(4);\n    const ctx_r4 = i0.ɵɵnextContext();\n    const area_r6 = ctx_r4.$implicit;\n    const index_r2 = ctx_r4.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"flex-basis\", ctx_r2.gutterSize, \"px\")(\"order\", index_r2 * 2 + 1);\n    i0.ɵɵclassProp(\"as-dragged\", ctx_r2.draggedGutterNum === index_r2 + 1);\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.gutterAriaLabel)(\"aria-orientation\", ctx_r2.direction)(\"aria-valuemin\", area_r6.minSize)(\"aria-valuemax\", area_r6.maxSize)(\"aria-valuenow\", area_r6.size === \"*\" ? null : area_r6.size)(\"aria-valuetext\", ctx_r2.getAriaAreaSizeText(area_r6.size));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.customGutter == null ? null : ctx_r2.customGutter.template)(\"ngIfElse\", defaultGutterTpl_r8);\n  }\n}\nfunction SplitComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SplitComponent_ng_template_1_div_0_Template, 5, 14, \"div\", 3);\n  }\n  if (rf & 2) {\n    const last_r9 = ctx.last;\n    i0.ɵɵproperty(\"ngIf\", last_r9 === false);\n  }\n}\nfunction getPointFromEvent(event) {\n  // TouchEvent\n  if (event.changedTouches !== undefined && event.changedTouches.length > 0) {\n    return {\n      x: event.changedTouches[0].clientX,\n      y: event.changedTouches[0].clientY\n    };\n  }\n  // MouseEvent\n  else if (event.clientX !== undefined && event.clientY !== undefined) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n  // KeyboardEvent\n  else if (event.currentTarget !== undefined) {\n    const gutterEl = event.currentTarget;\n    return {\n      x: gutterEl.offsetLeft,\n      y: gutterEl.offsetTop\n    };\n  }\n  return null;\n}\nfunction pointDeltaEquals(lhs, rhs, deltaPx) {\n  return Math.abs(lhs.x - rhs.x) <= deltaPx && Math.abs(lhs.y - rhs.y) <= deltaPx;\n}\nfunction getKeyboardEndpoint(event, direction) {\n  // Return null if direction keys on the opposite axis were pressed\n  if (direction === 'horizontal') {\n    switch (event.key) {\n      case 'ArrowLeft':\n      case 'ArrowRight':\n      case 'PageUp':\n      case 'PageDown':\n        break;\n      default:\n        return null;\n    }\n  }\n  if (direction === 'vertical') {\n    switch (event.key) {\n      case 'ArrowUp':\n      case 'ArrowDown':\n      case 'PageUp':\n      case 'PageDown':\n        break;\n      default:\n        return null;\n    }\n  }\n  const gutterEl = event.currentTarget;\n  const offset = event.key === 'PageUp' || event.key === 'PageDown' ? 50 * 10 : 50;\n  let offsetX = gutterEl.offsetLeft,\n    offsetY = gutterEl.offsetTop;\n  switch (event.key) {\n    case 'ArrowLeft':\n      offsetX -= offset;\n      break;\n    case 'ArrowRight':\n      offsetX += offset;\n      break;\n    case 'ArrowUp':\n      offsetY -= offset;\n      break;\n    case 'ArrowDown':\n      offsetY += offset;\n      break;\n    case 'PageUp':\n      if (direction === 'vertical') {\n        offsetY -= offset;\n      } else {\n        offsetX += offset;\n      }\n      break;\n    case 'PageDown':\n      if (direction === 'vertical') {\n        offsetY += offset;\n      } else {\n        offsetX -= offset;\n      }\n      break;\n    default:\n      return null;\n  }\n  return {\n    x: offsetX,\n    y: offsetY\n  };\n}\nfunction getElementPixelSize(elRef, direction) {\n  const rect = elRef.nativeElement.getBoundingClientRect();\n  return direction === 'horizontal' ? rect.width : rect.height;\n}\nfunction getInputBoolean(v) {\n  return typeof v === 'boolean' ? v : v !== 'false';\n}\nfunction getInputPositiveNumber(v, defaultValue) {\n  if (v === null || v === undefined) return defaultValue;\n  v = Number(v);\n  return !isNaN(v) && v >= 0 ? v : defaultValue;\n}\nfunction isUserSizesValid(unit, sizes) {\n  // All sizes total must be 100 unless there are wildcards.\n  // While having wildcards all other sizes sum should be less than 100.\n  // There should be maximum one wildcard.\n  if (unit === 'percent') {\n    const total = sizes.reduce((total, s) => s !== '*' ? total + s : total, 0);\n    const wildcardSizeAreas = sizes.filter(size => size === '*');\n    if (wildcardSizeAreas.length > 1) {\n      return false;\n    }\n    if (wildcardSizeAreas.length === 1) {\n      return total < 100.1;\n    }\n    return total > 99.9 && total < 100.1;\n  }\n  // A size at null is mandatory but only one.\n  if (unit === 'pixel') {\n    return sizes.filter(s => s === '*').length === 1;\n  }\n}\nfunction getAreaMinSize(a) {\n  if (a.size === '*') {\n    return null;\n  }\n  if (a.component.lockSize === true) {\n    return a.size;\n  }\n  if (a.component.minSize === null) {\n    return null;\n  }\n  return a.component.minSize;\n}\nfunction getAreaMaxSize(a) {\n  if (a.size === '*') {\n    return null;\n  }\n  if (a.component.lockSize === true) {\n    return a.size;\n  }\n  if (a.component.maxSize === null) {\n    return null;\n  }\n  if (a.component.maxSize < a.size) {\n    return a.size;\n  }\n  return a.component.maxSize;\n}\nfunction getGutterSideAbsorptionCapacity(unit, sideAreas, pixels, allAreasSizePixel) {\n  return sideAreas.reduce((acc, area) => {\n    const res = getAreaAbsorptionCapacity(unit, area, acc.remain, allAreasSizePixel);\n    acc.list.push(res);\n    acc.remain = res.pixelRemain;\n    return acc;\n  }, {\n    remain: pixels,\n    list: []\n  });\n}\nfunction getAreaAbsorptionCapacity(unit, areaSnapshot, pixels, allAreasSizePixel) {\n  // No pain no gain\n  if (pixels === 0) {\n    return {\n      areaSnapshot,\n      pixelAbsorb: 0,\n      percentAfterAbsorption: areaSnapshot.sizePercentAtStart,\n      pixelRemain: 0\n    };\n  }\n  // Area start at zero and need to be reduced, not possible\n  if (areaSnapshot.sizePixelAtStart === 0 && pixels < 0) {\n    return {\n      areaSnapshot,\n      pixelAbsorb: 0,\n      percentAfterAbsorption: 0,\n      pixelRemain: pixels\n    };\n  }\n  if (unit === 'percent') {\n    return getAreaAbsorptionCapacityPercent(areaSnapshot, pixels, allAreasSizePixel);\n  }\n  if (unit === 'pixel') {\n    return getAreaAbsorptionCapacityPixel(areaSnapshot, pixels);\n  }\n}\nfunction getAreaAbsorptionCapacityPercent(areaSnapshot, pixels, allAreasSizePixel) {\n  const tempPixelSize = areaSnapshot.sizePixelAtStart + pixels;\n  const tempPercentSize = tempPixelSize / allAreasSizePixel * 100;\n  // ENLARGE AREA\n  if (pixels > 0) {\n    // If maxSize & newSize bigger than it > absorb to max and return remaining pixels\n    if (areaSnapshot.area.maxSize !== null && tempPercentSize > areaSnapshot.area.maxSize) {\n      // Use area.area.maxSize as newPercentSize and return calculate pixels remaining\n      const maxSizePixel = areaSnapshot.area.maxSize / 100 * allAreasSizePixel;\n      return {\n        areaSnapshot,\n        pixelAbsorb: maxSizePixel,\n        percentAfterAbsorption: areaSnapshot.area.maxSize,\n        pixelRemain: areaSnapshot.sizePixelAtStart + pixels - maxSizePixel\n      };\n    }\n    return {\n      areaSnapshot,\n      pixelAbsorb: pixels,\n      percentAfterAbsorption: tempPercentSize > 100 ? 100 : tempPercentSize,\n      pixelRemain: 0\n    };\n  }\n  // REDUCE AREA\n  else if (pixels < 0) {\n    // If minSize & newSize smaller than it > absorb to min and return remaining pixels\n    if (areaSnapshot.area.minSize !== null && tempPercentSize < areaSnapshot.area.minSize) {\n      // Use area.area.minSize as newPercentSize and return calculate pixels remaining\n      const minSizePixel = areaSnapshot.area.minSize / 100 * allAreasSizePixel;\n      return {\n        areaSnapshot,\n        pixelAbsorb: minSizePixel,\n        percentAfterAbsorption: areaSnapshot.area.minSize,\n        pixelRemain: areaSnapshot.sizePixelAtStart + pixels - minSizePixel\n      };\n    }\n    // If reduced under zero > return remaining pixels\n    else if (tempPercentSize < 0) {\n      // Use 0 as newPercentSize and return calculate pixels remaining\n      return {\n        areaSnapshot,\n        pixelAbsorb: -areaSnapshot.sizePixelAtStart,\n        percentAfterAbsorption: 0,\n        pixelRemain: pixels + areaSnapshot.sizePixelAtStart\n      };\n    }\n    return {\n      areaSnapshot,\n      pixelAbsorb: pixels,\n      percentAfterAbsorption: tempPercentSize,\n      pixelRemain: 0\n    };\n  }\n}\nfunction getAreaAbsorptionCapacityPixel(areaSnapshot, pixels) {\n  const tempPixelSize = areaSnapshot.sizePixelAtStart + pixels;\n  // ENLARGE AREA\n  if (pixels > 0) {\n    // If maxSize & newSize bigger than it > absorb to max and return remaining pixels\n    if (areaSnapshot.area.maxSize !== null && tempPixelSize > areaSnapshot.area.maxSize) {\n      return {\n        areaSnapshot,\n        pixelAbsorb: areaSnapshot.area.maxSize - areaSnapshot.sizePixelAtStart,\n        percentAfterAbsorption: -1,\n        pixelRemain: tempPixelSize - areaSnapshot.area.maxSize\n      };\n    }\n    return {\n      areaSnapshot,\n      pixelAbsorb: pixels,\n      percentAfterAbsorption: -1,\n      pixelRemain: 0\n    };\n  }\n  // REDUCE AREA\n  else if (pixels < 0) {\n    // If minSize & newSize smaller than it > absorb to min and return remaining pixels\n    if (areaSnapshot.area.minSize !== null && tempPixelSize < areaSnapshot.area.minSize) {\n      return {\n        areaSnapshot,\n        pixelAbsorb: areaSnapshot.area.minSize + pixels - tempPixelSize,\n        percentAfterAbsorption: -1,\n        pixelRemain: tempPixelSize - areaSnapshot.area.minSize\n      };\n    }\n    // If reduced under zero > return remaining pixels\n    else if (tempPixelSize < 0) {\n      return {\n        areaSnapshot,\n        pixelAbsorb: -areaSnapshot.sizePixelAtStart,\n        percentAfterAbsorption: -1,\n        pixelRemain: pixels + areaSnapshot.sizePixelAtStart\n      };\n    }\n    return {\n      areaSnapshot,\n      pixelAbsorb: pixels,\n      percentAfterAbsorption: -1,\n      pixelRemain: 0\n    };\n  }\n}\nfunction updateAreaSize(unit, item) {\n  // Update size except for the wildcard size area\n  if (item.areaSnapshot.area.size !== '*') {\n    if (unit === 'percent') {\n      item.areaSnapshot.area.size = item.percentAfterAbsorption;\n    } else if (unit === 'pixel') {\n      item.areaSnapshot.area.size = item.areaSnapshot.sizePixelAtStart + item.pixelAbsorb;\n    }\n  }\n}\nconst ANGULAR_SPLIT_DEFAULT_OPTIONS = new InjectionToken('angular-split-global-config');\nclass SplitGutterDirective {\n  constructor(template) {\n    this.template = template;\n    /**\n     * The map holds reference to the drag handle elements inside instances\n     * of the provided template.\n     */\n    this.gutterToHandleElementMap = new Map();\n    /**\n     * The map holds reference to the excluded drag elements inside instances\n     * of the provided template.\n     */\n    this.gutterToExcludeDragElementMap = new Map();\n  }\n  canStartDragging(originElement, gutterNum) {\n    if (this.gutterToExcludeDragElementMap.has(gutterNum)) {\n      const isInsideExclude = this.gutterToExcludeDragElementMap.get(gutterNum).some(gutterExcludeElement => gutterExcludeElement.nativeElement.contains(originElement));\n      if (isInsideExclude) {\n        return false;\n      }\n    }\n    if (this.gutterToHandleElementMap.has(gutterNum)) {\n      return this.gutterToHandleElementMap.get(gutterNum).some(gutterHandleElement => gutterHandleElement.nativeElement.contains(originElement));\n    }\n    return true;\n  }\n  addToMap(map, gutterNum, elementRef) {\n    if (map.has(gutterNum)) {\n      map.get(gutterNum).push(elementRef);\n    } else {\n      map.set(gutterNum, [elementRef]);\n    }\n  }\n  removedFromMap(map, gutterNum, elementRef) {\n    const elements = map.get(gutterNum);\n    elements.splice(elements.indexOf(elementRef), 1);\n    if (elements.length === 0) {\n      map.delete(gutterNum);\n    }\n  }\n  static ngTemplateContextGuard(dir, ctx) {\n    return true;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitGutterDirective_Factory(t) {\n      return new (t || SplitGutterDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitGutterDirective,\n      selectors: [[\"\", \"asSplitGutter\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitGutterDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitGutter]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n\n/**\n * Identifies the gutter by number through DI\n * to allow SplitGutterDragHandleDirective and SplitGutterExcludeFromDragDirective to know\n * the gutter template context without inputs\n */\nconst GUTTER_NUM_TOKEN = new InjectionToken('Gutter num');\n\n/**\n * This directive allows creating a dynamic injector inside ngFor\n * with dynamic gutter num and expose the injector for ngTemplateOutlet usage\n */\nclass SplitGutterDynamicInjectorDirective {\n  set gutterNum(value) {\n    this.vcr.clear();\n    const injector = Injector.create({\n      providers: [{\n        provide: GUTTER_NUM_TOKEN,\n        useValue: value\n      }],\n      parent: this.vcr.injector\n    });\n    this.vcr.createEmbeddedView(this.templateRef, {\n      $implicit: injector\n    });\n  }\n  constructor(vcr, templateRef) {\n    this.vcr = vcr;\n    this.templateRef = templateRef;\n  }\n  static ngTemplateContextGuard(dir, ctx) {\n    return true;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitGutterDynamicInjectorDirective_Factory(t) {\n      return new (t || SplitGutterDynamicInjectorDirective)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitGutterDynamicInjectorDirective,\n      selectors: [[\"\", \"asSplitGutterDynamicInjector\", \"\"]],\n      inputs: {\n        gutterNum: [i0.ɵɵInputFlags.None, \"asSplitGutterDynamicInjector\", \"gutterNum\"]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitGutterDynamicInjectorDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitGutterDynamicInjector]'\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.TemplateRef\n  }], {\n    gutterNum: [{\n      type: Input,\n      args: ['asSplitGutterDynamicInjector']\n    }]\n  });\n})();\n\n/**\n * angular-split\n *\n *\n *  PERCENT MODE ([unit]=\"'percent'\")\n *  ___________________________________________________________________________________________\n * |       A       [g1]       B       [g2]       C       [g3]       D       [g4]       E       |\n * |-------------------------------------------------------------------------------------------|\n * |       20                 30                 20                 15                 15      | <-- [size]=\"x\"\n * |               10px               10px               10px               10px               | <-- [gutterSize]=\"10\"\n * |calc(20% - 8px)    calc(30% - 12px)   calc(20% - 8px)    calc(15% - 6px)    calc(15% - 6px)| <-- CSS flex-basis property (with flex-grow&shrink at 0)\n * |     152px              228px              152px              114px              114px     | <-- el.getBoundingClientRect().width\n * |___________________________________________________________________________________________|\n *                                                                                 800px         <-- el.getBoundingClientRect().width\n *  flex-basis = calc( { area.size }% - { area.size/100 * nbGutter*gutterSize }px );\n *\n *\n *  PIXEL MODE ([unit]=\"'pixel'\")\n *  ___________________________________________________________________________________________\n * |       A       [g1]       B       [g2]       C       [g3]       D       [g4]       E       |\n * |-------------------------------------------------------------------------------------------|\n * |      100                250                 *                 150                100      | <-- [size]=\"y\"\n * |               10px               10px               10px               10px               | <-- [gutterSize]=\"10\"\n * |   0 0 100px          0 0 250px           1 1 auto          0 0 150px          0 0 100px   | <-- CSS flex property (flex-grow/flex-shrink/flex-basis)\n * |     100px              250px              200px              150px              100px     | <-- el.getBoundingClientRect().width\n * |___________________________________________________________________________________________|\n *                                                                                 800px         <-- el.getBoundingClientRect().width\n *\n */\nclass SplitComponent {\n  set direction(v) {\n    this._direction = v === 'vertical' ? 'vertical' : 'horizontal';\n    this.renderer.addClass(this.elRef.nativeElement, `as-${this._direction}`);\n    this.renderer.removeClass(this.elRef.nativeElement, `as-${this._direction === 'vertical' ? 'horizontal' : 'vertical'}`);\n    this.build(false, false);\n  }\n  get direction() {\n    return this._direction;\n  }\n  set unit(v) {\n    this._unit = v === 'pixel' ? 'pixel' : 'percent';\n    this.renderer.addClass(this.elRef.nativeElement, `as-${this._unit}`);\n    this.renderer.removeClass(this.elRef.nativeElement, `as-${this._unit === 'pixel' ? 'percent' : 'pixel'}`);\n    this.build(false, true);\n  }\n  get unit() {\n    return this._unit;\n  }\n  set gutterSize(v) {\n    this._gutterSize = getInputPositiveNumber(v, 11);\n    this.build(false, false);\n  }\n  get gutterSize() {\n    return this._gutterSize;\n  }\n  set gutterStep(v) {\n    this._gutterStep = getInputPositiveNumber(v, 1);\n  }\n  get gutterStep() {\n    return this._gutterStep;\n  }\n  set restrictMove(v) {\n    this._restrictMove = getInputBoolean(v);\n  }\n  get restrictMove() {\n    return this._restrictMove;\n  }\n  set useTransition(v) {\n    this._useTransition = getInputBoolean(v);\n    if (this._useTransition) {\n      this.renderer.addClass(this.elRef.nativeElement, 'as-transition');\n    } else {\n      this.renderer.removeClass(this.elRef.nativeElement, 'as-transition');\n    }\n  }\n  get useTransition() {\n    return this._useTransition;\n  }\n  set disabled(v) {\n    this._disabled = getInputBoolean(v);\n    if (this._disabled) {\n      this.renderer.addClass(this.elRef.nativeElement, 'as-disabled');\n    } else {\n      this.renderer.removeClass(this.elRef.nativeElement, 'as-disabled');\n    }\n  }\n  get disabled() {\n    return this._disabled;\n  }\n  set dir(v) {\n    this._dir = v === 'rtl' ? 'rtl' : 'ltr';\n    this.renderer.setAttribute(this.elRef.nativeElement, 'dir', this._dir);\n  }\n  get dir() {\n    return this._dir;\n  }\n  set gutterDblClickDuration(v) {\n    this._gutterDblClickDuration = getInputPositiveNumber(v, 0);\n  }\n  get gutterDblClickDuration() {\n    return this._gutterDblClickDuration;\n  }\n  get transitionEnd() {\n    return new Observable(subscriber => this.transitionEndSubscriber = subscriber).pipe(debounceTime(20));\n  }\n  constructor(ngZone, elRef, cdRef, renderer, globalConfig) {\n    this.ngZone = ngZone;\n    this.elRef = elRef;\n    this.cdRef = cdRef;\n    this.renderer = renderer;\n    this.gutterClickDeltaPx = 2;\n    this._config = {\n      direction: 'horizontal',\n      unit: 'percent',\n      gutterSize: 11,\n      gutterStep: 1,\n      restrictMove: false,\n      useTransition: false,\n      disabled: false,\n      dir: 'ltr',\n      gutterDblClickDuration: 0\n    };\n    this.dragStart = new EventEmitter(false);\n    this.dragEnd = new EventEmitter(false);\n    this.gutterClick = new EventEmitter(false);\n    this.gutterDblClick = new EventEmitter(false);\n    this.dragProgressSubject = new Subject();\n    this.dragProgress$ = this.dragProgressSubject.asObservable();\n    this.isDragging = false;\n    this.isWaitingClear = false;\n    this.isWaitingInitialMove = false;\n    this.dragListeners = [];\n    this.snapshot = null;\n    this.startPoint = null;\n    this.endPoint = null;\n    this.displayedAreas = [];\n    this.hiddenAreas = [];\n    this._clickTimeout = null;\n    this.draggedGutterNum = undefined;\n    // To force adding default class, could be override by user @Input() or not\n    this.direction = this._direction;\n    this._config = globalConfig ? Object.assign(this._config, globalConfig) : this._config;\n    Object.keys(this._config).forEach(property => {\n      this[property] = this._config[property];\n    });\n  }\n  ngAfterViewInit() {\n    this.ngZone.runOutsideAngular(() => {\n      // To avoid transition at first rendering\n      setTimeout(() => this.renderer.addClass(this.elRef.nativeElement, 'as-init'));\n    });\n  }\n  getNbGutters() {\n    return this.displayedAreas.length === 0 ? 0 : this.displayedAreas.length - 1;\n  }\n  addArea(component) {\n    const newArea = {\n      component,\n      order: 0,\n      size: 0,\n      minSize: null,\n      maxSize: null,\n      sizeBeforeCollapse: null,\n      gutterBeforeCollapse: 0\n    };\n    if (component.visible === true) {\n      this.displayedAreas.push(newArea);\n      this.build(true, true);\n    } else {\n      this.hiddenAreas.push(newArea);\n    }\n  }\n  removeArea(component) {\n    if (this.displayedAreas.some(a => a.component === component)) {\n      const area = this.displayedAreas.find(a => a.component === component);\n      this.displayedAreas.splice(this.displayedAreas.indexOf(area), 1);\n      this.build(true, true);\n    } else if (this.hiddenAreas.some(a => a.component === component)) {\n      const area = this.hiddenAreas.find(a => a.component === component);\n      this.hiddenAreas.splice(this.hiddenAreas.indexOf(area), 1);\n    }\n  }\n  updateArea(component, resetOrders, resetSizes) {\n    if (component.visible === true) {\n      this.build(resetOrders, resetSizes);\n    }\n  }\n  showArea(component) {\n    const area = this.hiddenAreas.find(a => a.component === component);\n    if (area === undefined) {\n      return;\n    }\n    const areas = this.hiddenAreas.splice(this.hiddenAreas.indexOf(area), 1);\n    this.displayedAreas.push(...areas);\n    this.build(true, true);\n  }\n  hideArea(comp) {\n    const area = this.displayedAreas.find(a => a.component === comp);\n    if (area === undefined) {\n      return;\n    }\n    const areas = this.displayedAreas.splice(this.displayedAreas.indexOf(area), 1);\n    areas.forEach(item => {\n      item.order = 0;\n      item.size = 0;\n    });\n    this.hiddenAreas.push(...areas);\n    this.build(true, true);\n  }\n  getVisibleAreaSizes() {\n    return this.displayedAreas.map(a => a.size);\n  }\n  setVisibleAreaSizes(sizes) {\n    if (sizes.length !== this.displayedAreas.length) {\n      return false;\n    }\n    const formattedSizes = sizes.map(s => getInputPositiveNumber(s, '*'));\n    const isValid = isUserSizesValid(this.unit, formattedSizes);\n    if (isValid === false) {\n      return false;\n    }\n    // @@ts-expect-error\n    this.displayedAreas.forEach((area, i) => area.component.size = formattedSizes[i]);\n    this.build(false, true);\n    return true;\n  }\n  build(resetOrders, resetSizes) {\n    this.stopDragging();\n    // ¤ AREAS ORDER\n    if (resetOrders === true) {\n      // If user provided 'order' for each area, use it to sort them.\n      if (this.displayedAreas.every(a => a.component.order !== null)) {\n        this.displayedAreas.sort((a, b) => a.component.order - b.component.order);\n      }\n      // Then set real order with multiples of 2, numbers between will be used by gutters.\n      this.displayedAreas.forEach((area, i) => {\n        area.order = i * 2;\n        area.component.setStyleOrder(area.order);\n      });\n    }\n    // ¤ AREAS SIZE\n    if (resetSizes === true) {\n      const useUserSizes = isUserSizesValid(this.unit, this.displayedAreas.map(a => a.component.size));\n      switch (this.unit) {\n        case 'percent':\n          {\n            const defaultSize = 100 / this.displayedAreas.length;\n            this.displayedAreas.forEach(area => {\n              area.size = useUserSizes ? area.component.size : defaultSize;\n              area.minSize = getAreaMinSize(area);\n              area.maxSize = getAreaMaxSize(area);\n            });\n            break;\n          }\n        case 'pixel':\n          {\n            if (useUserSizes) {\n              this.displayedAreas.forEach(area => {\n                area.size = area.component.size;\n                area.minSize = getAreaMinSize(area);\n                area.maxSize = getAreaMaxSize(area);\n              });\n            } else {\n              const wildcardSizeAreas = this.displayedAreas.filter(a => a.component.size === '*');\n              // No wildcard area > Need to select one arbitrarily > first\n              if (wildcardSizeAreas.length === 0 && this.displayedAreas.length > 0) {\n                this.displayedAreas.forEach((area, i) => {\n                  area.size = i === 0 ? '*' : area.component.size;\n                  area.minSize = i === 0 ? area.component.minSize : getAreaMinSize(area);\n                  area.maxSize = i === 0 ? null : getAreaMaxSize(area);\n                });\n              } else if (wildcardSizeAreas.length > 1) {\n                // More than one wildcard area > Need to keep only one arbitrarily > first\n                let alreadyGotOne = false;\n                this.displayedAreas.forEach(area => {\n                  if (area.component.size === '*') {\n                    if (alreadyGotOne === false) {\n                      area.size = '*';\n                      area.minSize = null;\n                      area.maxSize = null;\n                      alreadyGotOne = true;\n                    } else {\n                      area.size = 100;\n                      area.minSize = null;\n                      area.maxSize = null;\n                    }\n                  } else {\n                    area.size = area.component.size;\n                    area.minSize = getAreaMinSize(area);\n                    area.maxSize = getAreaMaxSize(area);\n                  }\n                });\n              }\n            }\n            break;\n          }\n      }\n    }\n    this.refreshStyleSizes();\n    this.cdRef.markForCheck();\n  }\n  refreshStyleSizes() {\n    ///////////////////////////////////////////\n    // PERCENT MODE\n    if (this.unit === 'percent') {\n      // Only one area > flex-basis 100%\n      if (this.displayedAreas.length === 1) {\n        this.displayedAreas[0].component.setStyleFlex(0, 0, `100%`, false, false);\n      } else {\n        // Multiple areas > use each percent basis\n        const sumGutterSize = this.getNbGutters() * this.gutterSize;\n        this.displayedAreas.forEach(area => {\n          // Area with wildcard size\n          if (area.size === '*') {\n            if (this.displayedAreas.length === 1) {\n              area.component.setStyleFlex(1, 1, `100%`, false, false);\n            } else {\n              area.component.setStyleFlex(1, 1, `auto`, false, false);\n            }\n          } else {\n            area.component.setStyleFlex(0, 0, `calc( ${area.size}% - ${area.size / 100 * sumGutterSize}px )`, area.minSize !== null && area.minSize === area.size, area.maxSize !== null && area.maxSize === area.size);\n          }\n        });\n      }\n    } else if (this.unit === 'pixel') {\n      ///////////////////////////////////////////\n      // PIXEL MODE\n      this.displayedAreas.forEach(area => {\n        // Area with wildcard size\n        if (area.size === '*') {\n          if (this.displayedAreas.length === 1) {\n            area.component.setStyleFlex(1, 1, `100%`, false, false);\n          } else {\n            area.component.setStyleFlex(1, 1, `auto`, false, false);\n          }\n        } else {\n          // Area with pixel size\n          // Only one area > flex-basis 100%\n          if (this.displayedAreas.length === 1) {\n            area.component.setStyleFlex(0, 0, `100%`, false, false);\n          } else {\n            // Multiple areas > use each pixel basis\n            area.component.setStyleFlex(0, 0, `${area.size}px`, area.minSize !== null && area.minSize === area.size, area.maxSize !== null && area.maxSize === area.size);\n          }\n        }\n      });\n    }\n  }\n  clickGutter(event, gutterNum) {\n    const tempPoint = getPointFromEvent(event);\n    // Be sure mouseup/touchend happened if touch/cursor is not moved.\n    if (this.startPoint && pointDeltaEquals(this.startPoint, tempPoint, this.gutterClickDeltaPx) && (!this.isDragging || this.isWaitingInitialMove)) {\n      // If timeout in progress and new click > clearTimeout & dblClickEvent\n      if (this._clickTimeout !== null) {\n        window.clearTimeout(this._clickTimeout);\n        this._clickTimeout = null;\n        this.notify('dblclick', gutterNum);\n        this.stopDragging();\n      } else {\n        // Else start timeout to call clickEvent at end\n        this._clickTimeout = window.setTimeout(() => {\n          this._clickTimeout = null;\n          this.notify('click', gutterNum);\n          this.stopDragging();\n        }, this.gutterDblClickDuration);\n      }\n    }\n  }\n  startKeyboardDrag(event, gutterOrder, gutterNum) {\n    if (this.disabled === true || this.isWaitingClear === true) {\n      return;\n    }\n    const endPoint = getKeyboardEndpoint(event, this.direction);\n    if (endPoint === null) {\n      return;\n    }\n    this.endPoint = endPoint;\n    this.startPoint = getPointFromEvent(event);\n    event.preventDefault();\n    event.stopPropagation();\n    this.setupForDragEvent(gutterOrder, gutterNum);\n    this.startDragging();\n    this.drag();\n    this.stopDragging();\n  }\n  startMouseDrag(event, gutterOrder, gutterNum) {\n    if (this.customGutter && !this.customGutter.canStartDragging(event.target, gutterNum)) {\n      return;\n    }\n    event.preventDefault();\n    event.stopPropagation();\n    this.startPoint = getPointFromEvent(event);\n    if (this.startPoint === null || this.disabled === true || this.isWaitingClear === true) {\n      return;\n    }\n    this.setupForDragEvent(gutterOrder, gutterNum);\n    this.dragListeners.push(this.renderer.listen('document', 'mouseup', this.stopDragging.bind(this)));\n    this.dragListeners.push(this.renderer.listen('document', 'touchend', this.stopDragging.bind(this)));\n    this.dragListeners.push(this.renderer.listen('document', 'touchcancel', this.stopDragging.bind(this)));\n    this.ngZone.runOutsideAngular(() => {\n      this.dragListeners.push(this.renderer.listen('document', 'mousemove', this.mouseDragEvent.bind(this)));\n      this.dragListeners.push(this.renderer.listen('document', 'touchmove', this.mouseDragEvent.bind(this)));\n    });\n    this.startDragging();\n  }\n  setupForDragEvent(gutterOrder, gutterNum) {\n    this.snapshot = {\n      gutterNum,\n      lastSteppedOffset: 0,\n      allAreasSizePixel: getElementPixelSize(this.elRef, this.direction) - this.getNbGutters() * this.gutterSize,\n      allInvolvedAreasSizePercent: 100,\n      areasBeforeGutter: [],\n      areasAfterGutter: []\n    };\n    this.displayedAreas.forEach(area => {\n      const areaSnapshot = {\n        area,\n        sizePixelAtStart: getElementPixelSize(area.component.elRef, this.direction),\n        sizePercentAtStart: this.unit === 'percent' ? area.size : -1 // If pixel mode, anyway, will not be used.\n      };\n      if (area.order < gutterOrder) {\n        if (this.restrictMove === true) {\n          this.snapshot.areasBeforeGutter = [areaSnapshot];\n        } else {\n          this.snapshot.areasBeforeGutter.unshift(areaSnapshot);\n        }\n      } else if (area.order > gutterOrder) {\n        if (this.restrictMove === true) {\n          if (this.snapshot.areasAfterGutter.length === 0) {\n            this.snapshot.areasAfterGutter = [areaSnapshot];\n          }\n        } else {\n          this.snapshot.areasAfterGutter.push(areaSnapshot);\n        }\n      }\n    });\n    // allInvolvedAreasSizePercent is only relevant if there is restrictMove as otherwise the sum\n    // is always 100.\n    // Pixel mode doesn't have browser % problem which is the origin of allInvolvedAreasSizePercent.\n    if (this.restrictMove && this.unit === 'percent') {\n      const areaSnapshotBefore = this.snapshot.areasBeforeGutter[0];\n      const areaSnapshotAfter = this.snapshot.areasAfterGutter[0];\n      // We have a wildcard size area beside the dragged gutter.\n      // In this case we can only calculate the size based on the move restricted areas.\n      if (areaSnapshotBefore.area.size === '*' || areaSnapshotAfter.area.size === '*') {\n        const notInvolvedAreasSizesPercent = this.displayedAreas.reduce((accum, area) => {\n          if (areaSnapshotBefore.area !== area && areaSnapshotAfter.area !== area) {\n            return accum + area.size;\n          }\n          return accum;\n        }, 0);\n        this.snapshot.allInvolvedAreasSizePercent = 100 - notInvolvedAreasSizesPercent;\n      } else {\n        // No wildcard or not beside the gutter - we can just sum the areas beside gutter percents.\n        this.snapshot.allInvolvedAreasSizePercent = [...this.snapshot.areasBeforeGutter, ...this.snapshot.areasAfterGutter].reduce((t, a) => t + a.sizePercentAtStart, 0);\n      }\n    }\n    if (this.snapshot.areasBeforeGutter.length === 0 || this.snapshot.areasAfterGutter.length === 0) {\n      return;\n    }\n  }\n  startDragging() {\n    this.displayedAreas.forEach(area => area.component.lockEvents());\n    this.isDragging = true;\n    this.isWaitingInitialMove = true;\n  }\n  mouseDragEvent(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    const tempPoint = getPointFromEvent(event);\n    if (this._clickTimeout !== null && !pointDeltaEquals(this.startPoint, tempPoint, this.gutterClickDeltaPx)) {\n      window.clearTimeout(this._clickTimeout);\n      this._clickTimeout = null;\n    }\n    if (this.isDragging === false) {\n      return;\n    }\n    this.endPoint = getPointFromEvent(event);\n    if (this.endPoint === null) {\n      return;\n    }\n    this.drag();\n  }\n  drag() {\n    if (this.isWaitingInitialMove) {\n      if (this.startPoint.x !== this.endPoint.x || this.startPoint.y !== this.endPoint.y) {\n        this.ngZone.run(() => {\n          this.isWaitingInitialMove = false;\n          this.renderer.addClass(this.elRef.nativeElement, 'as-dragging');\n          this.draggedGutterNum = this.snapshot.gutterNum;\n          this.cdRef.markForCheck();\n          this.notify('start', this.snapshot.gutterNum);\n        });\n      } else {\n        return;\n      }\n    }\n    // Calculate steppedOffset\n    let offset = this.direction === 'horizontal' ? this.startPoint.x - this.endPoint.x : this.startPoint.y - this.endPoint.y;\n    // RTL requires negative offset only in horizontal mode as in vertical\n    // RTL has no effect on drag direction.\n    if (this.dir === 'rtl' && this.direction === 'horizontal') {\n      offset = -offset;\n    }\n    const steppedOffset = Math.round(offset / this.gutterStep) * this.gutterStep;\n    if (steppedOffset === this.snapshot.lastSteppedOffset) {\n      return;\n    }\n    this.snapshot.lastSteppedOffset = steppedOffset;\n    // Need to know if each gutter side areas could reacts to steppedOffset\n    let areasBefore = getGutterSideAbsorptionCapacity(this.unit, this.snapshot.areasBeforeGutter, -steppedOffset, this.snapshot.allAreasSizePixel);\n    let areasAfter = getGutterSideAbsorptionCapacity(this.unit, this.snapshot.areasAfterGutter, steppedOffset, this.snapshot.allAreasSizePixel);\n    // Each gutter side areas can't absorb all offset\n    if (areasBefore.remain !== 0 && areasAfter.remain !== 0) {\n      // TODO: fix this emty block\n      if (Math.abs(areasBefore.remain) === Math.abs(areasAfter.remain)) {\n        /* empty */\n      } else if (Math.abs(areasBefore.remain) > Math.abs(areasAfter.remain)) {\n        areasAfter = getGutterSideAbsorptionCapacity(this.unit, this.snapshot.areasAfterGutter, steppedOffset + areasBefore.remain, this.snapshot.allAreasSizePixel);\n      } else {\n        areasBefore = getGutterSideAbsorptionCapacity(this.unit, this.snapshot.areasBeforeGutter, -(steppedOffset - areasAfter.remain), this.snapshot.allAreasSizePixel);\n      }\n    } else if (areasBefore.remain !== 0) {\n      // Areas before gutter can't absorbs all offset > need to recalculate sizes for areas after gutter.\n      areasAfter = getGutterSideAbsorptionCapacity(this.unit, this.snapshot.areasAfterGutter, steppedOffset + areasBefore.remain, this.snapshot.allAreasSizePixel);\n    } else if (areasAfter.remain !== 0) {\n      // Areas after gutter can't absorbs all offset > need to recalculate sizes for areas before gutter.\n      areasBefore = getGutterSideAbsorptionCapacity(this.unit, this.snapshot.areasBeforeGutter, -(steppedOffset - areasAfter.remain), this.snapshot.allAreasSizePixel);\n    }\n    if (this.unit === 'percent') {\n      // Hack because of browser messing up with sizes using calc(X% - Ypx) -> el.getBoundingClientRect()\n      // If not there, playing with gutters makes total going down to 99.99875% then 99.99286%, 99.98986%,..\n      const all = [...areasBefore.list, ...areasAfter.list];\n      const wildcardArea = all.find(a => a.percentAfterAbsorption === '*');\n      // In case we have a wildcard area - always align the percents on the wildcard area.\n      const areaToReset = wildcardArea ?? all.find(a => a.percentAfterAbsorption !== 0 && a.percentAfterAbsorption !== a.areaSnapshot.area.minSize && a.percentAfterAbsorption !== a.areaSnapshot.area.maxSize);\n      if (areaToReset) {\n        areaToReset.percentAfterAbsorption = this.snapshot.allInvolvedAreasSizePercent - all.filter(a => a !== areaToReset).reduce((total, a) => total + a.percentAfterAbsorption, 0);\n      }\n    }\n    // Now we know areas could absorb steppedOffset, time to really update sizes\n    areasBefore.list.forEach(item => updateAreaSize(this.unit, item));\n    areasAfter.list.forEach(item => updateAreaSize(this.unit, item));\n    this.refreshStyleSizes();\n    this.notify('progress', this.snapshot.gutterNum);\n  }\n  stopDragging(event) {\n    if (event) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n    if (this.isDragging === false) {\n      return;\n    }\n    this.displayedAreas.forEach(area => area.component.unlockEvents());\n    while (this.dragListeners.length > 0) {\n      const fct = this.dragListeners.pop();\n      if (fct) {\n        fct();\n      }\n    }\n    // Warning: Have to be before \"notify('end')\"\n    // because \"notify('end')\"\" can be linked to \"[size]='x'\" > \"build()\" > \"stopDragging()\"\n    this.isDragging = false;\n    // If moved from starting point, notify end\n    if (this.isWaitingInitialMove === false) {\n      this.notify('end', this.snapshot.gutterNum);\n    }\n    this.renderer.removeClass(this.elRef.nativeElement, 'as-dragging');\n    this.draggedGutterNum = undefined;\n    this.cdRef.markForCheck();\n    this.snapshot = null;\n    this.isWaitingClear = true;\n    // Needed to let (click)=\"clickGutter(...)\" event run and verify if mouse moved or not\n    this.ngZone.runOutsideAngular(() => {\n      setTimeout(() => {\n        this.startPoint = null;\n        this.endPoint = null;\n        this.isWaitingClear = false;\n      });\n    });\n  }\n  notify(type, gutterNum) {\n    const sizes = this.getVisibleAreaSizes();\n    if (type === 'start') {\n      this.dragStart.emit({\n        gutterNum,\n        sizes\n      });\n    } else if (type === 'end') {\n      this.dragEnd.emit({\n        gutterNum,\n        sizes\n      });\n    } else if (type === 'click') {\n      this.gutterClick.emit({\n        gutterNum,\n        sizes\n      });\n    } else if (type === 'dblclick') {\n      this.gutterDblClick.emit({\n        gutterNum,\n        sizes\n      });\n    } else if (type === 'transitionEnd') {\n      if (this.transitionEndSubscriber) {\n        this.ngZone.run(() => this.transitionEndSubscriber.next(sizes));\n      }\n    } else if (type === 'progress') {\n      // Stay outside zone to allow users do what they want about change detection mechanism.\n      this.dragProgressSubject.next({\n        gutterNum,\n        sizes\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.stopDragging();\n  }\n  collapseArea(comp, newSize, gutter) {\n    const area = this.displayedAreas.find(a => a.component === comp);\n    if (area === undefined) {\n      return;\n    }\n    const whichGutter = gutter === 'right' ? 1 : -1;\n    if (!area.sizeBeforeCollapse) {\n      area.sizeBeforeCollapse = area.size;\n      area.gutterBeforeCollapse = whichGutter;\n    }\n    area.size = newSize;\n    const gtr = this.gutterEls.find(f => f.nativeElement.style.order === `${area.order + whichGutter}`);\n    if (gtr) {\n      this.renderer.addClass(gtr.nativeElement, 'as-split-gutter-collapsed');\n    }\n    this.updateArea(comp, false, false);\n  }\n  expandArea(comp) {\n    const area = this.displayedAreas.find(a => a.component === comp);\n    if (area === undefined) {\n      return;\n    }\n    if (!area.sizeBeforeCollapse) {\n      return;\n    }\n    area.size = area.sizeBeforeCollapse;\n    area.sizeBeforeCollapse = null;\n    const gtr = this.gutterEls.find(f => f.nativeElement.style.order === `${area.order + area.gutterBeforeCollapse}`);\n    if (gtr) {\n      this.renderer.removeClass(gtr.nativeElement, 'as-split-gutter-collapsed');\n    }\n    this.updateArea(comp, false, false);\n  }\n  getAriaAreaSizeText(size) {\n    if (size === '*') {\n      return null;\n    }\n    return size.toFixed(0) + ' ' + this.unit;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitComponent_Factory(t) {\n      return new (t || SplitComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(ANGULAR_SPLIT_DEFAULT_OPTIONS, 8));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SplitComponent,\n      selectors: [[\"as-split\"]],\n      contentQueries: function SplitComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, SplitGutterDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customGutter = _t.first);\n        }\n      },\n      viewQuery: function SplitComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.gutterEls = _t);\n        }\n      },\n      inputs: {\n        direction: \"direction\",\n        unit: \"unit\",\n        gutterSize: \"gutterSize\",\n        gutterStep: \"gutterStep\",\n        restrictMove: \"restrictMove\",\n        useTransition: \"useTransition\",\n        disabled: \"disabled\",\n        dir: \"dir\",\n        gutterDblClickDuration: \"gutterDblClickDuration\",\n        gutterClickDeltaPx: \"gutterClickDeltaPx\",\n        gutterAriaLabel: \"gutterAriaLabel\"\n      },\n      outputs: {\n        transitionEnd: \"transitionEnd\",\n        dragStart: \"dragStart\",\n        dragEnd: \"dragEnd\",\n        gutterClick: \"gutterClick\",\n        gutterDblClick: \"gutterDblClick\"\n      },\n      exportAs: [\"asSplit\"],\n      ngContentSelectors: _c1,\n      decls: 2,\n      vars: 1,\n      consts: [[\"gutterEls\", \"\"], [\"defaultGutterTpl\", \"\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"role\", \"separator\", \"tabindex\", \"0\", \"class\", \"as-split-gutter\", 3, \"as-dragged\", \"flex-basis\", \"order\", \"keydown\", \"mousedown\", \"touchstart\", \"mouseup\", \"touchend\", 4, \"ngIf\"], [\"role\", \"separator\", \"tabindex\", \"0\", 1, \"as-split-gutter\", 3, \"keydown\", \"mousedown\", \"touchstart\", \"mouseup\", \"touchend\"], [4, \"ngIf\", \"ngIfElse\"], [4, \"asSplitGutterDynamicInjector\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\", \"ngTemplateOutletInjector\"], [1, \"as-split-gutter-icon\"]],\n      template: function SplitComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵtemplate(1, SplitComponent_ng_template_1_Template, 1, 1, \"ng-template\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.displayedAreas);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, SplitGutterDynamicInjectorDirective],\n      styles: [\"[_nghost-%COMP%]{display:flex;flex-wrap:nowrap;justify-content:flex-start;align-items:stretch;overflow:hidden;width:100%;height:100%}[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%]{border:none;flex-grow:0;flex-shrink:0;background-color:#eee;display:flex;align-items:center;justify-content:center}[_nghost-%COMP%] > .as-split-gutter.as-split-gutter-collapsed[_ngcontent-%COMP%]{flex-basis:1px!important;pointer-events:none}[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%] > .as-split-gutter-icon[_ngcontent-%COMP%]{width:100%;height:100%;background-position:center center;background-repeat:no-repeat}[_nghost-%COMP%]    >.as-split-area{flex-grow:0;flex-shrink:0;overflow-x:hidden;overflow-y:auto}[_nghost-%COMP%]    >.as-split-area.as-hidden{flex:0 1 0px!important;overflow-x:hidden;overflow-y:hidden}[_nghost-%COMP%]    >.as-split-area .iframe-fix{position:absolute;top:0;left:0;width:100%;height:100%}.as-horizontal[_nghost-%COMP%]{flex-direction:row}.as-horizontal[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%]{flex-direction:row;cursor:col-resize;height:100%}.as-horizontal[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%] > .as-split-gutter-icon[_ngcontent-%COMP%]{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==)}.as-horizontal[_nghost-%COMP%]    >.as-split-area{height:100%}.as-vertical[_nghost-%COMP%]{flex-direction:column}.as-vertical[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%]{flex-direction:column;cursor:row-resize;width:100%}.as-vertical[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%]   .as-split-gutter-icon[_ngcontent-%COMP%]{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC)}.as-vertical[_nghost-%COMP%]    >.as-split-area{width:100%}.as-vertical[_nghost-%COMP%]    >.as-split-area.as-hidden{max-width:0}.as-disabled[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%]{cursor:default}.as-disabled[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%]   .as-split-gutter-icon[_ngcontent-%COMP%]{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==)}.as-transition.as-init[_nghost-%COMP%]:not(.as-dragging) > .as-split-gutter[_ngcontent-%COMP%], .as-transition.as-init[_nghost-%COMP%]:not(.as-dragging)    >.as-split-area{transition:flex-basis .3s}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitComponent, [{\n    type: Component,\n    args: [{\n      selector: 'as-split',\n      exportAs: 'asSplit',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: ` <ng-content></ng-content>\n    <ng-template\n      ngFor\n      [ngForOf]=\"displayedAreas\"\n      let-area=\"$implicit\"\n      let-index=\"index\"\n      let-first=\"first\"\n      let-last=\"last\"\n    >\n      <div\n        role=\"separator\"\n        tabindex=\"0\"\n        *ngIf=\"last === false\"\n        #gutterEls\n        class=\"as-split-gutter\"\n        [class.as-dragged]=\"draggedGutterNum === index + 1\"\n        [style.flex-basis.px]=\"gutterSize\"\n        [style.order]=\"index * 2 + 1\"\n        (keydown)=\"startKeyboardDrag($event, index * 2 + 1, index + 1)\"\n        (mousedown)=\"startMouseDrag($event, index * 2 + 1, index + 1)\"\n        (touchstart)=\"startMouseDrag($event, index * 2 + 1, index + 1)\"\n        (mouseup)=\"clickGutter($event, index + 1)\"\n        (touchend)=\"clickGutter($event, index + 1)\"\n        [attr.aria-label]=\"gutterAriaLabel\"\n        [attr.aria-orientation]=\"direction\"\n        [attr.aria-valuemin]=\"area.minSize\"\n        [attr.aria-valuemax]=\"area.maxSize\"\n        [attr.aria-valuenow]=\"area.size === '*' ? null : area.size\"\n        [attr.aria-valuetext]=\"getAriaAreaSizeText(area.size)\"\n      >\n        <ng-container *ngIf=\"customGutter?.template; else defaultGutterTpl\">\n          <ng-container *asSplitGutterDynamicInjector=\"index + 1; let injector\">\n            <ng-container\n              *ngTemplateOutlet=\"\n                customGutter.template;\n                context: {\n                  areaBefore: area,\n                  areaAfter: displayedAreas[index + 1],\n                  gutterNum: index + 1,\n                  first,\n                  last: index === displayedAreas.length - 2,\n                  isDragged: draggedGutterNum === index + 1\n                };\n                injector: injector\n              \"\n            ></ng-container>\n          </ng-container>\n        </ng-container>\n        <ng-template #defaultGutterTpl>\n          <div class=\"as-split-gutter-icon\"></div>\n        </ng-template>\n      </div>\n    </ng-template>`,\n      encapsulation: ViewEncapsulation.Emulated,\n      styles: [\":host{display:flex;flex-wrap:nowrap;justify-content:flex-start;align-items:stretch;overflow:hidden;width:100%;height:100%}:host>.as-split-gutter{border:none;flex-grow:0;flex-shrink:0;background-color:#eee;display:flex;align-items:center;justify-content:center}:host>.as-split-gutter.as-split-gutter-collapsed{flex-basis:1px!important;pointer-events:none}:host>.as-split-gutter>.as-split-gutter-icon{width:100%;height:100%;background-position:center center;background-repeat:no-repeat}:host ::ng-deep>.as-split-area{flex-grow:0;flex-shrink:0;overflow-x:hidden;overflow-y:auto}:host ::ng-deep>.as-split-area.as-hidden{flex:0 1 0px!important;overflow-x:hidden;overflow-y:hidden}:host ::ng-deep>.as-split-area .iframe-fix{position:absolute;top:0;left:0;width:100%;height:100%}:host.as-horizontal{flex-direction:row}:host.as-horizontal>.as-split-gutter{flex-direction:row;cursor:col-resize;height:100%}:host.as-horizontal>.as-split-gutter>.as-split-gutter-icon{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==)}:host.as-horizontal ::ng-deep>.as-split-area{height:100%}:host.as-vertical{flex-direction:column}:host.as-vertical>.as-split-gutter{flex-direction:column;cursor:row-resize;width:100%}:host.as-vertical>.as-split-gutter .as-split-gutter-icon{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC)}:host.as-vertical ::ng-deep>.as-split-area{width:100%}:host.as-vertical ::ng-deep>.as-split-area.as-hidden{max-width:0}:host.as-disabled>.as-split-gutter{cursor:default}:host.as-disabled>.as-split-gutter .as-split-gutter-icon{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==)}:host.as-transition.as-init:not(.as-dragging)>.as-split-gutter,:host.as-transition.as-init:not(.as-dragging) ::ng-deep>.as-split-area{transition:flex-basis .3s}\\n\"]\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANGULAR_SPLIT_DEFAULT_OPTIONS]\n    }]\n  }], {\n    customGutter: [{\n      type: ContentChild,\n      args: [SplitGutterDirective]\n    }],\n    direction: [{\n      type: Input\n    }],\n    unit: [{\n      type: Input\n    }],\n    gutterSize: [{\n      type: Input\n    }],\n    gutterStep: [{\n      type: Input\n    }],\n    restrictMove: [{\n      type: Input\n    }],\n    useTransition: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    dir: [{\n      type: Input\n    }],\n    gutterDblClickDuration: [{\n      type: Input\n    }],\n    gutterClickDeltaPx: [{\n      type: Input\n    }],\n    gutterAriaLabel: [{\n      type: Input\n    }],\n    transitionEnd: [{\n      type: Output\n    }],\n    dragStart: [{\n      type: Output\n    }],\n    dragEnd: [{\n      type: Output\n    }],\n    gutterClick: [{\n      type: Output\n    }],\n    gutterDblClick: [{\n      type: Output\n    }],\n    gutterEls: [{\n      type: ViewChildren,\n      args: ['gutterEls']\n    }]\n  });\n})();\nclass SplitAreaDirective {\n  set order(v) {\n    this._order = getInputPositiveNumber(v, null);\n    this.split.updateArea(this, true, false);\n  }\n  get order() {\n    return this._order;\n  }\n  set size(v) {\n    this._size = getInputPositiveNumber(v, '*');\n    this.split.updateArea(this, false, true);\n  }\n  get size() {\n    return this._size;\n  }\n  set minSize(v) {\n    this._minSize = getInputPositiveNumber(v, null);\n    this.split.updateArea(this, false, true);\n  }\n  get minSize() {\n    return this._minSize;\n  }\n  set maxSize(v) {\n    this._maxSize = getInputPositiveNumber(v, null);\n    this.split.updateArea(this, false, true);\n  }\n  get maxSize() {\n    return this._maxSize;\n  }\n  set lockSize(v) {\n    this._lockSize = getInputBoolean(v);\n    this.split.updateArea(this, false, true);\n  }\n  get lockSize() {\n    return this._lockSize;\n  }\n  set visible(v) {\n    this._visible = getInputBoolean(v);\n    if (this._visible) {\n      this.split.showArea(this);\n      this.renderer.removeClass(this.elRef.nativeElement, 'as-hidden');\n    } else {\n      this.split.hideArea(this);\n      this.renderer.addClass(this.elRef.nativeElement, 'as-hidden');\n    }\n  }\n  get visible() {\n    return this._visible;\n  }\n  constructor(ngZone, renderer, split, elRef) {\n    this.ngZone = ngZone;\n    this.renderer = renderer;\n    this.split = split;\n    this.elRef = elRef;\n    this._order = null;\n    this._size = '*';\n    this._minSize = null;\n    this._maxSize = null;\n    this._lockSize = false;\n    this._visible = true;\n    this.lockListeners = [];\n    this.renderer.addClass(this.elRef.nativeElement, 'as-split-area');\n  }\n  ngOnInit() {\n    this.split.addArea(this);\n    this.ngZone.runOutsideAngular(() => {\n      this.transitionListener = this.renderer.listen(this.elRef.nativeElement, 'transitionend', event => {\n        // Limit only flex-basis transition to trigger the event\n        if (event.propertyName === 'flex-basis') {\n          this.split.notify('transitionEnd', -1);\n        }\n      });\n    });\n    const iframeFixDiv = this.renderer.createElement('div');\n    this.renderer.addClass(iframeFixDiv, 'iframe-fix');\n    this.dragStartSubscription = this.split.dragStart.subscribe(() => {\n      this.renderer.setStyle(this.elRef.nativeElement, 'position', 'relative');\n      this.renderer.appendChild(this.elRef.nativeElement, iframeFixDiv);\n    });\n    this.dragEndSubscription = this.split.dragEnd.subscribe(() => {\n      this.renderer.removeStyle(this.elRef.nativeElement, 'position');\n      this.renderer.removeChild(this.elRef.nativeElement, iframeFixDiv);\n    });\n  }\n  setStyleOrder(value) {\n    this.renderer.setStyle(this.elRef.nativeElement, 'order', value);\n  }\n  setStyleFlex(grow, shrink, basis, isMin, isMax) {\n    // Need 3 separated properties to work on IE11 (https://github.com/angular/flex-layout/issues/323)\n    this.renderer.setStyle(this.elRef.nativeElement, 'flex-grow', grow);\n    this.renderer.setStyle(this.elRef.nativeElement, 'flex-shrink', shrink);\n    this.renderer.setStyle(this.elRef.nativeElement, 'flex-basis', basis);\n    if (isMin === true) {\n      this.renderer.addClass(this.elRef.nativeElement, 'as-min');\n    } else {\n      this.renderer.removeClass(this.elRef.nativeElement, 'as-min');\n    }\n    if (isMax === true) {\n      this.renderer.addClass(this.elRef.nativeElement, 'as-max');\n    } else {\n      this.renderer.removeClass(this.elRef.nativeElement, 'as-max');\n    }\n  }\n  lockEvents() {\n    this.ngZone.runOutsideAngular(() => {\n      this.lockListeners.push(this.renderer.listen(this.elRef.nativeElement, 'selectstart', () => false));\n      this.lockListeners.push(this.renderer.listen(this.elRef.nativeElement, 'dragstart', () => false));\n    });\n  }\n  unlockEvents() {\n    while (this.lockListeners.length > 0) {\n      const fct = this.lockListeners.pop();\n      if (fct) {\n        fct();\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.unlockEvents();\n    if (this.transitionListener) {\n      this.transitionListener();\n    }\n    this.dragStartSubscription?.unsubscribe();\n    this.dragEndSubscription?.unsubscribe();\n    this.split.removeArea(this);\n  }\n  collapse(newSize = 0, gutter = 'right') {\n    this.split.collapseArea(this, newSize, gutter);\n  }\n  expand() {\n    this.split.expandArea(this);\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitAreaDirective_Factory(t) {\n      return new (t || SplitAreaDirective)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(SplitComponent), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitAreaDirective,\n      selectors: [[\"as-split-area\"], [\"\", \"as-split-area\", \"\"]],\n      inputs: {\n        order: \"order\",\n        size: \"size\",\n        minSize: \"minSize\",\n        maxSize: \"maxSize\",\n        lockSize: \"lockSize\",\n        visible: \"visible\"\n      },\n      exportAs: [\"asSplitArea\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitAreaDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: 'as-split-area, [as-split-area]',\n      exportAs: 'asSplitArea'\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: SplitComponent\n  }, {\n    type: i0.ElementRef\n  }], {\n    order: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    minSize: [{\n      type: Input\n    }],\n    maxSize: [{\n      type: Input\n    }],\n    lockSize: [{\n      type: Input\n    }],\n    visible: [{\n      type: Input\n    }]\n  });\n})();\nclass SplitGutterDragHandleDirective {\n  constructor(gutterNum, elementRef, gutterDir) {\n    this.gutterNum = gutterNum;\n    this.elementRef = elementRef;\n    this.gutterDir = gutterDir;\n  }\n  ngOnInit() {\n    this.gutterDir.addToMap(this.gutterDir.gutterToHandleElementMap, this.gutterNum, this.elementRef);\n  }\n  ngOnDestroy() {\n    this.gutterDir.removedFromMap(this.gutterDir.gutterToHandleElementMap, this.gutterNum, this.elementRef);\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitGutterDragHandleDirective_Factory(t) {\n      return new (t || SplitGutterDragHandleDirective)(i0.ɵɵdirectiveInject(GUTTER_NUM_TOKEN), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(SplitGutterDirective));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitGutterDragHandleDirective,\n      selectors: [[\"\", \"asSplitGutterDragHandle\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitGutterDragHandleDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitGutterDragHandle]'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [GUTTER_NUM_TOKEN]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: SplitGutterDirective\n  }], null);\n})();\nclass SplitGutterExcludeFromDragDirective {\n  constructor(gutterNum, elementRef, gutterDir) {\n    this.gutterNum = gutterNum;\n    this.elementRef = elementRef;\n    this.gutterDir = gutterDir;\n  }\n  ngOnInit() {\n    this.gutterDir.addToMap(this.gutterDir.gutterToExcludeDragElementMap, this.gutterNum, this.elementRef);\n  }\n  ngOnDestroy() {\n    this.gutterDir.removedFromMap(this.gutterDir.gutterToExcludeDragElementMap, this.gutterNum, this.elementRef);\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitGutterExcludeFromDragDirective_Factory(t) {\n      return new (t || SplitGutterExcludeFromDragDirective)(i0.ɵɵdirectiveInject(GUTTER_NUM_TOKEN), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(SplitGutterDirective));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitGutterExcludeFromDragDirective,\n      selectors: [[\"\", \"asSplitGutterExcludeFromDrag\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitGutterExcludeFromDragDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitGutterExcludeFromDrag]'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [GUTTER_NUM_TOKEN]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: SplitGutterDirective\n  }], null);\n})();\nclass AngularSplitModule {\n  /** @nocollapse */static {\n    this.ɵfac = function AngularSplitModule_Factory(t) {\n      return new (t || AngularSplitModule)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AngularSplitModule,\n      declarations: [SplitComponent, SplitAreaDirective, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterDynamicInjectorDirective, SplitGutterExcludeFromDragDirective],\n      imports: [CommonModule],\n      exports: [SplitComponent, SplitAreaDirective, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective]\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularSplitModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [SplitComponent, SplitAreaDirective, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterDynamicInjectorDirective, SplitGutterExcludeFromDragDirective],\n      exports: [SplitComponent, SplitAreaDirective, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of angular-split\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ANGULAR_SPLIT_DEFAULT_OPTIONS, AngularSplitModule, SplitAreaDirective, SplitComponent, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ;AAAA,EACvC,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,OAAO;AAAA,EACP,MAAM;AAAA,EACN,WAAW;AACb;AACA,SAAS,yFAAyF,IAAI,KAAK;AACzG,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,0FAA0F,GAAG,GAAG,gBAAgB,CAAC;AAClI,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,UAAU,OAAO;AACvB,UAAM,WAAW,OAAO;AACxB,UAAM,WAAW,OAAO;AACxB,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,aAAa,QAAQ,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,SAAS,OAAO,eAAe,WAAW,CAAC,GAAG,WAAW,GAAG,UAAU,aAAa,OAAO,eAAe,SAAS,GAAG,OAAO,qBAAqB,WAAW,CAAC,CAAC,EAAE,4BAA4B,WAAW;AAAA,EAC/T;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,2EAA2E,GAAG,IAAI,gBAAgB,CAAC;AACpH,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,WAAW,gCAAgC,WAAW,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,WAAW,WAAW,SAAS,mEAAmE,QAAQ;AAC3G,MAAG,cAAc,GAAG;AACpB,YAAM,WAAc,cAAc,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,kBAAkB,QAAQ,WAAW,IAAI,GAAG,WAAW,CAAC,CAAC;AAAA,IACxF,CAAC,EAAE,aAAa,SAAS,qEAAqE,QAAQ;AACpG,MAAG,cAAc,GAAG;AACpB,YAAM,WAAc,cAAc,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,QAAQ,WAAW,IAAI,GAAG,WAAW,CAAC,CAAC;AAAA,IACrF,CAAC,EAAE,cAAc,SAAS,sEAAsE,QAAQ;AACtG,MAAG,cAAc,GAAG;AACpB,YAAM,WAAc,cAAc,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,QAAQ,WAAW,IAAI,GAAG,WAAW,CAAC,CAAC;AAAA,IACrF,CAAC,EAAE,WAAW,SAAS,mEAAmE,QAAQ;AAChG,MAAG,cAAc,GAAG;AACpB,YAAM,WAAc,cAAc,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,QAAQ,WAAW,CAAC,CAAC;AAAA,IAChE,CAAC,EAAE,YAAY,SAAS,oEAAoE,QAAQ;AAClG,MAAG,cAAc,GAAG;AACpB,YAAM,WAAc,cAAc,EAAE;AACpC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,QAAQ,WAAW,CAAC,CAAC;AAAA,IAChE,CAAC;AACD,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,2DAA2D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC3N,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,sBAAyB,YAAY,CAAC;AAC5C,UAAM,SAAY,cAAc;AAChC,UAAM,UAAU,OAAO;AACvB,UAAM,WAAW,OAAO;AACxB,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,cAAc,OAAO,YAAY,IAAI,EAAE,SAAS,WAAW,IAAI,CAAC;AAC/E,IAAG,YAAY,cAAc,OAAO,qBAAqB,WAAW,CAAC;AACrE,IAAG,YAAY,cAAc,OAAO,eAAe,EAAE,oBAAoB,OAAO,SAAS,EAAE,iBAAiB,QAAQ,OAAO,EAAE,iBAAiB,QAAQ,OAAO,EAAE,iBAAiB,QAAQ,SAAS,MAAM,OAAO,QAAQ,IAAI,EAAE,kBAAkB,OAAO,oBAAoB,QAAQ,IAAI,CAAC;AACtR,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,gBAAgB,OAAO,OAAO,OAAO,aAAa,QAAQ,EAAE,YAAY,mBAAmB;AAAA,EAC1H;AACF;AACA,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6CAA6C,GAAG,IAAI,OAAO,CAAC;AAAA,EAC/E;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,WAAW,QAAQ,YAAY,KAAK;AAAA,EACzC;AACF;AACA,SAAS,kBAAkB,OAAO;AAEhC,MAAI,MAAM,mBAAmB,UAAa,MAAM,eAAe,SAAS,GAAG;AACzE,WAAO;AAAA,MACL,GAAG,MAAM,eAAe,CAAC,EAAE;AAAA,MAC3B,GAAG,MAAM,eAAe,CAAC,EAAE;AAAA,IAC7B;AAAA,EACF,WAES,MAAM,YAAY,UAAa,MAAM,YAAY,QAAW;AACnE,WAAO;AAAA,MACL,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,IACX;AAAA,EACF,WAES,MAAM,kBAAkB,QAAW;AAC1C,UAAM,WAAW,MAAM;AACvB,WAAO;AAAA,MACL,GAAG,SAAS;AAAA,MACZ,GAAG,SAAS;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,KAAK,KAAK,SAAS;AAC3C,SAAO,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,WAAW,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK;AAC1E;AACA,SAAS,oBAAoB,OAAO,WAAW;AAE7C,MAAI,cAAc,cAAc;AAC9B,YAAQ,MAAM,KAAK;AAAA,MACjB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH;AAAA,MACF;AACE,eAAO;AAAA,IACX;AAAA,EACF;AACA,MAAI,cAAc,YAAY;AAC5B,YAAQ,MAAM,KAAK;AAAA,MACjB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH;AAAA,MACF;AACE,eAAO;AAAA,IACX;AAAA,EACF;AACA,QAAM,WAAW,MAAM;AACvB,QAAM,SAAS,MAAM,QAAQ,YAAY,MAAM,QAAQ,aAAa,KAAK,KAAK;AAC9E,MAAI,UAAU,SAAS,YACrB,UAAU,SAAS;AACrB,UAAQ,MAAM,KAAK;AAAA,IACjB,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,UAAI,cAAc,YAAY;AAC5B,mBAAW;AAAA,MACb,OAAO;AACL,mBAAW;AAAA,MACb;AACA;AAAA,IACF,KAAK;AACH,UAAI,cAAc,YAAY;AAC5B,mBAAW;AAAA,MACb,OAAO;AACL,mBAAW;AAAA,MACb;AACA;AAAA,IACF;AACE,aAAO;AAAA,EACX;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AACA,SAAS,oBAAoB,OAAO,WAAW;AAC7C,QAAM,OAAO,MAAM,cAAc,sBAAsB;AACvD,SAAO,cAAc,eAAe,KAAK,QAAQ,KAAK;AACxD;AACA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,OAAO,MAAM,YAAY,IAAI,MAAM;AAC5C;AACA,SAAS,uBAAuB,GAAG,cAAc;AAC/C,MAAI,MAAM,QAAQ,MAAM,OAAW,QAAO;AAC1C,MAAI,OAAO,CAAC;AACZ,SAAO,CAAC,MAAM,CAAC,KAAK,KAAK,IAAI,IAAI;AACnC;AACA,SAAS,iBAAiB,MAAM,OAAO;AAIrC,MAAI,SAAS,WAAW;AACtB,UAAM,QAAQ,MAAM,OAAO,CAACA,QAAO,MAAM,MAAM,MAAMA,SAAQ,IAAIA,QAAO,CAAC;AACzE,UAAM,oBAAoB,MAAM,OAAO,UAAQ,SAAS,GAAG;AAC3D,QAAI,kBAAkB,SAAS,GAAG;AAChC,aAAO;AAAA,IACT;AACA,QAAI,kBAAkB,WAAW,GAAG;AAClC,aAAO,QAAQ;AAAA,IACjB;AACA,WAAO,QAAQ,QAAQ,QAAQ;AAAA,EACjC;AAEA,MAAI,SAAS,SAAS;AACpB,WAAO,MAAM,OAAO,OAAK,MAAM,GAAG,EAAE,WAAW;AAAA,EACjD;AACF;AACA,SAAS,eAAe,GAAG;AACzB,MAAI,EAAE,SAAS,KAAK;AAClB,WAAO;AAAA,EACT;AACA,MAAI,EAAE,UAAU,aAAa,MAAM;AACjC,WAAO,EAAE;AAAA,EACX;AACA,MAAI,EAAE,UAAU,YAAY,MAAM;AAChC,WAAO;AAAA,EACT;AACA,SAAO,EAAE,UAAU;AACrB;AACA,SAAS,eAAe,GAAG;AACzB,MAAI,EAAE,SAAS,KAAK;AAClB,WAAO;AAAA,EACT;AACA,MAAI,EAAE,UAAU,aAAa,MAAM;AACjC,WAAO,EAAE;AAAA,EACX;AACA,MAAI,EAAE,UAAU,YAAY,MAAM;AAChC,WAAO;AAAA,EACT;AACA,MAAI,EAAE,UAAU,UAAU,EAAE,MAAM;AAChC,WAAO,EAAE;AAAA,EACX;AACA,SAAO,EAAE,UAAU;AACrB;AACA,SAAS,gCAAgC,MAAM,WAAW,QAAQ,mBAAmB;AACnF,SAAO,UAAU,OAAO,CAAC,KAAK,SAAS;AACrC,UAAM,MAAM,0BAA0B,MAAM,MAAM,IAAI,QAAQ,iBAAiB;AAC/E,QAAI,KAAK,KAAK,GAAG;AACjB,QAAI,SAAS,IAAI;AACjB,WAAO;AAAA,EACT,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,MAAM,CAAC;AAAA,EACT,CAAC;AACH;AACA,SAAS,0BAA0B,MAAM,cAAc,QAAQ,mBAAmB;AAEhF,MAAI,WAAW,GAAG;AAChB,WAAO;AAAA,MACL;AAAA,MACA,aAAa;AAAA,MACb,wBAAwB,aAAa;AAAA,MACrC,aAAa;AAAA,IACf;AAAA,EACF;AAEA,MAAI,aAAa,qBAAqB,KAAK,SAAS,GAAG;AACrD,WAAO;AAAA,MACL;AAAA,MACA,aAAa;AAAA,MACb,wBAAwB;AAAA,MACxB,aAAa;AAAA,IACf;AAAA,EACF;AACA,MAAI,SAAS,WAAW;AACtB,WAAO,iCAAiC,cAAc,QAAQ,iBAAiB;AAAA,EACjF;AACA,MAAI,SAAS,SAAS;AACpB,WAAO,+BAA+B,cAAc,MAAM;AAAA,EAC5D;AACF;AACA,SAAS,iCAAiC,cAAc,QAAQ,mBAAmB;AACjF,QAAM,gBAAgB,aAAa,mBAAmB;AACtD,QAAM,kBAAkB,gBAAgB,oBAAoB;AAE5D,MAAI,SAAS,GAAG;AAEd,QAAI,aAAa,KAAK,YAAY,QAAQ,kBAAkB,aAAa,KAAK,SAAS;AAErF,YAAM,eAAe,aAAa,KAAK,UAAU,MAAM;AACvD,aAAO;AAAA,QACL;AAAA,QACA,aAAa;AAAA,QACb,wBAAwB,aAAa,KAAK;AAAA,QAC1C,aAAa,aAAa,mBAAmB,SAAS;AAAA,MACxD;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA,aAAa;AAAA,MACb,wBAAwB,kBAAkB,MAAM,MAAM;AAAA,MACtD,aAAa;AAAA,IACf;AAAA,EACF,WAES,SAAS,GAAG;AAEnB,QAAI,aAAa,KAAK,YAAY,QAAQ,kBAAkB,aAAa,KAAK,SAAS;AAErF,YAAM,eAAe,aAAa,KAAK,UAAU,MAAM;AACvD,aAAO;AAAA,QACL;AAAA,QACA,aAAa;AAAA,QACb,wBAAwB,aAAa,KAAK;AAAA,QAC1C,aAAa,aAAa,mBAAmB,SAAS;AAAA,MACxD;AAAA,IACF,WAES,kBAAkB,GAAG;AAE5B,aAAO;AAAA,QACL;AAAA,QACA,aAAa,CAAC,aAAa;AAAA,QAC3B,wBAAwB;AAAA,QACxB,aAAa,SAAS,aAAa;AAAA,MACrC;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA,aAAa;AAAA,MACb,wBAAwB;AAAA,MACxB,aAAa;AAAA,IACf;AAAA,EACF;AACF;AACA,SAAS,+BAA+B,cAAc,QAAQ;AAC5D,QAAM,gBAAgB,aAAa,mBAAmB;AAEtD,MAAI,SAAS,GAAG;AAEd,QAAI,aAAa,KAAK,YAAY,QAAQ,gBAAgB,aAAa,KAAK,SAAS;AACnF,aAAO;AAAA,QACL;AAAA,QACA,aAAa,aAAa,KAAK,UAAU,aAAa;AAAA,QACtD,wBAAwB;AAAA,QACxB,aAAa,gBAAgB,aAAa,KAAK;AAAA,MACjD;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA,aAAa;AAAA,MACb,wBAAwB;AAAA,MACxB,aAAa;AAAA,IACf;AAAA,EACF,WAES,SAAS,GAAG;AAEnB,QAAI,aAAa,KAAK,YAAY,QAAQ,gBAAgB,aAAa,KAAK,SAAS;AACnF,aAAO;AAAA,QACL;AAAA,QACA,aAAa,aAAa,KAAK,UAAU,SAAS;AAAA,QAClD,wBAAwB;AAAA,QACxB,aAAa,gBAAgB,aAAa,KAAK;AAAA,MACjD;AAAA,IACF,WAES,gBAAgB,GAAG;AAC1B,aAAO;AAAA,QACL;AAAA,QACA,aAAa,CAAC,aAAa;AAAA,QAC3B,wBAAwB;AAAA,QACxB,aAAa,SAAS,aAAa;AAAA,MACrC;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA,aAAa;AAAA,MACb,wBAAwB;AAAA,MACxB,aAAa;AAAA,IACf;AAAA,EACF;AACF;AACA,SAAS,eAAe,MAAM,MAAM;AAElC,MAAI,KAAK,aAAa,KAAK,SAAS,KAAK;AACvC,QAAI,SAAS,WAAW;AACtB,WAAK,aAAa,KAAK,OAAO,KAAK;AAAA,IACrC,WAAW,SAAS,SAAS;AAC3B,WAAK,aAAa,KAAK,OAAO,KAAK,aAAa,mBAAmB,KAAK;AAAA,IAC1E;AAAA,EACF;AACF;AACA,IAAM,gCAAgC,IAAI,eAAe,6BAA6B;AACtF,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,UAAU;AACpB,SAAK,WAAW;AAKhB,SAAK,2BAA2B,oBAAI,IAAI;AAKxC,SAAK,gCAAgC,oBAAI,IAAI;AAAA,EAC/C;AAAA,EACA,iBAAiB,eAAe,WAAW;AACzC,QAAI,KAAK,8BAA8B,IAAI,SAAS,GAAG;AACrD,YAAM,kBAAkB,KAAK,8BAA8B,IAAI,SAAS,EAAE,KAAK,0BAAwB,qBAAqB,cAAc,SAAS,aAAa,CAAC;AACjK,UAAI,iBAAiB;AACnB,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,KAAK,yBAAyB,IAAI,SAAS,GAAG;AAChD,aAAO,KAAK,yBAAyB,IAAI,SAAS,EAAE,KAAK,yBAAuB,oBAAoB,cAAc,SAAS,aAAa,CAAC;AAAA,IAC3I;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS,KAAK,WAAW,YAAY;AACnC,QAAI,IAAI,IAAI,SAAS,GAAG;AACtB,UAAI,IAAI,SAAS,EAAE,KAAK,UAAU;AAAA,IACpC,OAAO;AACL,UAAI,IAAI,WAAW,CAAC,UAAU,CAAC;AAAA,IACjC;AAAA,EACF;AAAA,EACA,eAAe,KAAK,WAAW,YAAY;AACzC,UAAM,WAAW,IAAI,IAAI,SAAS;AAClC,aAAS,OAAO,SAAS,QAAQ,UAAU,GAAG,CAAC;AAC/C,QAAI,SAAS,WAAW,GAAG;AACzB,UAAI,OAAO,SAAS;AAAA,IACtB;AAAA,EACF;AAAA,EACA,OAAO,uBAAuB,KAAK,KAAK;AACtC,WAAO;AAAA,EACT;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,kBAAqB,WAAW,CAAC;AAAA,IAC7E;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACvC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAOH,IAAM,mBAAmB,IAAI,eAAe,YAAY;AAMxD,IAAM,sCAAN,MAAM,qCAAoC;AAAA,EACxC,IAAI,UAAU,OAAO;AACnB,SAAK,IAAI,MAAM;AACf,UAAM,WAAW,SAAS,OAAO;AAAA,MAC/B,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,MACD,QAAQ,KAAK,IAAI;AAAA,IACnB,CAAC;AACD,SAAK,IAAI,mBAAmB,KAAK,aAAa;AAAA,MAC5C,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,YAAY,KAAK,aAAa;AAC5B,SAAK,MAAM;AACX,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO,uBAAuB,KAAK,KAAK;AACtC,WAAO;AAAA,EACT;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,4CAA4C,GAAG;AAClE,aAAO,KAAK,KAAK,sCAAwC,kBAAqB,gBAAgB,GAAM,kBAAqB,WAAW,CAAC;AAAA,IACvI;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gCAAgC,EAAE,CAAC;AAAA,MACpD,QAAQ;AAAA,QACN,WAAW,CAAI,WAAa,MAAM,gCAAgC,WAAW;AAAA,MAC/E;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qCAAqC,CAAC;AAAA,IAC5G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AA+BH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,IAAI,UAAU,GAAG;AACf,SAAK,aAAa,MAAM,aAAa,aAAa;AAClD,SAAK,SAAS,SAAS,KAAK,MAAM,eAAe,MAAM,KAAK,UAAU,EAAE;AACxE,SAAK,SAAS,YAAY,KAAK,MAAM,eAAe,MAAM,KAAK,eAAe,aAAa,eAAe,UAAU,EAAE;AACtH,SAAK,MAAM,OAAO,KAAK;AAAA,EACzB;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,GAAG;AACV,SAAK,QAAQ,MAAM,UAAU,UAAU;AACvC,SAAK,SAAS,SAAS,KAAK,MAAM,eAAe,MAAM,KAAK,KAAK,EAAE;AACnE,SAAK,SAAS,YAAY,KAAK,MAAM,eAAe,MAAM,KAAK,UAAU,UAAU,YAAY,OAAO,EAAE;AACxG,SAAK,MAAM,OAAO,IAAI;AAAA,EACxB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,GAAG;AAChB,SAAK,cAAc,uBAAuB,GAAG,EAAE;AAC/C,SAAK,MAAM,OAAO,KAAK;AAAA,EACzB;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,GAAG;AAChB,SAAK,cAAc,uBAAuB,GAAG,CAAC;AAAA,EAChD;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa,GAAG;AAClB,SAAK,gBAAgB,gBAAgB,CAAC;AAAA,EACxC;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,GAAG;AACnB,SAAK,iBAAiB,gBAAgB,CAAC;AACvC,QAAI,KAAK,gBAAgB;AACvB,WAAK,SAAS,SAAS,KAAK,MAAM,eAAe,eAAe;AAAA,IAClE,OAAO;AACL,WAAK,SAAS,YAAY,KAAK,MAAM,eAAe,eAAe;AAAA,IACrE;AAAA,EACF;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,GAAG;AACd,SAAK,YAAY,gBAAgB,CAAC;AAClC,QAAI,KAAK,WAAW;AAClB,WAAK,SAAS,SAAS,KAAK,MAAM,eAAe,aAAa;AAAA,IAChE,OAAO;AACL,WAAK,SAAS,YAAY,KAAK,MAAM,eAAe,aAAa;AAAA,IACnE;AAAA,EACF;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,IAAI,GAAG;AACT,SAAK,OAAO,MAAM,QAAQ,QAAQ;AAClC,SAAK,SAAS,aAAa,KAAK,MAAM,eAAe,OAAO,KAAK,IAAI;AAAA,EACvE;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,uBAAuB,GAAG;AAC5B,SAAK,0BAA0B,uBAAuB,GAAG,CAAC;AAAA,EAC5D;AAAA,EACA,IAAI,yBAAyB;AAC3B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,IAAI,WAAW,gBAAc,KAAK,0BAA0B,UAAU,EAAE,KAAK,aAAa,EAAE,CAAC;AAAA,EACtG;AAAA,EACA,YAAY,QAAQ,OAAO,OAAO,UAAU,cAAc;AACxD,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,qBAAqB;AAC1B,SAAK,UAAU;AAAA,MACb,WAAW;AAAA,MACX,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,eAAe;AAAA,MACf,UAAU;AAAA,MACV,KAAK;AAAA,MACL,wBAAwB;AAAA,IAC1B;AACA,SAAK,YAAY,IAAI,aAAa,KAAK;AACvC,SAAK,UAAU,IAAI,aAAa,KAAK;AACrC,SAAK,cAAc,IAAI,aAAa,KAAK;AACzC,SAAK,iBAAiB,IAAI,aAAa,KAAK;AAC5C,SAAK,sBAAsB,IAAI,QAAQ;AACvC,SAAK,gBAAgB,KAAK,oBAAoB,aAAa;AAC3D,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,uBAAuB;AAC5B,SAAK,gBAAgB,CAAC;AACtB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,iBAAiB,CAAC;AACvB,SAAK,cAAc,CAAC;AACpB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AAExB,SAAK,YAAY,KAAK;AACtB,SAAK,UAAU,eAAe,OAAO,OAAO,KAAK,SAAS,YAAY,IAAI,KAAK;AAC/E,WAAO,KAAK,KAAK,OAAO,EAAE,QAAQ,cAAY;AAC5C,WAAK,QAAQ,IAAI,KAAK,QAAQ,QAAQ;AAAA,IACxC,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,SAAK,OAAO,kBAAkB,MAAM;AAElC,iBAAW,MAAM,KAAK,SAAS,SAAS,KAAK,MAAM,eAAe,SAAS,CAAC;AAAA,IAC9E,CAAC;AAAA,EACH;AAAA,EACA,eAAe;AACb,WAAO,KAAK,eAAe,WAAW,IAAI,IAAI,KAAK,eAAe,SAAS;AAAA,EAC7E;AAAA,EACA,QAAQ,WAAW;AACjB,UAAM,UAAU;AAAA,MACd;AAAA,MACA,OAAO;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,oBAAoB;AAAA,MACpB,sBAAsB;AAAA,IACxB;AACA,QAAI,UAAU,YAAY,MAAM;AAC9B,WAAK,eAAe,KAAK,OAAO;AAChC,WAAK,MAAM,MAAM,IAAI;AAAA,IACvB,OAAO;AACL,WAAK,YAAY,KAAK,OAAO;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,WAAW,WAAW;AACpB,QAAI,KAAK,eAAe,KAAK,OAAK,EAAE,cAAc,SAAS,GAAG;AAC5D,YAAM,OAAO,KAAK,eAAe,KAAK,OAAK,EAAE,cAAc,SAAS;AACpE,WAAK,eAAe,OAAO,KAAK,eAAe,QAAQ,IAAI,GAAG,CAAC;AAC/D,WAAK,MAAM,MAAM,IAAI;AAAA,IACvB,WAAW,KAAK,YAAY,KAAK,OAAK,EAAE,cAAc,SAAS,GAAG;AAChE,YAAM,OAAO,KAAK,YAAY,KAAK,OAAK,EAAE,cAAc,SAAS;AACjE,WAAK,YAAY,OAAO,KAAK,YAAY,QAAQ,IAAI,GAAG,CAAC;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,WAAW,WAAW,aAAa,YAAY;AAC7C,QAAI,UAAU,YAAY,MAAM;AAC9B,WAAK,MAAM,aAAa,UAAU;AAAA,IACpC;AAAA,EACF;AAAA,EACA,SAAS,WAAW;AAClB,UAAM,OAAO,KAAK,YAAY,KAAK,OAAK,EAAE,cAAc,SAAS;AACjE,QAAI,SAAS,QAAW;AACtB;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,YAAY,OAAO,KAAK,YAAY,QAAQ,IAAI,GAAG,CAAC;AACvE,SAAK,eAAe,KAAK,GAAG,KAAK;AACjC,SAAK,MAAM,MAAM,IAAI;AAAA,EACvB;AAAA,EACA,SAAS,MAAM;AACb,UAAM,OAAO,KAAK,eAAe,KAAK,OAAK,EAAE,cAAc,IAAI;AAC/D,QAAI,SAAS,QAAW;AACtB;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,eAAe,OAAO,KAAK,eAAe,QAAQ,IAAI,GAAG,CAAC;AAC7E,UAAM,QAAQ,UAAQ;AACpB,WAAK,QAAQ;AACb,WAAK,OAAO;AAAA,IACd,CAAC;AACD,SAAK,YAAY,KAAK,GAAG,KAAK;AAC9B,SAAK,MAAM,MAAM,IAAI;AAAA,EACvB;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,eAAe,IAAI,OAAK,EAAE,IAAI;AAAA,EAC5C;AAAA,EACA,oBAAoB,OAAO;AACzB,QAAI,MAAM,WAAW,KAAK,eAAe,QAAQ;AAC/C,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,MAAM,IAAI,OAAK,uBAAuB,GAAG,GAAG,CAAC;AACpE,UAAM,UAAU,iBAAiB,KAAK,MAAM,cAAc;AAC1D,QAAI,YAAY,OAAO;AACrB,aAAO;AAAA,IACT;AAEA,SAAK,eAAe,QAAQ,CAAC,MAAM,MAAM,KAAK,UAAU,OAAO,eAAe,CAAC,CAAC;AAChF,SAAK,MAAM,OAAO,IAAI;AACtB,WAAO;AAAA,EACT;AAAA,EACA,MAAM,aAAa,YAAY;AAC7B,SAAK,aAAa;AAElB,QAAI,gBAAgB,MAAM;AAExB,UAAI,KAAK,eAAe,MAAM,OAAK,EAAE,UAAU,UAAU,IAAI,GAAG;AAC9D,aAAK,eAAe,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,QAAQ,EAAE,UAAU,KAAK;AAAA,MAC1E;AAEA,WAAK,eAAe,QAAQ,CAAC,MAAM,MAAM;AACvC,aAAK,QAAQ,IAAI;AACjB,aAAK,UAAU,cAAc,KAAK,KAAK;AAAA,MACzC,CAAC;AAAA,IACH;AAEA,QAAI,eAAe,MAAM;AACvB,YAAM,eAAe,iBAAiB,KAAK,MAAM,KAAK,eAAe,IAAI,OAAK,EAAE,UAAU,IAAI,CAAC;AAC/F,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK,WACH;AACE,gBAAM,cAAc,MAAM,KAAK,eAAe;AAC9C,eAAK,eAAe,QAAQ,UAAQ;AAClC,iBAAK,OAAO,eAAe,KAAK,UAAU,OAAO;AACjD,iBAAK,UAAU,eAAe,IAAI;AAClC,iBAAK,UAAU,eAAe,IAAI;AAAA,UACpC,CAAC;AACD;AAAA,QACF;AAAA,QACF,KAAK,SACH;AACE,cAAI,cAAc;AAChB,iBAAK,eAAe,QAAQ,UAAQ;AAClC,mBAAK,OAAO,KAAK,UAAU;AAC3B,mBAAK,UAAU,eAAe,IAAI;AAClC,mBAAK,UAAU,eAAe,IAAI;AAAA,YACpC,CAAC;AAAA,UACH,OAAO;AACL,kBAAM,oBAAoB,KAAK,eAAe,OAAO,OAAK,EAAE,UAAU,SAAS,GAAG;AAElF,gBAAI,kBAAkB,WAAW,KAAK,KAAK,eAAe,SAAS,GAAG;AACpE,mBAAK,eAAe,QAAQ,CAAC,MAAM,MAAM;AACvC,qBAAK,OAAO,MAAM,IAAI,MAAM,KAAK,UAAU;AAC3C,qBAAK,UAAU,MAAM,IAAI,KAAK,UAAU,UAAU,eAAe,IAAI;AACrE,qBAAK,UAAU,MAAM,IAAI,OAAO,eAAe,IAAI;AAAA,cACrD,CAAC;AAAA,YACH,WAAW,kBAAkB,SAAS,GAAG;AAEvC,kBAAI,gBAAgB;AACpB,mBAAK,eAAe,QAAQ,UAAQ;AAClC,oBAAI,KAAK,UAAU,SAAS,KAAK;AAC/B,sBAAI,kBAAkB,OAAO;AAC3B,yBAAK,OAAO;AACZ,yBAAK,UAAU;AACf,yBAAK,UAAU;AACf,oCAAgB;AAAA,kBAClB,OAAO;AACL,yBAAK,OAAO;AACZ,yBAAK,UAAU;AACf,yBAAK,UAAU;AAAA,kBACjB;AAAA,gBACF,OAAO;AACL,uBAAK,OAAO,KAAK,UAAU;AAC3B,uBAAK,UAAU,eAAe,IAAI;AAClC,uBAAK,UAAU,eAAe,IAAI;AAAA,gBACpC;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AACA;AAAA,QACF;AAAA,MACJ;AAAA,IACF;AACA,SAAK,kBAAkB;AACvB,SAAK,MAAM,aAAa;AAAA,EAC1B;AAAA,EACA,oBAAoB;AAGlB,QAAI,KAAK,SAAS,WAAW;AAE3B,UAAI,KAAK,eAAe,WAAW,GAAG;AACpC,aAAK,eAAe,CAAC,EAAE,UAAU,aAAa,GAAG,GAAG,QAAQ,OAAO,KAAK;AAAA,MAC1E,OAAO;AAEL,cAAM,gBAAgB,KAAK,aAAa,IAAI,KAAK;AACjD,aAAK,eAAe,QAAQ,UAAQ;AAElC,cAAI,KAAK,SAAS,KAAK;AACrB,gBAAI,KAAK,eAAe,WAAW,GAAG;AACpC,mBAAK,UAAU,aAAa,GAAG,GAAG,QAAQ,OAAO,KAAK;AAAA,YACxD,OAAO;AACL,mBAAK,UAAU,aAAa,GAAG,GAAG,QAAQ,OAAO,KAAK;AAAA,YACxD;AAAA,UACF,OAAO;AACL,iBAAK,UAAU,aAAa,GAAG,GAAG,SAAS,KAAK,IAAI,OAAO,KAAK,OAAO,MAAM,aAAa,QAAQ,KAAK,YAAY,QAAQ,KAAK,YAAY,KAAK,MAAM,KAAK,YAAY,QAAQ,KAAK,YAAY,KAAK,IAAI;AAAA,UAC5M;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,WAAW,KAAK,SAAS,SAAS;AAGhC,WAAK,eAAe,QAAQ,UAAQ;AAElC,YAAI,KAAK,SAAS,KAAK;AACrB,cAAI,KAAK,eAAe,WAAW,GAAG;AACpC,iBAAK,UAAU,aAAa,GAAG,GAAG,QAAQ,OAAO,KAAK;AAAA,UACxD,OAAO;AACL,iBAAK,UAAU,aAAa,GAAG,GAAG,QAAQ,OAAO,KAAK;AAAA,UACxD;AAAA,QACF,OAAO;AAGL,cAAI,KAAK,eAAe,WAAW,GAAG;AACpC,iBAAK,UAAU,aAAa,GAAG,GAAG,QAAQ,OAAO,KAAK;AAAA,UACxD,OAAO;AAEL,iBAAK,UAAU,aAAa,GAAG,GAAG,GAAG,KAAK,IAAI,MAAM,KAAK,YAAY,QAAQ,KAAK,YAAY,KAAK,MAAM,KAAK,YAAY,QAAQ,KAAK,YAAY,KAAK,IAAI;AAAA,UAC9J;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,OAAO,WAAW;AAC5B,UAAM,YAAY,kBAAkB,KAAK;AAEzC,QAAI,KAAK,cAAc,iBAAiB,KAAK,YAAY,WAAW,KAAK,kBAAkB,MAAM,CAAC,KAAK,cAAc,KAAK,uBAAuB;AAE/I,UAAI,KAAK,kBAAkB,MAAM;AAC/B,eAAO,aAAa,KAAK,aAAa;AACtC,aAAK,gBAAgB;AACrB,aAAK,OAAO,YAAY,SAAS;AACjC,aAAK,aAAa;AAAA,MACpB,OAAO;AAEL,aAAK,gBAAgB,OAAO,WAAW,MAAM;AAC3C,eAAK,gBAAgB;AACrB,eAAK,OAAO,SAAS,SAAS;AAC9B,eAAK,aAAa;AAAA,QACpB,GAAG,KAAK,sBAAsB;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB,OAAO,aAAa,WAAW;AAC/C,QAAI,KAAK,aAAa,QAAQ,KAAK,mBAAmB,MAAM;AAC1D;AAAA,IACF;AACA,UAAM,WAAW,oBAAoB,OAAO,KAAK,SAAS;AAC1D,QAAI,aAAa,MAAM;AACrB;AAAA,IACF;AACA,SAAK,WAAW;AAChB,SAAK,aAAa,kBAAkB,KAAK;AACzC,UAAM,eAAe;AACrB,UAAM,gBAAgB;AACtB,SAAK,kBAAkB,aAAa,SAAS;AAC7C,SAAK,cAAc;AACnB,SAAK,KAAK;AACV,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,eAAe,OAAO,aAAa,WAAW;AAC5C,QAAI,KAAK,gBAAgB,CAAC,KAAK,aAAa,iBAAiB,MAAM,QAAQ,SAAS,GAAG;AACrF;AAAA,IACF;AACA,UAAM,eAAe;AACrB,UAAM,gBAAgB;AACtB,SAAK,aAAa,kBAAkB,KAAK;AACzC,QAAI,KAAK,eAAe,QAAQ,KAAK,aAAa,QAAQ,KAAK,mBAAmB,MAAM;AACtF;AAAA,IACF;AACA,SAAK,kBAAkB,aAAa,SAAS;AAC7C,SAAK,cAAc,KAAK,KAAK,SAAS,OAAO,YAAY,WAAW,KAAK,aAAa,KAAK,IAAI,CAAC,CAAC;AACjG,SAAK,cAAc,KAAK,KAAK,SAAS,OAAO,YAAY,YAAY,KAAK,aAAa,KAAK,IAAI,CAAC,CAAC;AAClG,SAAK,cAAc,KAAK,KAAK,SAAS,OAAO,YAAY,eAAe,KAAK,aAAa,KAAK,IAAI,CAAC,CAAC;AACrG,SAAK,OAAO,kBAAkB,MAAM;AAClC,WAAK,cAAc,KAAK,KAAK,SAAS,OAAO,YAAY,aAAa,KAAK,eAAe,KAAK,IAAI,CAAC,CAAC;AACrG,WAAK,cAAc,KAAK,KAAK,SAAS,OAAO,YAAY,aAAa,KAAK,eAAe,KAAK,IAAI,CAAC,CAAC;AAAA,IACvG,CAAC;AACD,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,kBAAkB,aAAa,WAAW;AACxC,SAAK,WAAW;AAAA,MACd;AAAA,MACA,mBAAmB;AAAA,MACnB,mBAAmB,oBAAoB,KAAK,OAAO,KAAK,SAAS,IAAI,KAAK,aAAa,IAAI,KAAK;AAAA,MAChG,6BAA6B;AAAA,MAC7B,mBAAmB,CAAC;AAAA,MACpB,kBAAkB,CAAC;AAAA,IACrB;AACA,SAAK,eAAe,QAAQ,UAAQ;AAClC,YAAM,eAAe;AAAA,QACnB;AAAA,QACA,kBAAkB,oBAAoB,KAAK,UAAU,OAAO,KAAK,SAAS;AAAA,QAC1E,oBAAoB,KAAK,SAAS,YAAY,KAAK,OAAO;AAAA;AAAA,MAC5D;AACA,UAAI,KAAK,QAAQ,aAAa;AAC5B,YAAI,KAAK,iBAAiB,MAAM;AAC9B,eAAK,SAAS,oBAAoB,CAAC,YAAY;AAAA,QACjD,OAAO;AACL,eAAK,SAAS,kBAAkB,QAAQ,YAAY;AAAA,QACtD;AAAA,MACF,WAAW,KAAK,QAAQ,aAAa;AACnC,YAAI,KAAK,iBAAiB,MAAM;AAC9B,cAAI,KAAK,SAAS,iBAAiB,WAAW,GAAG;AAC/C,iBAAK,SAAS,mBAAmB,CAAC,YAAY;AAAA,UAChD;AAAA,QACF,OAAO;AACL,eAAK,SAAS,iBAAiB,KAAK,YAAY;AAAA,QAClD;AAAA,MACF;AAAA,IACF,CAAC;AAID,QAAI,KAAK,gBAAgB,KAAK,SAAS,WAAW;AAChD,YAAM,qBAAqB,KAAK,SAAS,kBAAkB,CAAC;AAC5D,YAAM,oBAAoB,KAAK,SAAS,iBAAiB,CAAC;AAG1D,UAAI,mBAAmB,KAAK,SAAS,OAAO,kBAAkB,KAAK,SAAS,KAAK;AAC/E,cAAM,+BAA+B,KAAK,eAAe,OAAO,CAAC,OAAO,SAAS;AAC/E,cAAI,mBAAmB,SAAS,QAAQ,kBAAkB,SAAS,MAAM;AACvE,mBAAO,QAAQ,KAAK;AAAA,UACtB;AACA,iBAAO;AAAA,QACT,GAAG,CAAC;AACJ,aAAK,SAAS,8BAA8B,MAAM;AAAA,MACpD,OAAO;AAEL,aAAK,SAAS,8BAA8B,CAAC,GAAG,KAAK,SAAS,mBAAmB,GAAG,KAAK,SAAS,gBAAgB,EAAE,OAAO,CAAC,GAAG,MAAM,IAAI,EAAE,oBAAoB,CAAC;AAAA,MAClK;AAAA,IACF;AACA,QAAI,KAAK,SAAS,kBAAkB,WAAW,KAAK,KAAK,SAAS,iBAAiB,WAAW,GAAG;AAC/F;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,eAAe,QAAQ,UAAQ,KAAK,UAAU,WAAW,CAAC;AAC/D,SAAK,aAAa;AAClB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,eAAe;AACrB,UAAM,gBAAgB;AACtB,UAAM,YAAY,kBAAkB,KAAK;AACzC,QAAI,KAAK,kBAAkB,QAAQ,CAAC,iBAAiB,KAAK,YAAY,WAAW,KAAK,kBAAkB,GAAG;AACzG,aAAO,aAAa,KAAK,aAAa;AACtC,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,KAAK,eAAe,OAAO;AAC7B;AAAA,IACF;AACA,SAAK,WAAW,kBAAkB,KAAK;AACvC,QAAI,KAAK,aAAa,MAAM;AAC1B;AAAA,IACF;AACA,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,OAAO;AACL,QAAI,KAAK,sBAAsB;AAC7B,UAAI,KAAK,WAAW,MAAM,KAAK,SAAS,KAAK,KAAK,WAAW,MAAM,KAAK,SAAS,GAAG;AAClF,aAAK,OAAO,IAAI,MAAM;AACpB,eAAK,uBAAuB;AAC5B,eAAK,SAAS,SAAS,KAAK,MAAM,eAAe,aAAa;AAC9D,eAAK,mBAAmB,KAAK,SAAS;AACtC,eAAK,MAAM,aAAa;AACxB,eAAK,OAAO,SAAS,KAAK,SAAS,SAAS;AAAA,QAC9C,CAAC;AAAA,MACH,OAAO;AACL;AAAA,MACF;AAAA,IACF;AAEA,QAAI,SAAS,KAAK,cAAc,eAAe,KAAK,WAAW,IAAI,KAAK,SAAS,IAAI,KAAK,WAAW,IAAI,KAAK,SAAS;AAGvH,QAAI,KAAK,QAAQ,SAAS,KAAK,cAAc,cAAc;AACzD,eAAS,CAAC;AAAA,IACZ;AACA,UAAM,gBAAgB,KAAK,MAAM,SAAS,KAAK,UAAU,IAAI,KAAK;AAClE,QAAI,kBAAkB,KAAK,SAAS,mBAAmB;AACrD;AAAA,IACF;AACA,SAAK,SAAS,oBAAoB;AAElC,QAAI,cAAc,gCAAgC,KAAK,MAAM,KAAK,SAAS,mBAAmB,CAAC,eAAe,KAAK,SAAS,iBAAiB;AAC7I,QAAI,aAAa,gCAAgC,KAAK,MAAM,KAAK,SAAS,kBAAkB,eAAe,KAAK,SAAS,iBAAiB;AAE1I,QAAI,YAAY,WAAW,KAAK,WAAW,WAAW,GAAG;AAEvD,UAAI,KAAK,IAAI,YAAY,MAAM,MAAM,KAAK,IAAI,WAAW,MAAM,GAAG;AAAA,MAElE,WAAW,KAAK,IAAI,YAAY,MAAM,IAAI,KAAK,IAAI,WAAW,MAAM,GAAG;AACrE,qBAAa,gCAAgC,KAAK,MAAM,KAAK,SAAS,kBAAkB,gBAAgB,YAAY,QAAQ,KAAK,SAAS,iBAAiB;AAAA,MAC7J,OAAO;AACL,sBAAc,gCAAgC,KAAK,MAAM,KAAK,SAAS,mBAAmB,EAAE,gBAAgB,WAAW,SAAS,KAAK,SAAS,iBAAiB;AAAA,MACjK;AAAA,IACF,WAAW,YAAY,WAAW,GAAG;AAEnC,mBAAa,gCAAgC,KAAK,MAAM,KAAK,SAAS,kBAAkB,gBAAgB,YAAY,QAAQ,KAAK,SAAS,iBAAiB;AAAA,IAC7J,WAAW,WAAW,WAAW,GAAG;AAElC,oBAAc,gCAAgC,KAAK,MAAM,KAAK,SAAS,mBAAmB,EAAE,gBAAgB,WAAW,SAAS,KAAK,SAAS,iBAAiB;AAAA,IACjK;AACA,QAAI,KAAK,SAAS,WAAW;AAG3B,YAAM,MAAM,CAAC,GAAG,YAAY,MAAM,GAAG,WAAW,IAAI;AACpD,YAAM,eAAe,IAAI,KAAK,OAAK,EAAE,2BAA2B,GAAG;AAEnE,YAAM,cAAc,gBAAgB,IAAI,KAAK,OAAK,EAAE,2BAA2B,KAAK,EAAE,2BAA2B,EAAE,aAAa,KAAK,WAAW,EAAE,2BAA2B,EAAE,aAAa,KAAK,OAAO;AACxM,UAAI,aAAa;AACf,oBAAY,yBAAyB,KAAK,SAAS,8BAA8B,IAAI,OAAO,OAAK,MAAM,WAAW,EAAE,OAAO,CAAC,OAAO,MAAM,QAAQ,EAAE,wBAAwB,CAAC;AAAA,MAC9K;AAAA,IACF;AAEA,gBAAY,KAAK,QAAQ,UAAQ,eAAe,KAAK,MAAM,IAAI,CAAC;AAChE,eAAW,KAAK,QAAQ,UAAQ,eAAe,KAAK,MAAM,IAAI,CAAC;AAC/D,SAAK,kBAAkB;AACvB,SAAK,OAAO,YAAY,KAAK,SAAS,SAAS;AAAA,EACjD;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,OAAO;AACT,YAAM,eAAe;AACrB,YAAM,gBAAgB;AAAA,IACxB;AACA,QAAI,KAAK,eAAe,OAAO;AAC7B;AAAA,IACF;AACA,SAAK,eAAe,QAAQ,UAAQ,KAAK,UAAU,aAAa,CAAC;AACjE,WAAO,KAAK,cAAc,SAAS,GAAG;AACpC,YAAM,MAAM,KAAK,cAAc,IAAI;AACnC,UAAI,KAAK;AACP,YAAI;AAAA,MACN;AAAA,IACF;AAGA,SAAK,aAAa;AAElB,QAAI,KAAK,yBAAyB,OAAO;AACvC,WAAK,OAAO,OAAO,KAAK,SAAS,SAAS;AAAA,IAC5C;AACA,SAAK,SAAS,YAAY,KAAK,MAAM,eAAe,aAAa;AACjE,SAAK,mBAAmB;AACxB,SAAK,MAAM,aAAa;AACxB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AAEtB,SAAK,OAAO,kBAAkB,MAAM;AAClC,iBAAW,MAAM;AACf,aAAK,aAAa;AAClB,aAAK,WAAW;AAChB,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO,MAAM,WAAW;AACtB,UAAM,QAAQ,KAAK,oBAAoB;AACvC,QAAI,SAAS,SAAS;AACpB,WAAK,UAAU,KAAK;AAAA,QAClB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,WAAW,SAAS,OAAO;AACzB,WAAK,QAAQ,KAAK;AAAA,QAChB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,WAAW,SAAS,SAAS;AAC3B,WAAK,YAAY,KAAK;AAAA,QACpB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,WAAW,SAAS,YAAY;AAC9B,WAAK,eAAe,KAAK;AAAA,QACvB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,WAAW,SAAS,iBAAiB;AACnC,UAAI,KAAK,yBAAyB;AAChC,aAAK,OAAO,IAAI,MAAM,KAAK,wBAAwB,KAAK,KAAK,CAAC;AAAA,MAChE;AAAA,IACF,WAAW,SAAS,YAAY;AAE9B,WAAK,oBAAoB,KAAK;AAAA,QAC5B;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,aAAa,MAAM,SAAS,QAAQ;AAClC,UAAM,OAAO,KAAK,eAAe,KAAK,OAAK,EAAE,cAAc,IAAI;AAC/D,QAAI,SAAS,QAAW;AACtB;AAAA,IACF;AACA,UAAM,cAAc,WAAW,UAAU,IAAI;AAC7C,QAAI,CAAC,KAAK,oBAAoB;AAC5B,WAAK,qBAAqB,KAAK;AAC/B,WAAK,uBAAuB;AAAA,IAC9B;AACA,SAAK,OAAO;AACZ,UAAM,MAAM,KAAK,UAAU,KAAK,OAAK,EAAE,cAAc,MAAM,UAAU,GAAG,KAAK,QAAQ,WAAW,EAAE;AAClG,QAAI,KAAK;AACP,WAAK,SAAS,SAAS,IAAI,eAAe,2BAA2B;AAAA,IACvE;AACA,SAAK,WAAW,MAAM,OAAO,KAAK;AAAA,EACpC;AAAA,EACA,WAAW,MAAM;AACf,UAAM,OAAO,KAAK,eAAe,KAAK,OAAK,EAAE,cAAc,IAAI;AAC/D,QAAI,SAAS,QAAW;AACtB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,oBAAoB;AAC5B;AAAA,IACF;AACA,SAAK,OAAO,KAAK;AACjB,SAAK,qBAAqB;AAC1B,UAAM,MAAM,KAAK,UAAU,KAAK,OAAK,EAAE,cAAc,MAAM,UAAU,GAAG,KAAK,QAAQ,KAAK,oBAAoB,EAAE;AAChH,QAAI,KAAK;AACP,WAAK,SAAS,YAAY,IAAI,eAAe,2BAA2B;AAAA,IAC1E;AACA,SAAK,WAAW,MAAM,OAAO,KAAK;AAAA,EACpC;AAAA,EACA,oBAAoB,MAAM;AACxB,QAAI,SAAS,KAAK;AAChB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ,CAAC,IAAI,MAAM,KAAK;AAAA,EACtC;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAmB,kBAAqB,MAAM,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,+BAA+B,CAAC,CAAC;AAAA,IAC/O;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,MACxB,gBAAgB,SAAS,8BAA8B,IAAI,KAAK,UAAU;AACxE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,sBAAsB,CAAC;AAAA,QACrD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,QACrE;AAAA,MACF;AAAA,MACA,WAAW,SAAS,qBAAqB,IAAI,KAAK;AAChD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,QAC/D;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,eAAe;AAAA,QACf,UAAU;AAAA,QACV,KAAK;AAAA,QACL,wBAAwB;AAAA,QACxB,oBAAoB;AAAA,QACpB,iBAAiB;AAAA,MACnB;AAAA,MACA,SAAS;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,SAAS;AAAA,QACT,aAAa;AAAA,QACb,gBAAgB;AAAA,MAClB;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG,CAAC,QAAQ,aAAa,YAAY,KAAK,SAAS,mBAAmB,GAAG,cAAc,cAAc,SAAS,WAAW,aAAa,cAAc,WAAW,YAAY,GAAG,MAAM,GAAG,CAAC,QAAQ,aAAa,YAAY,KAAK,GAAG,mBAAmB,GAAG,WAAW,aAAa,cAAc,WAAW,UAAU,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,8BAA8B,GAAG,CAAC,GAAG,oBAAoB,2BAA2B,0BAA0B,GAAG,CAAC,GAAG,sBAAsB,CAAC;AAAA,MAC9iB,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AACjB,UAAG,WAAW,GAAG,uCAAuC,GAAG,GAAG,eAAe,CAAC;AAAA,QAChF;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,cAAc;AAAA,QAC7C;AAAA,MACF;AAAA,MACA,cAAc,CAAI,SAAY,MAAS,kBAAkB,mCAAmC;AAAA,MAC5F,QAAQ,CAAC,whFAAwhF;AAAA,MACjiF,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqDV,eAAe,oBAAkB;AAAA,MACjC,QAAQ,CAAC,ikEAAikE;AAAA,IAC5kE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,IAAI,MAAM,GAAG;AACX,SAAK,SAAS,uBAAuB,GAAG,IAAI;AAC5C,SAAK,MAAM,WAAW,MAAM,MAAM,KAAK;AAAA,EACzC;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,GAAG;AACV,SAAK,QAAQ,uBAAuB,GAAG,GAAG;AAC1C,SAAK,MAAM,WAAW,MAAM,OAAO,IAAI;AAAA,EACzC;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,GAAG;AACb,SAAK,WAAW,uBAAuB,GAAG,IAAI;AAC9C,SAAK,MAAM,WAAW,MAAM,OAAO,IAAI;AAAA,EACzC;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,GAAG;AACb,SAAK,WAAW,uBAAuB,GAAG,IAAI;AAC9C,SAAK,MAAM,WAAW,MAAM,OAAO,IAAI;AAAA,EACzC;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,GAAG;AACd,SAAK,YAAY,gBAAgB,CAAC;AAClC,SAAK,MAAM,WAAW,MAAM,OAAO,IAAI;AAAA,EACzC;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,GAAG;AACb,SAAK,WAAW,gBAAgB,CAAC;AACjC,QAAI,KAAK,UAAU;AACjB,WAAK,MAAM,SAAS,IAAI;AACxB,WAAK,SAAS,YAAY,KAAK,MAAM,eAAe,WAAW;AAAA,IACjE,OAAO;AACL,WAAK,MAAM,SAAS,IAAI;AACxB,WAAK,SAAS,SAAS,KAAK,MAAM,eAAe,WAAW;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,QAAQ,UAAU,OAAO,OAAO;AAC1C,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,gBAAgB,CAAC;AACtB,SAAK,SAAS,SAAS,KAAK,MAAM,eAAe,eAAe;AAAA,EAClE;AAAA,EACA,WAAW;AACT,SAAK,MAAM,QAAQ,IAAI;AACvB,SAAK,OAAO,kBAAkB,MAAM;AAClC,WAAK,qBAAqB,KAAK,SAAS,OAAO,KAAK,MAAM,eAAe,iBAAiB,WAAS;AAEjG,YAAI,MAAM,iBAAiB,cAAc;AACvC,eAAK,MAAM,OAAO,iBAAiB,EAAE;AAAA,QACvC;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,UAAM,eAAe,KAAK,SAAS,cAAc,KAAK;AACtD,SAAK,SAAS,SAAS,cAAc,YAAY;AACjD,SAAK,wBAAwB,KAAK,MAAM,UAAU,UAAU,MAAM;AAChE,WAAK,SAAS,SAAS,KAAK,MAAM,eAAe,YAAY,UAAU;AACvE,WAAK,SAAS,YAAY,KAAK,MAAM,eAAe,YAAY;AAAA,IAClE,CAAC;AACD,SAAK,sBAAsB,KAAK,MAAM,QAAQ,UAAU,MAAM;AAC5D,WAAK,SAAS,YAAY,KAAK,MAAM,eAAe,UAAU;AAC9D,WAAK,SAAS,YAAY,KAAK,MAAM,eAAe,YAAY;AAAA,IAClE,CAAC;AAAA,EACH;AAAA,EACA,cAAc,OAAO;AACnB,SAAK,SAAS,SAAS,KAAK,MAAM,eAAe,SAAS,KAAK;AAAA,EACjE;AAAA,EACA,aAAa,MAAM,QAAQ,OAAO,OAAO,OAAO;AAE9C,SAAK,SAAS,SAAS,KAAK,MAAM,eAAe,aAAa,IAAI;AAClE,SAAK,SAAS,SAAS,KAAK,MAAM,eAAe,eAAe,MAAM;AACtE,SAAK,SAAS,SAAS,KAAK,MAAM,eAAe,cAAc,KAAK;AACpE,QAAI,UAAU,MAAM;AAClB,WAAK,SAAS,SAAS,KAAK,MAAM,eAAe,QAAQ;AAAA,IAC3D,OAAO;AACL,WAAK,SAAS,YAAY,KAAK,MAAM,eAAe,QAAQ;AAAA,IAC9D;AACA,QAAI,UAAU,MAAM;AAClB,WAAK,SAAS,SAAS,KAAK,MAAM,eAAe,QAAQ;AAAA,IAC3D,OAAO;AACL,WAAK,SAAS,YAAY,KAAK,MAAM,eAAe,QAAQ;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,aAAa;AACX,SAAK,OAAO,kBAAkB,MAAM;AAClC,WAAK,cAAc,KAAK,KAAK,SAAS,OAAO,KAAK,MAAM,eAAe,eAAe,MAAM,KAAK,CAAC;AAClG,WAAK,cAAc,KAAK,KAAK,SAAS,OAAO,KAAK,MAAM,eAAe,aAAa,MAAM,KAAK,CAAC;AAAA,IAClG,CAAC;AAAA,EACH;AAAA,EACA,eAAe;AACb,WAAO,KAAK,cAAc,SAAS,GAAG;AACpC,YAAM,MAAM,KAAK,cAAc,IAAI;AACnC,UAAI,KAAK;AACP,YAAI;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa;AAClB,QAAI,KAAK,oBAAoB;AAC3B,WAAK,mBAAmB;AAAA,IAC1B;AACA,SAAK,uBAAuB,YAAY;AACxC,SAAK,qBAAqB,YAAY;AACtC,SAAK,MAAM,WAAW,IAAI;AAAA,EAC5B;AAAA,EACA,SAAS,UAAU,GAAG,SAAS,SAAS;AACtC,SAAK,MAAM,aAAa,MAAM,SAAS,MAAM;AAAA,EAC/C;AAAA,EACA,SAAS;AACP,SAAK,MAAM,WAAW,IAAI;AAAA,EAC5B;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAuB,kBAAqB,MAAM,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,cAAc,GAAM,kBAAqB,UAAU,CAAC;AAAA,IACrL;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACxD,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iCAAN,MAAM,gCAA+B;AAAA,EACnC,YAAY,WAAW,YAAY,WAAW;AAC5C,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,WAAW;AACT,SAAK,UAAU,SAAS,KAAK,UAAU,0BAA0B,KAAK,WAAW,KAAK,UAAU;AAAA,EAClG;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,eAAe,KAAK,UAAU,0BAA0B,KAAK,WAAW,KAAK,UAAU;AAAA,EACxG;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,uCAAuC,GAAG;AAC7D,aAAO,KAAK,KAAK,iCAAmC,kBAAkB,gBAAgB,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,oBAAoB,CAAC;AAAA,IAC1K;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC;AAAA,IACjD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,sCAAN,MAAM,qCAAoC;AAAA,EACxC,YAAY,WAAW,YAAY,WAAW;AAC5C,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,WAAW;AACT,SAAK,UAAU,SAAS,KAAK,UAAU,+BAA+B,KAAK,WAAW,KAAK,UAAU;AAAA,EACvG;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,eAAe,KAAK,UAAU,+BAA+B,KAAK,WAAW,KAAK,UAAU;AAAA,EAC7G;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,4CAA4C,GAAG;AAClE,aAAO,KAAK,KAAK,sCAAwC,kBAAkB,gBAAgB,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,oBAAoB,CAAC;AAAA,IAC/K;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gCAAgC,EAAE,CAAC;AAAA,IACtD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qCAAqC,CAAC;AAAA,IAC5G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACL,OAAO;AACvB,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,gBAAgB,oBAAoB,sBAAsB,gCAAgC,qCAAqC,mCAAmC;AAAA,MACjL,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,gBAAgB,oBAAoB,sBAAsB,gCAAgC,mCAAmC;AAAA,IACzI,CAAC;AAAA,EACH;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,cAAc,CAAC,gBAAgB,oBAAoB,sBAAsB,gCAAgC,qCAAqC,mCAAmC;AAAA,MACjL,SAAS,CAAC,gBAAgB,oBAAoB,sBAAsB,gCAAgC,mCAAmC;AAAA,IACzI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["total"]}