/* Request Sidebar Styles */
.request-sidebar {
  height: 100%;
  transition: var(--transition-default);
}

/* Active state styles */
.active-tab {
  background-color: var(--secondary-purple);
}

/* Dark mode support */
:host-context(.dark-mode) .active-tab {
  background-color: var(--active-tab-bg);
}

/* Hover effects */
.group:hover {
  background-color: var(--primary-purple);
  color: white;
}

/* Transition effects */
.transition-all {
  transition: all 0.3s ease;
}
:host-context(:not(.dark)) .group:hover i,
:host-context(:not(.dark)) .group:hover span {
  color: white !important;
}

/* Scrollbar styling for dark mode */
:host-context(.dark-mode) .overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

:host-context(.dark-mode) .overflow-y-auto::-webkit-scrollbar-track {
  background: #3a3a45;
}

:host-context(.dark-mode) .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

:host-context(.dark-mode) .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #666;
}
