import {
  Component,
  ViewChild,
  Element<PERSON>ef,
  OnInit,
  inject,
  AfterViewInit,
  OnDestroy,
  HostListener,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule, SlicePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { marked, use } from 'marked';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import {
  AgentDefinitionServiceProxy,
  WorkspaceServiceProxy,
  AgentChatServiceProxy,
  AgentChatRequestDto,
  WorkspaceChatRequestDto,
  AgentChatHistoryDto,
  AgentChatConversationDto,
  AgentChatResponseDto,
  PluginResponseDto,
  FileServiceProxy,
  FileParameter,
  PluginServiceProxy,
  EmailServiceProxy,
  SendEmailDto,
  UserAccountServiceProxy,
  AgentEvaluationServiceProxy,
  AgentFeedbackDto,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { AuthService } from '../../../shared/services/auth.service';
import { ChatService } from '../../services/chat.service';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzSliderModule } from 'ng-zorro-antd/slider';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzRateModule } from 'ng-zorro-antd/rate';
import { MarkdownModule } from 'ngx-markdown';
import { SourceReferencesComponent } from '../../components/@rightSideComponents/source-references/source-references.component';
import { AgentSidebarComponent } from '../../components/@rightSideComponents/agent-sidebar/agent-sidebar.component';
import { PluginsSidebarComponent } from '../../components/@rightSideComponents/plugins-sidebar/plugins-sidebar.component';
import { AngularSplitModule } from 'angular-split';
import { SaveToNotesDialogComponent } from '../../components/save-to-notes-dialog/save-to-notes-dialog.component';
import { ChatListService } from '../../services/chat-list.service';
import { DateTime } from 'luxon';
import { SqlConnectionDialogComponent } from '../../components/sql-connection-dialog/sql-connection-dialog.component';
import { BlogShareDialogComponent } from '../../components/blog-share-dialog/blog-share-dialog.component';
import { SidebarService } from '../../services/sidebar.service';
import { wrongSortOrder } from 'ng-zorro-antd/core/time';

@Component({
  selector: 'app-agent-chat',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ServiceProxyModule,
    MarkdownModule,
    NzModalModule,
    NzSliderModule,
    NzInputModule,
    NzButtonModule,
    NzIconModule,
    NzRateModule,
    SourceReferencesComponent,
    AgentSidebarComponent,
    PluginsSidebarComponent,
    AngularSplitModule,
    RouterModule, // Added RouterModule for routerLink directive
    SaveToNotesDialogComponent,
  ],
  providers: [NzModalService],
  templateUrl: './agent-chat.component.html',
  styleUrl: './agent-chat.component.css',
})
export class AgentChatComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('chatInput') chatInput!: ElementRef;
  @ViewChild('chatContainer') chatContainer!: ElementRef;
  @ViewChild('fileInput') fileInput!: ElementRef;
  // User message input
  userInput: AgentChatRequestDto = new AgentChatRequestDto();

  // Chat data
  conversations: any[] = [];
  currentConversation: AgentChatConversationDto = new AgentChatConversationDto({
    agentName: '',
    histories: [],
  });
  isMessageLoading = false;
  previousResponse = '';

  // User data for history selection
  allUsers: any[] = [];
  selectedUserEmail: string = '';

  // Search results and source references
  searchResults: any[] = [];
  currentSourceName = '';

  // Response navigation tracking
  currentResponseIndexes: { [key: string]: number } = {};
  // Streaming properties (similar to hero component)
  isStreamingActive = false;
  currentStreamingMessageId?: string;

  // Workspaces and agents
  workspaces: any = [];
  selectedWorkspace = '';
  workspaceAgents: any = [];
  // Pagination state
  currentPage: number = 1;
  pageSize: number = 5;
  hasMoreData: boolean = true;
  isLoadingMore: boolean = false;
  allHistoriesLoaded: boolean = false;
  totalHistoryCount: number = 0;

  // UI state
  showScrollButton: boolean = false;
  selectedAgent = '';
  isAgentSidebarOpen = false;
  agentSidebarTitle = 'Agent Tools';
  hasNoHistories = true;

  // Plugin sidebar state
  isPluginSidebarOpen = false;

  // Splitter properties
  mainContentSplitSize = 100;
  rightSidebarSplitSize = 0;
  rightSidebarWidth = 350;
  isDragging = false;
  showSearchResultsSidebar = false;

  isWorkspaceChat = false;
  isAgentChat = false;

  totalAgentsInWorkspace: number = 0;

  // Save to notes dialog
  showSaveToNotesDialog = false;
  saveToNotesPrompt = '';
  saveToNotesResponse = '';

  // Message subscription
  private messageSubscription?: Subscription;

  // Document upload properties
  uploadedFiles: Array<{
    id: string;
    name: string;
    file: File;
    uploadedFileName?: string;
    isUploading?: boolean;
    uploadError?: string;
  }> = [];
  isFileUploading = false;

  // Feedback properties
  showFeedbackModal = false;
  feedbackScore: number = 5;
  submittingFeedback = false;
  currentFeedbackMessage: any = null;
  isFeedbackPositive = true;

  // Slider marks for feedback rating
  sliderMarks = {
    1: '1',
    5: '5',
    10: '10'
  };

  // Inject services
  constructor(
    private modalService: NzModalService,
    private agentChatService: AgentChatServiceProxy,
    private chatService: ChatService,
    private workspaceService: WorkspaceServiceProxy,
    private agentDefinitionService: AgentDefinitionServiceProxy,
    private pluginServices: PluginServiceProxy,
    private cdr: ChangeDetectorRef,
    private chatListService: ChatListService,
    private fileService: FileServiceProxy,
    private emailService: EmailServiceProxy,
    private router: Router,
    private route: ActivatedRoute,
    public auth: AuthService,
    private message: NzMessageService,
    private sidebarService: SidebarService,
    private _userService: UserAccountServiceProxy,
    private agentEvaluationService: AgentEvaluationServiceProxy
  ) {
    // Configure marked options
    marked.setOptions({
      breaks: true,
      gfm: true,
    });
  }
  // View-to-agents mapping for better maintainability
  private readonly viewToAgentsMapping: { [key: string]: string[] } = {
    'sqlView': ['SqlQueryAgent'],
    'emailView': ['EmailContentGenerator', 'EmailGeneratorAgent'],
    'blogView': ['BlogGeneratorAgent'],
  };

  /**
   * Determines the view type based on agent name
   * @param agentName The name of the agent
   * @returns The view type (sqlView, emailView, blogView, or default)
   */
  getViewTypeByAgent(agentName: string | undefined): string {
    if (!agentName) return 'default';

    // Find which view type contains this agent
    for (const [viewType, agents] of Object.entries(this.viewToAgentsMapping)) {
      if (agents.includes(agentName)) {
        return viewType;
      }
    }

    return 'default';
  }

  /**
   * Checks if a response is a blog post
   * @param response The response text or object
   * @returns True if the response is a blog post
   */
  isBlogContent(response: string | any): boolean {
    if (!response) return false;

    // If response is an object with responseType property
    if (typeof response === 'object' && response.responseType === 'blogView') {
      return true;
    }

    // If it's a string, check if it has blog-like structure (headings, sections)
    if (typeof response === 'string') {
      // Look for markdown headings (# or ## or ###)
      const headingRegex = /^#+\s+.+$/m;
      return headingRegex.test(response);
    }

    return false;
  }

  /**
   * Extracts the title from a blog post
   * @param response The blog post content
   * @returns The blog title or a default title
   */
  extractBlogTitle(response: string): string {
    if (!response) return 'Blog Post';

    // Look for the first heading (# Heading)
    const titleRegex = /^#\s+(.+)$/m;
    const match = response.match(titleRegex);

    if (match && match[1]) {
      return match[1].trim();
    }

    return 'Blog Post';
  }

  /**
   * Copies blog content to clipboard
   * @param blogContent The blog content to copy
   */
  copyBlogContent(blogContent: string): void {
    if (!blogContent) return;

    navigator.clipboard.writeText(blogContent);
    this.message.success('Blog content copied to clipboard');
  }

  /**
   * Extracts email subject from a response
   * @param response The email response content
   * @returns The email subject or a default subject
   */
  extractEmailSubject(response: string): string {
    if (!response) return 'No Subject';

    // First, check if response is an object or string
    let textContent = typeof response === 'string' ? response : JSON.stringify(response);

    // Look for the Subject line with different formats
    // Format 1: **Subject:** Text
    const subjectRegex1 = /\*\*Subject:?\*\*\s*([^\n]+)/i;
    // Format 2: Subject: Text
    const subjectRegex2 = /Subject:\s*([^\n]+)/i;

    let match = textContent.match(subjectRegex1) || textContent.match(subjectRegex2);

    if (match && match[1]) {
      return match[1].trim();
    }

    return 'No Subject';
  }

  /**
   * Extracts email recipients (To field) from a response
   * @param response The email response content
   * @returns The email recipients or empty string
   */
  extractEmailTo(response: string): string {
    if (!response) return '';

    // First, check if response is an object or string
    let textContent = typeof response === 'string' ? response : JSON.stringify(response);

    // Look for the To line with different formats
    // Format 1: **To:** <EMAIL>
    const toRegex1 = /\*\*To:?\*\*\s*([^\n]+)/i;
    // Format 2: To: <EMAIL>
    const toRegex2 = /To:\s*([^\n]+)/i;

    let match = textContent.match(toRegex1) || textContent.match(toRegex2);

    if (match && match[1]) {
      return match[1].trim();
    }

    return '';
  }

  /**
   * Extracts email CC recipients from a response
   * @param response The email response content
   * @returns The CC recipients or empty string
   */
  extractEmailCc(response: string): string {
    if (!response) return '';

    // First, check if response is an object or string
    let textContent = typeof response === 'string' ? response : JSON.stringify(response);

    // Look for the CC line with different formats
    // Format 1: **Cc:** <EMAIL>
    const ccRegex1 = /\*\*Cc:?\*\*\s*([^\n]+)/i;
    // Format 2: Cc: <EMAIL>
    const ccRegex2 = /Cc:\s*([^\n]+)/i;
    // Format 3: CC: <EMAIL>
    const ccRegex3 = /CC:\s*([^\n]+)/i;

    let match = textContent.match(ccRegex1) || textContent.match(ccRegex2) || textContent.match(ccRegex3);

    if (match && match[1]) {
      return match[1].trim();
    }

    return '';
  }

  /**
   * Extracts email body content from a response
   * @param response The email response content
   * @returns The email body or the full response if body can't be extracted
   */
  extractEmailBody(response: string): string {
    if (!response) return '';

    // First, check if response is an object or string
    let textContent = typeof response === 'string' ? response : JSON.stringify(response);

    // Step 1: If there's a markdown email structure with To and CC,
    // find the actual body (starting after CC or To line and ending before signature)
    // This more specific approach should handle most AI-generated email formats

    // Remove the intro text if present
    textContent = textContent.replace(/^Hello!.*?email:\s*\n+/i, '');

    // Look for specific email structure with To: and Cc: lines
    let bodyText = '';

    // First find the To: line and CC: line if they exist
    const toLine = textContent.match(/\*\*To:?\*\*\s*[^\n]+|To:\s*[^\n]+/i);
    const ccLine = textContent.match(/\*\*Cc:?\*\*\s*[^\n]+|Cc:\s*[^\n]+/i);

    if (toLine || ccLine) {
      // Find the last header line (either To or CC, whichever comes last)
      const lastHeader = ccLine ? ccLine[0] : (toLine ? toLine[0] : '');
      const lastHeaderIndex = textContent.indexOf(lastHeader) + lastHeader.length;

      // Extract everything after the last header
      let afterHeaders = textContent.substring(lastHeaderIndex).trim();

      // If there's a blank line after headers, trim everything before it
      if (afterHeaders.startsWith('\n\n')) {
        afterHeaders = afterHeaders.substring(2).trim();
      } else if (afterHeaders.startsWith('\n')) {
        afterHeaders = afterHeaders.substring(1).trim();
      }

      // Find where the signature starts (if any)
      const signatureMatch = afterHeaders.match(/\n(?:Best,|Regards,|Sincerely,|Thanks|Thank you|Cheers|Yours truly)/i);

      if (signatureMatch) {
        // Extract everything before the signature
        bodyText = afterHeaders.substring(0, signatureMatch.index).trim();
      } else {
        // If no signature found, check for markdown closure
        const markdownEndMatch = afterHeaders.match(/\n---/i);
        if (markdownEndMatch) {
          bodyText = afterHeaders.substring(0, markdownEndMatch.index).trim();
        } else {
          bodyText = afterHeaders;
        }
      }

      return bodyText;
    }

    // If the specific extraction failed, try the standard markdown section approach
    const markdownSectionRegex = /---\s*([\s\S]+?)(?:---|\Z)/;
    const sectionMatch = textContent.match(markdownSectionRegex);

    if (sectionMatch && sectionMatch[1]) {
      // Further clean up by removing header lines
      const cleanedBody = sectionMatch[1]
        .replace(/\*\*(?:Subject|To|Cc|CC):?\*\*\s*[^\n]+\n*/gi, '')
        .replace(/(?:Subject|To|Cc|CC):\s*[^\n]+\n*/gi, '')
        .trim();

      return cleanedBody;
    }

    // If all extraction methods fail, return a basic cleaned version of the response
    return textContent
      .replace(/^Hello!.*?email:\s*\n+---\s*/i, '')
      .replace(/---\s*\n+Feel free to modify.*$/i, '')
      .replace(/\*\*(?:Subject|To|Cc|CC):?\*\*\s*[^\n]+\n*/gi, '')
      .replace(/(?:Subject|To|Cc|CC):\s*[^\n]+\n*/gi, '')
      .trim();
  }

  /**
   * Sends the email
   * @param subject Email subject
   * @param to Email recipients
   * @param cc Email CC recipients
   * @param body Email body
   */
  sendEmail(subject: string, to: string, cc: string, body: string): void {
    if (!subject || !to || !body) {
      this.message.error('Please fill in all required fields');
      return;
    }

    // Convert plain text to HTML if it doesn't already have HTML formatting
    let htmlBody = body;
    if (!body.includes('<')) {
      // Convert newlines to <br> tags for proper HTML formatting
      htmlBody = body.replace(/\n/g, '<br>');
    }

    // Create a SendEmailDto object
    const emailDto = new SendEmailDto({
      subject: subject,
      to: to,
      cc: cc ? cc.split(',').map(email => email.trim()) : [], // Convert comma-separated string to array
      htmlBody: htmlBody // Send as HTML body to preserve formatting
    });

    this.message.loading('Sending email...');

    // Call the email service to send the email
    this.emailService.sendCustomEmail(emailDto).subscribe(
      () => {
        // Show success message
        this.message.success('Email sent successfully');
      },
      (error: any) => {
        console.error('Error sending email:', error);
        this.message.error('Failed to send email: ' + (error.message || 'Please try again later'));
      }
    );
  }

  /**
   * Copies all email content to clipboard in a formatted way
   * @param subject Email subject
   * @param to Email recipients
   * @param cc Email CC recipients
   * @param body Email body
   */
  copyEmailContent(subject: string, to: string, cc: string, body: string): void {
    const emailContent = `Subject: ${subject}
To: ${to}
${cc ? `CC: ${cc}` : ''}

${body}`;

    navigator.clipboard.writeText(emailContent);
    this.message.success('Email content copied to clipboard');
  }

  /**
   * Extracts SQL content from a response
   * @param response The response text
   * @returns The SQL code
   */
  extractSqlContent(response: string): string {
    if (!response) return '';

    // Look for SQL code blocks in markdown format
    const sqlRegex = /```sql\s*([\s\S]*?)\s*```/i;
    const match = response.match(sqlRegex);

    if (match && match[1]) {
      return match[1].trim();
    }

    return '';
  }

  /**
   * Checks if a response contains SQL code
   * @param response The response text
   * @returns True if the response contains SQL code
   */
  hasSqlContent(response: string): boolean {
    if (!response) return false;

    // Check if the response contains SQL code blocks
    const sqlRegex = /```sql\s*([\s\S]*?)\s*```/i;
    const match = response.match(sqlRegex);

    return !!(match && match[1] && match[1].trim());
  }

  /**
   * Extracts non-SQL content from a response
   * @param response The response text
   * @returns The response without SQL code blocks
   */
  extractNonSqlContent(response: string): string {
    if (!response) return '';

    // Replace SQL code blocks with a placeholder
    return response.replace(/```sql\s*([\s\S]*?)\s*```/gi, '');
  }

  /**
   * Copies SQL content to clipboard
   * @param sqlContent The SQL content to copy
   */
  copySqlContent(sqlContent: string): void {
    if (!sqlContent) return;

    navigator.clipboard.writeText(sqlContent);
    this.message.success('SQL query copied to clipboard');
  }

  /**
   * Opens the SQL connection dialog when the "Run SQL" button is clicked
   * @param sqlContent The SQL content to show in the dialog
   */
  openSqlConnectionDialog(sqlContent: string): void {
    if (!sqlContent) {
      this.message.error('No SQL content to execute');
      return;
    }

    // Create a modal with our SQL connection dialog component
    const modalRef = this.modalService.create({
      nzTitle: '',
      nzContent: SqlConnectionDialogComponent,
      nzFooter: null,
      nzWidth: '600px',
      nzClassName: 'sql-connection-modal',
      nzMaskClosable: false,
      nzData: {
        sql: sqlContent // Pass the SQL content to the dialog
      }
    });
  }

  /**
   * Opens the blog share dialog when the "Share" button is clicked
   * @param blogContent The blog content to share
   */
  openBlogShareDialog(blogContent: string): void {
    if (!blogContent) {
      this.message.error('No blog content to share');
      return;
    }

    // Create a modal with our blog share dialog component
    const modalRef = this.modalService.create({
      nzTitle: '',
      nzContent: BlogShareDialogComponent,
      nzFooter: null,
      nzWidth: '800px',
      nzClassName: 'blog-share-modal',
      nzMaskClosable: false,
      nzData: {
        blogContent: blogContent // Pass the blog content to the dialog
      }
    });

    // Handle the result when the dialog is closed
    modalRef.afterClose.subscribe((result) => {
      if (result) {
        console.log('Blog shared successfully:', result);
        // The success message is already shown in the dialog component
      }
    });
  }
  /**
   * Opens the save to notes dialog when the "Save to Notes" button is clicked
   * @param prompt The user's prompt/question
   * @param response The AI response to save
   */
  openSaveToNotesDialog(prompt: string, response: string): void {
    if (!response) {
      this.message.error('No response content to save');
      return;
    }

    this.saveToNotesPrompt = prompt || '';
    this.saveToNotesResponse = response;
    this.showSaveToNotesDialog = true;
  }

  /**
   * Handle save to notes dialog close
   */
  onSaveToNotesDialogClose(): void {
    this.showSaveToNotesDialog = false;
    this.saveToNotesPrompt = '';
    this.saveToNotesResponse = '';
  }

  /**
   * Handle successful note save
   */
  onNoteSaveComplete(result: any): void {
    console.log('Note saved successfully:', result);
    this.message.success('Response saved to My Notes successfully!');
    this.onSaveToNotesDialogClose();
  }

  ngOnInit(): void {
    // Initialize component
    this.loadSavedRightSidebarWidth();
    // this.loadWorkspaces();
    // Set up streaming message subscription
    this.setupStreamingSubscription();

    // Get chat type and name from route parameters
    this.route.paramMap.subscribe((params) => {
      const chatType = params.get('type');
      const name = params.get('name');
      if (chatType === 'agent' && name) {
        this.selectedAgent = name;
        this.selectedWorkspace = ''
        this.userInput.agentName = name;
        this.loadAgentChatHistories(name);
        console.log('Agent name:', name);
        this.isAgentChat = true;
        this.isWorkspaceChat = false;

        // this.TotalAgentInWorkspace(name);
        this.totalPluginInAgent()
        this.isAgentSidebarOpen = false;
        this.isAgentSidebarOpen = false;
      } else if (chatType === 'workspace' && name) {
        this.totalAgentInWorkspace(name);
        this.isPluginSidebarOpen = false;

        this.loadworkspacesChatHistories(name);
        this.selectedWorkspace = name;
        this.selectedAgent = ''
        this.isWorkspaceChat = true;
        this.isAgentChat = false;
        console.log('Workspace name:', name);
      }
    });

    // Update the current chatting agent or workspace
    this.updateCurrentChat();
    this.LoadAllUser()
  }
  /**
   * Gets the current user's email address
   * @returns The user's email or empty string if not found
   */
  getUserEmail(): string {
    const user = this.auth.getUser();
    if (!user) return '';

    // Try different possible claim names for the email
    return user.email ||
      user['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'] ||
      user.preferred_username ||
      '';
  }

  /**
   * Loads all users from the system
   */
  LoadAllUser() {
    this._userService.getAllUsers().subscribe((res) => {
      console.log('All users loaded:', res);
      this.allUsers = res;
      // If users are loaded successfully, select the current user by default
      if (this.allUsers && this.allUsers.length > 0) {
        // Try to select the current logged-in user by default
        const currentUserEmail = this.getUserEmail();
        if (currentUserEmail) {
          this.selectedUserEmail = currentUserEmail;
        } else {
          // Or select the first user as default
          this.selectedUserEmail = this.allUsers[0].email;
        }
      }
    });
  }
  /**
   * Loads chat history for a specific user, agent, and/or workspace
   * @param userEmail Optional email of the user whose history to load. If not provided, uses selectedUserEmail
   */
  loadHistoryOfAgentAndWorkspace(userEmail?: string) {
    // Show loading state
    this.isMessageLoading = true;
    this.message.loading('Loading chat history...', { nzDuration: 2500 });

    // If no specific user email is provided, use the selected user
    if (!userEmail && this.selectedUserEmail) {
      userEmail = this.selectedUserEmail;
    }

    // We can pass either agent name or workspace name based on current mode
    const agentName = this.isAgentChat ? this.selectedAgent : undefined;
    const workspaceName = this.isWorkspaceChat ? this.selectedWorkspace : undefined;

    // Reset pagination when manually loading history
    this.currentPage = 1;
    this.hasMoreData = true;
    this.allHistoriesLoaded = false;

    console.log(userEmail, agentName, workspaceName, this.currentPage, this.pageSize)
    this.agentChatService.userHistoryPaginated(
      userEmail,
      agentName,
      workspaceName,
      this.currentPage,
      this.pageSize
    ).subscribe({
      next: (res: any) => {
        // Clear loading state
        this.isMessageLoading = false;

        if (res) {
          console.log('User history loaded:', res);
          // Process the history data as needed
          this.totalHistoryCount = res.totalCount || 0;
          this.hasMoreData = res.hasMore || false;

          if (res.histories && res.histories.length > 0) {
            // Update histories in currentConversation
            if (this.currentConversation) {
              // Backend now returns data in DESC order (newest first), so we need to reverse it
              // to display oldest first, newest last (for proper chat flow)
              const sortedHistories = res.histories.sort((a: any, b: any) => {
                const timeA = a.timestamp ? a.timestamp.toMillis() : 0;
                const timeB = b.timestamp ? b.timestamp.toMillis() : 0;
                return timeA - timeB; // Ascending order (oldest first, newest last)
              });

              this.currentConversation.histories = sortedHistories;
              this.hasNoHistories = false;

              // Initialize response indexes for all messages
              sortedHistories.forEach((history: any) => {
                if (history.id && history.responses && history.responses.length > 0) {
                  this.currentResponseIndexes[history.id] = history.responses.length - 1;
                }
              });

              this.message.success(`Loaded ${res.histories.length} chat messages`);
            }
          } else {
            this.hasNoHistories = true;
            this.message.info('No chat history found');
          }
        } else {
          console.warn('No history data received');
          this.message.info('No chat history available');
        }

        // Scroll to show the first message
        setTimeout(() => this.scrollToBottom(), 100);
      },
      error: (error) => {
        // Clear loading state
        this.isMessageLoading = false;
        console.error('Error loading user history:', error);
        this.message.error('Failed to load chat history');
      }
    });
  }




  totalAgentInWorkspace(workspaceName: string) {
    this.agentDefinitionService.totalAgentInWorkspace(workspaceName).subscribe({
      next: (agents) => {
        console.log('Total agents in workspace:', agents);
        // this.workspaceAgents = agents;
        this.totalAgentsInWorkspace = agents;
      },
      error: (error) => {
        console.error('Error loading agents for workspace:', error);
      },
    });
  }
  totalPluginInAgent() {
    this.pluginServices.countByAgentName(this.selectedAgent).subscribe((res) => {
      console.log(res)
      this.totalAgentsInWorkspace = res
    })
  }

  //   loadWorkspaces() {
  //   this.workspaceService.getAll().subscribe({
  //     next: (workspaces) => {
  //       this.workspaces = workspaces;
  //       if (this.workspaces.length > 0) {
  //         this.selectedWorkspace = this.workspaces[0].title;
  //         this.loadAgentsForWorkspace(this.selectedWorkspace);
  //       }
  //     },
  //     error: (error) => {
  //       console.error('Error loading workspaces:', error);
  //     },
  //   });
  // }


  // loadAgentsForWorkspace(workspaceName: string) {
  //   if (!workspaceName) return;

  //   this.agentDefinitionService.totalAgentInWorkspace("Developer").subscribe({
  //     next: (agents) => {
  //       console.log('Total agents in workspace:', agents);
  //       // this.workspaceAgents = agents;
  //     },
  //     error: (error) => {
  //       console.error('Error loading agents for workspace:', error);
  //     },
  //   });
  // }



  ngAfterViewInit(): void {
    this.scrollToBottom();
    this.adjustInputHeight();
  }

  ngOnDestroy(): void {
    if (this.messageSubscription) {
      this.messageSubscription.unsubscribe();
    }
  }

  /**
   * Handle window resize events to update split sizes
   */
  @HostListener('window:resize', ['$event'])
  onWindowResize(_event: any): void {
    this.updateSplitSizes();
  }

  /**
   * Sets up streaming message subscription similar to hero component
   */
  private setupStreamingSubscription(): void {
    this.messageSubscription = this.chatService.messageReceived$.subscribe(
      ({ message, isError, isComplete }) => {
        console.log('Agent chat - Message received:', message);

        if (isError) {
          this.isMessageLoading = false;
          this.isStreamingActive = false;
          this.currentStreamingMessageId = undefined;

          // Handle error message
          if (this.currentConversation?.histories) {
            const lastMessage =
              this.currentConversation.histories[
              this.currentConversation.histories.length - 1
              ];
            if (
              lastMessage &&
              lastMessage.id === this.currentStreamingMessageId
            ) {
              const messageText = this.extractMessageText(message);
              if (!lastMessage.responses) {
                lastMessage.responses = [];
              }
              lastMessage.responses.push(
                new AgentChatResponseDto({
                  id: '',
                  responseText: messageText,
                  chatSource: 'Error',
                  timestamp: DateTime.now(),
                })
              );
            }
          }
        } else {
          const messageText = this.extractMessageText(message);
          console.log('Agent chat - Extracted text:', messageText);

          // Only skip if this is specifically the completion message, but allow empty chunks
          if (messageText === 'Streaming completed') {
            console.log('Ignoring streaming completion message');
            return;
          }

          if (
            this.isStreamingActive &&
            this.currentStreamingMessageId
          ) {
            // Handle streaming completion
            if (isComplete) {
              this.handleStreamingComplete();
              return;
            }

            // Find the streaming message and append text (even if empty)
            if (this.currentConversation?.histories) {
              const streamingMessage = this.currentConversation.histories.find(
                (h) => h.id === this.currentStreamingMessageId
              );
              if (streamingMessage) {
                // Initialize responses if needed
                if (!streamingMessage.responses) {
                  streamingMessage.responses = [];
                }

                // Get or create the current response
                if (streamingMessage.responses.length === 0) {
                  streamingMessage.responses.push(
                    new AgentChatResponseDto({
                      id: '',
                      responseText: messageText || '',
                      chatSource: 'Streaming',
                      timestamp: DateTime.now(),
                    })
                  );
                } else {
                  // Append to existing response (even empty strings for formatting)
                  const lastResponse =
                    streamingMessage.responses[
                    streamingMessage.responses.length - 1
                    ];
                  lastResponse.responseText =
                    (lastResponse.responseText || '') + (messageText || '');
                }

                // Transition from loading to streaming state
                if (streamingMessage.isLoading) {
                  streamingMessage.isLoading = false;
                  // Add isStreaming property dynamically (similar to hero component)
                  (streamingMessage as any).isStreaming = true;
                }

                // Force change detection and auto-scroll
                this.cdr.detectChanges();
                setTimeout(() => this.scrollToBottom(), 50);
              }
            }
          }
        }
      }
    );
  }

  /**
   * Handles streaming completion
   */
  private handleStreamingComplete(): void {
    console.log('handleStreamingComplete called');
    if (this.currentStreamingMessageId && this.currentConversation?.histories) {
      const streamingMessage = this.currentConversation.histories.find(
        (h) => h.id === this.currentStreamingMessageId
      );
      if (streamingMessage) {
        console.log('Completing streaming for message:', streamingMessage.id);
        console.log('Final response text length:', streamingMessage.responses?.[0]?.responseText?.length);

        // Clear streaming state
        (streamingMessage as any).isStreaming = false;
        streamingMessage.isLoading = false;
        this.isStreamingActive = false;
        this.currentStreamingMessageId = undefined;
        this.isMessageLoading = false;

        // Force change detection to ensure UI updates
        this.cdr.detectChanges();
        this.scrollToBottom();
      }
    }
  }

  /**
   * Extracts message text from ResponseMessage (same as hero component)
   */
  private extractMessageText(message: any): string {
    return message.isError ? ' ' : message.message;
  }

  /**
   * Check if a message is currently streaming
   */
  isMessageStreaming(message: any): boolean {
    return (message as any).isStreaming === true;
  }

  loadAgentChatHistories(agentName?: string, resetPagination: boolean = true) {
    if (resetPagination) {
      this.currentPage = 1;
      this.hasMoreData = true;
      this.allHistoriesLoaded = false;
      this.totalHistoryCount = 0;
    }

    this.isMessageLoading = true;

    // Log the agent name for debugging
    console.log('Loading chat histories for agent:', agentName || 'all agents', 'Page:', this.currentPage);

    this.agentChatService.getHistoriesPaginated(agentName, this.currentPage, this.pageSize).subscribe({
      next: (paginatedResult) => {
        console.log('Paginated result:', paginatedResult);

        // Update pagination state
        this.hasMoreData = paginatedResult.hasMore || false;
        this.totalHistoryCount = paginatedResult.totalCount || 0;
        this.allHistoriesLoaded = !this.hasMoreData;

        // Process the histories from the paginated result
        const histories = paginatedResult.histories || [];

        // Set the current response index to show the latest response for each history
        histories.forEach((history) => {
          if (history.responses && history.responses.length > 0) {
            this.currentResponseIndexes[history.id || ''] =
              history.responses.length - 1;
          }
        }); if (resetPagination) {
          // First load - create new conversation
          // Backend returns data in DESC order (newest first), so we reverse it
          // to display oldest first, newest last (for proper chat flow)
          const sortedHistories = histories.sort((a, b) => {
            const timeA = a.timestamp ? a.timestamp.toMillis() : 0;
            const timeB = b.timestamp ? b.timestamp.toMillis() : 0;
            return timeA - timeB; // Ascending order (oldest first, newest last)
          });

          this.currentConversation = new AgentChatConversationDto({
            agentName: agentName || paginatedResult.agentName,
            histories: sortedHistories,
          });
          this.conversations = [this.currentConversation];
        } else {
          // Loading more data (older messages) - prepend to existing histories
          if (this.currentConversation.histories) {
            // Backend returns older messages in DESC order, so we reverse them
            // and prepend to the beginning of existing histories
            const sortedOlderHistories = histories.sort((a, b) => {
              const timeA = a.timestamp ? a.timestamp.toMillis() : 0;
              const timeB = b.timestamp ? b.timestamp.toMillis() : 0;
              return timeA - timeB; // Ascending order
            });

            // Prepend older messages to the beginning
            this.currentConversation.histories = [...sortedOlderHistories, ...this.currentConversation.histories];
          } else {
            this.currentConversation.histories = histories;
          }
        }

        // Set hasNoHistories flag
        this.hasNoHistories = this.totalHistoryCount === 0;

        // Set selected agent if not already set
        if (this.currentConversation.agentName && !this.selectedAgent) {
          this.selectedAgent = this.currentConversation.agentName;
          this.userInput.agentName = this.currentConversation.agentName;
        }

        this.isMessageLoading = false;
        this.isLoadingMore = false;

        if (resetPagination) {
          setTimeout(() => this.scrollToBottom(), 100);
        }
      },
      error: (error) => {
        console.error('Error loading agent chat histories:', error);
        this.message.error('Failed to load chat histories');
        this.isMessageLoading = false;
        this.isLoadingMore = false;
      },
    });
  }
  /**
   * Loads all workspace chat histories.
   * Loads actual workspace chat histories from the backend.
   */
  loadworkspacesChatHistories(workspaceName: string) {
    // Clear previous data
    this.currentConversation = new AgentChatConversationDto({
      agentName: '',
      histories: [],
    });
    this.conversations = [this.currentConversation];
    this.currentResponseIndexes = {};
    this.hasNoHistories = true;
    this.isMessageLoading = true;

    console.log('Loading workspace chat histories for workspace:', workspaceName);

    // Load actual workspace chat histories
    this.agentChatService.workspaceHistories(workspaceName).subscribe({
      next: (conversation) => {
        if (conversation && conversation.histories && conversation.histories.length > 0) {
          // Backend now returns data in DESC order (newest first), so we need to reverse it
          // to display oldest first, newest last (for proper chat flow)
          const sortedHistories = conversation.histories.sort((a: any, b: any) => {
            const timeA = a.timestamp ? a.timestamp.toMillis() : 0;
            const timeB = b.timestamp ? b.timestamp.toMillis() : 0;
            return timeA - timeB; // Ascending order (oldest first, newest last)
          });

          this.currentConversation = new AgentChatConversationDto({
            agentName: conversation.agentName,
            histories: sortedHistories
          });
          this.conversations = [this.currentConversation];
          this.hasNoHistories = false;

          // Initialize response indexes for all messages
          sortedHistories.forEach(history => {
            if (history.id && history.responses && history.responses.length > 0) {
              this.currentResponseIndexes[history.id] = history.responses.length - 1;
            }
          });

          // Update pagination state
          this.totalHistoryCount = sortedHistories.length;
          this.currentPage = 1;
          this.hasMoreData = false; // All data loaded in single call
          this.allHistoriesLoaded = true;
        } else {
          // No histories found for this workspace
          this.hasNoHistories = true;
        }

        this.isMessageLoading = false;

        // Scroll to bottom to show the latest message
        setTimeout(() => this.scrollToBottom(), 100);
      },
      error: (error) => {
        console.error('Error loading workspace chat histories:', error);
        this.message.error('Failed to load workspace chat histories');
        this.hasNoHistories = true;
        this.isMessageLoading = false;
      }
    });
  }

  loadWorkspaces() {
    this.workspaceService.getAll().subscribe({
      next: (workspaces) => {
        this.workspaces = workspaces;
        if (this.workspaces.length > 0) {
          this.selectedWorkspace = this.workspaces[0].title;
          this.loadAgentsForWorkspace(this.selectedWorkspace);
        }
      },
      error: (error) => {
        console.error('Error loading workspaces:', error);
      },
    });
  }

  /**
   * Loads agents for a specific workspace
   */
  loadAgentsForWorkspace(workspaceName: string) {
    if (!workspaceName) return;

    this.agentDefinitionService.getAllByWorkspace(workspaceName).subscribe({
      next: (agents) => {
        this.workspaceAgents = agents;
      },
      error: (error) => {
        console.error('Error loading agents for workspace:', error);
      },
    });
  }



  /**
   * Handles file selection for upload
   */
  onFileSelected(event: any) {
    const files: FileList = event.target.files;
    if (files.length > 0) {
      const file = files[0];
      this.uploadFile(file);
    }
  }
  /**
   * Uploads a file to the server
   */
  uploadFile(file: File) {
    const formData = new FormData();
    formData.append('file', file);

    this.isFileUploading = true;

    // Add file to list immediately with uploading status
    const tempFileId = Date.now().toString();
    this.uploadedFiles.push({
      id: tempFileId,
      name: file.name,
      file: file,
      isUploading: true
    });

    this.fileService.upload('chat', [{ data: file, fileName: file.name }]).subscribe({
      next: (response: any) => {
        this.isFileUploading = false;        // Find and update the temporary file entry
        const fileIndex = this.uploadedFiles.findIndex(f => f.id === tempFileId);
        if (fileIndex !== -1) {
          if (!response.isError) {
            // Use the actual filename returned by the backend
            const backendFileName = response.message || file.name;
            this.uploadedFiles[fileIndex] = {
              ...this.uploadedFiles[fileIndex],
              uploadedFileName: backendFileName,
              isUploading: false
            };
            this.message.success(`${file.name} uploaded successfully`);
          } else {
            // Remove the file from list on error
            this.uploadedFiles.splice(fileIndex, 1);
            this.message.error(`File upload failed: ${response.message}`);
          }
        }

        this.cdr.detectChanges();
      },
      error: (error: any) => {
        this.isFileUploading = false;

        // Remove the file from list on error
        const fileIndex = this.uploadedFiles.findIndex(f => f.id === tempFileId);
        if (fileIndex !== -1) {
          this.uploadedFiles.splice(fileIndex, 1);
        }

        this.message.error(`Failed to upload ${file.name}`);
        console.error('File upload error:', error);
        this.cdr.detectChanges();
      },
    });
  }

  /**
   * Removes a file from the uploaded files list
   */
  removeFile(fileId: string) {
    this.uploadedFiles = this.uploadedFiles.filter((file) => file.id !== fileId);
    this.message.success('File removed successfully');
  }

  /**
   * Views details of an uploaded file
   */  viewFile(file: any) {
    this.modalService.info({
      nzTitle: 'File Details',
      nzContent: `
        <div class="space-y-2">
          <p><strong>Original Name:</strong> ${file.name}</p>
          <p><strong>Backend Name:</strong> ${file.uploadedFileName || 'Not uploaded yet'}</p>
          <p><strong>Size:</strong> ${this.formatFileSize(file.file.size)}</p>
          <p><strong>Type:</strong> ${file.file.type || 'Unknown'}</p>
          <p><strong>Uploaded:</strong> ${file.uploadedFileName ? 'Yes' : 'No'}</p>
        </div>
      `,
      nzOkText: 'Close'
    });
  }

  /**
   * Clears all uploaded files
   */
  clearAllFiles() {
    if (this.uploadedFiles.length === 0) {
      this.message.info('No files to clear');
      return;
    }

    this.modalService.confirm({
      nzTitle: 'Clear All Files',
      nzContent: `Are you sure you want to remove all ${this.uploadedFiles.length} uploaded files?`,
      nzOkText: 'Yes, Clear All',
      nzCancelText: 'Cancel',
      nzOkDanger: true,
      nzOnOk: () => {
        this.uploadedFiles = [];
        this.message.success('All files cleared successfully');
      }
    });
  }

  /**
   * Loads and saves right sidebar width from localStorage
   */
  loadSavedRightSidebarWidth() {
    const savedWidth = localStorage.getItem('rightSidebarWidth');
    if (savedWidth) {
      this.rightSidebarWidth = parseInt(savedWidth, 10);
    }
  }

  /**
   * Saves right sidebar width to localStorage
   */
  private saveRightSidebarWidth(width: number): void {
    try {
      localStorage.setItem('rightSidebarWidth', String(width));
    } catch (error) {
      console.warn('Failed to save sidebar width to localStorage:', error);
    }
  }

  /**
   * Updates split sizes based on sidebar state and window width
   */
  private updateSplitSizes(): void {
    if (!this.showSearchResultsSidebar) {
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
      return;
    }

    const windowWidth = window.innerWidth;
    const sidebarPercentage = Math.min(
      Math.max((this.rightSidebarWidth / windowWidth) * 100, 20),
      50
    );

    this.rightSidebarSplitSize = sidebarPercentage;
    this.mainContentSplitSize = 100 - sidebarPercentage;
  }

  /**
   * Scrolls the chat container to the bottom
   */
  scrollToBottom() {
    try {
      if (this.chatContainer) {
        setTimeout(() => {
          this.chatContainer.nativeElement.scrollTop =
            this.chatContainer.nativeElement.scrollHeight;
        }, 100);
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }
  /**
   * Loads more chat histories when scrolling up
   */
  loadMoreHistories() {
    if (!this.hasMoreData || this.isLoadingMore || this.allHistoriesLoaded) {
      return;
    }

    this.isLoadingMore = true;
    this.currentPage++;

    // Store current scroll position and first visible message to maintain user's view
    const element = this.chatContainer.nativeElement;
    const previousScrollHeight = element.scrollHeight;
    const previousScrollTop = element.scrollTop;

    // Get the first visible message before loading more data
    const firstVisibleMessageBefore = this.getFirstVisibleMessage();

    this.loadAgentChatHistories(this.selectedAgent, false);

    // After loading, adjust scroll position to maintain user's view
    setTimeout(() => {
      if (this.chatContainer) {
        const newScrollHeight = this.chatContainer.nativeElement.scrollHeight;
        const scrollDifference = newScrollHeight - previousScrollHeight;

        // Maintain scroll position by adding the difference in height
        this.chatContainer.nativeElement.scrollTop = previousScrollTop + scrollDifference;
      }
    }, 100);
  }

  /**
   * Helper method to get the first visible message (for maintaining scroll position)
   */
  private getFirstVisibleMessage(): any {
    if (!this.currentConversation?.histories) return null;

    // For simplicity, we'll use scroll position calculation
    // In a more sophisticated implementation, you could find the actual message element
    return this.currentConversation.histories[0];
  }

  /**
   * Adjusts the height of the input textarea
   */
  adjustInputHeight() {
    if (this.chatInput && this.chatInput.nativeElement) {
      const element = this.chatInput.nativeElement;
      element.style.height = 'auto';
      element.style.height = Math.min(element.scrollHeight, 200) + 'px';
    }
  }
  /**
   * Sends a message to either agent chat or workspace chat API based on current mode
   */
  async sendMessage() {
    if (!this.userInput.question?.trim()) return;

    // For workspace chat, we need a workspace name
    if (this.isWorkspaceChat && !this.selectedWorkspace) {
      this.message.error('Please select a workspace');
      return;
    }

    // For agent chat, we need an agent name
    if (!this.isWorkspaceChat && !this.userInput.agentName && !this.selectedAgent) {
      this.message.error('Please select an agent');
      return;
    }

    // Store the original message for immediate display
    const originalMessage = this.userInput.question;
    const agentName = this.userInput.agentName || this.selectedAgent;

    // Find current conversation or create a new one
    if (!this.isWorkspaceChat && this.currentConversation.agentName !== agentName) {
      const foundConversation = this.conversations.find(
        (c) => c.agentName === agentName
      );

      if (foundConversation) {
        this.currentConversation = foundConversation;
      } else {
        this.currentConversation = new AgentChatConversationDto({
          agentName: agentName,
          histories: [],
        });

        this.conversations.push(this.currentConversation);
      }
    }

    // Ensure histories array exists
    if (!this.currentConversation.histories) {
      this.currentConversation.histories = [];
    }

    // Create a temporary message object to show immediately in the UI
    const tempMessage = new AgentChatHistoryDto({
      id: 'temp-' + Date.now(), // Temporary ID
      question: originalMessage,
      responses: [], // Empty responses initially
      timestamp: DateTime.now(),
    });
    tempMessage.isLoading = true;

    // Add the temporary message to the conversation immediately
    this.currentConversation.histories.push(tempMessage);

    // Update hasNoHistories flag since we now have a conversation
    this.hasNoHistories = false;

    // Update pagination state since we added a new message
    this.totalHistoryCount++;

    // Clear input immediately for better UX
    this.userInput.question = '';
    this.adjustInputHeight();

    // Set loading state
    this.isMessageLoading = true;

    // Scroll to show the new message
    setTimeout(() => this.scrollToBottom(), 100);

    console.log('Sending message to:', this.isWorkspaceChat ? `workspace: ${this.selectedWorkspace}` : `agent: ${agentName}`);

    // Set up streaming for this message
    this.currentStreamingMessageId = tempMessage.id;
    this.isStreamingActive = true;

    if (this.isWorkspaceChat) {
      // Send workspace chat request
      const workspaceRequestDto = new WorkspaceChatRequestDto({
        question: originalMessage,
        workspaceName: this.selectedWorkspace,
        orchestrationAgents: undefined // Let the backend choose the agent
      });

      this.agentChatService.workspaceChat(workspaceRequestDto).subscribe({
        next: (response) => {
          this.handleChatResponse(response, tempMessage);
        },
        error: (error) => {
          this.handleChatError(error, tempMessage, originalMessage);
        }
      });
    } else {
      // Send agent chat request
      const requestDto = new AgentChatRequestDto({
        question: originalMessage,
        agentName: agentName,
        fileNames: this.uploadedFiles.map(file => file.uploadedFileName || file.name)
      });

      this.agentChatService.sendAgentMessage(requestDto).subscribe({
        next: (response) => {
          this.handleChatResponse(response, tempMessage);
        },
        error: (error) => {
          this.handleChatError(error, tempMessage, originalMessage);
        }
      });
    }

    // Call updateCurrentChat when the chat changes
    this.updateCurrentChat();
  }
  /**
   * Handles successful chat response for both agent and workspace chats
   */
  private handleChatResponse(response: any, tempMessage: AgentChatHistoryDto) {
    console.log('handleChatResponse called with response:', response);
    console.log('Temp message:', tempMessage);

    if (!response) {
      console.error('Received null response from chat service');
      this.message.error('Received invalid response from server');
      this.isMessageLoading = false;
      return;
    }

    // Find and update the temporary message with the actual response
    if (this.currentConversation.histories) {
      const tempIndex = this.currentConversation.histories.findIndex(
        (h) => h.id === tempMessage.id
      );
      if (tempIndex !== -1) {
        const currentMessage = this.currentConversation.histories[tempIndex];
        console.log('Found temp message at index:', tempIndex, 'Current message:', currentMessage);

        // Check if we have streamed content to preserve
        const hasStreamedContent = currentMessage.responses &&
          currentMessage.responses.length > 0 &&
          currentMessage.responses[0].responseText &&
          currentMessage.responses[0].responseText.trim().length > 0;

        if (hasStreamedContent && currentMessage.responses) {
          console.log(
            'Preserving streamed content. Text length:',
            currentMessage.responses[0]?.responseText
              ? currentMessage.responses[0].responseText.length
              : 0
          );

          // Store the original temp ID and streamed response text
          const originalTempId = currentMessage.id;
          const streamedText = currentMessage.responses[0].responseText;

          // Update message properties but preserve the question and streamed response
          const originalQuestion = currentMessage.question;
          currentMessage.id = response.id;
          currentMessage.timestamp = response.timestamp;
          currentMessage.question = originalQuestion;

          // Update the first response with backend metadata while preserving streamed text
          if (response.responses && response.responses.length > 0) {
            const backendResponse = response.responses[0];
            const streamedResponse = currentMessage.responses[0];

            // Update metadata but keep the streamed text
            streamedResponse.id = backendResponse.id;
            streamedResponse.timestamp = backendResponse.timestamp;
            streamedResponse.chatSource = backendResponse.chatSource || 'Streaming';
            // CRITICAL: Keep the streamed text, don't overwrite it
            streamedResponse.responseText = streamedText;
          }

          // Add any additional responses from backend
          if (response.responses && response.responses.length > 1) {
            for (let i = 1; i < response.responses.length; i++) {
              currentMessage.responses.push(response.responses[i]);
            }
          }

          // Transfer response index from temp ID to actual ID
          if (originalTempId && this.currentResponseIndexes[originalTempId] !== undefined) {
            this.currentResponseIndexes[response.id] = this.currentResponseIndexes[originalTempId];
            delete this.currentResponseIndexes[originalTempId];
          }

          console.log('After preserving - final text length:', currentMessage.responses[0]?.responseText?.length);
        } else {
          console.log('No streamed content found, using backend response entirely');
          // No streamed content, use the backend response entirely
          this.currentConversation.histories[tempIndex] = response;
        }

        // CRITICAL FIX: Ensure the message ID is properly updated for regenerate to work
        // This fixes the workspace chat regenerate issue where temp ID was being used
        if (response.id && currentMessage.id !== response.id) {
          console.log('Updating message ID from temp to actual:', currentMessage.id, '->', response.id);
          currentMessage.id = response.id;
        }

        // Set the current response index to show the latest response (only if not already set)
        if (response.id && this.currentResponseIndexes[response.id] === undefined) {
          this.currentResponseIndexes[response.id] = Math.max(0, (response.responses || []).length - 1);
        }
      } else {
        console.log('Temp message not found, adding response to conversation');
        // Fallback: just add the response if temp message not found
        this.currentConversation.histories.push(response);
        if (response.id) {
          this.currentResponseIndexes[response.id] = Math.max(0, (response.responses || []).length - 1);
        }
      }
    }

    // Clear loading and streaming state
    this.isMessageLoading = false;
    this.isStreamingActive = false;
    this.currentStreamingMessageId = undefined;

    // Clear uploaded files after successful message send
    this.uploadedFiles = [];

    // Force change detection to ensure UI updates
    this.cdr.detectChanges();

    // Small delay to ensure DOM is updated before scrolling
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);
  }

  /**
   * Handles chat errors for both agent and workspace chats
   */
  private handleChatError(error: any, tempMessage: AgentChatHistoryDto, originalMessage: string) {
    console.error('Error sending chat message:', error);
    this.message.error('Failed to send message');

    // Remove the temporary message on error
    if (this.currentConversation.histories) {
      const tempIndex = this.currentConversation.histories.findIndex(
        (h) => h.id === tempMessage.id
      );
      if (tempIndex !== -1) {
        this.currentConversation.histories.splice(tempIndex, 1);
      }
    }

    // Restore the original message to the input
    this.userInput.question = originalMessage;
    this.adjustInputHeight();    // Clear loading state
    this.isMessageLoading = false;

    // Stop streaming
    this.isStreamingActive = false;
    this.currentStreamingMessageId = undefined;
  }

  /**
   * Regenerate response for a specific chat message
   */
  regenerateResponse(history: AgentChatHistoryDto) {
    if (!history || !history.id) return;

    // Validate that we have a proper ID (not a temp ID)
    if (history.id.toString().startsWith('temp-')) {
      console.error('Cannot regenerate with temporary ID:', history.id);
      this.message.error('Cannot regenerate response. Please wait for the message to complete first.');
      return;
    }

    // Set loading state for this specific message
    history.isLoading = true;

    // STREAMING FIX: Set up streaming for regenerate
    this.currentStreamingMessageId = history.id;
    this.isStreamingActive = true;

    // Clear existing streaming responses to prepare for new regenerated content
    if (history.responses && history.responses.length > 0) {
      // Keep existing responses but mark that we're regenerating
      const lastResponseIndex = history.responses.length - 1;
      // We'll add the new response when regeneration completes
    }

    if (this.isWorkspaceChat) {
      // Handle workspace chat regeneration
      this.agentChatService.workspaceChatRegenerate(history.id, this.selectedWorkspace).subscribe({
        next: (response) => {
          this.handleRegenerateResponse(history, response);
        },
        error: (error) => {
          this.handleRegenerateError(history, error);
        }
      });
    } else {
      // Handle agent chat regeneration
      const agentName = this.currentConversation?.agentName;
      this.agentChatService.agentChatRegenerate(history.id, agentName).subscribe({
        next: (response) => {
          this.handleRegenerateResponse(history, response);
        },
        error: (error) => {
          this.handleRegenerateError(history, error);
        }
      });
    }
  }

  /**
   * Handle successful regenerate response
   */
  private handleRegenerateResponse(history: AgentChatHistoryDto, response: any) {
    // Clear loading state for this message
    history.isLoading = false;

    // STREAMING FIX: Clear streaming state
    this.isStreamingActive = false;
    this.currentStreamingMessageId = undefined;

    // Update the responses in the history
    if (!history.responses) {
      history.responses = [];
    }

    // Create a new response from regenerated content
    if (response.responseText) {
      const newResponse = new AgentChatResponseDto({
        id: response.id || '',
        responseText: response.responseText,
        chatSource: response.chatSource || 'Regenerated',
        timestamp: response.timestamp || DateTime.now(),
      });
      history.responses.push(newResponse);

      // Update the current response index to show the latest response
      const historyId = history.id || '';
      this.currentResponseIndexes[historyId] = history.responses.length - 1;
    }

    this.cdr.detectChanges();
    this.scrollToBottom();
  }

  /**
   * Handle regenerate error
   */
  private handleRegenerateError(history: AgentChatHistoryDto, error: any) {
    console.error('Error regenerating response:', error);
    this.message.error('Failed to regenerate response');

    // Clear loading state for this message on error
    history.isLoading = false;

    // STREAMING FIX: Clear streaming state on error
    this.isStreamingActive = false;
    this.currentStreamingMessageId = undefined;
  }

  /**
   * Edit an existing message
   */
  editMessage(history: AgentChatHistoryDto) {
    if (!history) return;

    // Store the edited question
    const editedQuestion = history.question;

    // Exit editing mode
    history.editingMode = false;

    // Set the input to the edited question and send it
    this.userInput.question = editedQuestion;
    this.sendMessage();
  }

  // Input handling methods
  handleKeyDown(event: any) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  onInput() {
    this.adjustInputHeight();
  }

  onChatScroll() {
    if (!this.chatContainer) return;

    const element = this.chatContainer.nativeElement;
    const scrollTop = element.scrollTop;
    const scrollHeight = element.scrollHeight;
    const clientHeight = element.clientHeight;

    // Show/hide scroll to bottom button
    this.showScrollButton = scrollHeight - scrollTop - clientHeight > 100;

    // Check if user scrolled to top and we have more data to load
    if (scrollTop <= 50 && this.hasMoreData && !this.isLoadingMore && !this.isMessageLoading) {
      this.loadMoreHistories();
    }
  }

  // Splitter methods
  onSplitDragEnd(_event: any) {
    this.saveRightSidebarWidth(this.rightSidebarWidth);
  }

  onSplitDragProgress(_event: any) {
    this.isDragging = true;
    setTimeout(() => {
      this.isDragging = false;
    }, 1000);
  }

  onGutterDoubleClick(_event: any) {
    if (this.rightSidebarSplitSize > 0) {
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
    } else {
      this.rightSidebarSplitSize = 30;
      this.mainContentSplitSize = 70;
    }
  }

  // UI control methods
  toggleAgentSidebar() {
    // Determine if the current route is an agent or workspace chat
    const isAgent = this.router.url.includes('/chat/agent/');
    const isWorkspace = this.router.url.includes('/chat/workspace/');

    if (isWorkspace) {
      // Toggle agent sidebar, close plugin sidebar and search results
      this.isAgentSidebarOpen = !this.isAgentSidebarOpen;
      this.isPluginSidebarOpen = false;
      this.showSearchResultsSidebar = false;
    } else if (isAgent) {
      // Toggle plugin sidebar, close agent sidebar and search results
      this.isPluginSidebarOpen = !this.isPluginSidebarOpen;
      this.isAgentSidebarOpen = false;
      this.showSearchResultsSidebar = false;
    } else {
      // Default: toggle agent sidebar
      this.isAgentSidebarOpen = !this.isAgentSidebarOpen;
      this.isPluginSidebarOpen = false;
      this.showSearchResultsSidebar = false;
    }

    if (this.isAgentSidebarOpen || this.isPluginSidebarOpen) {
      this.rightSidebarSplitSize = 30;
      this.mainContentSplitSize = 70;
    } else {
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
    }
  }



  toggleSearchResultsSidebar(chatSourceDescriptions?: any[], sourceName?: string) {
    this.showSearchResultsSidebar = !this.showSearchResultsSidebar;

    // If we have chat source descriptions, populate the search results
    if (chatSourceDescriptions && chatSourceDescriptions.length > 0) {
      this.currentSourceName = sourceName || 'Web Search';
      this.searchResults = chatSourceDescriptions.map(desc => ({
        title: desc.title || desc.Title || 'No Title',
        url: desc.url || desc.URL || '',
        description: desc.description || desc.Description || 'No description available'
      }));
    }

    if (this.showSearchResultsSidebar) {
      this.rightSidebarSplitSize = 30;
      this.mainContentSplitSize = 70;
    } else {
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
    }
  }


  /**
   * Handles plugin selection from the sidebar
   * @param plugin The selected plugin
   */
  onSelectPlugin(plugin: PluginResponseDto): void {
    console.log('Selected plugin:', plugin);
    // You can implement plugin-specific actions here
    // For example, show plugin details, configure plugin, etc.
    this.message.info(`Selected plugin: ${plugin.pluginName}`);
  }

  /**
   * Handles closing the plugin sidebar
   */
  onClosePluginSidebar(): void {
    this.isPluginSidebarOpen = false;
    this.rightSidebarSplitSize = 0;
    this.mainContentSplitSize = 100;
  }


  hasChatSource(response: AgentChatResponseDto | null): boolean {
    return !!(response && response.chatSource && response.chatSource.trim());
  }
  getParsedChatSources(response: AgentChatResponseDto | null): any[] {
    if (!response || !response.chatSource || !response.chatSource.trim()) {
      return [];
    }

    // Skip parsing if chatSource is just "Streaming" or other non-JSON values
    const chatSource = response.chatSource.trim();
    if (chatSource === 'Streaming' || chatSource === 'Error' || chatSource.length < 10) {
      return [];
    }

    try {
      const parsed = JSON.parse(response.chatSource);

      // Ensure we return an array
      if (Array.isArray(parsed)) {
        return parsed.map(source => ({
          source: source.Source || source.source || 'Unknown',
          chatSourceDescriptions: source.ChatSourceDescriptions || source.chatSourceDescriptions || []
        }));
      }
      return [];
    } catch (error) {
      console.error('Error parsing chat sources:', error);
      return [];
    }
  }

  hasSourceDescriptions(response: any): boolean {
    if (!response || !response.chatSourceDescriptions) {
      return false;
    }

    if (!Array.isArray(response.chatSourceDescriptions)) {
      return false;
    }

    // Check if array has items and at least one item has valid data
    return response.chatSourceDescriptions.length > 0 &&
      response.chatSourceDescriptions.some((source: any) =>
        source && (source.title || source.url || source.description)
      );
  }

  /**
   * Gets the count of valid source descriptions for a response
   * @param response The response object
   * @returns The number of valid source descriptions
   */
  getSourceCount(response: any): number {
    if (!this.hasSourceDescriptions(response)) {
      return 0;
    }

    // Count only sources that have valid data
    return response.chatSourceDescriptions.filter((source: any) =>
      source && (source.title || source.url || source.description)
    ).length;
  }

  /**
   * Handles clicking on source indicators to show source references
   * @param response The response containing source descriptions
   */
  onSourceClick(response: any): void {
    if (!response || !this.hasSourceDescriptions(response)) {
      return;
    }

    this.toggleSearchResultsSidebar(
      response.chatSourceDescriptions,
      response.chatSource || 'Web Search'
    );
  }


  selectAgent(agent: any) {
    this.selectedAgent = agent.agentName || agent;
    this.userInput.agentName = this.selectedAgent;

    // Navigate to the agent's chat URL
    this.router.navigate(['/agent-chat', this.selectedAgent]);

    // Close sidebar if open
    if (this.isAgentSidebarOpen) {
      this.isAgentSidebarOpen = false;
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
    }
  }

  // Helper methods
  copyContent(content: string | undefined) {
    if (content) {
      navigator.clipboard.writeText(content);
      this.message.success('Content copied to clipboard');
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
  triggerFileUpload(): void {
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  // Response navigation methods that were missing
  getCurrentResponseIndex(historyId: string): number {
    return this.currentResponseIndexes[historyId] || 0;
  }

  getCurrentResponse(history: AgentChatHistoryDto): AgentChatResponseDto | null {
    if (!history?.responses?.length) return null;
    const currentIndex = this.getCurrentResponseIndex(history.id || '');
    return history.responses[currentIndex] || null;
  }

  goToPreviousResponse(history: AgentChatHistoryDto): void {
    if (!history?.responses?.length) return;
    const currentIndex = this.getCurrentResponseIndex(history.id || '');
    if (currentIndex > 0) {
      this.currentResponseIndexes[history.id || ''] = currentIndex - 1;
    }
  }

  nextResponse(history: AgentChatHistoryDto): void {
    if (!history?.responses?.length) return;
    const currentIndex = this.getCurrentResponseIndex(history.id || '');
    if (currentIndex < history.responses.length - 1) {
      this.currentResponseIndexes[history.id || ''] = currentIndex + 1;
    }
  }

  // Update the current chatting agent or workspace
  updateCurrentChat(): void {
    if (this.isAgentChat && this.currentConversation.agentName) {
      this.sidebarService.updateItemPosition('agent', this.currentConversation.agentName);
    } else if (this.isWorkspaceChat && this.selectedWorkspace) {
      this.sidebarService.updateItemPosition('workspace', this.selectedWorkspace);
    }
  }

  onUserSelected(userEmail: string): void {
    console.log('User selected:', userEmail);
    this.selectedUserEmail = userEmail;

    // Reset some UI state
    this.currentPage = 1;
    this.hasMoreData = true;
    this.allHistoriesLoaded = false;

    // Load history for the selected user
    this.loadHistoryOfAgentAndWorkspace(userEmail);
    console.log('Loading history for user:', userEmail);
  }

  // Feedback Methods
  /**
   * Opens the feedback modal for a specific message
   * @param message The message to provide feedback for
   * @param isPositive Whether this is positive feedback (like) or negative (dislike)
   */
  openFeedbackModal(message: any, isPositive: boolean): void {
    this.currentFeedbackMessage = message;
    this.isFeedbackPositive = isPositive;
    this.feedbackScore = isPositive ? 8 : 3; // Default scores based on like/dislike
    this.showFeedbackModal = true;
  }

  /**
   * Closes the feedback modal and resets feedback state
   */
  closeFeedbackModal(): void {
    this.showFeedbackModal = false;
    this.currentFeedbackMessage = null;
    this.feedbackScore = 5;
    this.submittingFeedback = false;
  }

  /**
   * Submits the feedback to the backend
   */
  submitFeedback(): void {
    if (!this.currentFeedbackMessage) {
      this.message.error('No message selected for feedback');
      return;
    }

    if (this.feedbackScore < 1 || this.feedbackScore > 10) {
      this.message.error('Please provide a score between 1 and 10');
      return;
    }

    this.submittingFeedback = true;

    const currentResponse = this.getCurrentResponse(this.currentFeedbackMessage);
    if (!currentResponse) {
      this.message.error('No response found for feedback');
      this.submittingFeedback = false;
      return;
    }

    const feedbackDto = new AgentFeedbackDto({
      agentName: this.currentConversation.agentName,
      prompt: this.currentFeedbackMessage.question,
      output: currentResponse.responseText,
      score: this.feedbackScore,
      isPositiveFeedback: this.isFeedbackPositive
    });

    this.agentEvaluationService.submitFeedback(feedbackDto).subscribe({
      next: (result) => {
        this.message.success('Feedback submitted successfully');
        this.closeFeedbackModal();

        // Optionally update the UI to show feedback was submitted
        if (this.currentFeedbackMessage) {
          this.currentFeedbackMessage.feedbackSubmitted = true;
          this.currentFeedbackMessage.feedbackScore = this.feedbackScore;
          this.currentFeedbackMessage.feedbackPositive = this.isFeedbackPositive;
        }
      },
      error: (error) => {
        console.error('Error submitting feedback:', error);
        this.message.error('Failed to submit feedback. Please try again.');
        this.submittingFeedback = false;
      },
      complete: () => {
        this.submittingFeedback = false;
      }
    });
  }

  /**
   * Quick feedback method for like button
   * @param message The message to like
   */
  likeFeedback(message: any): void {
    this.openFeedbackModal(message, true);
  }

  /**
   * Quick feedback method for dislike button
   * @param message The message to dislike
   */
  dislikeFeedback(message: any): void {
    this.openFeedbackModal(message, false);
  }
}

/**
 * Add this to the AgentChatHistoryDto class to support editing and loading states
 */
declare module '../../../shared/service-proxies/service-proxies' {
  interface AgentChatHistoryDto {
    editingMode?: boolean;
    isLoading?: boolean;
    feedbackSubmitted?: boolean;
    feedbackScore?: number;
    feedbackPositive?: boolean;
  }

  interface AgentChatResponseDto {
    copied?: boolean;
  }
}
