{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-outlet.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { TemplateRef, Directive, Input, NgModule } from '@angular/core';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzStringTemplateOutletDirective {\n  static ngTemplateContextGuard(_dir, _ctx) {\n    return true;\n  }\n  recreateView() {\n    this.viewContainer.clear();\n    const isTemplateRef = this.nzStringTemplateOutlet instanceof TemplateRef;\n    const templateRef = isTemplateRef ? this.nzStringTemplateOutlet : this.templateRef;\n    this.embeddedViewRef = this.viewContainer.createEmbeddedView(templateRef, isTemplateRef ? this.nzStringTemplateOutletContext : this.context);\n  }\n  updateContext() {\n    const isTemplateRef = this.nzStringTemplateOutlet instanceof TemplateRef;\n    const newCtx = isTemplateRef ? this.nzStringTemplateOutletContext : this.context;\n    const oldCtx = this.embeddedViewRef.context;\n    if (newCtx) {\n      for (const propName of Object.keys(newCtx)) {\n        oldCtx[propName] = newCtx[propName];\n      }\n    }\n  }\n  constructor(viewContainer, templateRef) {\n    this.viewContainer = viewContainer;\n    this.templateRef = templateRef;\n    this.embeddedViewRef = null;\n    this.context = new NzStringTemplateOutletContext();\n    this.nzStringTemplateOutletContext = null;\n    this.nzStringTemplateOutlet = null;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzStringTemplateOutletContext,\n      nzStringTemplateOutlet\n    } = changes;\n    const shouldRecreateView = () => {\n      let shouldOutletRecreate = false;\n      if (nzStringTemplateOutlet) {\n        if (nzStringTemplateOutlet.firstChange) {\n          shouldOutletRecreate = true;\n        } else {\n          const isPreviousOutletTemplate = nzStringTemplateOutlet.previousValue instanceof TemplateRef;\n          const isCurrentOutletTemplate = nzStringTemplateOutlet.currentValue instanceof TemplateRef;\n          shouldOutletRecreate = isPreviousOutletTemplate || isCurrentOutletTemplate;\n        }\n      }\n      const hasContextShapeChanged = ctxChange => {\n        const prevCtxKeys = Object.keys(ctxChange.previousValue || {});\n        const currCtxKeys = Object.keys(ctxChange.currentValue || {});\n        if (prevCtxKeys.length === currCtxKeys.length) {\n          for (const propName of currCtxKeys) {\n            if (prevCtxKeys.indexOf(propName) === -1) {\n              return true;\n            }\n          }\n          return false;\n        } else {\n          return true;\n        }\n      };\n      const shouldContextRecreate = nzStringTemplateOutletContext && hasContextShapeChanged(nzStringTemplateOutletContext);\n      return shouldContextRecreate || shouldOutletRecreate;\n    };\n    if (nzStringTemplateOutlet) {\n      this.context.$implicit = nzStringTemplateOutlet.currentValue;\n    }\n    const recreateView = shouldRecreateView();\n    if (recreateView) {\n      /** recreate view when context shape or outlet change **/\n      this.recreateView();\n    } else {\n      /** update context **/\n      this.updateContext();\n    }\n  }\n  static {\n    this.ɵfac = function NzStringTemplateOutletDirective_Factory(t) {\n      return new (t || NzStringTemplateOutletDirective)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzStringTemplateOutletDirective,\n      selectors: [[\"\", \"nzStringTemplateOutlet\", \"\"]],\n      inputs: {\n        nzStringTemplateOutletContext: \"nzStringTemplateOutletContext\",\n        nzStringTemplateOutlet: \"nzStringTemplateOutlet\"\n      },\n      exportAs: [\"nzStringTemplateOutlet\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzStringTemplateOutletDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzStringTemplateOutlet]',\n      exportAs: 'nzStringTemplateOutlet',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.TemplateRef\n  }], {\n    nzStringTemplateOutletContext: [{\n      type: Input\n    }],\n    nzStringTemplateOutlet: [{\n      type: Input\n    }]\n  });\n})();\nclass NzStringTemplateOutletContext {}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzOutletModule {\n  static {\n    this.ɵfac = function NzOutletModule_Factory(t) {\n      return new (t || NzOutletModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzOutletModule,\n      imports: [NzStringTemplateOutletDirective],\n      exports: [NzStringTemplateOutletDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzOutletModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzStringTemplateOutletDirective],\n      exports: [NzStringTemplateOutletDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzOutletModule, NzStringTemplateOutletDirective };\n"], "mappings": ";;;;;;;;;;;;;;;AAOA,IAAM,kCAAN,MAAM,iCAAgC;AAAA,EACpC,OAAO,uBAAuB,MAAM,MAAM;AACxC,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,SAAK,cAAc,MAAM;AACzB,UAAM,gBAAgB,KAAK,kCAAkC;AAC7D,UAAM,cAAc,gBAAgB,KAAK,yBAAyB,KAAK;AACvE,SAAK,kBAAkB,KAAK,cAAc,mBAAmB,aAAa,gBAAgB,KAAK,gCAAgC,KAAK,OAAO;AAAA,EAC7I;AAAA,EACA,gBAAgB;AACd,UAAM,gBAAgB,KAAK,kCAAkC;AAC7D,UAAM,SAAS,gBAAgB,KAAK,gCAAgC,KAAK;AACzE,UAAM,SAAS,KAAK,gBAAgB;AACpC,QAAI,QAAQ;AACV,iBAAW,YAAY,OAAO,KAAK,MAAM,GAAG;AAC1C,eAAO,QAAQ,IAAI,OAAO,QAAQ;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,eAAe,aAAa;AACtC,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,UAAU,IAAI,8BAA8B;AACjD,SAAK,gCAAgC;AACrC,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,qBAAqB,MAAM;AAC/B,UAAI,uBAAuB;AAC3B,UAAI,wBAAwB;AAC1B,YAAI,uBAAuB,aAAa;AACtC,iCAAuB;AAAA,QACzB,OAAO;AACL,gBAAM,2BAA2B,uBAAuB,yBAAyB;AACjF,gBAAM,0BAA0B,uBAAuB,wBAAwB;AAC/E,iCAAuB,4BAA4B;AAAA,QACrD;AAAA,MACF;AACA,YAAM,yBAAyB,eAAa;AAC1C,cAAM,cAAc,OAAO,KAAK,UAAU,iBAAiB,CAAC,CAAC;AAC7D,cAAM,cAAc,OAAO,KAAK,UAAU,gBAAgB,CAAC,CAAC;AAC5D,YAAI,YAAY,WAAW,YAAY,QAAQ;AAC7C,qBAAW,YAAY,aAAa;AAClC,gBAAI,YAAY,QAAQ,QAAQ,MAAM,IAAI;AACxC,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,wBAAwB,iCAAiC,uBAAuB,6BAA6B;AACnH,aAAO,yBAAyB;AAAA,IAClC;AACA,QAAI,wBAAwB;AAC1B,WAAK,QAAQ,YAAY,uBAAuB;AAAA,IAClD;AACA,UAAM,eAAe,mBAAmB;AACxC,QAAI,cAAc;AAEhB,WAAK,aAAa;AAAA,IACpB,OAAO;AAEL,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wCAAwC,GAAG;AAC9D,aAAO,KAAK,KAAK,kCAAoC,kBAAqB,gBAAgB,GAAM,kBAAqB,WAAW,CAAC;AAAA,IACnI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,0BAA0B,EAAE,CAAC;AAAA,MAC9C,QAAQ;AAAA,QACN,+BAA+B;AAAA,QAC/B,wBAAwB;AAAA,MAC1B;AAAA,MACA,UAAU,CAAC,wBAAwB;AAAA,MACnC,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iCAAiC,CAAC;AAAA,IACxG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,+BAA+B,CAAC;AAAA,MAC9B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gCAAN,MAAoC;AAAC;AAMrC,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,+BAA+B;AAAA,MACzC,SAAS,CAAC,+BAA+B;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,+BAA+B;AAAA,MACzC,SAAS,CAAC,+BAA+B;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}