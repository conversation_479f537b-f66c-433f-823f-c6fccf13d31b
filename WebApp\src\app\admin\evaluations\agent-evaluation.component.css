/* Agent evaluation specific styles */
.agent-card {
    margin-bottom: 20px;
}

.score-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    color: white;
    font-weight: 500;
}
:host ::ng-deep .ant-model-footer{
  background-color: var(--primary-purple);
}

.score-high {
    background-color: #52c41a;
}

.score-medium {
    background-color: #faad14;
}

.score-low {
    background-color: #f5222d;
}

/* Agent selection styling */
:host ::ng-deep .ant-select-selection-search-input {
    height: 100%;
}

:host ::ng-deep .ant-select-selection-placeholder,
:host ::ng-deep .ant-select-selection-item {
    line-height: 32px;
}

:host ::ng-deep .ant-select-focused .ant-select-selector {
    border-color: #40a9ff !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

:host ::ng-deep .ant-select-dropdown {
    max-height: 300px;
}

/* Table styling */
:host ::ng-deep .ant-table-cell {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Form styling */
.form-container {
    margin-bottom: 24px;
    background-color: #fafafa;
    padding: 16px;
    border-radius: 4px;
}

/* Tab styling */
:host ::ng-deep .ant-tabs-nav {
    margin-bottom: 2px;
}
/* :host ::ng-deep .modern-tabset .tab-container-modern{
  background-color: #a82d2d;
} */

:host ::ng-deep .ant-tabs-tab {
    font-size: 16px;
    padding: 10px 20px;
}

/* Execution log */
.execution-log {
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    background-color: #f8f8f8;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 10px;
}

.execution-log div {
    border-bottom: 1px solid #eee;
    padding: 12px 8px;
    margin-bottom: 4px;
}

.execution-log div:last-child {
    border-bottom: none;
}

.execution-log .font-semibold {
    color: #1890ff;
    font-weight: 600;
    margin-bottom: 4px;
}

/* Animation for streaming text */
@keyframes blink {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

.streaming-cursor {
    display: inline-block;
    width: 8px;
    height: 16px;
    background-color: #666;
    animation: blink 1s infinite;
    margin-left: 2px;
    vertical-align: middle;
}

/* Re-evaluation results */
.evaluation-details {
    background-color: #fafafa;
    padding: 16px;
    border-radius: 8px;
    margin-top: 16px;
    border: 1px solid #eee;
}

.evaluation-section {
    margin-bottom: 16px;
}

.evaluation-heading {
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.improvement-suggestion {
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.improvement-suggestion:before {
    content: '•';
    position: absolute;
    left: 0;
    color: #1890ff;
}

/* Modern Table Styles */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.modern-table thead tr {
  background-color: var(--text-dark, #374151);
}

.modern-table thead th {
  padding: 16px 24px;
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  color: white;
  letter-spacing: 0.025em;
}

.modern-table tbody tr {
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s ease;
}

.modern-table tbody tr:hover {
  background-color: #f9fafb;
}

.modern-table tbody tr:last-child {
  border-bottom: none;
}

.modern-table tbody td {
  padding: 16px 24px;
  font-size: 14px;
  color: #374151;
}

/* Table Specific Styles - Matching the Design Image */
.evaluation-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.evaluation-table thead {
  /* background-color: #374151; */
}

.evaluation-table thead th {
  padding: 16px 24px;
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  color: white;
  border: none;
}

.evaluation-table tbody tr {
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.15s ease-in-out;
}

.evaluation-table tbody tr:hover {
  background-color: #f9fafb;
}

.evaluation-table tbody tr:last-child {
  border-bottom: none;
}

.evaluation-table tbody td {
  padding: 16px 24px;
  font-size: 14px;
  color: #374151;
  vertical-align: middle;
}

/* Role Badge Styles */
.role-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.role-badge.user {
  background-color: #10b981;
  color: white;
}

.role-badge.doctor {
  background-color: #3b82f6;
  color: white;
}

.role-badge.admin {
  background-color: #8b5cf6;
  color: white;
}

.role-badge + .add-btn {
  margin-left: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  cursor: pointer;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.delete-btn {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: #fef2f2;
  border: none;
  color: #dc2626;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.delete-btn:hover {
  background-color: #fee2e2;
  color: #b91c1c;
}

/* Filter Section Styles */
.filter-section {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

/* Button Styles */
.btn-primary {
  padding: 10px 24px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-secondary {
  padding: 10px 16px;
  background-color: #f3f4f6;
  color: #374151;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
}

.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: 16px;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.modal-close-btn {
  background: transparent;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  transition: color 0.2s ease;
}

.modal-close-btn:hover {
  color: #374151;
}

/* Form Group Styles */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.form-textarea {
  width: 100%;
  padding: 10px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background-color: white;
  color: #374151;
  font-size: 14px;
  resize: vertical;
  min-height: 80px;
  transition: all 0.2s ease;
}

.form-textarea:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
}

/* Tab Styles */
.tab-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.tab-header {
  border-bottom: 1px solid #e5e7eb;
  display: flex;
}

/* Tab Styling with Dark/Light Mode Support */
.tab-button {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 2px solid transparent;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: var(--transition-default);
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-medium-gray);
}

.tab-button:hover {
  color: var(--text-dark);
  background-color: var(--hover-blue-gray);
}

.tab-button.active {
  border-bottom-color: var(--primary-purple);
  color: var(--primary-purple);
  background-color: var(--active-tab-bg);
}

.tab-badge {
  background-color: var(--hover-blue-gray);
  color: var(--text-dark);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-left: 4px;
  transition: var(--transition-default);
}

.tab-button.active .tab-badge {
  background-color: var(--primary-purple);
  color: var(--background-white);
}

/* Enhanced ng-zorro overrides for proper theming */
:host ::ng-deep .ant-input {
  background-color: var(--background-white) !important;
  border-color: var(--hover-blue-gray) !important;
  color: var(--text-dark) !important;
  transition: var(--transition-default) !important;
}

:host ::ng-deep .ant-input:focus {
  border-color: var(--primary-purple) !important;
  box-shadow: 0 0 0 2px rgba(var(--primary-purple-rgb), 0.2) !important;
}

:host ::ng-deep .ant-input::placeholder {
  color: var(--text-medium-gray) !important;
}

/* Select dropdown theming */
:host ::ng-deep .ant-select:not(.ant-select-customize-input) .ant-select-selector {
  background-color: var(--background-white) !important;
  border-color: var(--hover-blue-gray) !important;
  color: var(--text-dark) !important;
  transition: var(--transition-default) !important;
}

:host ::ng-deep .ant-select:hover .ant-select-selector,
:host ::ng-deep .ant-select.ant-select-focused .ant-select-selector {
  border-color: var(--primary-purple) !important;
  box-shadow: 0 0 0 2px rgba(var(--primary-purple-rgb), 0.2) !important;
}

:host ::ng-deep .ant-select-dropdown {
  background-color: var(--background-white) !important;
  border: 1px solid var(--hover-blue-gray) !important;
  box-shadow: var(--box-shadow) !important;
}

:host ::ng-deep .ant-select-item {
  color: var(--text-dark) !important;
  transition: var(--transition-default) !important;
}

:host ::ng-deep .ant-select-item:hover {
  background-color: var(--hover-blue-gray) !important;
}

:host ::ng-deep .ant-select-item-option-selected {
  background-color: var(--active-tab-bg) !important;
  color: var(--primary-purple) !important;
}

:host ::ng-deep .ant-select-selection-placeholder,
:host ::ng-deep .ant-select-selection-item {
  color: var(--text-dark) !important;
}

/* Button theming for proper dark/light mode support */
:host ::ng-deep .ant-btn {
  transition: var(--transition-default) !important;
}

/* Modal theming */
:host ::ng-deep .ant-modal-content {
  background-color: var(--dialog-bg) !important;
}

:host ::ng-deep .ant-modal-header {
  background-color: var(--dialog-bg) !important;
  border-bottom: 1px solid var(--hover-blue-gray) !important;
}

:host ::ng-deep .ant-modal-title {
  color: var(--text-dark) !important;
}

:host ::ng-deep .ant-modal-body {
  background-color: var(--dialog-bg) !important;
  color: var(--text-dark) !important;
}

:host ::ng-deep .ant-form-item-label > label {
  color: var(--text-dark) !important;
}

/* Spin container theming */
:host ::ng-deep .ant-spin-container {
  background-color: var(--background-light-gray) !important;
}

/* Smooth theme transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 48px;
  /* background-color: white; */
  border-radius: 8px;
  /* border: 2px dashed #d1d5db; */
  margin-top: 24px;
}
[data-theme="light"] .empty-state{
  /* background-color: var(--background-dark);
   */
   background-color: gray;
}

/* .empty-state i {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.empty-state p {
  color: #6b7280;
  margin-bottom: 16px;
} */

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .tab-header {
    flex-direction: column;
  }

  .tab-button {
    text-align: left;
  }

  .modern-table tbody td,
  .modern-table thead th {
    padding: 12px 16px;
  }

  .evaluation-table thead th,
  .evaluation-table tbody td {
    padding: 12px 16px;
  }
}

@media (max-width: 1024px) {
  .evaluation-table thead th,
  .evaluation-table tbody td {
    padding: 12px 16px;
  }
}

@media (max-width: 768px) {
  .evaluation-table {
    font-size: 12px;
  }

  .evaluation-table thead th,
  .evaluation-table tbody td {
    padding: 8px 12px;
  }

  .role-badge {
    font-size: 10px;
    padding: 2px 8px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}

/* Enhanced Button Styles */
.btn-modern {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-modern.primary {
  background-color: #3b82f6;
  color: white;
}

.btn-modern.primary:hover {
  background-color: #2563eb;
}

.btn-modern.secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.btn-modern.secondary:hover {
  background-color: #e5e7eb;
}

/* Card Container */
.card-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

/* Enhanced CSS for Input and Button Components */
action-button {
  opacity: 0.9;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.action-button:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

tr:hover .action-button {
  opacity: 1;
}

/* Dark mode button styles */
:host-context(.dark) .action-button {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

:host-context(.dark) .action-button:first-child {
  background-color: #68ca8e;
}

:host-context(.dark) .action-button:first-child:hover {
  background-color: var(--primary-purple);
  color:#FFF;
}

:host-context(.dark) .action-button:last-child {
  background-color: #742A2A;
}

:host-context(.dark) .action-button:last-child:hover {
  background-color: #9B2C2C;
}
