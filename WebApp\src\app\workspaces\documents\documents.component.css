/* Ensure dialog backdrop blur */
.fixed.inset-0 {
  backdrop-filter: blur(2px);
}

.documents-container {
  display: flex;
  height: 100%;
}

.sidebar {
  width: 250px;
  background-color: #f5f5f5;
  border-right: 1px solid #ddd;
  padding: 15px;
  overflow-y: auto;
}

.document-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 15px;
}

.document-button {
  text-align: left;
  padding: 10px;
  background-color: #1f2937;
  border: 1px solid #1f2937;
  width: 100%;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.document-button:hover {
  background-color: #111827;
}

.document-button.active {
  background-color: #007bff;
  color: white;
  border-color: #0069d9;
}

.add-document-button {
  margin-top: 15px;
  padding: 10px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.add-document-button:hover {
  background-color: #218838;
}

.content-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-buttons button {
  padding: 6px 12px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.document-content {
  margin-top: 20px;
}

.no-selection {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
  color: #6c757d;
  font-style: italic;
}

/* Add any custom styles not covered by Tailwind here */

/* Fix image size in document view */
.document-content img {
  max-width: 100%;
  height: auto;
}

/* Custom scrollbar for better visibility in dark mode */
:host-context(.dark-theme) ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

:host-context(.dark-theme) ::-webkit-scrollbar-track {
  background: #1f2937;
}

:host-context(.dark-theme) ::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

:host-context(.dark-theme) ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Custom scrollbar for light theme */
:host-context(:not(.dark-theme)) ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

:host-context(:not(.dark-theme)) ::-webkit-scrollbar-track {
  background: #f1f1f1;
}

:host-context(:not(.dark-theme)) ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

:host-context(:not(.dark-theme)) ::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Styles for the split layout */
as-split {
  --gutter-color: transparent;
  --gutter-hover-color: var(--primary-purple);
}

/* Custom styling for the gutter - completely invisible by default */
.as-split-gutter {
  background-color: transparent !important;
  transition: all 0.2s ease;
  position: relative;
  z-index: 40;
  width: 1px !important;
  /* Make the gutter very thin */
  opacity: 0;
}

/* Show a subtle gutter only on hover */
.as-split-gutter:hover {
  opacity: 1;
  width: 4px !important;
}

/* Add a subtle visual indicator for the gutter */
.as-split-gutter::after {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 1px;
  height: 100%;
  background-color: var(--hover-blue-gray);
  opacity: 0.3;
  transition: all 0.2s ease;
}

/* Enhance the gutter indicator on hover */
.as-split-gutter:hover::after {
  width: 2px;
  background-color: var(--primary-purple);
  opacity: 1;
}

/* Add resize cursor */
.as-split-gutter {
  cursor: col-resize !important;
}

/* Create a larger hover area for the gutter */
.as-split-gutter::before {
  content: "";
  position: absolute;
  top: 0;
  left: -10px;
  width: 20px;
  height: 100%;
  background-color: transparent;
  z-index: 39;
}

/* Ensure the split areas take full height */
as-split-area {
  height: 100%;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Sidebar state indicators */
.sidebar-collapsed-area {
  border-right: 2px solid transparent;
}

.sidebar-narrow-area {
  border-right: 2px solid var(--hover-blue-gray);
}

.sidebar-expanded-area {
  border-right: 2px solid var(--primary-purple);
}

/* Theme-specific sidebar styling */
:host-context(.dark-theme) .sidebar-narrow-area,
:host-context(.dark-theme) .sidebar-expanded-area {
  border-right-color: #3a3a45;
}

:host-context(:not(.dark-theme)) .sidebar-narrow-area,
:host-context(:not(.dark-theme)) .sidebar-expanded-area {
  border-right-color: var(--hover-blue-gray);
}

/* Width indicator during drag */
.width-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s ease;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.file-display-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin: 10px 0;
}

.file-item {
  position: relative;
  width: 120px;
  height: 140px;
  border: 1px solid #ddd;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-preview {
  position: relative;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.file-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.file-name {
  padding: 5px;
  text-align: center;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #f8f9fa;
}

.delete-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #dc3545;
  transition: all 0.2s;
  z-index: 5;
}

.delete-btn:hover {
  background-color: #dc3545;
  color: white;
}

.file-upload-section {
  margin-top: 15px;
}

.file-upload-btn {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  transition: all 0.2s;
}

.file-upload-btn:hover {
  background-color: #e0e0e0;
}

/* Document attachments styling */
.document-attachments-section {
  margin: 20px 0;
}

/* File gallery styling - similar to your document details display */
.file-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 10px 0;
}

.file-item {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-delete-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.file-delete-btn:hover {
  background-color: rgba(220, 53, 69, 0.8);
}

.file-upload-section {
  margin: 15px 0;
}

.file-input {
  display: none;
}

.file-upload-btn {
  padding: 6px 12px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.file-upload-btn:hover {
  background-color: #e9ecef;
}

/* Chat Section Fixed Positioning */
.chat-container-fixed {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  max-height: calc(100vh - 64px) !important;
  height: calc(100vh - 64px) !important;
  z-index: 9999 !important;
  overflow: hidden !important;
}

.chat-toggle-button-fixed {
  position: fixed !important;
  bottom: 24px !important;
  right: 24px !important;
  z-index: 9998 !important;
}

.chat-options-dropdown-fixed {
  position: absolute !important;
  z-index: 10000 !important;
}

/* Prevent chat from scrolling with page content */
.chat-container-fixed * {
  position: relative;
}

/* Ensure chat messages area scrolls independently */
.chat-messages-scroll {
  overflow-y: auto !important;
  height: 100% !important;
  max-height: 100% !important;
}

/* Fix for mobile devices */
@media (max-width: 640px) {
  .chat-container-fixed {
    width: 100vw !important;
    height: 100vh !important;
    height: 100dvh !important;
    /* Dynamic viewport height for mobile */
  }
}

/* Ensure proper stacking context */
.chat-container-fixed {
  isolation: isolate;
  contain: layout style paint;
}

/* Editor container styles with theme support */
#editor {
  min-height: 500px;
  font-family: var(--font-family);
  background-color: var(--background-white);
  color: var(--text-dark);
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

/* Ensure editor content is properly themed */
#editor .codex-editor {
  background-color: var(--background-white);
  color: var(--text-dark);
}

/* EditorJS custom styles with ::ng-deep for proper encapsulation */
::ng-deep .ce-block__content {
  max-width: 900px;
  margin: 0 auto;
  color: var(--text-dark);
}

::ng-deep .ce-header {
  font-weight: 600;
  color: var(--text-dark) !important;
  font-family: var(--font-family);
}

::ng-deep .ce-toolbar__content {
  max-width: 900px;
  background-color: var(--background-white);
}

/* Ensure editor background matches theme */
::ng-deep .codex-editor {
  background-color: var(--background-white) !important;
}

::ng-deep .codex-editor__redactor {
  background-color: var(--background-white) !important;
  color: var(--text-dark) !important;
}

/* Improve the appearance of the built-in toolbar */
::ng-deep .ce-toolbar {
  background-color: var(--background-white) !important;
  border-bottom: 1px solid var(--hover-blue-gray);
}

::ng-deep .ce-toolbar__plus {
  color: var(--primary-purple) !important;
  background-color: transparent !important;
}

::ng-deep .ce-toolbar__plus:hover {
  background-color: var(--hover-blue-gray) !important;
  color: var(--primary-purple) !important;
}

::ng-deep .ce-toolbar__settings-btn {
  color: var(--primary-purple) !important;
  background-color: transparent !important;
}

::ng-deep .ce-toolbar__settings-btn:hover {
  background-color: var(--hover-blue-gray) !important;
  color: var(--primary-purple) !important;
}

/* Remove the dash near the plus icon */
::ng-deep .ce-toolbar__actions {
  border-right: none !important;
}

::ng-deep .ce-toolbar__plus::after,
::ng-deep .ce-toolbar__plus::before {
  display: none !important;
}

::ng-deep .ce-toolbar__separator {
  display: none !important;
}

::ng-deep .ce-toolbar__content {
  border-right: none !important;
  background-color: var(--background-white) !important;
}

/* Header styles */
::ng-deep .ce-header {
  font-weight: 600;
  color: var(--text-dark);
}

::ng-deep .ce-header h1 {
  font-size: 1.75rem;
  margin-bottom: 0.75rem;
}

::ng-deep .ce-header h2 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

::ng-deep .ce-header h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

::ng-deep .ce-header h4 {
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
}

/* Paragraph styles */
::ng-deep .ce-paragraph {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-dark);
}

/* Checklist styles */
::ng-deep .cdx-checklist__item {
  padding: 0.5rem 0;
}

::ng-deep .cdx-checklist__item-checkbox {
  border-color: var(--primary-purple);
}

::ng-deep .cdx-checklist__item--checked .cdx-checklist__item-checkbox {
  background: var(--primary-purple);
}

::ng-deep .cdx-checklist__item-text {
  color: var(--text-dark);
}

::ng-deep .cdx-checklist__item--checked .cdx-checklist__item-text {
  text-decoration: line-through;
  color: var(--text-medium-gray);
}

/* Quote styles */
::ng-deep .cdx-quote {
  border-left: 3px solid var(--primary-purple);
  padding-left: 1rem;
  font-style: italic;
  color: var(--text-dark);
}

/* Warning block styles */
::ng-deep .cdx-warning {
  background-color: rgba(var(--primary-purple-rgb), 0.1);
  border-radius: 0.375rem;
  padding: 1rem;
  margin: 1rem 0;
}

::ng-deep .cdx-warning__title {
  color: var(--primary-purple);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

::ng-deep .cdx-warning__message {
  color: var(--text-dark);
}

/* Code block styles */
::ng-deep .ce-code {
  background-color: var(--background-light-gray);
  border-radius: 0.375rem;
  font-family: monospace;
  padding: 1rem;
  margin: 1rem 0;
  color: var(--text-dark);
}

::ng-deep .ce-popover-item {
  color: black !important;
}

::ng-deep .ce-popover-item__icon {
  color: black !important;
}

::ng-deep .ce-popover--inline .ce-popover__container i {
  color: black !important;


}

::ng-deep .ce-inline-tool svg {
  color: black !important;
}

/* Delimiter styles */
::ng-deep .ce-delimiter {
  color: var(--text-medium-gray);
  margin: 1rem 0;
}

/* General block styles */
::ng-deep .cdx-block {
  padding: 0.5rem 0;
  color: var(--text-dark);
}

/* Table styles */
::ng-deep .tc-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

::ng-deep .tc-table td,
::ng-deep .tc-table th {
  border: 1px solid var(--hover-blue-gray);
  padding: 0.5rem;
  color: var(--text-dark);
}

/* List styles */
::ng-deep .cdx-list {
  margin: 1rem 0;
  padding-left: 1.5rem;
  color: var(--text-dark);
}

::ng-deep .cdx-list__item {
  margin-bottom: 0.25rem;
  color: var(--text-dark);
}

/* Image styles */
::ng-deep .image-tool {
  margin: 1rem 0;
}

::ng-deep .image-tool__image {
  max-width: 100%;
  border-radius: 0.375rem;
}

::ng-deep .image-tool__caption {
  color: var(--text-medium-gray);
  font-size: 0.875rem;
  text-align: center;
  margin-top: 0.5rem;
}

/* Link styles */
::ng-deep .link-tool__content {
  border: 1px solid var(--hover-blue-gray);
  border-radius: 0.375rem;
  padding: 1rem;
  margin: 1rem 0;
}

::ng-deep .link-tool__title {
  color: var(--text-dark);
  font-weight: 600;
}

::ng-deep .link-tool__description {
  color: var(--text-medium-gray);
}

/* Dark theme support for EditorJS - Using .dark class selector */
:host-context(.dark) ::ng-deep #editor,
:host-context(.dark) ::ng-deep .codex-editor,
:host-context(.dark) ::ng-deep .codex-editor__redactor {
  background-color: var(--background-white) !important;
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-toolbar {
  background-color: var(--background-white) !important;
  border-bottom-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-toolbar__plus,
:host-context(.dark) ::ng-deep .ce-toolbar__settings-btn {
  color: var(--primary-purple) !important;
  background-color: transparent !important;
}

:host-context(.dark) ::ng-deep .ce-toolbar__plus:hover,
:host-context(.dark) ::ng-deep .ce-toolbar__settings-btn:hover {
  background-color: var(--hover-blue-gray) !important;
  color: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .ce-toolbar__content {
  background-color: var(--background-white) !important;
}

:host-context(.dark) ::ng-deep .ce-popover {
  background-color: var(--background-white) !important;
  border-color: var(--hover-blue-gray) !important;
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-popover__item {
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-popover__item:hover {
  background-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-popover__item-icon {
  color: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .ce-popover__search {
  background-color: var(--background-white) !important;
  border-color: var(--hover-blue-gray) !important;
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-popover__search::placeholder {
  color: var(--text-medium-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-inline-toolbar {
  background-color: var(--background-white) !important;
  border-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-inline-toolbar__buttons {
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-inline-tool {
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-inline-tool:hover {
  background-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-inline-tool--active {
  color: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .ce-conversion-toolbar {
  background-color: var(--background-white) !important;
  border-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-conversion-tool {
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-conversion-tool:hover {
  background-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-conversion-tool__icon {
  color: var(--primary-purple) !important;
}

/* Dark theme content block styling */
:host-context(.dark) ::ng-deep .ce-header,
:host-context(.dark) ::ng-deep .ce-paragraph,
:host-context(.dark) ::ng-deep .cdx-checklist__item-text,
:host-context(.dark) ::ng-deep .cdx-warning__message,
:host-context(.dark) ::ng-deep .ce-code,
:host-context(.dark) ::ng-deep .cdx-block,
:host-context(.dark) ::ng-deep .tc-table td,
:host-context(.dark) ::ng-deep .tc-table th,
:host-context(.dark) ::ng-deep .cdx-list__item,
:host-context(.dark) ::ng-deep .link-tool__title,
:host-context(.dark) ::ng-deep .cdx-quote {
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .cdx-checklist__item-checkbox {
  border-color: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .cdx-checklist__item--checked .cdx-checklist__item-checkbox {
  background: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .cdx-checklist__item--checked .cdx-checklist__item-text {
  color: var(--text-medium-gray) !important;
}

:host-context(.dark) ::ng-deep .cdx-warning {
  background-color: rgba(var(--primary-purple-rgb), 0.1) !important;
}

:host-context(.dark) ::ng-deep .cdx-warning__title {
  color: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .cdx-quote {
  border-left-color: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .ce-code {
  background-color: var(--hover-blue-gray) !important;
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-delimiter {
  color: var(--text-medium-gray) !important;
}

:host-context(.dark) ::ng-deep .tc-table td,
:host-context(.dark) ::ng-deep .tc-table th {
  border-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .link-tool__content {
  border-color: var(--hover-blue-gray) !important;
  background-color: var(--background-white) !important;
}

:host-context(.dark) ::ng-deep .link-tool__description {
  color: var(--text-medium-gray) !important;
}

/* Placeholder text styling */
::ng-deep .ce-paragraph[data-placeholder]::before {
  color: var(--text-medium-gray) !important;
  opacity: 0.7;
  font-style: italic;
}

:host-context(.dark) ::ng-deep .ce-paragraph[data-placeholder]::before {
  color: var(--text-medium-gray) !important;
  opacity: 0.8;
}

/* Focus states for better accessibility */
::ng-deep .ce-block--focused {
  background-color: rgba(var(--primary-purple-rgb), 0.05) !important;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

:host-context(.dark) ::ng-deep .ce-block--focused {
  background-color: rgba(var(--primary-purple-rgb), 0.1) !important;
}

/* Improve selection styling */
::ng-deep .ce-block--selected {
  background-color: rgba(var(--primary-purple-rgb), 0.1) !important;
  border-radius: 0.25rem;
}

:host-context(.dark) ::ng-deep .ce-block--selected {
  background-color: rgba(var(--primary-purple-rgb), 0.15) !important;
}

/* Sticky header styling */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Responsive adjustments */
@media (max-width: 640px) {

  ::ng-deep .ce-block__content,
  ::ng-deep .ce-toolbar__content {
    max-width: 100%;
    padding: 0 1rem;
  }

  ::ng-deep .ce-header h1 {
    font-size: 1.5rem;
  }

  ::ng-deep .ce-header h2 {
    font-size: 1.25rem;
  }

  ::ng-deep .ce-header h3 {
    font-size: 1.125rem;
  }

  ::ng-deep .ce-header h4 {
    font-size: 1rem;
  }
}

/* Document card styling */
.document-card {
  min-height: 200px;
  max-height: 280px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.document-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Card favorite button styling */
.document-card .favorite-button {
  transition: all 0.2s ease;
}

.favorite-button:hover .ri-star-line {
  color: #fbbf24 !important; /* yellow-400 */
}

.favorite-button .ri-star-fill {
  color: #fbbf24 !important; /* yellow-400 */
}

/* Text truncation utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Card hover effects */
.document-card .group-hover\:opacity-100 {
  transition: opacity 0.2s ease;
}

/* Responsive grid adjustments */
@media (max-width: 768px) {
  .document-card {
    min-height: 180px;
    max-height: 250px;
  }
}

/* Dark theme support for cards */
:host-context(.dark) .document-card {
  background-color: var(--background-white);
  border-color: var(--border-light);
}

:host-context(.dark) .document-card:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Text selection styling for better visibility */
::ng-deep .ce-paragraph::selection,
::ng-deep .ce-header::selection,
::ng-deep .cdx-list__item::selection,
::ng-deep .cdx-checklist__item-text::selection,
::ng-deep .cdx-quote::selection,
::ng-deep .ce-code::selection,
::ng-deep .cdx-block::selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}

/* Dark theme text selection */
:host-context(.dark) ::ng-deep .ce-paragraph::selection,
:host-context(.dark) ::ng-deep .ce-header::selection,
:host-context(.dark) ::ng-deep .cdx-list__item::selection,
:host-context(.dark) ::ng-deep .cdx-checklist__item-text::selection,
:host-context(.dark) ::ng-deep .cdx-quote::selection,
:host-context(.dark) ::ng-deep .ce-code::selection,
:host-context(.dark) ::ng-deep .cdx-block::selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}

/* Global text selection for the editor container */
#editor::selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}

#editor *::selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}

/* Ensure selection works in dark theme */
:host-context(.dark) #editor::selection,
:host-context(.dark) #editor *::selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}

/* Mozilla Firefox selection styling */
::ng-deep .ce-paragraph::-moz-selection,
::ng-deep .ce-header::-moz-selection,
::ng-deep .cdx-list__item::-moz-selection,
::ng-deep .cdx-checklist__item-text::-moz-selection,
::ng-deep .cdx-quote::-moz-selection,
::ng-deep .ce-code::-moz-selection,
::ng-deep .cdx-block::-moz-selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}

#editor::-moz-selection,
#editor *::-moz-selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}
