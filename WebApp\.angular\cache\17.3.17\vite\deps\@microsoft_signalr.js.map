{"version": 3, "sources": ["../../../../../node_modules/@microsoft/signalr/dist/esm/Errors.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/HttpClient.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/ILogger.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/Loggers.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/Utils.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/FetchHttpClient.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/XhrHttpClient.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/DefaultHttpClient.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/TextMessageFormat.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/HandshakeProtocol.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/IHubProtocol.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/Subject.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/MessageBuffer.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/HubConnection.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/DefaultReconnectPolicy.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/HeaderNames.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/AccessTokenHttpClient.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/ITransport.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/AbortController.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/LongPollingTransport.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/ServerSentEventsTransport.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/WebSocketTransport.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/HttpConnection.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/JsonHubProtocol.js", "../../../../../node_modules/@microsoft/signalr/dist/esm/HubConnectionBuilder.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n/** Error thrown when an HTTP request fails. */\r\nexport class HttpError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     * @param {number} statusCode The HTTP status code represented by this error.\r\n     */\r\n    constructor(errorMessage, statusCode) {\r\n        const trueProto = new.target.prototype;\r\n        super(`${errorMessage}: Status code '${statusCode}'`);\r\n        this.statusCode = statusCode;\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when a timeout elapses. */\r\nexport class TimeoutError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.TimeoutError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage = \"A timeout occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when an action is aborted. */\r\nexport class AbortError extends Error {\r\n    /** Constructs a new instance of {@link AbortError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage = \"An abort occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when the selected transport is unsupported by the browser. */\r\n/** @private */\r\nexport class UnsupportedTransportError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.UnsupportedTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message, transport) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'UnsupportedTransportError';\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when the selected transport is disabled by the browser. */\r\n/** @private */\r\nexport class DisabledTransportError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.DisabledTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message, transport) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'DisabledTransportError';\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when the selected transport cannot be started. */\r\n/** @private */\r\nexport class FailedToStartTransportError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToStartTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message, transport) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'FailedToStartTransportError';\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when the negotiation with the server failed to complete. */\r\n/** @private */\r\nexport class FailedToNegotiateWithServerError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToNegotiateWithServerError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     */\r\n    constructor(message) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.errorType = 'FailedToNegotiateWithServerError';\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when multiple errors have occurred. */\r\n/** @private */\r\nexport class AggregateErrors extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.AggregateErrors}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {Error[]} innerErrors The collection of errors this error is aggregating.\r\n     */\r\n    constructor(message, innerErrors) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.innerErrors = innerErrors;\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n/** Represents an HTTP response. */\r\nexport class HttpResponse {\r\n    constructor(statusCode, statusText, content) {\r\n        this.statusCode = statusCode;\r\n        this.statusText = statusText;\r\n        this.content = content;\r\n    }\r\n}\r\n/** Abstraction over an HTTP client.\r\n *\r\n * This class provides an abstraction over an HTTP client so that a different implementation can be provided on different platforms.\r\n */\r\nexport class HttpClient {\r\n    get(url, options) {\r\n        return this.send({\r\n            ...options,\r\n            method: \"GET\",\r\n            url,\r\n        });\r\n    }\r\n    post(url, options) {\r\n        return this.send({\r\n            ...options,\r\n            method: \"POST\",\r\n            url,\r\n        });\r\n    }\r\n    delete(url, options) {\r\n        return this.send({\r\n            ...options,\r\n            method: \"DELETE\",\r\n            url,\r\n        });\r\n    }\r\n    /** Gets all cookies that apply to the specified URL.\r\n     *\r\n     * @param url The URL that the cookies are valid for.\r\n     * @returns {string} A string containing all the key-value cookie pairs for the specified URL.\r\n     */\r\n    // @ts-ignore\r\n    getCookieString(url) {\r\n        return \"\";\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// These values are designed to match the ASP.NET Log Levels since that's the pattern we're emulating here.\r\n/** Indicates the severity of a log message.\r\n *\r\n * Log Levels are ordered in increasing severity. So `Debug` is more severe than `Trace`, etc.\r\n */\r\nexport var LogLevel;\r\n(function (LogLevel) {\r\n    /** Log level for very low severity diagnostic messages. */\r\n    LogLevel[LogLevel[\"Trace\"] = 0] = \"Trace\";\r\n    /** Log level for low severity diagnostic messages. */\r\n    LogLevel[LogLevel[\"Debug\"] = 1] = \"Debug\";\r\n    /** Log level for informational diagnostic messages. */\r\n    LogLevel[LogLevel[\"Information\"] = 2] = \"Information\";\r\n    /** Log level for diagnostic messages that indicate a non-fatal problem. */\r\n    LogLevel[LogLevel[\"Warning\"] = 3] = \"Warning\";\r\n    /** Log level for diagnostic messages that indicate a failure in the current operation. */\r\n    LogLevel[LogLevel[\"Error\"] = 4] = \"Error\";\r\n    /** Log level for diagnostic messages that indicate a failure that will terminate the entire application. */\r\n    LogLevel[LogLevel[\"Critical\"] = 5] = \"Critical\";\r\n    /** The highest possible log level. Used when configuring logging to indicate that no log messages should be emitted. */\r\n    LogLevel[LogLevel[\"None\"] = 6] = \"None\";\r\n})(LogLevel || (LogLevel = {}));\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n/** A logger that does nothing when log messages are sent to it. */\r\nexport class NullLogger {\r\n    constructor() { }\r\n    /** @inheritDoc */\r\n    // eslint-disable-next-line\r\n    log(_logLevel, _message) {\r\n    }\r\n}\r\n/** The singleton instance of the {@link @microsoft/signalr.NullLogger}. */\r\nNullLogger.instance = new NullLogger();\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { NullLogger } from \"./Loggers\";\r\n// Version token that will be replaced by the prepack command\r\n/** The version of the SignalR client. */\r\nexport const VERSION = \"8.0.7\";\r\n/** @private */\r\nexport class Arg {\r\n    static isRequired(val, name) {\r\n        if (val === null || val === undefined) {\r\n            throw new Error(`The '${name}' argument is required.`);\r\n        }\r\n    }\r\n    static isNotEmpty(val, name) {\r\n        if (!val || val.match(/^\\s*$/)) {\r\n            throw new Error(`The '${name}' argument should not be empty.`);\r\n        }\r\n    }\r\n    static isIn(val, values, name) {\r\n        // TypeScript enums have keys for **both** the name and the value of each enum member on the type itself.\r\n        if (!(val in values)) {\r\n            throw new Error(`Unknown ${name} value: ${val}.`);\r\n        }\r\n    }\r\n}\r\n/** @private */\r\nexport class Platform {\r\n    // react-native has a window but no document so we should check both\r\n    static get isBrowser() {\r\n        return !Platform.isNode && typeof window === \"object\" && typeof window.document === \"object\";\r\n    }\r\n    // WebWorkers don't have a window object so the isBrowser check would fail\r\n    static get isWebWorker() {\r\n        return !Platform.isNode && typeof self === \"object\" && \"importScripts\" in self;\r\n    }\r\n    // react-native has a window but no document\r\n    static get isReactNative() {\r\n        return !Platform.isNode && typeof window === \"object\" && typeof window.document === \"undefined\";\r\n    }\r\n    // Node apps shouldn't have a window object, but WebWorkers don't either\r\n    // so we need to check for both WebWorker and window\r\n    static get isNode() {\r\n        return typeof process !== \"undefined\" && process.release && process.release.name === \"node\";\r\n    }\r\n}\r\n/** @private */\r\nexport function getDataDetail(data, includeContent) {\r\n    let detail = \"\";\r\n    if (isArrayBuffer(data)) {\r\n        detail = `Binary data of length ${data.byteLength}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${formatArrayBuffer(data)}'`;\r\n        }\r\n    }\r\n    else if (typeof data === \"string\") {\r\n        detail = `String data of length ${data.length}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${data}'`;\r\n        }\r\n    }\r\n    return detail;\r\n}\r\n/** @private */\r\nexport function formatArrayBuffer(data) {\r\n    const view = new Uint8Array(data);\r\n    // Uint8Array.map only supports returning another Uint8Array?\r\n    let str = \"\";\r\n    view.forEach((num) => {\r\n        const pad = num < 16 ? \"0\" : \"\";\r\n        str += `0x${pad}${num.toString(16)} `;\r\n    });\r\n    // Trim of trailing space.\r\n    return str.substr(0, str.length - 1);\r\n}\r\n// Also in signalr-protocol-msgpack/Utils.ts\r\n/** @private */\r\nexport function isArrayBuffer(val) {\r\n    return val && typeof ArrayBuffer !== \"undefined\" &&\r\n        (val instanceof ArrayBuffer ||\r\n            // Sometimes we get an ArrayBuffer that doesn't satisfy instanceof\r\n            (val.constructor && val.constructor.name === \"ArrayBuffer\"));\r\n}\r\n/** @private */\r\nexport async function sendMessage(logger, transportName, httpClient, url, content, options) {\r\n    const headers = {};\r\n    const [name, value] = getUserAgentHeader();\r\n    headers[name] = value;\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) sending data. ${getDataDetail(content, options.logMessageContent)}.`);\r\n    const responseType = isArrayBuffer(content) ? \"arraybuffer\" : \"text\";\r\n    const response = await httpClient.post(url, {\r\n        content,\r\n        headers: { ...headers, ...options.headers },\r\n        responseType,\r\n        timeout: options.timeout,\r\n        withCredentials: options.withCredentials,\r\n    });\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) request complete. Response status: ${response.statusCode}.`);\r\n}\r\n/** @private */\r\nexport function createLogger(logger) {\r\n    if (logger === undefined) {\r\n        return new ConsoleLogger(LogLevel.Information);\r\n    }\r\n    if (logger === null) {\r\n        return NullLogger.instance;\r\n    }\r\n    if (logger.log !== undefined) {\r\n        return logger;\r\n    }\r\n    return new ConsoleLogger(logger);\r\n}\r\n/** @private */\r\nexport class SubjectSubscription {\r\n    constructor(subject, observer) {\r\n        this._subject = subject;\r\n        this._observer = observer;\r\n    }\r\n    dispose() {\r\n        const index = this._subject.observers.indexOf(this._observer);\r\n        if (index > -1) {\r\n            this._subject.observers.splice(index, 1);\r\n        }\r\n        if (this._subject.observers.length === 0 && this._subject.cancelCallback) {\r\n            this._subject.cancelCallback().catch((_) => { });\r\n        }\r\n    }\r\n}\r\n/** @private */\r\nexport class ConsoleLogger {\r\n    constructor(minimumLogLevel) {\r\n        this._minLevel = minimumLogLevel;\r\n        this.out = console;\r\n    }\r\n    log(logLevel, message) {\r\n        if (logLevel >= this._minLevel) {\r\n            const msg = `[${new Date().toISOString()}] ${LogLevel[logLevel]}: ${message}`;\r\n            switch (logLevel) {\r\n                case LogLevel.Critical:\r\n                case LogLevel.Error:\r\n                    this.out.error(msg);\r\n                    break;\r\n                case LogLevel.Warning:\r\n                    this.out.warn(msg);\r\n                    break;\r\n                case LogLevel.Information:\r\n                    this.out.info(msg);\r\n                    break;\r\n                default:\r\n                    // console.debug only goes to attached debuggers in Node, so we use console.log for Trace and Debug\r\n                    this.out.log(msg);\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n}\r\n/** @private */\r\nexport function getUserAgentHeader() {\r\n    let userAgentHeaderName = \"X-SignalR-User-Agent\";\r\n    if (Platform.isNode) {\r\n        userAgentHeaderName = \"User-Agent\";\r\n    }\r\n    return [userAgentHeaderName, constructUserAgent(VERSION, getOsName(), getRuntime(), getRuntimeVersion())];\r\n}\r\n/** @private */\r\nexport function constructUserAgent(version, os, runtime, runtimeVersion) {\r\n    // Microsoft SignalR/[Version] ([Detailed Version]; [Operating System]; [Runtime]; [Runtime Version])\r\n    let userAgent = \"Microsoft SignalR/\";\r\n    const majorAndMinor = version.split(\".\");\r\n    userAgent += `${majorAndMinor[0]}.${majorAndMinor[1]}`;\r\n    userAgent += ` (${version}; `;\r\n    if (os && os !== \"\") {\r\n        userAgent += `${os}; `;\r\n    }\r\n    else {\r\n        userAgent += \"Unknown OS; \";\r\n    }\r\n    userAgent += `${runtime}`;\r\n    if (runtimeVersion) {\r\n        userAgent += `; ${runtimeVersion}`;\r\n    }\r\n    else {\r\n        userAgent += \"; Unknown Runtime Version\";\r\n    }\r\n    userAgent += \")\";\r\n    return userAgent;\r\n}\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getOsName() {\r\n    if (Platform.isNode) {\r\n        switch (process.platform) {\r\n            case \"win32\":\r\n                return \"Windows NT\";\r\n            case \"darwin\":\r\n                return \"macOS\";\r\n            case \"linux\":\r\n                return \"Linux\";\r\n            default:\r\n                return process.platform;\r\n        }\r\n    }\r\n    else {\r\n        return \"\";\r\n    }\r\n}\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getRuntimeVersion() {\r\n    if (Platform.isNode) {\r\n        return process.versions.node;\r\n    }\r\n    return undefined;\r\n}\r\nfunction getRuntime() {\r\n    if (Platform.isNode) {\r\n        return \"NodeJS\";\r\n    }\r\n    else {\r\n        return \"Browser\";\r\n    }\r\n}\r\n/** @private */\r\nexport function getErrorString(e) {\r\n    if (e.stack) {\r\n        return e.stack;\r\n    }\r\n    else if (e.message) {\r\n        return e.message;\r\n    }\r\n    return `${e}`;\r\n}\r\n/** @private */\r\nexport function getGlobalThis() {\r\n    // globalThis is semi-new and not available in Node until v12\r\n    if (typeof globalThis !== \"undefined\") {\r\n        return globalThis;\r\n    }\r\n    if (typeof self !== \"undefined\") {\r\n        return self;\r\n    }\r\n    if (typeof window !== \"undefined\") {\r\n        return window;\r\n    }\r\n    if (typeof global !== \"undefined\") {\r\n        return global;\r\n    }\r\n    throw new Error(\"could not find global\");\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpResponse } from \"./HttpClient\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { Platform, getGlobalThis, isArrayBuffer } from \"./Utils\";\r\nexport class FetchHttpClient extends HttpClient {\r\n    constructor(logger) {\r\n        super();\r\n        this._logger = logger;\r\n        // Node added a fetch implementation to the global scope starting in v18.\r\n        // We need to add a cookie jar in node to be able to share cookies with WebSocket\r\n        if (typeof fetch === \"undefined\" || Platform.isNode) {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n            // Cookies aren't automatically handled in Node so we need to add a CookieJar to preserve cookies across requests\r\n            this._jar = new (requireFunc(\"tough-cookie\")).CookieJar();\r\n            if (typeof fetch === \"undefined\") {\r\n                this._fetchType = requireFunc(\"node-fetch\");\r\n            }\r\n            else {\r\n                // Use fetch from Node if available\r\n                this._fetchType = fetch;\r\n            }\r\n            // node-fetch doesn't have a nice API for getting and setting cookies\r\n            // fetch-cookie will wrap a fetch implementation with a default CookieJar or a provided one\r\n            this._fetchType = requireFunc(\"fetch-cookie\")(this._fetchType, this._jar);\r\n        }\r\n        else {\r\n            this._fetchType = fetch.bind(getGlobalThis());\r\n        }\r\n        if (typeof AbortController === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n            // Node needs EventListener methods on AbortController which our custom polyfill doesn't provide\r\n            this._abortControllerType = requireFunc(\"abort-controller\");\r\n        }\r\n        else {\r\n            this._abortControllerType = AbortController;\r\n        }\r\n    }\r\n    /** @inheritDoc */\r\n    async send(request) {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            throw new AbortError();\r\n        }\r\n        if (!request.method) {\r\n            throw new Error(\"No method defined.\");\r\n        }\r\n        if (!request.url) {\r\n            throw new Error(\"No url defined.\");\r\n        }\r\n        const abortController = new this._abortControllerType();\r\n        let error;\r\n        // Hook our abortSignal into the abort controller\r\n        if (request.abortSignal) {\r\n            request.abortSignal.onabort = () => {\r\n                abortController.abort();\r\n                error = new AbortError();\r\n            };\r\n        }\r\n        // If a timeout has been passed in, setup a timeout to call abort\r\n        // Type needs to be any to fit window.setTimeout and NodeJS.setTimeout\r\n        let timeoutId = null;\r\n        if (request.timeout) {\r\n            const msTimeout = request.timeout;\r\n            timeoutId = setTimeout(() => {\r\n                abortController.abort();\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                error = new TimeoutError();\r\n            }, msTimeout);\r\n        }\r\n        if (request.content === \"\") {\r\n            request.content = undefined;\r\n        }\r\n        if (request.content) {\r\n            // Explicitly setting the Content-Type header for React Native on Android platform.\r\n            request.headers = request.headers || {};\r\n            if (isArrayBuffer(request.content)) {\r\n                request.headers[\"Content-Type\"] = \"application/octet-stream\";\r\n            }\r\n            else {\r\n                request.headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\r\n            }\r\n        }\r\n        let response;\r\n        try {\r\n            response = await this._fetchType(request.url, {\r\n                body: request.content,\r\n                cache: \"no-cache\",\r\n                credentials: request.withCredentials === true ? \"include\" : \"same-origin\",\r\n                headers: {\r\n                    \"X-Requested-With\": \"XMLHttpRequest\",\r\n                    ...request.headers,\r\n                },\r\n                method: request.method,\r\n                mode: \"cors\",\r\n                redirect: \"follow\",\r\n                signal: abortController.signal,\r\n            });\r\n        }\r\n        catch (e) {\r\n            if (error) {\r\n                throw error;\r\n            }\r\n            this._logger.log(LogLevel.Warning, `Error from HTTP request. ${e}.`);\r\n            throw e;\r\n        }\r\n        finally {\r\n            if (timeoutId) {\r\n                clearTimeout(timeoutId);\r\n            }\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = null;\r\n            }\r\n        }\r\n        if (!response.ok) {\r\n            const errorMessage = await deserializeContent(response, \"text\");\r\n            throw new HttpError(errorMessage || response.statusText, response.status);\r\n        }\r\n        const content = deserializeContent(response, request.responseType);\r\n        const payload = await content;\r\n        return new HttpResponse(response.status, response.statusText, payload);\r\n    }\r\n    getCookieString(url) {\r\n        let cookies = \"\";\r\n        if (Platform.isNode && this._jar) {\r\n            // @ts-ignore: unused variable\r\n            this._jar.getCookies(url, (e, c) => cookies = c.join(\"; \"));\r\n        }\r\n        return cookies;\r\n    }\r\n}\r\nfunction deserializeContent(response, responseType) {\r\n    let content;\r\n    switch (responseType) {\r\n        case \"arraybuffer\":\r\n            content = response.arrayBuffer();\r\n            break;\r\n        case \"text\":\r\n            content = response.text();\r\n            break;\r\n        case \"blob\":\r\n        case \"document\":\r\n        case \"json\":\r\n            throw new Error(`${responseType} is not supported.`);\r\n        default:\r\n            content = response.text();\r\n            break;\r\n    }\r\n    return content;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpResponse } from \"./HttpClient\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\nexport class XhrHttpClient extends HttpClient {\r\n    constructor(logger) {\r\n        super();\r\n        this._logger = logger;\r\n    }\r\n    /** @inheritDoc */\r\n    send(request) {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n            const xhr = new XMLHttpRequest();\r\n            xhr.open(request.method, request.url, true);\r\n            xhr.withCredentials = request.withCredentials === undefined ? true : request.withCredentials;\r\n            xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\r\n            if (request.content === \"\") {\r\n                request.content = undefined;\r\n            }\r\n            if (request.content) {\r\n                // Explicitly setting the Content-Type header for React Native on Android platform.\r\n                if (isArrayBuffer(request.content)) {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"application/octet-stream\");\r\n                }\r\n                else {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"text/plain;charset=UTF-8\");\r\n                }\r\n            }\r\n            const headers = request.headers;\r\n            if (headers) {\r\n                Object.keys(headers)\r\n                    .forEach((header) => {\r\n                    xhr.setRequestHeader(header, headers[header]);\r\n                });\r\n            }\r\n            if (request.responseType) {\r\n                xhr.responseType = request.responseType;\r\n            }\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = () => {\r\n                    xhr.abort();\r\n                    reject(new AbortError());\r\n                };\r\n            }\r\n            if (request.timeout) {\r\n                xhr.timeout = request.timeout;\r\n            }\r\n            xhr.onload = () => {\r\n                if (request.abortSignal) {\r\n                    request.abortSignal.onabort = null;\r\n                }\r\n                if (xhr.status >= 200 && xhr.status < 300) {\r\n                    resolve(new HttpResponse(xhr.status, xhr.statusText, xhr.response || xhr.responseText));\r\n                }\r\n                else {\r\n                    reject(new HttpError(xhr.response || xhr.responseText || xhr.statusText, xhr.status));\r\n                }\r\n            };\r\n            xhr.onerror = () => {\r\n                this._logger.log(LogLevel.Warning, `Error from HTTP request. ${xhr.status}: ${xhr.statusText}.`);\r\n                reject(new HttpError(xhr.statusText, xhr.status));\r\n            };\r\n            xhr.ontimeout = () => {\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                reject(new TimeoutError());\r\n            };\r\n            xhr.send(request.content);\r\n        });\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { AbortError } from \"./Errors\";\r\nimport { FetchHttpClient } from \"./FetchHttpClient\";\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { Platform } from \"./Utils\";\r\nimport { XhrHttpClient } from \"./XhrHttpClient\";\r\n/** Default implementation of {@link @microsoft/signalr.HttpClient}. */\r\nexport class DefaultHttpClient extends HttpClient {\r\n    /** Creates a new instance of the {@link @microsoft/signalr.DefaultHttpClient}, using the provided {@link @microsoft/signalr.ILogger} to log messages. */\r\n    constructor(logger) {\r\n        super();\r\n        if (typeof fetch !== \"undefined\" || Platform.isNode) {\r\n            this._httpClient = new FetchHttpClient(logger);\r\n        }\r\n        else if (typeof XMLHttpRequest !== \"undefined\") {\r\n            this._httpClient = new XhrHttpClient(logger);\r\n        }\r\n        else {\r\n            throw new Error(\"No usable HttpClient found.\");\r\n        }\r\n    }\r\n    /** @inheritDoc */\r\n    send(request) {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n        return this._httpClient.send(request);\r\n    }\r\n    getCookieString(url) {\r\n        return this._httpClient.getCookieString(url);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// Not exported from index\r\n/** @private */\r\nexport class TextMessageFormat {\r\n    static write(output) {\r\n        return `${output}${TextMessageFormat.RecordSeparator}`;\r\n    }\r\n    static parse(input) {\r\n        if (input[input.length - 1] !== TextMessageFormat.RecordSeparator) {\r\n            throw new Error(\"Message is incomplete.\");\r\n        }\r\n        const messages = input.split(TextMessageFormat.RecordSeparator);\r\n        messages.pop();\r\n        return messages;\r\n    }\r\n}\r\nTextMessageFormat.RecordSeparatorCode = 0x1e;\r\nTextMessageFormat.RecordSeparator = String.fromCharCode(TextMessageFormat.RecordSeparatorCode);\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n/** @private */\r\nexport class HandshakeProtocol {\r\n    // Handshake request is always JSON\r\n    writeHandshakeRequest(handshakeRequest) {\r\n        return TextMessageFormat.write(JSON.stringify(handshakeRequest));\r\n    }\r\n    parseHandshakeResponse(data) {\r\n        let messageData;\r\n        let remainingData;\r\n        if (isArrayBuffer(data)) {\r\n            // Format is binary but still need to read JSON text from handshake response\r\n            const binaryData = new Uint8Array(data);\r\n            const separatorIndex = binaryData.indexOf(TextMessageFormat.RecordSeparatorCode);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = String.fromCharCode.apply(null, Array.prototype.slice.call(binaryData.slice(0, responseLength)));\r\n            remainingData = (binaryData.byteLength > responseLength) ? binaryData.slice(responseLength).buffer : null;\r\n        }\r\n        else {\r\n            const textData = data;\r\n            const separatorIndex = textData.indexOf(TextMessageFormat.RecordSeparator);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = textData.substring(0, responseLength);\r\n            remainingData = (textData.length > responseLength) ? textData.substring(responseLength) : null;\r\n        }\r\n        // At this point we should have just the single handshake message\r\n        const messages = TextMessageFormat.parse(messageData);\r\n        const response = JSON.parse(messages[0]);\r\n        if (response.type) {\r\n            throw new Error(\"Expected a handshake response from the server.\");\r\n        }\r\n        const responseMessage = response;\r\n        // multiple messages could have arrived with handshake\r\n        // return additional data to be parsed as usual, or null if all parsed\r\n        return [remainingData, responseMessage];\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n/** Defines the type of a Hub Message. */\r\nexport var MessageType;\r\n(function (MessageType) {\r\n    /** Indicates the message is an Invocation message and implements the {@link @microsoft/signalr.InvocationMessage} interface. */\r\n    MessageType[MessageType[\"Invocation\"] = 1] = \"Invocation\";\r\n    /** Indicates the message is a StreamItem message and implements the {@link @microsoft/signalr.StreamItemMessage} interface. */\r\n    MessageType[MessageType[\"StreamItem\"] = 2] = \"StreamItem\";\r\n    /** Indicates the message is a Completion message and implements the {@link @microsoft/signalr.CompletionMessage} interface. */\r\n    MessageType[MessageType[\"Completion\"] = 3] = \"Completion\";\r\n    /** Indicates the message is a Stream Invocation message and implements the {@link @microsoft/signalr.StreamInvocationMessage} interface. */\r\n    MessageType[MessageType[\"StreamInvocation\"] = 4] = \"StreamInvocation\";\r\n    /** Indicates the message is a Cancel Invocation message and implements the {@link @microsoft/signalr.CancelInvocationMessage} interface. */\r\n    MessageType[MessageType[\"CancelInvocation\"] = 5] = \"CancelInvocation\";\r\n    /** Indicates the message is a Ping message and implements the {@link @microsoft/signalr.PingMessage} interface. */\r\n    MessageType[MessageType[\"Ping\"] = 6] = \"Ping\";\r\n    /** Indicates the message is a Close message and implements the {@link @microsoft/signalr.CloseMessage} interface. */\r\n    MessageType[MessageType[\"Close\"] = 7] = \"Close\";\r\n    MessageType[MessageType[\"Ack\"] = 8] = \"Ack\";\r\n    MessageType[MessageType[\"Sequence\"] = 9] = \"Sequence\";\r\n})(MessageType || (MessageType = {}));\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { SubjectSubscription } from \"./Utils\";\r\n/** Stream implementation to stream items to the server. */\r\nexport class Subject {\r\n    constructor() {\r\n        this.observers = [];\r\n    }\r\n    next(item) {\r\n        for (const observer of this.observers) {\r\n            observer.next(item);\r\n        }\r\n    }\r\n    error(err) {\r\n        for (const observer of this.observers) {\r\n            if (observer.error) {\r\n                observer.error(err);\r\n            }\r\n        }\r\n    }\r\n    complete() {\r\n        for (const observer of this.observers) {\r\n            if (observer.complete) {\r\n                observer.complete();\r\n            }\r\n        }\r\n    }\r\n    subscribe(observer) {\r\n        this.observers.push(observer);\r\n        return new SubjectSubscription(this, observer);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { MessageType } from \"./IHubProtocol\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n/** @private */\r\nexport class MessageBuffer {\r\n    constructor(protocol, connection, bufferSize) {\r\n        this._bufferSize = 100000;\r\n        this._messages = [];\r\n        this._totalMessageCount = 0;\r\n        this._waitForSequenceMessage = false;\r\n        // Message IDs start at 1 and always increment by 1\r\n        this._nextReceivingSequenceId = 1;\r\n        this._latestReceivedSequenceId = 0;\r\n        this._bufferedByteCount = 0;\r\n        this._reconnectInProgress = false;\r\n        this._protocol = protocol;\r\n        this._connection = connection;\r\n        this._bufferSize = bufferSize;\r\n    }\r\n    async _send(message) {\r\n        const serializedMessage = this._protocol.writeMessage(message);\r\n        let backpressurePromise = Promise.resolve();\r\n        // Only count invocation messages. Acks, pings, etc. don't need to be resent on reconnect\r\n        if (this._isInvocationMessage(message)) {\r\n            this._totalMessageCount++;\r\n            let backpressurePromiseResolver = () => { };\r\n            let backpressurePromiseRejector = () => { };\r\n            if (isArrayBuffer(serializedMessage)) {\r\n                this._bufferedByteCount += serializedMessage.byteLength;\r\n            }\r\n            else {\r\n                this._bufferedByteCount += serializedMessage.length;\r\n            }\r\n            if (this._bufferedByteCount >= this._bufferSize) {\r\n                backpressurePromise = new Promise((resolve, reject) => {\r\n                    backpressurePromiseResolver = resolve;\r\n                    backpressurePromiseRejector = reject;\r\n                });\r\n            }\r\n            this._messages.push(new BufferedItem(serializedMessage, this._totalMessageCount, backpressurePromiseResolver, backpressurePromiseRejector));\r\n        }\r\n        try {\r\n            // If this is set it means we are reconnecting or resending\r\n            // We don't want to send on a disconnected connection\r\n            // And we don't want to send if resend is running since that would mean sending\r\n            // this message twice\r\n            if (!this._reconnectInProgress) {\r\n                await this._connection.send(serializedMessage);\r\n            }\r\n        }\r\n        catch {\r\n            this._disconnected();\r\n        }\r\n        await backpressurePromise;\r\n    }\r\n    _ack(ackMessage) {\r\n        let newestAckedMessage = -1;\r\n        // Find index of newest message being acked\r\n        for (let index = 0; index < this._messages.length; index++) {\r\n            const element = this._messages[index];\r\n            if (element._id <= ackMessage.sequenceId) {\r\n                newestAckedMessage = index;\r\n                if (isArrayBuffer(element._message)) {\r\n                    this._bufferedByteCount -= element._message.byteLength;\r\n                }\r\n                else {\r\n                    this._bufferedByteCount -= element._message.length;\r\n                }\r\n                // resolve items that have already been sent and acked\r\n                element._resolver();\r\n            }\r\n            else if (this._bufferedByteCount < this._bufferSize) {\r\n                // resolve items that now fall under the buffer limit but haven't been acked\r\n                element._resolver();\r\n            }\r\n            else {\r\n                break;\r\n            }\r\n        }\r\n        if (newestAckedMessage !== -1) {\r\n            // We're removing everything including the message pointed to, so add 1\r\n            this._messages = this._messages.slice(newestAckedMessage + 1);\r\n        }\r\n    }\r\n    _shouldProcessMessage(message) {\r\n        if (this._waitForSequenceMessage) {\r\n            if (message.type !== MessageType.Sequence) {\r\n                return false;\r\n            }\r\n            else {\r\n                this._waitForSequenceMessage = false;\r\n                return true;\r\n            }\r\n        }\r\n        // No special processing for acks, pings, etc.\r\n        if (!this._isInvocationMessage(message)) {\r\n            return true;\r\n        }\r\n        const currentId = this._nextReceivingSequenceId;\r\n        this._nextReceivingSequenceId++;\r\n        if (currentId <= this._latestReceivedSequenceId) {\r\n            if (currentId === this._latestReceivedSequenceId) {\r\n                // Should only hit this if we just reconnected and the server is sending\r\n                // Messages it has buffered, which would mean it hasn't seen an Ack for these messages\r\n                this._ackTimer();\r\n            }\r\n            // Ignore, this is a duplicate message\r\n            return false;\r\n        }\r\n        this._latestReceivedSequenceId = currentId;\r\n        // Only start the timer for sending an Ack message when we have a message to ack. This also conveniently solves\r\n        // timer throttling by not having a recursive timer, and by starting the timer via a network call (recv)\r\n        this._ackTimer();\r\n        return true;\r\n    }\r\n    _resetSequence(message) {\r\n        if (message.sequenceId > this._nextReceivingSequenceId) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._connection.stop(new Error(\"Sequence ID greater than amount of messages we've received.\"));\r\n            return;\r\n        }\r\n        this._nextReceivingSequenceId = message.sequenceId;\r\n    }\r\n    _disconnected() {\r\n        this._reconnectInProgress = true;\r\n        this._waitForSequenceMessage = true;\r\n    }\r\n    async _resend() {\r\n        const sequenceId = this._messages.length !== 0\r\n            ? this._messages[0]._id\r\n            : this._totalMessageCount + 1;\r\n        await this._connection.send(this._protocol.writeMessage({ type: MessageType.Sequence, sequenceId }));\r\n        // Get a local variable to the _messages, just in case messages are acked while resending\r\n        // Which would slice the _messages array (which creates a new copy)\r\n        const messages = this._messages;\r\n        for (const element of messages) {\r\n            await this._connection.send(element._message);\r\n        }\r\n        this._reconnectInProgress = false;\r\n    }\r\n    _dispose(error) {\r\n        error !== null && error !== void 0 ? error : (error = new Error(\"Unable to reconnect to server.\"));\r\n        // Unblock backpressure if any\r\n        for (const element of this._messages) {\r\n            element._rejector(error);\r\n        }\r\n    }\r\n    _isInvocationMessage(message) {\r\n        // There is no way to check if something implements an interface.\r\n        // So we individually check the messages in a switch statement.\r\n        // To make sure we don't miss any message types we rely on the compiler\r\n        // seeing the function returns a value and it will do the\r\n        // exhaustive check for us on the switch statement, since we don't use 'case default'\r\n        switch (message.type) {\r\n            case MessageType.Invocation:\r\n            case MessageType.StreamItem:\r\n            case MessageType.Completion:\r\n            case MessageType.StreamInvocation:\r\n            case MessageType.CancelInvocation:\r\n                return true;\r\n            case MessageType.Close:\r\n            case MessageType.Sequence:\r\n            case MessageType.Ping:\r\n            case MessageType.Ack:\r\n                return false;\r\n        }\r\n    }\r\n    _ackTimer() {\r\n        if (this._ackTimerHandle === undefined) {\r\n            this._ackTimerHandle = setTimeout(async () => {\r\n                try {\r\n                    if (!this._reconnectInProgress) {\r\n                        await this._connection.send(this._protocol.writeMessage({ type: MessageType.Ack, sequenceId: this._latestReceivedSequenceId }));\r\n                    }\r\n                    // Ignore errors, that means the connection is closed and we don't care about the Ack message anymore.\r\n                }\r\n                catch { }\r\n                clearTimeout(this._ackTimerHandle);\r\n                this._ackTimerHandle = undefined;\r\n                // 1 second delay so we don't spam Ack messages if there are many messages being received at once.\r\n            }, 1000);\r\n        }\r\n    }\r\n}\r\nclass BufferedItem {\r\n    constructor(message, id, resolver, rejector) {\r\n        this._message = message;\r\n        this._id = id;\r\n        this._resolver = resolver;\r\n        this._rejector = rejector;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { HandshakeProtocol } from \"./HandshakeProtocol\";\r\nimport { AbortError } from \"./Errors\";\r\nimport { MessageType } from \"./IHubProtocol\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { Subject } from \"./Subject\";\r\nimport { Arg, getErrorString, Platform } from \"./Utils\";\r\nimport { MessageBuffer } from \"./MessageBuffer\";\r\nconst DEFAULT_TIMEOUT_IN_MS = 30 * 1000;\r\nconst DEFAULT_PING_INTERVAL_IN_MS = 15 * 1000;\r\nconst DEFAULT_STATEFUL_RECONNECT_BUFFER_SIZE = 100000;\r\n/** Describes the current state of the {@link HubConnection} to the server. */\r\nexport var HubConnectionState;\r\n(function (HubConnectionState) {\r\n    /** The hub connection is disconnected. */\r\n    HubConnectionState[\"Disconnected\"] = \"Disconnected\";\r\n    /** The hub connection is connecting. */\r\n    HubConnectionState[\"Connecting\"] = \"Connecting\";\r\n    /** The hub connection is connected. */\r\n    HubConnectionState[\"Connected\"] = \"Connected\";\r\n    /** The hub connection is disconnecting. */\r\n    HubConnectionState[\"Disconnecting\"] = \"Disconnecting\";\r\n    /** The hub connection is reconnecting. */\r\n    HubConnectionState[\"Reconnecting\"] = \"Reconnecting\";\r\n})(HubConnectionState || (HubConnectionState = {}));\r\n/** Represents a connection to a SignalR Hub. */\r\nexport class HubConnection {\r\n    /** @internal */\r\n    // Using a public static factory method means we can have a private constructor and an _internal_\r\n    // create method that can be used by HubConnectionBuilder. An \"internal\" constructor would just\r\n    // be stripped away and the '.d.ts' file would have no constructor, which is interpreted as a\r\n    // public parameter-less constructor.\r\n    static create(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize) {\r\n        return new HubConnection(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize);\r\n    }\r\n    constructor(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize) {\r\n        this._nextKeepAlive = 0;\r\n        this._freezeEventListener = () => {\r\n            this._logger.log(LogLevel.Warning, \"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://learn.microsoft.com/aspnet/core/signalr/javascript-client#bsleep\");\r\n        };\r\n        Arg.isRequired(connection, \"connection\");\r\n        Arg.isRequired(logger, \"logger\");\r\n        Arg.isRequired(protocol, \"protocol\");\r\n        this.serverTimeoutInMilliseconds = serverTimeoutInMilliseconds !== null && serverTimeoutInMilliseconds !== void 0 ? serverTimeoutInMilliseconds : DEFAULT_TIMEOUT_IN_MS;\r\n        this.keepAliveIntervalInMilliseconds = keepAliveIntervalInMilliseconds !== null && keepAliveIntervalInMilliseconds !== void 0 ? keepAliveIntervalInMilliseconds : DEFAULT_PING_INTERVAL_IN_MS;\r\n        this._statefulReconnectBufferSize = statefulReconnectBufferSize !== null && statefulReconnectBufferSize !== void 0 ? statefulReconnectBufferSize : DEFAULT_STATEFUL_RECONNECT_BUFFER_SIZE;\r\n        this._logger = logger;\r\n        this._protocol = protocol;\r\n        this.connection = connection;\r\n        this._reconnectPolicy = reconnectPolicy;\r\n        this._handshakeProtocol = new HandshakeProtocol();\r\n        this.connection.onreceive = (data) => this._processIncomingData(data);\r\n        this.connection.onclose = (error) => this._connectionClosed(error);\r\n        this._callbacks = {};\r\n        this._methods = {};\r\n        this._closedCallbacks = [];\r\n        this._reconnectingCallbacks = [];\r\n        this._reconnectedCallbacks = [];\r\n        this._invocationId = 0;\r\n        this._receivedHandshakeResponse = false;\r\n        this._connectionState = HubConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n        this._cachedPingMessage = this._protocol.writeMessage({ type: MessageType.Ping });\r\n    }\r\n    /** Indicates the state of the {@link HubConnection} to the server. */\r\n    get state() {\r\n        return this._connectionState;\r\n    }\r\n    /** Represents the connection id of the {@link HubConnection} on the server. The connection id will be null when the connection is either\r\n     *  in the disconnected state or if the negotiation step was skipped.\r\n     */\r\n    get connectionId() {\r\n        return this.connection ? (this.connection.connectionId || null) : null;\r\n    }\r\n    /** Indicates the url of the {@link HubConnection} to the server. */\r\n    get baseUrl() {\r\n        return this.connection.baseUrl || \"\";\r\n    }\r\n    /**\r\n     * Sets a new url for the HubConnection. Note that the url can only be changed when the connection is in either the Disconnected or\r\n     * Reconnecting states.\r\n     * @param {string} url The url to connect to.\r\n     */\r\n    set baseUrl(url) {\r\n        if (this._connectionState !== HubConnectionState.Disconnected && this._connectionState !== HubConnectionState.Reconnecting) {\r\n            throw new Error(\"The HubConnection must be in the Disconnected or Reconnecting state to change the url.\");\r\n        }\r\n        if (!url) {\r\n            throw new Error(\"The HubConnection url must be a valid url.\");\r\n        }\r\n        this.connection.baseUrl = url;\r\n    }\r\n    /** Starts the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully established, or rejects with an error.\r\n     */\r\n    start() {\r\n        this._startPromise = this._startWithStateTransitions();\r\n        return this._startPromise;\r\n    }\r\n    async _startWithStateTransitions() {\r\n        if (this._connectionState !== HubConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start a HubConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n        this._connectionState = HubConnectionState.Connecting;\r\n        this._logger.log(LogLevel.Debug, \"Starting HubConnection.\");\r\n        try {\r\n            await this._startInternal();\r\n            if (Platform.isBrowser) {\r\n                // Log when the browser freezes the tab so users know why their connection unexpectedly stopped working\r\n                window.document.addEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n            this._connectionState = HubConnectionState.Connected;\r\n            this._connectionStarted = true;\r\n            this._logger.log(LogLevel.Debug, \"HubConnection connected successfully.\");\r\n        }\r\n        catch (e) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._logger.log(LogLevel.Debug, `HubConnection failed to start successfully because of error '${e}'.`);\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n    async _startInternal() {\r\n        this._stopDuringStartError = undefined;\r\n        this._receivedHandshakeResponse = false;\r\n        // Set up the promise before any connection is (re)started otherwise it could race with received messages\r\n        const handshakePromise = new Promise((resolve, reject) => {\r\n            this._handshakeResolver = resolve;\r\n            this._handshakeRejecter = reject;\r\n        });\r\n        await this.connection.start(this._protocol.transferFormat);\r\n        try {\r\n            let version = this._protocol.version;\r\n            if (!this.connection.features.reconnect) {\r\n                // Stateful Reconnect starts with HubProtocol version 2, newer clients connecting to older servers will fail to connect due to\r\n                // the handshake only supporting version 1, so we will try to send version 1 during the handshake to keep old servers working.\r\n                version = 1;\r\n            }\r\n            const handshakeRequest = {\r\n                protocol: this._protocol.name,\r\n                version,\r\n            };\r\n            this._logger.log(LogLevel.Debug, \"Sending handshake request.\");\r\n            await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(handshakeRequest));\r\n            this._logger.log(LogLevel.Information, `Using HubProtocol '${this._protocol.name}'.`);\r\n            // defensively cleanup timeout in case we receive a message from the server before we finish start\r\n            this._cleanupTimeout();\r\n            this._resetTimeoutPeriod();\r\n            this._resetKeepAliveInterval();\r\n            await handshakePromise;\r\n            // It's important to check the stopDuringStartError instead of just relying on the handshakePromise\r\n            // being rejected on close, because this continuation can run after both the handshake completed successfully\r\n            // and the connection was closed.\r\n            if (this._stopDuringStartError) {\r\n                // It's important to throw instead of returning a rejected promise, because we don't want to allow any state\r\n                // transitions to occur between now and the calling code observing the exceptions. Returning a rejected promise\r\n                // will cause the calling continuation to get scheduled to run later.\r\n                // eslint-disable-next-line @typescript-eslint/no-throw-literal\r\n                throw this._stopDuringStartError;\r\n            }\r\n            const useStatefulReconnect = this.connection.features.reconnect || false;\r\n            if (useStatefulReconnect) {\r\n                this._messageBuffer = new MessageBuffer(this._protocol, this.connection, this._statefulReconnectBufferSize);\r\n                this.connection.features.disconnected = this._messageBuffer._disconnected.bind(this._messageBuffer);\r\n                this.connection.features.resend = () => {\r\n                    if (this._messageBuffer) {\r\n                        return this._messageBuffer._resend();\r\n                    }\r\n                };\r\n            }\r\n            if (!this.connection.features.inherentKeepAlive) {\r\n                await this._sendMessage(this._cachedPingMessage);\r\n            }\r\n        }\r\n        catch (e) {\r\n            this._logger.log(LogLevel.Debug, `Hub handshake failed with error '${e}' during start(). Stopping HubConnection.`);\r\n            this._cleanupTimeout();\r\n            this._cleanupPingTimer();\r\n            // HttpConnection.stop() should not complete until after the onclose callback is invoked.\r\n            // This will transition the HubConnection to the disconnected state before HttpConnection.stop() completes.\r\n            await this.connection.stop(e);\r\n            throw e;\r\n        }\r\n    }\r\n    /** Stops the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully terminated, or rejects with an error.\r\n     */\r\n    async stop() {\r\n        // Capture the start promise before the connection might be restarted in an onclose callback.\r\n        const startPromise = this._startPromise;\r\n        this.connection.features.reconnect = false;\r\n        this._stopPromise = this._stopInternal();\r\n        await this._stopPromise;\r\n        try {\r\n            // Awaiting undefined continues immediately\r\n            await startPromise;\r\n        }\r\n        catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n    }\r\n    _stopInternal(error) {\r\n        if (this._connectionState === HubConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HubConnection.stop(${error}) ignored because it is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise;\r\n        }\r\n        const state = this._connectionState;\r\n        this._connectionState = HubConnectionState.Disconnecting;\r\n        this._logger.log(LogLevel.Debug, \"Stopping HubConnection.\");\r\n        if (this._reconnectDelayHandle) {\r\n            // We're in a reconnect delay which means the underlying connection is currently already stopped.\r\n            // Just clear the handle to stop the reconnect loop (which no one is waiting on thankfully) and\r\n            // fire the onclose callbacks.\r\n            this._logger.log(LogLevel.Debug, \"Connection stopped during reconnect delay. Done reconnecting.\");\r\n            clearTimeout(this._reconnectDelayHandle);\r\n            this._reconnectDelayHandle = undefined;\r\n            this._completeClose();\r\n            return Promise.resolve();\r\n        }\r\n        if (state === HubConnectionState.Connected) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._sendCloseMessage();\r\n        }\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n        this._stopDuringStartError = error || new AbortError(\"The connection was stopped before the hub handshake could complete.\");\r\n        // HttpConnection.stop() should not complete until after either HttpConnection.start() fails\r\n        // or the onclose callback is invoked. The onclose callback will transition the HubConnection\r\n        // to the disconnected state if need be before HttpConnection.stop() completes.\r\n        return this.connection.stop(error);\r\n    }\r\n    async _sendCloseMessage() {\r\n        try {\r\n            await this._sendWithProtocol(this._createCloseMessage());\r\n        }\r\n        catch {\r\n            // Ignore, this is a best effort attempt to let the server know the client closed gracefully.\r\n        }\r\n    }\r\n    /** Invokes a streaming hub method on the server using the specified name and arguments.\r\n     *\r\n     * @typeparam T The type of the items returned by the server.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {IStreamResult<T>} An object that yields results from the server as they are received.\r\n     */\r\n    stream(methodName, ...args) {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createStreamInvocation(methodName, args, streamIds);\r\n        // eslint-disable-next-line prefer-const\r\n        let promiseQueue;\r\n        const subject = new Subject();\r\n        subject.cancelCallback = () => {\r\n            const cancelInvocation = this._createCancelInvocation(invocationDescriptor.invocationId);\r\n            delete this._callbacks[invocationDescriptor.invocationId];\r\n            return promiseQueue.then(() => {\r\n                return this._sendWithProtocol(cancelInvocation);\r\n            });\r\n        };\r\n        this._callbacks[invocationDescriptor.invocationId] = (invocationEvent, error) => {\r\n            if (error) {\r\n                subject.error(error);\r\n                return;\r\n            }\r\n            else if (invocationEvent) {\r\n                // invocationEvent will not be null when an error is not passed to the callback\r\n                if (invocationEvent.type === MessageType.Completion) {\r\n                    if (invocationEvent.error) {\r\n                        subject.error(new Error(invocationEvent.error));\r\n                    }\r\n                    else {\r\n                        subject.complete();\r\n                    }\r\n                }\r\n                else {\r\n                    subject.next((invocationEvent.item));\r\n                }\r\n            }\r\n        };\r\n        promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n            .catch((e) => {\r\n            subject.error(e);\r\n            delete this._callbacks[invocationDescriptor.invocationId];\r\n        });\r\n        this._launchStreams(streams, promiseQueue);\r\n        return subject;\r\n    }\r\n    _sendMessage(message) {\r\n        this._resetKeepAliveInterval();\r\n        return this.connection.send(message);\r\n    }\r\n    /**\r\n     * Sends a js object to the server.\r\n     * @param message The js object to serialize and send.\r\n     */\r\n    _sendWithProtocol(message) {\r\n        if (this._messageBuffer) {\r\n            return this._messageBuffer._send(message);\r\n        }\r\n        else {\r\n            return this._sendMessage(this._protocol.writeMessage(message));\r\n        }\r\n    }\r\n    /** Invokes a hub method on the server using the specified name and arguments. Does not wait for a response from the receiver.\r\n     *\r\n     * The Promise returned by this method resolves when the client has sent the invocation to the server. The server may still\r\n     * be processing the invocation.\r\n     *\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<void>} A Promise that resolves when the invocation has been successfully sent, or rejects with an error.\r\n     */\r\n    send(methodName, ...args) {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const sendPromise = this._sendWithProtocol(this._createInvocation(methodName, args, true, streamIds));\r\n        this._launchStreams(streams, sendPromise);\r\n        return sendPromise;\r\n    }\r\n    /** Invokes a hub method on the server using the specified name and arguments.\r\n     *\r\n     * The Promise returned by this method resolves when the server indicates it has finished invoking the method. When the promise\r\n     * resolves, the server has finished invoking the method. If the server method returns a result, it is produced as the result of\r\n     * resolving the Promise.\r\n     *\r\n     * @typeparam T The expected return type.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<T>} A Promise that resolves with the result of the server method (if any), or rejects with an error.\r\n     */\r\n    invoke(methodName, ...args) {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createInvocation(methodName, args, false, streamIds);\r\n        const p = new Promise((resolve, reject) => {\r\n            // invocationId will always have a value for a non-blocking invocation\r\n            this._callbacks[invocationDescriptor.invocationId] = (invocationEvent, error) => {\r\n                if (error) {\r\n                    reject(error);\r\n                    return;\r\n                }\r\n                else if (invocationEvent) {\r\n                    // invocationEvent will not be null when an error is not passed to the callback\r\n                    if (invocationEvent.type === MessageType.Completion) {\r\n                        if (invocationEvent.error) {\r\n                            reject(new Error(invocationEvent.error));\r\n                        }\r\n                        else {\r\n                            resolve(invocationEvent.result);\r\n                        }\r\n                    }\r\n                    else {\r\n                        reject(new Error(`Unexpected message type: ${invocationEvent.type}`));\r\n                    }\r\n                }\r\n            };\r\n            const promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n                .catch((e) => {\r\n                reject(e);\r\n                // invocationId will always have a value for a non-blocking invocation\r\n                delete this._callbacks[invocationDescriptor.invocationId];\r\n            });\r\n            this._launchStreams(streams, promiseQueue);\r\n        });\r\n        return p;\r\n    }\r\n    on(methodName, newMethod) {\r\n        if (!methodName || !newMethod) {\r\n            return;\r\n        }\r\n        methodName = methodName.toLowerCase();\r\n        if (!this._methods[methodName]) {\r\n            this._methods[methodName] = [];\r\n        }\r\n        // Preventing adding the same handler multiple times.\r\n        if (this._methods[methodName].indexOf(newMethod) !== -1) {\r\n            return;\r\n        }\r\n        this._methods[methodName].push(newMethod);\r\n    }\r\n    off(methodName, method) {\r\n        if (!methodName) {\r\n            return;\r\n        }\r\n        methodName = methodName.toLowerCase();\r\n        const handlers = this._methods[methodName];\r\n        if (!handlers) {\r\n            return;\r\n        }\r\n        if (method) {\r\n            const removeIdx = handlers.indexOf(method);\r\n            if (removeIdx !== -1) {\r\n                handlers.splice(removeIdx, 1);\r\n                if (handlers.length === 0) {\r\n                    delete this._methods[methodName];\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            delete this._methods[methodName];\r\n        }\r\n    }\r\n    /** Registers a handler that will be invoked when the connection is closed.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection is closed. Optionally receives a single argument containing the error that caused the connection to close (if any).\r\n     */\r\n    onclose(callback) {\r\n        if (callback) {\r\n            this._closedCallbacks.push(callback);\r\n        }\r\n    }\r\n    /** Registers a handler that will be invoked when the connection starts reconnecting.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection starts reconnecting. Optionally receives a single argument containing the error that caused the connection to start reconnecting (if any).\r\n     */\r\n    onreconnecting(callback) {\r\n        if (callback) {\r\n            this._reconnectingCallbacks.push(callback);\r\n        }\r\n    }\r\n    /** Registers a handler that will be invoked when the connection successfully reconnects.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection successfully reconnects.\r\n     */\r\n    onreconnected(callback) {\r\n        if (callback) {\r\n            this._reconnectedCallbacks.push(callback);\r\n        }\r\n    }\r\n    _processIncomingData(data) {\r\n        this._cleanupTimeout();\r\n        if (!this._receivedHandshakeResponse) {\r\n            data = this._processHandshakeResponse(data);\r\n            this._receivedHandshakeResponse = true;\r\n        }\r\n        // Data may have all been read when processing handshake response\r\n        if (data) {\r\n            // Parse the messages\r\n            const messages = this._protocol.parseMessages(data, this._logger);\r\n            for (const message of messages) {\r\n                if (this._messageBuffer && !this._messageBuffer._shouldProcessMessage(message)) {\r\n                    // Don't process the message, we are either waiting for a SequenceMessage or received a duplicate message\r\n                    continue;\r\n                }\r\n                switch (message.type) {\r\n                    case MessageType.Invocation:\r\n                        this._invokeClientMethod(message)\r\n                            .catch((e) => {\r\n                            this._logger.log(LogLevel.Error, `Invoke client method threw error: ${getErrorString(e)}`);\r\n                        });\r\n                        break;\r\n                    case MessageType.StreamItem:\r\n                    case MessageType.Completion: {\r\n                        const callback = this._callbacks[message.invocationId];\r\n                        if (callback) {\r\n                            if (message.type === MessageType.Completion) {\r\n                                delete this._callbacks[message.invocationId];\r\n                            }\r\n                            try {\r\n                                callback(message);\r\n                            }\r\n                            catch (e) {\r\n                                this._logger.log(LogLevel.Error, `Stream callback threw error: ${getErrorString(e)}`);\r\n                            }\r\n                        }\r\n                        break;\r\n                    }\r\n                    case MessageType.Ping:\r\n                        // Don't care about pings\r\n                        break;\r\n                    case MessageType.Close: {\r\n                        this._logger.log(LogLevel.Information, \"Close message received from server.\");\r\n                        const error = message.error ? new Error(\"Server returned an error on close: \" + message.error) : undefined;\r\n                        if (message.allowReconnect === true) {\r\n                            // It feels wrong not to await connection.stop() here, but processIncomingData is called as part of an onreceive callback which is not async,\r\n                            // this is already the behavior for serverTimeout(), and HttpConnection.Stop() should catch and log all possible exceptions.\r\n                            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                            this.connection.stop(error);\r\n                        }\r\n                        else {\r\n                            // We cannot await stopInternal() here, but subsequent calls to stop() will await this if stopInternal() is still ongoing.\r\n                            this._stopPromise = this._stopInternal(error);\r\n                        }\r\n                        break;\r\n                    }\r\n                    case MessageType.Ack:\r\n                        if (this._messageBuffer) {\r\n                            this._messageBuffer._ack(message);\r\n                        }\r\n                        break;\r\n                    case MessageType.Sequence:\r\n                        if (this._messageBuffer) {\r\n                            this._messageBuffer._resetSequence(message);\r\n                        }\r\n                        break;\r\n                    default:\r\n                        this._logger.log(LogLevel.Warning, `Invalid message type: ${message.type}.`);\r\n                        break;\r\n                }\r\n            }\r\n        }\r\n        this._resetTimeoutPeriod();\r\n    }\r\n    _processHandshakeResponse(data) {\r\n        let responseMessage;\r\n        let remainingData;\r\n        try {\r\n            [remainingData, responseMessage] = this._handshakeProtocol.parseHandshakeResponse(data);\r\n        }\r\n        catch (e) {\r\n            const message = \"Error parsing handshake response: \" + e;\r\n            this._logger.log(LogLevel.Error, message);\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        }\r\n        if (responseMessage.error) {\r\n            const message = \"Server returned handshake error: \" + responseMessage.error;\r\n            this._logger.log(LogLevel.Error, message);\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        }\r\n        else {\r\n            this._logger.log(LogLevel.Debug, \"Server handshake complete.\");\r\n        }\r\n        this._handshakeResolver();\r\n        return remainingData;\r\n    }\r\n    _resetKeepAliveInterval() {\r\n        if (this.connection.features.inherentKeepAlive) {\r\n            return;\r\n        }\r\n        // Set the time we want the next keep alive to be sent\r\n        // Timer will be setup on next message receive\r\n        this._nextKeepAlive = new Date().getTime() + this.keepAliveIntervalInMilliseconds;\r\n        this._cleanupPingTimer();\r\n    }\r\n    _resetTimeoutPeriod() {\r\n        if (!this.connection.features || !this.connection.features.inherentKeepAlive) {\r\n            // Set the timeout timer\r\n            this._timeoutHandle = setTimeout(() => this.serverTimeout(), this.serverTimeoutInMilliseconds);\r\n            // Set keepAlive timer if there isn't one\r\n            if (this._pingServerHandle === undefined) {\r\n                let nextPing = this._nextKeepAlive - new Date().getTime();\r\n                if (nextPing < 0) {\r\n                    nextPing = 0;\r\n                }\r\n                // The timer needs to be set from a networking callback to avoid Chrome timer throttling from causing timers to run once a minute\r\n                this._pingServerHandle = setTimeout(async () => {\r\n                    if (this._connectionState === HubConnectionState.Connected) {\r\n                        try {\r\n                            await this._sendMessage(this._cachedPingMessage);\r\n                        }\r\n                        catch {\r\n                            // We don't care about the error. It should be seen elsewhere in the client.\r\n                            // The connection is probably in a bad or closed state now, cleanup the timer so it stops triggering\r\n                            this._cleanupPingTimer();\r\n                        }\r\n                    }\r\n                }, nextPing);\r\n            }\r\n        }\r\n    }\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    serverTimeout() {\r\n        // The server hasn't talked to us in a while. It doesn't like us anymore ... :(\r\n        // Terminate the connection, but we don't need to wait on the promise. This could trigger reconnecting.\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this.connection.stop(new Error(\"Server timeout elapsed without receiving a message from the server.\"));\r\n    }\r\n    async _invokeClientMethod(invocationMessage) {\r\n        const methodName = invocationMessage.target.toLowerCase();\r\n        const methods = this._methods[methodName];\r\n        if (!methods) {\r\n            this._logger.log(LogLevel.Warning, `No client method with the name '${methodName}' found.`);\r\n            // No handlers provided by client but the server is expecting a response still, so we send an error\r\n            if (invocationMessage.invocationId) {\r\n                this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                await this._sendWithProtocol(this._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null));\r\n            }\r\n            return;\r\n        }\r\n        // Avoid issues with handlers removing themselves thus modifying the list while iterating through it\r\n        const methodsCopy = methods.slice();\r\n        // Server expects a response\r\n        const expectsResponse = invocationMessage.invocationId ? true : false;\r\n        // We preserve the last result or exception but still call all handlers\r\n        let res;\r\n        let exception;\r\n        let completionMessage;\r\n        for (const m of methodsCopy) {\r\n            try {\r\n                const prevRes = res;\r\n                res = await m.apply(this, invocationMessage.arguments);\r\n                if (expectsResponse && res && prevRes) {\r\n                    this._logger.log(LogLevel.Error, `Multiple results provided for '${methodName}'. Sending error to server.`);\r\n                    completionMessage = this._createCompletionMessage(invocationMessage.invocationId, `Client provided multiple results.`, null);\r\n                }\r\n                // Ignore exception if we got a result after, the exception will be logged\r\n                exception = undefined;\r\n            }\r\n            catch (e) {\r\n                exception = e;\r\n                this._logger.log(LogLevel.Error, `A callback for the method '${methodName}' threw error '${e}'.`);\r\n            }\r\n        }\r\n        if (completionMessage) {\r\n            await this._sendWithProtocol(completionMessage);\r\n        }\r\n        else if (expectsResponse) {\r\n            // If there is an exception that means either no result was given or a handler after a result threw\r\n            if (exception) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId, `${exception}`, null);\r\n            }\r\n            else if (res !== undefined) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId, null, res);\r\n            }\r\n            else {\r\n                this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                // Client didn't provide a result or throw from a handler, server expects a response so we send an error\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null);\r\n            }\r\n            await this._sendWithProtocol(completionMessage);\r\n        }\r\n        else {\r\n            if (res) {\r\n                this._logger.log(LogLevel.Error, `Result given for '${methodName}' method but server is not expecting a result.`);\r\n            }\r\n        }\r\n    }\r\n    _connectionClosed(error) {\r\n        this._logger.log(LogLevel.Debug, `HubConnection.connectionClosed(${error}) called while in state ${this._connectionState}.`);\r\n        // Triggering this.handshakeRejecter is insufficient because it could already be resolved without the continuation having run yet.\r\n        this._stopDuringStartError = this._stopDuringStartError || error || new AbortError(\"The underlying connection was closed before the hub handshake could complete.\");\r\n        // If the handshake is in progress, start will be waiting for the handshake promise, so we complete it.\r\n        // If it has already completed, this should just noop.\r\n        if (this._handshakeResolver) {\r\n            this._handshakeResolver();\r\n        }\r\n        this._cancelCallbacksWithError(error || new Error(\"Invocation canceled due to the underlying connection being closed.\"));\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._completeClose(error);\r\n        }\r\n        else if (this._connectionState === HubConnectionState.Connected && this._reconnectPolicy) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._reconnect(error);\r\n        }\r\n        else if (this._connectionState === HubConnectionState.Connected) {\r\n            this._completeClose(error);\r\n        }\r\n        // If none of the above if conditions were true were called the HubConnection must be in either:\r\n        // 1. The Connecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail it.\r\n        // 2. The Reconnecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail the current reconnect attempt\r\n        //    and potentially continue the reconnect() loop.\r\n        // 3. The Disconnected state in which case we're already done.\r\n    }\r\n    _completeClose(error) {\r\n        if (this._connectionStarted) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._connectionStarted = false;\r\n            if (this._messageBuffer) {\r\n                this._messageBuffer._dispose(error !== null && error !== void 0 ? error : new Error(\"Connection closed.\"));\r\n                this._messageBuffer = undefined;\r\n            }\r\n            if (Platform.isBrowser) {\r\n                window.document.removeEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n            try {\r\n                this._closedCallbacks.forEach((c) => c.apply(this, [error]));\r\n            }\r\n            catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onclose callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n    async _reconnect(error) {\r\n        const reconnectStartTime = Date.now();\r\n        let previousReconnectAttempts = 0;\r\n        let retryError = error !== undefined ? error : new Error(\"Attempting to reconnect due to a unknown error.\");\r\n        let nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, 0, retryError);\r\n        if (nextRetryDelay === null) {\r\n            this._logger.log(LogLevel.Debug, \"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt.\");\r\n            this._completeClose(error);\r\n            return;\r\n        }\r\n        this._connectionState = HubConnectionState.Reconnecting;\r\n        if (error) {\r\n            this._logger.log(LogLevel.Information, `Connection reconnecting because of error '${error}'.`);\r\n        }\r\n        else {\r\n            this._logger.log(LogLevel.Information, \"Connection reconnecting.\");\r\n        }\r\n        if (this._reconnectingCallbacks.length !== 0) {\r\n            try {\r\n                this._reconnectingCallbacks.forEach((c) => c.apply(this, [error]));\r\n            }\r\n            catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onreconnecting callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n            // Exit early if an onreconnecting callback called connection.stop().\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.\");\r\n                return;\r\n            }\r\n        }\r\n        while (nextRetryDelay !== null) {\r\n            this._logger.log(LogLevel.Information, `Reconnect attempt number ${previousReconnectAttempts} will start in ${nextRetryDelay} ms.`);\r\n            await new Promise((resolve) => {\r\n                this._reconnectDelayHandle = setTimeout(resolve, nextRetryDelay);\r\n            });\r\n            this._reconnectDelayHandle = undefined;\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state during reconnect delay. Done reconnecting.\");\r\n                return;\r\n            }\r\n            try {\r\n                await this._startInternal();\r\n                this._connectionState = HubConnectionState.Connected;\r\n                this._logger.log(LogLevel.Information, \"HubConnection reconnected successfully.\");\r\n                if (this._reconnectedCallbacks.length !== 0) {\r\n                    try {\r\n                        this._reconnectedCallbacks.forEach((c) => c.apply(this, [this.connection.connectionId]));\r\n                    }\r\n                    catch (e) {\r\n                        this._logger.log(LogLevel.Error, `An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${e}'.`);\r\n                    }\r\n                }\r\n                return;\r\n            }\r\n            catch (e) {\r\n                this._logger.log(LogLevel.Information, `Reconnect attempt failed because of error '${e}'.`);\r\n                if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                    this._logger.log(LogLevel.Debug, `Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`);\r\n                    // The TypeScript compiler thinks that connectionState must be Connected here. The TypeScript compiler is wrong.\r\n                    if (this._connectionState === HubConnectionState.Disconnecting) {\r\n                        this._completeClose();\r\n                    }\r\n                    return;\r\n                }\r\n                retryError = e instanceof Error ? e : new Error(e.toString());\r\n                nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, Date.now() - reconnectStartTime, retryError);\r\n            }\r\n        }\r\n        this._logger.log(LogLevel.Information, `Reconnect retries have been exhausted after ${Date.now() - reconnectStartTime} ms and ${previousReconnectAttempts} failed attempts. Connection disconnecting.`);\r\n        this._completeClose();\r\n    }\r\n    _getNextRetryDelay(previousRetryCount, elapsedMilliseconds, retryReason) {\r\n        try {\r\n            return this._reconnectPolicy.nextRetryDelayInMilliseconds({\r\n                elapsedMilliseconds,\r\n                previousRetryCount,\r\n                retryReason,\r\n            });\r\n        }\r\n        catch (e) {\r\n            this._logger.log(LogLevel.Error, `IRetryPolicy.nextRetryDelayInMilliseconds(${previousRetryCount}, ${elapsedMilliseconds}) threw error '${e}'.`);\r\n            return null;\r\n        }\r\n    }\r\n    _cancelCallbacksWithError(error) {\r\n        const callbacks = this._callbacks;\r\n        this._callbacks = {};\r\n        Object.keys(callbacks)\r\n            .forEach((key) => {\r\n            const callback = callbacks[key];\r\n            try {\r\n                callback(null, error);\r\n            }\r\n            catch (e) {\r\n                this._logger.log(LogLevel.Error, `Stream 'error' callback called with '${error}' threw error: ${getErrorString(e)}`);\r\n            }\r\n        });\r\n    }\r\n    _cleanupPingTimer() {\r\n        if (this._pingServerHandle) {\r\n            clearTimeout(this._pingServerHandle);\r\n            this._pingServerHandle = undefined;\r\n        }\r\n    }\r\n    _cleanupTimeout() {\r\n        if (this._timeoutHandle) {\r\n            clearTimeout(this._timeoutHandle);\r\n        }\r\n    }\r\n    _createInvocation(methodName, args, nonblocking, streamIds) {\r\n        if (nonblocking) {\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n            else {\r\n                return {\r\n                    arguments: args,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        }\r\n        else {\r\n            const invocationId = this._invocationId;\r\n            this._invocationId++;\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n            else {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        }\r\n    }\r\n    _launchStreams(streams, promiseQueue) {\r\n        if (streams.length === 0) {\r\n            return;\r\n        }\r\n        // Synchronize stream data so they arrive in-order on the server\r\n        if (!promiseQueue) {\r\n            promiseQueue = Promise.resolve();\r\n        }\r\n        // We want to iterate over the keys, since the keys are the stream ids\r\n        // eslint-disable-next-line guard-for-in\r\n        for (const streamId in streams) {\r\n            streams[streamId].subscribe({\r\n                complete: () => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId)));\r\n                },\r\n                error: (err) => {\r\n                    let message;\r\n                    if (err instanceof Error) {\r\n                        message = err.message;\r\n                    }\r\n                    else if (err && err.toString) {\r\n                        message = err.toString();\r\n                    }\r\n                    else {\r\n                        message = \"Unknown error\";\r\n                    }\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId, message)));\r\n                },\r\n                next: (item) => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createStreamItemMessage(streamId, item)));\r\n                },\r\n            });\r\n        }\r\n    }\r\n    _replaceStreamingParams(args) {\r\n        const streams = [];\r\n        const streamIds = [];\r\n        for (let i = 0; i < args.length; i++) {\r\n            const argument = args[i];\r\n            if (this._isObservable(argument)) {\r\n                const streamId = this._invocationId;\r\n                this._invocationId++;\r\n                // Store the stream for later use\r\n                streams[streamId] = argument;\r\n                streamIds.push(streamId.toString());\r\n                // remove stream from args\r\n                args.splice(i, 1);\r\n            }\r\n        }\r\n        return [streams, streamIds];\r\n    }\r\n    _isObservable(arg) {\r\n        // This allows other stream implementations to just work (like rxjs)\r\n        return arg && arg.subscribe && typeof arg.subscribe === \"function\";\r\n    }\r\n    _createStreamInvocation(methodName, args, streamIds) {\r\n        const invocationId = this._invocationId;\r\n        this._invocationId++;\r\n        if (streamIds.length !== 0) {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                streamIds,\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        }\r\n        else {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        }\r\n    }\r\n    _createCancelInvocation(id) {\r\n        return {\r\n            invocationId: id,\r\n            type: MessageType.CancelInvocation,\r\n        };\r\n    }\r\n    _createStreamItemMessage(id, item) {\r\n        return {\r\n            invocationId: id,\r\n            item,\r\n            type: MessageType.StreamItem,\r\n        };\r\n    }\r\n    _createCompletionMessage(id, error, result) {\r\n        if (error) {\r\n            return {\r\n                error,\r\n                invocationId: id,\r\n                type: MessageType.Completion,\r\n            };\r\n        }\r\n        return {\r\n            invocationId: id,\r\n            result,\r\n            type: MessageType.Completion,\r\n        };\r\n    }\r\n    _createCloseMessage() {\r\n        return { type: MessageType.Close };\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// 0, 2, 10, 30 second delays before reconnect attempts.\r\nconst DEFAULT_RETRY_DELAYS_IN_MILLISECONDS = [0, 2000, 10000, 30000, null];\r\n/** @private */\r\nexport class DefaultReconnectPolicy {\r\n    constructor(retryDelays) {\r\n        this._retryDelays = retryDelays !== undefined ? [...retryDelays, null] : DEFAULT_RETRY_DELAYS_IN_MILLISECONDS;\r\n    }\r\n    nextRetryDelayInMilliseconds(retryContext) {\r\n        return this._retryDelays[retryContext.previousRetryCount];\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nexport class HeaderNames {\r\n}\r\nHeaderNames.Authorization = \"Authorization\";\r\nHeaderNames.Cookie = \"<PERSON>ie\";\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient } from \"./HttpClient\";\r\n/** @private */\r\nexport class AccessTokenHttpClient extends HttpClient {\r\n    constructor(innerClient, accessTokenFactory) {\r\n        super();\r\n        this._innerClient = innerClient;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n    }\r\n    async send(request) {\r\n        let allowRetry = true;\r\n        if (this._accessTokenFactory && (!this._accessToken || (request.url && request.url.indexOf(\"/negotiate?\") > 0))) {\r\n            // don't retry if the request is a negotiate or if we just got a potentially new token from the access token factory\r\n            allowRetry = false;\r\n            this._accessToken = await this._accessTokenFactory();\r\n        }\r\n        this._setAuthorizationHeader(request);\r\n        const response = await this._innerClient.send(request);\r\n        if (allowRetry && response.statusCode === 401 && this._accessTokenFactory) {\r\n            this._accessToken = await this._accessTokenFactory();\r\n            this._setAuthorizationHeader(request);\r\n            return await this._innerClient.send(request);\r\n        }\r\n        return response;\r\n    }\r\n    _setAuthorizationHeader(request) {\r\n        if (!request.headers) {\r\n            request.headers = {};\r\n        }\r\n        if (this._accessToken) {\r\n            request.headers[HeaderNames.Authorization] = `Bearer ${this._accessToken}`;\r\n        }\r\n        // don't remove the header if there isn't an access token factory, the user manually added the header in this case\r\n        else if (this._accessTokenFactory) {\r\n            if (request.headers[HeaderNames.Authorization]) {\r\n                delete request.headers[HeaderNames.Authorization];\r\n            }\r\n        }\r\n    }\r\n    getCookieString(url) {\r\n        return this._innerClient.getCookieString(url);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// This will be treated as a bit flag in the future, so we keep it using power-of-two values.\r\n/** Specifies a specific HTTP transport type. */\r\nexport var HttpTransportType;\r\n(function (HttpTransportType) {\r\n    /** Specifies no transport preference. */\r\n    HttpTransportType[HttpTransportType[\"None\"] = 0] = \"None\";\r\n    /** Specifies the WebSockets transport. */\r\n    HttpTransportType[HttpTransportType[\"WebSockets\"] = 1] = \"WebSockets\";\r\n    /** Specifies the Server-Sent Events transport. */\r\n    HttpTransportType[HttpTransportType[\"ServerSentEvents\"] = 2] = \"ServerSentEvents\";\r\n    /** Specifies the Long Polling transport. */\r\n    HttpTransportType[HttpTransportType[\"LongPolling\"] = 4] = \"LongPolling\";\r\n})(HttpTransportType || (HttpTransportType = {}));\r\n/** Specifies the transfer format for a connection. */\r\nexport var TransferFormat;\r\n(function (TransferFormat) {\r\n    /** Specifies that only text data will be transmitted over the connection. */\r\n    TransferFormat[TransferFormat[\"Text\"] = 1] = \"Text\";\r\n    /** Specifies that binary data will be transmitted over the connection. */\r\n    TransferFormat[TransferFormat[\"Binary\"] = 2] = \"Binary\";\r\n})(TransferFormat || (TransferFormat = {}));\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// Rough polyfill of https://developer.mozilla.org/en-US/docs/Web/API/AbortController\r\n// We don't actually ever use the API being polyfilled, we always use the polyfill because\r\n// it's a very new API right now.\r\n// Not exported from index.\r\n/** @private */\r\nexport class AbortController {\r\n    constructor() {\r\n        this._isAborted = false;\r\n        this.onabort = null;\r\n    }\r\n    abort() {\r\n        if (!this._isAborted) {\r\n            this._isAborted = true;\r\n            if (this.onabort) {\r\n                this.onabort();\r\n            }\r\n        }\r\n    }\r\n    get signal() {\r\n        return this;\r\n    }\r\n    get aborted() {\r\n        return this._isAborted;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { AbortController } from \"./AbortController\";\r\nimport { HttpError, TimeoutError } from \"./Errors\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, sendMessage } from \"./Utils\";\r\n// Not exported from 'index', this type is internal.\r\n/** @private */\r\nexport class LongPollingTransport {\r\n    // This is an internal type, not exported from 'index' so this is really just internal.\r\n    get pollAborted() {\r\n        return this._pollAbort.aborted;\r\n    }\r\n    constructor(httpClient, logger, options) {\r\n        this._httpClient = httpClient;\r\n        this._logger = logger;\r\n        this._pollAbort = new AbortController();\r\n        this._options = options;\r\n        this._running = false;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n    async connect(url, transferFormat) {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._url = url;\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Connecting.\");\r\n        // Allow binary format on Node and Browsers that support binary content (indicated by the presence of responseType property)\r\n        if (transferFormat === TransferFormat.Binary &&\r\n            (typeof XMLHttpRequest !== \"undefined\" && typeof new XMLHttpRequest().responseType !== \"string\")) {\r\n            throw new Error(\"Binary protocols over XmlHttpRequest not implementing advanced features are not supported.\");\r\n        }\r\n        const [name, value] = getUserAgentHeader();\r\n        const headers = { [name]: value, ...this._options.headers };\r\n        const pollOptions = {\r\n            abortSignal: this._pollAbort.signal,\r\n            headers,\r\n            timeout: 100000,\r\n            withCredentials: this._options.withCredentials,\r\n        };\r\n        if (transferFormat === TransferFormat.Binary) {\r\n            pollOptions.responseType = \"arraybuffer\";\r\n        }\r\n        // Make initial long polling request\r\n        // Server uses first long polling request to finish initializing connection and it returns without data\r\n        const pollUrl = `${url}&_=${Date.now()}`;\r\n        this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n        const response = await this._httpClient.get(pollUrl, pollOptions);\r\n        if (response.statusCode !== 200) {\r\n            this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n            // Mark running as false so that the poll immediately ends and runs the close logic\r\n            this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n            this._running = false;\r\n        }\r\n        else {\r\n            this._running = true;\r\n        }\r\n        this._receiving = this._poll(this._url, pollOptions);\r\n    }\r\n    async _poll(url, pollOptions) {\r\n        try {\r\n            while (this._running) {\r\n                try {\r\n                    const pollUrl = `${url}&_=${Date.now()}`;\r\n                    this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n                    const response = await this._httpClient.get(pollUrl, pollOptions);\r\n                    if (response.statusCode === 204) {\r\n                        this._logger.log(LogLevel.Information, \"(LongPolling transport) Poll terminated by server.\");\r\n                        this._running = false;\r\n                    }\r\n                    else if (response.statusCode !== 200) {\r\n                        this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n                        // Unexpected status code\r\n                        this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n                        this._running = false;\r\n                    }\r\n                    else {\r\n                        // Process the response\r\n                        if (response.content) {\r\n                            this._logger.log(LogLevel.Trace, `(LongPolling transport) data received. ${getDataDetail(response.content, this._options.logMessageContent)}.`);\r\n                            if (this.onreceive) {\r\n                                this.onreceive(response.content);\r\n                            }\r\n                        }\r\n                        else {\r\n                            // This is another way timeout manifest.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        }\r\n                    }\r\n                }\r\n                catch (e) {\r\n                    if (!this._running) {\r\n                        // Log but disregard errors that occur after stopping\r\n                        this._logger.log(LogLevel.Trace, `(LongPolling transport) Poll errored after shutdown: ${e.message}`);\r\n                    }\r\n                    else {\r\n                        if (e instanceof TimeoutError) {\r\n                            // Ignore timeouts and reissue the poll.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        }\r\n                        else {\r\n                            // Close the connection with the error as the result.\r\n                            this._closeError = e;\r\n                            this._running = false;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Polling complete.\");\r\n            // We will reach here with pollAborted==false when the server returned a response causing the transport to stop.\r\n            // If pollAborted==true then client initiated the stop and the stop method will raise the close event after DELETE is sent.\r\n            if (!this.pollAborted) {\r\n                this._raiseOnClose();\r\n            }\r\n        }\r\n    }\r\n    async send(data) {\r\n        if (!this._running) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"LongPolling\", this._httpClient, this._url, data, this._options);\r\n    }\r\n    async stop() {\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stopping polling.\");\r\n        // Tell receiving loop to stop, abort any current request, and then wait for it to finish\r\n        this._running = false;\r\n        this._pollAbort.abort();\r\n        try {\r\n            await this._receiving;\r\n            // Send DELETE to clean up long polling on the server\r\n            this._logger.log(LogLevel.Trace, `(LongPolling transport) sending DELETE request to ${this._url}.`);\r\n            const headers = {};\r\n            const [name, value] = getUserAgentHeader();\r\n            headers[name] = value;\r\n            const deleteOptions = {\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            };\r\n            let error;\r\n            try {\r\n                await this._httpClient.delete(this._url, deleteOptions);\r\n            }\r\n            catch (err) {\r\n                error = err;\r\n            }\r\n            if (error) {\r\n                if (error instanceof HttpError) {\r\n                    if (error.statusCode === 404) {\r\n                        this._logger.log(LogLevel.Trace, \"(LongPolling transport) A 404 response was returned from sending a DELETE request.\");\r\n                    }\r\n                    else {\r\n                        this._logger.log(LogLevel.Trace, `(LongPolling transport) Error sending a DELETE request: ${error}`);\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                this._logger.log(LogLevel.Trace, \"(LongPolling transport) DELETE request accepted.\");\r\n            }\r\n        }\r\n        finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stop finished.\");\r\n            // Raise close event here instead of in polling\r\n            // It needs to happen after the DELETE request is sent\r\n            this._raiseOnClose();\r\n        }\r\n    }\r\n    _raiseOnClose() {\r\n        if (this.onclose) {\r\n            let logMessage = \"(LongPolling transport) Firing onclose event.\";\r\n            if (this._closeError) {\r\n                logMessage += \" Error: \" + this._closeError;\r\n            }\r\n            this._logger.log(LogLevel.Trace, logMessage);\r\n            this.onclose(this._closeError);\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform, sendMessage } from \"./Utils\";\r\n/** @private */\r\nexport class ServerSentEventsTransport {\r\n    constructor(httpClient, accessToken, logger, options) {\r\n        this._httpClient = httpClient;\r\n        this._accessToken = accessToken;\r\n        this._logger = logger;\r\n        this._options = options;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n    async connect(url, transferFormat) {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._logger.log(LogLevel.Trace, \"(SSE transport) Connecting.\");\r\n        // set url before accessTokenFactory because this._url is only for send and we set the auth header instead of the query string for send\r\n        this._url = url;\r\n        if (this._accessToken) {\r\n            url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(this._accessToken)}`;\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n            let opened = false;\r\n            if (transferFormat !== TransferFormat.Text) {\r\n                reject(new Error(\"The Server-Sent Events transport only supports the 'Text' transfer format\"));\r\n                return;\r\n            }\r\n            let eventSource;\r\n            if (Platform.isBrowser || Platform.isWebWorker) {\r\n                eventSource = new this._options.EventSource(url, { withCredentials: this._options.withCredentials });\r\n            }\r\n            else {\r\n                // Non-browser passes cookies via the dictionary\r\n                const cookies = this._httpClient.getCookieString(url);\r\n                const headers = {};\r\n                headers.Cookie = cookies;\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n                eventSource = new this._options.EventSource(url, { withCredentials: this._options.withCredentials, headers: { ...headers, ...this._options.headers } });\r\n            }\r\n            try {\r\n                eventSource.onmessage = (e) => {\r\n                    if (this.onreceive) {\r\n                        try {\r\n                            this._logger.log(LogLevel.Trace, `(SSE transport) data received. ${getDataDetail(e.data, this._options.logMessageContent)}.`);\r\n                            this.onreceive(e.data);\r\n                        }\r\n                        catch (error) {\r\n                            this._close(error);\r\n                            return;\r\n                        }\r\n                    }\r\n                };\r\n                // @ts-ignore: not using event on purpose\r\n                eventSource.onerror = (e) => {\r\n                    // EventSource doesn't give any useful information about server side closes.\r\n                    if (opened) {\r\n                        this._close();\r\n                    }\r\n                    else {\r\n                        reject(new Error(\"EventSource failed to connect. The connection could not be found on the server,\"\r\n                            + \" either the connection ID is not present on the server, or a proxy is refusing/buffering the connection.\"\r\n                            + \" If you have multiple servers check that sticky sessions are enabled.\"));\r\n                    }\r\n                };\r\n                eventSource.onopen = () => {\r\n                    this._logger.log(LogLevel.Information, `SSE connected to ${this._url}`);\r\n                    this._eventSource = eventSource;\r\n                    opened = true;\r\n                    resolve();\r\n                };\r\n            }\r\n            catch (e) {\r\n                reject(e);\r\n                return;\r\n            }\r\n        });\r\n    }\r\n    async send(data) {\r\n        if (!this._eventSource) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"SSE\", this._httpClient, this._url, data, this._options);\r\n    }\r\n    stop() {\r\n        this._close();\r\n        return Promise.resolve();\r\n    }\r\n    _close(e) {\r\n        if (this._eventSource) {\r\n            this._eventSource.close();\r\n            this._eventSource = undefined;\r\n            if (this.onclose) {\r\n                this.onclose(e);\r\n            }\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform } from \"./Utils\";\r\n/** @private */\r\nexport class WebSocketTransport {\r\n    constructor(httpClient, accessTokenFactory, logger, logMessageContent, webSocketConstructor, headers) {\r\n        this._logger = logger;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logMessageContent = logMessageContent;\r\n        this._webSocketConstructor = webSocketConstructor;\r\n        this._httpClient = httpClient;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n        this._headers = headers;\r\n    }\r\n    async connect(url, transferFormat) {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) Connecting.\");\r\n        let token;\r\n        if (this._accessTokenFactory) {\r\n            token = await this._accessTokenFactory();\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n            url = url.replace(/^http/, \"ws\");\r\n            let webSocket;\r\n            const cookies = this._httpClient.getCookieString(url);\r\n            let opened = false;\r\n            if (Platform.isNode || Platform.isReactNative) {\r\n                const headers = {};\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n                if (token) {\r\n                    headers[HeaderNames.Authorization] = `Bearer ${token}`;\r\n                }\r\n                if (cookies) {\r\n                    headers[HeaderNames.Cookie] = cookies;\r\n                }\r\n                // Only pass headers when in non-browser environments\r\n                webSocket = new this._webSocketConstructor(url, undefined, {\r\n                    headers: { ...headers, ...this._headers },\r\n                });\r\n            }\r\n            else {\r\n                if (token) {\r\n                    url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\r\n                }\r\n            }\r\n            if (!webSocket) {\r\n                // Chrome is not happy with passing 'undefined' as protocol\r\n                webSocket = new this._webSocketConstructor(url);\r\n            }\r\n            if (transferFormat === TransferFormat.Binary) {\r\n                webSocket.binaryType = \"arraybuffer\";\r\n            }\r\n            webSocket.onopen = (_event) => {\r\n                this._logger.log(LogLevel.Information, `WebSocket connected to ${url}.`);\r\n                this._webSocket = webSocket;\r\n                opened = true;\r\n                resolve();\r\n            };\r\n            webSocket.onerror = (event) => {\r\n                let error = null;\r\n                // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                    error = event.error;\r\n                }\r\n                else {\r\n                    error = \"There was an error with the transport\";\r\n                }\r\n                this._logger.log(LogLevel.Information, `(WebSockets transport) ${error}.`);\r\n            };\r\n            webSocket.onmessage = (message) => {\r\n                this._logger.log(LogLevel.Trace, `(WebSockets transport) data received. ${getDataDetail(message.data, this._logMessageContent)}.`);\r\n                if (this.onreceive) {\r\n                    try {\r\n                        this.onreceive(message.data);\r\n                    }\r\n                    catch (error) {\r\n                        this._close(error);\r\n                        return;\r\n                    }\r\n                }\r\n            };\r\n            webSocket.onclose = (event) => {\r\n                // Don't call close handler if connection was never established\r\n                // We'll reject the connect call instead\r\n                if (opened) {\r\n                    this._close(event);\r\n                }\r\n                else {\r\n                    let error = null;\r\n                    // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                    if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                        error = event.error;\r\n                    }\r\n                    else {\r\n                        error = \"WebSocket failed to connect. The connection could not be found on the server,\"\r\n                            + \" either the endpoint may not be a SignalR endpoint,\"\r\n                            + \" the connection ID is not present on the server, or there is a proxy blocking WebSockets.\"\r\n                            + \" If you have multiple servers check that sticky sessions are enabled.\";\r\n                    }\r\n                    reject(new Error(error));\r\n                }\r\n            };\r\n        });\r\n    }\r\n    send(data) {\r\n        if (this._webSocket && this._webSocket.readyState === this._webSocketConstructor.OPEN) {\r\n            this._logger.log(LogLevel.Trace, `(WebSockets transport) sending data. ${getDataDetail(data, this._logMessageContent)}.`);\r\n            this._webSocket.send(data);\r\n            return Promise.resolve();\r\n        }\r\n        return Promise.reject(\"WebSocket is not in the OPEN state\");\r\n    }\r\n    stop() {\r\n        if (this._webSocket) {\r\n            // Manually invoke onclose callback inline so we know the HttpConnection was closed properly before returning\r\n            // This also solves an issue where websocket.onclose could take 18+ seconds to trigger during network disconnects\r\n            this._close(undefined);\r\n        }\r\n        return Promise.resolve();\r\n    }\r\n    _close(event) {\r\n        // webSocket will be null if the transport did not start successfully\r\n        if (this._webSocket) {\r\n            // Clear websocket handlers because we are considering the socket closed now\r\n            this._webSocket.onclose = () => { };\r\n            this._webSocket.onmessage = () => { };\r\n            this._webSocket.onerror = () => { };\r\n            this._webSocket.close();\r\n            this._webSocket = undefined;\r\n        }\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) socket closed.\");\r\n        if (this.onclose) {\r\n            if (this._isCloseEvent(event) && (event.wasClean === false || event.code !== 1000)) {\r\n                this.onclose(new Error(`WebSocket closed with status code: ${event.code} (${event.reason || \"no reason given\"}).`));\r\n            }\r\n            else if (event instanceof Error) {\r\n                this.onclose(event);\r\n            }\r\n            else {\r\n                this.onclose();\r\n            }\r\n        }\r\n    }\r\n    _isCloseEvent(event) {\r\n        return event && typeof event.wasClean === \"boolean\" && typeof event.code === \"number\";\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { AccessTokenHttpClient } from \"./AccessTokenHttpClient\";\r\nimport { DefaultHttpClient } from \"./DefaultHttpClient\";\r\nimport { AggregateErrors, DisabledTransportError, FailedToNegotiateWithServerError, FailedToStartTransportError, HttpError, UnsupportedTransportError, AbortError } from \"./Errors\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { HttpTransportType, TransferFormat } from \"./ITransport\";\r\nimport { LongPollingTransport } from \"./LongPollingTransport\";\r\nimport { ServerSentEventsTransport } from \"./ServerSentEventsTransport\";\r\nimport { Arg, createLogger, getUserAgentHeader, Platform } from \"./Utils\";\r\nimport { WebSocketTransport } from \"./WebSocketTransport\";\r\nconst MAX_REDIRECTS = 100;\r\n/** @private */\r\nexport class HttpConnection {\r\n    constructor(url, options = {}) {\r\n        this._stopPromiseResolver = () => { };\r\n        this.features = {};\r\n        this._negotiateVersion = 1;\r\n        Arg.isRequired(url, \"url\");\r\n        this._logger = createLogger(options.logger);\r\n        this.baseUrl = this._resolveUrl(url);\r\n        options = options || {};\r\n        options.logMessageContent = options.logMessageContent === undefined ? false : options.logMessageContent;\r\n        if (typeof options.withCredentials === \"boolean\" || options.withCredentials === undefined) {\r\n            options.withCredentials = options.withCredentials === undefined ? true : options.withCredentials;\r\n        }\r\n        else {\r\n            throw new Error(\"withCredentials option was not a 'boolean' or 'undefined' value\");\r\n        }\r\n        options.timeout = options.timeout === undefined ? 100 * 1000 : options.timeout;\r\n        let webSocketModule = null;\r\n        let eventSourceModule = null;\r\n        if (Platform.isNode && typeof require !== \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n            webSocketModule = requireFunc(\"ws\");\r\n            eventSourceModule = requireFunc(\"eventsource\");\r\n        }\r\n        if (!Platform.isNode && typeof WebSocket !== \"undefined\" && !options.WebSocket) {\r\n            options.WebSocket = WebSocket;\r\n        }\r\n        else if (Platform.isNode && !options.WebSocket) {\r\n            if (webSocketModule) {\r\n                options.WebSocket = webSocketModule;\r\n            }\r\n        }\r\n        if (!Platform.isNode && typeof EventSource !== \"undefined\" && !options.EventSource) {\r\n            options.EventSource = EventSource;\r\n        }\r\n        else if (Platform.isNode && !options.EventSource) {\r\n            if (typeof eventSourceModule !== \"undefined\") {\r\n                options.EventSource = eventSourceModule;\r\n            }\r\n        }\r\n        this._httpClient = new AccessTokenHttpClient(options.httpClient || new DefaultHttpClient(this._logger), options.accessTokenFactory);\r\n        this._connectionState = \"Disconnected\" /* ConnectionState.Disconnected */;\r\n        this._connectionStarted = false;\r\n        this._options = options;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n    async start(transferFormat) {\r\n        transferFormat = transferFormat || TransferFormat.Binary;\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._logger.log(LogLevel.Debug, `Starting connection with transfer format '${TransferFormat[transferFormat]}'.`);\r\n        if (this._connectionState !== \"Disconnected\" /* ConnectionState.Disconnected */) {\r\n            return Promise.reject(new Error(\"Cannot start an HttpConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n        this._connectionState = \"Connecting\" /* ConnectionState.Connecting */;\r\n        this._startInternalPromise = this._startInternal(transferFormat);\r\n        await this._startInternalPromise;\r\n        // The TypeScript compiler thinks that connectionState must be Connecting here. The TypeScript compiler is wrong.\r\n        if (this._connectionState === \"Disconnecting\" /* ConnectionState.Disconnecting */) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"Failed to start the HttpConnection before stop() was called.\";\r\n            this._logger.log(LogLevel.Error, message);\r\n            // We cannot await stopPromise inside startInternal since stopInternal awaits the startInternalPromise.\r\n            await this._stopPromise;\r\n            return Promise.reject(new AbortError(message));\r\n        }\r\n        else if (this._connectionState !== \"Connected\" /* ConnectionState.Connected */) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!\";\r\n            this._logger.log(LogLevel.Error, message);\r\n            return Promise.reject(new AbortError(message));\r\n        }\r\n        this._connectionStarted = true;\r\n    }\r\n    send(data) {\r\n        if (this._connectionState !== \"Connected\" /* ConnectionState.Connected */) {\r\n            return Promise.reject(new Error(\"Cannot send data if the connection is not in the 'Connected' State.\"));\r\n        }\r\n        if (!this._sendQueue) {\r\n            this._sendQueue = new TransportSendQueue(this.transport);\r\n        }\r\n        // Transport will not be null if state is connected\r\n        return this._sendQueue.send(data);\r\n    }\r\n    async stop(error) {\r\n        if (this._connectionState === \"Disconnected\" /* ConnectionState.Disconnected */) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n        if (this._connectionState === \"Disconnecting\" /* ConnectionState.Disconnecting */) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise;\r\n        }\r\n        this._connectionState = \"Disconnecting\" /* ConnectionState.Disconnecting */;\r\n        this._stopPromise = new Promise((resolve) => {\r\n            // Don't complete stop() until stopConnection() completes.\r\n            this._stopPromiseResolver = resolve;\r\n        });\r\n        // stopInternal should never throw so just observe it.\r\n        await this._stopInternal(error);\r\n        await this._stopPromise;\r\n    }\r\n    async _stopInternal(error) {\r\n        // Set error as soon as possible otherwise there is a race between\r\n        // the transport closing and providing an error and the error from a close message\r\n        // We would prefer the close message error.\r\n        this._stopError = error;\r\n        try {\r\n            await this._startInternalPromise;\r\n        }\r\n        catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n        // The transport's onclose will trigger stopConnection which will run our onclose event.\r\n        // The transport should always be set if currently connected. If it wasn't set, it's likely because\r\n        // stop was called during start() and start() failed.\r\n        if (this.transport) {\r\n            try {\r\n                await this.transport.stop();\r\n            }\r\n            catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.transport.stop() threw error '${e}'.`);\r\n                this._stopConnection();\r\n            }\r\n            this.transport = undefined;\r\n        }\r\n        else {\r\n            this._logger.log(LogLevel.Debug, \"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.\");\r\n        }\r\n    }\r\n    async _startInternal(transferFormat) {\r\n        // Store the original base url and the access token factory since they may change\r\n        // as part of negotiating\r\n        let url = this.baseUrl;\r\n        this._accessTokenFactory = this._options.accessTokenFactory;\r\n        this._httpClient._accessTokenFactory = this._accessTokenFactory;\r\n        try {\r\n            if (this._options.skipNegotiation) {\r\n                if (this._options.transport === HttpTransportType.WebSockets) {\r\n                    // No need to add a connection ID in this case\r\n                    this.transport = this._constructTransport(HttpTransportType.WebSockets);\r\n                    // We should just call connect directly in this case.\r\n                    // No fallback or negotiate in this case.\r\n                    await this._startTransport(url, transferFormat);\r\n                }\r\n                else {\r\n                    throw new Error(\"Negotiation can only be skipped when using the WebSocket transport directly.\");\r\n                }\r\n            }\r\n            else {\r\n                let negotiateResponse = null;\r\n                let redirects = 0;\r\n                do {\r\n                    negotiateResponse = await this._getNegotiationResponse(url);\r\n                    // the user tries to stop the connection when it is being started\r\n                    if (this._connectionState === \"Disconnecting\" /* ConnectionState.Disconnecting */ || this._connectionState === \"Disconnected\" /* ConnectionState.Disconnected */) {\r\n                        throw new AbortError(\"The connection was stopped during negotiation.\");\r\n                    }\r\n                    if (negotiateResponse.error) {\r\n                        throw new Error(negotiateResponse.error);\r\n                    }\r\n                    if (negotiateResponse.ProtocolVersion) {\r\n                        throw new Error(\"Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.\");\r\n                    }\r\n                    if (negotiateResponse.url) {\r\n                        url = negotiateResponse.url;\r\n                    }\r\n                    if (negotiateResponse.accessToken) {\r\n                        // Replace the current access token factory with one that uses\r\n                        // the returned access token\r\n                        const accessToken = negotiateResponse.accessToken;\r\n                        this._accessTokenFactory = () => accessToken;\r\n                        // set the factory to undefined so the AccessTokenHttpClient won't retry with the same token, since we know it won't change until a connection restart\r\n                        this._httpClient._accessToken = accessToken;\r\n                        this._httpClient._accessTokenFactory = undefined;\r\n                    }\r\n                    redirects++;\r\n                } while (negotiateResponse.url && redirects < MAX_REDIRECTS);\r\n                if (redirects === MAX_REDIRECTS && negotiateResponse.url) {\r\n                    throw new Error(\"Negotiate redirection limit exceeded.\");\r\n                }\r\n                await this._createTransport(url, this._options.transport, negotiateResponse, transferFormat);\r\n            }\r\n            if (this.transport instanceof LongPollingTransport) {\r\n                this.features.inherentKeepAlive = true;\r\n            }\r\n            if (this._connectionState === \"Connecting\" /* ConnectionState.Connecting */) {\r\n                // Ensure the connection transitions to the connected state prior to completing this.startInternalPromise.\r\n                // start() will handle the case when stop was called and startInternal exits still in the disconnecting state.\r\n                this._logger.log(LogLevel.Debug, \"The HttpConnection connected successfully.\");\r\n                this._connectionState = \"Connected\" /* ConnectionState.Connected */;\r\n            }\r\n            // stop() is waiting on us via this.startInternalPromise so keep this.transport around so it can clean up.\r\n            // This is the only case startInternal can exit in neither the connected nor disconnected state because stopConnection()\r\n            // will transition to the disconnected state. start() will wait for the transition using the stopPromise.\r\n        }\r\n        catch (e) {\r\n            this._logger.log(LogLevel.Error, \"Failed to start the connection: \" + e);\r\n            this._connectionState = \"Disconnected\" /* ConnectionState.Disconnected */;\r\n            this.transport = undefined;\r\n            // if start fails, any active calls to stop assume that start will complete the stop promise\r\n            this._stopPromiseResolver();\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n    async _getNegotiationResponse(url) {\r\n        const headers = {};\r\n        const [name, value] = getUserAgentHeader();\r\n        headers[name] = value;\r\n        const negotiateUrl = this._resolveNegotiateUrl(url);\r\n        this._logger.log(LogLevel.Debug, `Sending negotiation request: ${negotiateUrl}.`);\r\n        try {\r\n            const response = await this._httpClient.post(negotiateUrl, {\r\n                content: \"\",\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            });\r\n            if (response.statusCode !== 200) {\r\n                return Promise.reject(new Error(`Unexpected status code returned from negotiate '${response.statusCode}'`));\r\n            }\r\n            const negotiateResponse = JSON.parse(response.content);\r\n            if (!negotiateResponse.negotiateVersion || negotiateResponse.negotiateVersion < 1) {\r\n                // Negotiate version 0 doesn't use connectionToken\r\n                // So we set it equal to connectionId so all our logic can use connectionToken without being aware of the negotiate version\r\n                negotiateResponse.connectionToken = negotiateResponse.connectionId;\r\n            }\r\n            if (negotiateResponse.useStatefulReconnect && this._options._useStatefulReconnect !== true) {\r\n                return Promise.reject(new FailedToNegotiateWithServerError(\"Client didn't negotiate Stateful Reconnect but the server did.\"));\r\n            }\r\n            return negotiateResponse;\r\n        }\r\n        catch (e) {\r\n            let errorMessage = \"Failed to complete negotiation with the server: \" + e;\r\n            if (e instanceof HttpError) {\r\n                if (e.statusCode === 404) {\r\n                    errorMessage = errorMessage + \" Either this is not a SignalR endpoint or there is a proxy blocking the connection.\";\r\n                }\r\n            }\r\n            this._logger.log(LogLevel.Error, errorMessage);\r\n            return Promise.reject(new FailedToNegotiateWithServerError(errorMessage));\r\n        }\r\n    }\r\n    _createConnectUrl(url, connectionToken) {\r\n        if (!connectionToken) {\r\n            return url;\r\n        }\r\n        return url + (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + `id=${connectionToken}`;\r\n    }\r\n    async _createTransport(url, requestedTransport, negotiateResponse, requestedTransferFormat) {\r\n        let connectUrl = this._createConnectUrl(url, negotiateResponse.connectionToken);\r\n        if (this._isITransport(requestedTransport)) {\r\n            this._logger.log(LogLevel.Debug, \"Connection was provided an instance of ITransport, using that directly.\");\r\n            this.transport = requestedTransport;\r\n            await this._startTransport(connectUrl, requestedTransferFormat);\r\n            this.connectionId = negotiateResponse.connectionId;\r\n            return;\r\n        }\r\n        const transportExceptions = [];\r\n        const transports = negotiateResponse.availableTransports || [];\r\n        let negotiate = negotiateResponse;\r\n        for (const endpoint of transports) {\r\n            const transportOrError = this._resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat, (negotiate === null || negotiate === void 0 ? void 0 : negotiate.useStatefulReconnect) === true);\r\n            if (transportOrError instanceof Error) {\r\n                // Store the error and continue, we don't want to cause a re-negotiate in these cases\r\n                transportExceptions.push(`${endpoint.transport} failed:`);\r\n                transportExceptions.push(transportOrError);\r\n            }\r\n            else if (this._isITransport(transportOrError)) {\r\n                this.transport = transportOrError;\r\n                if (!negotiate) {\r\n                    try {\r\n                        negotiate = await this._getNegotiationResponse(url);\r\n                    }\r\n                    catch (ex) {\r\n                        return Promise.reject(ex);\r\n                    }\r\n                    connectUrl = this._createConnectUrl(url, negotiate.connectionToken);\r\n                }\r\n                try {\r\n                    await this._startTransport(connectUrl, requestedTransferFormat);\r\n                    this.connectionId = negotiate.connectionId;\r\n                    return;\r\n                }\r\n                catch (ex) {\r\n                    this._logger.log(LogLevel.Error, `Failed to start the transport '${endpoint.transport}': ${ex}`);\r\n                    negotiate = undefined;\r\n                    transportExceptions.push(new FailedToStartTransportError(`${endpoint.transport} failed: ${ex}`, HttpTransportType[endpoint.transport]));\r\n                    if (this._connectionState !== \"Connecting\" /* ConnectionState.Connecting */) {\r\n                        const message = \"Failed to select transport before stop() was called.\";\r\n                        this._logger.log(LogLevel.Debug, message);\r\n                        return Promise.reject(new AbortError(message));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        if (transportExceptions.length > 0) {\r\n            return Promise.reject(new AggregateErrors(`Unable to connect to the server with any of the available transports. ${transportExceptions.join(\" \")}`, transportExceptions));\r\n        }\r\n        return Promise.reject(new Error(\"None of the transports supported by the client are supported by the server.\"));\r\n    }\r\n    _constructTransport(transport) {\r\n        switch (transport) {\r\n            case HttpTransportType.WebSockets:\r\n                if (!this._options.WebSocket) {\r\n                    throw new Error(\"'WebSocket' is not supported in your environment.\");\r\n                }\r\n                return new WebSocketTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options.logMessageContent, this._options.WebSocket, this._options.headers || {});\r\n            case HttpTransportType.ServerSentEvents:\r\n                if (!this._options.EventSource) {\r\n                    throw new Error(\"'EventSource' is not supported in your environment.\");\r\n                }\r\n                return new ServerSentEventsTransport(this._httpClient, this._httpClient._accessToken, this._logger, this._options);\r\n            case HttpTransportType.LongPolling:\r\n                return new LongPollingTransport(this._httpClient, this._logger, this._options);\r\n            default:\r\n                throw new Error(`Unknown transport: ${transport}.`);\r\n        }\r\n    }\r\n    _startTransport(url, transferFormat) {\r\n        this.transport.onreceive = this.onreceive;\r\n        if (this.features.reconnect) {\r\n            this.transport.onclose = async (e) => {\r\n                let callStop = false;\r\n                if (this.features.reconnect) {\r\n                    try {\r\n                        this.features.disconnected();\r\n                        await this.transport.connect(url, transferFormat);\r\n                        await this.features.resend();\r\n                    }\r\n                    catch {\r\n                        callStop = true;\r\n                    }\r\n                }\r\n                else {\r\n                    this._stopConnection(e);\r\n                    return;\r\n                }\r\n                if (callStop) {\r\n                    this._stopConnection(e);\r\n                }\r\n            };\r\n        }\r\n        else {\r\n            this.transport.onclose = (e) => this._stopConnection(e);\r\n        }\r\n        return this.transport.connect(url, transferFormat);\r\n    }\r\n    _resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat, useStatefulReconnect) {\r\n        const transport = HttpTransportType[endpoint.transport];\r\n        if (transport === null || transport === undefined) {\r\n            this._logger.log(LogLevel.Debug, `Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n            return new Error(`Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n        }\r\n        else {\r\n            if (transportMatches(requestedTransport, transport)) {\r\n                const transferFormats = endpoint.transferFormats.map((s) => TransferFormat[s]);\r\n                if (transferFormats.indexOf(requestedTransferFormat) >= 0) {\r\n                    if ((transport === HttpTransportType.WebSockets && !this._options.WebSocket) ||\r\n                        (transport === HttpTransportType.ServerSentEvents && !this._options.EventSource)) {\r\n                        this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it is not supported in your environment.'`);\r\n                        return new UnsupportedTransportError(`'${HttpTransportType[transport]}' is not supported in your environment.`, transport);\r\n                    }\r\n                    else {\r\n                        this._logger.log(LogLevel.Debug, `Selecting transport '${HttpTransportType[transport]}'.`);\r\n                        try {\r\n                            this.features.reconnect = transport === HttpTransportType.WebSockets ? useStatefulReconnect : undefined;\r\n                            return this._constructTransport(transport);\r\n                        }\r\n                        catch (ex) {\r\n                            return ex;\r\n                        }\r\n                    }\r\n                }\r\n                else {\r\n                    this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it does not support the requested transfer format '${TransferFormat[requestedTransferFormat]}'.`);\r\n                    return new Error(`'${HttpTransportType[transport]}' does not support ${TransferFormat[requestedTransferFormat]}.`);\r\n                }\r\n            }\r\n            else {\r\n                this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it was disabled by the client.`);\r\n                return new DisabledTransportError(`'${HttpTransportType[transport]}' is disabled by the client.`, transport);\r\n            }\r\n        }\r\n    }\r\n    _isITransport(transport) {\r\n        return transport && typeof (transport) === \"object\" && \"connect\" in transport;\r\n    }\r\n    _stopConnection(error) {\r\n        this._logger.log(LogLevel.Debug, `HttpConnection.stopConnection(${error}) called while in state ${this._connectionState}.`);\r\n        this.transport = undefined;\r\n        // If we have a stopError, it takes precedence over the error from the transport\r\n        error = this._stopError || error;\r\n        this._stopError = undefined;\r\n        if (this._connectionState === \"Disconnected\" /* ConnectionState.Disconnected */) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is already in the disconnected state.`);\r\n            return;\r\n        }\r\n        if (this._connectionState === \"Connecting\" /* ConnectionState.Connecting */) {\r\n            this._logger.log(LogLevel.Warning, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is still in the connecting state.`);\r\n            throw new Error(`HttpConnection.stopConnection(${error}) was called while the connection is still in the connecting state.`);\r\n        }\r\n        if (this._connectionState === \"Disconnecting\" /* ConnectionState.Disconnecting */) {\r\n            // A call to stop() induced this call to stopConnection and needs to be completed.\r\n            // Any stop() awaiters will be scheduled to continue after the onclose callback fires.\r\n            this._stopPromiseResolver();\r\n        }\r\n        if (error) {\r\n            this._logger.log(LogLevel.Error, `Connection disconnected with error '${error}'.`);\r\n        }\r\n        else {\r\n            this._logger.log(LogLevel.Information, \"Connection disconnected.\");\r\n        }\r\n        if (this._sendQueue) {\r\n            this._sendQueue.stop().catch((e) => {\r\n                this._logger.log(LogLevel.Error, `TransportSendQueue.stop() threw error '${e}'.`);\r\n            });\r\n            this._sendQueue = undefined;\r\n        }\r\n        this.connectionId = undefined;\r\n        this._connectionState = \"Disconnected\" /* ConnectionState.Disconnected */;\r\n        if (this._connectionStarted) {\r\n            this._connectionStarted = false;\r\n            try {\r\n                if (this.onclose) {\r\n                    this.onclose(error);\r\n                }\r\n            }\r\n            catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.onclose(${error}) threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n    _resolveUrl(url) {\r\n        // startsWith is not supported in IE\r\n        if (url.lastIndexOf(\"https://\", 0) === 0 || url.lastIndexOf(\"http://\", 0) === 0) {\r\n            return url;\r\n        }\r\n        if (!Platform.isBrowser) {\r\n            throw new Error(`Cannot resolve '${url}'.`);\r\n        }\r\n        // Setting the url to the href propery of an anchor tag handles normalization\r\n        // for us. There are 3 main cases.\r\n        // 1. Relative path normalization e.g \"b\" -> \"http://localhost:5000/a/b\"\r\n        // 2. Absolute path normalization e.g \"/a/b\" -> \"http://localhost:5000/a/b\"\r\n        // 3. Networkpath reference normalization e.g \"//localhost:5000/a/b\" -> \"http://localhost:5000/a/b\"\r\n        const aTag = window.document.createElement(\"a\");\r\n        aTag.href = url;\r\n        this._logger.log(LogLevel.Information, `Normalizing '${url}' to '${aTag.href}'.`);\r\n        return aTag.href;\r\n    }\r\n    _resolveNegotiateUrl(url) {\r\n        const negotiateUrl = new URL(url);\r\n        if (negotiateUrl.pathname.endsWith('/')) {\r\n            negotiateUrl.pathname += \"negotiate\";\r\n        }\r\n        else {\r\n            negotiateUrl.pathname += \"/negotiate\";\r\n        }\r\n        const searchParams = new URLSearchParams(negotiateUrl.searchParams);\r\n        if (!searchParams.has(\"negotiateVersion\")) {\r\n            searchParams.append(\"negotiateVersion\", this._negotiateVersion.toString());\r\n        }\r\n        if (searchParams.has(\"useStatefulReconnect\")) {\r\n            if (searchParams.get(\"useStatefulReconnect\") === \"true\") {\r\n                this._options._useStatefulReconnect = true;\r\n            }\r\n        }\r\n        else if (this._options._useStatefulReconnect === true) {\r\n            searchParams.append(\"useStatefulReconnect\", \"true\");\r\n        }\r\n        negotiateUrl.search = searchParams.toString();\r\n        return negotiateUrl.toString();\r\n    }\r\n}\r\nfunction transportMatches(requestedTransport, actualTransport) {\r\n    return !requestedTransport || ((actualTransport & requestedTransport) !== 0);\r\n}\r\n/** @private */\r\nexport class TransportSendQueue {\r\n    constructor(_transport) {\r\n        this._transport = _transport;\r\n        this._buffer = [];\r\n        this._executing = true;\r\n        this._sendBufferedData = new PromiseSource();\r\n        this._transportResult = new PromiseSource();\r\n        this._sendLoopPromise = this._sendLoop();\r\n    }\r\n    send(data) {\r\n        this._bufferData(data);\r\n        if (!this._transportResult) {\r\n            this._transportResult = new PromiseSource();\r\n        }\r\n        return this._transportResult.promise;\r\n    }\r\n    stop() {\r\n        this._executing = false;\r\n        this._sendBufferedData.resolve();\r\n        return this._sendLoopPromise;\r\n    }\r\n    _bufferData(data) {\r\n        if (this._buffer.length && typeof (this._buffer[0]) !== typeof (data)) {\r\n            throw new Error(`Expected data to be of type ${typeof (this._buffer)} but was of type ${typeof (data)}`);\r\n        }\r\n        this._buffer.push(data);\r\n        this._sendBufferedData.resolve();\r\n    }\r\n    async _sendLoop() {\r\n        while (true) {\r\n            await this._sendBufferedData.promise;\r\n            if (!this._executing) {\r\n                if (this._transportResult) {\r\n                    this._transportResult.reject(\"Connection stopped.\");\r\n                }\r\n                break;\r\n            }\r\n            this._sendBufferedData = new PromiseSource();\r\n            const transportResult = this._transportResult;\r\n            this._transportResult = undefined;\r\n            const data = typeof (this._buffer[0]) === \"string\" ?\r\n                this._buffer.join(\"\") :\r\n                TransportSendQueue._concatBuffers(this._buffer);\r\n            this._buffer.length = 0;\r\n            try {\r\n                await this._transport.send(data);\r\n                transportResult.resolve();\r\n            }\r\n            catch (error) {\r\n                transportResult.reject(error);\r\n            }\r\n        }\r\n    }\r\n    static _concatBuffers(arrayBuffers) {\r\n        const totalLength = arrayBuffers.map((b) => b.byteLength).reduce((a, b) => a + b);\r\n        const result = new Uint8Array(totalLength);\r\n        let offset = 0;\r\n        for (const item of arrayBuffers) {\r\n            result.set(new Uint8Array(item), offset);\r\n            offset += item.byteLength;\r\n        }\r\n        return result.buffer;\r\n    }\r\n}\r\nclass PromiseSource {\r\n    constructor() {\r\n        this.promise = new Promise((resolve, reject) => [this._resolver, this._rejecter] = [resolve, reject]);\r\n    }\r\n    resolve() {\r\n        this._resolver();\r\n    }\r\n    reject(reason) {\r\n        this._rejecter(reason);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { MessageType } from \"./IHubProtocol\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\nconst JSON_HUB_PROTOCOL_NAME = \"json\";\r\n/** Implements the JSON Hub Protocol. */\r\nexport class JsonHubProtocol {\r\n    constructor() {\r\n        /** @inheritDoc */\r\n        this.name = JSON_HUB_PROTOCOL_NAME;\r\n        /** @inheritDoc */\r\n        this.version = 2;\r\n        /** @inheritDoc */\r\n        this.transferFormat = TransferFormat.Text;\r\n    }\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * @param {string} input A string containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    parseMessages(input, logger) {\r\n        // The interface does allow \"ArrayBuffer\" to be passed in, but this implementation does not. So let's throw a useful error.\r\n        if (typeof input !== \"string\") {\r\n            throw new Error(\"Invalid input for JSON hub protocol. Expected a string.\");\r\n        }\r\n        if (!input) {\r\n            return [];\r\n        }\r\n        if (logger === null) {\r\n            logger = NullLogger.instance;\r\n        }\r\n        // Parse the messages\r\n        const messages = TextMessageFormat.parse(input);\r\n        const hubMessages = [];\r\n        for (const message of messages) {\r\n            const parsedMessage = JSON.parse(message);\r\n            if (typeof parsedMessage.type !== \"number\") {\r\n                throw new Error(\"Invalid payload.\");\r\n            }\r\n            switch (parsedMessage.type) {\r\n                case MessageType.Invocation:\r\n                    this._isInvocationMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.StreamItem:\r\n                    this._isStreamItemMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Completion:\r\n                    this._isCompletionMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Ping:\r\n                    // Single value, no need to validate\r\n                    break;\r\n                case MessageType.Close:\r\n                    // All optional values, no need to validate\r\n                    break;\r\n                case MessageType.Ack:\r\n                    this._isAckMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Sequence:\r\n                    this._isSequenceMessage(parsedMessage);\r\n                    break;\r\n                default:\r\n                    // Future protocol changes can add message types, old clients can ignore them\r\n                    logger.log(LogLevel.Information, \"Unknown message type '\" + parsedMessage.type + \"' ignored.\");\r\n                    continue;\r\n            }\r\n            hubMessages.push(parsedMessage);\r\n        }\r\n        return hubMessages;\r\n    }\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string and returns it.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string} A string containing the serialized representation of the message.\r\n     */\r\n    writeMessage(message) {\r\n        return TextMessageFormat.write(JSON.stringify(message));\r\n    }\r\n    _isInvocationMessage(message) {\r\n        this._assertNotEmptyString(message.target, \"Invalid payload for Invocation message.\");\r\n        if (message.invocationId !== undefined) {\r\n            this._assertNotEmptyString(message.invocationId, \"Invalid payload for Invocation message.\");\r\n        }\r\n    }\r\n    _isStreamItemMessage(message) {\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for StreamItem message.\");\r\n        if (message.item === undefined) {\r\n            throw new Error(\"Invalid payload for StreamItem message.\");\r\n        }\r\n    }\r\n    _isCompletionMessage(message) {\r\n        if (message.result && message.error) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n        if (!message.result && message.error) {\r\n            this._assertNotEmptyString(message.error, \"Invalid payload for Completion message.\");\r\n        }\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for Completion message.\");\r\n    }\r\n    _isAckMessage(message) {\r\n        if (typeof message.sequenceId !== 'number') {\r\n            throw new Error(\"Invalid SequenceId for Ack message.\");\r\n        }\r\n    }\r\n    _isSequenceMessage(message) {\r\n        if (typeof message.sequenceId !== 'number') {\r\n            throw new Error(\"Invalid SequenceId for Sequence message.\");\r\n        }\r\n    }\r\n    _assertNotEmptyString(value, errorMessage) {\r\n        if (typeof value !== \"string\" || value === \"\") {\r\n            throw new Error(errorMessage);\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { DefaultReconnectPolicy } from \"./DefaultReconnectPolicy\";\r\nimport { HttpConnection } from \"./HttpConnection\";\r\nimport { HubConnection } from \"./HubConnection\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { JsonHubProtocol } from \"./JsonHubProtocol\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { Arg, ConsoleLogger } from \"./Utils\";\r\nconst LogLevelNameMapping = {\r\n    trace: LogLevel.Trace,\r\n    debug: LogLevel.Debug,\r\n    info: LogLevel.Information,\r\n    information: LogLevel.Information,\r\n    warn: LogLevel.Warning,\r\n    warning: LogLevel.Warning,\r\n    error: LogLevel.Error,\r\n    critical: LogLevel.Critical,\r\n    none: LogLevel.None,\r\n};\r\nfunction parseLogLevel(name) {\r\n    // Case-insensitive matching via lower-casing\r\n    // Yes, I know case-folding is a complicated problem in Unicode, but we only support\r\n    // the ASCII strings defined in LogLevelNameMapping anyway, so it's fine -anurse.\r\n    const mapping = LogLevelNameMapping[name.toLowerCase()];\r\n    if (typeof mapping !== \"undefined\") {\r\n        return mapping;\r\n    }\r\n    else {\r\n        throw new Error(`Unknown log level: ${name}`);\r\n    }\r\n}\r\n/** A builder for configuring {@link @microsoft/signalr.HubConnection} instances. */\r\nexport class HubConnectionBuilder {\r\n    configureLogging(logging) {\r\n        Arg.isRequired(logging, \"logging\");\r\n        if (isLogger(logging)) {\r\n            this.logger = logging;\r\n        }\r\n        else if (typeof logging === \"string\") {\r\n            const logLevel = parseLogLevel(logging);\r\n            this.logger = new ConsoleLogger(logLevel);\r\n        }\r\n        else {\r\n            this.logger = new ConsoleLogger(logging);\r\n        }\r\n        return this;\r\n    }\r\n    withUrl(url, transportTypeOrOptions) {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isNotEmpty(url, \"url\");\r\n        this.url = url;\r\n        // Flow-typing knows where it's at. Since HttpTransportType is a number and IHttpConnectionOptions is guaranteed\r\n        // to be an object, we know (as does TypeScript) this comparison is all we need to figure out which overload was called.\r\n        if (typeof transportTypeOrOptions === \"object\") {\r\n            this.httpConnectionOptions = { ...this.httpConnectionOptions, ...transportTypeOrOptions };\r\n        }\r\n        else {\r\n            this.httpConnectionOptions = {\r\n                ...this.httpConnectionOptions,\r\n                transport: transportTypeOrOptions,\r\n            };\r\n        }\r\n        return this;\r\n    }\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified Hub Protocol.\r\n     *\r\n     * @param {IHubProtocol} protocol The {@link @microsoft/signalr.IHubProtocol} implementation to use.\r\n     */\r\n    withHubProtocol(protocol) {\r\n        Arg.isRequired(protocol, \"protocol\");\r\n        this.protocol = protocol;\r\n        return this;\r\n    }\r\n    withAutomaticReconnect(retryDelaysOrReconnectPolicy) {\r\n        if (this.reconnectPolicy) {\r\n            throw new Error(\"A reconnectPolicy has already been set.\");\r\n        }\r\n        if (!retryDelaysOrReconnectPolicy) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy();\r\n        }\r\n        else if (Array.isArray(retryDelaysOrReconnectPolicy)) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy(retryDelaysOrReconnectPolicy);\r\n        }\r\n        else {\r\n            this.reconnectPolicy = retryDelaysOrReconnectPolicy;\r\n        }\r\n        return this;\r\n    }\r\n    /** Configures {@link @microsoft/signalr.HubConnection.serverTimeoutInMilliseconds} for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    withServerTimeout(milliseconds) {\r\n        Arg.isRequired(milliseconds, \"milliseconds\");\r\n        this._serverTimeoutInMilliseconds = milliseconds;\r\n        return this;\r\n    }\r\n    /** Configures {@link @microsoft/signalr.HubConnection.keepAliveIntervalInMilliseconds} for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    withKeepAliveInterval(milliseconds) {\r\n        Arg.isRequired(milliseconds, \"milliseconds\");\r\n        this._keepAliveIntervalInMilliseconds = milliseconds;\r\n        return this;\r\n    }\r\n    /** Enables and configures options for the Stateful Reconnect feature.\r\n     *\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    withStatefulReconnect(options) {\r\n        if (this.httpConnectionOptions === undefined) {\r\n            this.httpConnectionOptions = {};\r\n        }\r\n        this.httpConnectionOptions._useStatefulReconnect = true;\r\n        this._statefulReconnectBufferSize = options === null || options === void 0 ? void 0 : options.bufferSize;\r\n        return this;\r\n    }\r\n    /** Creates a {@link @microsoft/signalr.HubConnection} from the configuration options specified in this builder.\r\n     *\r\n     * @returns {HubConnection} The configured {@link @microsoft/signalr.HubConnection}.\r\n     */\r\n    build() {\r\n        // If httpConnectionOptions has a logger, use it. Otherwise, override it with the one\r\n        // provided to configureLogger\r\n        const httpConnectionOptions = this.httpConnectionOptions || {};\r\n        // If it's 'null', the user **explicitly** asked for null, don't mess with it.\r\n        if (httpConnectionOptions.logger === undefined) {\r\n            // If our logger is undefined or null, that's OK, the HttpConnection constructor will handle it.\r\n            httpConnectionOptions.logger = this.logger;\r\n        }\r\n        // Now create the connection\r\n        if (!this.url) {\r\n            throw new Error(\"The 'HubConnectionBuilder.withUrl' method must be called before building the connection.\");\r\n        }\r\n        const connection = new HttpConnection(this.url, httpConnectionOptions);\r\n        return HubConnection.create(connection, this.logger || NullLogger.instance, this.protocol || new JsonHubProtocol(), this.reconnectPolicy, this._serverTimeoutInMilliseconds, this._keepAliveIntervalInMilliseconds, this._statefulReconnectBufferSize);\r\n    }\r\n}\r\nfunction isLogger(logger) {\r\n    return logger.log !== undefined;\r\n}\r\n"], "mappings": ";;;;;;;;AAGO,IAAM,YAAN,cAAwB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjC,YAAY,cAAc,YAAY;AAClC,UAAM,YAAY,WAAW;AAC7B,UAAM,GAAG,YAAY,kBAAkB,UAAU,GAAG;AACpD,SAAK,aAAa;AAGlB,SAAK,YAAY;AAAA,EACrB;AACJ;AAEO,IAAM,eAAN,cAA2B,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpC,YAAY,eAAe,uBAAuB;AAC9C,UAAM,YAAY,WAAW;AAC7B,UAAM,YAAY;AAGlB,SAAK,YAAY;AAAA,EACrB;AACJ;AAEO,IAAM,aAAN,cAAyB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,YAAY,eAAe,sBAAsB;AAC7C,UAAM,YAAY,WAAW;AAC7B,UAAM,YAAY;AAGlB,SAAK,YAAY;AAAA,EACrB;AACJ;AAGO,IAAM,4BAAN,cAAwC,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjD,YAAY,SAAS,WAAW;AAC5B,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AAGjB,SAAK,YAAY;AAAA,EACrB;AACJ;AAGO,IAAM,yBAAN,cAAqC,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9C,YAAY,SAAS,WAAW;AAC5B,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AAGjB,SAAK,YAAY;AAAA,EACrB;AACJ;AAGO,IAAM,8BAAN,cAA0C,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnD,YAAY,SAAS,WAAW;AAC5B,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AAGjB,SAAK,YAAY;AAAA,EACrB;AACJ;AAGO,IAAM,mCAAN,cAA+C,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxD,YAAY,SAAS;AACjB,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,YAAY;AAGjB,SAAK,YAAY;AAAA,EACrB;AACJ;AAGO,IAAM,kBAAN,cAA8B,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvC,YAAY,SAAS,aAAa;AAC9B,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,cAAc;AAGnB,SAAK,YAAY;AAAA,EACrB;AACJ;;;ACjIO,IAAM,eAAN,MAAmB;AAAA,EACtB,YAAY,YAAY,YAAY,SAAS;AACzC,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,UAAU;AAAA,EACnB;AACJ;AAKO,IAAM,aAAN,MAAiB;AAAA,EACpB,IAAI,KAAK,SAAS;AACd,WAAO,KAAK,KAAK,iCACV,UADU;AAAA,MAEb,QAAQ;AAAA,MACR;AAAA,IACJ,EAAC;AAAA,EACL;AAAA,EACA,KAAK,KAAK,SAAS;AACf,WAAO,KAAK,KAAK,iCACV,UADU;AAAA,MAEb,QAAQ;AAAA,MACR;AAAA,IACJ,EAAC;AAAA,EACL;AAAA,EACA,OAAO,KAAK,SAAS;AACjB,WAAO,KAAK,KAAK,iCACV,UADU;AAAA,MAEb,QAAQ;AAAA,MACR;AAAA,IACJ,EAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,KAAK;AACjB,WAAO;AAAA,EACX;AACJ;;;ACtCO,IAAI;AAAA,CACV,SAAUA,WAAU;AAEjB,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAElC,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAElC,EAAAA,UAASA,UAAS,aAAa,IAAI,CAAC,IAAI;AAExC,EAAAA,UAASA,UAAS,SAAS,IAAI,CAAC,IAAI;AAEpC,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAElC,EAAAA,UAASA,UAAS,UAAU,IAAI,CAAC,IAAI;AAErC,EAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACrC,GAAG,aAAa,WAAW,CAAC,EAAE;;;ACpBvB,IAAM,aAAN,MAAiB;AAAA,EACpB,cAAc;AAAA,EAAE;AAAA;AAAA;AAAA,EAGhB,IAAI,WAAW,UAAU;AAAA,EACzB;AACJ;AAEA,WAAW,WAAW,IAAI,WAAW;;;ACL9B,IAAM,UAAU;AAEhB,IAAM,MAAN,MAAU;AAAA,EACb,OAAO,WAAW,KAAK,MAAM;AACzB,QAAI,QAAQ,QAAQ,QAAQ,QAAW;AACnC,YAAM,IAAI,MAAM,QAAQ,IAAI,yBAAyB;AAAA,IACzD;AAAA,EACJ;AAAA,EACA,OAAO,WAAW,KAAK,MAAM;AACzB,QAAI,CAAC,OAAO,IAAI,MAAM,OAAO,GAAG;AAC5B,YAAM,IAAI,MAAM,QAAQ,IAAI,iCAAiC;AAAA,IACjE;AAAA,EACJ;AAAA,EACA,OAAO,KAAK,KAAK,QAAQ,MAAM;AAE3B,QAAI,EAAE,OAAO,SAAS;AAClB,YAAM,IAAI,MAAM,WAAW,IAAI,WAAW,GAAG,GAAG;AAAA,IACpD;AAAA,EACJ;AACJ;AAEO,IAAM,WAAN,MAAM,UAAS;AAAA;AAAA,EAElB,WAAW,YAAY;AACnB,WAAO,CAAC,UAAS,UAAU,OAAO,WAAW,YAAY,OAAO,OAAO,aAAa;AAAA,EACxF;AAAA;AAAA,EAEA,WAAW,cAAc;AACrB,WAAO,CAAC,UAAS,UAAU,OAAO,SAAS,YAAY,mBAAmB;AAAA,EAC9E;AAAA;AAAA,EAEA,WAAW,gBAAgB;AACvB,WAAO,CAAC,UAAS,UAAU,OAAO,WAAW,YAAY,OAAO,OAAO,aAAa;AAAA,EACxF;AAAA;AAAA;AAAA,EAGA,WAAW,SAAS;AAChB,WAAO,OAAO,YAAY,eAAe,QAAQ,WAAW,QAAQ,QAAQ,SAAS;AAAA,EACzF;AACJ;AAEO,SAAS,cAAc,MAAM,gBAAgB;AAChD,MAAI,SAAS;AACb,MAAI,cAAc,IAAI,GAAG;AACrB,aAAS,yBAAyB,KAAK,UAAU;AACjD,QAAI,gBAAgB;AAChB,gBAAU,eAAe,kBAAkB,IAAI,CAAC;AAAA,IACpD;AAAA,EACJ,WACS,OAAO,SAAS,UAAU;AAC/B,aAAS,yBAAyB,KAAK,MAAM;AAC7C,QAAI,gBAAgB;AAChB,gBAAU,eAAe,IAAI;AAAA,IACjC;AAAA,EACJ;AACA,SAAO;AACX;AAEO,SAAS,kBAAkB,MAAM;AACpC,QAAM,OAAO,IAAI,WAAW,IAAI;AAEhC,MAAI,MAAM;AACV,OAAK,QAAQ,CAAC,QAAQ;AAClB,UAAM,MAAM,MAAM,KAAK,MAAM;AAC7B,WAAO,KAAK,GAAG,GAAG,IAAI,SAAS,EAAE,CAAC;AAAA,EACtC,CAAC;AAED,SAAO,IAAI,OAAO,GAAG,IAAI,SAAS,CAAC;AACvC;AAGO,SAAS,cAAc,KAAK;AAC/B,SAAO,OAAO,OAAO,gBAAgB,gBAChC,eAAe;AAAA,EAEX,IAAI,eAAe,IAAI,YAAY,SAAS;AACzD;AAEA,SAAsB,YAAY,QAAQ,eAAe,YAAY,KAAK,SAAS,SAAS;AAAA;AACxF,UAAM,UAAU,CAAC;AACjB,UAAM,CAAC,MAAM,KAAK,IAAI,mBAAmB;AACzC,YAAQ,IAAI,IAAI;AAChB,WAAO,IAAI,SAAS,OAAO,IAAI,aAAa,6BAA6B,cAAc,SAAS,QAAQ,iBAAiB,CAAC,GAAG;AAC7H,UAAM,eAAe,cAAc,OAAO,IAAI,gBAAgB;AAC9D,UAAM,WAAW,MAAM,WAAW,KAAK,KAAK;AAAA,MACxC;AAAA,MACA,SAAS,kCAAK,UAAY,QAAQ;AAAA,MAClC;AAAA,MACA,SAAS,QAAQ;AAAA,MACjB,iBAAiB,QAAQ;AAAA,IAC7B,CAAC;AACD,WAAO,IAAI,SAAS,OAAO,IAAI,aAAa,kDAAkD,SAAS,UAAU,GAAG;AAAA,EACxH;AAAA;AAEO,SAAS,aAAa,QAAQ;AACjC,MAAI,WAAW,QAAW;AACtB,WAAO,IAAI,cAAc,SAAS,WAAW;AAAA,EACjD;AACA,MAAI,WAAW,MAAM;AACjB,WAAO,WAAW;AAAA,EACtB;AACA,MAAI,OAAO,QAAQ,QAAW;AAC1B,WAAO;AAAA,EACX;AACA,SAAO,IAAI,cAAc,MAAM;AACnC;AAEO,IAAM,sBAAN,MAA0B;AAAA,EAC7B,YAAY,SAAS,UAAU;AAC3B,SAAK,WAAW;AAChB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,UAAU;AACN,UAAM,QAAQ,KAAK,SAAS,UAAU,QAAQ,KAAK,SAAS;AAC5D,QAAI,QAAQ,IAAI;AACZ,WAAK,SAAS,UAAU,OAAO,OAAO,CAAC;AAAA,IAC3C;AACA,QAAI,KAAK,SAAS,UAAU,WAAW,KAAK,KAAK,SAAS,gBAAgB;AACtE,WAAK,SAAS,eAAe,EAAE,MAAM,CAAC,MAAM;AAAA,MAAE,CAAC;AAAA,IACnD;AAAA,EACJ;AACJ;AAEO,IAAM,gBAAN,MAAoB;AAAA,EACvB,YAAY,iBAAiB;AACzB,SAAK,YAAY;AACjB,SAAK,MAAM;AAAA,EACf;AAAA,EACA,IAAI,UAAU,SAAS;AACnB,QAAI,YAAY,KAAK,WAAW;AAC5B,YAAM,MAAM,KAAI,oBAAI,KAAK,GAAE,YAAY,CAAC,KAAK,SAAS,QAAQ,CAAC,KAAK,OAAO;AAC3E,cAAQ,UAAU;AAAA,QACd,KAAK,SAAS;AAAA,QACd,KAAK,SAAS;AACV,eAAK,IAAI,MAAM,GAAG;AAClB;AAAA,QACJ,KAAK,SAAS;AACV,eAAK,IAAI,KAAK,GAAG;AACjB;AAAA,QACJ,KAAK,SAAS;AACV,eAAK,IAAI,KAAK,GAAG;AACjB;AAAA,QACJ;AAEI,eAAK,IAAI,IAAI,GAAG;AAChB;AAAA,MACR;AAAA,IACJ;AAAA,EACJ;AACJ;AAEO,SAAS,qBAAqB;AACjC,MAAI,sBAAsB;AAC1B,MAAI,SAAS,QAAQ;AACjB,0BAAsB;AAAA,EAC1B;AACA,SAAO,CAAC,qBAAqB,mBAAmB,SAAS,UAAU,GAAG,WAAW,GAAG,kBAAkB,CAAC,CAAC;AAC5G;AAEO,SAAS,mBAAmB,SAAS,IAAI,SAAS,gBAAgB;AAErE,MAAI,YAAY;AAChB,QAAM,gBAAgB,QAAQ,MAAM,GAAG;AACvC,eAAa,GAAG,cAAc,CAAC,CAAC,IAAI,cAAc,CAAC,CAAC;AACpD,eAAa,KAAK,OAAO;AACzB,MAAI,MAAM,OAAO,IAAI;AACjB,iBAAa,GAAG,EAAE;AAAA,EACtB,OACK;AACD,iBAAa;AAAA,EACjB;AACA,eAAa,GAAG,OAAO;AACvB,MAAI,gBAAgB;AAChB,iBAAa,KAAK,cAAc;AAAA,EACpC,OACK;AACD,iBAAa;AAAA,EACjB;AACA,eAAa;AACb,SAAO;AACX;AAEc,SAAS,YAAY;AAC/B,MAAI,SAAS,QAAQ;AACjB,YAAQ,QAAQ,UAAU;AAAA,MACtB,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX;AACI,eAAO,QAAQ;AAAA,IACvB;AAAA,EACJ,OACK;AACD,WAAO;AAAA,EACX;AACJ;AAEc,SAAS,oBAAoB;AACvC,MAAI,SAAS,QAAQ;AACjB,WAAO,QAAQ,SAAS;AAAA,EAC5B;AACA,SAAO;AACX;AACA,SAAS,aAAa;AAClB,MAAI,SAAS,QAAQ;AACjB,WAAO;AAAA,EACX,OACK;AACD,WAAO;AAAA,EACX;AACJ;AAEO,SAAS,eAAe,GAAG;AAC9B,MAAI,EAAE,OAAO;AACT,WAAO,EAAE;AAAA,EACb,WACS,EAAE,SAAS;AAChB,WAAO,EAAE;AAAA,EACb;AACA,SAAO,GAAG,CAAC;AACf;AAEO,SAAS,gBAAgB;AAE5B,MAAI,OAAO,eAAe,aAAa;AACnC,WAAO;AAAA,EACX;AACA,MAAI,OAAO,SAAS,aAAa;AAC7B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,QAAM,IAAI,MAAM,uBAAuB;AAC3C;;;AChPO,IAAM,kBAAN,cAA8B,WAAW;AAAA,EAC5C,YAAY,QAAQ;AAChB,UAAM;AACN,SAAK,UAAU;AAGf,QAAI,OAAO,UAAU,eAAe,SAAS,QAAQ;AAGjD,YAAM,cAAc,OAAO,wBAAwB,aAAa,0BAA0B;AAE1F,WAAK,OAAO,KAAK,YAAY,cAAc,GAAG,UAAU;AACxD,UAAI,OAAO,UAAU,aAAa;AAC9B,aAAK,aAAa,YAAY,YAAY;AAAA,MAC9C,OACK;AAED,aAAK,aAAa;AAAA,MACtB;AAGA,WAAK,aAAa,YAAY,cAAc,EAAE,KAAK,YAAY,KAAK,IAAI;AAAA,IAC5E,OACK;AACD,WAAK,aAAa,MAAM,KAAK,cAAc,CAAC;AAAA,IAChD;AACA,QAAI,OAAO,oBAAoB,aAAa;AAGxC,YAAM,cAAc,OAAO,wBAAwB,aAAa,0BAA0B;AAE1F,WAAK,uBAAuB,YAAY,kBAAkB;AAAA,IAC9D,OACK;AACD,WAAK,uBAAuB;AAAA,IAChC;AAAA,EACJ;AAAA;AAAA,EAEM,KAAK,SAAS;AAAA;AAEhB,UAAI,QAAQ,eAAe,QAAQ,YAAY,SAAS;AACpD,cAAM,IAAI,WAAW;AAAA,MACzB;AACA,UAAI,CAAC,QAAQ,QAAQ;AACjB,cAAM,IAAI,MAAM,oBAAoB;AAAA,MACxC;AACA,UAAI,CAAC,QAAQ,KAAK;AACd,cAAM,IAAI,MAAM,iBAAiB;AAAA,MACrC;AACA,YAAM,kBAAkB,IAAI,KAAK,qBAAqB;AACtD,UAAI;AAEJ,UAAI,QAAQ,aAAa;AACrB,gBAAQ,YAAY,UAAU,MAAM;AAChC,0BAAgB,MAAM;AACtB,kBAAQ,IAAI,WAAW;AAAA,QAC3B;AAAA,MACJ;AAGA,UAAI,YAAY;AAChB,UAAI,QAAQ,SAAS;AACjB,cAAM,YAAY,QAAQ;AAC1B,oBAAY,WAAW,MAAM;AACzB,0BAAgB,MAAM;AACtB,eAAK,QAAQ,IAAI,SAAS,SAAS,4BAA4B;AAC/D,kBAAQ,IAAI,aAAa;AAAA,QAC7B,GAAG,SAAS;AAAA,MAChB;AACA,UAAI,QAAQ,YAAY,IAAI;AACxB,gBAAQ,UAAU;AAAA,MACtB;AACA,UAAI,QAAQ,SAAS;AAEjB,gBAAQ,UAAU,QAAQ,WAAW,CAAC;AACtC,YAAI,cAAc,QAAQ,OAAO,GAAG;AAChC,kBAAQ,QAAQ,cAAc,IAAI;AAAA,QACtC,OACK;AACD,kBAAQ,QAAQ,cAAc,IAAI;AAAA,QACtC;AAAA,MACJ;AACA,UAAI;AACJ,UAAI;AACA,mBAAW,MAAM,KAAK,WAAW,QAAQ,KAAK;AAAA,UAC1C,MAAM,QAAQ;AAAA,UACd,OAAO;AAAA,UACP,aAAa,QAAQ,oBAAoB,OAAO,YAAY;AAAA,UAC5D,SAAS;AAAA,YACL,oBAAoB;AAAA,aACjB,QAAQ;AAAA,UAEf,QAAQ,QAAQ;AAAA,UAChB,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ,gBAAgB;AAAA,QAC5B,CAAC;AAAA,MACL,SACO,GAAG;AACN,YAAI,OAAO;AACP,gBAAM;AAAA,QACV;AACA,aAAK,QAAQ,IAAI,SAAS,SAAS,4BAA4B,CAAC,GAAG;AACnE,cAAM;AAAA,MACV,UACA;AACI,YAAI,WAAW;AACX,uBAAa,SAAS;AAAA,QAC1B;AACA,YAAI,QAAQ,aAAa;AACrB,kBAAQ,YAAY,UAAU;AAAA,QAClC;AAAA,MACJ;AACA,UAAI,CAAC,SAAS,IAAI;AACd,cAAM,eAAe,MAAM,mBAAmB,UAAU,MAAM;AAC9D,cAAM,IAAI,UAAU,gBAAgB,SAAS,YAAY,SAAS,MAAM;AAAA,MAC5E;AACA,YAAM,UAAU,mBAAmB,UAAU,QAAQ,YAAY;AACjE,YAAM,UAAU,MAAM;AACtB,aAAO,IAAI,aAAa,SAAS,QAAQ,SAAS,YAAY,OAAO;AAAA,IACzE;AAAA;AAAA,EACA,gBAAgB,KAAK;AACjB,QAAI,UAAU;AACd,QAAI,SAAS,UAAU,KAAK,MAAM;AAE9B,WAAK,KAAK,WAAW,KAAK,CAAC,GAAG,MAAM,UAAU,EAAE,KAAK,IAAI,CAAC;AAAA,IAC9D;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,mBAAmB,UAAU,cAAc;AAChD,MAAI;AACJ,UAAQ,cAAc;AAAA,IAClB,KAAK;AACD,gBAAU,SAAS,YAAY;AAC/B;AAAA,IACJ,KAAK;AACD,gBAAU,SAAS,KAAK;AACxB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,YAAM,IAAI,MAAM,GAAG,YAAY,oBAAoB;AAAA,IACvD;AACI,gBAAU,SAAS,KAAK;AACxB;AAAA,EACR;AACA,SAAO;AACX;;;ACpJO,IAAM,gBAAN,cAA4B,WAAW;AAAA,EAC1C,YAAY,QAAQ;AAChB,UAAM;AACN,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA,EAEA,KAAK,SAAS;AAEV,QAAI,QAAQ,eAAe,QAAQ,YAAY,SAAS;AACpD,aAAO,QAAQ,OAAO,IAAI,WAAW,CAAC;AAAA,IAC1C;AACA,QAAI,CAAC,QAAQ,QAAQ;AACjB,aAAO,QAAQ,OAAO,IAAI,MAAM,oBAAoB,CAAC;AAAA,IACzD;AACA,QAAI,CAAC,QAAQ,KAAK;AACd,aAAO,QAAQ,OAAO,IAAI,MAAM,iBAAiB,CAAC;AAAA,IACtD;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,YAAM,MAAM,IAAI,eAAe;AAC/B,UAAI,KAAK,QAAQ,QAAQ,QAAQ,KAAK,IAAI;AAC1C,UAAI,kBAAkB,QAAQ,oBAAoB,SAAY,OAAO,QAAQ;AAC7E,UAAI,iBAAiB,oBAAoB,gBAAgB;AACzD,UAAI,QAAQ,YAAY,IAAI;AACxB,gBAAQ,UAAU;AAAA,MACtB;AACA,UAAI,QAAQ,SAAS;AAEjB,YAAI,cAAc,QAAQ,OAAO,GAAG;AAChC,cAAI,iBAAiB,gBAAgB,0BAA0B;AAAA,QACnE,OACK;AACD,cAAI,iBAAiB,gBAAgB,0BAA0B;AAAA,QACnE;AAAA,MACJ;AACA,YAAM,UAAU,QAAQ;AACxB,UAAI,SAAS;AACT,eAAO,KAAK,OAAO,EACd,QAAQ,CAAC,WAAW;AACrB,cAAI,iBAAiB,QAAQ,QAAQ,MAAM,CAAC;AAAA,QAChD,CAAC;AAAA,MACL;AACA,UAAI,QAAQ,cAAc;AACtB,YAAI,eAAe,QAAQ;AAAA,MAC/B;AACA,UAAI,QAAQ,aAAa;AACrB,gBAAQ,YAAY,UAAU,MAAM;AAChC,cAAI,MAAM;AACV,iBAAO,IAAI,WAAW,CAAC;AAAA,QAC3B;AAAA,MACJ;AACA,UAAI,QAAQ,SAAS;AACjB,YAAI,UAAU,QAAQ;AAAA,MAC1B;AACA,UAAI,SAAS,MAAM;AACf,YAAI,QAAQ,aAAa;AACrB,kBAAQ,YAAY,UAAU;AAAA,QAClC;AACA,YAAI,IAAI,UAAU,OAAO,IAAI,SAAS,KAAK;AACvC,kBAAQ,IAAI,aAAa,IAAI,QAAQ,IAAI,YAAY,IAAI,YAAY,IAAI,YAAY,CAAC;AAAA,QAC1F,OACK;AACD,iBAAO,IAAI,UAAU,IAAI,YAAY,IAAI,gBAAgB,IAAI,YAAY,IAAI,MAAM,CAAC;AAAA,QACxF;AAAA,MACJ;AACA,UAAI,UAAU,MAAM;AAChB,aAAK,QAAQ,IAAI,SAAS,SAAS,4BAA4B,IAAI,MAAM,KAAK,IAAI,UAAU,GAAG;AAC/F,eAAO,IAAI,UAAU,IAAI,YAAY,IAAI,MAAM,CAAC;AAAA,MACpD;AACA,UAAI,YAAY,MAAM;AAClB,aAAK,QAAQ,IAAI,SAAS,SAAS,4BAA4B;AAC/D,eAAO,IAAI,aAAa,CAAC;AAAA,MAC7B;AACA,UAAI,KAAK,QAAQ,OAAO;AAAA,IAC5B,CAAC;AAAA,EACL;AACJ;;;ACzEO,IAAM,oBAAN,cAAgC,WAAW;AAAA;AAAA,EAE9C,YAAY,QAAQ;AAChB,UAAM;AACN,QAAI,OAAO,UAAU,eAAe,SAAS,QAAQ;AACjD,WAAK,cAAc,IAAI,gBAAgB,MAAM;AAAA,IACjD,WACS,OAAO,mBAAmB,aAAa;AAC5C,WAAK,cAAc,IAAI,cAAc,MAAM;AAAA,IAC/C,OACK;AACD,YAAM,IAAI,MAAM,6BAA6B;AAAA,IACjD;AAAA,EACJ;AAAA;AAAA,EAEA,KAAK,SAAS;AAEV,QAAI,QAAQ,eAAe,QAAQ,YAAY,SAAS;AACpD,aAAO,QAAQ,OAAO,IAAI,WAAW,CAAC;AAAA,IAC1C;AACA,QAAI,CAAC,QAAQ,QAAQ;AACjB,aAAO,QAAQ,OAAO,IAAI,MAAM,oBAAoB,CAAC;AAAA,IACzD;AACA,QAAI,CAAC,QAAQ,KAAK;AACd,aAAO,QAAQ,OAAO,IAAI,MAAM,iBAAiB,CAAC;AAAA,IACtD;AACA,WAAO,KAAK,YAAY,KAAK,OAAO;AAAA,EACxC;AAAA,EACA,gBAAgB,KAAK;AACjB,WAAO,KAAK,YAAY,gBAAgB,GAAG;AAAA,EAC/C;AACJ;;;ACnCO,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EAC3B,OAAO,MAAM,QAAQ;AACjB,WAAO,GAAG,MAAM,GAAG,mBAAkB,eAAe;AAAA,EACxD;AAAA,EACA,OAAO,MAAM,OAAO;AAChB,QAAI,MAAM,MAAM,SAAS,CAAC,MAAM,mBAAkB,iBAAiB;AAC/D,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC5C;AACA,UAAM,WAAW,MAAM,MAAM,mBAAkB,eAAe;AAC9D,aAAS,IAAI;AACb,WAAO;AAAA,EACX;AACJ;AACA,kBAAkB,sBAAsB;AACxC,kBAAkB,kBAAkB,OAAO,aAAa,kBAAkB,mBAAmB;;;ACbtF,IAAM,oBAAN,MAAwB;AAAA;AAAA,EAE3B,sBAAsB,kBAAkB;AACpC,WAAO,kBAAkB,MAAM,KAAK,UAAU,gBAAgB,CAAC;AAAA,EACnE;AAAA,EACA,uBAAuB,MAAM;AACzB,QAAI;AACJ,QAAI;AACJ,QAAI,cAAc,IAAI,GAAG;AAErB,YAAM,aAAa,IAAI,WAAW,IAAI;AACtC,YAAM,iBAAiB,WAAW,QAAQ,kBAAkB,mBAAmB;AAC/E,UAAI,mBAAmB,IAAI;AACvB,cAAM,IAAI,MAAM,wBAAwB;AAAA,MAC5C;AAGA,YAAM,iBAAiB,iBAAiB;AACxC,oBAAc,OAAO,aAAa,MAAM,MAAM,MAAM,UAAU,MAAM,KAAK,WAAW,MAAM,GAAG,cAAc,CAAC,CAAC;AAC7G,sBAAiB,WAAW,aAAa,iBAAkB,WAAW,MAAM,cAAc,EAAE,SAAS;AAAA,IACzG,OACK;AACD,YAAM,WAAW;AACjB,YAAM,iBAAiB,SAAS,QAAQ,kBAAkB,eAAe;AACzE,UAAI,mBAAmB,IAAI;AACvB,cAAM,IAAI,MAAM,wBAAwB;AAAA,MAC5C;AAGA,YAAM,iBAAiB,iBAAiB;AACxC,oBAAc,SAAS,UAAU,GAAG,cAAc;AAClD,sBAAiB,SAAS,SAAS,iBAAkB,SAAS,UAAU,cAAc,IAAI;AAAA,IAC9F;AAEA,UAAM,WAAW,kBAAkB,MAAM,WAAW;AACpD,UAAM,WAAW,KAAK,MAAM,SAAS,CAAC,CAAC;AACvC,QAAI,SAAS,MAAM;AACf,YAAM,IAAI,MAAM,gDAAgD;AAAA,IACpE;AACA,UAAM,kBAAkB;AAGxB,WAAO,CAAC,eAAe,eAAe;AAAA,EAC1C;AACJ;;;AC9CO,IAAI;AAAA,CACV,SAAUC,cAAa;AAEpB,EAAAA,aAAYA,aAAY,YAAY,IAAI,CAAC,IAAI;AAE7C,EAAAA,aAAYA,aAAY,YAAY,IAAI,CAAC,IAAI;AAE7C,EAAAA,aAAYA,aAAY,YAAY,IAAI,CAAC,IAAI;AAE7C,EAAAA,aAAYA,aAAY,kBAAkB,IAAI,CAAC,IAAI;AAEnD,EAAAA,aAAYA,aAAY,kBAAkB,IAAI,CAAC,IAAI;AAEnD,EAAAA,aAAYA,aAAY,MAAM,IAAI,CAAC,IAAI;AAEvC,EAAAA,aAAYA,aAAY,OAAO,IAAI,CAAC,IAAI;AACxC,EAAAA,aAAYA,aAAY,KAAK,IAAI,CAAC,IAAI;AACtC,EAAAA,aAAYA,aAAY,UAAU,IAAI,CAAC,IAAI;AAC/C,GAAG,gBAAgB,cAAc,CAAC,EAAE;;;ACjB7B,IAAM,UAAN,MAAc;AAAA,EACjB,cAAc;AACV,SAAK,YAAY,CAAC;AAAA,EACtB;AAAA,EACA,KAAK,MAAM;AACP,eAAW,YAAY,KAAK,WAAW;AACnC,eAAS,KAAK,IAAI;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,MAAM,KAAK;AACP,eAAW,YAAY,KAAK,WAAW;AACnC,UAAI,SAAS,OAAO;AAChB,iBAAS,MAAM,GAAG;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW;AACP,eAAW,YAAY,KAAK,WAAW;AACnC,UAAI,SAAS,UAAU;AACnB,iBAAS,SAAS;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,SAAK,UAAU,KAAK,QAAQ;AAC5B,WAAO,IAAI,oBAAoB,MAAM,QAAQ;AAAA,EACjD;AACJ;;;AC1BO,IAAM,gBAAN,MAAoB;AAAA,EACvB,YAAY,UAAU,YAAY,YAAY;AAC1C,SAAK,cAAc;AACnB,SAAK,YAAY,CAAC;AAClB,SAAK,qBAAqB;AAC1B,SAAK,0BAA0B;AAE/B,SAAK,2BAA2B;AAChC,SAAK,4BAA4B;AACjC,SAAK,qBAAqB;AAC1B,SAAK,uBAAuB;AAC5B,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,cAAc;AAAA,EACvB;AAAA,EACM,MAAM,SAAS;AAAA;AACjB,YAAM,oBAAoB,KAAK,UAAU,aAAa,OAAO;AAC7D,UAAI,sBAAsB,QAAQ,QAAQ;AAE1C,UAAI,KAAK,qBAAqB,OAAO,GAAG;AACpC,aAAK;AACL,YAAI,8BAA8B,MAAM;AAAA,QAAE;AAC1C,YAAI,8BAA8B,MAAM;AAAA,QAAE;AAC1C,YAAI,cAAc,iBAAiB,GAAG;AAClC,eAAK,sBAAsB,kBAAkB;AAAA,QACjD,OACK;AACD,eAAK,sBAAsB,kBAAkB;AAAA,QACjD;AACA,YAAI,KAAK,sBAAsB,KAAK,aAAa;AAC7C,gCAAsB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACnD,0CAA8B;AAC9B,0CAA8B;AAAA,UAClC,CAAC;AAAA,QACL;AACA,aAAK,UAAU,KAAK,IAAI,aAAa,mBAAmB,KAAK,oBAAoB,6BAA6B,2BAA2B,CAAC;AAAA,MAC9I;AACA,UAAI;AAKA,YAAI,CAAC,KAAK,sBAAsB;AAC5B,gBAAM,KAAK,YAAY,KAAK,iBAAiB;AAAA,QACjD;AAAA,MACJ,QACM;AACF,aAAK,cAAc;AAAA,MACvB;AACA,YAAM;AAAA,IACV;AAAA;AAAA,EACA,KAAK,YAAY;AACb,QAAI,qBAAqB;AAEzB,aAAS,QAAQ,GAAG,QAAQ,KAAK,UAAU,QAAQ,SAAS;AACxD,YAAM,UAAU,KAAK,UAAU,KAAK;AACpC,UAAI,QAAQ,OAAO,WAAW,YAAY;AACtC,6BAAqB;AACrB,YAAI,cAAc,QAAQ,QAAQ,GAAG;AACjC,eAAK,sBAAsB,QAAQ,SAAS;AAAA,QAChD,OACK;AACD,eAAK,sBAAsB,QAAQ,SAAS;AAAA,QAChD;AAEA,gBAAQ,UAAU;AAAA,MACtB,WACS,KAAK,qBAAqB,KAAK,aAAa;AAEjD,gBAAQ,UAAU;AAAA,MACtB,OACK;AACD;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,uBAAuB,IAAI;AAE3B,WAAK,YAAY,KAAK,UAAU,MAAM,qBAAqB,CAAC;AAAA,IAChE;AAAA,EACJ;AAAA,EACA,sBAAsB,SAAS;AAC3B,QAAI,KAAK,yBAAyB;AAC9B,UAAI,QAAQ,SAAS,YAAY,UAAU;AACvC,eAAO;AAAA,MACX,OACK;AACD,aAAK,0BAA0B;AAC/B,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAI,CAAC,KAAK,qBAAqB,OAAO,GAAG;AACrC,aAAO;AAAA,IACX;AACA,UAAM,YAAY,KAAK;AACvB,SAAK;AACL,QAAI,aAAa,KAAK,2BAA2B;AAC7C,UAAI,cAAc,KAAK,2BAA2B;AAG9C,aAAK,UAAU;AAAA,MACnB;AAEA,aAAO;AAAA,IACX;AACA,SAAK,4BAA4B;AAGjC,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AAAA,EACA,eAAe,SAAS;AACpB,QAAI,QAAQ,aAAa,KAAK,0BAA0B;AAEpD,WAAK,YAAY,KAAK,IAAI,MAAM,6DAA6D,CAAC;AAC9F;AAAA,IACJ;AACA,SAAK,2BAA2B,QAAQ;AAAA,EAC5C;AAAA,EACA,gBAAgB;AACZ,SAAK,uBAAuB;AAC5B,SAAK,0BAA0B;AAAA,EACnC;AAAA,EACM,UAAU;AAAA;AACZ,YAAM,aAAa,KAAK,UAAU,WAAW,IACvC,KAAK,UAAU,CAAC,EAAE,MAClB,KAAK,qBAAqB;AAChC,YAAM,KAAK,YAAY,KAAK,KAAK,UAAU,aAAa,EAAE,MAAM,YAAY,UAAU,WAAW,CAAC,CAAC;AAGnG,YAAM,WAAW,KAAK;AACtB,iBAAW,WAAW,UAAU;AAC5B,cAAM,KAAK,YAAY,KAAK,QAAQ,QAAQ;AAAA,MAChD;AACA,WAAK,uBAAuB;AAAA,IAChC;AAAA;AAAA,EACA,SAAS,OAAO;AACZ,cAAU,QAAQ,UAAU,SAAS,QAAS,QAAQ,IAAI,MAAM,gCAAgC;AAEhG,eAAW,WAAW,KAAK,WAAW;AAClC,cAAQ,UAAU,KAAK;AAAA,IAC3B;AAAA,EACJ;AAAA,EACA,qBAAqB,SAAS;AAM1B,YAAQ,QAAQ,MAAM;AAAA,MAClB,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AACb,eAAO;AAAA,MACX,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AACb,eAAO;AAAA,IACf;AAAA,EACJ;AAAA,EACA,YAAY;AACR,QAAI,KAAK,oBAAoB,QAAW;AACpC,WAAK,kBAAkB,WAAW,MAAY;AAC1C,YAAI;AACA,cAAI,CAAC,KAAK,sBAAsB;AAC5B,kBAAM,KAAK,YAAY,KAAK,KAAK,UAAU,aAAa,EAAE,MAAM,YAAY,KAAK,YAAY,KAAK,0BAA0B,CAAC,CAAC;AAAA,UAClI;AAAA,QAEJ,QACM;AAAA,QAAE;AACR,qBAAa,KAAK,eAAe;AACjC,aAAK,kBAAkB;AAAA,MAE3B,IAAG,GAAI;AAAA,IACX;AAAA,EACJ;AACJ;AACA,IAAM,eAAN,MAAmB;AAAA,EACf,YAAY,SAAS,IAAI,UAAU,UAAU;AACzC,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,YAAY;AACjB,SAAK,YAAY;AAAA,EACrB;AACJ;;;ACvLA,IAAM,wBAAwB,KAAK;AACnC,IAAM,8BAA8B,KAAK;AACzC,IAAM,yCAAyC;AAExC,IAAI;AAAA,CACV,SAAUC,qBAAoB;AAE3B,EAAAA,oBAAmB,cAAc,IAAI;AAErC,EAAAA,oBAAmB,YAAY,IAAI;AAEnC,EAAAA,oBAAmB,WAAW,IAAI;AAElC,EAAAA,oBAAmB,eAAe,IAAI;AAEtC,EAAAA,oBAAmB,cAAc,IAAI;AACzC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAE3C,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,OAAO,OAAO,YAAY,QAAQ,UAAU,iBAAiB,6BAA6B,iCAAiC,6BAA6B;AACpJ,WAAO,IAAI,eAAc,YAAY,QAAQ,UAAU,iBAAiB,6BAA6B,iCAAiC,2BAA2B;AAAA,EACrK;AAAA,EACA,YAAY,YAAY,QAAQ,UAAU,iBAAiB,6BAA6B,iCAAiC,6BAA6B;AAClJ,SAAK,iBAAiB;AACtB,SAAK,uBAAuB,MAAM;AAC9B,WAAK,QAAQ,IAAI,SAAS,SAAS,uNAAuN;AAAA,IAC9P;AACA,QAAI,WAAW,YAAY,YAAY;AACvC,QAAI,WAAW,QAAQ,QAAQ;AAC/B,QAAI,WAAW,UAAU,UAAU;AACnC,SAAK,8BAA8B,gCAAgC,QAAQ,gCAAgC,SAAS,8BAA8B;AAClJ,SAAK,kCAAkC,oCAAoC,QAAQ,oCAAoC,SAAS,kCAAkC;AAClK,SAAK,+BAA+B,gCAAgC,QAAQ,gCAAgC,SAAS,8BAA8B;AACnJ,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,mBAAmB;AACxB,SAAK,qBAAqB,IAAI,kBAAkB;AAChD,SAAK,WAAW,YAAY,CAAC,SAAS,KAAK,qBAAqB,IAAI;AACpE,SAAK,WAAW,UAAU,CAAC,UAAU,KAAK,kBAAkB,KAAK;AACjE,SAAK,aAAa,CAAC;AACnB,SAAK,WAAW,CAAC;AACjB,SAAK,mBAAmB,CAAC;AACzB,SAAK,yBAAyB,CAAC;AAC/B,SAAK,wBAAwB,CAAC;AAC9B,SAAK,gBAAgB;AACrB,SAAK,6BAA6B;AAClC,SAAK,mBAAmB,mBAAmB;AAC3C,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB,KAAK,UAAU,aAAa,EAAE,MAAM,YAAY,KAAK,CAAC;AAAA,EACpF;AAAA;AAAA,EAEA,IAAI,QAAQ;AACR,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,eAAe;AACf,WAAO,KAAK,aAAc,KAAK,WAAW,gBAAgB,OAAQ;AAAA,EACtE;AAAA;AAAA,EAEA,IAAI,UAAU;AACV,WAAO,KAAK,WAAW,WAAW;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ,KAAK;AACb,QAAI,KAAK,qBAAqB,mBAAmB,gBAAgB,KAAK,qBAAqB,mBAAmB,cAAc;AACxH,YAAM,IAAI,MAAM,wFAAwF;AAAA,IAC5G;AACA,QAAI,CAAC,KAAK;AACN,YAAM,IAAI,MAAM,4CAA4C;AAAA,IAChE;AACA,SAAK,WAAW,UAAU;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACJ,SAAK,gBAAgB,KAAK,2BAA2B;AACrD,WAAO,KAAK;AAAA,EAChB;AAAA,EACM,6BAA6B;AAAA;AAC/B,UAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC3D,eAAO,QAAQ,OAAO,IAAI,MAAM,uEAAuE,CAAC;AAAA,MAC5G;AACA,WAAK,mBAAmB,mBAAmB;AAC3C,WAAK,QAAQ,IAAI,SAAS,OAAO,yBAAyB;AAC1D,UAAI;AACA,cAAM,KAAK,eAAe;AAC1B,YAAI,SAAS,WAAW;AAEpB,iBAAO,SAAS,iBAAiB,UAAU,KAAK,oBAAoB;AAAA,QACxE;AACA,aAAK,mBAAmB,mBAAmB;AAC3C,aAAK,qBAAqB;AAC1B,aAAK,QAAQ,IAAI,SAAS,OAAO,uCAAuC;AAAA,MAC5E,SACO,GAAG;AACN,aAAK,mBAAmB,mBAAmB;AAC3C,aAAK,QAAQ,IAAI,SAAS,OAAO,gEAAgE,CAAC,IAAI;AACtG,eAAO,QAAQ,OAAO,CAAC;AAAA,MAC3B;AAAA,IACJ;AAAA;AAAA,EACM,iBAAiB;AAAA;AACnB,WAAK,wBAAwB;AAC7B,WAAK,6BAA6B;AAElC,YAAM,mBAAmB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtD,aAAK,qBAAqB;AAC1B,aAAK,qBAAqB;AAAA,MAC9B,CAAC;AACD,YAAM,KAAK,WAAW,MAAM,KAAK,UAAU,cAAc;AACzD,UAAI;AACA,YAAI,UAAU,KAAK,UAAU;AAC7B,YAAI,CAAC,KAAK,WAAW,SAAS,WAAW;AAGrC,oBAAU;AAAA,QACd;AACA,cAAM,mBAAmB;AAAA,UACrB,UAAU,KAAK,UAAU;AAAA,UACzB;AAAA,QACJ;AACA,aAAK,QAAQ,IAAI,SAAS,OAAO,4BAA4B;AAC7D,cAAM,KAAK,aAAa,KAAK,mBAAmB,sBAAsB,gBAAgB,CAAC;AACvF,aAAK,QAAQ,IAAI,SAAS,aAAa,sBAAsB,KAAK,UAAU,IAAI,IAAI;AAEpF,aAAK,gBAAgB;AACrB,aAAK,oBAAoB;AACzB,aAAK,wBAAwB;AAC7B,cAAM;AAIN,YAAI,KAAK,uBAAuB;AAK5B,gBAAM,KAAK;AAAA,QACf;AACA,cAAM,uBAAuB,KAAK,WAAW,SAAS,aAAa;AACnE,YAAI,sBAAsB;AACtB,eAAK,iBAAiB,IAAI,cAAc,KAAK,WAAW,KAAK,YAAY,KAAK,4BAA4B;AAC1G,eAAK,WAAW,SAAS,eAAe,KAAK,eAAe,cAAc,KAAK,KAAK,cAAc;AAClG,eAAK,WAAW,SAAS,SAAS,MAAM;AACpC,gBAAI,KAAK,gBAAgB;AACrB,qBAAO,KAAK,eAAe,QAAQ;AAAA,YACvC;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,CAAC,KAAK,WAAW,SAAS,mBAAmB;AAC7C,gBAAM,KAAK,aAAa,KAAK,kBAAkB;AAAA,QACnD;AAAA,MACJ,SACO,GAAG;AACN,aAAK,QAAQ,IAAI,SAAS,OAAO,oCAAoC,CAAC,2CAA2C;AACjH,aAAK,gBAAgB;AACrB,aAAK,kBAAkB;AAGvB,cAAM,KAAK,WAAW,KAAK,CAAC;AAC5B,cAAM;AAAA,MACV;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,OAAO;AAAA;AAET,YAAM,eAAe,KAAK;AAC1B,WAAK,WAAW,SAAS,YAAY;AACrC,WAAK,eAAe,KAAK,cAAc;AACvC,YAAM,KAAK;AACX,UAAI;AAEA,cAAM;AAAA,MACV,SACO,GAAG;AAAA,MAEV;AAAA,IACJ;AAAA;AAAA,EACA,cAAc,OAAO;AACjB,QAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC3D,WAAK,QAAQ,IAAI,SAAS,OAAO,8BAA8B,KAAK,4DAA4D;AAChI,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AACA,QAAI,KAAK,qBAAqB,mBAAmB,eAAe;AAC5D,WAAK,QAAQ,IAAI,SAAS,OAAO,+BAA+B,KAAK,yEAAyE;AAC9I,aAAO,KAAK;AAAA,IAChB;AACA,UAAM,QAAQ,KAAK;AACnB,SAAK,mBAAmB,mBAAmB;AAC3C,SAAK,QAAQ,IAAI,SAAS,OAAO,yBAAyB;AAC1D,QAAI,KAAK,uBAAuB;AAI5B,WAAK,QAAQ,IAAI,SAAS,OAAO,+DAA+D;AAChG,mBAAa,KAAK,qBAAqB;AACvC,WAAK,wBAAwB;AAC7B,WAAK,eAAe;AACpB,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AACA,QAAI,UAAU,mBAAmB,WAAW;AAExC,WAAK,kBAAkB;AAAA,IAC3B;AACA,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,wBAAwB,SAAS,IAAI,WAAW,qEAAqE;AAI1H,WAAO,KAAK,WAAW,KAAK,KAAK;AAAA,EACrC;AAAA,EACM,oBAAoB;AAAA;AACtB,UAAI;AACA,cAAM,KAAK,kBAAkB,KAAK,oBAAoB,CAAC;AAAA,MAC3D,QACM;AAAA,MAEN;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,eAAe,MAAM;AACxB,UAAM,CAAC,SAAS,SAAS,IAAI,KAAK,wBAAwB,IAAI;AAC9D,UAAM,uBAAuB,KAAK,wBAAwB,YAAY,MAAM,SAAS;AAErF,QAAI;AACJ,UAAM,UAAU,IAAI,QAAQ;AAC5B,YAAQ,iBAAiB,MAAM;AAC3B,YAAM,mBAAmB,KAAK,wBAAwB,qBAAqB,YAAY;AACvF,aAAO,KAAK,WAAW,qBAAqB,YAAY;AACxD,aAAO,aAAa,KAAK,MAAM;AAC3B,eAAO,KAAK,kBAAkB,gBAAgB;AAAA,MAClD,CAAC;AAAA,IACL;AACA,SAAK,WAAW,qBAAqB,YAAY,IAAI,CAAC,iBAAiB,UAAU;AAC7E,UAAI,OAAO;AACP,gBAAQ,MAAM,KAAK;AACnB;AAAA,MACJ,WACS,iBAAiB;AAEtB,YAAI,gBAAgB,SAAS,YAAY,YAAY;AACjD,cAAI,gBAAgB,OAAO;AACvB,oBAAQ,MAAM,IAAI,MAAM,gBAAgB,KAAK,CAAC;AAAA,UAClD,OACK;AACD,oBAAQ,SAAS;AAAA,UACrB;AAAA,QACJ,OACK;AACD,kBAAQ,KAAM,gBAAgB,IAAK;AAAA,QACvC;AAAA,MACJ;AAAA,IACJ;AACA,mBAAe,KAAK,kBAAkB,oBAAoB,EACrD,MAAM,CAAC,MAAM;AACd,cAAQ,MAAM,CAAC;AACf,aAAO,KAAK,WAAW,qBAAqB,YAAY;AAAA,IAC5D,CAAC;AACD,SAAK,eAAe,SAAS,YAAY;AACzC,WAAO;AAAA,EACX;AAAA,EACA,aAAa,SAAS;AAClB,SAAK,wBAAwB;AAC7B,WAAO,KAAK,WAAW,KAAK,OAAO;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,SAAS;AACvB,QAAI,KAAK,gBAAgB;AACrB,aAAO,KAAK,eAAe,MAAM,OAAO;AAAA,IAC5C,OACK;AACD,aAAO,KAAK,aAAa,KAAK,UAAU,aAAa,OAAO,CAAC;AAAA,IACjE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,KAAK,eAAe,MAAM;AACtB,UAAM,CAAC,SAAS,SAAS,IAAI,KAAK,wBAAwB,IAAI;AAC9D,UAAM,cAAc,KAAK,kBAAkB,KAAK,kBAAkB,YAAY,MAAM,MAAM,SAAS,CAAC;AACpG,SAAK,eAAe,SAAS,WAAW;AACxC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,eAAe,MAAM;AACxB,UAAM,CAAC,SAAS,SAAS,IAAI,KAAK,wBAAwB,IAAI;AAC9D,UAAM,uBAAuB,KAAK,kBAAkB,YAAY,MAAM,OAAO,SAAS;AACtF,UAAM,IAAI,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEvC,WAAK,WAAW,qBAAqB,YAAY,IAAI,CAAC,iBAAiB,UAAU;AAC7E,YAAI,OAAO;AACP,iBAAO,KAAK;AACZ;AAAA,QACJ,WACS,iBAAiB;AAEtB,cAAI,gBAAgB,SAAS,YAAY,YAAY;AACjD,gBAAI,gBAAgB,OAAO;AACvB,qBAAO,IAAI,MAAM,gBAAgB,KAAK,CAAC;AAAA,YAC3C,OACK;AACD,sBAAQ,gBAAgB,MAAM;AAAA,YAClC;AAAA,UACJ,OACK;AACD,mBAAO,IAAI,MAAM,4BAA4B,gBAAgB,IAAI,EAAE,CAAC;AAAA,UACxE;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,eAAe,KAAK,kBAAkB,oBAAoB,EAC3D,MAAM,CAAC,MAAM;AACd,eAAO,CAAC;AAER,eAAO,KAAK,WAAW,qBAAqB,YAAY;AAAA,MAC5D,CAAC;AACD,WAAK,eAAe,SAAS,YAAY;AAAA,IAC7C,CAAC;AACD,WAAO;AAAA,EACX;AAAA,EACA,GAAG,YAAY,WAAW;AACtB,QAAI,CAAC,cAAc,CAAC,WAAW;AAC3B;AAAA,IACJ;AACA,iBAAa,WAAW,YAAY;AACpC,QAAI,CAAC,KAAK,SAAS,UAAU,GAAG;AAC5B,WAAK,SAAS,UAAU,IAAI,CAAC;AAAA,IACjC;AAEA,QAAI,KAAK,SAAS,UAAU,EAAE,QAAQ,SAAS,MAAM,IAAI;AACrD;AAAA,IACJ;AACA,SAAK,SAAS,UAAU,EAAE,KAAK,SAAS;AAAA,EAC5C;AAAA,EACA,IAAI,YAAY,QAAQ;AACpB,QAAI,CAAC,YAAY;AACb;AAAA,IACJ;AACA,iBAAa,WAAW,YAAY;AACpC,UAAM,WAAW,KAAK,SAAS,UAAU;AACzC,QAAI,CAAC,UAAU;AACX;AAAA,IACJ;AACA,QAAI,QAAQ;AACR,YAAM,YAAY,SAAS,QAAQ,MAAM;AACzC,UAAI,cAAc,IAAI;AAClB,iBAAS,OAAO,WAAW,CAAC;AAC5B,YAAI,SAAS,WAAW,GAAG;AACvB,iBAAO,KAAK,SAAS,UAAU;AAAA,QACnC;AAAA,MACJ;AAAA,IACJ,OACK;AACD,aAAO,KAAK,SAAS,UAAU;AAAA,IACnC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,UAAU;AACd,QAAI,UAAU;AACV,WAAK,iBAAiB,KAAK,QAAQ;AAAA,IACvC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,UAAU;AACrB,QAAI,UAAU;AACV,WAAK,uBAAuB,KAAK,QAAQ;AAAA,IAC7C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,UAAU;AACpB,QAAI,UAAU;AACV,WAAK,sBAAsB,KAAK,QAAQ;AAAA,IAC5C;AAAA,EACJ;AAAA,EACA,qBAAqB,MAAM;AACvB,SAAK,gBAAgB;AACrB,QAAI,CAAC,KAAK,4BAA4B;AAClC,aAAO,KAAK,0BAA0B,IAAI;AAC1C,WAAK,6BAA6B;AAAA,IACtC;AAEA,QAAI,MAAM;AAEN,YAAM,WAAW,KAAK,UAAU,cAAc,MAAM,KAAK,OAAO;AAChE,iBAAW,WAAW,UAAU;AAC5B,YAAI,KAAK,kBAAkB,CAAC,KAAK,eAAe,sBAAsB,OAAO,GAAG;AAE5E;AAAA,QACJ;AACA,gBAAQ,QAAQ,MAAM;AAAA,UAClB,KAAK,YAAY;AACb,iBAAK,oBAAoB,OAAO,EAC3B,MAAM,CAAC,MAAM;AACd,mBAAK,QAAQ,IAAI,SAAS,OAAO,qCAAqC,eAAe,CAAC,CAAC,EAAE;AAAA,YAC7F,CAAC;AACD;AAAA,UACJ,KAAK,YAAY;AAAA,UACjB,KAAK,YAAY,YAAY;AACzB,kBAAM,WAAW,KAAK,WAAW,QAAQ,YAAY;AACrD,gBAAI,UAAU;AACV,kBAAI,QAAQ,SAAS,YAAY,YAAY;AACzC,uBAAO,KAAK,WAAW,QAAQ,YAAY;AAAA,cAC/C;AACA,kBAAI;AACA,yBAAS,OAAO;AAAA,cACpB,SACO,GAAG;AACN,qBAAK,QAAQ,IAAI,SAAS,OAAO,gCAAgC,eAAe,CAAC,CAAC,EAAE;AAAA,cACxF;AAAA,YACJ;AACA;AAAA,UACJ;AAAA,UACA,KAAK,YAAY;AAEb;AAAA,UACJ,KAAK,YAAY,OAAO;AACpB,iBAAK,QAAQ,IAAI,SAAS,aAAa,qCAAqC;AAC5E,kBAAM,QAAQ,QAAQ,QAAQ,IAAI,MAAM,wCAAwC,QAAQ,KAAK,IAAI;AACjG,gBAAI,QAAQ,mBAAmB,MAAM;AAIjC,mBAAK,WAAW,KAAK,KAAK;AAAA,YAC9B,OACK;AAED,mBAAK,eAAe,KAAK,cAAc,KAAK;AAAA,YAChD;AACA;AAAA,UACJ;AAAA,UACA,KAAK,YAAY;AACb,gBAAI,KAAK,gBAAgB;AACrB,mBAAK,eAAe,KAAK,OAAO;AAAA,YACpC;AACA;AAAA,UACJ,KAAK,YAAY;AACb,gBAAI,KAAK,gBAAgB;AACrB,mBAAK,eAAe,eAAe,OAAO;AAAA,YAC9C;AACA;AAAA,UACJ;AACI,iBAAK,QAAQ,IAAI,SAAS,SAAS,yBAAyB,QAAQ,IAAI,GAAG;AAC3E;AAAA,QACR;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,oBAAoB;AAAA,EAC7B;AAAA,EACA,0BAA0B,MAAM;AAC5B,QAAI;AACJ,QAAI;AACJ,QAAI;AACA,OAAC,eAAe,eAAe,IAAI,KAAK,mBAAmB,uBAAuB,IAAI;AAAA,IAC1F,SACO,GAAG;AACN,YAAM,UAAU,uCAAuC;AACvD,WAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AACxC,YAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,WAAK,mBAAmB,KAAK;AAC7B,YAAM;AAAA,IACV;AACA,QAAI,gBAAgB,OAAO;AACvB,YAAM,UAAU,sCAAsC,gBAAgB;AACtE,WAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AACxC,YAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,WAAK,mBAAmB,KAAK;AAC7B,YAAM;AAAA,IACV,OACK;AACD,WAAK,QAAQ,IAAI,SAAS,OAAO,4BAA4B;AAAA,IACjE;AACA,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACX;AAAA,EACA,0BAA0B;AACtB,QAAI,KAAK,WAAW,SAAS,mBAAmB;AAC5C;AAAA,IACJ;AAGA,SAAK,kBAAiB,oBAAI,KAAK,GAAE,QAAQ,IAAI,KAAK;AAClD,SAAK,kBAAkB;AAAA,EAC3B;AAAA,EACA,sBAAsB;AAClB,QAAI,CAAC,KAAK,WAAW,YAAY,CAAC,KAAK,WAAW,SAAS,mBAAmB;AAE1E,WAAK,iBAAiB,WAAW,MAAM,KAAK,cAAc,GAAG,KAAK,2BAA2B;AAE7F,UAAI,KAAK,sBAAsB,QAAW;AACtC,YAAI,WAAW,KAAK,kBAAiB,oBAAI,KAAK,GAAE,QAAQ;AACxD,YAAI,WAAW,GAAG;AACd,qBAAW;AAAA,QACf;AAEA,aAAK,oBAAoB,WAAW,MAAY;AAC5C,cAAI,KAAK,qBAAqB,mBAAmB,WAAW;AACxD,gBAAI;AACA,oBAAM,KAAK,aAAa,KAAK,kBAAkB;AAAA,YACnD,QACM;AAGF,mBAAK,kBAAkB;AAAA,YAC3B;AAAA,UACJ;AAAA,QACJ,IAAG,QAAQ;AAAA,MACf;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA,EAEA,gBAAgB;AAIZ,SAAK,WAAW,KAAK,IAAI,MAAM,qEAAqE,CAAC;AAAA,EACzG;AAAA,EACM,oBAAoB,mBAAmB;AAAA;AACzC,YAAM,aAAa,kBAAkB,OAAO,YAAY;AACxD,YAAM,UAAU,KAAK,SAAS,UAAU;AACxC,UAAI,CAAC,SAAS;AACV,aAAK,QAAQ,IAAI,SAAS,SAAS,mCAAmC,UAAU,UAAU;AAE1F,YAAI,kBAAkB,cAAc;AAChC,eAAK,QAAQ,IAAI,SAAS,SAAS,wBAAwB,UAAU,+BAA+B,kBAAkB,YAAY,IAAI;AACtI,gBAAM,KAAK,kBAAkB,KAAK,yBAAyB,kBAAkB,cAAc,mCAAmC,IAAI,CAAC;AAAA,QACvI;AACA;AAAA,MACJ;AAEA,YAAM,cAAc,QAAQ,MAAM;AAElC,YAAM,kBAAkB,kBAAkB,eAAe,OAAO;AAEhE,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,iBAAW,KAAK,aAAa;AACzB,YAAI;AACA,gBAAM,UAAU;AAChB,gBAAM,MAAM,EAAE,MAAM,MAAM,kBAAkB,SAAS;AACrD,cAAI,mBAAmB,OAAO,SAAS;AACnC,iBAAK,QAAQ,IAAI,SAAS,OAAO,kCAAkC,UAAU,6BAA6B;AAC1G,gCAAoB,KAAK,yBAAyB,kBAAkB,cAAc,qCAAqC,IAAI;AAAA,UAC/H;AAEA,sBAAY;AAAA,QAChB,SACO,GAAG;AACN,sBAAY;AACZ,eAAK,QAAQ,IAAI,SAAS,OAAO,8BAA8B,UAAU,kBAAkB,CAAC,IAAI;AAAA,QACpG;AAAA,MACJ;AACA,UAAI,mBAAmB;AACnB,cAAM,KAAK,kBAAkB,iBAAiB;AAAA,MAClD,WACS,iBAAiB;AAEtB,YAAI,WAAW;AACX,8BAAoB,KAAK,yBAAyB,kBAAkB,cAAc,GAAG,SAAS,IAAI,IAAI;AAAA,QAC1G,WACS,QAAQ,QAAW;AACxB,8BAAoB,KAAK,yBAAyB,kBAAkB,cAAc,MAAM,GAAG;AAAA,QAC/F,OACK;AACD,eAAK,QAAQ,IAAI,SAAS,SAAS,wBAAwB,UAAU,+BAA+B,kBAAkB,YAAY,IAAI;AAEtI,8BAAoB,KAAK,yBAAyB,kBAAkB,cAAc,mCAAmC,IAAI;AAAA,QAC7H;AACA,cAAM,KAAK,kBAAkB,iBAAiB;AAAA,MAClD,OACK;AACD,YAAI,KAAK;AACL,eAAK,QAAQ,IAAI,SAAS,OAAO,qBAAqB,UAAU,gDAAgD;AAAA,QACpH;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA,EACA,kBAAkB,OAAO;AACrB,SAAK,QAAQ,IAAI,SAAS,OAAO,kCAAkC,KAAK,2BAA2B,KAAK,gBAAgB,GAAG;AAE3H,SAAK,wBAAwB,KAAK,yBAAyB,SAAS,IAAI,WAAW,+EAA+E;AAGlK,QAAI,KAAK,oBAAoB;AACzB,WAAK,mBAAmB;AAAA,IAC5B;AACA,SAAK,0BAA0B,SAAS,IAAI,MAAM,oEAAoE,CAAC;AACvH,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,QAAI,KAAK,qBAAqB,mBAAmB,eAAe;AAC5D,WAAK,eAAe,KAAK;AAAA,IAC7B,WACS,KAAK,qBAAqB,mBAAmB,aAAa,KAAK,kBAAkB;AAEtF,WAAK,WAAW,KAAK;AAAA,IACzB,WACS,KAAK,qBAAqB,mBAAmB,WAAW;AAC7D,WAAK,eAAe,KAAK;AAAA,IAC7B;AAAA,EAMJ;AAAA,EACA,eAAe,OAAO;AAClB,QAAI,KAAK,oBAAoB;AACzB,WAAK,mBAAmB,mBAAmB;AAC3C,WAAK,qBAAqB;AAC1B,UAAI,KAAK,gBAAgB;AACrB,aAAK,eAAe,SAAS,UAAU,QAAQ,UAAU,SAAS,QAAQ,IAAI,MAAM,oBAAoB,CAAC;AACzG,aAAK,iBAAiB;AAAA,MAC1B;AACA,UAAI,SAAS,WAAW;AACpB,eAAO,SAAS,oBAAoB,UAAU,KAAK,oBAAoB;AAAA,MAC3E;AACA,UAAI;AACA,aAAK,iBAAiB,QAAQ,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC;AAAA,MAC/D,SACO,GAAG;AACN,aAAK,QAAQ,IAAI,SAAS,OAAO,0CAA0C,KAAK,kBAAkB,CAAC,IAAI;AAAA,MAC3G;AAAA,IACJ;AAAA,EACJ;AAAA,EACM,WAAW,OAAO;AAAA;AACpB,YAAM,qBAAqB,KAAK,IAAI;AACpC,UAAI,4BAA4B;AAChC,UAAI,aAAa,UAAU,SAAY,QAAQ,IAAI,MAAM,iDAAiD;AAC1G,UAAI,iBAAiB,KAAK,mBAAmB,6BAA6B,GAAG,UAAU;AACvF,UAAI,mBAAmB,MAAM;AACzB,aAAK,QAAQ,IAAI,SAAS,OAAO,oGAAoG;AACrI,aAAK,eAAe,KAAK;AACzB;AAAA,MACJ;AACA,WAAK,mBAAmB,mBAAmB;AAC3C,UAAI,OAAO;AACP,aAAK,QAAQ,IAAI,SAAS,aAAa,6CAA6C,KAAK,IAAI;AAAA,MACjG,OACK;AACD,aAAK,QAAQ,IAAI,SAAS,aAAa,0BAA0B;AAAA,MACrE;AACA,UAAI,KAAK,uBAAuB,WAAW,GAAG;AAC1C,YAAI;AACA,eAAK,uBAAuB,QAAQ,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC;AAAA,QACrE,SACO,GAAG;AACN,eAAK,QAAQ,IAAI,SAAS,OAAO,iDAAiD,KAAK,kBAAkB,CAAC,IAAI;AAAA,QAClH;AAEA,YAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC3D,eAAK,QAAQ,IAAI,SAAS,OAAO,uFAAuF;AACxH;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,mBAAmB,MAAM;AAC5B,aAAK,QAAQ,IAAI,SAAS,aAAa,4BAA4B,yBAAyB,kBAAkB,cAAc,MAAM;AAClI,cAAM,IAAI,QAAQ,CAAC,YAAY;AAC3B,eAAK,wBAAwB,WAAW,SAAS,cAAc;AAAA,QACnE,CAAC;AACD,aAAK,wBAAwB;AAC7B,YAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC3D,eAAK,QAAQ,IAAI,SAAS,OAAO,mFAAmF;AACpH;AAAA,QACJ;AACA,YAAI;AACA,gBAAM,KAAK,eAAe;AAC1B,eAAK,mBAAmB,mBAAmB;AAC3C,eAAK,QAAQ,IAAI,SAAS,aAAa,yCAAyC;AAChF,cAAI,KAAK,sBAAsB,WAAW,GAAG;AACzC,gBAAI;AACA,mBAAK,sBAAsB,QAAQ,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,KAAK,WAAW,YAAY,CAAC,CAAC;AAAA,YAC3F,SACO,GAAG;AACN,mBAAK,QAAQ,IAAI,SAAS,OAAO,uDAAuD,KAAK,WAAW,YAAY,kBAAkB,CAAC,IAAI;AAAA,YAC/I;AAAA,UACJ;AACA;AAAA,QACJ,SACO,GAAG;AACN,eAAK,QAAQ,IAAI,SAAS,aAAa,8CAA8C,CAAC,IAAI;AAC1F,cAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC3D,iBAAK,QAAQ,IAAI,SAAS,OAAO,4BAA4B,KAAK,gBAAgB,4EAA4E;AAE9J,gBAAI,KAAK,qBAAqB,mBAAmB,eAAe;AAC5D,mBAAK,eAAe;AAAA,YACxB;AACA;AAAA,UACJ;AACA,uBAAa,aAAa,QAAQ,IAAI,IAAI,MAAM,EAAE,SAAS,CAAC;AAC5D,2BAAiB,KAAK,mBAAmB,6BAA6B,KAAK,IAAI,IAAI,oBAAoB,UAAU;AAAA,QACrH;AAAA,MACJ;AACA,WAAK,QAAQ,IAAI,SAAS,aAAa,+CAA+C,KAAK,IAAI,IAAI,kBAAkB,WAAW,yBAAyB,6CAA6C;AACtM,WAAK,eAAe;AAAA,IACxB;AAAA;AAAA,EACA,mBAAmB,oBAAoB,qBAAqB,aAAa;AACrE,QAAI;AACA,aAAO,KAAK,iBAAiB,6BAA6B;AAAA,QACtD;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL,SACO,GAAG;AACN,WAAK,QAAQ,IAAI,SAAS,OAAO,6CAA6C,kBAAkB,KAAK,mBAAmB,kBAAkB,CAAC,IAAI;AAC/I,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,0BAA0B,OAAO;AAC7B,UAAM,YAAY,KAAK;AACvB,SAAK,aAAa,CAAC;AACnB,WAAO,KAAK,SAAS,EAChB,QAAQ,CAAC,QAAQ;AAClB,YAAM,WAAW,UAAU,GAAG;AAC9B,UAAI;AACA,iBAAS,MAAM,KAAK;AAAA,MACxB,SACO,GAAG;AACN,aAAK,QAAQ,IAAI,SAAS,OAAO,wCAAwC,KAAK,kBAAkB,eAAe,CAAC,CAAC,EAAE;AAAA,MACvH;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB;AAChB,QAAI,KAAK,mBAAmB;AACxB,mBAAa,KAAK,iBAAiB;AACnC,WAAK,oBAAoB;AAAA,IAC7B;AAAA,EACJ;AAAA,EACA,kBAAkB;AACd,QAAI,KAAK,gBAAgB;AACrB,mBAAa,KAAK,cAAc;AAAA,IACpC;AAAA,EACJ;AAAA,EACA,kBAAkB,YAAY,MAAM,aAAa,WAAW;AACxD,QAAI,aAAa;AACb,UAAI,UAAU,WAAW,GAAG;AACxB,eAAO;AAAA,UACH,WAAW;AAAA,UACX;AAAA,UACA,QAAQ;AAAA,UACR,MAAM,YAAY;AAAA,QACtB;AAAA,MACJ,OACK;AACD,eAAO;AAAA,UACH,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,MAAM,YAAY;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ,OACK;AACD,YAAM,eAAe,KAAK;AAC1B,WAAK;AACL,UAAI,UAAU,WAAW,GAAG;AACxB,eAAO;AAAA,UACH,WAAW;AAAA,UACX,cAAc,aAAa,SAAS;AAAA,UACpC;AAAA,UACA,QAAQ;AAAA,UACR,MAAM,YAAY;AAAA,QACtB;AAAA,MACJ,OACK;AACD,eAAO;AAAA,UACH,WAAW;AAAA,UACX,cAAc,aAAa,SAAS;AAAA,UACpC,QAAQ;AAAA,UACR,MAAM,YAAY;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,eAAe,SAAS,cAAc;AAClC,QAAI,QAAQ,WAAW,GAAG;AACtB;AAAA,IACJ;AAEA,QAAI,CAAC,cAAc;AACf,qBAAe,QAAQ,QAAQ;AAAA,IACnC;AAGA,eAAW,YAAY,SAAS;AAC5B,cAAQ,QAAQ,EAAE,UAAU;AAAA,QACxB,UAAU,MAAM;AACZ,yBAAe,aAAa,KAAK,MAAM,KAAK,kBAAkB,KAAK,yBAAyB,QAAQ,CAAC,CAAC;AAAA,QAC1G;AAAA,QACA,OAAO,CAAC,QAAQ;AACZ,cAAI;AACJ,cAAI,eAAe,OAAO;AACtB,sBAAU,IAAI;AAAA,UAClB,WACS,OAAO,IAAI,UAAU;AAC1B,sBAAU,IAAI,SAAS;AAAA,UAC3B,OACK;AACD,sBAAU;AAAA,UACd;AACA,yBAAe,aAAa,KAAK,MAAM,KAAK,kBAAkB,KAAK,yBAAyB,UAAU,OAAO,CAAC,CAAC;AAAA,QACnH;AAAA,QACA,MAAM,CAAC,SAAS;AACZ,yBAAe,aAAa,KAAK,MAAM,KAAK,kBAAkB,KAAK,yBAAyB,UAAU,IAAI,CAAC,CAAC;AAAA,QAChH;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,wBAAwB,MAAM;AAC1B,UAAM,UAAU,CAAC;AACjB,UAAM,YAAY,CAAC;AACnB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,YAAM,WAAW,KAAK,CAAC;AACvB,UAAI,KAAK,cAAc,QAAQ,GAAG;AAC9B,cAAM,WAAW,KAAK;AACtB,aAAK;AAEL,gBAAQ,QAAQ,IAAI;AACpB,kBAAU,KAAK,SAAS,SAAS,CAAC;AAElC,aAAK,OAAO,GAAG,CAAC;AAAA,MACpB;AAAA,IACJ;AACA,WAAO,CAAC,SAAS,SAAS;AAAA,EAC9B;AAAA,EACA,cAAc,KAAK;AAEf,WAAO,OAAO,IAAI,aAAa,OAAO,IAAI,cAAc;AAAA,EAC5D;AAAA,EACA,wBAAwB,YAAY,MAAM,WAAW;AACjD,UAAM,eAAe,KAAK;AAC1B,SAAK;AACL,QAAI,UAAU,WAAW,GAAG;AACxB,aAAO;AAAA,QACH,WAAW;AAAA,QACX,cAAc,aAAa,SAAS;AAAA,QACpC;AAAA,QACA,QAAQ;AAAA,QACR,MAAM,YAAY;AAAA,MACtB;AAAA,IACJ,OACK;AACD,aAAO;AAAA,QACH,WAAW;AAAA,QACX,cAAc,aAAa,SAAS;AAAA,QACpC,QAAQ;AAAA,QACR,MAAM,YAAY;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,wBAAwB,IAAI;AACxB,WAAO;AAAA,MACH,cAAc;AAAA,MACd,MAAM,YAAY;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,yBAAyB,IAAI,MAAM;AAC/B,WAAO;AAAA,MACH,cAAc;AAAA,MACd;AAAA,MACA,MAAM,YAAY;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,yBAAyB,IAAI,OAAO,QAAQ;AACxC,QAAI,OAAO;AACP,aAAO;AAAA,QACH;AAAA,QACA,cAAc;AAAA,QACd,MAAM,YAAY;AAAA,MACtB;AAAA,IACJ;AACA,WAAO;AAAA,MACH,cAAc;AAAA,MACd;AAAA,MACA,MAAM,YAAY;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,sBAAsB;AAClB,WAAO,EAAE,MAAM,YAAY,MAAM;AAAA,EACrC;AACJ;;;ACt6BA,IAAM,uCAAuC,CAAC,GAAG,KAAM,KAAO,KAAO,IAAI;AAElE,IAAM,yBAAN,MAA6B;AAAA,EAChC,YAAY,aAAa;AACrB,SAAK,eAAe,gBAAgB,SAAY,CAAC,GAAG,aAAa,IAAI,IAAI;AAAA,EAC7E;AAAA,EACA,6BAA6B,cAAc;AACvC,WAAO,KAAK,aAAa,aAAa,kBAAkB;AAAA,EAC5D;AACJ;;;ACVO,IAAM,cAAN,MAAkB;AACzB;AACA,YAAY,gBAAgB;AAC5B,YAAY,SAAS;;;ACAd,IAAM,wBAAN,cAAoC,WAAW;AAAA,EAClD,YAAY,aAAa,oBAAoB;AACzC,UAAM;AACN,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAAA,EAC/B;AAAA,EACM,KAAK,SAAS;AAAA;AAChB,UAAI,aAAa;AACjB,UAAI,KAAK,wBAAwB,CAAC,KAAK,gBAAiB,QAAQ,OAAO,QAAQ,IAAI,QAAQ,aAAa,IAAI,IAAK;AAE7G,qBAAa;AACb,aAAK,eAAe,MAAM,KAAK,oBAAoB;AAAA,MACvD;AACA,WAAK,wBAAwB,OAAO;AACpC,YAAM,WAAW,MAAM,KAAK,aAAa,KAAK,OAAO;AACrD,UAAI,cAAc,SAAS,eAAe,OAAO,KAAK,qBAAqB;AACvE,aAAK,eAAe,MAAM,KAAK,oBAAoB;AACnD,aAAK,wBAAwB,OAAO;AACpC,eAAO,MAAM,KAAK,aAAa,KAAK,OAAO;AAAA,MAC/C;AACA,aAAO;AAAA,IACX;AAAA;AAAA,EACA,wBAAwB,SAAS;AAC7B,QAAI,CAAC,QAAQ,SAAS;AAClB,cAAQ,UAAU,CAAC;AAAA,IACvB;AACA,QAAI,KAAK,cAAc;AACnB,cAAQ,QAAQ,YAAY,aAAa,IAAI,UAAU,KAAK,YAAY;AAAA,IAC5E,WAES,KAAK,qBAAqB;AAC/B,UAAI,QAAQ,QAAQ,YAAY,aAAa,GAAG;AAC5C,eAAO,QAAQ,QAAQ,YAAY,aAAa;AAAA,MACpD;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,gBAAgB,KAAK;AACjB,WAAO,KAAK,aAAa,gBAAgB,GAAG;AAAA,EAChD;AACJ;;;ACxCO,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAE1B,EAAAA,mBAAkBA,mBAAkB,MAAM,IAAI,CAAC,IAAI;AAEnD,EAAAA,mBAAkBA,mBAAkB,YAAY,IAAI,CAAC,IAAI;AAEzD,EAAAA,mBAAkBA,mBAAkB,kBAAkB,IAAI,CAAC,IAAI;AAE/D,EAAAA,mBAAkBA,mBAAkB,aAAa,IAAI,CAAC,IAAI;AAC9D,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEzC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAEvB,EAAAA,gBAAeA,gBAAe,MAAM,IAAI,CAAC,IAAI;AAE7C,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,CAAC,IAAI;AACnD,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;;;ACfnC,IAAMC,mBAAN,MAAsB;AAAA,EACzB,cAAc;AACV,SAAK,aAAa;AAClB,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,QAAQ;AACJ,QAAI,CAAC,KAAK,YAAY;AAClB,WAAK,aAAa;AAClB,UAAI,KAAK,SAAS;AACd,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,IAAI,SAAS;AACT,WAAO;AAAA,EACX;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK;AAAA,EAChB;AACJ;;;ACjBO,IAAM,uBAAN,MAA2B;AAAA;AAAA,EAE9B,IAAI,cAAc;AACd,WAAO,KAAK,WAAW;AAAA,EAC3B;AAAA,EACA,YAAY,YAAY,QAAQ,SAAS;AACrC,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,aAAa,IAAIC,iBAAgB;AACtC,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,UAAU;AAAA,EACnB;AAAA,EACM,QAAQ,KAAK,gBAAgB;AAAA;AAC/B,UAAI,WAAW,KAAK,KAAK;AACzB,UAAI,WAAW,gBAAgB,gBAAgB;AAC/C,UAAI,KAAK,gBAAgB,gBAAgB,gBAAgB;AACzD,WAAK,OAAO;AACZ,WAAK,QAAQ,IAAI,SAAS,OAAO,qCAAqC;AAEtE,UAAI,mBAAmB,eAAe,WACjC,OAAO,mBAAmB,eAAe,OAAO,IAAI,eAAe,EAAE,iBAAiB,WAAW;AAClG,cAAM,IAAI,MAAM,4FAA4F;AAAA,MAChH;AACA,YAAM,CAAC,MAAM,KAAK,IAAI,mBAAmB;AACzC,YAAM,UAAU,iBAAE,CAAC,IAAI,GAAG,SAAU,KAAK,SAAS;AAClD,YAAM,cAAc;AAAA,QAChB,aAAa,KAAK,WAAW;AAAA,QAC7B;AAAA,QACA,SAAS;AAAA,QACT,iBAAiB,KAAK,SAAS;AAAA,MACnC;AACA,UAAI,mBAAmB,eAAe,QAAQ;AAC1C,oBAAY,eAAe;AAAA,MAC/B;AAGA,YAAM,UAAU,GAAG,GAAG,MAAM,KAAK,IAAI,CAAC;AACtC,WAAK,QAAQ,IAAI,SAAS,OAAO,oCAAoC,OAAO,GAAG;AAC/E,YAAM,WAAW,MAAM,KAAK,YAAY,IAAI,SAAS,WAAW;AAChE,UAAI,SAAS,eAAe,KAAK;AAC7B,aAAK,QAAQ,IAAI,SAAS,OAAO,qDAAqD,SAAS,UAAU,GAAG;AAE5G,aAAK,cAAc,IAAI,UAAU,SAAS,cAAc,IAAI,SAAS,UAAU;AAC/E,aAAK,WAAW;AAAA,MACpB,OACK;AACD,aAAK,WAAW;AAAA,MACpB;AACA,WAAK,aAAa,KAAK,MAAM,KAAK,MAAM,WAAW;AAAA,IACvD;AAAA;AAAA,EACM,MAAM,KAAK,aAAa;AAAA;AAC1B,UAAI;AACA,eAAO,KAAK,UAAU;AAClB,cAAI;AACA,kBAAM,UAAU,GAAG,GAAG,MAAM,KAAK,IAAI,CAAC;AACtC,iBAAK,QAAQ,IAAI,SAAS,OAAO,oCAAoC,OAAO,GAAG;AAC/E,kBAAM,WAAW,MAAM,KAAK,YAAY,IAAI,SAAS,WAAW;AAChE,gBAAI,SAAS,eAAe,KAAK;AAC7B,mBAAK,QAAQ,IAAI,SAAS,aAAa,oDAAoD;AAC3F,mBAAK,WAAW;AAAA,YACpB,WACS,SAAS,eAAe,KAAK;AAClC,mBAAK,QAAQ,IAAI,SAAS,OAAO,qDAAqD,SAAS,UAAU,GAAG;AAE5G,mBAAK,cAAc,IAAI,UAAU,SAAS,cAAc,IAAI,SAAS,UAAU;AAC/E,mBAAK,WAAW;AAAA,YACpB,OACK;AAED,kBAAI,SAAS,SAAS;AAClB,qBAAK,QAAQ,IAAI,SAAS,OAAO,0CAA0C,cAAc,SAAS,SAAS,KAAK,SAAS,iBAAiB,CAAC,GAAG;AAC9I,oBAAI,KAAK,WAAW;AAChB,uBAAK,UAAU,SAAS,OAAO;AAAA,gBACnC;AAAA,cACJ,OACK;AAED,qBAAK,QAAQ,IAAI,SAAS,OAAO,oDAAoD;AAAA,cACzF;AAAA,YACJ;AAAA,UACJ,SACO,GAAG;AACN,gBAAI,CAAC,KAAK,UAAU;AAEhB,mBAAK,QAAQ,IAAI,SAAS,OAAO,wDAAwD,EAAE,OAAO,EAAE;AAAA,YACxG,OACK;AACD,kBAAI,aAAa,cAAc;AAE3B,qBAAK,QAAQ,IAAI,SAAS,OAAO,oDAAoD;AAAA,cACzF,OACK;AAED,qBAAK,cAAc;AACnB,qBAAK,WAAW;AAAA,cACpB;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,UACA;AACI,aAAK,QAAQ,IAAI,SAAS,OAAO,2CAA2C;AAG5E,YAAI,CAAC,KAAK,aAAa;AACnB,eAAK,cAAc;AAAA,QACvB;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA,EACM,KAAK,MAAM;AAAA;AACb,UAAI,CAAC,KAAK,UAAU;AAChB,eAAO,QAAQ,OAAO,IAAI,MAAM,8CAA8C,CAAC;AAAA,MACnF;AACA,aAAO,YAAY,KAAK,SAAS,eAAe,KAAK,aAAa,KAAK,MAAM,MAAM,KAAK,QAAQ;AAAA,IACpG;AAAA;AAAA,EACM,OAAO;AAAA;AACT,WAAK,QAAQ,IAAI,SAAS,OAAO,2CAA2C;AAE5E,WAAK,WAAW;AAChB,WAAK,WAAW,MAAM;AACtB,UAAI;AACA,cAAM,KAAK;AAEX,aAAK,QAAQ,IAAI,SAAS,OAAO,qDAAqD,KAAK,IAAI,GAAG;AAClG,cAAM,UAAU,CAAC;AACjB,cAAM,CAAC,MAAM,KAAK,IAAI,mBAAmB;AACzC,gBAAQ,IAAI,IAAI;AAChB,cAAM,gBAAgB;AAAA,UAClB,SAAS,kCAAK,UAAY,KAAK,SAAS;AAAA,UACxC,SAAS,KAAK,SAAS;AAAA,UACvB,iBAAiB,KAAK,SAAS;AAAA,QACnC;AACA,YAAI;AACJ,YAAI;AACA,gBAAM,KAAK,YAAY,OAAO,KAAK,MAAM,aAAa;AAAA,QAC1D,SACO,KAAK;AACR,kBAAQ;AAAA,QACZ;AACA,YAAI,OAAO;AACP,cAAI,iBAAiB,WAAW;AAC5B,gBAAI,MAAM,eAAe,KAAK;AAC1B,mBAAK,QAAQ,IAAI,SAAS,OAAO,oFAAoF;AAAA,YACzH,OACK;AACD,mBAAK,QAAQ,IAAI,SAAS,OAAO,2DAA2D,KAAK,EAAE;AAAA,YACvG;AAAA,UACJ;AAAA,QACJ,OACK;AACD,eAAK,QAAQ,IAAI,SAAS,OAAO,kDAAkD;AAAA,QACvF;AAAA,MACJ,UACA;AACI,aAAK,QAAQ,IAAI,SAAS,OAAO,wCAAwC;AAGzE,aAAK,cAAc;AAAA,MACvB;AAAA,IACJ;AAAA;AAAA,EACA,gBAAgB;AACZ,QAAI,KAAK,SAAS;AACd,UAAI,aAAa;AACjB,UAAI,KAAK,aAAa;AAClB,sBAAc,aAAa,KAAK;AAAA,MACpC;AACA,WAAK,QAAQ,IAAI,SAAS,OAAO,UAAU;AAC3C,WAAK,QAAQ,KAAK,WAAW;AAAA,IACjC;AAAA,EACJ;AACJ;;;AC/KO,IAAM,4BAAN,MAAgC;AAAA,EACnC,YAAY,YAAY,aAAa,QAAQ,SAAS;AAClD,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,UAAU;AAAA,EACnB;AAAA,EACM,QAAQ,KAAK,gBAAgB;AAAA;AAC/B,UAAI,WAAW,KAAK,KAAK;AACzB,UAAI,WAAW,gBAAgB,gBAAgB;AAC/C,UAAI,KAAK,gBAAgB,gBAAgB,gBAAgB;AACzD,WAAK,QAAQ,IAAI,SAAS,OAAO,6BAA6B;AAE9D,WAAK,OAAO;AACZ,UAAI,KAAK,cAAc;AACnB,gBAAQ,IAAI,QAAQ,GAAG,IAAI,IAAI,MAAM,OAAO,gBAAgB,mBAAmB,KAAK,YAAY,CAAC;AAAA,MACrG;AACA,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,YAAI,SAAS;AACb,YAAI,mBAAmB,eAAe,MAAM;AACxC,iBAAO,IAAI,MAAM,2EAA2E,CAAC;AAC7F;AAAA,QACJ;AACA,YAAI;AACJ,YAAI,SAAS,aAAa,SAAS,aAAa;AAC5C,wBAAc,IAAI,KAAK,SAAS,YAAY,KAAK,EAAE,iBAAiB,KAAK,SAAS,gBAAgB,CAAC;AAAA,QACvG,OACK;AAED,gBAAM,UAAU,KAAK,YAAY,gBAAgB,GAAG;AACpD,gBAAM,UAAU,CAAC;AACjB,kBAAQ,SAAS;AACjB,gBAAM,CAAC,MAAM,KAAK,IAAI,mBAAmB;AACzC,kBAAQ,IAAI,IAAI;AAChB,wBAAc,IAAI,KAAK,SAAS,YAAY,KAAK,EAAE,iBAAiB,KAAK,SAAS,iBAAiB,SAAS,kCAAK,UAAY,KAAK,SAAS,SAAU,CAAC;AAAA,QAC1J;AACA,YAAI;AACA,sBAAY,YAAY,CAAC,MAAM;AAC3B,gBAAI,KAAK,WAAW;AAChB,kBAAI;AACA,qBAAK,QAAQ,IAAI,SAAS,OAAO,kCAAkC,cAAc,EAAE,MAAM,KAAK,SAAS,iBAAiB,CAAC,GAAG;AAC5H,qBAAK,UAAU,EAAE,IAAI;AAAA,cACzB,SACO,OAAO;AACV,qBAAK,OAAO,KAAK;AACjB;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAEA,sBAAY,UAAU,CAAC,MAAM;AAEzB,gBAAI,QAAQ;AACR,mBAAK,OAAO;AAAA,YAChB,OACK;AACD,qBAAO,IAAI,MAAM,8PAE4D,CAAC;AAAA,YAClF;AAAA,UACJ;AACA,sBAAY,SAAS,MAAM;AACvB,iBAAK,QAAQ,IAAI,SAAS,aAAa,oBAAoB,KAAK,IAAI,EAAE;AACtE,iBAAK,eAAe;AACpB,qBAAS;AACT,oBAAQ;AAAA,UACZ;AAAA,QACJ,SACO,GAAG;AACN,iBAAO,CAAC;AACR;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AAAA;AAAA,EACM,KAAK,MAAM;AAAA;AACb,UAAI,CAAC,KAAK,cAAc;AACpB,eAAO,QAAQ,OAAO,IAAI,MAAM,8CAA8C,CAAC;AAAA,MACnF;AACA,aAAO,YAAY,KAAK,SAAS,OAAO,KAAK,aAAa,KAAK,MAAM,MAAM,KAAK,QAAQ;AAAA,IAC5F;AAAA;AAAA,EACA,OAAO;AACH,SAAK,OAAO;AACZ,WAAO,QAAQ,QAAQ;AAAA,EAC3B;AAAA,EACA,OAAO,GAAG;AACN,QAAI,KAAK,cAAc;AACnB,WAAK,aAAa,MAAM;AACxB,WAAK,eAAe;AACpB,UAAI,KAAK,SAAS;AACd,aAAK,QAAQ,CAAC;AAAA,MAClB;AAAA,IACJ;AAAA,EACJ;AACJ;;;AC9FO,IAAM,qBAAN,MAAyB;AAAA,EAC5B,YAAY,YAAY,oBAAoB,QAAQ,mBAAmB,sBAAsB,SAAS;AAClG,SAAK,UAAU;AACf,SAAK,sBAAsB;AAC3B,SAAK,qBAAqB;AAC1B,SAAK,wBAAwB;AAC7B,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,WAAW;AAAA,EACpB;AAAA,EACM,QAAQ,KAAK,gBAAgB;AAAA;AAC/B,UAAI,WAAW,KAAK,KAAK;AACzB,UAAI,WAAW,gBAAgB,gBAAgB;AAC/C,UAAI,KAAK,gBAAgB,gBAAgB,gBAAgB;AACzD,WAAK,QAAQ,IAAI,SAAS,OAAO,oCAAoC;AACrE,UAAI;AACJ,UAAI,KAAK,qBAAqB;AAC1B,gBAAQ,MAAM,KAAK,oBAAoB;AAAA,MAC3C;AACA,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,cAAM,IAAI,QAAQ,SAAS,IAAI;AAC/B,YAAI;AACJ,cAAM,UAAU,KAAK,YAAY,gBAAgB,GAAG;AACpD,YAAI,SAAS;AACb,YAAI,SAAS,UAAU,SAAS,eAAe;AAC3C,gBAAM,UAAU,CAAC;AACjB,gBAAM,CAAC,MAAM,KAAK,IAAI,mBAAmB;AACzC,kBAAQ,IAAI,IAAI;AAChB,cAAI,OAAO;AACP,oBAAQ,YAAY,aAAa,IAAI,UAAU,KAAK;AAAA,UACxD;AACA,cAAI,SAAS;AACT,oBAAQ,YAAY,MAAM,IAAI;AAAA,UAClC;AAEA,sBAAY,IAAI,KAAK,sBAAsB,KAAK,QAAW;AAAA,YACvD,SAAS,kCAAK,UAAY,KAAK;AAAA,UACnC,CAAC;AAAA,QACL,OACK;AACD,cAAI,OAAO;AACP,oBAAQ,IAAI,QAAQ,GAAG,IAAI,IAAI,MAAM,OAAO,gBAAgB,mBAAmB,KAAK,CAAC;AAAA,UACzF;AAAA,QACJ;AACA,YAAI,CAAC,WAAW;AAEZ,sBAAY,IAAI,KAAK,sBAAsB,GAAG;AAAA,QAClD;AACA,YAAI,mBAAmB,eAAe,QAAQ;AAC1C,oBAAU,aAAa;AAAA,QAC3B;AACA,kBAAU,SAAS,CAAC,WAAW;AAC3B,eAAK,QAAQ,IAAI,SAAS,aAAa,0BAA0B,GAAG,GAAG;AACvE,eAAK,aAAa;AAClB,mBAAS;AACT,kBAAQ;AAAA,QACZ;AACA,kBAAU,UAAU,CAAC,UAAU;AAC3B,cAAI,QAAQ;AAEZ,cAAI,OAAO,eAAe,eAAe,iBAAiB,YAAY;AAClE,oBAAQ,MAAM;AAAA,UAClB,OACK;AACD,oBAAQ;AAAA,UACZ;AACA,eAAK,QAAQ,IAAI,SAAS,aAAa,0BAA0B,KAAK,GAAG;AAAA,QAC7E;AACA,kBAAU,YAAY,CAAC,YAAY;AAC/B,eAAK,QAAQ,IAAI,SAAS,OAAO,yCAAyC,cAAc,QAAQ,MAAM,KAAK,kBAAkB,CAAC,GAAG;AACjI,cAAI,KAAK,WAAW;AAChB,gBAAI;AACA,mBAAK,UAAU,QAAQ,IAAI;AAAA,YAC/B,SACO,OAAO;AACV,mBAAK,OAAO,KAAK;AACjB;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,kBAAU,UAAU,CAAC,UAAU;AAG3B,cAAI,QAAQ;AACR,iBAAK,OAAO,KAAK;AAAA,UACrB,OACK;AACD,gBAAI,QAAQ;AAEZ,gBAAI,OAAO,eAAe,eAAe,iBAAiB,YAAY;AAClE,sBAAQ,MAAM;AAAA,YAClB,OACK;AACD,sBAAQ;AAAA,YAIZ;AACA,mBAAO,IAAI,MAAM,KAAK,CAAC;AAAA,UAC3B;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AAAA;AAAA,EACA,KAAK,MAAM;AACP,QAAI,KAAK,cAAc,KAAK,WAAW,eAAe,KAAK,sBAAsB,MAAM;AACnF,WAAK,QAAQ,IAAI,SAAS,OAAO,wCAAwC,cAAc,MAAM,KAAK,kBAAkB,CAAC,GAAG;AACxH,WAAK,WAAW,KAAK,IAAI;AACzB,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AACA,WAAO,QAAQ,OAAO,oCAAoC;AAAA,EAC9D;AAAA,EACA,OAAO;AACH,QAAI,KAAK,YAAY;AAGjB,WAAK,OAAO,MAAS;AAAA,IACzB;AACA,WAAO,QAAQ,QAAQ;AAAA,EAC3B;AAAA,EACA,OAAO,OAAO;AAEV,QAAI,KAAK,YAAY;AAEjB,WAAK,WAAW,UAAU,MAAM;AAAA,MAAE;AAClC,WAAK,WAAW,YAAY,MAAM;AAAA,MAAE;AACpC,WAAK,WAAW,UAAU,MAAM;AAAA,MAAE;AAClC,WAAK,WAAW,MAAM;AACtB,WAAK,aAAa;AAAA,IACtB;AACA,SAAK,QAAQ,IAAI,SAAS,OAAO,uCAAuC;AACxE,QAAI,KAAK,SAAS;AACd,UAAI,KAAK,cAAc,KAAK,MAAM,MAAM,aAAa,SAAS,MAAM,SAAS,MAAO;AAChF,aAAK,QAAQ,IAAI,MAAM,sCAAsC,MAAM,IAAI,KAAK,MAAM,UAAU,iBAAiB,IAAI,CAAC;AAAA,MACtH,WACS,iBAAiB,OAAO;AAC7B,aAAK,QAAQ,KAAK;AAAA,MACtB,OACK;AACD,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,cAAc,OAAO;AACjB,WAAO,SAAS,OAAO,MAAM,aAAa,aAAa,OAAO,MAAM,SAAS;AAAA,EACjF;AACJ;;;AC9IA,IAAM,gBAAgB;AAEf,IAAM,iBAAN,MAAqB;AAAA,EACxB,YAAY,KAAK,UAAU,CAAC,GAAG;AAC3B,SAAK,uBAAuB,MAAM;AAAA,IAAE;AACpC,SAAK,WAAW,CAAC;AACjB,SAAK,oBAAoB;AACzB,QAAI,WAAW,KAAK,KAAK;AACzB,SAAK,UAAU,aAAa,QAAQ,MAAM;AAC1C,SAAK,UAAU,KAAK,YAAY,GAAG;AACnC,cAAU,WAAW,CAAC;AACtB,YAAQ,oBAAoB,QAAQ,sBAAsB,SAAY,QAAQ,QAAQ;AACtF,QAAI,OAAO,QAAQ,oBAAoB,aAAa,QAAQ,oBAAoB,QAAW;AACvF,cAAQ,kBAAkB,QAAQ,oBAAoB,SAAY,OAAO,QAAQ;AAAA,IACrF,OACK;AACD,YAAM,IAAI,MAAM,iEAAiE;AAAA,IACrF;AACA,YAAQ,UAAU,QAAQ,YAAY,SAAY,MAAM,MAAO,QAAQ;AACvE,QAAI,kBAAkB;AACtB,QAAI,oBAAoB;AACxB,QAAI,SAAS,UAAU,OAAO,cAAY,aAAa;AAGnD,YAAM,cAAc,OAAO,wBAAwB,aAAa,0BAA0B;AAC1F,wBAAkB,YAAY,IAAI;AAClC,0BAAoB,YAAY,aAAa;AAAA,IACjD;AACA,QAAI,CAAC,SAAS,UAAU,OAAO,cAAc,eAAe,CAAC,QAAQ,WAAW;AAC5E,cAAQ,YAAY;AAAA,IACxB,WACS,SAAS,UAAU,CAAC,QAAQ,WAAW;AAC5C,UAAI,iBAAiB;AACjB,gBAAQ,YAAY;AAAA,MACxB;AAAA,IACJ;AACA,QAAI,CAAC,SAAS,UAAU,OAAO,gBAAgB,eAAe,CAAC,QAAQ,aAAa;AAChF,cAAQ,cAAc;AAAA,IAC1B,WACS,SAAS,UAAU,CAAC,QAAQ,aAAa;AAC9C,UAAI,OAAO,sBAAsB,aAAa;AAC1C,gBAAQ,cAAc;AAAA,MAC1B;AAAA,IACJ;AACA,SAAK,cAAc,IAAI,sBAAsB,QAAQ,cAAc,IAAI,kBAAkB,KAAK,OAAO,GAAG,QAAQ,kBAAkB;AAClI,SAAK,mBAAmB;AACxB,SAAK,qBAAqB;AAC1B,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,UAAU;AAAA,EACnB;AAAA,EACM,MAAM,gBAAgB;AAAA;AACxB,uBAAiB,kBAAkB,eAAe;AAClD,UAAI,KAAK,gBAAgB,gBAAgB,gBAAgB;AACzD,WAAK,QAAQ,IAAI,SAAS,OAAO,6CAA6C,eAAe,cAAc,CAAC,IAAI;AAChH,UAAI,KAAK,qBAAqB,gBAAmD;AAC7E,eAAO,QAAQ,OAAO,IAAI,MAAM,yEAAyE,CAAC;AAAA,MAC9G;AACA,WAAK,mBAAmB;AACxB,WAAK,wBAAwB,KAAK,eAAe,cAAc;AAC/D,YAAM,KAAK;AAEX,UAAI,KAAK,qBAAqB,iBAAqD;AAE/E,cAAM,UAAU;AAChB,aAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AAExC,cAAM,KAAK;AACX,eAAO,QAAQ,OAAO,IAAI,WAAW,OAAO,CAAC;AAAA,MACjD,WACS,KAAK,qBAAqB,aAA6C;AAE5E,cAAM,UAAU;AAChB,aAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AACxC,eAAO,QAAQ,OAAO,IAAI,WAAW,OAAO,CAAC;AAAA,MACjD;AACA,WAAK,qBAAqB;AAAA,IAC9B;AAAA;AAAA,EACA,KAAK,MAAM;AACP,QAAI,KAAK,qBAAqB,aAA6C;AACvE,aAAO,QAAQ,OAAO,IAAI,MAAM,qEAAqE,CAAC;AAAA,IAC1G;AACA,QAAI,CAAC,KAAK,YAAY;AAClB,WAAK,aAAa,IAAI,mBAAmB,KAAK,SAAS;AAAA,IAC3D;AAEA,WAAO,KAAK,WAAW,KAAK,IAAI;AAAA,EACpC;AAAA,EACM,KAAK,OAAO;AAAA;AACd,UAAI,KAAK,qBAAqB,gBAAmD;AAC7E,aAAK,QAAQ,IAAI,SAAS,OAAO,+BAA+B,KAAK,wEAAwE;AAC7I,eAAO,QAAQ,QAAQ;AAAA,MAC3B;AACA,UAAI,KAAK,qBAAqB,iBAAqD;AAC/E,aAAK,QAAQ,IAAI,SAAS,OAAO,+BAA+B,KAAK,yEAAyE;AAC9I,eAAO,KAAK;AAAA,MAChB;AACA,WAAK,mBAAmB;AACxB,WAAK,eAAe,IAAI,QAAQ,CAAC,YAAY;AAEzC,aAAK,uBAAuB;AAAA,MAChC,CAAC;AAED,YAAM,KAAK,cAAc,KAAK;AAC9B,YAAM,KAAK;AAAA,IACf;AAAA;AAAA,EACM,cAAc,OAAO;AAAA;AAIvB,WAAK,aAAa;AAClB,UAAI;AACA,cAAM,KAAK;AAAA,MACf,SACO,GAAG;AAAA,MAEV;AAIA,UAAI,KAAK,WAAW;AAChB,YAAI;AACA,gBAAM,KAAK,UAAU,KAAK;AAAA,QAC9B,SACO,GAAG;AACN,eAAK,QAAQ,IAAI,SAAS,OAAO,gDAAgD,CAAC,IAAI;AACtF,eAAK,gBAAgB;AAAA,QACzB;AACA,aAAK,YAAY;AAAA,MACrB,OACK;AACD,aAAK,QAAQ,IAAI,SAAS,OAAO,wFAAwF;AAAA,MAC7H;AAAA,IACJ;AAAA;AAAA,EACM,eAAe,gBAAgB;AAAA;AAGjC,UAAI,MAAM,KAAK;AACf,WAAK,sBAAsB,KAAK,SAAS;AACzC,WAAK,YAAY,sBAAsB,KAAK;AAC5C,UAAI;AACA,YAAI,KAAK,SAAS,iBAAiB;AAC/B,cAAI,KAAK,SAAS,cAAc,kBAAkB,YAAY;AAE1D,iBAAK,YAAY,KAAK,oBAAoB,kBAAkB,UAAU;AAGtE,kBAAM,KAAK,gBAAgB,KAAK,cAAc;AAAA,UAClD,OACK;AACD,kBAAM,IAAI,MAAM,8EAA8E;AAAA,UAClG;AAAA,QACJ,OACK;AACD,cAAI,oBAAoB;AACxB,cAAI,YAAY;AAChB,aAAG;AACC,gCAAoB,MAAM,KAAK,wBAAwB,GAAG;AAE1D,gBAAI,KAAK,qBAAqB,mBAAuD,KAAK,qBAAqB,gBAAmD;AAC9J,oBAAM,IAAI,WAAW,gDAAgD;AAAA,YACzE;AACA,gBAAI,kBAAkB,OAAO;AACzB,oBAAM,IAAI,MAAM,kBAAkB,KAAK;AAAA,YAC3C;AACA,gBAAI,kBAAkB,iBAAiB;AACnC,oBAAM,IAAI,MAAM,8LAA8L;AAAA,YAClN;AACA,gBAAI,kBAAkB,KAAK;AACvB,oBAAM,kBAAkB;AAAA,YAC5B;AACA,gBAAI,kBAAkB,aAAa;AAG/B,oBAAM,cAAc,kBAAkB;AACtC,mBAAK,sBAAsB,MAAM;AAEjC,mBAAK,YAAY,eAAe;AAChC,mBAAK,YAAY,sBAAsB;AAAA,YAC3C;AACA;AAAA,UACJ,SAAS,kBAAkB,OAAO,YAAY;AAC9C,cAAI,cAAc,iBAAiB,kBAAkB,KAAK;AACtD,kBAAM,IAAI,MAAM,uCAAuC;AAAA,UAC3D;AACA,gBAAM,KAAK,iBAAiB,KAAK,KAAK,SAAS,WAAW,mBAAmB,cAAc;AAAA,QAC/F;AACA,YAAI,KAAK,qBAAqB,sBAAsB;AAChD,eAAK,SAAS,oBAAoB;AAAA,QACtC;AACA,YAAI,KAAK,qBAAqB,cAA+C;AAGzE,eAAK,QAAQ,IAAI,SAAS,OAAO,4CAA4C;AAC7E,eAAK,mBAAmB;AAAA,QAC5B;AAAA,MAIJ,SACO,GAAG;AACN,aAAK,QAAQ,IAAI,SAAS,OAAO,qCAAqC,CAAC;AACvE,aAAK,mBAAmB;AACxB,aAAK,YAAY;AAEjB,aAAK,qBAAqB;AAC1B,eAAO,QAAQ,OAAO,CAAC;AAAA,MAC3B;AAAA,IACJ;AAAA;AAAA,EACM,wBAAwB,KAAK;AAAA;AAC/B,YAAM,UAAU,CAAC;AACjB,YAAM,CAAC,MAAM,KAAK,IAAI,mBAAmB;AACzC,cAAQ,IAAI,IAAI;AAChB,YAAM,eAAe,KAAK,qBAAqB,GAAG;AAClD,WAAK,QAAQ,IAAI,SAAS,OAAO,gCAAgC,YAAY,GAAG;AAChF,UAAI;AACA,cAAM,WAAW,MAAM,KAAK,YAAY,KAAK,cAAc;AAAA,UACvD,SAAS;AAAA,UACT,SAAS,kCAAK,UAAY,KAAK,SAAS;AAAA,UACxC,SAAS,KAAK,SAAS;AAAA,UACvB,iBAAiB,KAAK,SAAS;AAAA,QACnC,CAAC;AACD,YAAI,SAAS,eAAe,KAAK;AAC7B,iBAAO,QAAQ,OAAO,IAAI,MAAM,mDAAmD,SAAS,UAAU,GAAG,CAAC;AAAA,QAC9G;AACA,cAAM,oBAAoB,KAAK,MAAM,SAAS,OAAO;AACrD,YAAI,CAAC,kBAAkB,oBAAoB,kBAAkB,mBAAmB,GAAG;AAG/E,4BAAkB,kBAAkB,kBAAkB;AAAA,QAC1D;AACA,YAAI,kBAAkB,wBAAwB,KAAK,SAAS,0BAA0B,MAAM;AACxF,iBAAO,QAAQ,OAAO,IAAI,iCAAiC,gEAAgE,CAAC;AAAA,QAChI;AACA,eAAO;AAAA,MACX,SACO,GAAG;AACN,YAAI,eAAe,qDAAqD;AACxE,YAAI,aAAa,WAAW;AACxB,cAAI,EAAE,eAAe,KAAK;AACtB,2BAAe,eAAe;AAAA,UAClC;AAAA,QACJ;AACA,aAAK,QAAQ,IAAI,SAAS,OAAO,YAAY;AAC7C,eAAO,QAAQ,OAAO,IAAI,iCAAiC,YAAY,CAAC;AAAA,MAC5E;AAAA,IACJ;AAAA;AAAA,EACA,kBAAkB,KAAK,iBAAiB;AACpC,QAAI,CAAC,iBAAiB;AAClB,aAAO;AAAA,IACX;AACA,WAAO,OAAO,IAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,OAAO,MAAM,eAAe;AAAA,EAC9E;AAAA,EACM,iBAAiB,KAAK,oBAAoB,mBAAmB,yBAAyB;AAAA;AACxF,UAAI,aAAa,KAAK,kBAAkB,KAAK,kBAAkB,eAAe;AAC9E,UAAI,KAAK,cAAc,kBAAkB,GAAG;AACxC,aAAK,QAAQ,IAAI,SAAS,OAAO,yEAAyE;AAC1G,aAAK,YAAY;AACjB,cAAM,KAAK,gBAAgB,YAAY,uBAAuB;AAC9D,aAAK,eAAe,kBAAkB;AACtC;AAAA,MACJ;AACA,YAAM,sBAAsB,CAAC;AAC7B,YAAM,aAAa,kBAAkB,uBAAuB,CAAC;AAC7D,UAAI,YAAY;AAChB,iBAAW,YAAY,YAAY;AAC/B,cAAM,mBAAmB,KAAK,yBAAyB,UAAU,oBAAoB,0BAA0B,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,0BAA0B,IAAI;AAC7M,YAAI,4BAA4B,OAAO;AAEnC,8BAAoB,KAAK,GAAG,SAAS,SAAS,UAAU;AACxD,8BAAoB,KAAK,gBAAgB;AAAA,QAC7C,WACS,KAAK,cAAc,gBAAgB,GAAG;AAC3C,eAAK,YAAY;AACjB,cAAI,CAAC,WAAW;AACZ,gBAAI;AACA,0BAAY,MAAM,KAAK,wBAAwB,GAAG;AAAA,YACtD,SACO,IAAI;AACP,qBAAO,QAAQ,OAAO,EAAE;AAAA,YAC5B;AACA,yBAAa,KAAK,kBAAkB,KAAK,UAAU,eAAe;AAAA,UACtE;AACA,cAAI;AACA,kBAAM,KAAK,gBAAgB,YAAY,uBAAuB;AAC9D,iBAAK,eAAe,UAAU;AAC9B;AAAA,UACJ,SACO,IAAI;AACP,iBAAK,QAAQ,IAAI,SAAS,OAAO,kCAAkC,SAAS,SAAS,MAAM,EAAE,EAAE;AAC/F,wBAAY;AACZ,gCAAoB,KAAK,IAAI,4BAA4B,GAAG,SAAS,SAAS,YAAY,EAAE,IAAI,kBAAkB,SAAS,SAAS,CAAC,CAAC;AACtI,gBAAI,KAAK,qBAAqB,cAA+C;AACzE,oBAAM,UAAU;AAChB,mBAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AACxC,qBAAO,QAAQ,OAAO,IAAI,WAAW,OAAO,CAAC;AAAA,YACjD;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,oBAAoB,SAAS,GAAG;AAChC,eAAO,QAAQ,OAAO,IAAI,gBAAgB,yEAAyE,oBAAoB,KAAK,GAAG,CAAC,IAAI,mBAAmB,CAAC;AAAA,MAC5K;AACA,aAAO,QAAQ,OAAO,IAAI,MAAM,6EAA6E,CAAC;AAAA,IAClH;AAAA;AAAA,EACA,oBAAoB,WAAW;AAC3B,YAAQ,WAAW;AAAA,MACf,KAAK,kBAAkB;AACnB,YAAI,CAAC,KAAK,SAAS,WAAW;AAC1B,gBAAM,IAAI,MAAM,mDAAmD;AAAA,QACvE;AACA,eAAO,IAAI,mBAAmB,KAAK,aAAa,KAAK,qBAAqB,KAAK,SAAS,KAAK,SAAS,mBAAmB,KAAK,SAAS,WAAW,KAAK,SAAS,WAAW,CAAC,CAAC;AAAA,MACjL,KAAK,kBAAkB;AACnB,YAAI,CAAC,KAAK,SAAS,aAAa;AAC5B,gBAAM,IAAI,MAAM,qDAAqD;AAAA,QACzE;AACA,eAAO,IAAI,0BAA0B,KAAK,aAAa,KAAK,YAAY,cAAc,KAAK,SAAS,KAAK,QAAQ;AAAA,MACrH,KAAK,kBAAkB;AACnB,eAAO,IAAI,qBAAqB,KAAK,aAAa,KAAK,SAAS,KAAK,QAAQ;AAAA,MACjF;AACI,cAAM,IAAI,MAAM,sBAAsB,SAAS,GAAG;AAAA,IAC1D;AAAA,EACJ;AAAA,EACA,gBAAgB,KAAK,gBAAgB;AACjC,SAAK,UAAU,YAAY,KAAK;AAChC,QAAI,KAAK,SAAS,WAAW;AACzB,WAAK,UAAU,UAAU,CAAO,MAAM;AAClC,YAAI,WAAW;AACf,YAAI,KAAK,SAAS,WAAW;AACzB,cAAI;AACA,iBAAK,SAAS,aAAa;AAC3B,kBAAM,KAAK,UAAU,QAAQ,KAAK,cAAc;AAChD,kBAAM,KAAK,SAAS,OAAO;AAAA,UAC/B,QACM;AACF,uBAAW;AAAA,UACf;AAAA,QACJ,OACK;AACD,eAAK,gBAAgB,CAAC;AACtB;AAAA,QACJ;AACA,YAAI,UAAU;AACV,eAAK,gBAAgB,CAAC;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ,OACK;AACD,WAAK,UAAU,UAAU,CAAC,MAAM,KAAK,gBAAgB,CAAC;AAAA,IAC1D;AACA,WAAO,KAAK,UAAU,QAAQ,KAAK,cAAc;AAAA,EACrD;AAAA,EACA,yBAAyB,UAAU,oBAAoB,yBAAyB,sBAAsB;AAClG,UAAM,YAAY,kBAAkB,SAAS,SAAS;AACtD,QAAI,cAAc,QAAQ,cAAc,QAAW;AAC/C,WAAK,QAAQ,IAAI,SAAS,OAAO,uBAAuB,SAAS,SAAS,+CAA+C;AACzH,aAAO,IAAI,MAAM,uBAAuB,SAAS,SAAS,+CAA+C;AAAA,IAC7G,OACK;AACD,UAAI,iBAAiB,oBAAoB,SAAS,GAAG;AACjD,cAAM,kBAAkB,SAAS,gBAAgB,IAAI,CAAC,MAAM,eAAe,CAAC,CAAC;AAC7E,YAAI,gBAAgB,QAAQ,uBAAuB,KAAK,GAAG;AACvD,cAAK,cAAc,kBAAkB,cAAc,CAAC,KAAK,SAAS,aAC7D,cAAc,kBAAkB,oBAAoB,CAAC,KAAK,SAAS,aAAc;AAClF,iBAAK,QAAQ,IAAI,SAAS,OAAO,uBAAuB,kBAAkB,SAAS,CAAC,qDAAqD;AACzI,mBAAO,IAAI,0BAA0B,IAAI,kBAAkB,SAAS,CAAC,2CAA2C,SAAS;AAAA,UAC7H,OACK;AACD,iBAAK,QAAQ,IAAI,SAAS,OAAO,wBAAwB,kBAAkB,SAAS,CAAC,IAAI;AACzF,gBAAI;AACA,mBAAK,SAAS,YAAY,cAAc,kBAAkB,aAAa,uBAAuB;AAC9F,qBAAO,KAAK,oBAAoB,SAAS;AAAA,YAC7C,SACO,IAAI;AACP,qBAAO;AAAA,YACX;AAAA,UACJ;AAAA,QACJ,OACK;AACD,eAAK,QAAQ,IAAI,SAAS,OAAO,uBAAuB,kBAAkB,SAAS,CAAC,gEAAgE,eAAe,uBAAuB,CAAC,IAAI;AAC/L,iBAAO,IAAI,MAAM,IAAI,kBAAkB,SAAS,CAAC,sBAAsB,eAAe,uBAAuB,CAAC,GAAG;AAAA,QACrH;AAAA,MACJ,OACK;AACD,aAAK,QAAQ,IAAI,SAAS,OAAO,uBAAuB,kBAAkB,SAAS,CAAC,0CAA0C;AAC9H,eAAO,IAAI,uBAAuB,IAAI,kBAAkB,SAAS,CAAC,gCAAgC,SAAS;AAAA,MAC/G;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,cAAc,WAAW;AACrB,WAAO,aAAa,OAAQ,cAAe,YAAY,aAAa;AAAA,EACxE;AAAA,EACA,gBAAgB,OAAO;AACnB,SAAK,QAAQ,IAAI,SAAS,OAAO,iCAAiC,KAAK,2BAA2B,KAAK,gBAAgB,GAAG;AAC1H,SAAK,YAAY;AAEjB,YAAQ,KAAK,cAAc;AAC3B,SAAK,aAAa;AAClB,QAAI,KAAK,qBAAqB,gBAAmD;AAC7E,WAAK,QAAQ,IAAI,SAAS,OAAO,yCAAyC,KAAK,4EAA4E;AAC3J;AAAA,IACJ;AACA,QAAI,KAAK,qBAAqB,cAA+C;AACzE,WAAK,QAAQ,IAAI,SAAS,SAAS,yCAAyC,KAAK,wEAAwE;AACzJ,YAAM,IAAI,MAAM,iCAAiC,KAAK,qEAAqE;AAAA,IAC/H;AACA,QAAI,KAAK,qBAAqB,iBAAqD;AAG/E,WAAK,qBAAqB;AAAA,IAC9B;AACA,QAAI,OAAO;AACP,WAAK,QAAQ,IAAI,SAAS,OAAO,uCAAuC,KAAK,IAAI;AAAA,IACrF,OACK;AACD,WAAK,QAAQ,IAAI,SAAS,aAAa,0BAA0B;AAAA,IACrE;AACA,QAAI,KAAK,YAAY;AACjB,WAAK,WAAW,KAAK,EAAE,MAAM,CAAC,MAAM;AAChC,aAAK,QAAQ,IAAI,SAAS,OAAO,0CAA0C,CAAC,IAAI;AAAA,MACpF,CAAC;AACD,WAAK,aAAa;AAAA,IACtB;AACA,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,QAAI,KAAK,oBAAoB;AACzB,WAAK,qBAAqB;AAC1B,UAAI;AACA,YAAI,KAAK,SAAS;AACd,eAAK,QAAQ,KAAK;AAAA,QACtB;AAAA,MACJ,SACO,GAAG;AACN,aAAK,QAAQ,IAAI,SAAS,OAAO,0BAA0B,KAAK,kBAAkB,CAAC,IAAI;AAAA,MAC3F;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,YAAY,KAAK;AAEb,QAAI,IAAI,YAAY,YAAY,CAAC,MAAM,KAAK,IAAI,YAAY,WAAW,CAAC,MAAM,GAAG;AAC7E,aAAO;AAAA,IACX;AACA,QAAI,CAAC,SAAS,WAAW;AACrB,YAAM,IAAI,MAAM,mBAAmB,GAAG,IAAI;AAAA,IAC9C;AAMA,UAAM,OAAO,OAAO,SAAS,cAAc,GAAG;AAC9C,SAAK,OAAO;AACZ,SAAK,QAAQ,IAAI,SAAS,aAAa,gBAAgB,GAAG,SAAS,KAAK,IAAI,IAAI;AAChF,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,qBAAqB,KAAK;AACtB,UAAM,eAAe,IAAI,IAAI,GAAG;AAChC,QAAI,aAAa,SAAS,SAAS,GAAG,GAAG;AACrC,mBAAa,YAAY;AAAA,IAC7B,OACK;AACD,mBAAa,YAAY;AAAA,IAC7B;AACA,UAAM,eAAe,IAAI,gBAAgB,aAAa,YAAY;AAClE,QAAI,CAAC,aAAa,IAAI,kBAAkB,GAAG;AACvC,mBAAa,OAAO,oBAAoB,KAAK,kBAAkB,SAAS,CAAC;AAAA,IAC7E;AACA,QAAI,aAAa,IAAI,sBAAsB,GAAG;AAC1C,UAAI,aAAa,IAAI,sBAAsB,MAAM,QAAQ;AACrD,aAAK,SAAS,wBAAwB;AAAA,MAC1C;AAAA,IACJ,WACS,KAAK,SAAS,0BAA0B,MAAM;AACnD,mBAAa,OAAO,wBAAwB,MAAM;AAAA,IACtD;AACA,iBAAa,SAAS,aAAa,SAAS;AAC5C,WAAO,aAAa,SAAS;AAAA,EACjC;AACJ;AACA,SAAS,iBAAiB,oBAAoB,iBAAiB;AAC3D,SAAO,CAAC,uBAAwB,kBAAkB,wBAAwB;AAC9E;AAEO,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EAC5B,YAAY,YAAY;AACpB,SAAK,aAAa;AAClB,SAAK,UAAU,CAAC;AAChB,SAAK,aAAa;AAClB,SAAK,oBAAoB,IAAI,cAAc;AAC3C,SAAK,mBAAmB,IAAI,cAAc;AAC1C,SAAK,mBAAmB,KAAK,UAAU;AAAA,EAC3C;AAAA,EACA,KAAK,MAAM;AACP,SAAK,YAAY,IAAI;AACrB,QAAI,CAAC,KAAK,kBAAkB;AACxB,WAAK,mBAAmB,IAAI,cAAc;AAAA,IAC9C;AACA,WAAO,KAAK,iBAAiB;AAAA,EACjC;AAAA,EACA,OAAO;AACH,SAAK,aAAa;AAClB,SAAK,kBAAkB,QAAQ;AAC/B,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,YAAY,MAAM;AACd,QAAI,KAAK,QAAQ,UAAU,OAAQ,KAAK,QAAQ,CAAC,MAAO,OAAQ,MAAO;AACnE,YAAM,IAAI,MAAM,+BAA+B,OAAQ,KAAK,OAAQ,oBAAoB,OAAQ,IAAK,EAAE;AAAA,IAC3G;AACA,SAAK,QAAQ,KAAK,IAAI;AACtB,SAAK,kBAAkB,QAAQ;AAAA,EACnC;AAAA,EACM,YAAY;AAAA;AACd,aAAO,MAAM;AACT,cAAM,KAAK,kBAAkB;AAC7B,YAAI,CAAC,KAAK,YAAY;AAClB,cAAI,KAAK,kBAAkB;AACvB,iBAAK,iBAAiB,OAAO,qBAAqB;AAAA,UACtD;AACA;AAAA,QACJ;AACA,aAAK,oBAAoB,IAAI,cAAc;AAC3C,cAAM,kBAAkB,KAAK;AAC7B,aAAK,mBAAmB;AACxB,cAAM,OAAO,OAAQ,KAAK,QAAQ,CAAC,MAAO,WACtC,KAAK,QAAQ,KAAK,EAAE,IACpB,oBAAmB,eAAe,KAAK,OAAO;AAClD,aAAK,QAAQ,SAAS;AACtB,YAAI;AACA,gBAAM,KAAK,WAAW,KAAK,IAAI;AAC/B,0BAAgB,QAAQ;AAAA,QAC5B,SACO,OAAO;AACV,0BAAgB,OAAO,KAAK;AAAA,QAChC;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA,EACA,OAAO,eAAe,cAAc;AAChC,UAAM,cAAc,aAAa,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC;AAChF,UAAM,SAAS,IAAI,WAAW,WAAW;AACzC,QAAI,SAAS;AACb,eAAW,QAAQ,cAAc;AAC7B,aAAO,IAAI,IAAI,WAAW,IAAI,GAAG,MAAM;AACvC,gBAAU,KAAK;AAAA,IACnB;AACA,WAAO,OAAO;AAAA,EAClB;AACJ;AACA,IAAM,gBAAN,MAAoB;AAAA,EAChB,cAAc;AACV,SAAK,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW,CAAC,KAAK,WAAW,KAAK,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC;AAAA,EACxG;AAAA,EACA,UAAU;AACN,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,OAAO,QAAQ;AACX,SAAK,UAAU,MAAM;AAAA,EACzB;AACJ;;;ACjjBA,IAAM,yBAAyB;AAExB,IAAM,kBAAN,MAAsB;AAAA,EACzB,cAAc;AAEV,SAAK,OAAO;AAEZ,SAAK,UAAU;AAEf,SAAK,iBAAiB,eAAe;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,OAAO,QAAQ;AAEzB,QAAI,OAAO,UAAU,UAAU;AAC3B,YAAM,IAAI,MAAM,yDAAyD;AAAA,IAC7E;AACA,QAAI,CAAC,OAAO;AACR,aAAO,CAAC;AAAA,IACZ;AACA,QAAI,WAAW,MAAM;AACjB,eAAS,WAAW;AAAA,IACxB;AAEA,UAAM,WAAW,kBAAkB,MAAM,KAAK;AAC9C,UAAM,cAAc,CAAC;AACrB,eAAW,WAAW,UAAU;AAC5B,YAAM,gBAAgB,KAAK,MAAM,OAAO;AACxC,UAAI,OAAO,cAAc,SAAS,UAAU;AACxC,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACtC;AACA,cAAQ,cAAc,MAAM;AAAA,QACxB,KAAK,YAAY;AACb,eAAK,qBAAqB,aAAa;AACvC;AAAA,QACJ,KAAK,YAAY;AACb,eAAK,qBAAqB,aAAa;AACvC;AAAA,QACJ,KAAK,YAAY;AACb,eAAK,qBAAqB,aAAa;AACvC;AAAA,QACJ,KAAK,YAAY;AAEb;AAAA,QACJ,KAAK,YAAY;AAEb;AAAA,QACJ,KAAK,YAAY;AACb,eAAK,cAAc,aAAa;AAChC;AAAA,QACJ,KAAK,YAAY;AACb,eAAK,mBAAmB,aAAa;AACrC;AAAA,QACJ;AAEI,iBAAO,IAAI,SAAS,aAAa,2BAA2B,cAAc,OAAO,YAAY;AAC7F;AAAA,MACR;AACA,kBAAY,KAAK,aAAa;AAAA,IAClC;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,SAAS;AAClB,WAAO,kBAAkB,MAAM,KAAK,UAAU,OAAO,CAAC;AAAA,EAC1D;AAAA,EACA,qBAAqB,SAAS;AAC1B,SAAK,sBAAsB,QAAQ,QAAQ,yCAAyC;AACpF,QAAI,QAAQ,iBAAiB,QAAW;AACpC,WAAK,sBAAsB,QAAQ,cAAc,yCAAyC;AAAA,IAC9F;AAAA,EACJ;AAAA,EACA,qBAAqB,SAAS;AAC1B,SAAK,sBAAsB,QAAQ,cAAc,yCAAyC;AAC1F,QAAI,QAAQ,SAAS,QAAW;AAC5B,YAAM,IAAI,MAAM,yCAAyC;AAAA,IAC7D;AAAA,EACJ;AAAA,EACA,qBAAqB,SAAS;AAC1B,QAAI,QAAQ,UAAU,QAAQ,OAAO;AACjC,YAAM,IAAI,MAAM,yCAAyC;AAAA,IAC7D;AACA,QAAI,CAAC,QAAQ,UAAU,QAAQ,OAAO;AAClC,WAAK,sBAAsB,QAAQ,OAAO,yCAAyC;AAAA,IACvF;AACA,SAAK,sBAAsB,QAAQ,cAAc,yCAAyC;AAAA,EAC9F;AAAA,EACA,cAAc,SAAS;AACnB,QAAI,OAAO,QAAQ,eAAe,UAAU;AACxC,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACzD;AAAA,EACJ;AAAA,EACA,mBAAmB,SAAS;AACxB,QAAI,OAAO,QAAQ,eAAe,UAAU;AACxC,YAAM,IAAI,MAAM,0CAA0C;AAAA,IAC9D;AAAA,EACJ;AAAA,EACA,sBAAsB,OAAO,cAAc;AACvC,QAAI,OAAO,UAAU,YAAY,UAAU,IAAI;AAC3C,YAAM,IAAI,MAAM,YAAY;AAAA,IAChC;AAAA,EACJ;AACJ;;;AC5GA,IAAM,sBAAsB;AAAA,EACxB,OAAO,SAAS;AAAA,EAChB,OAAO,SAAS;AAAA,EAChB,MAAM,SAAS;AAAA,EACf,aAAa,SAAS;AAAA,EACtB,MAAM,SAAS;AAAA,EACf,SAAS,SAAS;AAAA,EAClB,OAAO,SAAS;AAAA,EAChB,UAAU,SAAS;AAAA,EACnB,MAAM,SAAS;AACnB;AACA,SAAS,cAAc,MAAM;AAIzB,QAAM,UAAU,oBAAoB,KAAK,YAAY,CAAC;AACtD,MAAI,OAAO,YAAY,aAAa;AAChC,WAAO;AAAA,EACX,OACK;AACD,UAAM,IAAI,MAAM,sBAAsB,IAAI,EAAE;AAAA,EAChD;AACJ;AAEO,IAAM,uBAAN,MAA2B;AAAA,EAC9B,iBAAiB,SAAS;AACtB,QAAI,WAAW,SAAS,SAAS;AACjC,QAAI,SAAS,OAAO,GAAG;AACnB,WAAK,SAAS;AAAA,IAClB,WACS,OAAO,YAAY,UAAU;AAClC,YAAM,WAAW,cAAc,OAAO;AACtC,WAAK,SAAS,IAAI,cAAc,QAAQ;AAAA,IAC5C,OACK;AACD,WAAK,SAAS,IAAI,cAAc,OAAO;AAAA,IAC3C;AACA,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,KAAK,wBAAwB;AACjC,QAAI,WAAW,KAAK,KAAK;AACzB,QAAI,WAAW,KAAK,KAAK;AACzB,SAAK,MAAM;AAGX,QAAI,OAAO,2BAA2B,UAAU;AAC5C,WAAK,wBAAwB,kCAAK,KAAK,wBAA0B;AAAA,IACrE,OACK;AACD,WAAK,wBAAwB,iCACtB,KAAK,wBADiB;AAAA,QAEzB,WAAW;AAAA,MACf;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,UAAU;AACtB,QAAI,WAAW,UAAU,UAAU;AACnC,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AAAA,EACA,uBAAuB,8BAA8B;AACjD,QAAI,KAAK,iBAAiB;AACtB,YAAM,IAAI,MAAM,yCAAyC;AAAA,IAC7D;AACA,QAAI,CAAC,8BAA8B;AAC/B,WAAK,kBAAkB,IAAI,uBAAuB;AAAA,IACtD,WACS,MAAM,QAAQ,4BAA4B,GAAG;AAClD,WAAK,kBAAkB,IAAI,uBAAuB,4BAA4B;AAAA,IAClF,OACK;AACD,WAAK,kBAAkB;AAAA,IAC3B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,cAAc;AAC5B,QAAI,WAAW,cAAc,cAAc;AAC3C,SAAK,+BAA+B;AACpC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,cAAc;AAChC,QAAI,WAAW,cAAc,cAAc;AAC3C,SAAK,mCAAmC;AACxC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,SAAS;AAC3B,QAAI,KAAK,0BAA0B,QAAW;AAC1C,WAAK,wBAAwB,CAAC;AAAA,IAClC;AACA,SAAK,sBAAsB,wBAAwB;AACnD,SAAK,+BAA+B,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAC9F,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AAGJ,UAAM,wBAAwB,KAAK,yBAAyB,CAAC;AAE7D,QAAI,sBAAsB,WAAW,QAAW;AAE5C,4BAAsB,SAAS,KAAK;AAAA,IACxC;AAEA,QAAI,CAAC,KAAK,KAAK;AACX,YAAM,IAAI,MAAM,0FAA0F;AAAA,IAC9G;AACA,UAAM,aAAa,IAAI,eAAe,KAAK,KAAK,qBAAqB;AACrE,WAAO,cAAc,OAAO,YAAY,KAAK,UAAU,WAAW,UAAU,KAAK,YAAY,IAAI,gBAAgB,GAAG,KAAK,iBAAiB,KAAK,8BAA8B,KAAK,kCAAkC,KAAK,4BAA4B;AAAA,EACzP;AACJ;AACA,SAAS,SAAS,QAAQ;AACtB,SAAO,OAAO,QAAQ;AAC1B;", "names": ["LogLevel", "MessageType", "HubConnectionState", "HttpTransportType", "TransferFormat", "AbortController", "AbortController"]}