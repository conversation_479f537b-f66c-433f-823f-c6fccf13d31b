import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ApiCredentialsServiceProxy, ModelDetailsServiceProxy, ResponseMessage } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { FormsModule } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { concatMap, finalize } from 'rxjs/operators';
import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';
import { AddApiDialogComponent } from '../../components/models/add-api-dialog/add-api-dialog.component';

@Component({
  selector: 'app-connections-settings',
  standalone: true,
  imports: [CommonModule, ServiceProxyModule, FormsModule, SpinnerComponent, AddApiDialogComponent],
  templateUrl: './conection.component.html',
  styles: [`
    /* Animations */
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    @keyframes slideInRight {
      from { opacity: 0; transform: translateX(20px); }
      to { opacity: 1; transform: translateX(0); }
    }

    .animate-fadeIn {
      animation: fadeIn 0.5s ease-in-out;
    }

    .animate-pulse {
      animation: pulse 2s ease-in-out infinite;
    }

    /* Header styles */
    .sticky-header {
      position: sticky;
      top: 0;
      z-index: 10;
      backdrop-filter: blur(5px);
    }

    /* Button styles */
    .action-button {
      opacity: 0.9;
      transition: all 0.3s ease;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .action-button:hover {
      opacity: 1;
      transform: translateY(-2px) !important;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Card hover effects for action buttons */
    .api-card .action-button {
      opacity: 0;
      transform: translateY(10px);
      transition: all 0.3s ease;
    }

    .api-card:hover .action-button {
      opacity: 1;
      transform: translateY(0);
    }

    /* Staggered animation for multiple buttons */
    .api-card:hover .action-button:nth-child(1) {
      transition-delay: 0.05s;
    }

    .api-card:hover .action-button:nth-child(2) {
      transition-delay: 0.1s;
    }

    /* Dark mode specific styles */
    :host-context(.dark) input,
    :host-context(.dark) select,
    :host-context(.dark) button {
      color: var(--text-dark);
      border-color: rgba(255, 255, 255, 0.1);
    }

    :host-context(.dark) .action-button {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    :host-context(.dark) button[title="Resync models"] {
      background-color: #1E3A8A;
    }

    :host-context(.dark) button[title="Resync models"]:hover {
      background-color: #2563EB;
    }

    :host-context(.dark) button[title="Delete API"] {
      background-color: #742A2A;
    }

    :host-context(.dark) button[title="Delete API"]:hover {
      background-color: #9B2C2C;
    }

    /* Card view dark mode styles */
    :host-context(.dark) .grid > div {
      background-color: var(--hover-blue-gray);
      border-color: rgba(255, 255, 255, 0.1);
    }

    :host-context(.dark) .grid > div:hover {
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    }

    :host-context(.dark) .grid > div .border-b,
    :host-context(.dark) .grid > div .border-t {
      border-color: rgba(255, 255, 255, 0.1);
    }

    :host-context(.dark) .grid > div .bg-[var(--hover-blue-gray)] {
      background-color: rgba(0, 0, 0, 0.2);
    }

    /* Tooltip styles */
    .tooltip {
      position: relative;
    }

    .tooltip .tooltip-text {
      visibility: hidden;
      width: 120px;
      background-color: #333;
      color: #fff;
      text-align: center;
      border-radius: 6px;
      padding: 5px;
      position: absolute;
      z-index: 1;
      bottom: 125%;
      left: 50%;
      margin-left: -60px;
      opacity: 0;
      transition: opacity 0.3s;
    }

    .tooltip:hover .tooltip-text {
      visibility: visible;
      opacity: 1;
    }

    /* Responsive styles */
    @media (max-width: 768px) {
      .action-button {
        opacity: 1;
      }
    }
  `]
})
export class ConnectionsComponent implements OnInit {
  apiLists: any = [];
  filteredApis: any = []; // For search functionality
  searchQuery: string = ''; // For search input
  isLoading: boolean = false; // Loading state for spinner

  // For editing custom models
  editCustomModelsData: any = {
    id: '',
    customModels: [],
    customModelsText: ''
  };
  showEditCustomModelsForm = false;
  showAddApiDialog = false;
  isResyncingAll = false;
  isResyncing: { [key: string]: boolean } = {};

  constructor(
    private apiCredentialsService: ApiCredentialsServiceProxy,
    private nzMessageService: NzMessageService,
    private modelDetailsService: ModelDetailsServiceProxy
  ) { }

  ngOnInit() {
    this.loadApiCredentials();
  }

  loadApiCredentials() {
    this.isLoading = true; // Show spinner
    this.apiCredentialsService.getAll().subscribe({
      next: (res: any) => {
        if (res) {
          this.apiLists = res;
          // Apply current search filter if exists, otherwise show all
          if (this.searchQuery) {
            this.filterApis();
          } else {
            this.filteredApis = [...this.apiLists]; // Initialize filtered list with all APIs
          }
        }
        this.isLoading = false; // Hide spinner
      },
      error: (error: any) => {
        console.error('Error loading API credentials:', error);
        this.isLoading = false; // Hide spinner on error
      }
    });
  }

  /**
   * Filter APIs based on search query
   */
  filterApis() {
    if (!this.searchQuery) {
      this.filteredApis = [...this.apiLists];
      return;
    }

    const query = this.searchQuery.toLowerCase();
    this.filteredApis = this.apiLists.filter((api: any) =>
      api.tokenUrl?.toLowerCase().includes(query) ||
      (api.tokenUrl?.split('.')[1]?.toLowerCase() || '').includes(query)
    );
  }

  /**
   * Clear search and reset filtered APIs
   */
  clearSearch() {
    this.searchQuery = '';
    this.filteredApis = [...this.apiLists];
  }

  onAddApi() {
    this.showAddApiDialog = true;
  }

  onApiAdded(newApi: any) {
    this.apiLists.push(newApi);
    this.filteredApis = [...this.apiLists]; // Update filtered list
    this.showAddApiDialog = false;
  }

  onDialogClosed() {
    this.showAddApiDialog = false;
  }



  searchName(url: string): string {
    const providers = {
      'azure': 'Azure OpenAI',
      'openai': 'OpenAI',
      'googleapis': 'Google APIs',
    };

    for (const [key, value] of Object.entries(providers)) {
      if (url.includes(key)) {
        return value;
      }
    }

    return 'Local';
  }

  async deleteApi(api: any) {
    try {
      let res: any = await this.apiCredentialsService.delete(api.id).toPromise();

      if (!res.isError) {
        this.apiLists = this.apiLists.filter((item: any) => item.id != api.id);
        this.filteredApis = this.filteredApis.filter((item: any) => item.id != api.id); // Update filtered list
        this.nzMessageService.success(res.message);
      }
    } catch (error: any) {
      this.nzMessageService.error(
        'Cannot delete credentials; remove linked models in Agent first'
      );
    }
  }



  /**
   * Update edit custom models array from text input
   */
  updateEditCustomModels() {
    if (!this.editCustomModelsData.customModelsText) {
      this.editCustomModelsData.customModels = [];
      return;
    }

    // Split by newline and filter out empty lines
    this.editCustomModelsData.customModels = this.editCustomModelsData.customModelsText
      .split('\n')
      .map((model: string) => model.trim())
      .filter((model: string | any[]) => model.length > 0);
  }

  /**
   * Open the edit custom models form
   */
  onEditCustomModels(api: any) {
    this.editCustomModelsData = {
      id: api.id,
      customModels: [],
      customModelsText: ''
    };

    // Get existing models for this connection
    this.apiCredentialsService.getById(api.id).subscribe((credentials: any) => {
      if (credentials) {
        // Get models for this provider
        this.modelDetailsService.getByApiCredentialsId(api.id).subscribe((models: any) => {
          if (models && models.length > 0) {
            // Extract model names and join with newlines
            const modelNames = models.map((model: any) => this.removeProvider(model.modelName));
            this.editCustomModelsData.customModelsText = modelNames.join('\n');
            this.updateEditCustomModels();
            this.showEditCustomModelsForm = true;
          } else {
            this.nzMessageService.error('No models found for this connection');
          }
        });
      }
    });
  }

  removeProvider(model: string) {
    const parts = model.split('_');
    return parts.slice(1).join('_');
  }

  /**
   * Save updated custom models
   */
  saveEditCustomModels() {
    if (this.editCustomModelsData.customModels.length === 0) {
      this.nzMessageService.error('Please enter at least one model name');
      return;
    }

    const loadingMessage = this.nzMessageService.loading('Saving custom models...');

    this.apiCredentialsService.addCustomModels(this.editCustomModelsData.id, this.editCustomModelsData.customModels)
      .subscribe({
        next: (response: ResponseMessage) => {
          this.nzMessageService.remove(loadingMessage.messageId);
          if (!response.isError) {
            this.nzMessageService.success(response.message || 'Custom models updated successfully');
            this.resetEditCustomModelsForm();
            // Refresh the API list to reflect any changes
            this.loadApiCredentials();
          } else {
            this.nzMessageService.error(response.message || 'Failed to update custom models');
          }
        },
        error: (error) => {
          this.nzMessageService.remove(loadingMessage.messageId);
          this.nzMessageService.error('Error updating custom models: ' + (error.message || 'Unknown error'));
        }
      });
  }

  /**
   * Reset the edit custom models form
   */
  resetEditCustomModelsForm() {
    this.editCustomModelsData = {
      id: '',
      customModels: [],
      customModelsText: ''
    };
    this.showEditCustomModelsForm = false;
  }

  /**
   * Resync models for a specific API connection
   */
  resyncModels(api: any) {
    if (this.isResyncing[api.id]) {
      return; // Prevent multiple clicks
    }

    this.isResyncing[api.id] = true;
    const loadingMessage = this.nzMessageService.loading('Resyncing models...');

    this.apiCredentialsService.resyncModels(api.id)
      .pipe(
        finalize(() => {
          this.isResyncing[api.id] = false;
        })
      )
      .subscribe({
        next: (response: ResponseMessage) => {
          this.nzMessageService.remove(loadingMessage.messageId);
          if (!response.isError) {
            this.nzMessageService.success(response.message || 'Models resynced successfully');
            // Refresh the API list to reflect any changes
            this.loadApiCredentials();
            // The search will be maintained as loadApiCredentials calls filterApis
          } else {
            this.nzMessageService.error(response.message || 'Failed to resync models');
          }
        },
        error: (error) => {
          this.nzMessageService.remove(loadingMessage.messageId);
          this.nzMessageService.error('Error resyncing models: ' + (error.message || 'Unknown error'));
        }
      });
  }

  /**
   * Resync models for all API connections
   */
  resyncAllModels() {
    if (this.isResyncingAll) {
      return; // Prevent multiple clicks
    }

    this.isResyncingAll = true;
    const loadingMessage = this.nzMessageService.loading('Resyncing all models...');

    this.apiCredentialsService.resyncAllModels()
      .pipe(
        finalize(() => {
          this.isResyncingAll = false;
        })
      )
      .subscribe({
        next: (response: ResponseMessage) => {
          this.nzMessageService.remove(loadingMessage.messageId);
          if (!response.isError) {
            this.nzMessageService.success(response.message || 'All models resynced successfully');
            // Refresh the API list to reflect any changes
            this.loadApiCredentials();
            // The search will be maintained as loadApiCredentials calls filterApis
          } else {
            this.nzMessageService.error(response.message || 'Failed to resync all models');
          }
        },
        error: (error) => {
          this.nzMessageService.remove(loadingMessage.messageId);
          this.nzMessageService.error('Error resyncing all models: ' + (error.message || 'Unknown error'));
        }
      });
  }
}
